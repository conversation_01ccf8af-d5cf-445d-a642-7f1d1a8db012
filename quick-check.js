#!/usr/bin/env node

// 快速性能检查脚本
import fs from 'fs';
import { execSync } from 'child_process';

console.log('🚀 Nuxt.js 开发环境性能快速检查\n');

// 检查 Node.js 版本
const nodeVersion = process.version;
console.log(`📦 Node.js 版本: ${nodeVersion}`);

// 检查内存使用
const memUsage = process.memoryUsage();
console.log(`💾 内存使用: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB / ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);

// 检查 node_modules 大小
try {
  const nodeModulesStats = fs.statSync('./node_modules');
  console.log('📁 node_modules 存在');
} catch (error) {
  console.log('❌ node_modules 不存在，请运行 npm install');
}

// 检查关键配置文件
const configFiles = [
  'nuxt.config.ts',
  'package.json',
  '.env.development'
];

configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} 存在`);
  } else {
    console.log(`❌ ${file} 不存在`);
  }
});

// 检查依赖数量
try {
  const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
  const depCount = Object.keys(packageJson.dependencies || {}).length;
  const devDepCount = Object.keys(packageJson.devDependencies || {}).length;
  console.log(`📦 生产依赖: ${depCount} 个`);
  console.log(`🔧 开发依赖: ${devDepCount} 个`);
  
  if (depCount > 50) {
    console.log('⚠️  依赖较多，可能影响启动速度');
  }
} catch (error) {
  console.log('❌ 无法读取 package.json');
}

console.log('\n💡 优化建议:');
console.log('1. 使用 npm run dev:fast 启动优化的开发服务器');
console.log('2. 确保使用 SSD 硬盘');
console.log('3. 关闭不必要的 VS Code 插件');
console.log('4. 增加系统内存到 8GB 以上');
console.log('5. 定期清理 node_modules: rm -rf node_modules && npm install');
