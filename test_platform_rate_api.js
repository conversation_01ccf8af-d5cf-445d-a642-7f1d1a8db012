// 测试平台费率API的脚本
const axios = require('axios');

const baseURL = 'http://127.0.0.1:4000/api/v1/provider';

// 测试数据
const testUpdateData = {
  user_id: '587926350434674688',
  business_info: {
    // 基本信息（保持现有数据不变）
    legalPersonName: '测试法人',
    companyName: '测试公司'
  },
  platform_rate: [
    {
      id: '185570623764959232',
      rate: '0.0500' // 5%
    },
    {
      id: '185571825244311552',
      rate: '0.0300' // 3%
    }
  ]
};

async function testUpdateWithPlatformRate() {
  try {
    console.log('测试通过update接口更新平台费率...');
    console.log('请求数据:', JSON.stringify(testUpdateData, null, 2));

    const response = await axios.post(`${baseURL}/info/update`, testUpdateData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));

    if (response.data.code === 200) {
      console.log('✅ 通过update接口更新平台费率成功');
    } else {
      console.log('❌ 通过update接口更新平台费率失败:', response.data.message);
    }
  } catch (error) {
    console.error('❌ API调用失败:', error.message);
    if (error.response) {
      console.error('错误响应:', error.response.data);
    }
  }
}

async function testGetProviderDetail() {
  try {
    console.log('\n测试获取服务商详情API...');

    const response = await axios.get(`${baseURL}/info/detail/${testUpdateData.user_id}`, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('响应状态:', response.status);
    console.log('平台费率数据:', JSON.stringify(response.data.data.platform_rate, null, 2));

    if (response.data.code === 200) {
      console.log('✅ 服务商详情获取成功');

      // 验证是否包含所有平台
      const platformRate = response.data.data.platform_rate;
      if (platformRate && platformRate.length > 0) {
        console.log(`📊 共获取到 ${platformRate.length} 个平台的费率信息`);
        platformRate.forEach(rate => {
          console.log(`   - ${rate.name}: ${(rate.rate * 100).toFixed(2)}%`);
        });
      } else {
        console.log('⚠️  未获取到平台费率数据');
      }
    } else {
      console.log('❌ 服务商详情获取失败:', response.data.message);
    }
  } catch (error) {
    console.error('❌ API调用失败:', error.message);
    if (error.response) {
      console.error('错误响应:', error.response.data);
    }
  }
}

async function testGetAllChannels() {
  try {
    console.log('\n测试获取所有渠道信息...');

    // 直接查询数据库获取渠道信息
    const { exec } = require('child_process');
    const util = require('util');
    const execPromise = util.promisify(exec);

    const { stdout } = await execPromise('psql ************************************************/julingcloud5 -c "SELECT id, name FROM base.channel WHERE deleted_at IS NULL ORDER BY id;" -t');

    console.log('数据库中的渠道信息:');
    console.log(stdout);
  } catch (error) {
    console.error('❌ 获取渠道信息失败:', error.message);
  }
}

// 运行测试
async function runTests() {
  await testGetAllChannels();
  await testUpdateWithPlatformRate();
  await testGetProviderDetail();
}

runTests();
