import { defineConfig } from 'vite'

export default defineConfig({
  // 开发环境专用配置
  server: {
    // 启用文件系统缓存
    fs: {
      cachedChecks: false
    },
    // 预热常用文件
    warmup: {
      clientFiles: [
        './app.vue',
        './pages/**/*.vue',
        './components/**/*.vue',
        './layouts/**/*.vue'
      ]
    }
  },
  
  // 优化依赖处理
  optimizeDeps: {
    // 强制预构建这些依赖
    force: true,
    include: [
      'vue',
      'vue-router',
      '@arco-design/web-vue',
      'axios',
      'dayjs',
      'lodash-es',
      'pinia',
      '@iconify/vue'
    ]
  },

  // 构建优化
  build: {
    // 开发环境不需要压缩
    minify: false,
    // 禁用 CSS 代码分割
    cssCodeSplit: false,
    // 减少构建输出
    reportCompressedSize: false
  },

  // 缓存配置
  cacheDir: 'node_modules/.vite-dev'
})
