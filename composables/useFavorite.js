import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import mallApi from '@/api/mall'

/**
 * 收藏功能组合函数
 * 提供收藏相关的功能，可在任何组件中使用
 */
export const useFavorite = () => {
  const isLoading = ref(false)

  /**
   * 添加收藏
   * @param {string|number} productId - 商品ID
   * @param {string} productName - 商品名称（可选）
   * @param {string} remark - 备注（可选）
   * @returns {Promise<Object>} - 返回操作结果
   */
  const addToFavorites = async (productId, productName = '', remark = '') => {
    try {
      isLoading.value = true
      
      const response = await mallApi.favorite.addFavorite({
        targetId: [productId],
        targetType: 1,
        remark: remark || `收藏商品: ${productName}`
      })
      
      if (response.code === 200) {
        Message.success('收藏成功')
        return { success: true, data: response.data }
      } else {
        Message.error(response.message || '收藏失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('收藏失败:', error)
      Message.error('收藏失败，请稍后再试')
      return { success: false, error }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 取消收藏
   * @param {string|number} productId - 商品ID
   * @param {string} productName - 商品名称（可选）
   * @param {string} remark - 备注（可选）
   * @returns {Promise<Object>} - 返回操作结果
   */
  const removeFromFavorites = async (productId, productName = '', remark = '') => {
    try {
      isLoading.value = true
      
      const response = await mallApi.favorite.cancelFavorite({
        targetId: [productId],
        targetType: 1,
        remark: remark || `取消收藏商品: ${productName}`
      })
      
      if (response.code === 200) {
        Message.success('取消收藏成功')
        return { success: true, data: response.data }
      } else {
        Message.error(response.message || '取消收藏失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('取消收藏失败:', error)
      Message.error('取消收藏失败，请稍后再试')
      return { success: false, error }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 切换收藏状态
   * @param {string|number} productId - 商品ID
   * @param {boolean} currentStatus - 当前收藏状态
   * @param {string} productName - 商品名称（可选）
   * @param {string} remark - 备注（可选）
   * @returns {Promise<Object>} - 返回操作结果
   */
  const toggleFavorite = async (productId, currentStatus, productName = '', remark = '') => {
    if (currentStatus) {
      return await removeFromFavorites(productId, productName, remark)
    } else {
      return await addToFavorites(productId, productName, remark)
    }
  }

  /**
   * 检查收藏状态
   * @param {string|number} productId - 商品ID
   * @returns {Promise<Object>} - 返回检查结果
   */
  const checkFavoriteStatus = async (productId) => {
    try {
      const response = await mallApi.favorite.checkFavoriteStatus({
        targetId: productId,
        targetType: 1
      })
      
      if (response.code === 200) {
        return { success: true, isFavorited: response.data.isFavorited }
      } else {
        return { success: false, isFavorited: false, message: response.message }
      }
    } catch (error) {
      console.error('检查收藏状态失败:', error)
      return { success: false, isFavorited: false, error }
    }
  }

  /**
   * 批量添加收藏
   * @param {Array} productIds - 商品ID数组
   * @param {string} remark - 备注（可选）
   * @returns {Promise<Object>} - 返回操作结果
   */
  const batchAddToFavorites = async (productIds, remark = '批量收藏') => {
    try {
      isLoading.value = true
      
      const response = await mallApi.favorite.batchAddFavorite(productIds, 1, remark)
      
      if (response.code === 200) {
        Message.success(`成功收藏${response.data.successCount}件商品`)
        return { success: true, data: response.data }
      } else {
        Message.error(response.message || '批量收藏失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('批量收藏失败:', error)
      Message.error('批量收藏失败，请稍后再试')
      return { success: false, error }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 批量取消收藏
   * @param {Array} productIds - 商品ID数组
   * @param {string} remark - 备注（可选）
   * @returns {Promise<Object>} - 返回操作结果
   */
  const batchRemoveFromFavorites = async (productIds, remark = '批量取消收藏') => {
    try {
      isLoading.value = true
      
      const response = await mallApi.favorite.batchCancelFavorite(productIds, 1, remark)
      
      if (response.code === 200) {
        Message.success(`成功取消收藏${response.data.successCount}件商品`)
        return { success: true, data: response.data }
      } else {
        Message.error(response.message || '批量取消收藏失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('批量取消收藏失败:', error)
      Message.error('批量取消收藏失败，请稍后再试')
      return { success: false, error }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 获取收藏列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.targetType - 目标类型，默认为1
   * @returns {Promise<Object>} - 返回收藏列表
   */
  const getFavoriteList = async (params = { page: 1, pageSize: 10, targetType: 1 }) => {
    try {
      isLoading.value = true

      // 确保targetType默认为1
      const queryParams = {
        targetType: 1,
        ...params
      }

      const response = await mallApi.favorite.getFavoriteList(queryParams)

      if (response.code === 200) {
        return { success: true, data: response.data }
      } else {
        Message.error(response.message || '获取收藏列表失败')
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('获取收藏列表失败:', error)
      Message.error('获取收藏列表失败，请稍后再试')
      return { success: false, error }
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoading,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    checkFavoriteStatus,
    batchAddToFavorites,
    batchRemoveFromFavorites,
    getFavoriteList
  }
}
