import * as echarts from 'echarts/core'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  <PERSON>atter<PERSON>hart,
  PictorialBar<PERSON>hart,
  RadarChart,
  GaugeChart
} from 'echarts/charts'
import {
  TitleComponent,
  LegendComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  ToolboxComponent,
  DataZoomComponent,
} from 'echarts/components'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { useColorMode } from '@vueuse/core'

// 引入词云图组件
import 'echarts-wordcloud'

// 注册必要的组件
echarts.use([
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  PictorialBarChart,
  RadarChart,
  GaugeChart,
  TitleComponent,
  LegendComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  ToolboxComponent,
  DataZoomComponent,
  LabelLayout,
  UniversalT<PERSON>sition,
  <PERSON>vasRenderer
])

const colorMode = useColorMode()

function themeMode() {
  return colorMode.value === 'dark' ? 'dark' : 'default'
}

export function useEcharts(el: Ref<HTMLElement | undefined>) {
  let chart: echarts.ECharts | null = null

  function initChart() {
    if (el.value) {
      chart = echarts.init(el.value, themeMode())
    }
  }

  function setOption(option: echarts.EChartsCoreOption) {
    nextTick(() => {
      if (!chart) {
        initChart()
      }
      if (chart) {
        chart.setOption(option)
      }
    })
  }

  function resize() {
    chart?.resize()
  }

  function dispose() {
    chart?.dispose()
    chart = null
  }

  onMounted(() => {
    window.addEventListener('resize', resize)
  })

  onUnmounted(() => {
    window.removeEventListener('resize', resize)
    dispose()
  })

  return {
    chart,
    initChart,
    setOption,
    resize,
    dispose
  }
}
