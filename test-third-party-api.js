/**
 * 测试第三方商品API（无鉴权）
 * 验证接口是否能正常工作
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1/master/third-party/goods-spu';

// 测试数据
const testCreateData = {
  name: "测试第三方商品",
  channelId: "123456789",
  platformId: "987654321",
  storeId: "456789123",
  subtitle: "测试副标题",
  description: "基础描述",
  spuImages: [
    "https://example.com/spu-image1.jpg",
    "https://example.com/spu-image2.jpg"
  ],
  richDescription: "<p>这是富文本详情</p><ul><li>特点1</li><li>特点2</li></ul>",
  skus: [
    {
      skuCode: "TEST_SKU_001",
      salesPrice: 99.99,
      stock: 100,
      marketPrice: 129.99,
      image: "https://example.com/sku1-image.jpg",
      weight: 0.5,
      volume: 0.1,
      unit: "件"
    },
    {
      skuCode: "TEST_SKU_002",
      salesPrice: 89.99,
      stock: 50,
      marketPrice: 119.99,
      image: "https://example.com/sku2-image.jpg",
      weight: 0.6,
      volume: 0.12,
      unit: "盒"
    }
  ]
};

async function testCreateProduct() {
  try {
    console.log('=== 测试创建第三方商品 ===');
    console.log('请求数据:', JSON.stringify(testCreateData, null, 2));
    
    const response = await axios.post(BASE_URL, testCreateData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 创建成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    return response.data.data?.id;
  } catch (error) {
    console.error('❌ 创建失败');
    if (error.response) {
      console.error('错误状态:', error.response.status);
      console.error('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('错误信息:', error.message);
    }
    return null;
  }
}

async function testUpdateProduct(productId) {
  if (!productId) {
    console.log('⏭️ 跳过更新测试（没有产品ID）');
    return;
  }
  
  try {
    console.log('\n=== 测试更新第三方商品 ===');
    
    const updateData = {
      name: "更新后的第三方商品",
      spuImages: [
        "https://example.com/updated-spu-image1.jpg"
      ],
      richDescription: "<p>更新后的富文本详情</p>",
      skus: [
        {
          id: "123456789", // 这里需要实际的SKU ID
          skuCode: "TEST_SKU_001_UPDATED",
          salesPrice: 109.99,
          stock: 150,
          marketPrice: 139.99,
          image: "https://example.com/updated-sku-image.jpg",
          weight: 0.7,
          volume: 0.15,
          unit: "套"
        }
      ]
    };
    
    console.log('更新数据:', JSON.stringify(updateData, null, 2));
    
    const response = await axios.put(`${BASE_URL}/${productId}`, updateData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ 更新成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ 更新失败');
    if (error.response) {
      console.error('错误状态:', error.response.status);
      console.error('错误数据:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('错误信息:', error.message);
    }
  }
}

async function runTests() {
  console.log('开始测试第三方商品API（无鉴权）...\n');
  
  // 测试创建
  const productId = await testCreateProduct();
  
  // 测试更新
  await testUpdateProduct(productId);
  
  console.log('\n=== 测试完成 ===');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testCreateProduct, testUpdateProduct };
