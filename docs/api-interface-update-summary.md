# 第三方商品API接口更新总结

## 📋 更新背景

根据用户需求，我们移除了不需要的接口（获取列表和详情），只保留了创建和更新接口。

## 🔄 接口变更

### ❌ 移除的接口
1. **GET /api/v1/master/third-party/goods-spu** - 获取第三方商品列表
2. **GET /api/v1/master/third-party/goods-spu/{id}** - 获取第三方商品详情

### ✅ 保留的接口
1. **POST /api/v1/master/third-party/goods-spu** - 创建第三方商品
2. **PUT /api/v1/master/third-party/goods-spu/{id}** - 更新第三方商品（新增）

## 🆕 新增更新接口详情

### 接口信息
- **方法**: PUT
- **路径**: `/api/v1/master/third-party/goods-spu/{id}`
- **描述**: 更新第三方平台商品信息，支持更新商品基本信息和SKU信息

### 请求参数
```json
{
  "name": "更新后的商品名称",           // 可选
  "subtitle": "更新后的副标题",        // 可选
  "description": "更新后的商品描述",    // 可选
  "skus": [                           // 可选
    {
      "id": "123456789",              // 必填（更新现有SKU时）
      "skuCode": "SKU001-UPDATED",    // 可选
      "salesPrice": 109.99,           // 可选
      "stock": 150                    // 可选
    }
  ]
}
```

### 响应示例
```json
{
  "code": 200,
  "success": true,
  "message": "更新第三方商品成功",
  "data": {
    "id": "1234567890123456789",
    "spuCode": "TP_SPU_20241225_001",
    "name": "更新后的商品名称",
    "subtitle": "更新后的副标题",
    "updatedAt": 1703500800000
  }
}
```

## 🔧 技术实现

### 1. 路由层 (ThirdPartyGoodsSpuRoute.js)
- ✅ 移除了列表和详情接口的路由定义
- ✅ 更新了API文档注释，支持多平台关联
- ✅ 添加了完整的更新接口文档

### 2. 控制器层 (ThirdPartyGoodsSpuController.js)
- ✅ 移除了 `getThirdPartyProductList()` 方法
- ✅ 移除了 `getThirdPartyProductById()` 方法
- ✅ 移除了 `deleteThirdPartyProduct()` 方法
- ✅ 移除了 `batchCreateThirdPartyProducts()` 方法
- ✅ 完善了 `updateThirdPartyProduct()` 方法实现

### 3. 服务层 (ThirdPartyGoodsSpuService.js)
- ✅ 新增了 `updateThirdPartyProduct()` 方法
- ✅ 新增了 `preprocessUpdateData()` 数据预处理方法
- ✅ 新增了 `validateUpdateBusinessRules()` 业务验证方法

### 4. 模型层 (ThirdPartyGoodsSpuModel.js)
- ✅ 新增了 `updateThirdPartyProduct()` 主更新方法
- ✅ 新增了 `updateSkusForSpu()` SKU更新方法
- ✅ 新增了 `findSkusByCodes()` SKU编码冲突检查方法
- ✅ 新增了 `findThirdPartyProductById()` 商品验证方法

### 5. DTO层 (ThirdPartyGoodsSpuDto.js)
- ✅ 重新实现了 `validateUpdate()` 方法
- ✅ 支持所有字段可选更新
- ✅ 支持SKU更新验证（需要SKU ID）

## 🎯 核心功能特性

### 更新功能支持
1. **基本信息更新**: 商品名称、副标题、描述
2. **SKU信息更新**: SKU编码、销售价格、库存
3. **部分更新**: 只更新提供的字段，其他字段保持不变
4. **数据验证**: 完整的输入验证和业务规则验证

### 业务规则验证
1. **SKU编码唯一性**: 确保SKU编码在全局范围内唯一
2. **商品存在性**: 验证要更新的商品确实存在
3. **SKU归属验证**: 确保更新的SKU属于指定商品
4. **数据完整性**: 事务保证数据一致性

### 错误处理
1. **404**: 商品不存在
2. **400**: 参数验证失败、SKU编码冲突
3. **401**: 用户身份验证失败
4. **500**: 服务器内部错误

## 📊 数据库操作

### 事务处理
- 使用Prisma事务确保数据一致性
- 支持SPU和SKU的联合更新
- 自动重新计算商品统计信息

### 统计信息更新
- 自动更新商品总库存
- 保持数据一致性

## ✅ 完成状态

所有相关文件已更新完毕：

- 📁 **路由文件**: 移除不需要的接口，完善更新接口文档
- 🎮 **控制器**: 简化为创建和更新两个核心方法
- 🔧 **服务层**: 完整的更新业务逻辑实现
- 💾 **模型层**: 完整的数据库更新操作
- ✅ **验证层**: 灵活的更新数据验证

现在第三方商品API只提供创建和更新两个核心接口，满足第三方平台的基本需求！🚀

## 📄 API文档

完整的API接口文档已更新到 `docs/third-party-goods-apifox.json`，包含：

### 📋 接口列表
1. **POST** `/api/v1/master/third-party/goods-spu` - 创建第三方商品
2. **PUT** `/api/v1/master/third-party/goods-spu/{id}` - 更新第三方商品

### 📖 文档特性
- ✅ **完整的OpenAPI 3.0规范**
- ✅ **详细的请求/响应示例**
- ✅ **多种使用场景示例**
- ✅ **完整的错误码说明**
- ✅ **数据验证规则**
- ✅ **支持Apifox、Postman等工具导入**

### 🔧 使用方式
1. 将 `docs/third-party-goods-apifox.json` 导入到Apifox或Postman
2. 配置服务器地址和认证信息
3. 开始测试API接口

文档已移除不需要的列表和详情接口，专注于创建和更新功能！
