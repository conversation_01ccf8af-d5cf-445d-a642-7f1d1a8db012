-- =====================================================
-- 第三方商品多平台销售 - 完整数据库设置脚本
-- 执行前请备份数据库！
-- =====================================================

-- 开始事务
BEGIN;

-- =====================================================
-- 第一步：创建新的 goods_sku_platform_relation 表
-- =====================================================

-- 1. 创建新表（不影响现有的 goods_sku_channel 表）
CREATE TABLE IF NOT EXISTS "base"."goods_sku_platform_relation" (
    "id" BIGINT PRIMARY KEY,
    "goods_sku_id" BIGINT NOT NULL,
    "channel_id" BIGINT NOT NULL,
    "platform_id" BIGINT NOT NULL,
    "store_id" BIGINT NOT NULL,
    "third_party_sku_code" VARCHAR(100),
    "is_enabled" INT DEFAULT 1,
    "created_at" BIGINT NOT NULL,
    "updated_at" BIGINT NOT NULL,
    "deleted_at" BIGINT
);

-- 2. 添加同步状态字段
-- 推送状态相关
ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "push_status" INT DEFAULT 0;

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "push_time" BIGINT;

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "push_error_msg" TEXT;

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "push_retry_count" INT DEFAULT 0;

-- 同步状态相关
ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "sync_status" INT DEFAULT 0;

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "sync_time" BIGINT;

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "sync_error_msg" TEXT;

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "sync_retry_count" INT DEFAULT 0;

-- 平台信息字段
ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "platform_sku_id" VARCHAR(100);

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "platform_status" INT;

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "platform_price" DECIMAL(10,2);

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "platform_stock" INT;

ALTER TABLE "base"."goods_sku_platform_relation" 
ADD COLUMN IF NOT EXISTS "last_sync_data" JSONB;

-- =====================================================
-- 第二步：添加基础外键约束到新表
-- =====================================================

-- 添加基础外键约束
ALTER TABLE "base"."goods_sku_platform_relation"
ADD CONSTRAINT "fk_goods_sku_platform_relation_sku"
FOREIGN KEY ("goods_sku_id") REFERENCES "base"."goods_skus"("id")
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "base"."goods_sku_platform_relation"
ADD CONSTRAINT "fk_goods_sku_platform_relation_channel"
FOREIGN KEY ("channel_id") REFERENCES "base"."channel"("id")
ON DELETE CASCADE ON UPDATE CASCADE;

-- =====================================================
-- 第三步：扩展 goods_spus 表
-- =====================================================

-- 添加商品来源标识字段
ALTER TABLE "base"."goods_spus"
ADD COLUMN IF NOT EXISTS "source_type" INT DEFAULT 0;

-- 添加创建来源信息
ALTER TABLE "base"."goods_spus" 
ADD COLUMN IF NOT EXISTS "created_channel_id" BIGINT;

ALTER TABLE "base"."goods_spus" 
ADD COLUMN IF NOT EXISTS "created_platform_id" BIGINT;

ALTER TABLE "base"."goods_spus" 
ADD COLUMN IF NOT EXISTS "created_store_id" BIGINT;

-- =====================================================
-- 第四步：添加剩余外键约束
-- =====================================================

-- goods_sku_platform_relation 表的平台和店铺外键约束
ALTER TABLE "base"."goods_sku_platform_relation"
ADD CONSTRAINT "fk_goods_sku_platform_relation_platform"
FOREIGN KEY ("platform_id") REFERENCES "base"."platform"("id")
ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "base"."goods_sku_platform_relation"
ADD CONSTRAINT "fk_goods_sku_platform_relation_store"
FOREIGN KEY ("store_id") REFERENCES "base"."store"("id")
ON DELETE CASCADE ON UPDATE CASCADE;

-- goods_spus 表的外键约束
ALTER TABLE "base"."goods_spus" 
ADD CONSTRAINT "fk_goods_spus_created_channel" 
FOREIGN KEY ("created_channel_id") REFERENCES "base"."channel"("id") 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "base"."goods_spus" 
ADD CONSTRAINT "fk_goods_spus_created_platform" 
FOREIGN KEY ("created_platform_id") REFERENCES "base"."platform"("id") 
ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "base"."goods_spus" 
ADD CONSTRAINT "fk_goods_spus_created_store" 
FOREIGN KEY ("created_store_id") REFERENCES "base"."store"("id") 
ON DELETE SET NULL ON UPDATE CASCADE;

-- =====================================================
-- 第五步：创建唯一约束
-- =====================================================

-- 一个SKU在同一个渠道+平台+店铺组合中只能存在一次
CREATE UNIQUE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_unique_combination" 
ON "base"."goods_sku_platform_relation" (goods_sku_id, channel_id, platform_id, store_id) 
WHERE deleted_at IS NULL;

-- 第三方SKU编码在同一个渠道+平台+店铺组合中唯一
CREATE UNIQUE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_third_party_unique" 
ON "base"."goods_sku_platform_relation" (channel_id, platform_id, store_id, third_party_sku_code) 
WHERE deleted_at IS NULL AND third_party_sku_code IS NOT NULL;

-- =====================================================
-- 第六步：创建性能优化索引
-- =====================================================

-- 基础查询索引
CREATE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_platform" 
ON "base"."goods_sku_platform_relation" (platform_id) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_store" 
ON "base"."goods_sku_platform_relation" (store_id) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_composite" 
ON "base"."goods_sku_platform_relation" (channel_id, platform_id, store_id, is_enabled) 
WHERE deleted_at IS NULL;

-- 同步状态索引
CREATE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_push_status" 
ON "base"."goods_sku_platform_relation" (push_status) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_sync_status" 
ON "base"."goods_sku_platform_relation" (sync_status) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_platform_sku_id" 
ON "base"."goods_sku_platform_relation" (platform_sku_id) 
WHERE deleted_at IS NULL AND platform_sku_id IS NOT NULL;

-- 重试任务索引
CREATE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_push_retry" 
ON "base"."goods_sku_platform_relation" (push_status, push_retry_count, push_time) 
WHERE deleted_at IS NULL AND push_status = 3;

CREATE INDEX IF NOT EXISTS "idx_goods_sku_platform_relation_sync_retry" 
ON "base"."goods_sku_platform_relation" (sync_status, sync_retry_count, sync_time) 
WHERE deleted_at IS NULL AND sync_status = 3;

-- 商品相关索引
CREATE INDEX IF NOT EXISTS "idx_goods_spus_source_type"
ON "base"."goods_spus" (source_type)
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS "idx_goods_spus_created_source" 
ON "base"."goods_spus" (source_type, created_channel_id, created_platform_id) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS "idx_goods_spus_status_created_at" 
ON "base"."goods_spus" (status, created_at DESC) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS "idx_goods_spus_name_gin" 
ON "base"."goods_spus" USING gin(to_tsvector('simple', name)) 
WHERE deleted_at IS NULL;

CREATE UNIQUE INDEX IF NOT EXISTS "idx_goods_skus_sku_code_unique" 
ON "base"."goods_skus" (sku_code) 
WHERE deleted_at IS NULL;

-- =====================================================
-- 第七步：添加字段注释
-- =====================================================

-- goods_sku_platform_relation 表注释
COMMENT ON TABLE "base"."goods_sku_platform_relation" IS 'SKU平台关系表，管理SKU在不同渠道、平台、店铺的关系和同步状态';

COMMENT ON COLUMN "base"."goods_sku_platform_relation"."platform_id" IS '平台ID，支持SKU在多平台销售';
COMMENT ON COLUMN "base"."goods_sku_platform_relation"."store_id" IS '店铺ID，支持SKU在多店铺销售';
COMMENT ON COLUMN "base"."goods_sku_platform_relation"."push_status" IS '推送状态：0=未推送，1=推送中，2=推送成功，3=推送失败 (GoodsSyncStatusEnum.PUSH_STATUS)';
COMMENT ON COLUMN "base"."goods_sku_platform_relation"."sync_status" IS '同步状态：0=未同步，1=同步中，2=同步成功，3=同步失败 (GoodsSyncStatusEnum.SYNC_STATUS)';
COMMENT ON COLUMN "base"."goods_sku_platform_relation"."platform_sku_id" IS '平台返回的SKU ID';
COMMENT ON COLUMN "base"."goods_sku_platform_relation"."last_sync_data" IS '最后同步的完整数据（JSON格式）';

-- goods_spus 表注释
COMMENT ON COLUMN "base"."goods_spus"."source_type" IS '商品来源类型：0=系统订单(GoodsSourceTypeEnum.SYSTEM)，1=第三方订单(GoodsSourceTypeEnum.THIRD_PARTY)';
COMMENT ON COLUMN "base"."goods_spus"."created_channel_id" IS '商品创建时的渠道ID（记录来源）';
COMMENT ON COLUMN "base"."goods_spus"."created_platform_id" IS '商品创建时的平台ID（记录来源）';
COMMENT ON COLUMN "base"."goods_spus"."created_store_id" IS '商品创建时的店铺ID（记录来源）';

-- =====================================================
-- 第八步：更新表统计信息
-- =====================================================

ANALYZE "base"."goods_spus";
ANALYZE "base"."goods_skus";
ANALYZE "base"."goods_sku_platform_relation";
ANALYZE "base"."channel";
ANALYZE "base"."platform";
ANALYZE "base"."store";

-- 提交事务
COMMIT;

-- =====================================================
-- 执行完成提示
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '=================================================';
    RAISE NOTICE '第三方商品多平台销售数据库设置完成！';
    RAISE NOTICE '=================================================';
    RAISE NOTICE '✅ 表结构扩展完成';
    RAISE NOTICE '✅ 外键约束创建完成';
    RAISE NOTICE '✅ 性能索引创建完成';
    RAISE NOTICE '✅ 同步状态字段添加完成';
    RAISE NOTICE '=================================================';
    RAISE NOTICE '核心功能：';
    RAISE NOTICE '🚀 支持一个SKU在多个平台销售';
    RAISE NOTICE '🚀 记录商品创建来源';
    RAISE NOTICE '🚀 跟踪推送和同步状态';
    RAISE NOTICE '🚀 支持重试机制';
    RAISE NOTICE '🚀 完整的错误处理';
    RAISE NOTICE '=================================================';
    RAISE NOTICE '现在可以使用第三方商品创建接口了！';
    RAISE NOTICE 'API地址: POST /api/v1/master/third-party/goods-spu';
    RAISE NOTICE '=================================================';
END $$;
