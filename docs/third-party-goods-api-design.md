# 第三方商品创建接口技术方案

## 1. 项目背景

为第三方平台提供简化的商品创建接口，支持快速创建商品而无需复杂的分类、品牌、属性关联。接口设计遵循最小必填字段原则，支持多SKU创建。

## 2. 接口设计

### 2.1 基本信息

- **接口路径**: `/api/v1/master/third-party/goods-spu`
- **请求方法**: `POST`
- **认证方式**: JWT Token
- **内容类型**: `application/json`

### 2.2 必填字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 商品名称 |
| channelId | string | 是 | 渠道ID |
| platformId | string | 是 | 平台ID |
| storeId | string | 是 | 店铺ID |
| skus | array | 是 | SKU数组，至少包含1个SKU |

### 2.3 SKU字段结构

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| skuCode | string | 是 | SKU编码，同一商品内唯一 |
| salesPrice | number | 是 | 销售价格，必须大于0 |
| stock | number | 是 | 库存数量，必须大于等于0 |

### 2.4 请求示例

```json
{
  "name": "第三方商品名称",
  "channelId": "123456789",
  "platformId": "987654321", 
  "storeId": "456789123",
  "skus": [
    {
      "skuCode": "SKU001",
      "salesPrice": 99.99,
      "stock": 100
    },
    {
      "skuCode": "SKU002", 
      "salesPrice": 89.99,
      "stock": 50
    }
  ]
}
```

## 3. 数据库设计

### 3.1 使用现有表结构

- `goods_spus` - 商品SPU主表
- `goods_skus` - 商品SKU表
- `goods_sku_channel` - SKU渠道关联表

### 3.2 字段映射策略

**goods_spus表自动填充：**
- `spu_code`: 系统自动生成
- `slug`: 基于商品名称生成
- `goods_brand_id`: null（可空）
- `status`: 1（默认上架）
- `is_virtual`: 0（实体商品）
- `sort_order`: 0
- `total_sales`: 0

**goods_skus表自动填充：**
- `market_price`: null
- `cost_price`: null
- `weight`: null
- `volume`: null
- `unit`: null
- `is_enabled`: 1

**goods_sku_channel表关联：**
- 自动为每个SKU创建渠道关联记录

### 3.3 不创建的关联

- `goods_category_associations` - 分类关联（可选）
- `goods_attribute_values` - 属性值关联（可选）
- `goods_service_associations` - 服务关联（可选）

## 4. 业务逻辑

### 4.1 SKU类型判断

- 1个SKU → `skuType = 1`（单规格）
- 多个SKU → `skuType = 2`（多规格）

### 4.2 验证规则

1. **渠道验证**: 验证channelId、platformId、storeId的有效性和关联关系
2. **SKU编码唯一性**: 验证同一商品内SKU编码不重复
3. **全局SKU编码唯一性**: 验证SKU编码在系统中全局唯一
4. **价格验证**: 销售价格必须大于0
5. **库存验证**: 库存数量必须大于等于0

### 4.3 事务处理

使用数据库事务确保数据一致性：
1. 创建商品SPU
2. 批量创建SKU
3. 批量创建SKU渠道关联
4. 更新商品总库存

## 5. 响应格式

### 5.1 成功响应

```json
{
  "code": 200,
  "success": true,
  "message": "第三方商品创建成功",
  "data": {
    "id": "1234567890123456789",
    "spuCode": "SPU_20241225_001",
    "name": "第三方商品名称",
    "skuCount": 2,
    "totalStock": 150,
    "createdAt": 1703472000000
  }
}
```

### 5.2 错误响应

```json
{
  "code": 400,
  "success": false,
  "message": "SKU编码SKU001已存在",
  "data": null
}
```

## 6. 文件结构

```
server/apps/master/
├── routes/ThirdPartyGoodsSpuRoute.js          # 路由配置
├── controllers/ThirdPartyGoodsSpuController.js # 控制器
├── services/ThirdPartyGoodsSpuService.js      # 业务服务
├── models/ThirdPartyGoodsSpuModel.js          # 数据模型
└── dto/ThirdPartyGoodsSpuDto.js               # 数据验证
```

## 7. 实现优势

1. **简化接口**: 只需要最核心的字段，降低第三方接入成本
2. **复用架构**: 使用现有数据库表结构，无需额外表设计
3. **灵活扩展**: 后续可以根据需要添加可选字段
4. **数据完整性**: 通过事务保证数据一致性
5. **渠道关联**: 自动建立SKU与渠道的关联关系

## 8. 后续扩展

- 支持商品图片上传
- 支持商品描述和详情
- 支持批量商品创建
- 支持商品同步状态跟踪
