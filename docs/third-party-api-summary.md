# 第三方商品管理API接口文档总结

## 📋 接口概览

本文档包含了完整的第三方商品管理API接口，支持第三方平台进行商品、分类、品牌的创建和更新操作。

### 🔗 API端点列表

| 功能 | 方法 | 端点 | 描述 |
|------|------|------|------|
| 创建商品SPU | POST | `/api/v1/master/third-party/goods-spu` | 创建第三方商品，支持多SKU |
| 更新商品SPU | PUT | `/api/v1/master/third-party/goods-spu/{id}` | 更新第三方商品信息 |
| 创建商品分类 | POST | `/api/v1/master/third-party/goods-category` | 创建第三方商品分类 |
| 更新商品分类 | PUT | `/api/v1/master/third-party/goods-category/{id}` | 更新第三方商品分类 |
| 创建商品品牌 | POST | `/api/v1/master/third-party/goods-brand` | 创建第三方商品品牌 |
| 更新商品品牌 | PUT | `/api/v1/master/third-party/goods-brand/{id}` | 更新第三方商品品牌 |

### 🔑 核心特性

1. **无需认证** - 所有接口都不需要JWT认证，方便第三方平台直接调用
2. **多平台支持** - 支持一个实体关联多个渠道-平台-店铺组合
3. **雪花ID** - 使用16-19位雪花ID确保全局唯一性
4. **数据验证** - 完整的参数验证和关联关系验证
5. **事务安全** - 使用数据库事务确保数据一致性

### 📊 必填参数

所有创建接口都需要以下必填参数：
- `name` - 实体名称
- `channelId` - 关联渠道ID
- `platformId` - 关联平台ID  
- `storeId` - 关联店铺ID

### 📁 文档文件

- **OpenAPI文档**: `docs/third-party-goods-apifox.json`
- **导入方式**: 可直接导入Apifox进行接口测试和管理

### 🧪 测试状态

所有接口已完成全面测试：
- ✅ 基础CRUD操作
- ✅ 数据验证和错误处理
- ✅ 多平台关联场景
- ✅ 边界条件测试
- ✅ 数据库完整性验证

### 🏗️ 架构设计

采用统一的架构模式：
- **主表** + **关联表** 的设计模式
- **主表**：存储实体基本信息和创建来源
- **关联表**：管理多平台关联关系和同步状态

### 📝 使用说明

1. 将 `docs/third-party-goods-apifox.json` 导入到Apifox
2. 根据实际的渠道、平台、店铺ID修改示例数据
3. 按照接口文档进行API调用测试
4. 所有接口返回统一的响应格式

### 🔄 响应格式

```json
{
  "code": 200,
  "success": true,
  "message": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### ⚠️ 注意事项

1. 确保channelId、platformId、storeId之间的关联关系正确
2. 所有ID参数都是字符串格式的数字
3. 可选字段可以不传或传空字符串
4. 更新接口只更新传入的字段，未传入的字段保持不变
