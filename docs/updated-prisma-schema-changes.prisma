// =====================================================
// 更新后的 Prisma Schema 变更
// 需要在 goods_spus.prisma 文件中添加以下关联
// =====================================================

// 在 goods_spus.prisma 文件中的 GoodsSpu 模型添加以下字段和关联：

model GoodsSpu {
  // ... 现有字段 ...
  
  // 新增字段
  source_channel_id   BigInt?  // 来源渠道ID
  source_platform_id  BigInt?  // 来源平台ID  
  source_store_id     BigInt?  // 来源店铺ID
  source_type         String   @default("internal") @db.VarChar(20) // 商品来源类型
  
  // ... 现有关联 ...
  
  // 新增关联
  channel             channel?  @relation("goods_spus_source_channel", fields: [source_channel_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  platform            platform? @relation("goods_spus_source_platform", fields: [source_platform_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  store               store?    @relation("goods_spus_source_store", fields: [source_store_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  // 新增索引
  @@index([source_type], map: "idx_goods_spus_source_type")
  @@index([source_channel_id], map: "idx_goods_spus_source_channel")
  @@index([source_platform_id], map: "idx_goods_spus_source_platform") 
  @@index([source_store_id], map: "idx_goods_spus_source_store")
  @@index([source_type, source_channel_id, created_at], map: "idx_goods_spus_third_party_composite")
}

// =====================================================
// 同时需要在对应的模型中添加反向关联
// =====================================================

// 在 channel.prisma 中添加：
model channel {
  // ... 现有字段 ...
  
  // 新增关联
  source_goods_spus   GoodsSpu[] @relation("goods_spus_source_channel")
}

// 在 platform.prisma 中添加：
model platform {
  // ... 现有字段 ...
  
  // 新增关联  
  source_goods_spus   GoodsSpu[] @relation("goods_spus_source_platform")
}

// 在 store.prisma 中添加：
model store {
  // ... 现有字段 ...
  
  // 新增关联
  source_goods_spus   GoodsSpu[] @relation("goods_spus_source_store")
}
