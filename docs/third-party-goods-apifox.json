{"openapi": "3.0.0", "info": {"title": "第三方商品管理接口", "description": "为第三方平台提供简化的商品创建和管理接口", "version": "1.0.0", "contact": {"name": "API 支持", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "本地开发环境"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"SKU": {"type": "object", "required": ["skuCode", "salesPrice", "stock"], "properties": {"skuCode": {"type": "string", "description": "SKU编码", "example": "SKU001"}, "salesPrice": {"type": "number", "description": "销售价格", "example": 99.99}, "stock": {"type": "integer", "description": "库存数量", "example": 100}}}, "CreateRequest": {"type": "object", "required": ["name", "channelId", "platformId", "storeId", "skus"], "properties": {"name": {"type": "string", "description": "商品名称", "example": "第三方商品名称"}, "channelId": {"type": "string", "description": "渠道ID", "example": "123456789"}, "platformId": {"type": "string", "description": "平台ID", "example": "987654321"}, "storeId": {"type": "string", "description": "店铺ID", "example": "456789123"}, "subtitle": {"type": "string", "description": "商品副标题", "example": "商品副标题"}, "description": {"type": "string", "description": "商品描述", "example": "商品详细描述"}, "skus": {"type": "array", "items": {"$ref": "#/components/schemas/SKU"}}}}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "操作成功"}, "data": {"type": "object"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "操作失败"}, "data": {"nullable": true, "example": null}}}}}, "paths": {"/api/v1/master/third-party/goods-spu": {"post": {"summary": "创建第三方商品", "description": "为第三方平台创建商品", "tags": ["第三方商品管理"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRequest"}, "examples": {"单SKU示例": {"value": {"name": "第三方单规格商品", "channelId": "123456789", "platformId": "987654321", "storeId": "456789123", "skus": [{"skuCode": "SKU001", "salesPrice": 99.99, "stock": 100}]}}, "多SKU示例": {"value": {"name": "第三方多规格商品", "channelId": "123456789", "platformId": "987654321", "storeId": "456789123", "subtitle": "多种规格可选", "description": "商品详细描述", "skus": [{"skuCode": "SKU001", "salesPrice": 99.99, "stock": 100}, {"skuCode": "SKU002", "salesPrice": 89.99, "stock": 50}]}}}}}}, "responses": {"200": {"description": "商品创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}, "example": {"code": 200, "success": true, "message": "第三方商品创建成功", "data": {"id": "1234567890123456789", "spuCode": "TP_SPU_20241225_001", "name": "第三方商品名称", "skuCount": 2, "totalStock": 150, "createdAt": 1703472000000}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "success": false, "message": "SKU编码SKU001已存在", "data": null}}}}}}, "get": {"summary": "获取第三方商品列表", "description": "获取第三方商品列表，支持分页、筛选", "tags": ["第三方商品管理"], "parameters": [{"name": "page", "in": "query", "description": "页码", "schema": {"type": "integer", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页数量", "schema": {"type": "integer", "default": 20}}, {"name": "keyword", "in": "query", "description": "搜索关键词", "schema": {"type": "string"}}, {"name": "channelId", "in": "query", "description": "渠道ID筛选", "schema": {"type": "string"}}, {"name": "status", "in": "query", "description": "商品状态（0-草稿，1-上架，2-下架）", "schema": {"type": "integer", "enum": [0, 1, 2]}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}, "example": {"code": 200, "success": true, "message": "获取第三方商品列表成功", "data": {"items": [{"id": "1234567890123456789", "spuCode": "TP_SPU_20241225_001", "name": "第三方商品名称", "status": 1, "statusText": "已上架", "skuCount": 2, "totalStock": 150, "priceRange": "¥89.99 - ¥99.99", "createdAt": 1703472000000}], "pagination": {"page": 1, "pageSize": 20, "total": 1, "totalPages": 1}}}}}}}}}, "/api/v1/master/third-party/goods-spu/{id}": {"get": {"summary": "获取第三方商品详情", "description": "根据ID获取第三方商品详细信息", "tags": ["第三方商品管理"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "商品SPU ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}, "example": {"code": 200, "success": true, "message": "获取第三方商品详情成功", "data": {"id": "1234567890123456789", "spuCode": "TP_SPU_20241225_001", "name": "第三方商品名称", "subtitle": "商品副标题", "description": "商品详细描述", "status": 1, "statusText": "已上架", "totalStock": 150, "skus": [{"id": "2345678901234567890", "skuCode": "SKU001", "salesPrice": 99.99, "stock": 100, "isEnabled": true}], "createdAt": 1703472000000}}}}}, "404": {"description": "商品不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 404, "success": false, "message": "商品不存在", "data": null}}}}}}}}, "tags": [{"name": "第三方商品管理", "description": "为第三方平台提供的商品管理接口"}]}