{"info": {"name": "第三方商品管理接口", "description": "为第三方平台提供简化的商品创建和管理接口", "version": "1.0.0"}, "item": [{"name": "创建第三方商品", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/master/third-party/goods-spu", "host": ["{{baseUrl}}"], "path": ["api", "v1", "master", "third-party", "goods-spu"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"第三方商品名称\",\n  \"channelId\": \"123456789\",\n  \"platformId\": \"987654321\",\n  \"storeId\": \"456789123\",\n  \"subtitle\": \"商品副标题\",\n  \"description\": \"商品详细描述\",\n  \"skus\": [\n    {\n      \"skuCode\": \"SKU001\",\n      \"salesPrice\": 99.99,\n      \"stock\": 100\n    },\n    {\n      \"skuCode\": \"SKU002\",\n      \"salesPrice\": 89.99,\n      \"stock\": 50\n    }\n  ]\n}"}}, "response": [{"name": "创建成功", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"第三方商品名称\",\n  \"channelId\": \"123456789\",\n  \"platformId\": \"987654321\",\n  \"storeId\": \"456789123\",\n  \"skus\": [\n    {\n      \"skuCode\": \"SKU001\",\n      \"salesPrice\": 99.99,\n      \"stock\": 100\n    }\n  ]\n}"}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 200,\n  \"success\": true,\n  \"message\": \"第三方商品创建成功\",\n  \"data\": {\n    \"id\": \"1234567890123456789\",\n    \"spuCode\": \"TP_SPU_20241225_001\",\n    \"name\": \"第三方商品名称\",\n    \"skuCount\": 1,\n    \"totalStock\": 100,\n    \"createdAt\": 1703472000000\n  }\n}"}, {"name": "SKU编码已存在", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"第三方商品名称\",\n  \"channelId\": \"123456789\",\n  \"platformId\": \"987654321\",\n  \"storeId\": \"456789123\",\n  \"skus\": [\n    {\n      \"skuCode\": \"EXISTING_SKU\",\n      \"salesPrice\": 99.99,\n      \"stock\": 100\n    }\n  ]\n}"}}, "status": "Bad Request", "code": 400, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 400,\n  \"success\": false,\n  \"message\": \"SKU编码EXISTING_SKU已存在\",\n  \"data\": null\n}"}]}, {"name": "获取第三方商品列表", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/master/third-party/goods-spu?page=1&pageSize=20&keyword=商品&channelId=123456789&status=1", "host": ["{{baseUrl}}"], "path": ["api", "v1", "master", "third-party", "goods-spu"], "query": [{"key": "page", "value": "1", "description": "页码"}, {"key": "pageSize", "value": "20", "description": "每页数量"}, {"key": "keyword", "value": "商品", "description": "搜索关键词"}, {"key": "channelId", "value": "123456789", "description": "渠道ID筛选"}, {"key": "platformId", "value": "", "description": "平台ID筛选", "disabled": true}, {"key": "storeId", "value": "", "description": "店铺ID筛选", "disabled": true}, {"key": "status", "value": "1", "description": "商品状态（0-草稿，1-上架，2-下架）"}, {"key": "startTime", "value": "", "description": "开始时间戳（毫秒）", "disabled": true}, {"key": "endTime", "value": "", "description": "结束时间戳（毫秒）", "disabled": true}]}}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/master/third-party/goods-spu?page=1&pageSize=20", "host": ["{{baseUrl}}"], "path": ["api", "v1", "master", "third-party", "goods-spu"], "query": [{"key": "page", "value": "1"}, {"key": "pageSize", "value": "20"}]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 200,\n  \"success\": true,\n  \"message\": \"获取第三方商品列表成功\",\n  \"data\": {\n    \"items\": [\n      {\n        \"id\": \"1234567890123456789\",\n        \"spuCode\": \"TP_SPU_20241225_001\",\n        \"name\": \"第三方商品名称\",\n        \"subtitle\": \"商品副标题\",\n        \"status\": 1,\n        \"statusText\": \"已上架\",\n        \"skuCount\": 2,\n        \"totalStock\": 150,\n        \"priceRange\": \"¥89.99 - ¥99.99\",\n        \"channels\": [\n          {\n            \"id\": \"123456789\",\n            \"name\": \"淘宝\"\n          }\n        ],\n        \"createdAt\": 1703472000000,\n        \"updatedAt\": 1703472000000\n      }\n    ],\n    \"pagination\": {\n      \"page\": 1,\n      \"pageSize\": 20,\n      \"total\": 1,\n      \"totalPages\": 1\n    }\n  }\n}"}]}, {"name": "获取第三方商品详情", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/v1/master/third-party/goods-spu/1234567890123456789", "host": ["{{baseUrl}}"], "path": ["api", "v1", "master", "third-party", "goods-spu", "1234567890123456789"]}}, "response": [{"name": "获取成功", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/master/third-party/goods-spu/1234567890123456789", "host": ["{{baseUrl}}"], "path": ["api", "v1", "master", "third-party", "goods-spu", "1234567890123456789"]}}, "status": "OK", "code": 200, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 200,\n  \"success\": true,\n  \"message\": \"获取第三方商品详情成功\",\n  \"data\": {\n    \"id\": \"1234567890123456789\",\n    \"spuCode\": \"TP_SPU_20241225_001\",\n    \"name\": \"第三方商品名称\",\n    \"subtitle\": \"商品副标题\",\n    \"slug\": \"third-party-product-name\",\n    \"description\": \"商品详细描述\",\n    \"status\": 1,\n    \"statusText\": \"已上架\",\n    \"isVirtual\": false,\n    \"sortOrder\": 0,\n    \"totalSales\": 0,\n    \"totalStock\": 150,\n    \"skus\": [\n      {\n        \"id\": \"2345678901234567890\",\n        \"skuCode\": \"SKU001\",\n        \"salesPrice\": 99.99,\n        \"marketPrice\": null,\n        \"costPrice\": null,\n        \"stock\": 100,\n        \"weight\": null,\n        \"volume\": null,\n        \"unit\": null,\n        \"isEnabled\": true,\n        \"channels\": [\n          {\n            \"id\": \"123456789\",\n            \"name\": \"淘宝\",\n            \"isEnabled\": true\n          }\n        ]\n      },\n      {\n        \"id\": \"3456789012345678901\",\n        \"skuCode\": \"SKU002\",\n        \"salesPrice\": 89.99,\n        \"marketPrice\": null,\n        \"costPrice\": null,\n        \"stock\": 50,\n        \"weight\": null,\n        \"volume\": null,\n        \"unit\": null,\n        \"isEnabled\": true,\n        \"channels\": [\n          {\n            \"id\": \"123456789\",\n            \"name\": \"淘宝\",\n            \"isEnabled\": true\n          }\n        ]\n      }\n    ],\n    \"createdAt\": 1703472000000,\n    \"updatedAt\": 1703472000000\n  }\n}"}, {"name": "商品不存在", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/v1/master/third-party/goods-spu/9999999999999999999", "host": ["{{baseUrl}}"], "path": ["api", "v1", "master", "third-party", "goods-spu", "9999999999999999999"]}}, "status": "Not Found", "code": 404, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"code\": 404,\n  \"success\": false,\n  \"message\": \"商品不存在\",\n  \"data\": null\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "description": "API基础URL"}, {"key": "token", "value": "", "description": "JWT认证令牌"}]}