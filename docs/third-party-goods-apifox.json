{"openapi": "3.0.0", "info": {"title": "第三方商品管理API", "description": "为第三方平台提供简化的商品管理接口，支持多平台关联。只提供创建和更新两个核心接口。", "version": "1.0.0", "contact": {"name": "API 支持", "email": "<EMAIL>"}}, "servers": [{"url": "http://localhost:3000", "description": "本地开发环境"}], "security": [{"bearerAuth": []}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}, "schemas": {"SKU": {"type": "object", "required": ["skuCode", "salesPrice", "stock"], "properties": {"skuCode": {"type": "string", "description": "SKU编码", "example": "SKU001"}, "salesPrice": {"type": "number", "description": "销售价格", "example": 99.99}, "stock": {"type": "integer", "description": "库存数量", "example": 100}, "marketPrice": {"type": "number", "description": "市场价格（可选）", "example": 129.99}, "image": {"type": "string", "description": "SKU图片URL（可选）", "example": "https://example.com/sku-image.jpg"}, "weight": {"type": "number", "description": "重量（可选）", "example": 0.5}, "volume": {"type": "number", "description": "体积（可选）", "example": 0.1}, "unit": {"type": "string", "description": "单位（可选）", "example": "件"}}}, "CreateRequest": {"type": "object", "required": ["name", "channelId", "platformId", "storeId", "skus"], "properties": {"name": {"type": "string", "description": "商品名称", "example": "第三方商品名称"}, "channelId": {"type": "string", "description": "渠道ID", "example": "123456789"}, "platformId": {"type": "string", "description": "平台ID", "example": "987654321"}, "storeId": {"type": "string", "description": "店铺ID", "example": "456789123"}, "subtitle": {"type": "string", "description": "商品副标题", "example": "商品副标题"}, "description": {"type": "string", "description": "商品描述", "example": "商品详细描述"}, "spuImages": {"type": "array", "items": {"type": "string"}, "description": "SPU多图URL数组（可选）", "example": ["https://example.com/spu-image1.jpg", "https://example.com/spu-image2.jpg"]}, "richDescription": {"type": "string", "description": "富文本详情（可选）", "example": "<p>这是富文本格式的商品详情</p>"}, "skus": {"type": "array", "items": {"$ref": "#/components/schemas/SKU"}}}}, "SuccessResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "操作成功"}, "data": {"type": "object"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "操作失败"}, "data": {"nullable": true, "example": null}}}, "UpdateRequest": {"type": "object", "minProperties": 1, "properties": {"name": {"type": "string", "description": "商品名称（可选）", "maxLength": 255, "example": "更新后的商品名称"}, "subtitle": {"type": "string", "description": "商品副标题（可选）", "maxLength": 500, "example": "更新后的副标题"}, "description": {"type": "string", "description": "商品描述（可选）", "maxLength": 5000, "example": "更新后的商品描述"}, "spuImages": {"type": "array", "items": {"type": "string"}, "description": "SPU多图URL数组（可选）", "maxItems": 20, "example": ["https://example.com/updated-spu-image1.jpg", "https://example.com/updated-spu-image2.jpg"]}, "richDescription": {"type": "string", "description": "富文本详情（可选）", "maxLength": 50000, "example": "<p>更新后的富文本格式商品详情</p>"}, "skus": {"type": "array", "description": "SKU列表（可选，如果提供则会更新SKU信息）", "minItems": 1, "items": {"type": "object", "required": ["id"], "properties": {"id": {"type": "string", "pattern": "^\\d+$", "description": "SKU ID（更新现有SKU时必填）", "example": "123456789"}, "skuCode": {"type": "string", "description": "SKU编码", "maxLength": 100, "example": "SKU001-UPDATED"}, "salesPrice": {"type": "number", "description": "销售价格", "minimum": 0, "example": 109.99}, "stock": {"type": "integer", "description": "库存数量", "minimum": 0, "example": 150}, "marketPrice": {"type": "number", "description": "市场价格（可选）", "minimum": 0, "example": 139.99}, "image": {"type": "string", "description": "SKU图片URL（可选）", "example": "https://example.com/updated-sku-image.jpg"}, "weight": {"type": "number", "description": "重量（可选）", "minimum": 0, "example": 0.6}, "volume": {"type": "number", "description": "体积（可选）", "minimum": 0, "example": 0.12}, "unit": {"type": "string", "description": "单位（可选）", "maxLength": 20, "example": "盒"}}}}}}}}, "paths": {"/api/v1/master/third-party/goods-spu": {"post": {"summary": "创建第三方商品", "description": "为第三方平台创建商品，只需要必填字段，支持多SKU和多平台关联", "tags": ["第三方商品管理"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRequest"}, "examples": {"单SKU示例": {"value": {"name": "第三方单规格商品", "channelId": "123456789", "platformId": "987654321", "storeId": "456789123", "skus": [{"skuCode": "SKU001", "salesPrice": 99.99, "stock": 100}]}}, "多SKU示例": {"value": {"name": "第三方多规格商品", "channelId": "123456789", "platformId": "987654321", "storeId": "456789123", "subtitle": "多种规格可选", "description": "商品详细描述", "skus": [{"skuCode": "SKU001", "salesPrice": 99.99, "stock": 100}, {"skuCode": "SKU002", "salesPrice": 89.99, "stock": 50}]}}, "完整字段示例": {"value": {"name": "第三方完整字段商品", "channelId": "123456789", "platformId": "987654321", "storeId": "456789123", "subtitle": "包含所有可选字段的示例", "description": "基础商品描述", "spuImages": ["https://example.com/spu-image1.jpg", "https://example.com/spu-image2.jpg"], "richDescription": "<p>这是富文本格式的详细商品描述</p><ul><li>特点1</li><li>特点2</li></ul>", "skus": [{"skuCode": "SKU001", "salesPrice": 99.99, "stock": 100, "marketPrice": 129.99, "image": "https://example.com/sku1-image.jpg", "weight": 0.5, "volume": 0.1, "unit": "件"}, {"skuCode": "SKU002", "salesPrice": 89.99, "stock": 50, "marketPrice": 119.99, "image": "https://example.com/sku2-image.jpg", "weight": 0.6, "volume": 0.12, "unit": "盒"}]}}}}}}, "responses": {"200": {"description": "商品创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}, "example": {"code": 200, "success": true, "message": "第三方商品创建成功", "data": {"id": "1234567890123456789", "spuCode": "TP_SPU_20241225_001", "name": "第三方商品名称", "skuCount": 2, "totalStock": 150, "createdAt": 1703472000000}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "success": false, "message": "SKU编码SKU001已存在", "data": null}}}}}}}, "/api/v1/master/third-party/goods-spu/{id}": {"put": {"summary": "更新第三方商品", "description": "更新第三方平台商品信息，支持更新商品基本信息和SKU信息", "tags": ["第三方商品管理"], "parameters": [{"name": "id", "in": "path", "required": true, "description": "商品SPU ID", "schema": {"type": "string", "pattern": "^\\d+$"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRequest"}, "examples": {"基本信息更新": {"summary": "只更新商品基本信息", "value": {"name": "更新后的商品名称", "subtitle": "更新后的副标题", "description": "更新后的商品描述"}}, "SKU信息更新": {"summary": "更新SKU信息", "value": {"skus": [{"id": "123456789", "skuCode": "SKU001-UPDATED", "salesPrice": 109.99, "stock": 150}]}}, "完整更新": {"summary": "更新商品和SKU信息", "value": {"name": "完全更新的商品名称", "subtitle": "新的副标题", "description": "新的商品描述", "spuImages": ["https://example.com/updated-spu-image1.jpg", "https://example.com/updated-spu-image2.jpg"], "richDescription": "<p>更新后的富文本详情</p><p>包含更多产品信息</p>", "skus": [{"id": "123456789", "skuCode": "SKU001-NEW", "salesPrice": 119.99, "stock": 200, "marketPrice": 149.99, "image": "https://example.com/updated-sku-image.jpg", "weight": 0.7, "volume": 0.15, "unit": "套"}]}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}, "example": {"code": 200, "success": true, "message": "更新第三方商品成功", "data": {"id": "1234567890123456789", "spuCode": "TP_SPU_20241225_001", "name": "更新后的商品名称", "subtitle": "更新后的副标题", "skuCount": 2, "totalStock": 300, "updatedAt": 1703500800000}}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 400, "success": false, "message": "SKU编码 SKU001 已存在", "data": null}}}}, "404": {"description": "商品不存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 404, "success": false, "message": "商品不存在", "data": null}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 401, "success": false, "message": "用户身份验证失败", "data": null}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"code": 500, "success": false, "message": "服务器内部错误", "data": null}}}}}}}}, "tags": [{"name": "第三方商品管理", "description": "为第三方平台提供的商品管理接口，支持多平台关联。只提供创建和更新两个核心接口。"}]}