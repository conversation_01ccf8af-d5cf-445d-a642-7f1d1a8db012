/**
 * 枚举使用示例
 * 展示如何在代码中使用商品相关的枚举
 */

const GoodsSourceTypeEnum = require('../server/apps/master/constants/GoodsSourceTypeEnum');
const GoodsSyncStatusEnum = require('../server/apps/master/constants/GoodsSyncStatusEnum');

// =====================================================
// 1. 商品来源类型枚举使用示例
// =====================================================

console.log('=== 商品来源类型枚举示例 ===');

// 获取枚举值
console.log('系统订单:', GoodsSourceTypeEnum.SYSTEM); // 0
console.log('第三方订单:', GoodsSourceTypeEnum.THIRD_PARTY); // 1

// 获取名称
console.log('来源类型名称:', GoodsSourceTypeEnum.getSourceTypeName(0)); // '系统订单'
console.log('来源类型名称:', GoodsSourceTypeEnum.getSourceTypeName(1)); // '第三方订单'

// 验证有效性
console.log('是否有效:', GoodsSourceTypeEnum.isValid(0)); // true
console.log('是否有效:', GoodsSourceTypeEnum.isValid(999)); // false

// 获取所有选项
console.log('所有选项:', GoodsSourceTypeEnum.getAllOptions());
// [
//   { label: '系统订单', value: 0 },
//   { label: '第三方订单', value: 1 }
// ]

// =====================================================
// 2. 同步状态枚举使用示例
// =====================================================

console.log('\n=== 同步状态枚举示例 ===');

// 推送状态
console.log('推送状态:');
console.log('未推送:', GoodsSyncStatusEnum.PUSH_STATUS.NOT_PUSHED); // 0
console.log('推送中:', GoodsSyncStatusEnum.PUSH_STATUS.PUSHING); // 1
console.log('推送成功:', GoodsSyncStatusEnum.PUSH_STATUS.PUSH_SUCCESS); // 2
console.log('推送失败:', GoodsSyncStatusEnum.PUSH_STATUS.PUSH_FAILED); // 3

// 同步状态
console.log('同步状态:');
console.log('未同步:', GoodsSyncStatusEnum.SYNC_STATUS.NOT_SYNCED); // 0
console.log('同步中:', GoodsSyncStatusEnum.SYNC_STATUS.SYNCING); // 1
console.log('同步成功:', GoodsSyncStatusEnum.SYNC_STATUS.SYNC_SUCCESS); // 2
console.log('同步失败:', GoodsSyncStatusEnum.SYNC_STATUS.SYNC_FAILED); // 3

// 获取状态名称
console.log('推送状态名称:', GoodsSyncStatusEnum.getPushStatusName(2)); // '推送成功'
console.log('同步状态名称:', GoodsSyncStatusEnum.getSyncStatusName(3)); // '同步失败'

// 验证状态有效性
console.log('推送状态有效:', GoodsSyncStatusEnum.isValidPushStatus(2)); // true
console.log('同步状态有效:', GoodsSyncStatusEnum.isValidSyncStatus(999)); // false

// 判断是否需要重试
console.log('需要重试推送:', GoodsSyncStatusEnum.shouldRetryPush(3, 2, 3)); // true
console.log('需要重试同步:', GoodsSyncStatusEnum.shouldRetrySync(3, 5, 3)); // false

// =====================================================
// 3. 在业务代码中的实际使用示例
// =====================================================

console.log('\n=== 业务代码使用示例 ===');

// 示例1：创建第三方商品时设置来源类型
function createThirdPartyProduct(productData) {
  const spuData = {
    ...productData,
    source_type: GoodsSourceTypeEnum.THIRD_PARTY, // 使用枚举而不是硬编码数字
    created_at: Date.now(),
    updated_at: Date.now()
  };
  
  console.log('创建商品数据:', spuData);
  return spuData;
}

// 示例2：更新推送状态
function updatePushStatus(skuId, platformId, success, errorMsg = null) {
  const updateData = {
    push_status: success 
      ? GoodsSyncStatusEnum.PUSH_STATUS.PUSH_SUCCESS 
      : GoodsSyncStatusEnum.PUSH_STATUS.PUSH_FAILED,
    push_time: Date.now(),
    push_error_msg: errorMsg,
    updated_at: Date.now()
  };
  
  if (!success) {
    // 增加重试次数
    updateData.push_retry_count = 'push_retry_count + 1';
  }
  
  console.log('更新推送状态:', updateData);
  return updateData;
}

// 示例3：查询需要重试的任务
function findRetryTasks() {
  const pushFailedStatus = GoodsSyncStatusEnum.PUSH_STATUS.PUSH_FAILED;
  const syncFailedStatus = GoodsSyncStatusEnum.SYNC_STATUS.SYNC_FAILED;
  
  const query = {
    where: {
      OR: [
        {
          push_status: pushFailedStatus,
          push_retry_count: { lt: 3 }
        },
        {
          sync_status: syncFailedStatus,
          sync_retry_count: { lt: 3 }
        }
      ],
      deleted_at: null
    }
  };
  
  console.log('重试任务查询条件:', JSON.stringify(query, null, 2));
  return query;
}

// 示例4：状态统计
function getStatusStatistics(records) {
  const stats = {
    sourceTypes: {},
    pushStatuses: {},
    syncStatuses: {}
  };
  
  records.forEach(record => {
    // 统计来源类型
    const sourceTypeName = GoodsSourceTypeEnum.getSourceTypeName(record.source_type);
    stats.sourceTypes[sourceTypeName] = (stats.sourceTypes[sourceTypeName] || 0) + 1;
    
    // 统计推送状态
    const pushStatusName = GoodsSyncStatusEnum.getPushStatusName(record.push_status);
    stats.pushStatuses[pushStatusName] = (stats.pushStatuses[pushStatusName] || 0) + 1;
    
    // 统计同步状态
    const syncStatusName = GoodsSyncStatusEnum.getSyncStatusName(record.sync_status);
    stats.syncStatuses[syncStatusName] = (stats.syncStatuses[syncStatusName] || 0) + 1;
  });
  
  return stats;
}

// 示例5：验证输入数据
function validateProductData(data) {
  const errors = [];
  
  // 验证来源类型
  if (data.source_type !== undefined && !GoodsSourceTypeEnum.isValid(data.source_type)) {
    errors.push(`无效的来源类型: ${data.source_type}`);
  }
  
  // 验证推送状态
  if (data.push_status !== undefined && !GoodsSyncStatusEnum.isValidPushStatus(data.push_status)) {
    errors.push(`无效的推送状态: ${data.push_status}`);
  }
  
  // 验证同步状态
  if (data.sync_status !== undefined && !GoodsSyncStatusEnum.isValidSyncStatus(data.sync_status)) {
    errors.push(`无效的同步状态: ${data.sync_status}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// 运行示例
const sampleProduct = createThirdPartyProduct({
  name: '测试商品',
  channelId: '123',
  platformId: '456',
  storeId: '789'
});

const pushUpdate = updatePushStatus('sku123', 'platform456', false, '接口超时');

const retryQuery = findRetryTasks();

const sampleRecords = [
  { source_type: 0, push_status: 2, sync_status: 2 },
  { source_type: 1, push_status: 3, sync_status: 1 },
  { source_type: 1, push_status: 2, sync_status: 3 }
];
const statistics = getStatusStatistics(sampleRecords);
console.log('状态统计:', statistics);

const validation = validateProductData({ source_type: 1, push_status: 2, sync_status: 999 });
console.log('数据验证结果:', validation);

// =====================================================
// 6. 在Joi验证中使用枚举
// =====================================================

const Joi = require('joi');

const productSchema = Joi.object({
  name: Joi.string().required(),
  source_type: Joi.number().valid(
    ...Object.values(GoodsSourceTypeEnum).filter(v => typeof v === 'number')
  ).default(GoodsSourceTypeEnum.THIRD_PARTY),
  
  push_status: Joi.number().valid(
    ...Object.values(GoodsSyncStatusEnum.PUSH_STATUS)
  ).default(GoodsSyncStatusEnum.PUSH_STATUS.NOT_PUSHED),
  
  sync_status: Joi.number().valid(
    ...Object.values(GoodsSyncStatusEnum.SYNC_STATUS)
  ).default(GoodsSyncStatusEnum.SYNC_STATUS.NOT_SYNCED)
});

console.log('\n=== Joi验证示例 ===');
const validationResult = productSchema.validate({
  name: '测试商品',
  source_type: GoodsSourceTypeEnum.THIRD_PARTY,
  push_status: GoodsSyncStatusEnum.PUSH_STATUS.PUSH_SUCCESS
});

console.log('Joi验证结果:', validationResult);

console.log('\n=== 枚举使用示例完成 ===');
