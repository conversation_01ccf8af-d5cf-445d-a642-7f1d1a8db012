-- =====================================================
-- 第三方商品多平台关联 - 使用示例
-- =====================================================

-- =====================================================
-- 0. 状态常量说明
-- =====================================================

-- source_type 商品来源类型 (GoodsSourceTypeEnum)：
-- 0 = 系统订单 (GoodsSourceTypeEnum.SYSTEM)
-- 1 = 第三方订单 (GoodsSourceTypeEnum.THIRD_PARTY)

-- push_status 推送状态 (GoodsSyncStatusEnum.PUSH_STATUS)：
-- 0 = 未推送 (NOT_PUSHED)
-- 1 = 推送中 (PUSHING)
-- 2 = 推送成功 (PUSH_SUCCESS)
-- 3 = 推送失败 (PUSH_FAILED)

-- sync_status 同步状态 (GoodsSyncStatusEnum.SYNC_STATUS)：
-- 0 = 未同步 (NOT_SYNCED)
-- 1 = 同步中 (SYNCING)
-- 2 = 同步成功 (SYNC_SUCCESS)
-- 3 = 同步失败 (SYNC_FAILED)

-- =====================================================
-- 1. 查询示例
-- =====================================================

-- 示例1：查询某个SKU关联了哪些平台
SELECT 
    sku.sku_code,
    c.name as channel_name,
    p.name as platform_name,
    s.name as store_name,
    spr.push_status,
    spr.sync_status,
    spr.platform_sku_id,
    spr.is_enabled
FROM "base"."goods_skus" sku
JOIN "base"."goods_sku_platform_relation" spr ON sku.id = spr.goods_sku_id
JOIN "base"."channel" c ON spr.channel_id = c.id
JOIN "base"."platform" p ON spr.platform_id = p.id
JOIN "base"."store" s ON spr.store_id = s.id
WHERE sku.sku_code = 'SKU001'
  AND spr.deleted_at IS NULL;

-- 示例2：查询第三方商品及其关联渠道
SELECT 
    spu.spu_code,
    spu.name,
    COUNT(DISTINCT spr.channel_id) as channel_count,
    COUNT(DISTINCT spr.platform_id) as platform_count,
    COUNT(DISTINCT spr.store_id) as store_count,
    STRING_AGG(DISTINCT c.name, ', ') as channels
FROM "base"."goods_spus" spu
JOIN "base"."goods_skus" sku ON spu.id = sku.goods_spu_id
JOIN "base"."goods_sku_platform_relation" spr ON sku.id = spr.goods_sku_id
JOIN "base"."channel" c ON spr.channel_id = c.id
WHERE spu.source_type = 1  -- 第三方订单 (GoodsSourceTypeEnum.THIRD_PARTY)
  AND spu.deleted_at IS NULL
  AND sku.deleted_at IS NULL
  AND spr.deleted_at IS NULL
GROUP BY spu.id, spu.spu_code, spu.name
ORDER BY spu.created_at DESC;

-- 示例3：查询某个平台的所有商品
SELECT 
    spu.spu_code,
    spu.name,
    sku.sku_code,
    p.name as platform_name,
    s.name as store_name,
    spr.push_status,
    spr.sync_status
FROM "base"."goods_spus" spu
JOIN "base"."goods_skus" sku ON spu.id = sku.goods_spu_id
JOIN "base"."goods_sku_platform_relation" spr ON sku.id = spr.goods_sku_id
JOIN "base"."platform" p ON spr.platform_id = p.id
JOIN "base"."store" s ON spr.store_id = s.id
WHERE spr.platform_id = 123456789  -- 指定平台ID
  AND spr.deleted_at IS NULL
  AND sku.deleted_at IS NULL
  AND spu.deleted_at IS NULL
ORDER BY spu.created_at DESC;

-- =====================================================
-- 2. 同步状态管理示例
-- =====================================================

-- 示例4：查询需要推送的SKU
SELECT 
    sku.sku_code,
    p.name as platform_name,
    spr.push_status,
    spr.push_retry_count,
    spr.push_error_msg
FROM "base"."goods_skus" sku
JOIN "base"."goods_sku_platform_relation" spr ON sku.id = spr.goods_sku_id
JOIN "base"."platform" p ON spr.platform_id = p.id
WHERE spr.push_status IN (0, 3)  -- 未推送或推送失败
  AND spr.push_retry_count < 3   -- 重试次数小于3次
  AND spr.deleted_at IS NULL
ORDER BY spr.push_time ASC NULLS FIRST
LIMIT 10;

-- 示例5：查询需要同步的SKU
SELECT 
    sku.sku_code,
    p.name as platform_name,
    spr.sync_status,
    spr.sync_retry_count,
    spr.platform_sku_id,
    spr.sync_error_msg
FROM "base"."goods_skus" sku
JOIN "base"."goods_sku_platform_relation" spr ON sku.id = spr.goods_sku_id
JOIN "base"."platform" p ON spr.platform_id = p.id
WHERE spr.sync_status IN (0, 3)  -- 未同步或同步失败
  AND spr.sync_retry_count < 3   -- 重试次数小于3次
  AND spr.platform_sku_id IS NOT NULL  -- 已有平台SKU ID
  AND spr.deleted_at IS NULL
ORDER BY spr.sync_time ASC NULLS FIRST
LIMIT 10;

-- =====================================================
-- 3. 状态更新示例
-- =====================================================

-- 示例6：更新推送状态为成功
UPDATE "base"."goods_sku_platform_relation" 
SET 
    push_status = 2,  -- 推送成功
    push_time = EXTRACT(epoch FROM NOW()) * 1000,
    platform_sku_id = 'PLATFORM_SKU_123',
    push_error_msg = NULL,
    updated_at = EXTRACT(epoch FROM NOW()) * 1000
WHERE goods_sku_id = 'SKU_ID' 
  AND platform_id = 'PLATFORM_ID'
  AND deleted_at IS NULL;

-- 示例7：更新推送状态为失败
UPDATE "base"."goods_sku_platform_relation" 
SET 
    push_status = 3,  -- 推送失败
    push_time = EXTRACT(epoch FROM NOW()) * 1000,
    push_error_msg = '平台接口返回错误：商品信息不完整',
    push_retry_count = push_retry_count + 1,
    updated_at = EXTRACT(epoch FROM NOW()) * 1000
WHERE goods_sku_id = 'SKU_ID' 
  AND platform_id = 'PLATFORM_ID'
  AND deleted_at IS NULL;

-- 示例8：更新同步状态为成功
UPDATE "base"."goods_sku_platform_relation" 
SET 
    sync_status = 2,  -- 同步成功
    sync_time = EXTRACT(epoch FROM NOW()) * 1000,
    platform_price = 99.99,
    platform_stock = 100,
    platform_status = 1,
    last_sync_data = '{"price": 99.99, "stock": 100, "status": "active", "updated_at": "2024-12-25T10:00:00Z"}'::jsonb,
    sync_error_msg = NULL,
    updated_at = EXTRACT(epoch FROM NOW()) * 1000
WHERE goods_sku_id = 'SKU_ID' 
  AND platform_id = 'PLATFORM_ID'
  AND deleted_at IS NULL;

-- =====================================================
-- 4. 统计分析示例
-- =====================================================

-- 示例9：统计各状态的数量
SELECT 
    '推送状态' as status_type,
    CASE push_status
        WHEN 0 THEN '未推送'
        WHEN 1 THEN '推送中'
        WHEN 2 THEN '推送成功'
        WHEN 3 THEN '推送失败'
        ELSE '未知'
    END as status_name,
    COUNT(*) as count
FROM "base"."goods_sku_platform_relation" 
WHERE deleted_at IS NULL
GROUP BY push_status

UNION ALL

SELECT 
    '同步状态' as status_type,
    CASE sync_status
        WHEN 0 THEN '未同步'
        WHEN 1 THEN '同步中'
        WHEN 2 THEN '同步成功'
        WHEN 3 THEN '同步失败'
        ELSE '未知'
    END as status_name,
    COUNT(*) as count
FROM "base"."goods_sku_platform_relation" 
WHERE deleted_at IS NULL
GROUP BY sync_status
ORDER BY status_type, count DESC;

-- 示例10：按平台统计商品数量
SELECT 
    p.name as platform_name,
    COUNT(DISTINCT spr.goods_sku_id) as sku_count,
    COUNT(DISTINCT sku.goods_spu_id) as spu_count,
    SUM(CASE WHEN spr.push_status = 2 THEN 1 ELSE 0 END) as pushed_count,
    SUM(CASE WHEN spr.sync_status = 2 THEN 1 ELSE 0 END) as synced_count
FROM "base"."goods_sku_platform_relation" spr
JOIN "base"."platform" p ON spr.platform_id = p.id
JOIN "base"."goods_skus" sku ON spr.goods_sku_id = sku.id
WHERE spr.deleted_at IS NULL
  AND sku.deleted_at IS NULL
GROUP BY p.id, p.name
ORDER BY sku_count DESC;

-- =====================================================
-- 5. 数据清理示例
-- =====================================================

-- 示例11：清理超过重试次数的失败记录（软删除）
UPDATE "base"."goods_sku_platform_relation" 
SET 
    deleted_at = EXTRACT(epoch FROM NOW()) * 1000,
    updated_at = EXTRACT(epoch FROM NOW()) * 1000
WHERE (
    (push_status = 3 AND push_retry_count >= 5) OR
    (sync_status = 3 AND sync_retry_count >= 5)
)
AND deleted_at IS NULL;

-- 示例12：重置失败状态（用于重新尝试）
UPDATE "base"."goods_sku_platform_relation" 
SET 
    push_status = 0,  -- 重置为未推送
    push_retry_count = 0,
    push_error_msg = NULL,
    updated_at = EXTRACT(epoch FROM NOW()) * 1000
WHERE push_status = 3 
  AND push_retry_count >= 3
  AND push_time < (EXTRACT(epoch FROM NOW()) - 86400) * 1000  -- 24小时前的失败记录
  AND deleted_at IS NULL;
