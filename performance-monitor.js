#!/usr/bin/env node

// 性能监控脚本
import fs from 'fs';
import path from 'path';
import { performance } from 'perf_hooks';

class PerformanceMonitor {
  constructor() {
    this.startTime = performance.now();
    this.metrics = {
      fileCount: 0,
      totalSize: 0,
      largeFiles: [],
      nodeModulesSize: 0
    };
  }

  // 分析项目文件结构
  analyzeProject() {
    console.log('🔍 分析项目性能指标...\n');
    
    this.analyzeDirectory('.');
    this.analyzeNodeModules();
    this.checkLargeFiles();
    this.generateReport();
  }

  analyzeDirectory(dir, level = 0) {
    if (level > 2) return; // 限制递归深度，提高性能

    try {
      const items = fs.readdirSync(dir, { withFileTypes: true });

      for (const item of items) {
        const fullPath = path.join(dir, item.name);

        // 跳过不需要分析的目录
        if (this.shouldSkip(item.name)) continue;

        if (item.isDirectory()) {
          this.analyzeDirectory(fullPath, level + 1);
        } else {
          this.analyzeFile(fullPath);
        }
      }
    } catch (error) {
      // 忽略无法访问的目录
    }
  }

  analyzeFile(filePath) {
    try {
      const stats = fs.statSync(filePath);
      this.metrics.fileCount++;
      this.metrics.totalSize += stats.size;
      
      // 记录大文件
      if (stats.size > 1024 * 1024) { // 大于 1MB
        this.metrics.largeFiles.push({
          path: filePath,
          size: this.formatSize(stats.size)
        });
      }
    } catch (error) {
      // 忽略无法访问的文件
    }
  }

  analyzeNodeModules() {
    try {
      const nodeModulesPath = './node_modules';
      if (fs.existsSync(nodeModulesPath)) {
        const stats = this.getDirectorySize(nodeModulesPath);
        this.metrics.nodeModulesSize = stats;
      }
    } catch (error) {
      console.warn('无法分析 node_modules 目录');
    }
  }

  getDirectorySize(dirPath) {
    let totalSize = 0;
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item.name);
      try {
        if (item.isDirectory()) {
          totalSize += this.getDirectorySize(fullPath);
        } else {
          const stats = fs.statSync(fullPath);
          totalSize += stats.size;
        }
      } catch (error) {
        // 忽略无法访问的文件
      }
    }
    
    return totalSize;
  }

  shouldSkip(name) {
    const skipList = [
      'node_modules', '.git', '.nuxt', 'dist', '.output',
      'logs', 'backups', 'coverage', '.vscode', '.idea'
    ];
    return skipList.includes(name) || name.startsWith('.');
  }

  checkLargeFiles() {
    // 按大小排序
    this.metrics.largeFiles.sort((a, b) => {
      const sizeA = parseInt(a.size.replace(/[^\d]/g, ''));
      const sizeB = parseInt(b.size.replace(/[^\d]/g, ''));
      return sizeB - sizeA;
    });
  }

  formatSize(bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  generateReport() {
    const endTime = performance.now();
    const analysisTime = Math.round(endTime - this.startTime);
    
    console.log('📊 项目性能分析报告');
    console.log('='.repeat(50));
    console.log(`📁 总文件数: ${this.metrics.fileCount.toLocaleString()}`);
    console.log(`📦 项目总大小: ${this.formatSize(this.metrics.totalSize)}`);
    console.log(`🗂️  node_modules 大小: ${this.formatSize(this.metrics.nodeModulesSize)}`);
    console.log(`⏱️  分析耗时: ${analysisTime}ms\n`);
    
    if (this.metrics.largeFiles.length > 0) {
      console.log('🔍 大文件列表 (>1MB):');
      console.log('-'.repeat(50));
      this.metrics.largeFiles.slice(0, 10).forEach(file => {
        console.log(`  ${file.size.padEnd(10)} ${file.path}`);
      });
      
      if (this.metrics.largeFiles.length > 10) {
        console.log(`  ... 还有 ${this.metrics.largeFiles.length - 10} 个大文件`);
      }
      console.log();
    }
    
    this.generateRecommendations();
  }

  generateRecommendations() {
    console.log('💡 性能优化建议:');
    console.log('-'.repeat(50));
    
    if (this.metrics.nodeModulesSize > 500 * 1024 * 1024) {
      console.log('  ⚠️  node_modules 过大，考虑清理未使用的依赖');
    }
    
    if (this.metrics.largeFiles.length > 5) {
      console.log('  ⚠️  项目中有较多大文件，可能影响开发性能');
    }
    
    if (this.metrics.fileCount > 10000) {
      console.log('  ⚠️  文件数量较多，建议优化文件监听配置');
    }
    
    console.log('  ✅ 使用 npm run dev:fast 启动优化的开发服务器');
    console.log('  ✅ 确保关闭不必要的 VS Code 插件');
    console.log('  ✅ 考虑使用 SSD 硬盘提高 I/O 性能');
    console.log('  ✅ 增加系统内存以提高缓存效果\n');
  }
}

// 运行分析
const monitor = new PerformanceMonitor();
monitor.analyzeProject();
