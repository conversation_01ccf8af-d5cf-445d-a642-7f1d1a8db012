/**
 * 测试第三方商品可选字段功能
 * 验证新增的SPU和SKU可选字段是否正常工作
 */

const { ThirdPartyGoodsSpuDto } = require('./server/apps/master/dto/ThirdPartyGoodsSpuDto');

// 测试数据 - 包含所有新增的可选字段
const testCreateData = {
  name: "测试商品",
  channelId: "123456789",
  platformId: "987654321", 
  storeId: "456789123",
  subtitle: "测试副标题",
  description: "基础描述",
  spuImages: [
    "https://example.com/spu-image1.jpg",
    "https://example.com/spu-image2.jpg"
  ],
  richDescription: "<p>这是富文本详情</p><ul><li>特点1</li><li>特点2</li></ul>",
  skus: [
    {
      skuCode: "SKU001",
      salesPrice: 99.99,
      stock: 100,
      marketPrice: 129.99,
      image: "https://example.com/sku1-image.jpg",
      weight: 0.5,
      volume: 0.1,
      unit: "件"
    },
    {
      skuCode: "SKU002", 
      salesPrice: 89.99,
      stock: 50,
      marketPrice: 119.99,
      image: "https://example.com/sku2-image.jpg",
      weight: 0.6,
      volume: 0.12,
      unit: "盒"
    }
  ]
};

const testUpdateData = {
  name: "更新后的商品名称",
  spuImages: [
    "https://example.com/updated-spu-image1.jpg"
  ],
  richDescription: "<p>更新后的富文本详情</p>",
  skus: [
    {
      id: "123456789",
      skuCode: "SKU001-UPDATED",
      salesPrice: 109.99,
      stock: 150,
      marketPrice: 139.99,
      image: "https://example.com/updated-sku-image.jpg",
      weight: 0.7,
      volume: 0.15,
      unit: "套"
    }
  ]
};

// 测试创建验证
console.log('=== 测试创建商品验证 ===');
const createResult = ThirdPartyGoodsSpuDto.validateCreate(testCreateData);
if (createResult.error) {
  console.error('创建验证失败:', createResult.error.details);
} else {
  console.log('✅ 创建验证通过');
  console.log('验证后的数据:', JSON.stringify(createResult.value, null, 2));
}

console.log('\n=== 测试更新商品验证 ===');
const updateResult = ThirdPartyGoodsSpuDto.validateUpdate(testUpdateData);
if (updateResult.error) {
  console.error('更新验证失败:', updateResult.error.details);
} else {
  console.log('✅ 更新验证通过');
  console.log('验证后的数据:', JSON.stringify(updateResult.value, null, 2));
}

// 测试只包含必填字段的情况
console.log('\n=== 测试最小必填字段 ===');
const minimalData = {
  name: "最小测试商品",
  channelId: "123456789",
  platformId: "987654321",
  storeId: "456789123",
  skus: [
    {
      skuCode: "SKU001",
      salesPrice: 99.99,
      stock: 100
    }
  ]
};

const minimalResult = ThirdPartyGoodsSpuDto.validateCreate(minimalData);
if (minimalResult.error) {
  console.error('最小字段验证失败:', minimalResult.error.details);
} else {
  console.log('✅ 最小字段验证通过');
}

console.log('\n=== 测试完成 ===');
