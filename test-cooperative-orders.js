#!/usr/bin/env node

/**
 * 服务商订单列表功能测试脚本
 * 测试后端API和权限控制
 */

import axios from 'axios';

// 配置
const BASE_URL = 'http://localhost:4000/api/v1';
const TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjE5NDI1ODQwMzg2NTAwNjA4MCIsInVzZXJuYW1lIjoiYWRtaW4iLCJuaWNrbmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsInJvbGUiOiIxOTQyNTg0MDM3NzY5MjU2OTYiLCJkZXB0X2lkIjpudWxsLCJpYXQiOjE3NTA2NjcwOTksImV4cCI6MTc1MDc1MzQ5OX0.3Miwga4Uwkyl7qL6JXVumW2cXI7QFIpHqT07N1UDMb8';

console.log('🔍 测试配置:');
console.log('API Base URL:', BASE_URL);
console.log('用户ID: 194258403865006080 (从JWT token解析)');
console.log('前端页面: http://localhost:3000/master/cooperative/ordermanagement/orderlist');
console.log('');

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// 测试用例
const tests = [
  {
    name: '获取服务商订单列表 - 基础测试',
    method: 'GET',
    url: '/master/cooperative/orders',
    params: {},
    expectedStatus: 200,
    validate: (response) => {
      const { data } = response.data;
      return data && Array.isArray(data.items) && typeof data.pageInfo === 'object';
    }
  },
  {
    name: '获取服务商订单列表 - 分页测试',
    method: 'GET',
    url: '/master/cooperative/orders',
    params: { page: 1, pageSize: 5 },
    expectedStatus: 200,
    validate: (response) => {
      const { data } = response.data;
      return data.pageInfo.pageSize === 5 && data.pageInfo.currentPage === 1;
    }
  },
  {
    name: '获取服务商订单列表 - 排序测试',
    method: 'GET',
    url: '/master/cooperative/orders',
    params: { sortField: 'created_at', sortOrder: 'asc' },
    expectedStatus: 200,
    validate: (response) => {
      return response.data.code === 200;
    }
  },
  {
    name: '获取服务商订单详情 - 有权限订单',
    method: 'GET',
    url: '/master/cooperative/orders/194274160413904896',
    params: {},
    expectedStatus: 200,
    validate: (response) => {
      const { data } = response.data;
      return data && data.id === '194274160413904896';
    }
  },
  {
    name: '获取服务商订单详情 - 不存在的订单（应该返回404）',
    method: 'GET',
    url: '/master/cooperative/orders/999999999999999999',
    params: {},
    expectedStatus: 404,
    validate: (response) => {
      return response.data.code === 404;
    }
  },
  {
    name: '获取服务商订单列表 - 订单状态筛选',
    method: 'GET',
    url: '/master/cooperative/orders',
    params: { orderStatus: 0 },
    expectedStatus: 200,
    validate: (response) => {
      return response.data.code === 200;
    }
  },
  {
    name: '获取服务商订单列表 - 订单号筛选',
    method: 'GET',
    url: '/master/cooperative/orders',
    params: { orderNumber: '194274160413904896' },
    expectedStatus: 200,
    validate: (response) => {
      const { data } = response.data;
      return data && data.items.length === 1 && data.items[0].id === '194274160413904896';
    }
  },
  {
    name: '获取服务商订单列表 - 无效订单号筛选',
    method: 'GET',
    url: '/master/cooperative/orders',
    params: { orderNumber: 'invalid_order_id' },
    expectedStatus: 200,
    validate: (response) => {
      const { data } = response.data;
      return data && data.items.length === 0 && data.pageInfo.total === 0;
    }
  },
  {
    name: '获取服务商订单列表 - 时间范围筛选',
    method: 'GET',
    url: '/master/cooperative/orders',
    params: { timeRange: 'recent1w' },
    expectedStatus: 200,
    validate: (response) => {
      return response.data.code === 200;
    }
  }
];

// 运行测试
async function runTests() {
  console.log('🚀 开始测试服务商订单列表功能...\n');
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      console.log(`📋 测试: ${test.name}`);
      
      let response;
      if (test.method === 'GET') {
        response = await api.get(test.url, { params: test.params });
      } else if (test.method === 'POST') {
        response = await api.post(test.url, test.params);
      }
      
      // 检查状态码
      if (response.status !== test.expectedStatus) {
        console.log(`❌ 失败: 期望状态码 ${test.expectedStatus}, 实际 ${response.status}`);
        failed++;
        continue;
      }
      
      // 运行自定义验证
      if (test.validate && !test.validate(response)) {
        console.log(`❌ 失败: 响应数据验证失败`);
        console.log(`   响应数据:`, JSON.stringify(response.data, null, 2));
        failed++;
        continue;
      }
      
      console.log(`✅ 通过`);
      passed++;
      
    } catch (error) {
      if (error.response && error.response.status === test.expectedStatus) {
        // 期望的错误状态码
        if (test.validate && !test.validate(error.response)) {
          console.log(`❌ 失败: 错误响应验证失败`);
          failed++;
        } else {
          console.log(`✅ 通过 (期望的错误状态)`);
          passed++;
        }
      } else {
        console.log(`❌ 失败: ${error.message}`);
        if (error.response) {
          console.log(`   状态码: ${error.response.status}`);
          console.log(`   响应: ${JSON.stringify(error.response.data, null, 2)}`);
        }
        failed++;
      }
    }
    
    console.log(''); // 空行分隔
  }
  
  // 输出测试结果
  console.log('📊 测试结果:');
  console.log(`✅ 通过: ${passed}`);
  console.log(`❌ 失败: ${failed}`);
  console.log(`📈 成功率: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 所有测试通过！服务商订单列表功能正常工作。');
  } else {
    console.log('\n⚠️  部分测试失败，请检查相关功能。');
  }
}

// 运行测试
runTests().catch(console.error);
