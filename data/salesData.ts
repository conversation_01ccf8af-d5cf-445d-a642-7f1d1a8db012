/**
 * 聚灵云销售报告数据源
 */
export default function getSalesData() {
  // KPI数据
  const getKpiData = () => {
    return {
      salesAmount: {
        value: 317454,
        growth: 5.62,
        trend: [30, 40, 35, 50, 49, 60, 70, 91, 125, 160, 200, 220, 250, 270, 280, 300, 340, 350, 360, 317]
      },
      orderCount: {
        value: 12,
        growth: 50.00,
        trend: [3, 4, 5, 4, 5, 6, 7, 8, 7, 9, 10, 11, 12]
      },
      salesTarget: {
        target: 10000,
        current: 4742404,
        rate: 47424.04
      }
    }
  }

  // 工作台待处理事项
  const getPendingItems = () => {
    return [
      { name: '待发货', count: 5, icon: 'file' },
      { name: '待开票', count: 3, icon: 'file' },
      { name: '等待指派', count: 2, icon: 'user-add' },
      { name: '物流异常', count: 1, icon: 'exclamation-circle' },
      { name: '超时未发货', count: 1, icon: 'clock-circle' }
    ]
  }

  // 渠道销售额数据
  const getChannelSalesData = () => {
    return {
      channels: ['晨光平台', '京东平台', '天猫平台', '拼多多', '其他'],
      selected: '晨光平台',
      data: [
        { name: '晨光平台', value: 4280 },
        { name: '京东平台', value: 2350 },
        { name: '天猫平台', value: 1890 },
        { name: '拼多多', value: 980 },
        { name: '其他', value: 500 }
      ]
    }
  }

  // 平台销售额数据
  const getPlatformSalesData = () => {
    return {
      data: [
        { name: '晨光平台', value: 4280 },
        { name: '京东平台', value: 2350 },
        { name: '天猫平台', value: 1890 },
        { name: '拼多多', value: 980 },
        { name: '其他', value: 500 }
      ]
    }
  }

  // 订单流量对比数据
  const getOrderFlowData = () => {
    return {
      total: 10000,
      data: [
        { name: '自然流量', value: 3560 },
        { name: '广告流量', value: 2480 },
        { name: '社交媒体', value: 1890 },
        { name: '站内推荐', value: 1250 },
        { name: '其他来源', value: 820 }
      ]
    }
  }

  // 热销类目词云数据
  const getHotCategoriesData = () => [
    { name: '办公用品', value: 100 },
    { name: '文具', value: 85 },
    { name: '笔记本', value: 70 },
    { name: '钢笔', value: 65 },
    { name: '铅笔', value: 60 },
    { name: '橡皮擦', value: 55 },
    { name: '文件夹', value: 50 },
    { name: '书包', value: 45 },
    { name: '尺子', value: 40 },
    { name: '计算器', value: 35 },
    { name: '胶水', value: 30 },
    { name: '剪刀', value: 25 },
    { name: '订书机', value: 20 },
    { name: '记事本', value: 15 },
    { name: '便利贴', value: 10 }
  ]

  // 业绩排行数据
  const getSalesRankingData = () => {
    return [
      { name: '张三', department: '销售一部', amount: 12580, target: 15000, rate: 83.9 },
      { name: '李四', department: '销售二部', amount: 11420, target: 12000, rate: 95.2 },
      { name: '王五', department: '销售三部', amount: 10890, target: 12000, rate: 90.8 },
      { name: '赵六', department: '销售一部', amount: 9750, target: 10000, rate: 97.5 },
      { name: '钱七', department: '销售二部', amount: 8960, target: 10000, rate: 89.6 }
    ]
  }

  // 热销商品排行数据
  const getTopProductsData = () => {
    return [
      { name: '晨光中性笔套装', category: '文具', sales: 2580, growth: 15.2, amount: 25800, rate: 25.8 },
      { name: '晨光A4纸', category: '办公用品', sales: 2350, growth: 12.8, amount: 23500, rate: 23.5 },
      { name: '晨光彩色铅笔', category: '文具', sales: 1980, growth: 10.5, amount: 19800, rate: 19.8 },
      { name: '晨光笔记本', category: '办公用品', sales: 1750, growth: 8.9, amount: 17500, rate: 17.5 },
      { name: '晨光文件夹', category: '办公用品', sales: 1620, growth: 7.5, amount: 16200, rate: 16.2 }
    ]
  }

  // 本月热销省份TOP5
  const getTopProvincesData = () => {
    return {
      type: 'salesAmount',
      data: [
        { name: '广东省', sales: 5280, percentage: 18.5, value: 5280, rate: 18.5 },
        { name: '江苏省', sales: 4350, percentage: 15.2, value: 4350, rate: 15.2 },
        { name: '浙江省', sales: 3980, percentage: 13.9, value: 3980, rate: 13.9 },
        { name: '山东省', sales: 3650, percentage: 12.8, value: 3650, rate: 12.8 },
        { name: '四川省', sales: 2890, percentage: 10.1, value: 2890, rate: 10.1 }
      ]
    }
  }

  // 月度销售数据
  const getMonthlySalesData = () => {
    return {
      months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      data: [
        {
          name: '2024年销售订单',
          data: [320, 350, 360, 370, 380, 390, 400, 410, 420, 430, 440, 450]
        },
        {
          name: '2024年售后订单',
          data: [30, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45]
        },
        {
          name: '2025年销售订单',
          data: [450, 460, 470, 480, 0, 0, 0, 0, 0, 0, 0, 0]
        },
        {
          name: '2025年售后订单',
          data: [45, 46, 47, 48, 0, 0, 0, 0, 0, 0, 0, 0]
        }
      ]
    }
  }

  // 财务数据概览
  const getFinancialOverviewData = () => {
    return {
      receivables: {
        total: 1370032.87,
        received: 870032.87,
        pending: 500000.00
      },
      payables: {
        total: 678270.64,
        paid: 478270.64,
        pending: 200000.00
      }
    }
  }

  return {
    getKpiData,
    getPendingItems,
    getChannelSalesData,
    getPlatformSalesData,
    getOrderFlowData,
    getHotCategoriesData,
    getSalesRankingData,
    getTopProductsData,
    getTopProvincesData,
    getMonthlySalesData,
    getFinancialOverviewData
  }
}
