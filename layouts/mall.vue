<template>
  <div class="mall-layout overflow-x-hidden">
    <!-- 顶部导航栏 -->
    <!-- 顶部导航栏 - 在移动端隐藏 -->
    <div class="header-nav text-gray-600 hidden md:block">
      <div class="container mx-auto px-4 flex justify-between items-center">
        <div class="flex space-x-4">
          <LocationSelector @select="handleProvinceSelect" />
          <template v-if="userStore.loggedIn">
            <span>你好，{{ userStore.user.nickname }}</span>
            <NuxtLink to="#" @click.prevent="logout" class="hover:text-red-600">退出登录</NuxtLink>
          </template>
          <template v-else>
            <NuxtLink to="/mall/login" class="hover:text-red-600">你好，请登录</NuxtLink>
            <NuxtLink to="/mall/register" class="hover:text-red-600">免费注册</NuxtLink>
          </template>
        </div>
        <div class="flex space-x-6">
          <!-- 登录后才显示的个人相关链接 -->
          <template v-if="userStore.loggedIn">
            <NuxtLink to="/mall/user/orders" class="hover:text-red-600">我的订单</NuxtLink>
            <NuxtLink to="/mall/user/profile" class="hover:text-red-600">我的商城</NuxtLink>
            <NuxtLink to="/mall/user/center" class="hover:text-red-600">会员中心</NuxtLink>
          </template>
          <!-- 未登录时显示登录提示 -->
          <template v-else>
            <NuxtLink to="/mall/login" class="hover:text-red-600">我的订单</NuxtLink>
            <NuxtLink to="/mall/login" class="hover:text-red-600">我的商城</NuxtLink>
            <NuxtLink to="/mall/login" class="hover:text-red-600">会员中心</NuxtLink>
          </template>
          <!-- 通用链接，不管是否登录都显示 -->
          <NuxtLink to="/mall/business/purchase" class="hover:text-red-600">企业采购</NuxtLink>
          <NuxtLink to="/mall/service" class="hover:text-red-600">客户服务</NuxtLink>
          <NuxtLink to="/mall/sitemap" class="hover:text-red-600">网站导航</NuxtLink>
          <NuxtLink to="/mall/mobile" class="hover:text-red-600">手机商城</NuxtLink>
        </div>
      </div>
    </div>

    <!-- 主要头部带Logo和搜索 -->
    <!-- 主要头部带Logo和搜索 - 响应式布局 -->
    <div class="main-header py-4 w-full">
      <div class="container mx-auto px-4 w-full">
        <!-- 移动端布局 -->
        <div class="md:hidden">
          <div class="flex items-center justify-between mb-3">
            <div class="logo">
              <NuxtLink to="/mall" class="no-underline">
                <img src="https://jlc-4.oss-cn-guangzhou.aliyuncs.com/avatar/cf49eb9e51b3c935200b32595fc5bf56.png" alt="logo" class="w-auto h-6">
              </NuxtLink>
            </div>
            <div class="flex items-center">
              <NuxtLink to="/mall/user/profile" class="mr-4 text-gray-600">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>
              </NuxtLink>
              <NuxtLink to="/mall/cart" class="relative">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>
                <span v-if="cartStore.totalCount > 0" class="absolute -top-1 -right-1 bg-red-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {{ cartStore.totalCount }}
                </span>
              </NuxtLink>
            </div>
          </div>
          <div class="search-box w-full">
            <div class="relative w-full">
              <div class="flex">
                <input
                  type="text"
                  v-model="searchKeyword"
                  placeholder="请输入搜索关键词"
                  class="flex-1 border-2 border-r-0 border-red-600 h-10 px-3 rounded-l-full focus:outline-none"
                  @keyup.enter="handleSearch"
                >
                <button
                  @click="handleSearch"
                  class="search-btn h-10 w-16 text-white bg-red-600 rounded-r-full border-2 border-red-600 hover:bg-red-700"
                >搜索</button>
              </div>
            </div>
          </div>
          <div class="flex justify-between mt-3 text-sm">
            <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink>
            <NuxtLink to="/mall/category" class="hover:text-red-600">全部分类</NuxtLink>
            <NuxtLink to="#" class="hover:text-red-600">企业采购</NuxtLink>
            <NuxtLink to="/mall/user/orders" class="hover:text-red-600">我的订单</NuxtLink>
          </div>
        </div>
        
        <!-- 桌面端布局 -->
        <div class="hidden md:flex items-center">
          <div class="logo mr-10">
            <NuxtLink to="/mall" class="no-underline">
              <img src="https://jlc-4.oss-cn-guangzhou.aliyuncs.com/avatar/cf49eb9e51b3c935200b32595fc5bf56.png" alt="logo" class="w-auto h-8">
              <div class="text-xs text-gray-500 pl-2">好货·好价·优服务</div>
            </NuxtLink>
          </div>
          <div class="flex space-x-6 text-sm mr-6">
            <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink>
            <NuxtLink to="/mall/category" class="hover:text-red-600">全部分类</NuxtLink>
            <NuxtLink to="#" class="hover:text-red-600">企业采购</NuxtLink>
          </div>
          <div class="search-box flex-1 flex items-center">
            <div class="relative w-full">
              <div class="flex">
                <input
                  type="text"
                  v-model="searchKeyword"
                  placeholder="请输入搜索关键词"
                  class="flex-1 border-2 border-r-0 border-red-600 h-10 px-3 rounded-l-full focus:outline-none"
                  @keyup.enter="handleSearch"
                >
                <button
                  @click="handleSearch"
                  class="search-btn h-10 w-20 text-white bg-red-600 rounded-r-full border-2 border-red-600 hover:bg-red-700"
                >搜索</button>
              </div>
            </div>
          </div>
          <div class="ml-4">
            <NuxtLink to="/mall/cart" class="cart-btn flex items-center hover:text-red-600">
              <span class="cart-icon mr-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>
              </span>
              <span>我的购物车</span>
              <span class="cart-count ml-1 bg-red-600 text-white text-xs rounded-full px-1.5">{{ cartStore.totalCount || 0 }}</span>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="w-full overflow-x-hidden">
      <slot />
    </div>

    <!-- 页脚 -->
    <div class="footer mt-8 py-6 border-t">
      <div class="container mx-auto px-4">
        <!-- 服务特点区块 - 仅在平板和桌面端显示 -->
        <div class="hidden md:grid grid-cols-2 md:grid-cols-5 gap-4 md:gap-8 mb-6">
          <div v-for="(footerItem, index) in footerItems" :key="index" class="flex">
            <div class="icon mr-2 text-red-600">
              <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <div class="title font-bold mb-1">{{ footerItem.title }}</div>
              <div class="desc text-gray-500">{{ footerItem.desc }}</div>
            </div>
          </div>
        </div>
        
        <!-- 页脚链接区块 - 仅在平板和桌面端显示 -->
        <div class="hidden md:grid grid-cols-2 md:grid-cols-5 gap-4 md:gap-8 text-gray-600">
          <div v-for="(section, index) in footerLinks" :key="index">
            <!-- 分类标题 - 如果有categoryLink则可点击 -->
            <div class="title font-bold mb-2">
              <template v-if="section.categoryLink">
                <NuxtLink :to="section.categoryLink" class="hover:text-red-600">
                  {{ section.title }}
                </NuxtLink>
              </template>
              <template v-else>
                {{ section.title }}
              </template>
            </div>

            <!-- 链接列表 -->
            <div v-for="(link, linkIndex) in section.links" :key="linkIndex" class="link mb-1">
              <template v-if="section.isNews && typeof link === 'object'">
                <NuxtLink :to="link.url" class="hover:text-red-600 text-sm" :title="link.title">
                  {{ link.title.length > 15 ? link.title.substring(0, 15) + '...' : link.title }}
                </NuxtLink>
              </template>
              <template v-else>
                <NuxtLink to="#" class="hover:text-red-600">{{ link }}</NuxtLink>
              </template>
            </div>

            <!-- 如果是新闻分类但没有文章，显示提示 -->
            <div v-if="section.isNews && section.links.length === 0" class="text-xs text-gray-400">
              暂无文章
            </div>
          </div>
        </div>
        
        <div class="text-center text-gray-500 text-xs mt-8">
          <div class="mb-2 flex flex-wrap justify-center">
            <NuxtLink to="#" class="mx-2 mb-1 hover:text-red-600">关于我们</NuxtLink>
            <NuxtLink to="#" class="mx-2 mb-1 hover:text-red-600">联系我们</NuxtLink>
            <NuxtLink to="#" class="mx-2 mb-1 hover:text-red-600">人才招聘</NuxtLink>
            <NuxtLink to="#" class="mx-2 mb-1 hover:text-red-600">商家入驻</NuxtLink>
            <NuxtLink to="#" class="mx-2 mb-1 hover:text-red-600">广告服务</NuxtLink>
            <NuxtLink to="#" class="mx-2 mb-1 hover:text-red-600">手机商城</NuxtLink>
            <NuxtLink to="#" class="mx-2 mb-1 hover:text-red-600">友情链接</NuxtLink>
            <NuxtLink to="#" class="mx-2 mb-1 hover:text-red-600">销售联盟</NuxtLink>
          </div>
          <div>Copyright 2004-2025 聚灵云 版权所有</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';
import { useMallCartStore } from '~/store/mall/cart';
import { useMallGoodsStore } from '~/store/mall/goods';
import LocationSelector from '~/components/mall/user/LocationSelector.vue';
import { useMallLocalStore } from '~/store/mall/local';
import mallApi from '@/api/mall';

const localStore = useMallLocalStore()
const location = ref('北京');
const userStore = useMallUserStore();
const cartStore = useMallCartStore();
const goodsStore = useMallGoodsStore();
const router = useRouter();

// 搜索关键词
const searchKeyword = ref('');

// 新闻相关数据
const newsCategories = ref([]);
const newsArticles = ref([]);

// 处理搜索
const handleSearch = () => {
  // if (!searchKeyword.value.trim()) {
  //   return;
  // }

  // 调用商品store的查询方法
  goodsStore.searchParams.keyword = searchKeyword.value.trim();

  if (router.currentRoute.value.path.startsWith("/mall/category/")) {
    return;
  }
  // 跳转到商品列表页面，带上搜索关键词参数
  router.push({
    path: '/mall/category/0'
  });
};

// 获取新闻分类
const fetchNewsCategories = async () => {
  try {
    const response = await mallApi.news.getCategories({ limit: 5 });
    if (response.code === 200) {
      newsCategories.value = response.data;
    }
  } catch (error) {
    console.error('获取新闻分类失败:', error);
  }
};

// 获取新闻文章
const fetchNewsArticles = async () => {
  try {
    // 获取更多文章以确保每个分类都有足够的文章
    const response = await mallApi.news.getLatestArticles({ limit: 25 });
    if (response.code === 200) {
      newsArticles.value = response.data.map(article => ({
        ...article,
        category_id: article.category_id || article.category?.id
      }));
    }
  } catch (error) {
    console.error('获取新闻文章失败:', error);
  }
};

// 在组件挂载后恢复用户会话
onMounted(async () => {
  try {
    console.log('商城布局初始化，开始恢复用户会话...');

    // 检查本地存储中的用户信息
    if (process.client) {
      const storedToken = localStorage.getItem('mall_user_token');
      console.log('本地存储中的用户令牌:', storedToken);

      // 如果已经有用户信息，则直接设置登录状态
      if (userStore.user) {
        console.log('已有用户信息，无需恢复会话:', userStore.user);
        return;
      }

      // 如果有令牌但没有用户信息，才进行恢复
      if (storedToken) {
        console.log('开始恢复用户会话...');
        const result = await userStore.restoreSession();
        console.log('恢复用户会话结果:', result, '用户状态:', userStore.loggedIn);
      } else {
        console.log('没有找到用户令牌，无法恢复会话');
      }
    }

    // 获取新闻数据
    await fetchNewsCategories();
    await fetchNewsArticles();
  } catch (error) {
    console.error('恢复用户会话失败:', error);
  }
});

// 退出登录
const logout = () => {
  userStore.logout();
};

const footerItems = ref([
  { title: '品质保障', desc: '品质护航 购物无忧' },
  { title: '七天无理由退换', desc: '为您提供售后无忧服务' },
  { title: '特色服务', desc: '精选特色 企业专享' },
  { title: '帮助中心', desc: '您的购物指南' },
  { title: '商家服务', desc: '商家入驻 培训中心' }
]);

// 计算属性：动态生成footerLinks
const footerLinks = computed(() => {
  const staticLinks = [

  ];

  // 动态生成新闻分类sections（前5个分类）
  const newsLinks = newsCategories.value.slice(0, 5).map(category => {
    // 获取该分类下的文章（最多5篇）
    const categoryArticles = newsArticles.value
      .filter(article => article.category_id === category.id)
      .slice(0, 5);

    return {
      title: category.name,
      categoryLink: `/mall/news?category=${category.id}`,
      links: categoryArticles.map(article => ({
        title: article.title,
        url: `/mall/news/${article.id}`
      })),
      isNews: true
    };
  });

  return [...staticLinks, ...newsLinks];
});
const handleProvinceSelect = (province) => {
  console.log('选择的省份:', province);
  localStore.updateLocationInfo(province.code, province.name);
}

</script>

<style scoped>
body {
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  background-color: #f5f5f5;
  color: #333;
}
.jd-red {
  background-color: #e4393c;
  color: white;
}
.jd-price {
  color: #e4393c;
  font-weight: bold;
}
.category-menu {
  /* background-color: #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1); */
}
.category-item {
  padding: 0.5rem 0.625rem;
  font-size: 0.875rem;
}
.category-item:hover {
  background-color: #f7f7f7;
  color: #e4393c;
}
.product-card {
  background-color: #fff;
  transition: all 0.3s;
  border-radius: 2px;
}
.product-card:hover {
  box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,0.1);
  transform: translateY(-0.125rem);
}
.header-nav {
  font-size: 0.75rem;
  height: 2rem;
  line-height: 2rem;
  background-color: #f5f5f5;
  border-bottom: 1px solid #eee;
}
.main-header {
  background-color: #fff;
}
.search-btn {
  background-color: #e4393c;
  font-size: 0.875rem;
  font-weight: 500;
  letter-spacing: 0.0625rem;
  border-color: #e4393c;
}

.cart-btn {
  display: flex;
  align-items: center;
  height: 2.5rem;
  padding: 0 1rem;
  border: 1px solid #e4e4e4;
  background-color: #f9f9f9;
  border-radius: 1.25rem;
  transition: all 0.3s;
}

.cart-btn:hover {
  border-color: #e4393c;
  background-color: #fff;
}

.cart-count {
  height: 1rem;
  line-height: 1rem;
  min-width: 1rem;
  text-align: center;
}

.cart-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #666;
}
.section-title {
  font-size: 1.125rem;
  font-weight: bold;
  position: relative;
  padding-left: 0.75rem;
}
.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 25%;
  height: 50%;
  width: 0.25rem;
  background-color: #e4393c;
}
.footer {
  background-color: #fff;
  font-size: 0.75rem;
}
/* 移动端响应式样式 */
@media (max-width: 768px) {
  .main-header {
    padding: 0.5rem 0;
    width: 100%;
  }
  
  .footer {
    padding-top: 1rem;
    padding-bottom: 1rem;
    width: 100%;
  }
  
  .section-title {
    font-size: 1rem;
  }
  
  .section-title::before {
    height: 70%;
  }
  
  .container {
    width: 100%;
    max-width: 100%;
  }
}

/* 针对1536px以下屏幕的样式 */
@media (max-width: 1536px) {
  .container {
    width: 100%;
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .mall-layout {
    overflow-x: hidden;
  }
  
  .main-header {
    padding: 0.75rem 0;
  }
  
  .logo {
    flex-shrink: 0;
  }
  
  .search-box {
    max-width: 100%;
  }
}

/* 针对1280px以下屏幕的样式 */
@media (max-width: 1280px) {
  .container {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  
  .hidden.md\:flex {
    flex-wrap: wrap;
  }
  
  .logo.mr-10 {
    margin-right: 1.5rem;
  }
}
</style>
