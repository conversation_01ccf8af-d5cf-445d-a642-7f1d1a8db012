<template>
  <div class="master-layout">
    <a-layout class="layout-container">
      <!-- 悬浮按钮组件 -->
      <floating-buttons />
      <a-layout-header class="header">
        <div class="logo">
          <img src="/assets/logo.svg" alt="Logo" class="logo-img" />
          <span class="logo-text">管理系统</span>
        </div>
        <div class="header-right">
          <a-dropdown trigger="click">
            <a-avatar :size="32" style="background-color: #165DFF">
              <IconUser />
            </a-avatar>
            <template #content>
              <a-doption>
                <IconUser />
                <span>个人中心</span>
              </a-doption>
              <a-doption>
                <IconSettings />
                <span>设置</span>
              </a-doption>
              <a-doption @click="handleLogout">
                <IconExport />
                <span>退出登录</span>
              </a-doption>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      <a-layout>
        <a-layout-sider
          collapsible
          :collapsed="collapsed"
          :width="220"
          :collapsed-width="64"
          breakpoint="xl"
          @collapse="handleCollapse"
        >
          <a-menu
            :default-selected-keys="['1']"
            :default-open-keys="['sub1']"
            :collapsed="collapsed"
            show-collapse-button
            @collapse="handleCollapse"
          >
            <a-menu-item key="1">
              <template #icon><IconDashboard /></template>
              <template #default>仪表盘</template>
            </a-menu-item>
            <a-sub-menu key="sub1">
              <template #icon><IconApps /></template>
              <template #title>系统管理</template>
              <a-menu-item key="2">用户管理</a-menu-item>
              <a-menu-item key="3">角色管理</a-menu-item>
              <a-menu-item key="4">菜单管理</a-menu-item>
              <a-menu-item key="5">部门管理</a-menu-item>
            </a-sub-menu>
            <a-sub-menu key="sub2">
              <template #icon><IconGift /></template>
              <template #title>商品管理</template>
              <a-menu-item key="6">商品列表</a-menu-item>
              <a-menu-item key="7">分类管理</a-menu-item>
              <a-menu-item key="8">品牌管理</a-menu-item>
              <a-menu-item key="9">规格管理</a-menu-item>
            </a-sub-menu>
            <a-sub-menu key="sub3">
              <template #icon><IconFile /></template>
              <template #title>订单管理</template>
              <a-menu-item key="10">订单列表</a-menu-item>
              <a-menu-item key="11">退款管理</a-menu-item>
            </a-sub-menu>
          </a-menu>
        </a-layout-sider>
        <a-layout-content class="content">
          <slot />
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconUser, IconSettings, IconExport, IconDashboard, IconApps, IconGift, IconFile } from '@arco-design/web-vue/es/icon'
import useUserStore from '@/store/modules/user'
import authApi from '@/api/master/auth'
import FloatingButtons from '~/components/base/floating-buttons/index.vue'

const collapsed = ref(false)
const router = useRouter()
const userStore = useUserStore()

// 侧边栏折叠
const handleCollapse = (value) => {
  collapsed.value = value;
};
</script>

<style scoped>
.master-layout {
  height: 100vh;
  width: 100%;
}

.layout-container {
  height: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 32px;
  margin-right: 10px;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
  color: #1D2129;
}

.header-right {
  display: flex;
  align-items: center;
}

.content {
  padding: 16px;
  background-color: #f5f5f5;
  overflow: auto;
}
</style>
