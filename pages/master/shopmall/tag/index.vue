<!--
 - 商城标签管理页面
 - 用于管理商品分类标签
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-switch :checked-value="1" :unchecked-value="0" @change="changeStatus($event, record.id)"
          :default-checked="record.status == 1" />
      </template>

      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-popconfirm content="确定要删除该标签吗?" position="bottom" @ok="handleDelete(record.id)">
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>
    </ma-crud>

    <!-- 自定义弹窗组件 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @cancel="modalVisible = false" @before-ok="handleSubmit"
      :width="600">
      <ma-form ref="formRef" v-model="formData" :columns="tagColumns" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { Message } from "@arco-design/web-vue";

definePageMeta({
  name: 'master-shopmall-tag',
  path: '/master/shopmall/tag'
})

const crudRef = ref();
const formRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增标签');
const currentId = ref(null);

// 标签类型选项
const tagTypes = [
  { label: '商品分类', value: 'category' },
  { label: '商品标签', value: 'tag' },
  { label: '热门搜索', value: 'hot' }
];

// 表单数据
const formData = reactive({
  name: '',
  type: 'category',
  icon: '',
  color: '#1890FF',
  sort: 0,
  description: '',
  status: 1
});

// 标签表单配置
const tagColumns = reactive([
  {
    dataIndex: 'name',
    title: '标签名称',
    labelWidth: '80px',
    rules: [{ required: true, message: '标签名称必填' }],
    formType: 'input',
    placeholder: '请输入标签名称'
  },
  {
    dataIndex: 'type',
    title: '标签类型',
    labelWidth: '80px',
    rules: [{ required: true, message: '标签类型必选' }],
    formType: 'select',
    dict: {
      data: tagTypes
    },
    placeholder: '请选择标签类型'
  },
  {
    dataIndex: 'icon',
    title: '标签图标',
    labelWidth: '80px',
    formType: 'upload-image',
    placeholder: '请上传标签图标'
  },
  {
    dataIndex: 'color',
    title: '标签颜色',
    labelWidth: '80px',
    formType: 'color-picker',
    defaultValue: '#1890FF'
  },
  {
    dataIndex: 'sort',
    title: '排序',
    labelWidth: '80px',
    formType: 'input-number',
    placeholder: '请输入排序值',
    defaultValue: 0
  },
  {
    dataIndex: 'description',
    title: '标签描述',
    labelWidth: '80px',
    formType: 'textarea',
    placeholder: '请输入标签描述'
  },
  {
    dataIndex: 'status',
    title: '状态',
    labelWidth: '80px',
    formType: 'radio',
    dict: {
      data: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  }
]);

// 页面初始化
onMounted(() => {
  // 加载标签列表
  crudRef.value?.refresh();
});

// CRUD配置
const crud = reactive({
  // API配置
  api({ page = 1, pageSize = 10 }) {
    // 模拟API调用，实际项目中应该从后端获取数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '操作成功',
          data: {
            items: [
              {
                id: 1,
                name: '电子产品',
                type: 'category',
                type_text: '商品分类',
                icon: '/assets/images/electronic.png',
                color: '#1890FF',
                sort: 0,
                description: '电子产品分类',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 2,
                name: '服装鞋帽',
                type: 'category',
                type_text: '商品分类',
                icon: '/assets/images/clothing.png',
                color: '#52C41A',
                sort: 1,
                description: '服装鞋帽分类',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 3,
                name: '热销',
                type: 'tag',
                type_text: '商品标签',
                icon: '/assets/images/hot.png',
                color: '#F5222D',
                sort: 0,
                description: '热销商品标签',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 4,
                name: '新品',
                type: 'tag',
                type_text: '商品标签',
                icon: '/assets/images/new.png',
                color: '#722ED1',
                sort: 1,
                description: '新品商品标签',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 5,
                name: '手机',
                type: 'hot',
                type_text: '热门搜索',
                icon: '',
                color: '#FA8C16',
                sort: 0,
                description: '热门搜索关键词',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              }
            ],
            total: 5
          }
        });
      }, 300);
    });
  },
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 160,
  add: { show: false },
  edit: { show: false },
  delete: { show: false }
});

// 表格列配置
const columns = reactive([
  {
    title: '标签名称',
    dataIndex: 'name',
    search: true,
    width: 120
  },
  {
    title: '标签类型',
    dataIndex: 'type',
    width: 120,
    search: true,
    dict: {
      data: tagTypes
    },
    render({ record }) {
      return record.type_text || '-';
    }
  },
  {
    title: '标签颜色',
    dataIndex: 'color',
    width: 100,
    render({ record }) {
      return `<span style="display: inline-block; width: 20px; height: 20px; background-color: ${record.color}; border-radius: 4px;"></span>`;
    }
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slotName: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180,
    render({ record }) {
      return record.created_at || '-';
    }
  },
]);

// 打开新增弹窗
const handleAdd = () => {
  currentId.value = null;
  modalTitle.value = '新增标签';
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = 1;
    } else if (key === 'sort') {
      formData[key] = 0;
    } else if (key === 'type') {
      formData[key] = 'category';
    } else if (key === 'color') {
      formData[key] = '#1890FF';
    } else {
      formData[key] = '';
    }
  });
  
  modalVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = async (record) => {
  try {
    currentId.value = record.id;
    modalTitle.value = '编辑标签';
    
    // 填充表单数据
    Object.keys(formData).forEach(key => {
      if (key in record) {
        formData[key] = record[key];
      }
    });
    modalVisible.value = true;
  } catch (error) {
    console.error('获取标签详情失败:', error);
    Message.error('获取标签详情失败');
  }
};

// 处理删除
const handleDelete = async (id) => {
  try {
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success('删除标签成功');
      crudRef.value?.refresh();
    }, 300);
  } catch (error) {
    console.error('删除标签失败:', error);
    Message.error('删除标签失败');
  }
};

// 处理表单提交
const handleSubmit = async (done) => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) {
      done(false);
      return;
    }
    
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success(currentId.value ? '更新标签成功' : '创建标签成功');
      modalVisible.value = false;
      crudRef.value?.refresh();
      done(true);
    }, 300);
  } catch (error) {
    console.error(currentId.value ? '更新标签失败:' : '创建标签失败:', error);
    Message.error(currentId.value ? '更新标签失败' : '创建标签失败');
    done(false);
  }
};

// 修改状态
const changeStatus = async (status, id) => {
  try {
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success('修改状态成功');
      crudRef.value?.refresh();
    }, 300);
  } catch (error) {
    console.error('修改状态失败:', error);
    Message.error('修改状态失败');
  }
};
</script>

<script>
export default { name: "master-shopmall-tag" };
</script>

<style scoped></style>
