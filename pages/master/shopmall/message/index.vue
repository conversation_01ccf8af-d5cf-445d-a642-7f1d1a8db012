<!--
 - 商城消息管理页面
 - 用于管理商城系统消息、用户通知等
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-switch :checked-value="1" :unchecked-value="0" @change="changeStatus($event, record.id)"
          :default-checked="record.status == 1" />
      </template>

      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-button type="text" size="small" @click="handleView(record)">
            <template #icon><icon-eye /></template>
            查看
          </a-button>
          <a-popconfirm content="确定要删除该消息吗?" position="bottom" @ok="handleDelete(record.id)">
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>
    </ma-crud>

    <!-- 自定义弹窗组件 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @cancel="modalVisible = false" @before-ok="handleSubmit"
      :width="800">
      <ma-form ref="formRef" v-model="formData" :columns="messageColumns" />
    </a-modal>

    <!-- 消息查看抽屉 -->
    <a-drawer v-model:visible="viewVisible" :title="'消息详情'" :width="600" :footer="false">
      <div v-if="currentMessage">
        <h3 class="text-lg font-bold mb-4">{{ currentMessage.title }}</h3>
        <div class="flex justify-between text-gray-500 mb-4">
          <span>发送时间：{{ currentMessage.created_at }}</span>
          <span>消息类型：{{ getTypeText(currentMessage.type) }}</span>
        </div>
        <div class="border-t pt-4">
          <div v-html="currentMessage.content"></div>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { Message } from "@arco-design/web-vue";

definePageMeta({
  name: 'master-shopmall-message',
  path: '/master/shopmall/message'
})

const crudRef = ref();
const formRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增消息');
const currentId = ref(null);
const viewVisible = ref(false);
const currentMessage = ref(null);

// 消息类型选项
const messageTypes = [
  { label: '系统通知', value: 'system' },
  { label: '活动通知', value: 'activity' },
  { label: '订单通知', value: 'order' },
  { label: '会员通知', value: 'member' }
];

// 获取类型文本
const getTypeText = (type) => {
  const found = messageTypes.find(item => item.value === type);
  return found ? found.label : type;
};

// 表单数据
const formData = reactive({
  title: '',
  type: 'system',
  content: '',
  target_type: 'all',
  target_ids: [],
  send_time: '',
  status: 1
});

// 消息表单配置
const messageColumns = reactive([
  {
    dataIndex: 'title',
    title: '消息标题',
    labelWidth: '80px',
    rules: [{ required: true, message: '消息标题必填' }],
    formType: 'input',
    placeholder: '请输入消息标题'
  },
  {
    dataIndex: 'type',
    title: '消息类型',
    labelWidth: '80px',
    rules: [{ required: true, message: '消息类型必选' }],
    formType: 'select',
    dict: {
      data: messageTypes
    },
    placeholder: '请选择消息类型'
  },
  {
    dataIndex: 'content',
    title: '消息内容',
    labelWidth: '80px',
    rules: [{ required: true, message: '消息内容必填' }],
    formType: 'editor',
    placeholder: '请输入消息内容',
    span: 24
  },
  {
    dataIndex: 'target_type',
    title: '接收对象',
    labelWidth: '80px',
    rules: [{ required: true, message: '接收对象必选' }],
    formType: 'radio',
    dict: {
      data: [
        { label: '全部会员', value: 'all' },
        { label: '指定会员', value: 'specific' }
      ]
    }
  },
  {
    dataIndex: 'target_ids',
    title: '指定会员',
    labelWidth: '80px',
    rules: [{ required: (data) => data.target_type === 'specific', message: '请选择指定会员' }],
    formType: 'select',
    dict: {
      data: [
        { label: '张三', value: 1 },
        { label: '李四', value: 2 },
        { label: '王五', value: 3 }
      ]
    },
    placeholder: '请选择指定会员',
    multiple: true,
    hidden: (data) => data.target_type !== 'specific'
  },
  {
    dataIndex: 'send_time',
    title: '发送时间',
    labelWidth: '80px',
    formType: 'date-picker',
    placeholder: '请选择发送时间',
    showTime: true,
    format: 'YYYY-MM-DD HH:mm:ss'
  },
  {
    dataIndex: 'status',
    title: '状态',
    labelWidth: '80px',
    formType: 'radio',
    dict: {
      data: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  }
]);

// 页面初始化
onMounted(() => {
  // 加载消息列表
  crudRef.value?.refresh();
});

// CRUD配置
const crud = reactive({
  // API配置
  api({ page = 1, pageSize = 10 }) {
    // 模拟API调用，实际项目中应该从后端获取数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '操作成功',
          data: {
            items: [
              {
                id: 1,
                title: '系统维护通知',
                type: 'system',
                content: '<p>尊敬的用户，系统将于2025年4月20日凌晨2:00-4:00进行系统维护，期间系统将暂停服务，给您带来不便敬请谅解。</p>',
                target_type: 'all',
                target_ids: [],
                send_time: '2025-04-17 10:00:00',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 2,
                title: '五一促销活动',
                type: 'activity',
                content: '<p>五一大促销，全场商品8折起，更有满减优惠等您来！活动时间：2025年5月1日-5月5日</p>',
                target_type: 'all',
                target_ids: [],
                send_time: '2025-04-25 10:00:00',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 3,
                title: '订单发货通知',
                type: 'order',
                content: '<p>您的订单已发货，订单号：2025041712345，物流公司：顺丰快递，物流单号：SF1234567890</p>',
                target_type: 'specific',
                target_ids: [1],
                send_time: '2025-04-17 15:00:00',
                status: 1,
                created_at: '2025-04-17 15:00:00'
              },
              {
                id: 4,
                title: '会员等级升级通知',
                type: 'member',
                content: '<p>恭喜您成功升级为银卡会员，可享受95折优惠！</p>',
                target_type: 'specific',
                target_ids: [2],
                send_time: '2025-04-17 16:00:00',
                status: 1,
                created_at: '2025-04-17 16:00:00'
              }
            ],
            total: 4
          }
        });
      }, 300);
    });
  },
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 200,
  add: { show: false },
  edit: { show: false },
  delete: { show: false }
});

// 表格列配置
const columns = reactive([
  {
    title: '消息标题',
    dataIndex: 'title',
    search: true,
    width: 200
  },
  {
    title: '消息类型',
    dataIndex: 'type',
    width: 120,
    search: true,
    dict: {
      data: messageTypes
    },
    render({ record }) {
      return getTypeText(record.type);
    }
  },
  {
    title: '接收对象',
    dataIndex: 'target_type',
    width: 120,
    render({ record }) {
      return record.target_type === 'all' ? '全部会员' : '指定会员';
    }
  },
  {
    title: '发送时间',
    dataIndex: 'send_time',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slotName: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180,
    render({ record }) {
      return record.created_at || '-';
    }
  },
]);

// 打开新增弹窗
const handleAdd = () => {
  currentId.value = null;
  modalTitle.value = '新增消息';
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = 1;
    } else if (key === 'type') {
      formData[key] = 'system';
    } else if (key === 'target_type') {
      formData[key] = 'all';
    } else if (key === 'target_ids') {
      formData[key] = [];
    } else {
      formData[key] = '';
    }
  });
  
  modalVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = async (record) => {
  try {
    currentId.value = record.id;
    modalTitle.value = '编辑消息';
    
    // 填充表单数据
    Object.keys(formData).forEach(key => {
      if (key in record) {
        formData[key] = record[key];
      }
    });
    modalVisible.value = true;
  } catch (error) {
    console.error('获取消息详情失败:', error);
    Message.error('获取消息详情失败');
  }
};

// 查看消息详情
const handleView = (record) => {
  currentMessage.value = record;
  viewVisible.value = true;
};

// 处理删除
const handleDelete = async (id) => {
  try {
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success('删除消息成功');
      crudRef.value?.refresh();
    }, 300);
  } catch (error) {
    console.error('删除消息失败:', error);
    Message.error('删除消息失败');
  }
};

// 处理表单提交
const handleSubmit = async (done) => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) {
      done(false);
      return;
    }
    
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success(currentId.value ? '更新消息成功' : '创建消息成功');
      modalVisible.value = false;
      crudRef.value?.refresh();
      done(true);
    }, 300);
  } catch (error) {
    console.error(currentId.value ? '更新消息失败:' : '创建消息失败:', error);
    Message.error(currentId.value ? '更新消息失败' : '创建消息失败');
    done(false);
  }
};

// 修改状态
const changeStatus = async (status, id) => {
  try {
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success('修改状态成功');
      crudRef.value?.refresh();
    }, 300);
  } catch (error) {
    console.error('修改状态失败:', error);
    Message.error('修改状态失败');
  }
};
</script>

<script>
export default { name: "master-shopmall-message" };
</script>

<style scoped></style>
