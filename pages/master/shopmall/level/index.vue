<!--
 - 商城会员等级管理页面
 - 用于管理商城会员的等级设置
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-switch :checked-value="1" :unchecked-value="0" @change="changeStatus($event, record.id)"
          :default-checked="record.status == 1" />
      </template>

      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-popconfirm content="确定要删除该会员等级吗?" position="bottom" @ok="handleDelete(record.id)">
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>
    </ma-crud>

    <!-- 自定义弹窗组件 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @cancel="modalVisible = false" @before-ok="handleSubmit"
      :width="600">
      <ma-form ref="formRef" v-model="formData" :columns="levelColumns" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { Message } from "@arco-design/web-vue";

definePageMeta({
  name: 'master-shopmall-level',
  path: '/master/shopmall/level'
})

const crudRef = ref();
const formRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增会员等级');
const currentId = ref(null);

// 表单数据
const formData = reactive({
  name: '',
  discount: 100,
  min_points: 0,
  max_points: 0,
  icon: '',
  description: '',
  status: 1
});

// 会员等级表单配置
const levelColumns = reactive([
  {
    dataIndex: 'name',
    title: '等级名称',
    labelWidth: '80px',
    rules: [{ required: true, message: '等级名称必填' }],
    formType: 'input',
    placeholder: '请输入等级名称'
  },
  {
    dataIndex: 'discount',
    title: '折扣率(%)',
    labelWidth: '80px',
    rules: [
      { required: true, message: '折扣率必填' },
      { type: 'number', min: 0, max: 100, message: '折扣率必须在0-100之间' }
    ],
    formType: 'input-number',
    placeholder: '请输入折扣率',
    defaultValue: 100
  },
  {
    dataIndex: 'min_points',
    title: '最小积分',
    labelWidth: '80px',
    rules: [{ required: true, message: '最小积分必填' }],
    formType: 'input-number',
    placeholder: '请输入最小积分',
    defaultValue: 0
  },
  {
    dataIndex: 'max_points',
    title: '最大积分',
    labelWidth: '80px',
    rules: [{ required: true, message: '最大积分必填' }],
    formType: 'input-number',
    placeholder: '请输入最大积分',
    defaultValue: 0
  },
  {
    dataIndex: 'icon',
    title: '等级图标',
    labelWidth: '80px',
    formType: 'upload-image',
    placeholder: '请上传等级图标'
  },
  {
    dataIndex: 'description',
    title: '等级描述',
    labelWidth: '80px',
    formType: 'textarea',
    placeholder: '请输入等级描述'
  },
  {
    dataIndex: 'status',
    title: '状态',
    labelWidth: '80px',
    formType: 'radio',
    dict: {
      data: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  }
]);

// 页面初始化
onMounted(() => {
  // 加载会员等级列表
  crudRef.value?.refresh();
});

// CRUD配置
const crud = reactive({
  // API配置
  api({ page = 1, pageSize = 10 }) {
    // 模拟API调用，实际项目中应该从后端获取数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '操作成功',
          data: {
            items: [
              {
                id: 1,
                name: '普通会员',
                discount: 100,
                min_points: 0,
                max_points: 999,
                icon: '/assets/images/level1.png',
                description: '普通会员享受正常价格',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 2,
                name: '银卡会员',
                discount: 95,
                min_points: 1000,
                max_points: 4999,
                icon: '/assets/images/level2.png',
                description: '银卡会员享受95折优惠',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 3,
                name: '金卡会员',
                discount: 90,
                min_points: 5000,
                max_points: 9999,
                icon: '/assets/images/level3.png',
                description: '金卡会员享受9折优惠',
                status: 1,
                created_at: '2025-04-17 10:00:00'
              }
            ],
            total: 3
          }
        });
      }, 300);
    });
  },
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 160,
  add: { show: false },
  edit: { show: false },
  delete: { show: false }
});

// 表格列配置
const columns = reactive([
  {
    title: '等级名称',
    dataIndex: 'name',
    search: true,
    width: 120
  },
  {
    title: '折扣率',
    dataIndex: 'discount',
    width: 100,
    render({ record }) {
      return `${record.discount}%`;
    }
  },
  {
    title: '积分范围',
    dataIndex: 'points_range',
    width: 150,
    render({ record }) {
      return `${record.min_points} - ${record.max_points}`;
    }
  },
  {
    title: '等级图标',
    dataIndex: 'icon',
    width: 100,
    render({ record }) {
      return record.icon ? `<img src="${record.icon}" alt="${record.name}" style="width: 30px; height: 30px;" />` : '无';
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slotName: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180,
    render({ record }) {
      return record.created_at || '-';
    }
  },
]);

// 打开新增弹窗
const handleAdd = () => {
  currentId.value = null;
  modalTitle.value = '新增会员等级';
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = 1;
    } else if (key === 'discount') {
      formData[key] = 100;
    } else if (key === 'min_points' || key === 'max_points') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  
  modalVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = async (record) => {
  try {
    currentId.value = record.id;
    modalTitle.value = '编辑会员等级';
    
    // 填充表单数据
    Object.keys(formData).forEach(key => {
      if (key in record) {
        formData[key] = record[key];
      }
    });
    modalVisible.value = true;
  } catch (error) {
    console.error('获取会员等级详情失败:', error);
    Message.error('获取会员等级详情失败');
  }
};

// 处理删除
const handleDelete = async (id) => {
  try {
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success('删除会员等级成功');
      crudRef.value?.refresh();
    }, 300);
  } catch (error) {
    console.error('删除会员等级失败:', error);
    Message.error('删除会员等级失败');
  }
};

// 处理表单提交
const handleSubmit = async (done) => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) {
      done(false);
      return;
    }
    
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success(currentId.value ? '更新会员等级成功' : '创建会员等级成功');
      modalVisible.value = false;
      crudRef.value?.refresh();
      done(true);
    }, 300);
  } catch (error) {
    console.error(currentId.value ? '更新会员等级失败:' : '创建会员等级失败:', error);
    Message.error(currentId.value ? '更新会员等级失败' : '创建会员等级失败');
    done(false);
  }
};

// 修改状态
const changeStatus = async (status, id) => {
  try {
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success('修改状态成功');
      crudRef.value?.refresh();
    }, 300);
  } catch (error) {
    console.error('修改状态失败:', error);
    Message.error('修改状态失败');
  }
};
</script>

<script>
export default { name: "master-shopmall-level" };
</script>

<style scoped></style>
