<!--
 - 商城会员管理页面
 - 用于管理商城的会员信息
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-switch :checked-value="1" :unchecked-value="0" @change="changeStatus($event, record.id)"
          :default-checked="record.status == 1" />
      </template>

      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-button type="text" size="small" @click="handleResetPassword(record.id)">
            <template #icon><icon-refresh /></template>
            重置密码
          </a-button>
          <a-popconfirm content="确定要删除该会员吗?" position="bottom" @ok="handleDelete(record.id)">
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>
    </ma-crud>

    <!-- 自定义弹窗组件 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @cancel="modalVisible = false" @before-ok="handleSubmit"
      :width="600">
      <ma-form ref="formRef" v-model="formData" :columns="userColumns" />
    </a-modal>

    <!-- 密码修改抽屉 -->
    <a-drawer v-model:visible="passwordVisible" :title="'修改密码'" :width="400" @ok="changePassword">
      <ma-form ref="passwordFormRef" v-model="passwordForm" :columns="passwordColumns" />
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { Message } from "@arco-design/web-vue";

definePageMeta({
  name: 'master-shopmall-user',
  path: '/master/shopmall/user'
})

// 会员等级数据，实际项目中应该从API获取
const levels = ref([
  { id: 1, name: "普通会员" },
  { id: 2, name: "银卡会员" },
  { id: 3, name: "金卡会员" }
]);

const crudRef = ref();
const formRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增会员');
const currentId = ref(null);
const passwordVisible = ref(false);
const passwordFormRef = ref();

// 表单数据
const formData = reactive({
  username: '',
  password: '',
  level_id: null,
  nickname: '',
  phone: '',
  email: '',
  avatar: '',
  points: 0,
  balance: 0,
  status: 1
});

// 密码表单数据
const passwordForm = reactive({
  id: '',
  new_password: '',
  confirm_password: ''
});

// 密码表单配置
const passwordColumns = reactive([
  {
    dataIndex: 'new_password',
    title: '新密码',
    labelWidth: '80px',
    rules: [
      { required: true, message: '新密码不能为空' },
      {
        match: /^[^\s]{6,16}$/,
        message: '密码长度必须在6到16位之间，且不能包含空格'
      }
    ],
    formType: 'input-password',
    placeholder: '请输入新密码'
  },
  {
    dataIndex: 'confirm_password',
    title: '确认密码',
    labelWidth: '80px',
    rules: [
      { required: true, message: '确认密码不能为空' },
      {
        validator: (value, cb) => {
          if (value !== passwordForm.new_password) {
            cb('两次输入密码不一致')
          }
        }
      }
    ],
    formType: 'input-password',
    placeholder: '请输入确认密码'
  }
]);

// 会员表单配置
const userColumns = reactive([
  {
    dataIndex: 'username',
    title: '用户名',
    labelWidth: '80px',
    rules: [{ required: true, message: '用户名必填' }],
    formType: 'input',
    placeholder: '请输入用户名',
    disabled: (data) => !!currentId.value
  },
  {
    dataIndex: 'password',
    title: '密码',
    labelWidth: '80px',
    rules: [
      { required: (data) => !currentId.value, message: '密码必填' },
      {
        match: /^[^\s]{6,16}$/,
        message: '密码长度必须在6到16位之间，且不能包含空格'
      }
    ],
    formType: 'input-password',
    placeholder: '请输入密码',
    hidden: (data) => !!currentId.value
  },
  {
    dataIndex: 'level_id',
    title: '会员等级',
    labelWidth: '80px',
    rules: [{ required: true, message: '会员等级必选' }],
    formType: 'select',
    dict: {
      data: levels.value.map(item => ({ label: item.name, value: item.id }))
    },
    placeholder: '请选择会员等级'
  },
  {
    dataIndex: 'nickname',
    title: '昵称',
    labelWidth: '80px',
    formType: 'input',
    placeholder: '请输入昵称'
  },
  {
    dataIndex: 'avatar',
    title: '头像',
    labelWidth: '80px',
    formType: 'upload-image',
    placeholder: '请上传头像'
  },
  {
    dataIndex: 'phone',
    title: '手机号',
    labelWidth: '80px',
    rules: [{ match: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码' }],
    formType: 'input',
    placeholder: '请输入手机号'
  },
  {
    dataIndex: 'email',
    title: '邮箱',
    labelWidth: '80px',
    rules: [{ type: 'email', message: '请输入正确的邮箱' }],
    formType: 'input',
    placeholder: '请输入邮箱'
  },
  {
    dataIndex: 'points',
    title: '积分',
    labelWidth: '80px',
    formType: 'input-number',
    placeholder: '请输入积分',
    defaultValue: 0
  },
  {
    dataIndex: 'balance',
    title: '余额',
    labelWidth: '80px',
    formType: 'input-number',
    placeholder: '请输入余额',
    defaultValue: 0
  },
  {
    dataIndex: 'status',
    title: '状态',
    labelWidth: '80px',
    formType: 'radio',
    dict: {
      data: [
        { label: '正常', value: 1 },
        { label: '禁用', value: 0 }
      ]
    }
  }
]);

// 页面初始化
onMounted(() => {
  // 加载会员列表
  crudRef.value?.refresh();
});

// CRUD配置
const crud = reactive({
  // API配置
  api({ page = 1, pageSize = 10 }) {
    // 模拟API调用，实际项目中应该从后端获取数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '操作成功',
          data: {
            items: [
              {
                id: 1,
                username: 'user001',
                level_id: 1,
                level_name: '普通会员',
                nickname: '张三',
                phone: '13800138001',
                email: '<EMAIL>',
                avatar: '/assets/images/avatar1.png',
                points: 500,
                balance: 1000,
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 2,
                username: 'user002',
                level_id: 2,
                level_name: '银卡会员',
                nickname: '李四',
                phone: '13800138002',
                email: '<EMAIL>',
                avatar: '/assets/images/avatar2.png',
                points: 2000,
                balance: 5000,
                status: 1,
                created_at: '2025-04-17 10:00:00'
              },
              {
                id: 3,
                username: 'user003',
                level_id: 3,
                level_name: '金卡会员',
                nickname: '王五',
                phone: '13800138003',
                email: '<EMAIL>',
                avatar: '/assets/images/avatar3.png',
                points: 8000,
                balance: 10000,
                status: 0,
                created_at: '2025-04-17 10:00:00'
              }
            ],
            total: 3
          }
        });
      }, 300);
    });
  },
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 240,
  add: { show: true },
  edit: { show: false },
  delete: { show: false }
});

// 表格列配置
const columns = reactive([
  {
    title: '用户名',
    dataIndex: 'username',
    formType: "input",
    addDisplay: true,
    editDisplay: true,
    commonRules: {
      required: true,
      message: "请输入ID"
    }
  },
  {
    title: '昵称',
    dataIndex: 'nickname',
    search: true,
    width: 120
  },
  {
    title: '会员等级',
    dataIndex: 'level_name',
    width: 120
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    search: true,
    width: 150
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    search: true,
    width: 180
  },
  {
    title: '积分',
    dataIndex: 'points',
    width: 100
  },
  {
    title: '余额',
    dataIndex: 'balance',
    width: 100,
    render({ record }) {
      return `¥${record.balance.toFixed(2)}`;
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slotName: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180,
    render({ record }) {
      return record.created_at || '-';
    }
  },
]);

// 打开新增弹窗
const handleAdd = () => {
  currentId.value = null;
  modalTitle.value = '新增会员';
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = 1;
    } else if (key === 'points' || key === 'balance') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  
  modalVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = async (record) => {
  try {
    currentId.value = record.id;
    modalTitle.value = '编辑会员';
    
    // 填充表单数据
    Object.keys(formData).forEach(key => {
      if (key in record) {
        formData[key] = record[key];
      }
    });
    modalVisible.value = true;
  } catch (error) {
    console.error('获取会员详情失败:', error);
    Message.error('获取会员详情失败');
  }
};

// 处理删除
const handleDelete = async (id) => {
  try {
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success('删除会员成功');
      crudRef.value?.refresh();
    }, 300);
  } catch (error) {
    console.error('删除会员失败:', error);
    Message.error('删除会员失败');
  }
};

// 处理表单提交
const handleSubmit = async (done) => {
  try {
    // 表单验证
    const valid = await formRef.value.validate();
    if (!valid) {
      done(false);
      return;
    }
    
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success(currentId.value ? '更新会员成功' : '创建会员成功');
      modalVisible.value = false;
      crudRef.value?.refresh();
      done(true);
    }, 300);
  } catch (error) {
    console.error(currentId.value ? '更新会员失败:' : '创建会员失败:', error);
    Message.error(currentId.value ? '更新会员失败' : '创建会员失败');
    done(false);
  }
};

// 修改会员状态
const changeStatus = async (status, id) => {
  try {
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success('修改状态成功');
      crudRef.value?.refresh();
    }, 300);
  } catch (error) {
    console.error('修改状态失败:', error);
    Message.error('修改状态失败');
  }
};

// 重置密码
const handleResetPassword = (id) => {
  passwordForm.id = id;
  passwordForm.new_password = '';
  passwordForm.confirm_password = '';
  passwordVisible.value = true;
};

// 修改密码
const changePassword = async () => {
  try {
    // 表单验证
    const valid = await passwordFormRef.value.validate();
    if (!valid) return false; // 验证失败阻止关闭
    
    // 模拟API调用，实际项目中应该调用后端API
    setTimeout(() => {
      Message.success('密码修改成功');
      passwordVisible.value = false;
    }, 300);
    return true;
  } catch (error) {
    console.error('密码修改失败:', error);
    Message.error('密码修改失败');
    return false;
  }
};
</script>

<script>
export default { name: "master-shopmall-user" };
</script>

<style scoped></style>
