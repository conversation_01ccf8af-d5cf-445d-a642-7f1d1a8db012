<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义列 - 最后更新时间 -->
      <template #updateTime="{ record }">
        <div v-time="record.updateTime"></div>
      </template>
      
      <!-- 操作列自定义 -->
      <template #operation="{ record }">
        <a-space>
          <a-button type="text" size="small" @click="bindSalesman(record)">
            <icon-link />
            绑定
          </a-button>
          <a-button type="text" size="small" @click="viewModifyRecord(record)">
            <icon-history />
            修改记录
          </a-button>
        </a-space>
      </template>
    </MaCrud>
    
    <!-- 修改记录弹窗组件 -->
    <LogComponent
      v-model:visible="logVisible"
      ref="logRef"
      @ok="logVisible = false"
      @cancel="logVisible = false"
    />
    
    <!-- 绑定业务员弹窗组件 -->
    <BindComponent
      :visible="bindVisible"
      @update:visible="bindVisible = $event"
      ref="bindRef"
      @refresh="refreshTable"
    />
  </div>
</template>

<script setup>
import { ref, reactive, nextTick } from "vue";
import { Message } from "@arco-design/web-vue";
import LogComponent from "./components/log.vue";
import BindComponent from "./components/bind.vue";
import { IconLink, IconHistory } from '@arco-design/web-vue/es/icon';

definePageMeta({
  name: "master-inquireModule-salesmanBinding",
  path: "/master/inquireModule/salesmanBinding",
})

const crudRef = ref();

// 绑定业务员弹窗引用
const bindRef = ref(null);
const bindVisible = ref(false);

// 绑定业务员
const bindSalesman = (record) => {
  // 打开绑定业务员弹窗
  nextTick(() => {
    if (bindRef.value) {
      bindRef.value.open(record);
      // 不需要在这里设置bindVisible，因为子组件会通过emit触发update:visible事件
    }
  });
};

// 修改记录弹窗引用
const logRef = ref(null);
const logVisible = ref(false);

// 查看修改记录
const viewModifyRecord = (record) => {
  // 打开修改记录弹窗
  logVisible.value = true;
  // 如果组件已挂载，则调用其open方法
  nextTick(() => {
    if (logRef.value) {
      logRef.value.open(record.id);
    }
  });
};

// 模拟数据
const mockData = reactive({
  list: [
    {
      id: 1,
      salesman: '张三',
      merchandiser: '李四',
      inquirer: '王五',
      orderAccount: 'account001',
      receiver: '赵六',
      receiverAddress: '上海市浦东新区张江高科技园区',
      lastModifier: '系统管理员',
      updateTime: Date.now() - ******** * 2
    },
    {
      id: 2,
      salesman: '钱七',
      merchandiser: '孙八',
      inquirer: '周九',
      orderAccount: 'account002',
      receiver: '吴十',
      receiverAddress: '北京市海淀区中关村科技园',
      lastModifier: '系统管理员',
      updateTime: Date.now() - ********
    },
    {
      id: 3,
      salesman: '郑十一',
      merchandiser: '冯十二',
      inquirer: '陈十三',
      orderAccount: 'account003',
      receiver: '楚十四',
      receiverAddress: '广州市天河区珠江新城',
      lastModifier: '系统管理员',
      updateTime: Date.now()
    },
    {
      id: 4,
      salesman: '魏十五',
      merchandiser: '蒋十六',
      inquirer: '沈十七',
      orderAccount: 'account004',
      receiver: '韩十八',
      receiverAddress: '深圳市南山区科技园',
      lastModifier: '系统管理员',
      updateTime: Date.now() - ******** * 3
    },
    {
      id: 5,
      salesman: '杨十九',
      merchandiser: '朱二十',
      inquirer: '秦二一',
      orderAccount: 'account005',
      receiver: '尤二二',
      receiverAddress: '杭州市西湖区文三路',
      lastModifier: '系统管理员',
      updateTime: Date.now() - ******** * 4
    }
  ],
  total: 5
});

// 刷新表格数据
const refreshTable = () => {
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 模拟获取列表数据的函数
const getSalesmanBindingList = (params) => {
  console.log('查询参数:', params);
  
  // 过滤逻辑
  let filteredList = [...mockData.list];
  
  // 按业务员筛选
  if (params.salesman) {
    filteredList = filteredList.filter(item => 
      item.salesman.includes(params.salesman)
    );
  }
  
  // 按跟单员筛选
  if (params.merchandiser) {
    filteredList = filteredList.filter(item => 
      item.merchandiser.includes(params.merchandiser)
    );
  }
  
  // 按询价员筛选
  if (params.inquirer) {
    filteredList = filteredList.filter(item => 
      item.inquirer.includes(params.inquirer)
    );
  }
  
  // 按下单账号筛选
  if (params.orderAccount) {
    filteredList = filteredList.filter(item => 
      item.orderAccount.includes(params.orderAccount)
    );
  }
  
  // 按收货人筛选
  if (params.receiver) {
    filteredList = filteredList.filter(item => 
      item.receiver.includes(params.receiver)
    );
  }
  
  // 按时间范围筛选
  if (params.startTime && params.endTime) {
    filteredList = filteredList.filter(item => 
      item.updateTime >= params.startTime && item.updateTime <= params.endTime
    );
  }
  
  // 分页处理
  const pageSize = params.pageSize || 10;
  const currentPage = params.page || 1;
  const start = (currentPage - 1) * pageSize;
  const end = start + pageSize;
  const pagedList = filteredList.slice(start, end);
  
  // 返回Promise以模拟异步API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          items: pagedList,
          pageInfo: {total: filteredList.length}
        },
        code: 200,
        message: '获取成功'
      });
    }, 300);
  });
};

// CRUD 配置
const crud = reactive({
  api: getSalesmanBindingList, // 使用本地mock数据函数
  pageLayout: 'fixed',
  showPagination: true,
  add: {
    show: false,
  },
  edit: {
    show: false,
  },
  delete: {
    show: false,
  },
  beforeSearch(params) {
    // 处理时间范围参数
    if (params.updateTime && params.updateTime.length === 2) {
      params.startTime = params.updateTime[0];
      params.endTime = params.updateTime[1];
      delete params.updateTime;
    } else {
      delete params.startTime;
      delete params.endTime;
    }
    return params;
  },
});

// 表格列配置
const columns = reactive([
  {
    title: '序号',
    dataIndex: 'id',
    width: 80,
  },
  {
    title: '业务员',
    dataIndex: 'salesman',
    search: true,
    width: 120,
  },
  {
    title: '跟单员',
    dataIndex: 'merchandiser',
    search: true,
    width: 120,
  },
  {
    title: '询价员',
    dataIndex: 'inquirer',
    search: true,
    width: 120,
  },
  {
    title: '下单账号',
    dataIndex: 'orderAccount',
    search: true,
    width: 150,
  },
  {
    title: '收货人',
    dataIndex: 'receiver',
    search: true,
    width: 120,
  },
  {
    title: '收货地址',
    dataIndex: 'receiverAddress',
    search: false,
    ellipsis: true,
    width: 200,
  },
  {
    title: '最后修改人',
    dataIndex: 'lastModifier',
    search: false,
    width: 120,
  },
  {
    title: '最后更新时间',
    dataIndex: 'updateTime',
    formType: "range",
    search: false,
    width: 180,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 200,
    fixed: 'right'
  }
]);
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>