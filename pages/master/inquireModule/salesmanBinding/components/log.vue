<template>
  <a-drawer
    :visible="visible"
    title="审核记录"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <!-- 添加审核记录类型选择 -->
    <!-- <div class="mb-4">
        <a-radio-group type="button" size="mini" v-model="recordType">
          <a-radio value="inquiry">询价审核记录</a-radio>
          <a-radio value="material">资料审核记录</a-radio>
        </a-radio-group>
      </div> -->

    <a-table
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      stripe
      :scroll="{ x: '100%', y: '400px' }"
    >
      <!-- 参考链接插槽 -->
      <template #referenceLink="{ record }">
        <a-link v-if="record.referenceLink" href="#" target="_blank"
          >查看链接</a-link
        >
        <span v-else>-</span>
      </template>
    </a-table>

    <template #footer>
      <div class="flex justify-end">
        <div>
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleOk">确定</a-button>
          </a-space>
        </div>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch } from "vue";

// 定义组件属性
const props = defineProps({
  // 传入的询价产品数据
  inquiryProducts: {
    type: Array,
    default: () => [],
  },
});

const visible = ref(false);
// 审核记录类型：询价审核记录或资料审核记录
const recordType = ref("inquiry");

// 定义事件
const emit = defineEmits(["update:visible", "ok", "cancel"]);

// 询价员选项
const inquirerOptions = reactive([
  { label: "李明", value: "1" },
  { label: "王华", value: "2" },
  { label: "张伟", value: "3" },
  { label: "刘芳", value: "4" },
  { label: "陈晓", value: "5" },
  { label: "赵强", value: "6" },
]);

// 选中的询价员
const selectedInquirer = ref("");

// 表格列定义
const columns = reactive([
  {
    title: "业务员",
    dataIndex: "salesman",
    width: 120,
  },
  {
    title: "跟单员",
    dataIndex: "merchandiser",
    width: 120,
  },
  {
    title: "询价员",
    dataIndex: "inquirer",
    width: 120,
  },
  {
    title: "修改人",
    dataIndex: "modifier",
    width: 120,
  },
  {
    title: "修改时间",
    dataIndex: "modifyTime",
    width: 150,
  },
]);

// 表格数据
const tableData = reactive([
  {
    id: 1,
    salesman: "王小明",
    merchandiser: "李小红",
    inquirer: "张小华",
    modifier: "陈经理",
    modifyTime: "2025-06-16 09:30:45",
  },
  {
    id: 2,
    salesman: "赵小龙",
    merchandiser: "钱小芳",
    inquirer: "孙小亮",
    modifier: "刘主管",
    modifyTime: "2025-06-16 10:15:22",
  },
  {
    id: 3,
    salesman: "周小强",
    merchandiser: "吴小燕",
    inquirer: "郑小伟",
    modifier: "黄总监",
    modifyTime: "2025-06-16 11:05:37",
  },
  {
    id: 4,
    salesman: "冯小刚",
    merchandiser: "陈小婷",
    inquirer: "徐小勇",
    modifier: "马经理",
    modifyTime: "2025-06-17 08:45:12",
  },
  {
    id: 5,
    salesman: "朱小杰",
    merchandiser: "杨小娟",
    inquirer: "林小峰",
    modifier: "王总",
    modifyTime: "2025-06-17 14:20:33",
  },
]);

// 选择行相关
const selectedRowKeys = ref([]);
const selectAll = ref(false);

// 行选择配置
const rowSelection = reactive({
  type: "checkbox",
  showCheckedAll: true,
  selectedRowKeys: selectedRowKeys,
  onChange: (selectedKeys) => {
    selectedRowKeys.value = selectedKeys;
    selectAll.value = selectedKeys.length === tableData.length;
  },
});

// 全选/取消全选处理
const handleSelectAllChange = (checked) => {
  if (checked) {
    selectedRowKeys.value = tableData.map((item) => item.id);
  } else {
    selectedRowKeys.value = [];
  }
};

// 确认按钮处理
const handleOk = () => {
  // 触发确认事件
  emit("ok");

  // 关闭弹窗
  visible.value = false;
  emit("update:visible", false);

  // 重置组件状态
  resetState();
};

// 取消按钮处理
const handleCancel = () => {
  visible.value = false;
  emit("update:visible", false);
  emit("cancel");
  resetState();
};

// 重置组件状态
const resetState = () => {
  selectedInquirer.value = "";
  selectedRowKeys.value = [];
  selectAll.value = false;
};

// 暴露给父组件的打开弹窗方法
const open = (recordId) => {
  console.log("打开审核记录，记录ID:", recordId);
  // 这里可以根据记录ID加载对应的审核数据
  // 例如：loadAuditRecords(recordId);
  visible.value = true;
};

// 监听记录类型变化，切换不同的数据
watch(recordType, (newValue) => {
  // 这里可以根据选择的记录类型加载不同的数据
  // 例如：loadRecordData(newValue);
  console.log("记录类型变更为:", newValue);
  // TODO: 根据选择类型加载对应的审核记录数据
});

// 暴露方法给父组件
defineExpose({
  open,
});
</script>
