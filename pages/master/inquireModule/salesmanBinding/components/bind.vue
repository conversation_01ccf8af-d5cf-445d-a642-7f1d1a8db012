<template>
  <a-drawer
    title="绑定人员"
    :visible="visible"
    @update:visible="visible = $event"
    @ok="handleOk"
    @cancel="handleCancel"
    :mask-closable="false"
    :unmount-on-close="false"
    width="500px"
  >
    <a-form
      :model="formData"
      :rules="rules"
      ref="formRef"
      label-align="right"
      :label-col-props="{ span: 6 }"
      :wrapper-col-props="{ span: 18 }"
    >
      <!-- 当前业务员信息 -->
      <a-form-item label="当前业务员">
        <a-input
          v-model="currentSalesman"
          disabled
          placeholder="暂无绑定业务员"
        />
      </a-form-item>

      <!-- 跟单员选择 -->
      <a-form-item
        field="merchandiserId"
        label="跟单员"
        validate-trigger="change"
        required
      >
        <a-select
          v-model="formData.merchandiserId"
          placeholder="请选择跟单员"
          allow-clear
          :loading="loading.merchandiser"
        >
          <a-option
            v-for="item in merchandiserOptions"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          >
            {{ item.name }} ({{ item.code }})
          </a-option>
        </a-select>
      </a-form-item>

      <!-- 询价员选择 -->
      <a-form-item
        field="inquirerId"
        label="询价员"
        validate-trigger="change"
        required
      >
        <a-select
          v-model="formData.inquirerId"
          placeholder="请选择询价员"
          allow-clear
          :loading="loading.inquirer"
        >
          <a-option
            v-for="item in inquirerOptions"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          >
            {{ item.name }} ({{ item.code }})
          </a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { Message } from "@arco-design/web-vue";

// 接收父组件传递的visible属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});

// 内部visible状态
const visible = ref(props.visible);
const formRef = ref(null);
const loading = reactive({
  salesman: false,
  merchandiser: false,
  inquirer: false,
  submit: false,
});

// 当前记录ID
const currentRecordId = ref(null);
// 当前业务员
const currentSalesman = ref("");

// 表单数据
const formData = reactive({
  merchandiserId: null,
  inquirerId: null,
});

// 表单验证规则
const rules = {
  merchandiserId: [{ required: true, message: "请选择跟单员", type: "error" }],
  inquirerId: [{ required: true, message: "请选择询价员", type: "error" }],
};

// 业务员选项
const salesmanOptions = ref([]);
// 跟单员选项
const merchandiserOptions = ref([]);
// 询价员选项
const inquirerOptions = ref([]);

// 获取业务员列表
const fetchSalesmanList = async () => {
  try {
    loading.salesman = true;
    // 实际项目中应该调用API获取业务员列表
    // const res = await fetch('/api/salesman/list');
    // const data = await res.json();
    // salesmanOptions.value = data.items;

    // 模拟数据
    setTimeout(() => {
      salesmanOptions.value = [
        { id: 1, name: "张三", code: "ZS001" },
        { id: 2, name: "李四", code: "LS002" },
        { id: 3, name: "王五", code: "WW003" },
        { id: 4, name: "赵六", code: "ZL004" },
        { id: 5, name: "钱七", code: "QQ005" },
      ];
      loading.salesman = false;
    }, 500);
  } catch (error) {
    console.error("获取业务员列表失败:", error);
    Message.error("获取业务员列表失败");
    loading.salesman = false;
  }
};

// 获取当前记录的业务员信息
const fetchCurrentSalesman = async (id) => {
  try {
    // 实际项目中应该调用API获取当前记录的业务员信息
    // const res = await fetch(`/api/binding/${id}`);
    // const data = await res.json();
    // currentSalesman.value = data.salesmanName || '暂无绑定业务员';

    // 模拟数据
    const mockData = {
      1: "张三",
      2: "钱七",
      3: "郑十一",
      4: "魏十五",
      5: "杨十九",
    };

    currentSalesman.value = mockData[id] || "暂无绑定业务员";
  } catch (error) {
    console.error("获取当前业务员信息失败:", error);
    Message.error("获取当前业务员信息失败");
    currentSalesman.value = "获取失败";
  }
};

// 业务员变更处理
const handleSalesmanChange = (value) => {
  if (!value) {
    formData.salesmanId = null;
  }
};

const emit = defineEmits(["update:visible", "refresh"]);

// 监听props.visible变化，同步到内部状态
watch(
  () => props.visible,
  (newVal) => {
    visible.value = newVal;
  }
);

// 提交表单
const handleOk = async () => {
  if (loading.submit) return;

  try {
    await formRef.value.validate();
    loading.submit = true;

    // 实际项目中应该调用API提交绑定请求
    // const res = await fetch('/api/binding/save', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify({
    //         recordId: currentRecordId.value,
    //         salesmanId: formData.salesmanId,
    //         reason: formData.reason
    //     })
    // });
    // const data = await res.json();
    // if (data.code === 0) {
    //     Message.success('绑定业务员成功');
    //     emit('refresh');
    //     emit('update:visible', false);
    // } else {
    //     Message.error(data.message || '绑定业务员失败');
    // }

    // 模拟提交
    setTimeout(() => {
      const selectedMerchandiser = merchandiserOptions.value.find(
        (item) => item.id === formData.merchandiserId
      );
      const selectedInquirer = inquirerOptions.value.find(
        (item) => item.id === formData.inquirerId
      );

      console.log("提交数据:", {
        recordId: currentRecordId.value,
        merchandiserId: formData.merchandiserId,
        merchandiserName: selectedMerchandiser ? selectedMerchandiser.name : "",
        inquirerId: formData.inquirerId,
        inquirerName: selectedInquirer ? selectedInquirer.name : "",
      });

      Message.success("绑定业务员成功");
      emit("refresh");
      emit("update:visible", false);
      loading.submit = false;
      resetForm();
    }, 1000);
  } catch (error) {
    console.error("表单验证失败:", error);
    loading.submit = false;
  }
};

// 取消操作
const handleCancel = () => {
  resetForm();
  emit("update:visible", false);
};

// 重置表单
const resetForm = () => {
  formData.merchandiserId = null;
  formData.inquirerId = null;
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 打开弹窗
const open = (record) => {
  if (!record || !record.id) {
    Message.error("记录ID不能为空");
    return;
  }

  currentRecordId.value = record.id;
  fetchCurrentSalesman(record.id);
  // 通知父组件更新visible状态
  emit("update:visible", true);
};

// 获取跟单员列表
const fetchMerchandiserList = async () => {
  try {
    loading.merchandiser = true;
    // 实际项目中应该调用API获取跟单员列表

    // 模拟数据
    setTimeout(() => {
      merchandiserOptions.value = [
        { id: 1, name: "李四", code: "LS002" },
        { id: 2, name: "孙八", code: "SB008" },
        { id: 3, name: "周九", code: "ZJ009" },
        { id: 4, name: "吴十", code: "WS010" },
        { id: 5, name: "郭十四", code: "GSS014" },
      ];
      loading.merchandiser = false;
    }, 500);
  } catch (error) {
    console.error("获取跟单员列表失败:", error);
    Message.error("获取跟单员列表失败");
    loading.merchandiser = false;
  }
};

// 获取询价员列表
const fetchInquirerList = async () => {
  try {
    loading.inquirer = true;
    // 实际项目中应该调用API获取询价员列表

    // 模拟数据
    setTimeout(() => {
      inquirerOptions.value = [
        { id: 1, name: "王五", code: "WW003" },
        { id: 2, name: "陈十三", code: "CSS013" },
        { id: 3, name: "朱二十", code: "ZES020" },
        { id: 4, name: "秦二一", code: "QEY021" },
        { id: 5, name: "尤二二", code: "YEE022" },
      ];
      loading.inquirer = false;
    }, 500);
  } catch (error) {
    console.error("获取询价员列表失败:", error);
    Message.error("获取询价员列表失败");
    loading.inquirer = false;
  }
};

onMounted(() => {
  fetchSalesmanList();
  fetchMerchandiserList();
  fetchInquirerList();
});

defineExpose({ open });
</script>
