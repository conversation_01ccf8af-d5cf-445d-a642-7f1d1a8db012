<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
 <template>
    <div class="ma-content-block p-4">
      <!-- CRUD 组件 -->
      <MaCrud :options="crud" :columns="columns" ref="crudRef">
        <!-- 自定义列 - 询价时间 -->
        <template #inquiryTime="{ record }">
          <div v-time="record.inquiryTime"></div>
        </template>
        
        <!-- 操作列自定义 -->
        <template #operation="{ record }">
          <a-space>
            <a-button type="text" size="small" @click="viewDetail(record)">
              <icon-eye />
              查看
            </a-button>
            <a-button type="text" size="small" @click="editItem(record)">
              <icon-edit />
              编辑
            </a-button>
          </a-space>
        </template>
      </MaCrud>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive } from "vue";
  import { Message } from "@arco-design/web-vue";
  
  definePageMeta({
    name: "master-inquireModule-contactsList",
    path: "/master/inquireModule/contactsList",
  })
  
  const crudRef = ref();
  
  // 查看详情
  const viewDetail = (record) => {
    // 跳转到详情页或打开详情抽屉
    Message.info(`查看联系人: ${record.contactNo}`);
  };
  
  // 编辑项目
  const editItem = (record) => {
    // 编辑逻辑
    Message.info(`编辑联系人: ${record.contactNo}`);
  };
  
  // 模拟数据
  const mockData = reactive({
    list: [
      {
        id: 1,
        contactNo: 'LX20250616001',
        customerSource: '官网',
        creator: '张三',
        createTime: Date.now() - 86400000 * 2,
        level: 'A',
        receiverName: '李明',
        receiverPhone: '13812345678',
        receiverAddress: '上海市浦东新区张江高科技园区',
        followMerchant: '上海某科技有限公司',
        follower: '王五'
      },
      {
        id: 2,
        contactNo: 'LX20250616002',
        customerSource: '展会',
        creator: '赵六',
        createTime: Date.now() - 86400000,
        level: 'B',
        receiverName: '陈华',
        receiverPhone: '13987654321',
        receiverAddress: '北京市海淀区中关村科技园',
        followMerchant: '北京某电子有限公司',
        follower: '钱七'
      },
      {
        id: 3,
        contactNo: 'LX20250616003',
        customerSource: '客户推荐',
        creator: '周九',
        createTime: Date.now(),
        level: 'A',
        receiverName: '刘强',
        receiverPhone: '13765432198',
        receiverAddress: '广州市天河区珠江新城',
        followMerchant: '广州某贸易有限公司',
        follower: '郑十一'
      },
      {
        id: 4,
        contactNo: 'LX20250615001',
        customerSource: '搜索引擎',
        creator: '张三',
        createTime: Date.now() - 86400000 * 3,
        level: 'C',
        receiverName: '赵敏',
        receiverPhone: '13678901234',
        receiverAddress: '深圳市南山区科技园',
        followMerchant: '深圳某科技有限公司',
        follower: '李四'
      },
      {
        id: 5,
        contactNo: 'LX20250614001',
        customerSource: '社交媒体',
        creator: '赵六',
        createTime: Date.now() - 86400000 * 4,
        level: 'B',
        receiverName: '王芳',
        receiverPhone: '13567890123',
        receiverAddress: '杭州市西湖区文三路',
        followMerchant: '杭州某信息技术有限公司',
        follower: '孙八'
      }
    ],
    total: 5
  });
  
  // 模拟获取列表数据的函数
  const getInquiryList = (params) => {
    console.log('查询参数:', params);
    
    // 过滤逻辑
    let filteredList = [...mockData.list];
    
    // 按编号筛选
    if (params.contactNo) {
      filteredList = filteredList.filter(item => 
        item.contactNo.toLowerCase().includes(params.contactNo.toLowerCase())
      );
    }
    
    // 按客户来源筛选
    if (params.customerSource) {
      filteredList = filteredList.filter(item => 
        item.customerSource.includes(params.customerSource)
      );
    }
    
    // 按添加人筛选
    if (params.creator) {
      filteredList = filteredList.filter(item => 
        item.creator.includes(params.creator)
      );
    }
    
    // 按等级筛选
    if (params.level) {
      filteredList = filteredList.filter(item => 
        item.level === params.level
      );
    }
    
    // 按收货人姓名筛选
    if (params.receiverName) {
      filteredList = filteredList.filter(item => 
        item.receiverName.includes(params.receiverName)
      );
    }
    
    // 按收货人电话筛选
    if (params.receiverPhone) {
      filteredList = filteredList.filter(item => 
        item.receiverPhone.includes(params.receiverPhone)
      );
    }
    
    // 按时间范围筛选
    if (params.startTime && params.endTime) {
      filteredList = filteredList.filter(item => 
        item.createTime >= params.startTime && item.createTime <= params.endTime
      );
    }
    
    // 分页处理
    const pageSize = params.pageSize || 10;
    const currentPage = params.page || 1;
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    const pagedList = filteredList.slice(start, end);
    
    // 返回Promise以模拟异步API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            items: pagedList,
            pageInfo: {total: filteredList.length}
          },
          code: 200,
          message: '获取成功'
        });
      }, 300);
    });
  };
  
  // CRUD 配置
  const crud = reactive({
    api: getInquiryList, // 使用本地mock数据函数
    pageLayout: 'fixed',
    operationColumn: true,
    operationColumnWidth: 150,
    searchLabelWidth: '100px',
    add: {
      show: true,
      api: (params) => {
        console.log('新增联系人:', params);
        return Promise.resolve({ code: 200, message: '新增成功' });
      }
    },
    edit: {
      show: true,
      api: (params) => {
        console.log('编辑联系人:', params);
        return Promise.resolve({ code: 200, message: '编辑成功' });
      }
    },
    delete: {
      show: true,
      api: (ids) => {
        console.log('删除联系人:', ids);
        return Promise.resolve({ code: 200, message: '删除成功' });
      }
    },
    // 搜索前处理参数
    beforeSearch: (params) => {
      // 如果有创建时间参数，转换为毫秒级时间戳
      if(params.createTime && params.createTime.length > 0){
        // 设置开始时间为当天的00:00:00
        const startDate = new Date(params.createTime[0]);
        startDate.setHours(0, 0, 0, 0);
        params.startTime = startDate.getTime();
        
        // 设置结束时间为当天的23:59:59
        const endDate = new Date(params.createTime[1]);
        endDate.setHours(23, 59, 59, 999);
        params.endTime = endDate.getTime();
        
        delete params.createTime;
      } else {
        delete params.startTime;
        delete params.endTime;
      }
      return params;
    },
  });
  
  // 表格列配置
  const columns = reactive([
    {
      title: '编号',
      dataIndex: 'contactNo',
      search: true,
      width: 150,
    },
    {
      title: '客户来源',
      dataIndex: 'customerSource',
      search: true,
      width: 120,
    },
    {
      title: '添加人',
      dataIndex: 'creator',
      search: true,
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      formType: "range",
      search: true,
      width: 180,
      addDisplay: false,
      editDisplay: false,
    },
    {
      title: '等级',
      dataIndex: 'level',
      search: true,
      width: 100,
    },
    {
      title: '收货人姓名',
      dataIndex: 'receiverName',
      search: true,
      width: 120,
    },
    {
      title: '收货人电话',
      dataIndex: 'receiverPhone',
      search: true,
      width: 130,
    },
    {
      title: '收货地址',
      dataIndex: 'receiverAddress',
      search: false,
      ellipsis: true,
      width: 200,
    },
    {
      title: '跟进商家-主登录名称',
      dataIndex: 'followMerchant',
      search: false,
      width: 180,
    },
    {
      title: '跟进人',
      dataIndex: 'follower',
      search: true,
      width: 120,
    }
  ]);
  </script>
  
  <style scoped>
  .ma-content-block {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }
  </style>
  