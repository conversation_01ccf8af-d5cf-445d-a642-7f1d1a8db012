<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义列 - 询价时间 -->
      <template #inquiryTime="{ record }">
        <div v-time="record.inquiryTime"></div>
      </template>
      
      <!-- 操作列自定义 -->
      <template #operation="{ record }">
        <a-space>
          <a-link type="primary" size="small" @click="viewShelfDetail(record)">
            上架详情
          </a-link>
        </a-space>
      </template>
    </MaCrud>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconUpload, IconEye } from '@arco-design/web-vue/es/icon';
import { useRouter } from "vue-router";

definePageMeta({
  name: "master-inquireModule-shelfApplicationManagement",
  path: "/master/inquireModule/shelfApplicationManagement",
})

const router = useRouter();
const crudRef = ref();

// 查看上架详情
const viewShelfDetail = (record) => {
  // 跳转到上架详情页
  import("@/utils/common").then(module => {
    module.navigateWithTag(router, `/master/inquireModule/shelfDetails/${record.id}`);
  });
};

// 模拟数据
const mockData = reactive({
  list: [
    {
      id: 1,
      inquiryNo: 'XJ20250616001',
      inquiryTime: Date.now() - 86400000 * 2,
      salesman: '张三',
      pendingShelfCount: 5,
      merchandiser: '李四',
      inquirer: '王五'
    },
    {
      id: 2,
      inquiryNo: 'XJ20250616002',
      inquiryTime: Date.now() - 86400000,
      salesman: '赵六',
      pendingShelfCount: 8,
      merchandiser: '钱七',
      inquirer: '孙八'
    },
    {
      id: 3,
      inquiryNo: 'XJ20250616003',
      inquiryTime: Date.now(),
      salesman: '周九',
      pendingShelfCount: 3,
      merchandiser: '吴十',
      inquirer: '郑十一'
    },
    {
      id: 4,
      inquiryNo: 'XJ20250615001',
      inquiryTime: Date.now() - 86400000 * 3,
      salesman: '张三',
      pendingShelfCount: 10,
      merchandiser: '李四',
      inquirer: '王五'
    },
    {
      id: 5,
      inquiryNo: 'XJ20250614001',
      inquiryTime: Date.now() - 86400000 * 4,
      salesman: '赵六',
      pendingShelfCount: 6,
      merchandiser: '钱七',
      inquirer: '孙八'
    }
  ],
  total: 5
});

// 模拟获取列表数据的函数
const getShelfApplicationList = (params) => {
  console.log('查询参数:', params);
  
  // 过滤逻辑
  let filteredList = [...mockData.list];
  
  // 按询价单号筛选
  if (params.inquiryNo) {
    filteredList = filteredList.filter(item => 
      item.inquiryNo.toLowerCase().includes(params.inquiryNo.toLowerCase())
    );
  }
  
  // 按业务员筛选
  if (params.salesman) {
    filteredList = filteredList.filter(item => 
      item.salesman.includes(params.salesman)
    );
  }
  
  // 按跟单员筛选
  if (params.merchandiser) {
    filteredList = filteredList.filter(item => 
      item.merchandiser.includes(params.merchandiser)
    );
  }
  
  // 按询价员筛选
  if (params.inquirer) {
    filteredList = filteredList.filter(item => 
      item.inquirer.includes(params.inquirer)
    );
  }
  
  // 按时间范围筛选
  if (params.startTime && params.endTime) {
    filteredList = filteredList.filter(item => 
      item.inquiryTime >= params.startTime && item.inquiryTime <= params.endTime
    );
  }
  
  // 分页处理
  const pageSize = params.pageSize || 10;
  const currentPage = params.page || 1;
  const start = (currentPage - 1) * pageSize;
  const end = start + pageSize;
  const pagedList = filteredList.slice(start, end);
  
  // 返回Promise以模拟异步API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          items: pagedList,
          pageInfo: {total: filteredList.length}
        },
        code: 200,
        message: '获取成功'
      });
    }, 300);
  });
};

// CRUD 配置
const crud = reactive({
  api: getShelfApplicationList, // 使用本地mock数据函数
  pageLayout: 'fixed',
  showPagination: true,
  add: {
    show: false,
  },
  edit: {
    show: false,
  },
  delete: {
    show: false,
  },
  beforeSearch(params) {
    // 处理日期范围
    if (params.inquiryTime && params.inquiryTime.length === 2) {
      params.startTime = params.inquiryTime[0];
      params.endTime = params.inquiryTime[1];
      delete params.inquiryTime;
    } else {
      delete params.startTime;
      delete params.endTime;
    }
    return params;
  },
});

// 表格列配置
const columns = reactive([
  {
    title: '询价单号',
    dataIndex: 'inquiryNo',
    search: true,
    width: 150,
  },
  {
    title: '询价时间',
    dataIndex: 'inquiryTime',
    formType: "range",
    search: false,
    width: 180,
  },
  {
    title: '业务员',
    dataIndex: 'salesman',
    search: true,
    width: 120,
  },
  {
    title: '待上架产品数',
    dataIndex: 'pendingShelfCount',
    search: false,
    width: 120,
  },
  {
    title: '跟单员',
    dataIndex: 'merchandiser',
    search: true,
    width: 120,
  },
  {
    title: '询价员',
    dataIndex: 'inquirer',
    search: true,
    width: 120,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    slotName: 'operation',
    width: 120,
  },
]);
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>