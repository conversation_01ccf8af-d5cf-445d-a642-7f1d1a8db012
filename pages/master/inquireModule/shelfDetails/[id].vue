<template>
  <div class="p-4">
    <!-- 订单基本信息 -->
    <a-card class="mb-4" :bordered="false">
      <a-descriptions :data="orderInfoData" title="订单基本信息" bordered />
    </a-card>
    
    <!-- 表格组件 -->
    <a-table
        :columns="columns"
        :data="tableData"
        :pagination="{ pageSize: 10 }"
        :bordered="true"
        stripe
        :scroll="{ x: '100%' }"
        :row-key="(record) => record.id"
      >
        <!-- 上架操作列插槽 -->
        <template #operations="{ record }">
          <a-space>
            <a-button type="text" size="small" @click="handleShelf(record)">
              上架
            </a-button>
            <a-button type="text" size="small" @click="handleAudit(record)">
              审核
            </a-button>
            <a-button type="text" size="small" @click="handleViewAudit(record)">
              审核记录
            </a-button>
          </a-space>
        </template>
      </a-table>
    <!-- 审核记录弹窗组件 -->
    <AuditRecord
      ref="auditRecordRef"
    />
    <!-- 审核弹窗组件 -->
    <Audit
      ref="auditRef"
    />
  </div>
</template>

<script setup>
import { ref, h, defineAsyncComponent } from 'vue';

// 创建组件目录
const AuditRecord = defineAsyncComponent(() => import('./components/AuditRecord.vue'));
const Audit = defineAsyncComponent(() => import('./components/Audit.vue'));

definePageMeta({
  name: "master-inquireModule-shelfDetails",
  path: "/master/inquireModule/shelfDetails/:id",
});

// 创建组件引用
const auditRecordRef = ref(null);
const auditRef = ref(null);

// 订单基本信息数据
const orderInfoData = [
  {
    label: "下单平台",
    value: "京东商城",
  },
  {
    label: "业务员",
    value: "李明",
  },
  {
    label: "跟单员",
    value: "王华",
  },
  {
    label: "询价员",
    value: "张伟",
  },
];

// 表格列定义
const columns = [
  {
    title: "实际产品名称",
    dataIndex: "actualProductName",
    width: 150,
  },
  {
    title: "实际品牌",
    dataIndex: "actualBrand",
    width: 120,
  },
  {
    title: "实际型号",
    dataIndex: "actualModel",
    width: 120,
  },
  {
    title: "实际规格描述",
    dataIndex: "actualSpecDescription",
    width: 180,
  },
  {
    title: "包装量",
    dataIndex: "packageQuantity",
    width: 100,
  },
  {
    title: "询价产品编码",
    dataIndex: "inquiryProductCode",
    width: 150,
  },
  {
    title: "产品编码",
    dataIndex: "productCode",
    width: 120,
  },
  {
    title: "成本价",
    dataIndex: "costPrice",
    width: 100,
    render: ({ record }) => {
      return `¥${record.costPrice}`;
    },
  },
  {
    title: "询价单位",
    dataIndex: "inquiryUnit",
    width: 100,
  },
  {
    title: "含税运情况",
    dataIndex: "taxAndShipping",
    width: 120,
  },
  {
    title: "运费",
    dataIndex: "shippingCost",
    width: 100,
    render: ({ record }) => {
      return `¥${record.shippingCost}`;
    },
  },
  {
    title: "货期",
    dataIndex: "deliveryPeriod",
    width: 100,
  },
  {
    title: "附加费",
    dataIndex: "additionalFee",
    width: 100,
    render: ({ record }) => {
      return `¥${record.additionalFee}`;
    },
  },
  {
    title: "销售价",
    dataIndex: "sellingPrice",
    width: 100,
    render: ({ record }) => {
      return `¥${record.sellingPrice}`;
    },
  },
  {
    title: "实际询价备注",
    dataIndex: "actualInquiryRemark",
    width: 150,
  },
  {
    title: "供应商",
    dataIndex: "supplier",
    width: 120,
  },
  {
    title: "上架资料链接",
    dataIndex: "shelfDataLink",
    width: 150,
    render: ({ record }) => {
      return record.shelfDataLink ? 
        h('a-link', { href: record.shelfDataLink, target: '_blank' }, '查看') : 
        '无';
    },
  },
  {
    title: "询价员",
    dataIndex: "inquirer",
    width: 100,
  },
  {
    title: "询价状态",
    dataIndex: "inquiryStatus",
    width: 100,
    render: ({ record }) => {
      const statusMap = {
        pending: { text: '待询价', color: 'orange' },
        inProgress: { text: '询价中', color: 'blue' },
        completed: { text: '已完成', color: 'green' },
        failed: { text: '询价失败', color: 'red' },
      };
      const status = statusMap[record.inquiryStatus] || { text: '未知', color: 'gray' };
      return h('a-tag', { color: status.color }, status.text);
    },
  },
  {
    title: "操作",
    width: 280,
    slotName: "operations",
    fixed: "right",
  },
];

// 模拟表格数据
const tableData = ref([
  {
    id: '1',
    actualProductName: '高精度电阻器',
    actualBrand: '欧姆龙',
    actualModel: 'R-5000',
    actualSpecDescription: '5W 1% 精度 金属膜电阻',
    packageQuantity: 100,
    inquiryProductCode: 'INQ-R5000',
    productCode: 'R5000-100',
    costPrice: 1.25,
    inquiryUnit: '个',
    taxAndShipping: '含税含运费',
    shippingCost: 50,
    deliveryPeriod: '3-5天',
    additionalFee: 10,
    sellingPrice: 2.5,
    actualInquiryRemark: '客户要求高精度电阻',
    supplier: '欧姆龙电子（深圳）有限公司',
    shelfDataLink: 'https://example.com/shelf/R5000',
    inquirer: '张三',
    inquiryStatus: 'completed',
  },
  {
    id: '2',
    actualProductName: '工业级电容器',
    actualBrand: '松下',
    actualModel: 'C-2200',
    actualSpecDescription: '2200uF 35V 电解电容',
    packageQuantity: 50,
    inquiryProductCode: 'INQ-C2200',
    productCode: 'C2200-50',
    costPrice: 0.85,
    inquiryUnit: '个',
    taxAndShipping: '含税不含运费',
    shippingCost: 30,
    deliveryPeriod: '7天',
    additionalFee: 5,
    sellingPrice: 1.7,
    actualInquiryRemark: '需要工业级耐高温产品',
    supplier: '松下电器（中国）有限公司',
    shelfDataLink: 'https://example.com/shelf/C2200',
    inquirer: '李四',
    inquiryStatus: 'inProgress',
  },
  {
    id: '3',
    actualProductName: '高频晶体振荡器',
    actualBrand: 'TI',
    actualModel: 'OSC-8M',
    actualSpecDescription: '8MHz 5V 精度±20ppm',
    packageQuantity: 25,
    inquiryProductCode: 'INQ-OSC8M',
    productCode: 'OSC8M-25',
    costPrice: 3.5,
    inquiryUnit: '个',
    taxAndShipping: '不含税不含运费',
    shippingCost: 45,
    deliveryPeriod: '10-15天',
    additionalFee: 15,
    sellingPrice: 7.2,
    actualInquiryRemark: '用于高精度计时设备',
    supplier: '德州仪器半导体技术（上海）有限公司',
    shelfDataLink: '',
    inquirer: '王五',
    inquiryStatus: 'pending',
  },
]);

// 处理上架操作
const handleShelf = (record) => {
  // 实现上架逻辑
  console.log('上架产品:', record);
  // 这里可以调用API进行上架操作
};

// 查看审核记录
const handleViewAudit = (record) => {
  // 打开审核记录弹窗
  auditRecordRef.value.open(record.id);
};

// 处理审核操作
const handleAudit = (record) => {
  // 打开审核弹窗
  auditRef.value.open(record);
};
</script>

<style scoped>
/* 自定义样式 */
</style>