<template>
  <a-drawer
    :visible="visible"
    title="产品审核"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="audit-form">
      <a-descriptions
        :data="productData"
        title="产品信息"
        bordered
        :column="3"
      />
      <div class="mt-4">
        <a-form :model="formData" layout="vertical">
          <a-form-item label="审核结果" required>
            <a-radio-group v-model="formData.auditResult">
              <a-radio value="pass">通过</a-radio>
              <a-radio value="reject">不通过</a-radio>
            </a-radio-group>
          </a-form-item>

          <a-form-item label="审核意见" required>
            <a-textarea
              v-model="formData.auditComment"
              placeholder="请输入审核意见"
              :auto-size="{ minRows: 4, maxRows: 6 }"
            />
          </a-form-item>
        </a-form>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineExpose, computed } from "vue";

const visible = ref(false);
const currentRecord = ref(null);

// 产品信息数据
const productData = computed(() => {
  if (!currentRecord.value) return [];

  return [
    {
      label: "实际产品名称",
      value: currentRecord.value.actualProductName || "--",
    },
    { label: "实际品牌", value: currentRecord.value.actualBrand || "--" },
    { label: "实际型号", value: currentRecord.value.actualModel || "--" },
    { label: "实际规格", value: currentRecord.value.actualSpec || "--" },
    {
      label: "实际规格描述",
      value: currentRecord.value.actualSpecDescription || "--",
    },
    { label: "包装量", value: currentRecord.value.packageQuantity || "--" },
    {
      label: "询价产品编码",
      value: currentRecord.value.inquiryProductCode || "--",
    },
    { label: "产品编码", value: currentRecord.value.productCode || "--" },
    {
      label: "成本价",
      value: currentRecord.value.costPrice
        ? `¥${currentRecord.value.costPrice}`
        : "--",
    },
    { label: "询价单位", value: currentRecord.value.inquiryUnit || "--" },
    { label: "含税运情况", value: currentRecord.value.taxAndShipping || "--" },
    {
      label: "运费",
      value: currentRecord.value.shippingCost
        ? `¥${currentRecord.value.shippingCost}`
        : "--",
    },
    { label: "货期", value: currentRecord.value.deliveryPeriod || "--" },
    {
      label: "附加费",
      value: currentRecord.value.additionalFee
        ? `¥${currentRecord.value.additionalFee}`
        : "--",
    },
    {
      label: "实际询价备注",
      value: currentRecord.value.actualInquiryRemark || "--",
    },
    { label: "供应商", value: currentRecord.value.supplier || "--" },
    {
      label: "上架资料链接",
      value: currentRecord.value.shelfDataLink ? "已上传" : "未上传",
    },
    { label: "询价员", value: currentRecord.value.inquirer || "--" },
    {
      label: "询价状态",
      value: currentRecord.value.inquiryStatus
        ? getStatusText(currentRecord.value.inquiryStatus)
        : "--",
    },
  ];
});

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: "待询价",
    inProgress: "询价中",
    completed: "已完成",
    failed: "询价失败",
  };
  return statusMap[status] || "未知";
};

// 表单数据
const formData = ref({
  auditResult: "pass",
  auditComment: "",
});

// 文件列表
const fileList = ref([]);

// 定义事件
const emit = defineEmits(["update:visible", "ok", "cancel"]);

// 打开弹窗的方法
const open = (record) => {
  currentRecord.value = record;
  visible.value = true;
  // 重置表单
  formData.value = {
    auditResult: "pass",
    auditComment: "",
  };
  fileList.value = [];
};

// 处理确定按钮点击
const handleOk = () => {
  // 直接关闭弹窗
  visible.value = false;
};

// 处理取消按钮点击
const handleCancel = () => {
  emit("cancel");
  visible.value = false;
};

// 处理文件上传变化
const handleFileChange = (info) => {
  fileList.value = info.fileList;
};

// 暴露方法给父组件使用
defineExpose({
  open,
});
</script>
