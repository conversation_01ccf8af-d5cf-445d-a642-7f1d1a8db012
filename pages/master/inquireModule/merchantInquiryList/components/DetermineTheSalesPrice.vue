<template>
  <a-drawer
    :visible="visible"
    title="确定销售价"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <!-- 客户信息描述列表 -->
    <div class="mb-4">
      <a-descriptions
        title="客户信息"
        bordered
        layout="horizontal"
        :column="3"
        size="medium"
      >
        <a-descriptions-item
          v-for="(item, index) in customerData"
          :key="index"
          :label="item.label"
        >
          {{ item.value }}
        </a-descriptions-item>
        <a-descriptions-item label="毛利率">
          <a-input-number
            v-model="grossProfitRate"
            :min="0"
            :max="100"
            :precision="2"
            suffix="%"
            style="width: 120px"
          />
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 询价信息表格 -->
    <div class="mt-4">
      <a-table
        :data="inquiryResultData"
        :pagination="false"
        :bordered="true"
        stripe
        :scroll="{ x: '100%' }"
        size="small"
      >
        <template #columns>
          <a-table-column
            title="实际产品名称"
            data-index="actualProductName"
            :width="150"
          />
          <a-table-column
            title="实际品牌"
            data-index="actualBrand"
            :width="100"
          />
          <a-table-column
            title="实际型号"
            data-index="actualModel"
            :width="120"
          />
          <a-table-column
            title="实际规格描述"
            data-index="actualSpecDescription"
            :width="180"
            show-tooltip
          />
          <a-table-column
            title="包装量"
            data-index="packagingQuantity"
            :width="80"
            align="center"
          />
          <a-table-column
            title="询价产品编码"
            data-index="inquiryProductCode"
            :width="140"
          />
          <a-table-column
            title="产品编码"
            data-index="productCode"
            :width="120"
          />
          <a-table-column
            title="成本价"
            data-index="costPrice"
            :width="100"
            align="right"
          />
          <a-table-column
            title="询价单位"
            data-index="inquiryUnit"
            :width="80"
            align="center"
          />
          <a-table-column
            title="实际单位"
            data-index="actualUnit"
            :width="80"
            align="center"
          >
            <template #cell="{ record }">
              <a-input
                v-if="record.inquiryUnit !== record.actualUnit"
                v-model="record.actualUnit"
                placeholder="请输入实际单位"
              />
              <span v-else>{{ record.actualUnit }}</span>
            </template>
          </a-table-column>
          <a-table-column
            title="含税运情况"
            data-index="taxAndShipping"
            :width="120"
          />
          <a-table-column
            title="运费"
            data-index="shippingCost"
            :width="80"
            align="right"
          >
            <template #cell="{ record }">
              <a-input-number
                v-model="record.shippingCost"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </template>
          </a-table-column>
          <a-table-column
            title="货期"
            data-index="deliveryPeriod"
            :width="100"
          />
          <a-table-column
            title="附加费"
            data-index="additionalFee"
            :width="80"
            align="right"
          />
          <a-table-column
            title="销售价"
            data-index="sellingPrice"
            :width="100"
            align="right"
          >
            <template #cell="{ record }">
              <a-input-number
                v-model="record.sellingPrice"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </template>
          </a-table-column>
          <a-table-column
            title="询价备注"
            data-index="inquiryRemarks"
            :width="150"
            show-tooltip
          />
          <a-table-column title="供应商" data-index="supplier" :width="120" />
          <a-table-column
            title="上架资料链接"
            data-index="listingDocLink"
            :width="120"
          >
            <template #cell="{ record }">
              <a-link
                v-if="record.listingDocLink"
                href="javascript:void(0)"
                @click="openLink(record.listingDocLink)"
                >查看资料</a-link
              >
              <span v-else>-</span>
            </template>
          </a-table-column>
          <a-table-column
            title="实际询价员"
            data-index="actualInquiryStaff"
            :width="100"
          />
          <a-table-column
            title="询价状态"
            data-index="inquiryStatus"
            :width="100"
          >
            <template #cell="{ record }">
              <a-tag :color="getStatusColor(record.inquiryStatus)">
                {{ record.inquiryStatus }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column
            title="操作"
            :width="120"
            fixed="right"
            align="center"
          >
            <template #cell="{ record }">
              <a-space>
                <a-popconfirm
                  v-if="record.isSelected"
                  content="确定要取消选择该询价项吗？"
                  @ok="handleToggleSelection(record)"
                  okText="确定"
                  cancelText="取消"
                >
                  <a-link status="danger">取消选择</a-link>
                </a-popconfirm>
                <a-link
                  v-else
                  status="success"
                  @click="handleToggleSelection(record)"
                  >确认选择</a-link
                >
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineExpose } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconDelete } from "@arco-design/web-vue/es/icon";

// 在组件内部定义visible状态
const visible = ref(false);
const inquiryId = ref("");
const grossProfitRate = ref(20.0); // 默认毛利率20%

const emit = defineEmits(["refresh"]);

// 客户信息mock数据
const customerData = ref([
  { label: "客户", value: "上海聚灵云科技有限公司" },
  { label: "客户编码", value: "JLY20250619" },
  { label: "客户地址", value: "上海市浦东新区张江高科技园区" },
  { label: "下单平台", value: "官方网站" },
  { label: "业务员", value: "张三" },
]);

// 询价结果mock数据
const inquiryResultData = ref([
  {
    id: "1",
    actualProductName: "PowerEdge R740服务器",
    actualBrand: "Dell",
    isSelected: true,
    actualModel: "R740",
    actualSpecDescription:
      "2U机架服务器，双路英特尔至强可扩展处理器，384GB内存",
    packagingQuantity: "1",
    inquiryProductCode: "INQ20250619001",
    actualUnit: "台",
    productCode: "DELL-R740-01",
    costPrice: "42000.00",
    inquiryUnit: "台",
    taxAndShipping: "含税含运",
    shippingCost: "300.00",
    deliveryPeriod: "7-10个工作日",
    additionalFee: "200.00",
    sellingPrice: "45000.00",
    inquiryRemarks: "含3年原厂保修",
    supplier: "戴尔（中国）有限公司",
    listingDocLink: "https://example.com/docs/dell-r740",
    actualInquiryStaff: "王五",
    inquiryStatus: "已完成",
    reinquiryReason: "",
  },
  {
    id: "2",
    actualProductName: "Catalyst 9300交换机",
    actualBrand: "Cisco",
    isSelected: true,
    actualModel: "C9300-24P",
    actualSpecDescription: "24端口千兆以太网交换机，支持PoE+",
    packagingQuantity: "1",
    inquiryProductCode: "INQ20250619002",
    actualUnit: "个",
    productCode: "CISCO-C9300-01",
    costPrice: "16500.00",
    inquiryUnit: "台",
    taxAndShipping: "含税不含运",
    shippingCost: "150.00",
    deliveryPeriod: "5-7个工作日",
    additionalFee: "100.00",
    sellingPrice: "18000.00",
    inquiryRemarks: "含思科SmartNet服务",
    supplier: "思科系统（中国）网络技术有限公司",
    listingDocLink: "https://example.com/docs/cisco-c9300",
    actualInquiryStaff: "赵六",
    inquiryStatus: "已完成",
    reinquiryReason: "",
  },
  {
    id: "3",
    actualProductName: "ThinkSystem SR650服务器",
    actualBrand: "Lenovo",
    isSelected: true,
    actualModel: "SR650",
    actualSpecDescription: "2U机架服务器，双路英特尔至强处理器，256GB内存",
    packagingQuantity: "1",
    inquiryProductCode: "INQ20250619003",
    actualUnit: "台",
    productCode: "LENOVO-SR650-01",
    costPrice: "38000.00",
    inquiryUnit: "台",
    taxAndShipping: "含税含运",
    shippingCost: "280.00",
    deliveryPeriod: "7-10个工作日",
    additionalFee: "180.00",
    sellingPrice: "41000.00",
    inquiryRemarks: "含3年原厂保修",
    supplier: "联想（北京）有限公司",
    listingDocLink: "https://example.com/docs/lenovo-sr650",
    actualInquiryStaff: "李四",
    inquiryStatus: "已完成",
    reinquiryReason: "",
  },
]);

// 根据询价状态获取标签颜色
const getStatusColor = (status) => {
  switch (status) {
    case "已完成":
      return "green";
    case "处理中":
      return "blue";
    case "已提交":
      return "orange";
    case "已取消":
      return "red";
    default:
      return "gray";
  }
};

// 打开链接
const openLink = (url) => {
  if (url) {
    window.open(url, "_blank");
  }
};

// 处理选择状态切换
const handleToggleSelection = (record) => {
  const index = inquiryResultData.value.findIndex(
    (item) => item.id === record.id
  );
  if (index !== -1) {
    // 切换选择状态
    inquiryResultData.value[index].isSelected =
      !inquiryResultData.value[index].isSelected;
    const status = inquiryResultData.value[index].isSelected
      ? "已确认选择"
      : "已取消选择";
    Message.success(`${status}该询价项`);
  }
};

// 处理确认
const handleOk = () => {
  // 验证是否有选中的项
  const selectedItems = inquiryResultData.value.filter(
    (item) => item.isSelected
  );
  if (selectedItems.length === 0) {
    Message.error("请至少选择一项询价项");
    return;
  }

  // 提交重新询价请求
  Message.success("已提交重新询价请求");
  emit("refresh");
  visible.value = false;
};

// 处理取消
const handleCancel = () => {
  visible.value = false;
};

// 打开弹窗方法
const open = (id) => {
  inquiryId.value = id || "";
  visible.value = true;
  // 这里可以根据 inquiryId 加载数据
  // 当前使用模拟数据，实际应用中可以调用API获取数据
};

// 暴露方法给父组件调用
defineExpose({
  open,
});
</script>

<style scoped>
.mb-4 {
  margin-bottom: 1rem;
}

.mt-4 {
  margin-top: 1rem;
}
</style>
