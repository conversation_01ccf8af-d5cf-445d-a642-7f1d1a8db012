<template>
    <div>
        <div class="mb-4">
            <a-descriptions :data="customerData" title="客户信息" bordered layout="horizontal" :column="3" size="medium">
                <template #item="{ data }">
                    <a-descriptions-item v-for="(item, index) in data" :key="index" :label="item.label">
                        {{ item.value }}
                    </a-descriptions-item>
                </template>
            </a-descriptions>
        </div>
        
        <!-- 编辑询价信息弹窗 -->
        <Edit ref="editRef" @update="handleUpdateInquiry" />
        
        <!-- 审核记录弹窗 -->
        <AuditRecord ref="auditRecordRef" />
        
        <!-- 重新询价弹窗 -->
        <Reinquiry ref="reinquiryRef" @refresh="fetchInquiryResultData" />
        
        <!-- 确定销售价弹窗 -->
        <DetermineTheSalesPrice ref="determineTheSalesPriceRef" @refresh="fetchInquiryResultData" />
        
        <!-- 下载报价单弹窗 -->
        <QuotationDownload ref="quotationDownloadRef" />
        
        <!-- 申请上架弹窗 -->
        <ApplyForShelves ref="applyForShelvesRef" />
      
        <!-- 询价信息表格 -->
        <div class="mt-4">
            <a-card title="询价信息" :bordered="false">
                <a-table :data="inquiryData" :pagination="{ pageSize: 10 }" :bordered="true" stripe>
                    <template #columns>
                        <a-table-column title="产品名称" data-index="productName" />
                        <a-table-column title="询价产品编码" data-index="inquiryProductCode" />
                        <a-table-column title="单位" data-index="unit" />
                        <a-table-column title="品牌" data-index="brand" />
                        <a-table-column title="型号" data-index="model" />
                        <a-table-column title="规格描述" data-index="specDescription" />
                        <a-table-column title="参考链接" data-index="referenceLink">
                            <template #cell="{ record }">
                                <a-link v-if="record.referenceLink" href="javascript:void(0)" @click="openLink(record.referenceLink)">查看链接</a-link>
                                <span v-else>-</span>
                            </template>
                        </a-table-column>
                        <a-table-column title="需求数量" data-index="requiredQuantity" />
                        <a-table-column title="参考价格" data-index="referencePrice" />
                        <a-table-column title="客户产品编码" data-index="customerProductCode" />
                        <a-table-column title="询价备注" data-index="inquiryRemarks" />
                        <a-table-column title="询价员" data-index="inquiryStaff" />
                        <a-table-column title="询价状态" data-index="inquiryStatus">
                            <template #cell="{ record }">
                                <a-tag :color="getStatusColor(record.inquiryStatus)">{{ record.inquiryStatus }}</a-tag>
                            </template>
                        </a-table-column>
                        <a-table-column title="操作" align="center" width="120">
                            <template #cell>
                                <a-popconfirm
                                    content="确定要取消此询价信息吗？"
                                    @ok="handleCancelConfirm"
                                    position="br"
                                >
                                    <a-link status="danger" size="small">取消</a-link>
                                </a-popconfirm>
                            </template>
                        </a-table-column>
                    </template>
                </a-table>
            </a-card>
        </div>

        <!-- 询价结果列表 -->
        <div class="mt-4">
            <a-card :bordered="false">
                <template #title>
                    <div class="flex justify-between items-center w-full">
                        <span class="text-lg font-medium">询价结果</span>
                        <div class="flex space-x-2">
                            <a-button type="primary" size="small" @click="handleReinquiry">
                                <template #icon><icon-refresh /></template>
                                重新询价
                            </a-button>
                            <a-button type="primary" status="success" size="small" @click="handleDetermineTheSalesPrice">
                                <template #icon><icon-check-circle /></template>
                                确定销售价
                            </a-button>
                            <a-button type="outline" status="normal" size="small" @click="handleQuotationDownload">
                                <template #icon><icon-download /></template>
                                下载报价单
                            </a-button>
                            <a-button type="outline" status="warning" size="small" @click="handleApplyForShelves">
                                <template #icon><icon-upload /></template>
                                申请上架
                            </a-button>
                        </div>
                    </div>
                </template>
                <a-table :data="inquiryResultData" :pagination="{ pageSize: 10 }" :bordered="true" stripe :scroll="{ x: '100%' }" size="medium">
                    <template #columns>
                        <a-table-column title="序号" data-index="serialNumber" :width="60" align="center" fixed="left" />
                        <a-table-column title="实际产品名称" data-index="actualProductName" :width="150" fixed="left" />
                        <a-table-column title="实际品牌" data-index="actualBrand" :width="100" />
                        <a-table-column title="实际型号" data-index="actualModel" :width="120" />
                        <a-table-column title="实际规格描述" data-index="actualSpecDescription" :width="180" show-tooltip />
                        <a-table-column title="包装量" data-index="packagingQuantity" :width="80" align="center" />
                        <a-table-column title="询价产品编码" data-index="inquiryProductCode" :width="140" />
                        <a-table-column title="产品编码" data-index="productCode" :width="120" />
                        <a-table-column title="成本价" data-index="costPrice" :width="100" align="right" />
                        <a-table-column title="询价单位" data-index="inquiryUnit" :width="80" align="center" />
                        <a-table-column title="含税运情况" data-index="taxAndShipping" :width="120" />
                        <a-table-column title="运费" data-index="shippingCost" :width="80" align="right" />
                        <a-table-column title="货期" data-index="deliveryPeriod" :width="100" />
                        <a-table-column title="附加费" data-index="additionalFee" :width="80" align="right" />
                        <a-table-column title="销售价" data-index="sellingPrice" :width="100" align="right" />
                        <a-table-column title="实际询价备注" data-index="actualInquiryRemarks" :width="150" show-tooltip />
                        <a-table-column title="上架资料链接" data-index="listingDocLink" :width="120">
                            <template #cell="{ record }">
                                <a-link v-if="record.listingDocLink" href="javascript:void(0)" @click="openLink(record.listingDocLink)">查看资料</a-link>
                                <span v-else>-</span>
                            </template>
                        </a-table-column>
                        <a-table-column title="实际询价员" data-index="actualInquiryStaff" :width="100" />
                        <a-table-column title="询价状态" data-index="inquiryStatus" :width="100">
                            <template #cell="{ record }">
                                <a-tag :color="getStatusColor(record.inquiryStatus)">
                                    {{ record.inquiryStatus }}
                                </a-tag>
                            </template>
                        </a-table-column>
                        <a-table-column title="上架状态" data-index="listingStatus" :width="100">
                            <template #cell="{ record }">
                                <a-tag :color="getListingStatusColor(record.listingStatus)">
                                    {{ record.listingStatus }}
                                </a-tag>
                            </template>
                        </a-table-column>
                        <a-table-column title="商品编码" data-index="goodsCode" :width="120" />
                        <a-table-column title="操作" :width="250" fixed="right" align="center">
                            <template #cell="{ record }">
                                <a-space>
                                    <a-button type="text" size="small" @click="handleEdit(record)">
                                        <template #icon><icon-edit /></template>
                                        编辑
                                    </a-button>
                                    <a-button type="text" size="small" @click="handleViewAuditRecords(record)">
                                        <template #icon><icon-file /></template>
                                        审核记录
                                    </a-button>
                                </a-space>
                            </template>
                        </a-table-column>
                    </template>
                </a-table>
            </a-card>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import { IconEdit, IconFile, IconRefresh, IconCheckCircle, IconDownload, IconUpload } from '@arco-design/web-vue/es/icon'
import Edit from './components/Edit.vue'
import AuditRecord from './components/AuditRecord.vue'
import Reinquiry from './components/Reinquiry.vue'
import DetermineTheSalesPrice from './components/DetermineTheSalesPrice.vue'
import QuotationDownload from './components/QuotationDownload.vue'
import ApplyForShelves from './components/ApplyForShelves.vue'

// 组件引用
const editRef = ref(null)
const auditRecordRef = ref(null)
const reinquiryRef = ref(null)
const determineTheSalesPriceRef = ref(null)
const quotationDownloadRef = ref(null)
const applyForShelvesRef = ref(null)

definePageMeta({
    name: "master-inquireModule-merchantInquiryDetails",
    path: "/master/inquireModule/merchantInquiryDetails/:id",
})

// 客户信息mock数据
const customerData = ref([
    { label: '客户', value: '上海聚灵云科技有限公司' },
    { label: '客户编码', value: 'JLY20250619' },
    { label: '客户地址', value: '上海市浦东新区张江高科技园区' },
    { label: '下单平台', value: '官方网站' },
    { label: '业务员', value: '张三' },
])

// 询价信息mock数据
const inquiryData = ref([
    {
        id: '1',
        productName: '服务器主机',
        inquiryProductCode: 'INQ20250619001',
        unit: '台',
        brand: 'Dell',
        model: 'PowerEdge R740',
        specDescription: '2U机架服务器，双路英特尔至强可扩展至银处理器，384GB内存',
        referenceLink: 'https://www.dell.com/zh-cn/work/shop/povw/poweredge-r740',
        requiredQuantity: '5',
        referencePrice: '45000.00',
        customerProductCode: 'CP20250619001',
        inquiryRemarks: '需要包含3年上门服务',
        inquiryStaff: '李四',
        inquiryStatus: '已提交'
    },
    {
        id: '2',
        productName: '交换机',
        inquiryProductCode: 'INQ20250619002',
        unit: '台',
        brand: 'Cisco',
        model: 'Catalyst 9300',
        specDescription: '24端口企业级交换机，支持PoE+',
        referenceLink: 'https://www.cisco.com/c/zh_cn/products/switches/catalyst-9300-series-switches',
        requiredQuantity: '3',
        referencePrice: '18000.00',
        customerProductCode: 'CP20250619002',
        inquiryRemarks: '需要支持堆叠',
        inquiryStaff: '李四',
        inquiryStatus: '处理中'
    },
    {
        id: '3',
        productName: '存储阵列',
        inquiryProductCode: 'INQ20250619003',
        unit: '套',
        brand: 'NetApp',
        model: 'FAS8300',
        specDescription: '企业级存储阵列，50TB可用容量',
        referenceLink: '',
        requiredQuantity: '1',
        referencePrice: '120000.00',
        customerProductCode: 'CP20250619003',
        inquiryRemarks: '需要包含数据迁移服务',
        inquiryStaff: '王五',
        inquiryStatus: '已完成'
    },
    {
        id: '4',
        productName: 'UPS电源',
        inquiryProductCode: 'INQ20250619004',
        unit: '台',
        brand: 'APC',
        model: 'Smart-UPS SRT 10000VA',
        specDescription: '10KVA 在线式不间断电源，机架式',
        referenceLink: 'https://www.apc.com/shop/cn/zh/products/APC-Smart-UPS-SRT-10000VA-RM-230V/P-SRT10KRMXLI',
        requiredQuantity: '2',
        referencePrice: '32000.00',
        customerProductCode: 'CP20250619004',
        inquiryRemarks: '',
        inquiryStaff: '王五',
        inquiryStatus: '已取消'
    }
])

// 询价结果列表mock数据
const inquiryResultData = ref([
    {
        id: '1',
        serialNumber: 1,
        actualProductName: 'PowerEdge R740服务器',
        actualBrand: 'Dell',
        actualModel: 'PowerEdge R740',
        actualSpecDescription: '2U机架服务器，双路英特尔至强可扩展至银处理器，384GB内存，8TB SSD存储',
        packagingQuantity: '1',
        inquiryProductCode: 'INQ20250619001',
        productCode: 'PRD20250619001',
        costPrice: '42000.00',
        inquiryUnit: '台',
        taxAndShipping: '含税含运费',
        shippingCost: '0.00',
        deliveryPeriod: '7-10工作日',
        additionalFee: '500.00',
        sellingPrice: '48000.00',
        actualInquiryRemarks: '已确认包含3年上门服务和安装调试',
        listingDocLink: 'https://example.com/docs/server-r740',
        actualInquiryStaff: '赵六',
        inquiryStatus: '已完成',
        listingStatus: '已上架',
        goodsCode: 'G20250619001'
    },
    {
        id: '2',
        serialNumber: 2,
        actualProductName: 'Catalyst 9300交换机',
        actualBrand: 'Cisco',
        actualModel: 'C9300-24P-A',
        actualSpecDescription: '24端口企业级交换机，支持PoE+，包含网络优势许可证',
        packagingQuantity: '1',
        inquiryProductCode: 'INQ20250619002',
        productCode: 'PRD20250619002',
        costPrice: '16500.00',
        inquiryUnit: '台',
        taxAndShipping: '含税不含运',
        shippingCost: '200.00',
        deliveryPeriod: '5-7工作日',
        additionalFee: '300.00',
        sellingPrice: '19800.00',
        actualInquiryRemarks: '已确认支持堆叠，最多可堆叠8台',
        listingDocLink: 'https://example.com/docs/cisco-c9300',
        actualInquiryStaff: '赵六',
        inquiryStatus: '已完成',
        listingStatus: '审核中',
        goodsCode: 'G20250619002'
    },
    {
        id: '3',
        serialNumber: 3,
        actualProductName: 'NetApp存储阵列',
        actualBrand: 'NetApp',
        actualModel: 'FAS8300-A400-12TB',
        actualSpecDescription: '企业级存储阵列，12TB原始容量，可扩展至50TB，包含数据管理软件',
        packagingQuantity: '1',
        inquiryProductCode: 'INQ20250619003',
        productCode: 'PRD20250619003',
        costPrice: '110000.00',
        inquiryUnit: '套',
        taxAndShipping: '含税含运费',
        shippingCost: '0.00',
        deliveryPeriod: '15-20工作日',
        additionalFee: '2000.00',
        sellingPrice: '135000.00',
        actualInquiryRemarks: '包含数据迁移服务和5年维保',
        listingDocLink: '',
        actualInquiryStaff: '钱七',
        inquiryStatus: '处理中',
        listingStatus: '未上架',
        goodsCode: ''
    },
    {
        id: '4',
        serialNumber: 4,
        actualProductName: 'APC Smart-UPS',
        actualBrand: 'APC',
        actualModel: 'SRT10KRMXLI',
        actualSpecDescription: '10KVA 在线式不间断电源，机架式，含网络管理卡',
        packagingQuantity: '1',
        inquiryProductCode: 'INQ20250619004',
        productCode: 'PRD20250619004',
        costPrice: '29500.00',
        inquiryUnit: '台',
        taxAndShipping: '含税不含运',
        shippingCost: '500.00',
        deliveryPeriod: '3-5工作日',
        additionalFee: '0.00',
        sellingPrice: '34500.00',
        actualInquiryRemarks: '包含基础安装服务',
        listingDocLink: 'https://example.com/docs/apc-ups',
        actualInquiryStaff: '钱七',
        inquiryStatus: '已取消',
        listingStatus: '已下架',
        goodsCode: 'G20250619004'
    }
])

/**
 * 根据询价状态获取标签颜色
 * @param {string} status 询价状态
 * @returns {string} 颜色值
 */
const getStatusColor = (status) => {
    const statusMap = {
        '已提交': 'blue',
        '处理中': 'orange',
        '已完成': 'green',
        '已取消': 'red'
    }
    return statusMap[status] || 'gray'
}

/**
 * 根据上架状态获取标签颜色
 * @param {string} status 上架状态
 * @returns {string} 颜色值
 */
const getListingStatusColor = (status) => {
    const statusMap = {
        '已上架': 'green',
        '审核中': 'blue',
        '未上架': 'gray',
        '已下架': 'red'
    }
    return statusMap[status] || 'gray'
}

/**
 * 打开参考链接
 * @param {string} url 链接地址
 */
const openLink = (url) => {
    if (url) {
        window.open(url, '_blank')
    }
}

/**
 * 处理取消询价确认
 */
const handleCancelConfirm = () => {
    Message.success('已成功取消询价')
    // 这里实现取消询价的逻辑
}

/**
 * 处理编辑询价结果
 * @param {Object} record 记录数据
 */
const handleEdit = (record) => {
    console.log('编辑询价结果:', record)
    // 打开编辑弹窗
    editRef.value.open(record, inquiryResultData.value.findIndex(item => item.id === record.id))
}

/**
 * 处理更新询价信息
 * @param {Object} updatedData 更新后的数据
 * @param {Number} index 数据索引
 */
const handleUpdateInquiry = (updatedData, index) => {
    if (index > -1 && index < inquiryResultData.value.length) {
        // 更新数据
        Object.assign(inquiryResultData.value[index], updatedData)
        Message.success('询价信息更新成功')
    }
}

/**
 * 查看审核记录
 * @param {Object} record 记录数据
 */
const handleViewAuditRecords = (record) => {
    // 打开审核记录弹窗
    auditRecordRef.value.open(record.id)
    console.log('查看审核记录', record)
}

// 处理重新询价
const handleReinquiry = () => {
    // 打开重新询价弹窗
    reinquiryRef.value.open()
    console.log('打开重新询价弹窗')
}

// 处理确定销售价
const handleDetermineTheSalesPrice = () => {
    // 打开确定销售价弹窗
    determineTheSalesPriceRef.value.open()
}

// 处理下载报价单
const handleQuotationDownload = () => {
    // 打开下载报价单弹窗
    quotationDownloadRef.value.open()
}

// 处理申请上架
const handleApplyForShelves = () => {
    applyForShelvesRef.value.open()
}

// 获取询价结果数据
const fetchInquiryResultData = () => {
    // 这里应该是从API获取最新数据的逻辑
    // 目前使用mock数据，实际项目中应该调用API
    Message.success('询价数据已更新')
}


</script>