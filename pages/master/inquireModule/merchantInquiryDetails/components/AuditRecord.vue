<template>
    <a-modal
      :visible="visible"
      title="审核记录"
      width="900px"
      @ok="handleOk"
      @cancel="handleCancel"
      @update:visible="(val) => emit('update:visible', val)"
    >
    
  
      <a-table
        :columns="columns"
        :data="tableData"
        :pagination="false"
        :bordered="true"
        stripe
        :scroll="{ x: '100%', y: '400px' }"
        :row-selection="rowSelection"
        :row-key="(record) => record.id"
      >
        <!-- 参考链接插槽 -->
        <template #referenceLink="{ record }">
          <a-link v-if="record.referenceLink" href="#" target="_blank">查看链接</a-link>
          <span v-else>-</span>
        </template>
      </a-table>
  
      <template #footer>
        <div class="flex justify-end">
          <div>
            <a-space>
              <a-button @click="handleCancel">取消</a-button>
              <a-button type="primary" @click="handleOk">确定</a-button>
            </a-space>
          </div>
        </div>
      </template>
    </a-modal>
  </template>
  
  <script setup>
  import { ref, reactive, watch } from 'vue';
  
  // 定义组件属性
  const props = defineProps({

    // 传入的询价产品数据
    inquiryProducts: {
      type: Array,
      default: () => []
    }
  });

  const visible = ref(false);

  
  // 定义事件
  const emit = defineEmits(['update:visible', 'ok', 'cancel']);
  
  // 询价员选项
  const inquirerOptions = reactive([
    { label: '李明', value: '1' },
    { label: '王华', value: '2' },
    { label: '张伟', value: '3' },
    { label: '刘芳', value: '4' },
    { label: '陈晓', value: '5' },
    { label: '赵强', value: '6' },
  ]);
  
  // 选中的询价员
  const selectedInquirer = ref('');
  
  // 表格列定义
  const columns = reactive([
    {
      title: '实际产品名称',
      dataIndex: 'actualProductName',
      width: 150,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '实际品牌',
      dataIndex: 'actualBrand',
      width: 100,
    },
    {
      title: '实际型号',
      dataIndex: 'actualModel',
      width: 120,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '实际规格描述',
      dataIndex: 'actualSpecification',
      width: 200,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '包装量',
      dataIndex: 'packageQuantity',
      width: 80,
      align: 'right',
    },
    {
      title: '询价产品编码',
      dataIndex: 'inquiryProductCode',
      width: 140,
    },
    {
      title: '产品编码',
      dataIndex: 'productCode',
      width: 120,
    },
    {
      title: '成本价',
      dataIndex: 'costPrice',
      width: 100,
      align: 'right',
    },
    {
      title: '询价单位',
      dataIndex: 'inquiryUnit',
      width: 80,
    },
    {
      title: '含税运情况',
      dataIndex: 'taxAndShipping',
      width: 120,
    },
    {
      title: '运费',
      dataIndex: 'shippingCost',
      width: 80,
      align: 'right',
    },
    {
      title: '货期',
      dataIndex: 'deliveryPeriod',
      width: 80,
    },
    {
      title: '附加费',
      dataIndex: 'additionalFee',
      width: 80,
      align: 'right',
    },
    {
      title: '销售价',
      dataIndex: 'sellingPrice',
      width: 100,
      align: 'right',
    },
    {
      title: '实际询价备注',
      dataIndex: 'actualInquiryRemark',
      width: 150,
      ellipsis: true,
      tooltip: true,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      width: 120,
    },
    {
      title: '上架资料链接',
      dataIndex: 'referenceLink',
      width: 120,
      slotName: 'referenceLink',
    },
    {
      title: '实际询价员',
      dataIndex: 'actualInquirer',
      width: 100,
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      width: 150,
    },
    {
      title: '审核人',
      dataIndex: 'auditor',
      width: 100,
    },
    {
      title: '审核态度',
      dataIndex: 'auditAttitude',
      width: 100,
    },
    {
      title: '审核备注',
      dataIndex: 'auditRemark',
      width: 150,
      ellipsis: true,
      tooltip: true,
    },
  ]);
  
  // 表格数据
  const tableData = reactive([
    {
      id: 1,
      actualProductName: '电阻器',
      actualBrand: '国巨',
      actualModel: 'RC0603FR-071KL',
      actualSpecification: '1KΩ ±1% 0603 厚膜电阻器',
      packageQuantity: 5000,
      inquiryProductCode: 'INQ20250616001',
      productCode: 'P20250616001',
      costPrice: 0.03,
      inquiryUnit: '个',
      taxAndShipping: '含税含运费',
      shippingCost: 50,
      deliveryPeriod: '7天',
      additionalFee: 10,
      sellingPrice: 0.05,
      actualInquiryRemark: '需要原装正品',
      supplier: '深圳市电子有限公司',
      referenceLink: 'https://example.com/product1',
      actualInquirer: '李明',
      auditTime: '2025-06-15 14:30:00',
      auditor: '张经理',
      auditAttitude: '通过',
      auditRemark: '价格合理，供应商可靠',
    },
    {
      id: 2,
      actualProductName: '电容器',
      actualBrand: 'TDK',
      actualModel: 'C1608X7R1H104K080AA',
      actualSpecification: '0.1uF 50V X7R 0603',
      packageQuantity: 10000,
      inquiryProductCode: 'INQ20250616002',
      productCode: 'P20250616002',
      costPrice: 0.06,
      inquiryUnit: '个',
      taxAndShipping: '含税不含运费',
      shippingCost: 80,
      deliveryPeriod: '10天',
      additionalFee: 0,
      sellingPrice: 0.08,
      actualInquiryRemark: '需要原厂货',
      supplier: '广州电子科技有限公司',
      referenceLink: '',
      actualInquirer: '李明',
      auditTime: '2025-06-15 15:20:00',
      auditor: '张经理',
      auditAttitude: '通过',
      auditRemark: '符合客户要求',
    },
    {
      id: 3,
      actualProductName: '二极管',
      actualBrand: '安森美',
      actualModel: '1N4148W-7-F',
      actualSpecification: '小信号二极管 SOD-123',
      packageQuantity: 3000,
      inquiryProductCode: 'INQ20250616003',
      productCode: 'P20250616003',
      costPrice: 0.09,
      inquiryUnit: '个',
      taxAndShipping: '含税含运费',
      shippingCost: 30,
      deliveryPeriod: '5天',
      additionalFee: 5,
      sellingPrice: 0.12,
      actualInquiryRemark: '需要原厂正品',
      supplier: '上海半导体贸易有限公司',
      referenceLink: 'https://example.com/product3',
      actualInquirer: '王华',
      auditTime: '2025-06-16 09:15:00',
      auditor: '李总监',
      auditAttitude: '通过',
      auditRemark: '价格有竞争力',
    },
    {
      id: 4,
      actualProductName: '三极管',
      actualBrand: '安森美',
      actualModel: 'MMBT3904LT1G',
      actualSpecification: 'NPN 40V 200mA SOT-23',
      packageQuantity: 2500,
      inquiryProductCode: 'INQ20250616004',
      productCode: 'P20250616004',
      costPrice: 0.11,
      inquiryUnit: '个',
      taxAndShipping: '不含税不含运费',
      shippingCost: 60,
      deliveryPeriod: '15天',
      additionalFee: 0,
      sellingPrice: 0.15,
      actualInquiryRemark: '需要原装',
      supplier: '北京电子元器件有限公司',
      referenceLink: '',
      actualInquirer: '王华',
      auditTime: '2025-06-16 10:45:00',
      auditor: '李总监',
      auditAttitude: '需调整',
      auditRemark: '货期太长，建议寻找其他供应商',
    },
    {
      id: 5,
      actualProductName: '集成电路',
      actualBrand: 'TI',
      actualModel: 'LM358DR',
      actualSpecification: '双运算放大器 SOIC-8',
      packageQuantity: 1000,
      inquiryProductCode: 'INQ20250616005',
      productCode: 'P20250616005',
      costPrice: 0.95,
      inquiryUnit: '个',
      taxAndShipping: '含税含运费',
      shippingCost: 40,
      deliveryPeriod: '12天',
      additionalFee: 15,
      sellingPrice: 1.20,
      actualInquiryRemark: '需要17+批次',
      supplier: '深圳市芯片科技有限公司',
      referenceLink: 'https://example.com/product5',
      actualInquirer: '张伟',
      auditTime: '2025-06-16 14:00:00',
      auditor: '王副总',
      auditAttitude: '通过',
      auditRemark: '批次符合要求，价格合理',
    },
  ]);
  
  // 选择行相关
  const selectedRowKeys = ref([]);
  const selectAll = ref(false);
  
  // 行选择配置
  const rowSelection = reactive({
    type: 'checkbox',
    showCheckedAll: true,
    selectedRowKeys: selectedRowKeys,
    onChange: (selectedKeys) => {
      selectedRowKeys.value = selectedKeys;
      selectAll.value = selectedKeys.length === tableData.length;
    },
  });
  
  // 全选/取消全选处理
  const handleSelectAllChange = (checked) => {
    if (checked) {
      selectedRowKeys.value = tableData.map(item => item.id);
    } else {
      selectedRowKeys.value = [];
    }
  };
  
  // 确认按钮处理
  const handleOk = () => {
    // 触发确认事件
    emit('ok');
    
    // 关闭弹窗
    visible.value = false;
    emit('update:visible', false);
    
    // 重置组件状态
    resetState();
  };
  
  // 取消按钮处理
  const handleCancel = () => {
    visible.value = false;
    emit('update:visible', false);
    emit('cancel');
    resetState();
  };
  
  // 重置组件状态
  const resetState = () => {
    selectedInquirer.value = '';
    selectedRowKeys.value = [];
    selectAll.value = false;
  };
  
  // 暴露给父组件的打开弹窗方法
  const open = () => {
    visible.value = true;
  };
  
  // 暴露方法给父组件
  defineExpose({
    open
  });
  </script>