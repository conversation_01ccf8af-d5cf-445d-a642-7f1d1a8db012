<template>
  <a-drawer
    :visible="visible"
    title="确认重复商品"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="mb-4">
      <div class="text-lg font-medium mb-2">客户询价商品信息</div>
      <a-table
        :data="data"
        :bordered="true"
        :pagination="false"
        :scroll="{ x: '100%' }"
      >
        <template #columns>
          <a-table-column
            title="产品名称"
            data-index="productName"
            :width="150"
          />
          <a-table-column
            title="询价产品编码"
            data-index="inquiryProductCode"
            :width="120"
          />
          <a-table-column title="单位" data-index="unit" :width="80" />
          <a-table-column title="品牌" data-index="brand" :width="100" />
          <a-table-column title="型号" data-index="model" :width="120" />
          <a-table-column
            title="规格描述"
            data-index="specification"
            :width="180"
          />
          <a-table-column
            title="参考链接"
            data-index="referenceLink"
            :width="150"
          />
          <a-table-column
            title="需求数量"
            data-index="demandQuantity"
            :width="100"
          />
          <a-table-column
            title="参考价格"
            data-index="referencePrice"
            :width="100"
          />
          <a-table-column
            title="客户产品编码"
            data-index="customerProductCode"
            :width="120"
          />
          <a-table-column
            title="询价备注"
            data-index="inquiryRemark"
            :width="150"
          />
          <a-table-column
            title="询价员"
            data-index="inquiryStaff"
            :width="100"
          />
        </template>
      </a-table>
    </div>

    <div class="mb-4">
      <div class="text-lg font-medium mb-2">询价信息</div>
      <a-table
        :data="inquiryData"
        :bordered="true"
        :pagination="{ total: 3, showTotal: true }"
        :scroll="{ x: '2000px' }"
      >
        <template #columns>
          <a-table-column
            title="实际产品名称"
            data-index="actualProductName"
            :width="150"
          />
          <a-table-column
            title="实际品牌"
            data-index="actualBrand"
            :width="100"
          />
          <a-table-column
            title="实际型号"
            data-index="actualModel"
            :width="120"
          />
          <a-table-column
            title="实际规格描述"
            data-index="actualSpecification"
            :width="180"
          />
          <a-table-column
            title="包装量"
            data-index="packageAmount"
            :width="100"
          />
          <a-table-column
            title="询价产品编码"
            data-index="inquiryProductCode"
            :width="120"
          />
          <a-table-column
            title="产品编码"
            data-index="productCode"
            :width="100"
          />
          <a-table-column title="成本价" data-index="costPrice" :width="90" />
          <a-table-column
            title="询价单位"
            data-index="inquiryUnit"
            :width="90"
          />
          <a-table-column
            title="含税运情况"
            data-index="taxIncludedShipping"
            :width="110"
          />
          <a-table-column title="运费" data-index="shippingCost" :width="80" />
          <a-table-column
            title="货期"
            data-index="deliveryPeriod"
            :width="80"
          />
          <a-table-column
            title="附加费"
            data-index="additionalFee"
            :width="80"
          />
          <a-table-column
            title="协议价"
            data-index="agreementPrice"
            :width="90"
          />
          <a-table-column
            title="时间询价备注"
            data-index="timeInquiryRemark"
            :width="150"
          />
          <a-table-column title="供应商" data-index="supplier" :width="150" />
          <a-table-column
            title="上架资料链接"
            data-index="shelfInfoLink"
            :width="150"
          />
          <a-table-column
            title="实际询价员"
            data-index="actualInquirer"
            :width="100"
          />
          <a-table-column
            title="询价状态"
            data-index="inquiryStatus"
            :width="90"
          />
          <a-table-column
            title="实际状态"
            data-index="actualStatus"
            :width="90"
          />
          <a-table-column title="操作" fixed="right" :width="100">
            <template #cell="{ record }">
              <a-radio
                :model-value="selectedItem === record"
                @change="() => selectItem(record)"
                >使用</a-radio
              >
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineExpose } from "vue";

const visible = ref(false);
const data = ref([]);
const inquiryData = ref([]);
const currentRecord = ref(null);
const currentPage = ref(1);
const selectedItem = ref(null);

// 模拟询价信息数据
const initInquiryData = () => {
  return [
    {
      actualProductName: "高压电容器",
      actualBrand: "德力",
      actualModel: "DL-HVC-500",
      actualSpecification: "500V 10μF 陶瓷电容",
      packageAmount: "50个/盒",
      inquiryProductCode: "HVC-2023-001",
      productCode: "P110",
      costPrice: "22.5",
      inquiryUnit: "个",
      taxIncludedShipping: "含税含运",
      shippingCost: "200",
      deliveryPeriod: "7天",
      additionalFee: "50",
      agreementPrice: "25.8",
      timeInquiryRemark: "急需",
      supplier: "德力电子有限公司",
      shelfInfoLink: "https://example.com/shelf/HVC-2023-001",
      actualInquirer: "张三",
      inquiryStatus: "已完成",
      actualStatus: "有货",
    },
    {
      actualProductName: "精密电阻",
      actualBrand: "欧姆",
      actualModel: "OM-PR-100",
      actualSpecification: "100Ω ±0.1% 金属膜电阻",
      packageAmount: "100个/盒",
      inquiryProductCode: "PR-2023-002",
      productCode: "P112",
      costPrice: "1.2",
      inquiryUnit: "个",
      taxIncludedShipping: "含税不含运",
      shippingCost: "50",
      deliveryPeriod: "3天",
      additionalFee: "0",
      agreementPrice: "1.5",
      timeInquiryRemark: "常规",
      supplier: "欧姆电子科技",
      shelfInfoLink: "https://example.com/shelf/PR-2023-002",
      actualInquirer: "李四",
      inquiryStatus: "处理中",
      actualStatus: "缺货",
    },
    {
      actualProductName: "散热风扇",
      actualBrand: "冷风",
      actualModel: "CF-120",
      actualSpecification: "120mm 2000RPM",
      packageAmount: "10个/箱",
      inquiryProductCode: "FAN-2023-003",
      productCode: "P115",
      costPrice: "35.0",
      inquiryUnit: "台",
      taxIncludedShipping: "不含税不含运",
      shippingCost: "100",
      deliveryPeriod: "5天",
      additionalFee: "20",
      agreementPrice: "42.0",
      timeInquiryRemark: "需要测试报告",
      supplier: "冷风科技",
      shelfInfoLink: "https://example.com/shelf/FAN-2023-003",
      actualInquirer: "王五",
      inquiryStatus: "待确认",
      actualStatus: "部分有货",
    },
  ];
};

const handleOk = () => {
  visible.value = false;
};

const handleCancel = () => {
  visible.value = false;
};

const handleView = (record) => {
  // 查看详情逻辑
  console.log("查看详情", record);
};

const selectItem = (record) => {
  selectedItem.value = record;
  console.log("已选择", record);
};

const handlePageChange = (page) => {
  currentPage.value = page;
  // 这里可以添加分页加载数据的逻辑
};

const open = (record) => {
  currentRecord.value = record;
  // 设置客户询价商品信息
  data.value = [
    {
      productName: "高压电容器",
      inquiryProductCode: "HVC-2023-001",
      unit: "个",
      brand: "德力",
      model: "DL-HVC-500",
      specification: "500V 10μF 陶瓷电容",
      referenceLink: "https://example.com/capacitor-500v",
      demandQuantity: "50",
      referencePrice: "25.8",
      customerProductCode: "CUS-CAP-001",
      inquiryRemark: "需要防潮包装",
      inquiryStaff: "张三",
    },
    {
      productName: "精密电阻",
      inquiryProductCode: "PR-2023-002",
      unit: "个",
      brand: "欧姆",
      model: "OM-PR-100",
      specification: "100Ω ±0.1% 金属膜电阻",
      referenceLink: "https://example.com/resistor-100",
      demandQuantity: "200",
      referencePrice: "1.5",
      customerProductCode: "CUS-RES-002",
      inquiryRemark: "需要防静电包装",
      inquiryStaff: "李四",
    },
    {
      productName: "散热风扇",
      inquiryProductCode: "FAN-2023-003",
      unit: "台",
      brand: "冷风",
      model: "CF-120",
      specification: "120mm 3000RPM 静音风扇",
      referenceLink: "https://example.com/fan-120mm",
      demandQuantity: "30",
      referencePrice: "45.6",
      customerProductCode: "CUS-FAN-003",
      inquiryRemark: "需要长寿命型号",
      inquiryStaff: "王五",
    },
  ];

  // 设置询价信息数据
  inquiryData.value = initInquiryData();
  visible.value = true;
};

defineExpose({
  open,
});
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
