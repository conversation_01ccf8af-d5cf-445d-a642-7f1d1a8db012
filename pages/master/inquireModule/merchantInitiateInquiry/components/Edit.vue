<template>
  <a-drawer
    :visible="visible"
    title="编辑询价信息"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div>
      <a-form :model="formData" ref="formRef">
        <!-- 第一行：询价序号、产品名称、品牌 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              field="inquiryNo"
              label="询价序号"
              label-col-flex="100px"
              :rules="[{ required: true, message: '请输入询价序号' }]"
            >
              <a-input
                v-model="formData.inquiryNo"
                placeholder="请输入询价序号"
                :disabled="true"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="productName"
              label="产品名称"
              label-col-flex="100px"
              :rules="[{ required: true, message: '请输入产品名称' }]"
            >
              <a-input
                v-model="formData.productName"
                placeholder="请输入产品名称"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="brand" label="品牌" label-col-flex="100px">
              <a-input v-model="formData.brand" placeholder="请输入品牌" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第二行：型号、单位、需求数量 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="model" label="型号" label-col-flex="100px">
              <a-input v-model="formData.model" placeholder="请输入型号" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="unit" label="单位" label-col-flex="100px">
              <a-input v-model="formData.unit" placeholder="请输入单位" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item
              field="quantity"
              label="需求数量"
              label-col-flex="100px"
              :rules="[{ required: true, message: '请输入需求数量' }]"
            >
              <a-input-number
                v-model="formData.quantity"
                placeholder="请输入需求数量"
                :min="0"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第三行：参考价格、客户产品编码 -->
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item
              field="referencePrice"
              label="参考价格"
              label-col-flex="100px"
            >
              <a-input-number
                v-model="formData.referencePrice"
                placeholder="请输入参考价格"
                :min="0"
                :precision="2"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="16">
            <a-form-item
              field="customerProductCode"
              label="客户产品编码"
              label-col-flex="100px"
            >
              <a-input
                v-model="formData.customerProductCode"
                placeholder="请输入客户产品编码"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第四行：规格描述 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item
              field="specification"
              label="规格描述"
              label-col-flex="100px"
            >
              <a-textarea
                v-model="formData.specification"
                placeholder="请输入规格描述"
                :auto-size="{ minRows: 2, maxRows: 4 }"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第五行：参考链接 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item
              field="referenceLink"
              label="参考链接"
              label-col-flex="100px"
            >
              <a-input
                v-model="formData.referenceLink"
                placeholder="请输入参考链接"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第六行：询价备注 -->
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item
              field="inquiryRemark"
              label="询价备注"
              label-col-flex="100px"
            >
              <a-textarea
                v-model="formData.inquiryRemark"
                placeholder="请输入询价备注"
                :auto-size="{ minRows: 2, maxRows: 4 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch } from "vue";
import { Message } from "@arco-design/web-vue";

const props = defineProps({
  record: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update"]);

const visible = ref(false);
const formRef = ref(null);
const currentIndex = ref(-1);

// 表单数据
const formData = reactive({
  inquiryNo: "",
  productName: "",
  brand: "",
  model: "",
  specification: "",
  referenceLink: "",
  unit: "",
  quantity: 0,
  referencePrice: 0,
  customerProductCode: "",
  inquiryRemark: "",
});

// 监听表单数据变化
watch(
  () => props.record,
  (newVal) => {
    if (newVal) {
      Object.assign(formData, newVal);
    }
  },
  { deep: true, immediate: true }
);

// 表单验证
const validate = () => {
  return new Promise((resolve, reject) => {
    if (formRef.value) {
      formRef.value
        .validate()
        .then(() => {
          resolve(true);
        })
        .catch((error) => {
          reject(error);
        });
    } else {
      console.warn("表单引用不存在，跳过验证");
      resolve(true);
    }
  });
};

// 确认按钮
const handleOk = async () => {
  try {
    await validate();
    // 触发父组件更新数据
    emit("update", formData, currentIndex.value);
    Message.success("保存成功");
    visible.value = false;
  } catch (error) {
    console.error("表单验证失败:", error);
  }
};

// 取消按钮
const handleCancel = () => {
  visible.value = false;
};

// 打开弹窗
const open = (record, index) => {
  Object.assign(formData, record);
  currentIndex.value = index;
  visible.value = true;
};

// 关闭弹窗
const close = () => {
  visible.value = false;
  currentIndex.value = -1;
  // 重置表单数据
  Object.keys(formData).forEach((key) => {
    if (typeof formData[key] === "number") {
      formData[key] = 0;
    } else {
      formData[key] = "";
    }
  });
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 清除验证
const clearValidate = (fields) => {
  if (formRef.value) {
    formRef.value.clearValidate(fields);
  }
};

// 暴露方法给父组件
defineExpose({
  open,
  close,
  formRef,
  formData,
  validate,
  resetForm,
  clearValidate,
});
</script>

<style scoped>
.arco-form-item-layout-vertical {
  margin-bottom: 16px;
}
</style>
