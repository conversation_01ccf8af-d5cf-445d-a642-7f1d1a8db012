<template>
  <a-drawer
    :visible="visible"
    title="询价导入"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
  >

    <ma-upload type="file" draggable v-model="fileList" :multiple="true" :limit="3" :accept="'.xlsx,.xls'">
    </ma-upload>
    <a-divider />
    <a-table :columns="columns" :data="data">
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">{{ record.status }}</a-tag>
      </template>
      <template #fileDownload="{ record }">
        <a-button type="text" @click="downloadFile(record.fileUrl)">
          <template #icon><icon-download /></template>
          下载
        </a-button>
      </template>
      <template #resultDownload="{ record }">
        <a-button type="text" @click="downloadFile(record.resultUrl)">
          <template #icon><icon-download /></template>
          下载
        </a-button>
      </template>
    </a-table>
  </a-drawer>
</template>

<script setup>
import { ref } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconDownload } from "@arco-design/web-vue/es/icon";

const visible = ref(false);

const fileList = ref([]);

const emit = defineEmits(["update:visible", "ok", "cancel"]);

// 表格列定义
const columns = [
  {
    title: "导入文件名称",
    dataIndex: "fileName",
  },
  {
    title: "导入时间",
    dataIndex: "importTime",
  },
  {
    title: "更新状态",
    dataIndex: "status",
    slotName: "status",
  },
  {
    title: "导入文件下载",
    dataIndex: "fileDownload",
    slotName: "fileDownload",
  },
  {
    title: "导入结果下载",
    dataIndex: "resultDownload",
    slotName: "resultDownload",
  },
];

// 模拟表格数据
const data = ref([
  {
    id: 1,
    fileName: "询价导入数据20250617.xlsx",
    importTime: "2025-06-17 15:30:22",
    status: "成功",
    fileUrl: "https://example.com/files/询价导入数据20250617.xlsx",
    resultUrl: "https://example.com/results/询价导入结果20250617.xlsx",
  },
  {
    id: 2,
    fileName: "询价导入数据20250616.xlsx",
    importTime: "2025-06-16 14:25:18",
    status: "部分成功",
    fileUrl: "https://example.com/files/询价导入数据20250616.xlsx",
    resultUrl: "https://example.com/results/询价导入结果20250616.xlsx",
  },
  {
    id: 3,
    fileName: "询价导入数据20250615.xlsx",
    importTime: "2025-06-15 09:15:36",
    status: "失败",
    fileUrl: "https://example.com/files/询价导入数据20250615.xlsx",
    resultUrl: "https://example.com/results/询价导入结果20250615.xlsx",
  },
]);

// 打开弹窗方法，供外部调用
const open = () => {
  visible.value = true;
  fileList.value = ['https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/6d23a963132792e01bdd4807e35d6938.jpeg'];
};

const handleOk = () => {
  // 这里可以添加导入文件的处理逻辑
  Message.success('导入成功');
  console.log(fileList.value);
  visible.value = false;
  emit('ok');
};

const handleCancel = () => {
  visible.value = false;
  emit('cancel');
};

// 获取状态标签颜色
const getStatusColor = (status) => {
  switch (status) {
    case "成功":
      return "green";
    case "部分成功":
      return "orange";
    case "失败":
      return "red";
    default:
      return "blue";
  }
};

// 下载文件
const downloadFile = (url) => {
  window.open(url, "_blank");
  Message.success("开始下载文件");
};

// 暴露方法供父组件调用
defineExpose({
  open,
});
</script>
