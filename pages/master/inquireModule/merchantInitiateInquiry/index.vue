<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <div>
    <a-card class="general-card" title="商户发起询价">
      <ma-form
        ref="formRef"
        :columns="columns"
        :options="formOptions"
        v-model="formData"
        @submit="handleSubmit"
        class="grid-form"
      >
      </ma-form>

      <!-- 引入询价导入组件 -->
      <InquiryImport ref="inquiryImportRef" />

      <!-- 引入确认重复项组件 -->
      <ConfirmDuplicates ref="confirmDuplicatesRef" />

      <!-- 引入编辑弹窗组件 -->
      <Edit ref="editRef" @update="handleEditUpdate" />

      <div class="mt-4">
        <div class="flex justify-between mb-2">
          <div class="text-lg font-medium">询价信息</div>
          <div>
            <a-button type="primary" @click="addRow">
              <template #icon><icon-plus /></template>
              询价信息导入
            </a-button>
          </div>
        </div>

        <a-table
          :data="tableData"
          :bordered="true"
          :pagination="false"
          :scroll="{ x: '100%' }"
        >
          <!-- 询价序号 -->
          <template #columns>
            <a-table-column title="询价序号" data-index="inquiryNo" width="100">
              <template #cell="{ record }">
                <span>{{ record.inquiryNo }}</span>
              </template>
            </a-table-column>

            <!-- 产品名称 -->
            <a-table-column
              title="产品名称"
              data-index="productName"
              width="150"
            >
              <template #cell="{ record }">
                <span>{{ record.productName }}</span>
              </template>
            </a-table-column>

            <!-- 品牌 -->
            <a-table-column title="品牌" data-index="brand" width="120">
              <template #cell="{ record }">
                <span>{{ record.brand }}</span>
              </template>
            </a-table-column>

            <!-- 型号 -->
            <a-table-column title="型号" data-index="model" width="120">
              <template #cell="{ record }">
                <span>{{ record.model }}</span>
              </template>
            </a-table-column>

            <!-- 规格描述 -->
            <a-table-column
              title="规格描述"
              data-index="specification"
              width="180"
            >
              <template #cell="{ record }">
                <span>{{ record.specification }}</span>
              </template>
            </a-table-column>

            <!-- 参考链接 -->
            <a-table-column
              title="参考链接"
              data-index="referenceLink"
              width="180"
            >
              <template #cell="{ record }">
                <span>{{ record.referenceLink }}</span>
              </template>
            </a-table-column>

            <!-- 单位 -->
            <a-table-column title="单位" data-index="unit" width="80">
              <template #cell="{ record }">
                <span>{{ record.unit }}</span>
              </template>
            </a-table-column>

            <!-- 需求数量 -->
            <a-table-column title="需求数量" data-index="quantity" width="100">
              <template #cell="{ record }">
                <span>{{ record.quantity }}</span>
              </template>
            </a-table-column>

            <!-- 参考价格 -->
            <a-table-column
              title="参考价格"
              data-index="referencePrice"
              width="120"
            >
              <template #cell="{ record }">
                <span>{{ record.referencePrice }}</span>
              </template>
            </a-table-column>

            <!-- 客户产品编码 -->
            <a-table-column
              title="客户产品编码"
              data-index="customerProductCode"
              width="150"
            >
              <template #cell="{ record }">
                <span>{{ record.customerProductCode }}</span>
              </template>
            </a-table-column>

            <!-- 询价备注 -->
            <a-table-column
              title="询价备注"
              data-index="inquiryRemark"
              width="180"
            >
              <template #cell="{ record }">
                <span>{{ record.inquiryRemark }}</span>
              </template>
            </a-table-column>

            <!-- 操作 -->
            <a-table-column title="操作" width="400" fixed="right">
              <template #cell="{ rowIndex, record }">
                <a-space>
                  <a-button type="text" @click="editRow(record, rowIndex)">
                    <template #icon><icon-edit /></template>
                    编辑
                  </a-button>
                  <a-button
                    type="text"
                    status="warning"
                    @click="checkDuplicate(record)"
                  >
                    <template #icon><icon-copy /></template>
                    确认重复项
                  </a-button>
                  <a-popconfirm
                    content="确定要删除此项吗？"
                    @ok="removeRow(rowIndex)"
                  >
                    <a-button type="text" status="danger">
                      <template #icon><icon-delete /></template>
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>

        <div class="mt-4">
          <ma-upload
            v-model="uploadFile"
            type="file"
            :multiple="false"
            accept=".xlsx,.xls,.csv"
            :tip="'支持上传Excel或CSV格式文件，大小不超过5MB'"
            @update:modelValue="handleFileUpload"
          />

          <div class="mt-4">
            <div class="flex justify-between items-center mb-2">
              <div class="text-lg font-medium">重复项信息</div>
            </div>

            <a-table
              :data="duplicateData"
              :bordered="true"
              :pagination="false"
              :scroll="{ x: '100%' }"
              :row-class="getRowClass"
            >
              <template #columns>
                <!-- 实际产品名称 -->
                <a-table-column
                  title="实际产品名称"
                  data-index="actualProductName"
                  width="150"
                />

                <!-- 实际品牌 -->
                <a-table-column
                  title="实际品牌"
                  data-index="actualBrand"
                  width="120"
                />

                <!-- 实际型号 -->
                <a-table-column
                  title="实际型号"
                  data-index="actualModel"
                  width="120"
                />

                <!-- 实际规格描述 -->
                <a-table-column
                  title="实际规格描述"
                  data-index="actualSpecification"
                  width="150"
                />

                <!-- 包装量 -->
                <a-table-column
                  title="包装量"
                  data-index="packagingQuantity"
                  width="100"
                />

                <!-- 询价产品编码 -->
                <a-table-column
                  title="询价产品编码"
                  data-index="inquiryProductCode"
                  width="150"
                />

                <!-- 产品编码 -->
                <a-table-column
                  title="产品编码"
                  data-index="productCode"
                  width="120"
                />

                <!-- 成本价 -->
                <a-table-column
                  title="成本价"
                  data-index="costPrice"
                  width="100"
                />

                <!-- 询价单位 -->
                <a-table-column
                  title="询价单位"
                  data-index="inquiryUnit"
                  width="100"
                />

                <!-- 含税运情况 -->
                <a-table-column
                  title="含税运情况"
                  data-index="taxAndShipping"
                  width="120"
                />

                <!-- 运费 -->
                <a-table-column
                  title="运费"
                  data-index="shippingCost"
                  width="100"
                />

                <!-- 货期 -->
                <a-table-column
                  title="货期"
                  data-index="deliveryPeriod"
                  width="100"
                />

                <!-- 附加费 -->
                <a-table-column
                  title="附加费"
                  data-index="additionalFee"
                  width="100"
                />

                <!-- 销售价 -->
                <a-table-column
                  title="销售价"
                  data-index="sellingPrice"
                  width="100"
                />

                <!-- 实际询价备注 -->
                <a-table-column
                  title="实际询价备注"
                  data-index="actualInquiryRemark"
                  width="150"
                />

                <!-- 上架资料链接 -->
                <a-table-column
                  title="上架资料链接"
                  data-index="shelfInfoLink"
                  width="150"
                />

                <!-- 实际询价 -->
                <a-table-column
                  title="实际询价"
                  data-index="actualInquiry"
                  width="100"
                />

                <!-- 询价状态 -->
                <a-table-column
                  title="询价状态"
                  data-index="inquiryStatus"
                  width="100"
                >
                  <template #cell="{ record }">
                    <a-tag :color="getStatusColor(record.inquiryStatus)">
                      {{ record.inquiryStatus }}
                    </a-tag>
                  </template>
                </a-table-column>
              </template>
            </a-table>
          </div>

          <div class="flex justify-between mt-4">
            <a-space>
              <a-button @click="resetInquiry">返回</a-button>
              <a-button type="primary" @click="submitInquiry"
                >提交询价</a-button
              >
            </a-space>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRoute, useRouter } from "vue-router";
import { closeCurrentTagAndNavigate } from "@/utils/common";
import InquiryImport from "./components/InquiryImport.vue";
import ConfirmDuplicates from "./components/ConfirmDuplicates.vue";
import Edit from "./components/Edit.vue";
definePageMeta({
  name: "master-inquireModule-merchantInitiateInquiry",
  path: "/master/inquireModule/merchantInitiateInquiry",
});
const route = useRoute();
const router = useRouter();
// 表单引用
const formRef = ref(null);

// 确认重复项组件引用
const confirmDuplicatesRef = ref(null);

// 编辑弹窗组件引用
const editRef = ref(null);

// 表单数据
const formData = reactive({
  customerFullName: "",
  customerCode: "",
  customerContact: "",
  customerAddress: "",
  salesman: "",
  inquirer: "",
  orderPlatform: "",
});

// 表格数据
const tableData = ref([
  {
    inquiryNo: "INQ20250619001",
    productName: "高压电容器",
    brand: "科陆",
    model: "KL-HC1000",
    specification: "1000μF/450V",
    referenceLink: "https://example.com/product/KL-HC1000",
    unit: "个",
    quantity: 100,
    referencePrice: 130.5,
    customerProductCode: "CUS-CAP-001",
    inquiryRemark: "需要质检报告和技术参数",
  },
  {
    inquiryNo: "INQ20250619002",
    productName: "直流电机",
    brand: "松下",
    model: "PANA-M2000",
    specification: "24V 2000RPM",
    referenceLink: "https://example.com/product/PANA-M2000",
    unit: "台",
    quantity: 20,
    referencePrice: 350.0,
    customerProductCode: "CUS-MOT-002",
    inquiryRemark: "需要技术参数表和使用说明",
  },
  {
    inquiryNo: "INQ20250619003",
    productName: "温度传感器",
    brand: "西门子",
    model: "SIEMENS-TS100",
    specification: "-50°C至150°C",
    referenceLink: "https://example.com/product/SIEMENS-TS100",
    unit: "个",
    quantity: 200,
    referencePrice: 45.6,
    customerProductCode: "CUS-SEN-003",
    inquiryRemark: "需要校准证书",
  },
  {
    inquiryNo: "INQ20250619004",
    productName: "LED驱动电源",
    brand: "明纬",
    model: "MW-LPV-60",
    specification: "60W 12V 5A",
    referenceLink: "https://example.com/product/MW-LPV-60",
    unit: "个",
    quantity: 50,
    referencePrice: 85.0,
    customerProductCode: "CUS-PWR-004",
    inquiryRemark: "需要CE认证",
  },
  {
    inquiryNo: "INQ20250619005",
    productName: "压力变送器",
    brand: "ABB",
    model: "ABB-PT100",
    specification: "0-10MPa",
    referenceLink: "https://example.com/product/ABB-PT100",
    unit: "台",
    quantity: 30,
    referencePrice: 1200.0,
    customerProductCode: "CUS-TRA-005",
    inquiryRemark: "需要防爆认证",
  },
]);

// 上传文件
const uploadFile = ref(null);

// 重复项数据
const duplicateData = ref([
  {
    actualProductName: "高压电容器",
    actualBrand: "科陆",
    actualModel: "KL-HC1000",
    actualSpecification: "1000μF/450V",
    packagingQuantity: 50,
    inquiryProductCode: "CUS-CAP-001",
    productCode: "P20250619",
    costPrice: "128.50",
    inquiryUnit: "个",
    taxAndShipping: "含税不含运",
    shippingCost: "15.00",
    deliveryPeriod: "7-10天",
    additionalFee: "0.00",
    sellingPrice: "150.00",
    actualInquiryRemark: "需要质检报告",
    shelfInfoLink: "https://example.com/product/KL-HC1000",
    actualInquiry: "是",
    inquiryStatus: "待询价",
    isHighlighted: true,
  },
  {
    actualProductName: "直流电机",
    actualBrand: "松下",
    actualModel: "PANA-M2000",
    actualSpecification: "24V 2000RPM",
    packagingQuantity: 10,
    inquiryProductCode: "CUS-MOT-002",
    productCode: "P20250620",
    costPrice: "320.00",
    inquiryUnit: "台",
    taxAndShipping: "含税含运",
    shippingCost: "0.00",
    deliveryPeriod: "15-20天",
    additionalFee: "25.00",
    sellingPrice: "420.00",
    actualInquiryRemark: "需要技术参数表",
    shelfInfoLink: "https://example.com/product/PANA-M2000",
    actualInquiry: "是",
    inquiryStatus: "已询价",
    isHighlighted: false,
  },
  {
    actualProductName: "温度传感器",
    actualBrand: "西门子",
    actualModel: "SIEMENS-TS100",
    actualSpecification: "-50°C至150°C",
    packagingQuantity: 100,
    inquiryProductCode: "CUS-SEN-003",
    productCode: "P20250621",
    costPrice: "45.60",
    inquiryUnit: "个",
    taxAndShipping: "含税不含运",
    shippingCost: "8.50",
    deliveryPeriod: "5-7天",
    additionalFee: "0.00",
    sellingPrice: "65.00",
    actualInquiryRemark: "需要校准证书",
    shelfInfoLink: "https://example.com/product/SIEMENS-TS100",
    actualInquiry: "是",
    inquiryStatus: "询价中",
    isHighlighted: true,
  },
]);

// 平台选项
const platformOptions = [
  { label: "平台一", value: "platform1" },
  { label: "平台二", value: "platform2" },
  { label: "平台三", value: "platform3" },
];

// 表单配置项
const formOptions = reactive({
  submitText: "查询",
  resetText: "重置",
  labelWidth: "110px",
  layout: "horizontal",
  submitShowBtn: false, // 隐藏提交按钮
  resetShowBtn: false, // 隐藏重置按钮
  showButtons: false, // 隐藏所有按钮
  customClass: "grid-form", // 添加自定义类名
  gutter: 12, // 设置栅格间隔
});

// 表单字段配置
const columns = [
  {
    title: "客户全称",
    dataIndex: "customerFullName",
    formType: "input",
    rules: [{ required: true, message: "请输入客户全称" }],
    labelWidth: "110px",
    formProps: {
      placeholder: "请输入客户全称...",
    },
  },
  {
    title: "客户编码",
    dataIndex: "customerCode",
    formType: "input",
    rules: [{ required: true, message: "请输入客户编码" }],
    labelWidth: "110px",
    formProps: {
      placeholder: "请输入客户编码...",
    },
  },
  {
    title: "客户联系人",
    dataIndex: "customerContact",
    formType: "input",
    rules: [{ required: true, message: "请输入客户联系人" }],
    labelWidth: "110px",
    formProps: {
      placeholder: "请输入客户联系人...",
    },
  },
  {
    title: "客户地址",
    dataIndex: "customerAddress",
    formType: "input",
    labelWidth: "110px",
    formProps: {
      placeholder: "请输入客户地址...",
    },
  },
  {
    title: "业务员",
    dataIndex: "salesman",
    formType: "input",
    labelWidth: "110px",
    formProps: {
      placeholder: "请输入业务员...",
    },
  },
  {
    title: "询价员",
    dataIndex: "inquirer",
    formType: "input",
    labelWidth: "110px",
    formProps: {
      placeholder: "请输入询价员...",
    },
  },
  {
    title: "下单平台",
    dataIndex: "orderPlatform",
    formType: "select",
    labelWidth: "110px",
    dict: {
      data: platformOptions,
    },
    formProps: {
      placeholder: "请选择下单平台...",
    },
  },
];

// 初始化数据
onMounted(() => {
  // 添加一行空数据作为初始行
  // addRow();
});

// 询价导入组件引用
const inquiryImportRef = ref(null);

// 添加行
const addRow = () => {
  // 打开询价导入组件
  inquiryImportRef.value.open();
};

// 删除行
const removeRow = (index) => {
  tableData.value.splice(index, 1);
  // 如果删除后没有数据了，添加一行空数据
  if (tableData.value.length === 0) {
    addRow();
  }
};

// 编辑行
const editRow = (record, index) => {
  // 打开编辑弹窗
  editRef.value.open(record, index);
};

// 处理编辑组件的更新事件
const handleEditUpdate = (updatedRecord, index) => {
  if (index >= 0 && index < tableData.value.length) {
    tableData.value[index] = { ...updatedRecord };
    Message.success("询价信息已更新");
  }
};

// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    待询价: "blue",
    询价中: "orange",
    已询价: "green",
    已取消: "red",
    已过期: "gray",
  };
  return statusMap[status] || "default";
};

// 获取行样式
const getRowClass = (record) => {
  if (record.isHighlighted) {
    return "highlighted-row";
  }
  return "";
};

// 确认重复项
const checkDuplicate = (record) => {
  // 直接打开确认重复项弹窗
  confirmDuplicatesRef.value.open(record);
};

// 返回到商户询价列表
const resetInquiry = () => {
  // 关闭当前标签页并跳转到商户询价列表页面
  closeCurrentTagAndNavigate(
    router,
    "/master/inquireModule/merchantInquiryList"
  );
};

// 提交询价
const submitInquiry = () => {
  // 验证表单数据
  if (!formData.customerFullName) {
    Message.error("请填写客户全称");
    return;
  }

  // 验证表格数据
  if (tableData.value.length === 0) {
    Message.error("请添加询价信息");
    return;
  }

  // 检查是否有空行
  const emptyRow = tableData.value.some((row) => !row.productName);
  if (emptyRow) {
    Message.error("请填写完整的产品信息");
    return;
  }

  // 构造提交数据
  const submitData = {
    customerInfo: { ...formData },
    inquiryItems: tableData.value,
  };

  console.log("提交的询价数据:", submitData);
  // 这里可以添加API请求逻辑

  Message.success("提交成功");
  // 关闭当前标签页并跳转到商户询价列表页面
  closeCurrentTagAndNavigate(
    router,
    "/master/inquireModule/merchantInquiryList"
  );
};

// 处理文件上传
const handleFileUpload = (file) => {
  if (!file) return;

  Message.loading("正在解析文件数据...");

  // 这里可以添加文件解析逻辑
  // 模拟从文件中解析数据并添加到表格中
  setTimeout(() => {
    // 模拟数据解析完成
    const mockData = [
      {
        inquiryNo: `INQ${new Date().getTime().toString().slice(-6)}`,
        productName: "测试产品A",
        brand: "品牌A",
        model: "A-001",
        specification: "规格描述A",
        referenceLink: "https://example.com/productA",
        unit: "个",
        quantity: 100,
        referencePrice: 199.99,
        customerProductCode: "CP-001",
        inquiryRemark: "导入的测试数据",
      },
      {
        inquiryNo: `INQ${new Date().getTime().toString().slice(-6) + 1}`,
        productName: "测试产品B",
        brand: "品牌B",
        model: "B-002",
        specification: "规格描述B",
        referenceLink: "https://example.com/productB",
        unit: "件",
        quantity: 50,
        referencePrice: 299.99,
        customerProductCode: "CP-002",
        inquiryRemark: "导入的测试数据",
      },
    ];

    // 将解析的数据添加到表格中
    tableData.value = [...tableData.value, ...mockData];

    Message.success(`成功导入 ${mockData.length} 条数据`);
  }, 1000);
};

// 下载模板
const downloadTemplate = () => {
  Message.info("正在准备下载模板...");

  // 这里可以添加下载模板的逻辑
  // 如果有实际的API，可以调用API下载模板文件
  setTimeout(() => {
    // 模拟下载成功
    Message.success("模板下载成功");
  }, 1000);
};

// 提交表单
const handleSubmit = (values) => {
  console.log("提交的表单数据:", values);
  // 这里可以添加表单提交逻辑，例如发送API请求等
  Message.success("提交成功");
};
</script>

<style scoped>
.grid-form :deep(.ma-form) {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.grid-form :deep(.ma-form .col-16) {
  grid-column: span 2;
}

.grid-form :deep(.ma-form .col-24) {
  grid-column: span 3;
}

/* 重复项高亮样式 */
.highlighted-row {
  background-color: rgba(var(--warning-6), 0.1);
}
</style>
