<template>
    <div >
        <a-card :title="pageTitle" :bordered="false">
            <a-form :model="formData" layout="vertical" :disabled="isViewMode">
                <!-- 客户信息 -->
                <div class="mb-4">
                    <div class="text-lg font-bold mb-2">客户</div>
                    <div class="grid grid-cols-3 gap-4">
                        <a-form-item field="clientName" label="客户全称">
                            <a-input v-model="formData.clientName" placeholder="请输入客户全称" />
                        </a-form-item>
                        <a-form-item field="clientCode" label="客户编码">
                            <a-input v-model="formData.clientCode" placeholder="请输入客户编码" />
                        </a-form-item>
                        <a-form-item field="seatAddress" label="所属地址">
                            <a-input v-model="formData.seatAddress" placeholder="请输入所在地" />
                        </a-form-item>
                    </div>
                </div>

                <!-- 基本信息 -->
                <div class="mb-4">
                    <div class="text-lg font-bold mb-2">基本信息</div>
                    <div class="grid grid-cols-3 gap-4">
                        <a-form-item field="platform" label="下单平台">
                            <a-select v-model="formData.platform" placeholder="请选择下单平台">
                                <a-option value="1">淘宝平台</a-option>
                                <a-option value="2">京东平台</a-option>
                                <a-option value="3">拼多多平台</a-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item field="orderNumber" label="下单编号">
                            <a-input v-model="formData.orderNumber" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="clientType" label="客户类型">
                            <a-select v-model="formData.clientType" placeholder="请选择">
                                <a-option value="1">普通客户</a-option>
                                <a-option value="2">VIP客户</a-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item field="clientProperty" label="客户性质">
                            <a-input v-model="formData.clientProperty" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="clientState" label="客户状态">
                            <a-select v-model="formData.clientState" placeholder="请选择">
                                <a-option value="1">正常</a-option>
                                <a-option value="2">冻结</a-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item field="responsibility" label="责任人">
                            <a-input v-model="formData.responsibility" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="industryType1" label="行业类型">
                            <a-input v-model="formData.industryType1" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="industryType2" label="行业类型二">
                            <a-input v-model="formData.industryType2" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="industryType3" label="行业类型三">
                            <a-input v-model="formData.industryType3" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="city" label="城市">
                            <a-input v-model="formData.city" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="birthday" label="生日">
                            <a-input v-model="formData.birthday" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="businessType1" label="业务类型1">
                            <a-select v-model="formData.businessType1" placeholder="请选择">
                                <a-option value="1">类型一</a-option>
                                <a-option value="2">类型二</a-option>
                            </a-select>
                        </a-form-item>
                        <a-form-item field="businessType2" label="业务类型2">
                            <a-input v-model="formData.businessType2" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="businessType3" label="业务类型3">
                            <a-input v-model="formData.businessType3" placeholder="请输入" />
                        </a-form-item>
                    </div>
                </div>

                <!-- 通讯信息 -->
                <div class="mb-4">
                    <div class="text-lg font-bold mb-2">通讯信息</div>
                    <div class="grid grid-cols-3 gap-4">
                        <a-form-item field="contactPerson" label="联系人">
                            <a-input v-model="formData.contactPerson" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="contactPhone" label="联系人电话">
                            <a-input v-model="formData.contactPhone" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="contactWeChat" label="联系人微信号">
                            <a-input v-model="formData.contactWeChat" placeholder="请输入" />
                        </a-form-item>
                    </div>
                    <div class="grid grid-cols-1 gap-4 mt-2">
                        <a-form-item field="isDefaultAddress" label="是否为关联收货地址">
                            <a-radio-group v-model="formData.isDefaultAddress">
                                <a-radio value="1">是</a-radio>
                                <a-radio value="0">否</a-radio>
                            </a-radio-group>
                        </a-form-item>
                        <a-form-item field="addressRegion" label="通讯地址">
                            <a-input v-model="formData.addressRegion" placeholder="请选择省市区" style="width: 200px;" />
                            <a-input v-model="formData.addressDetail" placeholder="请输入详细地址" class="ml-2" style="width: 320px;" />
                        </a-form-item>
                    </div>
                </div>

                <!-- 开票信息 -->
                <div class="mb-4">
                    <div class="text-lg font-bold mb-2">开票信息</div>
                    <div class="grid grid-cols-3 gap-4">
                        <a-form-item field="taxNumber" label="税号">
                            <a-input v-model="formData.taxNumber" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="bankAccount" label="开户行">
                            <a-input v-model="formData.bankAccount" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="bankNumber" label="银行账号">
                            <a-input v-model="formData.bankNumber" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="invoiceAddress" label="开票地址">
                            <a-input v-model="formData.invoiceAddress" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="invoicePhone" label="开票电话">
                            <a-input v-model="formData.invoicePhone" placeholder="请输入" />
                        </a-form-item>
                    </div>
                </div>

                <!-- 注意事项 -->
                <div class="mb-4">
                    <div class="text-lg font-bold mb-2">注意事项</div>
                    <div class="grid grid-cols-1 gap-4">
                        <a-form-item field="purchaseNote" label="采购注意事项">
                            <a-textarea v-model="formData.purchaseNote" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="logisticsNote" label="上架注意事项">
                            <a-textarea v-model="formData.logisticsNote" placeholder="请输入" />
                        </a-form-item>
                        <a-form-item field="deliveryNote" label="发货注意事项">
                            <a-textarea v-model="formData.deliveryNote" placeholder="请输入" />
                        </a-form-item>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-center mt-4">
                    <a-space>
                        <a-button type="primary" @click="saveForm" v-if="!isViewMode">保存</a-button>
                        <a-button @click="goBack">返回</a-button>
                    </a-space>
                </div>
            </a-form>
        </a-card>
    </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";

definePageMeta({
    name: "master-inquireModule-customerUnitList-id",
    path: "/master/inquireModule/customerUnitList/:id"
});

const route = useRoute();
const router = useRouter();
const id = route.params.id;
const isEdit = id !== 'create';
const isViewMode = route.query.mode === 'view';

// 页面标题
const pageTitle = isViewMode ? '客户单位详情（查看）' : isEdit ? '客户单位详情（编辑）' : '客户单位详情（新增）';

// 表单数据
const formData = ref({
    clientName: '',
    clientCode: '',
    seatAddress: '',
    platform: '',
    orderNumber: '',
    clientType: '',
    clientProperty: '',
    clientState: '',
    responsibility: '',
    industryType1: '',
    industryType2: '',
    industryType3: '',
    city: '',
    birthday: '',
    businessType1: '',
    businessType2: '',
    businessType3: '',
    contactPerson: '',
    contactPhone: '',
    contactWeChat: '',
    isDefaultAddress: '0',
    addressRegion: '',
    addressDetail: '',
    taxNumber: '',
    bankAccount: '',
    bankNumber: '',
    invoiceAddress: '',
    invoicePhone: '',
    purchaseNote: '',
    logisticsNote: '',
    deliveryNote: ''
});

// 获取详情数据
const fetchDetail = async () => {
    if (isEdit) {
        try {
            // 这里替换为实际的API调用
            // const res = await api.getCustomerDetail(id);
            // formData.value = res.data;
            console.log('获取客户单位详情', id);
        } catch (error) {
            console.error('获取客户单位详情失败', error);
        }
    }
};

// 保存表单
const saveForm = async () => {
    try {
        if (isEdit) {
            // 编辑模式
            // await api.updateCustomer(id, formData.value);
            console.log('更新客户单位信息', formData.value);
        } else {
            // 新增模式
            // await api.createCustomer(formData.value);
            console.log('创建客户单位信息', formData.value);
        }
        // 保存成功后返回列表页
        goBack();
    } catch (error) {
        console.error('保存客户单位信息失败', error);
    }
};

// 返回上一页
const goBack = () => {
    router.push('/master/inquireModule/customerUnitList');
};

// 页面加载时获取详情
onMounted(() => {
    fetchDetail();
});
</script>