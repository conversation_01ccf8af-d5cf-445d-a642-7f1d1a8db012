<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
 <template>
    <div class="ma-content-block p-4">
      <!-- 修改记录弹窗组件 -->
      <LogComponent ref="logComponentRef" />
      
      <!-- 关联联系人弹窗组件 -->
      <AssociatedContactPerson ref="contactPersonRef" />
      
      <!-- CRUD 组件 -->
      <MaCrud :options="crud" :columns="columns" ref="crudRef">
        <!-- 自定义列 - 最后更新时间 -->
        <template #updateTime="{ record }">
          <div v-time="record.updateTime"></div>
        </template>
        
        <!-- 操作列自定义 -->
        <template #operation="{ record }">
          <a-space>
            <a-button type="text" size="small" @click="associateContacts(record)">
              <icon-user-group />
              关联联系人
            </a-button>
            <a-button type="text" size="small" @click="showStatusChangeConfirm(record)">
                <icon-switch />
                修改状态
              </a-button>
            <a-button type="text" size="small" @click="viewDetail(record)">
              <icon-eye />
              详情
            </a-button>
            <a-button type="text" size="small" @click="editCustomer(record)">
              <icon-edit />
              修改
            </a-button>
            <a-button type="text" size="small" @click="viewModifyRecord(record)">
              <icon-history />
              修改记录
            </a-button>
          </a-space>
        </template>
      </MaCrud>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, nextTick } from "vue";
  import { Message, Modal } from "@arco-design/web-vue";
  // import { useRouter } from "vue-router";
  import LogComponent from "./components/log.vue";
import AssociatedContactPerson from "./components/AssociatedContactPerson.vue";
  
  definePageMeta({
    name: "master-inquireModule-customerUnitList",
    path: "/master/inquireModule/customerUnitList",
  })
  
  const crudRef = ref();
  const logComponentRef = ref();
const contactPersonRef = ref();
  // const router = useRouter();
  
  // 关联联系人
const associateContacts = (record) => {
  // 打开关联联系人弹窗
  if (contactPersonRef.value) {
    contactPersonRef.value.open(record.id);
  }
};
  
  // 显示状态变更确认弹窗
  const showStatusChangeConfirm = (record) => {
    const currentStatus = record.customerStatus;
    const newStatus = currentStatus === '营业' ? '关闭' : '营业';
    
    Modal.warning({
      title: '状态变更确认',
      content: `是否将客户「${record.customerFullName}」的状态从「${currentStatus}」修改为「${newStatus}」？`,
      okText: '确认修改',
      cancelText: '取消',
      onOk: () => changeStatus(record, newStatus)
    });
  };
  
  // 修改状态
  const changeStatus = (record, status) => {
    Message.success(`将客户 ${record.customerFullName} 状态修改为：${status}`);
    // 模拟更新状态
    const index = mockData.list.findIndex(item => item.id === record.id);
    if (index !== -1) {
      mockData.list[index].customerStatus = status;
      refreshTable();
    }
  };
  
  // 查看客户详情
  const viewDetail = (record) => {
    // 跳转到详情页面，并传递查看模式参数
    import("@/utils/common").then(module => {
      const router = useRouter();
      // 使用对象形式传递查询参数，更加清晰和易于扩展
      module.navigateWithTag(router, `/master/inquireModule/customerUnitList/${record.id}`, { 
        mode: 'view',
        name: record.customerFullName || '客户详情', // 传递客户名称，用于在标签页显示
        type: 'detail', // 标记是详情页面
        operation: 'view' // 标记是查看操作
      });
    }).catch(error => {
      console.error('导航模块加载失败:', error);
      // 导航模块加载失败时的备用方案
      const router = useRouter();
      router.push({
        path: `/master/inquireModule/customerUnitList/${record.id}`,
        query: { 
          mode: 'view',
          type: 'detail',
          operation: 'view'
        }
      });
    });
  };
  
  // 编辑客户信息
  const editCustomer = (record) => {
    // 跳转到编辑页面
    import("@/utils/common").then(module => {
      const router = useRouter();
      // 使用对象形式传递查询参数，更加清晰和易于扩展
      module.navigateWithTag(router, `/master/inquireModule/customerUnitList/${record.id}`, { 
        name: record.customerFullName || '编辑客户', // 传递客户名称，用于在标签页显示
        type: 'detail', // 标记是详情页面
        operation: 'edit' // 标记是编辑操作
      });
    }).catch(error => {
      console.error('导航模块加载失败:', error);
      // 导航模块加载失败时的备用方案
      const router = useRouter();
      router.push({
        path: `/master/inquireModule/customerUnitList/${record.id}`,
        query: {
          type: 'detail',
          operation: 'edit'
        }
      });
    });
  };
  
  // 查看修改记录
  const viewModifyRecord = (record) => {
  
    // 打开修改记录弹窗
    if (logComponentRef.value) {
      logComponentRef.value.open(record.id);
    }
  };
  
  // 模拟数据
  const mockData = reactive({
    list: [
      {
        id: 1,
        customerFullName: '上海聚灵云信息科技有限公司',
        customerCode: 'CUST20250001',
        customerShortName: '聚灵云科技',
        groupName: '聚灵集团',
        salesman: '张三',
        customerLevel: 'A级',
        enterpriseNature: '民营企业',
        customerStatus: '营业',
        businessScope: '计算机软件开发、信息系统集成服务、数据处理和存储服务、信息技术咨询服务',
        industryAttribute: '信息技术',
        updateTime: Date.now() - 86400000 * 2
      },
      {
        id: 2,
        customerFullName: '北京智慧云创科技有限公司',
        customerCode: 'CUST20250002',
        customerShortName: '智慧云创',
        groupName: '智慧科技集团',
        salesman: '李四',
        customerLevel: 'A级',
        enterpriseNature: '国有企业',
        customerStatus: '营业',
        businessScope: '人工智能技术研发、大数据处理、云计算服务、软件开发与销售',
        industryAttribute: '人工智能',
        updateTime: Date.now() - 86400000
      },
      {
        id: 3,
        customerFullName: '广州数字未来电子科技有限公司',
        customerCode: 'CUST20250003',
        customerShortName: '数字未来',
        groupName: '未来科技集团',
        salesman: '王五',
        customerLevel: 'B级',
        enterpriseNature: '合资企业',
        customerStatus: '营业',
        businessScope: '电子产品研发与销售、智能硬件制造、物联网技术服务、系统集成',
        industryAttribute: '电子科技',
        updateTime: Date.now()
      },
      {
        id: 4,
        customerFullName: '深圳市创新科技发展有限公司',
        customerCode: 'CUST20250004',
        customerShortName: '创新科技',
        groupName: '创新控股',
        salesman: '赵六',
        customerLevel: 'C级',
        enterpriseNature: '外资企业',
        customerStatus: '关闭',
        businessScope: '新能源技术开发、环保设备制造、节能技术咨询、可再生能源利用',
        industryAttribute: '新能源',
        updateTime: Date.now() - 86400000 * 3
      },
      {
        id: 5,
        customerFullName: '杭州智联物联网科技有限公司',
        customerCode: 'CUST20250005',
        customerShortName: '智联物联',
        groupName: '智联集团',
        salesman: '钱七',
        customerLevel: 'A级',
        enterpriseNature: '民营企业',
        customerStatus: '营业',
        businessScope: '物联网技术研发、智能家居产品设计与销售、智慧城市解决方案、传感器制造',
        industryAttribute: '物联网',
        updateTime: Date.now() - 86400000 * 4
      }
    ],
    total: 5
  });
  
  // 刷新表格数据
  const refreshTable = () => {
    if (crudRef.value) {
      crudRef.value.refresh();
    }
  };
  
  // 模拟获取客户单位列表数据的函数
  const getSalesmanBindingList = (params) => {
    console.log('查询参数:', params);
    
    // 过滤逻辑
    let filteredList = [...mockData.list];
    
    // 按客户全称筛选
    if (params.customerFullName) {
      filteredList = filteredList.filter(item => 
        item.customerFullName.includes(params.customerFullName)
      );
    }
    
    // 按客户编码筛选
    if (params.customerCode) {
      filteredList = filteredList.filter(item => 
        item.customerCode.includes(params.customerCode)
      );
    }
    
    // 按客户简称筛选
    if (params.customerShortName) {
      filteredList = filteredList.filter(item => 
        item.customerShortName.includes(params.customerShortName)
      );
    }
    
    // 按所属集团筛选
    if (params.groupName) {
      filteredList = filteredList.filter(item => 
        item.groupName.includes(params.groupName)
      );
    }
    
    // 按业务员筛选
    if (params.salesman) {
      filteredList = filteredList.filter(item => 
        item.salesman.includes(params.salesman)
      );
    }
    
    // 按客户等级筛选
    if (params.customerLevel) {
      filteredList = filteredList.filter(item => 
        item.customerLevel.includes(params.customerLevel)
      );
    }
    
    // 按企业性质筛选
    if (params.enterpriseNature) {
      filteredList = filteredList.filter(item => 
        item.enterpriseNature.includes(params.enterpriseNature)
      );
    }
    
    // 按客户状态筛选
    if (params.customerStatus) {
      filteredList = filteredList.filter(item => 
        item.customerStatus.includes(params.customerStatus)
      );
    }
    
    // 按行业属性筛选
    if (params.industryAttribute) {
      filteredList = filteredList.filter(item => 
        item.industryAttribute.includes(params.industryAttribute)
      );
    }
    
    // 按时间范围筛选
    if (params.startTime && params.endTime) {
      filteredList = filteredList.filter(item => 
        item.updateTime >= params.startTime && item.updateTime <= params.endTime
      );
    }
    
    // 分页处理
    const pageSize = params.pageSize || 10;
    const currentPage = params.page || 1;
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    const pagedList = filteredList.slice(start, end);
    
    // 返回Promise以模拟异步API调用
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            items: pagedList,
            pageInfo: {total: filteredList.length}
          },
          code: 200,
          message: '获取成功'
        });
      }, 300);
    });
  };
  
  // 新增客户单位
  const handleAdd = () => {
    import("@/utils/common").then(module => {
      const router = useRouter();
      module.navigateWithTag(router, "/master/inquireModule/customerUnitList/add");
    });
  };

  // CRUD 配置
  const crud = reactive({
    api: getSalesmanBindingList, // 使用本地mock数据函数
    pageLayout: 'fixed',
    showPagination: true,
    add: {
    show: true,
    text: "新增",
    action: handleAdd /* api: '/api/master/goods/save' */
  },
    edit: {
      show: false,
    },
    delete: {
      show: false,
    },
    beforeSearch(params) {
      // 处理时间范围参数
      if (params.updateTime && params.updateTime.length === 2) {
        params.startTime = params.updateTime[0];
        params.endTime = params.updateTime[1];
        delete params.updateTime;
      } else {
        delete params.startTime;
        delete params.endTime;
      }
      return params;
    },
  });
  
  // 表格列配置
  const columns = reactive([
    {
      title: '序号',
      dataIndex: 'id',
      width: 80,
    },
    {
      title: '客户全称',
      dataIndex: 'customerFullName',
      search: true,
      width: 180,
    },
    {
      title: '客户编码',
      dataIndex: 'customerCode',
      search: true,
      width: 120,
    },
    {
      title: '客户简称',
      dataIndex: 'customerShortName',
      search: true,
      width: 150,
    },
    {
      title: '所属集团',
      dataIndex: 'groupName',
      search: true,
      width: 150,
    },
    {
      title: '业务员',
      dataIndex: 'salesman',
      search: true,
      width: 120,
    },
    {
      title: '客户等级',
      dataIndex: 'customerLevel',
      search: true,
      width: 120,
    },
    {
      title: '企业性质',
      dataIndex: 'enterpriseNature',
      search: true,
      width: 120,
    },
    {
      title: '客户状态',
      dataIndex: 'customerStatus',
      search: true,
      width: 120,
    },
    {
      title: '经营范围',
      dataIndex: 'businessScope',
      search: false,
      ellipsis: true,
      width: 200,
    },
    {
      title: '行业属性',
      dataIndex: 'industryAttribute',
      search: true,
      width: 120,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 550,
      fixed: 'right'
    }
  ]);


  </script>
  
  <style scoped>
  .ma-content-block {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }
  </style>