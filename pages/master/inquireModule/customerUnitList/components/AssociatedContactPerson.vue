<template>
  <a-drawer
    :visible="visible"
    title="关联联系人"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="mb-4">
      <div>
        <a-form
          :model="form"
          :layout="layout"
          :label-col-props="{ span: 4 }"
          :wrapper-col-props="{ span: 20 }"
        >
          <div class="grid grid-cols-3 gap-4">
            <a-form-item field="receiver" label="收货人：">
              <a-input v-model="form.receiver" placeholder="请输入收货人" />
            </a-form-item>
            <a-form-item field="phone" label="电话：">
              <a-input v-model="form.phone" placeholder="请输入电话" />
            </a-form-item>
            <a-form-item field="contact" label="联系人：">
              <a-input v-model="form.contact" placeholder="请输入联系人" />
            </a-form-item>
            <a-form-item field="address" label="地址：">
              <a-input v-model="form.address" placeholder="请输入地址" />
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleSearch">查询</a-button>
                <a-button @click="handleReset">重置</a-button>
              </a-space>
            </a-form-item>
          </div>
        </a-form>
      </div>
    </div>

    <a-table
      :columns="columns"
      :data="tableData"
      :pagination="pagination"
      :bordered="true"
      stripe
      :scroll="{ x: '100%' }"
      :row-selection="rowSelection"
      row-key="id"
    >
      <template #address="{ record }">
        <span>{{ record.address }}</span>
      </template>
    </a-table>

    <template #footer>
      <div class="flex justify-end">
        <div>
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleOk">确定</a-button>
          </a-space>
        </div>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, computed } from "vue";

// 定义组件属性
const props = defineProps({
  // 传入的客户单位ID
  customerId: {
    type: [String, Number],
    default: "",
  },
});

const visible = ref(false);

// 定义事件
const emit = defineEmits(["update:visible", "ok", "cancel"]);

// 表单布局
const layout = ref("inline");

// 搜索表单
const form = reactive({
  receiver: "",
  phone: "",
  contact: "",
  address: "",
});

// 表格列定义
const columns = reactive([
  {
    title: "",
    slotName: "selection",
    width: 40,
  },
  {
    title: "收货人姓名",
    dataIndex: "receiver",
    width: 120,
  },
  {
    title: "收货人电话",
    dataIndex: "phone",
    width: 120,
  },
  {
    title: "联系人",
    dataIndex: "contact",
    width: 120,
  },
  {
    title: "收货地址",
    slotName: "address",
    width: 300,
  },
]);

// 表格数据
const tableData = reactive([
  {
    id: 1,
    receiver: "王**",
    phone: "131********",
    contact: "广东/********",
    address: "深圳 ************************",
  },
  {
    id: 2,
    receiver: "赵**",
    phone: "157********",
    contact: "广东/********",
    address: "湖北 ************",
  },
  {
    id: 3,
    receiver: "田**",
    phone: "177********",
    contact: "广东/********",
    address: "新疆 ************************",
  },
  {
    id: 4,
    receiver: "周**",
    phone: "176********",
    contact: "广东/********",
    address: "新疆 ************************",
  },
  {
    id: 5,
    receiver: "李**",
    phone: "123********",
    contact: "广东/********",
    address: "北京市********",
  },
  {
    id: 6,
    receiver: "陈**",
    phone: "155********",
    contact: "广东/********",
    address: "湖北 ************",
  },
  {
    id: 7,
    receiver: "张**",
    phone: "176********",
    contact: "广东/********",
    address: "海南 ************************",
  },
  {
    id: 8,
    receiver: "刘**",
    phone: "178********",
    contact: "广东/********",
    address: "福建 ************************",
  },
  {
    id: 9,
    receiver: "孙**",
    phone: "186********",
    contact: "八达****",
    address: "台州 ************************",
  },
  {
    id: 10,
    receiver: "0*********",
    phone: "156********",
    contact: "常熟***",
    address: "北京市********",
  },
]);

// 分页配置
const pagination = reactive({
  total: 689,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
});

// 行选择功能
const selectedRowKeys = ref([]);

// 行选择配置
const rowSelection = {
  type: "radio",
};

// 搜索处理
const handleSearch = () => {
  // 这里可以根据form中的条件进行筛选或API调用
  console.log("搜索条件:", form);

  // 模拟API调用
  // 实际项目中应该调用API获取数据
  pagination.current = 1;
  // loadData(pagination.current, pagination.pageSize, form);
};

// 重置搜索
const handleReset = () => {
  form.receiver = "";
  form.phone = "";
  form.contact = "";
  form.address = "";

  // 重置后自动查询
  handleSearch();
};

// 确认按钮处理
const handleOk = () => {
  // 获取选中的联系人数据
  const selectedContacts = tableData.filter((item) =>
    selectedRowKeys.value.includes(item.id)
  );

  // 触发确认事件，并传递选中的联系人数据
  emit("ok", selectedContacts);

  // 关闭弹窗
  visible.value = false;
  emit("update:visible", false);

  // 重置组件状态
  resetState();
};

// 取消按钮处理
const handleCancel = () => {
  visible.value = false;
  emit("update:visible", false);
  emit("cancel");
  resetState();
};

// 重置组件状态
const resetState = () => {
  selectedRowKeys.value = [];
  Object.keys(searchForm).forEach((key) => {
    searchForm[key] = "";
  });
};

// 暴露给父组件的打开弹窗方法
const open = (customerId) => {
  console.log("打开关联联系人弹窗，客户ID:", customerId || props.customerId);
  // 这里可以根据客户ID加载对应的联系人数据
  // 例如：loadContactPersons(customerId || props.customerId);
  visible.value = true;
};

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style scoped>
.search-form {
  padding: 12px;
  background-color: #f7f7f7;
  border-radius: 4px;
  margin-bottom: 16px;
}

.label {
  font-size: 14px;
  color: #333;
  margin-right: 8px;
}

.w-40 {
  width: 160px;
}
</style>
