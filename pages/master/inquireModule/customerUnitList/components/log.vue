<template>
  <a-drawer
    :visible="visible"
    title="审核记录"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="mb-4">
      <div class="log-header">
        <div class="log-info">
          <span class="info-item">修改人：{{ currentLog.modifier }}</span>
          <span class="info-item">修改时间：{{ currentLog.modifyTime }}</span>
        </div>
      </div>
    </div>

    <a-table
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      stripe
      :scroll="{ x: '100%' }"
      :row-key="(record) => record.id"
    >
    </a-table>

    <div class="mt-4 mb-4">
      <div class="log-header">
        <div class="log-info">
          <span class="info-item">修改人：{{ currentLog.modifier }}</span>
          <span class="info-item">修改时间：{{ currentLog.modifyTime }}</span>
        </div>
      </div>
    </div>

    <a-table
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      stripe
      :scroll="{ x: '100%' }"
      :row-key="(record) => record.id"
    >
    </a-table>

    <template #footer>
      <div class="flex justify-end">
        <div>
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleOk">确定</a-button>
          </a-space>
        </div>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, reactive } from "vue";

// 定义组件属性
const props = defineProps({
  // 传入的询价产品数据
  inquiryProducts: {
    type: Array,
    default: () => [],
  },
});

const visible = ref(false);

// 定义事件
const emit = defineEmits(["update:visible", "ok", "cancel"]);

// 表格列定义
const columns = reactive([
  {
    title: "序号",
    dataIndex: "id",
    width: 80,
  },
  {
    title: "修改内容",
    dataIndex: "content",
    width: 150,
  },
  {
    title: "修改前",
    dataIndex: "before",
    width: 150,
  },
  {
    title: "修改后",
    dataIndex: "after",
    width: 150,
  },
]);

// 表格数据
const tableData = reactive([
  {
    id: 1,
    content: "行业属性",
    before: "修改前行业属性",
    after: "修改后行业属性",
  },
  {
    id: 2,
    content: "客户等级",
    before: "修改前客户等级",
    after: "修改后客户等级",
  },
  {
    id: 3,
    content: "经营范围",
    before: "修改前经营范围",
    after: "修改后经营范围",
  },
]);

// 当前日志信息
const currentLog = reactive({
  modifier: "张三",
  modifyTime: "2024-10-10 09:24:55",
});

// 不再需要行选择功能
const selectedRowKeys = ref([]);
const selectAll = ref(false);

// 确认按钮处理
const handleOk = () => {
  // 触发确认事件
  emit("ok");

  // 关闭弹窗
  visible.value = false;
  emit("update:visible", false);

  // 重置组件状态
  resetState();
};

// 取消按钮处理
const handleCancel = () => {
  visible.value = false;
  emit("update:visible", false);
  emit("cancel");
  resetState();
};

// 重置组件状态
const resetState = () => {
  selectedRowKeys.value = [];
  selectAll.value = false;
};

// 暴露给父组件的打开弹窗方法
const open = (recordId) => {
  console.log("打开审核记录，记录ID:", recordId);
  // 这里可以根据记录ID加载对应的审核数据
  // 例如：loadAuditRecords(recordId);
  visible.value = true;
};

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style scoped>
.log-header {
  padding: 8px 0;
  border-bottom: 1px solid #e9e9e9;
  margin-bottom: 10px;
}

.log-info {
  display: flex;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.info-item {
  margin-right: 20px;
}
</style>
