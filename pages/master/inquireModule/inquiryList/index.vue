<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义列 - 询价时间 -->
      <template #inquiryTime="{ record }">
        <div v-time="record.inquiryTime"></div>
      </template>
      
      <template #operationBeforeExtend="{ record }" >
        <a-link @click="editItem(record)">询价详情</a-link>
        <a-link @click="editItem(record)">询价审核</a-link>
        <a-link @click="downloadQuote(record)">下载报价单</a-link>
      </template>
    </MaCrud>
    
    <!-- 引入报价单下载组件 -->
    <QuotationDownload ref="quotationDownloadRef" />
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconEdit, IconDelete, IconPlus, IconEye } from '@arco-design/web-vue/es/icon';
import { useRouter } from "vue-router";
import QuotationDownload from './components/QuotationDownload.vue';

definePageMeta({
  name: "master-inquireModule-inquiryList",
  path: "/master/inquireModule/inquiryList",
})

const router = useRouter();
const crudRef = ref();
const quotationDownloadRef = ref();

// 查看详情
const viewDetail = (record) => {
  // 跳转到详情页
  import("@/utils/common").then(module => {
    module.navigateWithTag(router, `/master/inquireModule/inquiryDetails/${record.id}`);
  });
};

// 编辑项目
const editItem = (record) => {
  // 编辑逻辑
  import("@/utils/common").then(module => {
    // 分离路径和查询参数，避免创建两个路由页面
    module.navigateWithTag(router, `/master/inquireModule/inquiryDetails/${record.id}`, { mode: 'edit' });
  });
};

// 下载报价单
const downloadQuote = (record) => {
  // 打开报价单下载弹窗
  quotationDownloadRef.value.open();
};

// 模拟数据
const mockData = reactive({
  list: [
    {
      id: 1,
      inquiryNo: 'XJ20250616001',
      inquiryTime: Date.now() - 86400000 * 2,
      salesman: '张三',
      customer: '上海某科技有限公司',
      customerCode: 'SH001',
      customerAddress: '上海市浦东新区张江高科技园区',
      inquiryItemCount: 5,
      pendingItemCount: 2,
      merchandiser: '李四',
      inquirer: '王五'
    },
    {
      id: 2,
      inquiryNo: 'XJ20250616002',
      inquiryTime: Date.now() - 86400000,
      salesman: '赵六',
      customer: '北京某电子有限公司',
      customerCode: 'BJ002',
      customerAddress: '北京市海淀区中关村科技园',
      inquiryItemCount: 8,
      pendingItemCount: 0,
      merchandiser: '钱七',
      inquirer: '孙八'
    },
    {
      id: 3,
      inquiryNo: 'XJ20250616003',
      inquiryTime: Date.now(),
      salesman: '周九',
      customer: '广州某贸易有限公司',
      customerCode: 'GZ003',
      customerAddress: '广州市天河区珠江新城',
      inquiryItemCount: 3,
      pendingItemCount: 3,
      merchandiser: '吴十',
      inquirer: '郑十一'
    },
    {
      id: 4,
      inquiryNo: 'XJ20250615001',
      inquiryTime: Date.now() - 86400000 * 3,
      salesman: '张三',
      customer: '深圳某科技有限公司',
      customerCode: 'SZ004',
      customerAddress: '深圳市南山区科技园',
      inquiryItemCount: 10,
      pendingItemCount: 5,
      merchandiser: '李四',
      inquirer: '王五'
    },
    {
      id: 5,
      inquiryNo: 'XJ20250614001',
      inquiryTime: Date.now() - 86400000 * 4,
      salesman: '赵六',
      customer: '杭州某信息技术有限公司',
      customerCode: 'HZ005',
      customerAddress: '杭州市西湖区文三路',
      inquiryItemCount: 6,
      pendingItemCount: 0,
      merchandiser: '钱七',
      inquirer: '孙八'
    }
  ],
  total: 5
});

// 模拟获取列表数据的函数
const getInquiryList = (params) => {
  console.log('查询参数:', params);
  
  // 过滤逻辑
  let filteredList = [...mockData.list];
  
  // 按询价单号筛选
  if (params.inquiryNo) {
    filteredList = filteredList.filter(item => 
      item.inquiryNo.toLowerCase().includes(params.inquiryNo.toLowerCase())
    );
  }
  
  // 按业务员筛选
  if (params.salesman) {
    filteredList = filteredList.filter(item => 
      item.salesman.includes(params.salesman)
    );
  }
  
  // 按客户筛选
  if (params.customer) {
    filteredList = filteredList.filter(item => 
      item.customer.includes(params.customer)
    );
  }
  
  // 按客户编码筛选
  if (params.customerCode) {
    filteredList = filteredList.filter(item => 
      item.customerCode.includes(params.customerCode)
    );
  }
  
  // 按跟单员筛选
  if (params.merchandiser) {
    filteredList = filteredList.filter(item => 
      item.merchandiser.includes(params.merchandiser)
    );
  }
  
  // 按询价员筛选
  if (params.inquirer) {
    filteredList = filteredList.filter(item => 
      item.inquirer.includes(params.inquirer)
    );
  }
  
  // 按时间范围筛选
  if (params.startTime && params.endTime) {
    filteredList = filteredList.filter(item => 
      item.inquiryTime >= params.startTime && item.inquiryTime <= params.endTime
    );
  }
  
  // 分页处理
  const pageSize = params.pageSize || 10;
  const currentPage = params.page || 1;
  const start = (currentPage - 1) * pageSize;
  const end = start + pageSize;
  const pagedList = filteredList.slice(start, end);
  
  // 返回Promise以模拟异步API调用
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          items: pagedList,
          pageInfo: {total: filteredList.length}
        },
        code: 200,
        message: '获取成功'
      });
    }, 300);
  });
};

// 新增询价单
const handleAdd = () => {
  import("@/utils/common").then(module => {
    module.navigateWithTag(router, "/master/inquireModule/inquiryDetails/add");
  });
};
const handleDelete = () => {
  console.log('删除询价单');
}

// CRUD 配置
const crud = reactive({
  api: getInquiryList, // 使用本地mock数据函数
  pageLayout: 'fixed',
  operationColumn: true,
  operationColumnWidth: 250,
  searchLabelWidth: '100px',
  add: {
    show: false,
    text: "新增",
    action: handleAdd
  },
  edit: {
    show: false,
    action: editItem
  },
  delete: {
    show: false,
    action: handleDelete
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有询价时间参数，转换为毫秒级时间戳
    if(params.inquiryTime && params.inquiryTime.length > 0){
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.inquiryTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startTime = startDate.getTime();
      
      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.inquiryTime[1]);
      endDate.setHours(23, 59, 59, 999);
      params.endTime = endDate.getTime();
      
      delete params.inquiryTime;
    } else {
      delete params.startTime;
      delete params.endTime;
    }
    return params;
  },
});

// 表格列配置
const columns = reactive([
  {
    title: '询价单号',
    dataIndex: 'inquiryNo',
    search: true,
    width: 150,
  },
  {
    title: '询价时间',
    dataIndex: 'inquiryTime',
    formType: "range",
    search: true,
    width: 180,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: '业务员',
    dataIndex: 'salesman',
    search: true,
    width: 120,
  },
  {
    title: '客户',
    dataIndex: 'customer',
    search: true,
    width: 150,
  },
  {
    title: '客户编码',
    dataIndex: 'customerCode',
    search: true,
    width: 120,
  },
  {
    title: '客户地址',
    dataIndex: 'customerAddress',
    search: false,
    ellipsis: true,
    width: 200,
  },
  {
    title: '询价项数',
    dataIndex: 'inquiryItemCount',
    search: false,
    width: 100,
  },
  {
    title: '待询价项数',
    dataIndex: 'pendingItemCount',
    search: false,
    width: 100,
  },
  {
    title: '跟单员',
    dataIndex: 'merchandiser',
    search: true,
    width: 120,
  },
  {
    title: '询价员',
    dataIndex: 'inquirer',
    search: true,
    width: 120,
  }
]);
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>
