<template>
  <a-drawer
    :visible="visible"
    title="更改询价员"
    width="1200px"
    @ok="handleOk"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="mb-4">
      <div class="mb-2 font-medium">询价员</div>
      <a-select
        v-model="selectedInquirer"
        placeholder="请选择询价员"
        style="width: 240px"
        allow-search
      >
        <a-option
          v-for="item in inquirerOptions"
          :key="item.value"
          :value="item.value"
        >
          {{ item.label }}
        </a-option>
      </a-select>
    </div>

    <a-table
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :bordered="true"
      stripe
      :scroll="{ x: '100%', y: '400px' }"
      :row-selection="rowSelection"
      :row-key="(record) => record.id"
    >
      <!-- 参考链接插槽 -->
      <template #referenceLink="{ record }">
        <a-link v-if="record.referenceLink" href="#" target="_blank"
          >查看链接</a-link
        >
        <span v-else>-</span>
      </template>
    </a-table>

    <template #footer>
      <div class="flex justify-between">
        <div>
          <a-checkbox v-model="selectAll" @change="handleSelectAllChange">
            全选
          </a-checkbox>
          <span class="ml-4">已选择 {{ selectedRowKeys.length }} 项</span>
        </div>
        <div>
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button type="primary" @click="handleOk">确定</a-button>
          </a-space>
        </div>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, reactive } from "vue";

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  // 传入的询价产品数据
  inquiryProducts: {
    type: Array,
    default: () => [],
  },
});

// 定义事件
const emit = defineEmits(["update:visible", "ok", "cancel"]);

// 询价员选项
const inquirerOptions = reactive([
  { label: "李明", value: "1" },
  { label: "王华", value: "2" },
  { label: "张伟", value: "3" },
  { label: "刘芳", value: "4" },
  { label: "陈晓", value: "5" },
  { label: "赵强", value: "6" },
]);

// 选中的询价员
const selectedInquirer = ref("");

// 表格列定义
const columns = reactive([
  {
    title: "产品名称",
    dataIndex: "productName",
    width: 150,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "品牌",
    dataIndex: "brand",
    width: 100,
  },
  {
    title: "型号",
    dataIndex: "model",
    width: 120,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "规格描述",
    dataIndex: "specification",
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "询价产品编码",
    dataIndex: "inquiryCode",
    width: 140,
  },
  {
    title: "参考链接",
    dataIndex: "referenceLink",
    width: 100,
    slotName: "referenceLink",
  },
  {
    title: "询价备注",
    dataIndex: "remark",
    width: 150,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "单位",
    dataIndex: "unit",
    width: 80,
  },
  {
    title: "需求数量",
    dataIndex: "quantity",
    width: 90,
    align: "right",
  },
  {
    title: "参考价格",
    dataIndex: "referencePrice",
    width: 100,
    align: "right",
  },
  {
    title: "询价员",
    dataIndex: "inquirer",
    width: 100,
  },
  {
    title: "客户产品编码",
    dataIndex: "customerProductCode",
    width: 140,
  },
]);

// 模拟表格数据
const tableData = reactive([
  {
    id: 1,
    productName: "电阻器",
    brand: "国巨",
    model: "RC0603FR-071KL",
    specification: "1KΩ ±1% 0603 厚膜电阻器",
    inquiryCode: "INQ20250616001",
    referenceLink: "https://example.com/product1",
    remark: "需要原装正品",
    unit: "个",
    quantity: 1000,
    referencePrice: 0.05,
    inquirer: "李明",
    customerProductCode: "CP001",
  },
  {
    id: 2,
    productName: "电容器",
    brand: "TDK",
    model: "C1608X7R1H104K080AA",
    specification: "0.1uF 50V X7R 0603",
    inquiryCode: "INQ20250616002",
    referenceLink: "",
    remark: "需要原厂货",
    unit: "个",
    quantity: 2000,
    referencePrice: 0.08,
    inquirer: "李明",
    customerProductCode: "CP002",
  },
  {
    id: 3,
    productName: "二极管",
    brand: "安森美",
    model: "1N4148W-7-F",
    specification: "小信号二极管 SOD-123",
    inquiryCode: "INQ20250616003",
    referenceLink: "https://example.com/product3",
    remark: "",
    unit: "个",
    quantity: 500,
    referencePrice: 0.12,
    inquirer: "王华",
    customerProductCode: "CP003",
  },
  {
    id: 4,
    productName: "三极管",
    brand: "安森美",
    model: "MMBT3904LT1G",
    specification: "NPN 40V 200mA SOT-23",
    inquiryCode: "INQ20250616004",
    referenceLink: "",
    remark: "需要原装",
    unit: "个",
    quantity: 300,
    referencePrice: 0.15,
    inquirer: "王华",
    customerProductCode: "CP004",
  },
  {
    id: 5,
    productName: "集成电路",
    brand: "TI",
    model: "LM358DR",
    specification: "双运算放大器 SOIC-8",
    inquiryCode: "INQ20250616005",
    referenceLink: "https://example.com/product5",
    remark: "需要17+批次",
    unit: "个",
    quantity: 100,
    referencePrice: 1.2,
    inquirer: "张伟",
    customerProductCode: "CP005",
  },
]);

// 选择行相关
const selectedRowKeys = ref([]);
const selectAll = ref(false);

// 行选择配置
const rowSelection = reactive({
  type: "checkbox",
  showCheckedAll: true,
  selectedRowKeys: selectedRowKeys,
  onChange: (selectedKeys) => {
    selectedRowKeys.value = selectedKeys;
    selectAll.value = selectedKeys.length === tableData.length;
  },
});

// 全选/取消全选处理
const handleSelectAllChange = (checked) => {
  if (checked) {
    selectedRowKeys.value = tableData.map((item) => item.id);
  } else {
    selectedRowKeys.value = [];
  }
};

// 确认按钮处理
const handleOk = () => {
  // 触发确认事件
  emit("ok");

  // 关闭弹窗
  visible.value = false;
  emit("update:visible", false);

  // 重置组件状态
  resetState();
};

// 取消按钮处理
const handleCancel = () => {
  visible.value = false;
  emit("update:visible", false);
  emit("cancel");
  resetState();
};

// 重置组件状态
const resetState = () => {
  selectedInquirer.value = "";
  selectedRowKeys.value = [];
  selectAll.value = false;
};

// 暴露给父组件的打开弹窗方法
const open = () => {
  emit("update:visible", true);
};

// 暴露方法给父组件
defineExpose({
  open,
});
</script>
