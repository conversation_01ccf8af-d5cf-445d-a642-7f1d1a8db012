<template>
  <a-drawer
    :visible="visible"
    @update:visible="visible = $event"
    :title="getModalTitle()"
    width="1200px"
    :mask-closable="false"
    :footer="false"
  >
    <!-- 使用a-form组件，实现一行三列布局 -->
    <a-form :model="inquiryForm" layout="vertical">
      <a-row :gutter="16">
        <!-- 实际产品名称 -->
        <a-col :span="8">
          <a-form-item
            field="actualProductName"
            label="实际产品名称"
            :rules="[{ required: true, message: '请输入实际产品名称' }]"
          >
            <a-input
              v-model="inquiryForm.actualProductName"
              placeholder="请输入实际产品名称"
              :disabled="isFieldDisabled('actualProductName')"
            />
          </a-form-item>
        </a-col>

        <!-- 实际品牌 -->
        <a-col :span="8">
          <a-form-item
            field="actualBrand"
            label="实际品牌"
            :rules="[{ required: true, message: '请输入实际品牌' }]"
          >
            <a-input
              v-model="inquiryForm.actualBrand"
              placeholder="请输入实际品牌"
              :disabled="isFieldDisabled('actualBrand')"
            />
          </a-form-item>
        </a-col>

        <!-- 实际型号 -->
        <a-col :span="8">
          <a-form-item
            field="actualModel"
            label="实际型号"
            :rules="[{ required: true, message: '请输入实际型号' }]"
          >
            <a-input
              v-model="inquiryForm.actualModel"
              placeholder="请输入实际型号"
              :disabled="isFieldDisabled('actualModel')"
            />
          </a-form-item>
        </a-col>

        <!-- 实际规格 -->
        <a-col :span="8">
          <a-form-item
            field="actualSpec"
            label="实际规格"
            :rules="[{ required: true, message: '请输入实际规格' }]"
          >
            <a-input
              v-model="inquiryForm.actualSpec"
              placeholder="请输入实际规格"
              :disabled="isFieldDisabled('actualSpec')"
            />
          </a-form-item>
        </a-col>

        <!-- 包装量 -->
        <a-col :span="8">
          <a-form-item field="packageQuantity" label="包装量">
            <a-input-number
              v-model="inquiryForm.packageQuantity"
              placeholder="请输入包装量"
              :min="0"
              style="width: 100%"
              :disabled="isFieldDisabled('packageQuantity')"
            />
          </a-form-item>
        </a-col>

        <!-- 询价产品编码 -->
        <a-col :span="8">
          <a-form-item field="inquiryProductCode" label="询价产品编码">
            <a-input
              v-model="inquiryForm.inquiryProductCode"
              placeholder="请输入询价产品编码"
              :disabled="isFieldDisabled('inquiryProductCode')"
            />
          </a-form-item>
        </a-col>

        <!-- 产品编码 -->
        <a-col :span="8">
          <a-form-item field="productCode" label="产品编码">
            <a-input
              v-model="inquiryForm.productCode"
              placeholder="请输入产品编码"
              :disabled="isFieldDisabled('productCode')"
            />
          </a-form-item>
        </a-col>

        <!-- 成本价 -->
        <a-col :span="8">
          <a-form-item field="costPrice" label="成本价">
            <a-input-number
              v-model="inquiryForm.costPrice"
              placeholder="请输入成本价"
              :min="0"
              :precision="2"
              prefix="¥"
              style="width: 100%"
              :disabled="isFieldDisabled('costPrice')"
            />
          </a-form-item>
        </a-col>

        <!-- 询价单位 -->
        <a-col :span="8">
          <a-form-item field="inquiryUnit" label="询价单位">
            <a-select
              v-model="inquiryForm.inquiryUnit"
              placeholder="请选择询价单位"
              style="width: 100%"
              :disabled="isFieldDisabled('inquiryUnit')"
            >
              <a-option
                v-for="unit in unitOptions"
                :key="unit.value"
                :value="unit.value"
                >{{ unit.label }}</a-option
              >
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 含税运情况 -->
        <a-col :span="8">
          <a-form-item field="taxAndShipping" label="含税运情况">
            <a-select
              v-model="inquiryForm.taxAndShipping"
              placeholder="请选择含税运情况"
              style="width: 100%"
              :disabled="isFieldDisabled('taxAndShipping')"
            >
              <a-option
                v-for="option in taxShippingOptions"
                :key="option.value"
                :value="option.value"
                >{{ option.label }}</a-option
              >
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 运费 -->
        <a-col :span="8">
          <a-form-item field="shippingCost" label="运费">
            <a-input-number
              v-model="inquiryForm.shippingCost"
              placeholder="请输入运费"
              :min="0"
              :precision="2"
              prefix="¥"
              style="width: 100%"
              :disabled="isFieldDisabled('shippingCost')"
            />
          </a-form-item>
        </a-col>

        <!-- 货期 -->
        <a-col :span="8">
          <a-form-item field="deliveryTime" label="货期">
            <a-date-picker
              v-model="inquiryForm.deliveryTime"
              placeholder="请选择货期"
              style="width: 100%"
              :disabled="isFieldDisabled('deliveryTime')"
            />
          </a-form-item>
        </a-col>

        <!-- 附加费 -->
        <a-col :span="8">
          <a-form-item field="additionalFee" label="附加费">
            <a-input-number
              v-model="inquiryForm.additionalFee"
              placeholder="请输入附加费"
              :min="0"
              :precision="2"
              prefix="¥"
              style="width: 100%"
              :disabled="isFieldDisabled('additionalFee')"
            />
          </a-form-item>
        </a-col>

        <!-- 供应商 -->
        <a-col :span="8">
          <a-form-item field="supplier" label="供应商">
            <a-select
              v-model="inquiryForm.supplier"
              placeholder="请选择供应商"
              style="width: 100%"
              :disabled="isFieldDisabled('supplier')"
            >
              <a-option
                v-for="supplier in suppliers"
                :key="supplier.value"
                :value="supplier.value"
                >{{ supplier.label }}</a-option
              >
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 上架资料链接 -->
        <a-col :span="8">
          <a-form-item field="listingDataLink" label="上架资料链接">
            <a-input
              v-model="inquiryForm.listingDataLink"
              placeholder="请输入上架资料链接"
              :disabled="isFieldDisabled('listingDataLink')"
            />
          </a-form-item>
        </a-col>

        <!-- 询价员 -->
        <a-col :span="8">
          <a-form-item field="inquiryClerk" label="询价员">
            <a-select
              v-model="inquiryForm.inquiryClerk"
              placeholder="请选择询价员"
              style="width: 100%"
              :disabled="isFieldDisabled('inquiryClerk')"
            >
              <a-option
                v-for="clerk in inquiryClerks"
                :key="clerk.value"
                :value="clerk.value"
                >{{ clerk.label }}</a-option
              >
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 询价状态 -->
        <a-col :span="8">
          <a-form-item field="inquiryStatus" label="询价状态">
            <a-select
              v-model="inquiryForm.inquiryStatus"
              placeholder="请选择询价状态"
              style="width: 100%"
              :disabled="isFieldDisabled('inquiryStatus')"
            >
              <a-option
                v-for="status in statusOptions"
                :key="status.value"
                :value="status.value"
                >{{ status.label }}</a-option
              >
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 技术员选择，只在转技术类型时显示 -->
        <a-col :span="8" v-if="currentOperationType === 'technical'">
          <a-form-item field="technical" label="技术员">
            <a-select
              v-model="inquiryForm.technical"
              placeholder="请选择技术员"
              style="width: 100%"
              :disabled="isFieldDisabled('technical')"
            >
              <a-option
                v-for="tech in technicians"
                :key="tech.value"
                :value="tech.value"
                >{{ tech.label }}</a-option
              >
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 实际规格描述 (占整行) -->
        <a-col :span="24">
          <a-form-item field="actualSpecDesc" label="实际规格描述">
            <a-textarea
              v-model="inquiryForm.actualSpecDesc"
              placeholder="请输入实际规格描述"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              :disabled="isFieldDisabled('actualSpecDesc')"
            />
          </a-form-item>
        </a-col>

        <!-- 实际询价备注 (占整行) -->
        <a-col :span="24">
          <a-form-item field="inquiryRemark" label="实际询价备注">
            <a-textarea
              v-model="inquiryForm.inquiryRemark"
              placeholder="请输入实际询价备注"
              :auto-size="{ minRows: 3, maxRows: 5 }"
              :disabled="isFieldDisabled('inquiryRemark')"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <div class="flex justify-end gap-2 mt-4">
      <a-button @click="close" :disabled="submitting">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="submitting"
        >提交</a-button
      >
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { Message } from "@arco-design/web-vue";

// 控制弹窗显示
const visible = ref(false);
const submitting = ref(false);

// 供应商、询价员和技术员选项
const suppliers = ref([]);
const inquiryClerks = ref([]);
const technicians = ref([]);

// 询价表单数据
const inquiryForm = reactive({
  actualProductName: "", // 实际产品名称
  actualBrand: "", // 实际品牌
  actualModel: "", // 实际型号
  actualSpec: "", // 实际规格
  actualSpecDesc: "", // 实际规格描述
  packageQuantity: "", // 包装量
  inquiryProductCode: "", // 询价产品编码
  productCode: "", // 产品编码
  costPrice: "", // 成本价
  inquiryUnit: "", // 询价单位
  taxAndShipping: "", // 含税运情况
  shippingCost: "", // 运费
  deliveryTime: "", // 货期
  additionalFee: "", // 附加费
  inquiryRemark: "", // 实际询价备注
  supplier: "", // 供应商
  listingDataLink: "", // 上架资料链接
  inquiryClerk: "", // 询价员
  inquiryStatus: "", // 询价状态
  technical: "", // 技术员
});

// 选项配置
const unitOptions = [
  { label: "个", value: "个" },
  { label: "箱", value: "箱" },
  { label: "件", value: "件" },
  { label: "套", value: "套" },
  { label: "台", value: "台" },
  { label: "千克", value: "千克" },
];

const taxShippingOptions = [
  { label: "含税含运", value: "含税含运" },
  { label: "含税不含运", value: "含税不含运" },
  { label: "不含税含运", value: "不含税含运" },
  { label: "不含税不含运", value: "不含税不含运" },
];

const statusOptions = [
  { label: "待询价", value: "待询价" },
  { label: "询价中", value: "询价中" },
  { label: "已询价", value: "已询价" },
  { label: "已取消", value: "已取消" },
];

// 当前操作类型
const currentOperationType = ref("edit");

// 获取弹窗标题
const getModalTitle = () => {
  switch (currentOperationType.value) {
    case "supplement":
      return "补充资料";
    case "technical":
      return "转技术";
    default:
      return "询价表单";
  }
};

// 打开弹窗
const open = (data = {}, type = "edit") => {
  // 设置当前操作类型
  currentOperationType.value = type;
  Message.info(`当前操作类型: ${currentOperationType.value}`);
  console.log(
    "🚀 ~ file: InquiryFormModal.vue:243 ~ currentOperationType:",
    currentOperationType
  );

  // 如果传入数据，则填充表单
  if (Object.keys(data).length > 0) {
    Object.keys(inquiryForm).forEach((key) => {
      if (data[key] !== undefined) {
        inquiryForm[key] = data[key];
      }
    });
  }

  // 根据不同操作类型设置弹窗标题和表单状态
  switch (type) {
    case "supplement":
      // 补充资料模式
      break;
    case "technical":
      // 转技术模式
      break;
    default:
      // 默认编辑模式
      break;
  }

  visible.value = true;
};

// 关闭弹窗
const close = () => {
  visible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.keys(inquiryForm).forEach((key) => {
    inquiryForm[key] = "";
  });
};

// 获取供应商列表
const fetchSuppliers = async () => {
  try {
    // 模拟API调用，实际项目中应替换为真实API
    const response = await fetch("/api/suppliers");
    const data = await response.json();

    // 更新供应商选项
    suppliers.value = data.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  } catch (error) {
    console.error("获取供应商列表失败:", error);
    // 设置一些默认数据用于演示
    suppliers.value = [
      { label: "供应商A", value: "supplier_a" },
      { label: "供应商B", value: "supplier_b" },
      { label: "供应商C", value: "supplier_c" },
    ];
  }
};

// 获取询价员列表
const fetchInquiryClerks = async () => {
  try {
    // 模拟API调用，实际项目中应替换为真实API
    const response = await fetch("/api/inquiry-clerks");
    const data = await response.json();

    // 更新询价员选项
    inquiryClerks.value = data.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  } catch (error) {
    console.error("获取询价员列表失败:", error);
    // 设置一些默认数据用于演示
    inquiryClerks.value = [
      { label: "询价员A", value: "clerk_a" },
      { label: "询价员B", value: "clerk_b" },
      { label: "询价员C", value: "clerk_c" },
    ];
  }
};

// 提交表单
const handleSubmit = async () => {
  try {
    submitting.value = true;

    // 这里可以添加表单验证逻辑
    const requiredFields = formColumns
      .filter((column) => column.rules?.some((rule) => rule.required))
      .map((column) => column.dataIndex);

    const missingFields = requiredFields.filter((field) => !inquiryForm[field]);

    if (missingFields.length > 0) {
      const missingFieldTitles = missingFields
        .map(
          (field) =>
            formColumns.find((column) => column.dataIndex === field)?.title
        )
        .join("、");

      Message.warning(`请填写必填项: ${missingFieldTitles}`);
      submitting.value = false;
      return;
    }

    // 这里可以添加提交表单的API调用
    // const response = await inquiryApi.submitForm(inquiryForm)

    // 模拟API调用成功
    setTimeout(() => {
      submitting.value = false;

      // 提交成功
      Message.success("提交成功");

      // 触发提交事件，将表单数据和操作类型传递给父组件
      emit("submit", {
        ...inquiryForm,
        operationType: currentOperationType.value,
      });

      // 关闭弹窗
      close();
    }, 1000);
  } catch (error) {
    console.error("提交询价表单失败:", error);
    Message.error("提交失败，请稍后重试");
    submitting.value = false;
  }
};

// 定义组件事件
const emit = defineEmits(["submit", "cancel"]);

// 获取技术员列表
const fetchTechnicians = async () => {
  try {
    // 模拟API调用，实际项目中应替换为真实API
    const response = await fetch("/api/technicians");
    const data = await response.json();

    // 更新技术员选项
    technicians.value = data.map((item) => ({
      label: item.name,
      value: item.id,
    }));
  } catch (error) {
    console.error("获取技术员列表失败:", error);
    // 设置一些默认数据用于演示
    technicians.value = [
      { label: "技术员A", value: "tech_a" },
      { label: "技术员B", value: "tech_b" },
      { label: "技术员C", value: "tech_c" },
    ];
  }
};

// 组件挂载时获取下拉选项数据
onMounted(() => {
  fetchSuppliers();
  fetchInquiryClerks();
  fetchTechnicians();
});

// 判断字段是否禁用的方法
const isFieldDisabled = (fieldName) => {
  // 编辑模式：禁用询价员和询价状态表单项
  if (currentOperationType.value === "edit") {
    return fieldName === "inquiryClerk" || fieldName === "inquiryStatus";
  }

  // 补充资料模式：除了上架资料链接表单项，其他都禁用
  if (currentOperationType.value === "supplement") {
    return fieldName !== "listingDataLink";
  }

  // 转技术模式：除了技术员表单项，其他都禁用
  if (currentOperationType.value === "technical") {
    return fieldName !== "technical";
  }

  return false;
};

// 暴露方法给父组件
defineExpose({
  open,
  close,
});
</script>

<style scoped>
/* 可以根据需要添加样式 */
</style>
