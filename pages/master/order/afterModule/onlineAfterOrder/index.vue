<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" @row-click="viewDetail">
      <!-- 处理状态列 -->
      <template #process_status="{ record }">
        <a-tag :color="getStatusColor(record.process_status)">
          {{ processStatusMap[record.process_status] || "未知" }}
        </a-tag>
      </template>
      <!-- 入库状态列 -->
      <template #is_storage="{ record }">
        <a-tag :color="record.is_storage ? 'green' : 'orange'">
          {{ record.is_storage ? "已入库" : "未入库" }}
        </a-tag>
      </template>
      <!-- 自定义列 - 申请时间 -->
      <template #apply_time="{ record }">
        <div v-time="record.apply_time"></div>
      </template>
      <!-- 自定义列 - 完成时间 -->
      <template #complete_time="{ record }">
        <div v-time="record.complete_time"></div>
      </template>
      <!-- 操作列已被移除，仅保留查看详情功能 -->
    </ma-crud>

    <!-- 售后单详情抽屉组件 -->
    <AfterOrderDetails
      v-model:visible="drawerVisible"
      :order-detail="currentRecord"
      @close="drawerVisible = false"
      @agree-exchange="agreeExchange"
      @agree-return="agreeReturn"
      @agree-refund="agreeRefund"
      @agree-resend="agreeResend"
      @reject-application="rejectApplication"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
// 需要创建售后单详情组件
import AfterOrderDetails from "./components/AfterOrderDetails.vue";

definePageMeta({
  name: "onlineAfterOrder",
  path: "/master/order/afterModule/onlineAfterOrder",
});

const crudRef = ref();
const drawerVisible = ref(false);
const currentRecord = ref(null);

// 处理状态映射
const processStatusMap = {
  waiting_follow: "待跟进",
  waiting_review: "待审核",
  waiting_return: "待退货",
  waiting_receipt: "待收货",
  rejected: "已拒绝",
  closed: "已关闭",
  completed: "已完成",
  waiting_send_back: "待寄回",
  waiting_process: "待处理",
  waiting_resend: "待补发",
};

// 售后类型映射
const afterTypeMap = {
  exchange: "换货",
  return: "退货",
  refund: "仅退款",
  resend: "补发",
};

// 售后阶段映射
const afterStageMap = {
  application: "申请阶段",
  processing: "处理中",
  complete: "已完成",
  canceled: "已取消",
};

// 小二介入状态映射
const staffInterventionMap = {
  not_required: "不需要小二介入",
  required: "需要小二介入",
  intervened: "小二已介入",
  initial_review_completed: "小二初审完成",
  manager_review_failed: "小二主管复审失败",
  completed: "小二处理完成",
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    waiting_follow: "orange",
    waiting_review: "blue",
    waiting_return: "purple",
    waiting_receipt: "gold",
    rejected: "red",
    closed: "gray",
    completed: "green",
    waiting_send_back: "cyan",
    waiting_process: "magenta",
    waiting_resend: "lime",
  };
  return colorMap[status] || "default";
};

// 查看详情
const viewDetail = (record) => {
  currentRecord.value = record;
  drawerVisible.value = true;
};

// 判断是否可以同意换货
const canAgreeExchange = (record) => {
  return (
    record.process_status === "waiting_review" &&
    (record.after_type === "exchange" || record.after_type === "")
  );
};

// 判断是否可以同意退货
const canAgreeReturn = (record) => {
  return (
    record.process_status === "waiting_review" &&
    (record.after_type === "return" || record.after_type === "")
  );
};

// 判断是否可以同意退款
const canAgreeRefund = (record) => {
  return (
    record.process_status === "waiting_review" &&
    (record.after_type === "refund" || record.after_type === "")
  );
};

// 判断是否可以同意补发
const canAgreeResend = (record) => {
  return (
    record.process_status === "waiting_review" &&
    (record.after_type === "resend" || record.after_type === "")
  );
};

// 判断是否可以拒绝申请
const canReject = (record) => {
  return record.process_status === "waiting_review";
};

// 同意换货
const agreeExchange = (record) => {
  // 此处添加同意换货的逻辑
  Message.success("已同意换货申请");
  // 刷新列表数据
  crudRef.value?.refresh();
};

// 同意退货
const agreeReturn = (record) => {
  // 此处添加同意退货的逻辑
  Message.success("已同意退货申请");
  // 刷新列表数据
  crudRef.value?.refresh();
};

// 同意退款
const agreeRefund = (record) => {
  // 此处添加同意退款的逻辑
  Message.success("已同意退款申请");
  // 刷新列表数据
  crudRef.value?.refresh();
};

// 同意补发
const agreeResend = (record) => {
  // 此处添加同意补发的逻辑
  Message.success("已同意补发申请");
  // 刷新列表数据
  crudRef.value?.refresh();
};

// 拒绝申请
const rejectApplication = (record) => {
  // 此处添加拒绝申请的逻辑
  Message.success("已拒绝售后申请");
  // 刷新列表数据
  crudRef.value?.refresh();
};

// 模拟数据
const mockData = reactive({
  list: [
    {
      id: 1001,
      process_status: "waiting_review",
      remaining_time: "23小时",
      is_storage: false,
      after_order_no: "AO2025051501",
      after_type: "exchange",
      after_stage: "application",
      platform_refund: 199.99,
      refund_amount: 199.99,
      service_fee: 10.0,
      shipping_fee: 15.0,
      issue_reason: "商品质量问题",
      issue_details: "商品收到后发现有明显划痕",
      apply_time: "2025-05-14 09:30:25",
      complete_time: null,
      buyer_info: "张三 (13812345678)",
      return_express_company: "顺丰速运",
      return_express_no: "SF1234567890",
      shop_name: "官方旗舰店",
      system_order_status: "已发货",
      original_order_remarks: "请尽快发货",
      original_express: "京东物流",
      original_express_no: "JD9876543210",
      system_order_no: "SO2025051101",
      upstream_order_no: "UO9876543210",
      document_no: "DOC2025051401",
      system_shipping_status: "已发货",
      staff_intervention: "not_required",
    },
    {
      id: 1002,
      process_status: "waiting_return",
      remaining_time: "48小时",
      is_storage: false,
      after_order_no: "AO2025051502",
      after_type: "return",
      after_stage: "processing",
      platform_refund: 299.5,
      refund_amount: 299.5,
      service_fee: 0.0,
      shipping_fee: 0.0,
      issue_reason: "七天无理由退货",
      issue_details: "不喜欢，想退货",
      apply_time: "2025-05-13 14:20:10",
      complete_time: null,
      buyer_info: "李四 (13987654321)",
      return_express_company: "",
      return_express_no: "",
      shop_name: "数码专营店",
      system_order_status: "已完成",
      original_order_remarks: "",
      original_express: "圆通速递",
      original_express_no: "YT1234567890",
      system_order_no: "SO2025051005",
      upstream_order_no: "UO1234567890",
      document_no: "DOC2025051302",
      system_shipping_status: "已签收",
      staff_intervention: "required",
    },
    {
      id: 1003,
      process_status: "completed",
      remaining_time: "0小时",
      is_storage: true,
      after_order_no: "AO2025051203",
      after_type: "refund",
      after_stage: "complete",
      platform_refund: 99.0,
      refund_amount: 99.0,
      service_fee: 5.0,
      shipping_fee: 0.0,
      issue_reason: "商品缺货",
      issue_details: "下单后客服告知商品缺货",
      apply_time: "2025-05-12 10:15:30",
      complete_time: "2025-05-13 09:20:15",
      buyer_info: "王五 (13611112222)",
      return_express_company: "",
      return_express_no: "",
      shop_name: "生活日用旗舰店",
      system_order_status: "已关闭",
      original_order_remarks: "",
      original_express: "",
      original_express_no: "",
      system_order_no: "SO2025051202",
      upstream_order_no: "UO7654321098",
      document_no: "DOC2025051210",
      system_shipping_status: "未发货",
      staff_intervention: "completed",
    },
    {
      id: 1004,
      process_status: "waiting_receipt",
      remaining_time: "72小时",
      is_storage: false,
      after_order_no: "AO2025051104",
      after_type: "return",
      after_stage: "processing",
      platform_refund: 599.0,
      refund_amount: 570.0,
      service_fee: 29.0,
      shipping_fee: 0.0,
      issue_reason: "商品不符合描述",
      issue_details: "颜色与网页展示不符",
      apply_time: "2025-05-10 16:40:20",
      complete_time: null,
      buyer_info: "赵六 (13533334444)",
      return_express_company: "中通快递",
      return_express_no: "ZT9876543210",
      shop_name: "服装专卖店",
      system_order_status: "已发货",
      original_order_remarks: "送礼物，请包装精美",
      original_express: "申通快递",
      original_express_no: "ST1234567890",
      system_order_no: "SO2025050901",
      upstream_order_no: "UO5432109876",
      document_no: "DOC2025051008",
      system_shipping_status: "已签收",
      staff_intervention: "intervened",
    },
    {
      id: 1005,
      process_status: "rejected",
      remaining_time: "0小时",
      is_storage: false,
      after_order_no: "AO2025051005",
      after_type: "resend",
      after_stage: "canceled",
      platform_refund: 0.0,
      refund_amount: 0.0,
      service_fee: 0.0,
      shipping_fee: 20.0,
      issue_reason: "快递丢失",
      issue_details: "快递显示已签收但未收到货",
      apply_time: "2025-05-08 11:25:35",
      complete_time: "2025-05-09 14:30:40",
      buyer_info: "钱七 (13755556666)",
      return_express_company: "",
      return_express_no: "",
      shop_name: "母婴专营店",
      system_order_status: "已完成",
      original_order_remarks: "",
      original_express: "韵达快递",
      original_express_no: "YD6543210987",
      system_order_no: "SO2025050505",
      upstream_order_no: "UO1357924680",
      document_no: "DOC2025050801",
      system_shipping_status: "已签收",
      staff_intervention: "initial_review_completed",
    },
  ],
  total: {
    total: 5,
    currentPage: 1,
    totalPage: null,
  },
});

// CRUD配置
const crud = reactive({
  // 接口API配置
  api: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: "200",
          message: "操作成功",
          data: {
            items: mockData.list,
            pageInfo: mockData.total,
          },
        });
      }, 300); // 模拟网络请求延迟
    });
  },
  searchLabelWidth: '120px',
  enableRowHighlight: true
});

// 表格列配置
const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    width: 80,
  },
  {
    title: "处理状态",
    dataIndex: "process_status",
    width: 100,
    // search: true,
  },
  {
    title: "剩余处理时间",
    dataIndex: "remaining_time",
    width: 120,
  },
  {
    title: "入库",
    dataIndex: "is_storage",
    width: 80,
    search: true,
  },
  {
    title: "售后单号",
    dataIndex: "after_order_no",
    width: 150,
    search: true,
  },
  {
    title: "售后类型",
    dataIndex: "after_type",
    width: 100,
    search: true,
    customRender: ({ record }) => {
      return afterTypeMap[record.after_type] || "未知";
    },
  },
  {
    title: "售后阶段",
    dataIndex: "after_stage",
    width: 100,
    search: true,
    customRender: ({ record }) => {
      return afterStageMap[record.after_stage] || "未知";
    },
  },
  {
    title: "平台退款",
    dataIndex: "platform_refund",
    width: 100,
    customRender: ({ record }) => {
      return record.platform_refund
        ? `¥${record.platform_refund.toFixed(2)}`
        : "0.00";
    },
  },
  {
    title: "应退",
    dataIndex: "refund_amount",
    width: 100,
    customRender: ({ record }) => {
      return record.refund_amount
        ? `¥${record.refund_amount.toFixed(2)}`
        : "0.00";
    },
  },
  {
    title: "服务费",
    dataIndex: "service_fee",
    width: 100,
    customRender: ({ record }) => {
      return record.service_fee ? `¥${record.service_fee.toFixed(2)}` : "0.00";
    },
  },
  {
    title: "邮费",
    dataIndex: "shipping_fee",
    width: 100,
    customRender: ({ record }) => {
      return record.shipping_fee
        ? `¥${record.shipping_fee.toFixed(2)}`
        : "0.00";
    },
  },
  {
    title: "问题原因",
    dataIndex: "issue_reason",
    width: 150,
    search: true,
  },
  {
    title: "问题详情",
    dataIndex: "issue_details",
    width: 200,
    ellipsis: true,
  },
  {
    title: "申请时间",
    dataIndex: "apply_time",
    width: 160,
    search: true,
    formType: "range",
  },
  {
    title: "完成时间",
    dataIndex: "complete_time",
    width: 160,
  },
  {
    title: "买家信息",
    dataIndex: "buyer_info",
    width: 180,
    search: true,
  },
  {
    title: "退回快递公司",
    dataIndex: "return_express_company",
    width: 120,
    search: true,
  },
  {
    title: "退回快递单号",
    dataIndex: "return_express_no",
    width: 150,
  },
  {
    title: "店铺",
    dataIndex: "shop_name",
    width: 150,
    search: true,
  },
  {
    title: "系统订单状态",
    dataIndex: "system_order_status",
    width: 120,
  },
  {
    title: "原订单备注",
    dataIndex: "original_order_remarks",
    width: 200,
    ellipsis: true,
  },
  {
    title: "原快递",
    dataIndex: "original_express",
    width: 120,
  },
  {
    title: "原快递单号",
    dataIndex: "original_express_no",
    width: 150,
    search: true,
  },
  {
    title: "系统订单号",
    dataIndex: "system_order_no",
    width: 150,
  },
  {
    title: "上游单号",
    dataIndex: "upstream_order_no",
    width: 150,
    search: true,
  },
  {
    title: "单据编号",
    dataIndex: "document_no",
    width: 150,
    search: true,
  },
  {
    title: "系统发货状态",
    dataIndex: "system_shipping_status",
    width: 120,
    search: true,
  },
  {
    title: "小二介入",
    dataIndex: "staff_intervention",
    width: 120,
    search: true,
    hide: true,
    formType: 'select',
    dict: {
      data: [
        { label: '不需要小二介入', value: 'not_required' },
        { label: '需要小二介入', value: 'required' },
        { label: '小二已介入', value: 'intervened' },
        { label: '小二初审完成', value: 'initial_review_completed' },
        { label: '小二主管复审失败', value: 'manager_review_failed' },
        { label: '小二处理完成', value: 'completed' }
      ]
    },
  },
  {
    title: "标记",
    dataIndex: "mark",
    width: 120,
    search: true,
    hide: true,
  },
  {
    title: "商品",
    dataIndex: "goods",
    width: 120,
    search: true,
    hide: true,
  },
  {
    title: "超时情况",
    dataIndex: "goods",
    width: 120,
    search: true,
    hide: true,
  },
  {
    title: "退货天数",
    dataIndex: "goods",
    width: 120,
    search: true,
    hide: true,
  },
]);
</script>
