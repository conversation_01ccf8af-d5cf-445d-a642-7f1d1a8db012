<template>
  <a-drawer
    :visible="visible"
    :width="800"
    @cancel="close"
    unmount-on-close
    :footer="false"
    :mask-closable="true"
    title="售后订单详情"
  >
    <a-spin :loading="loading" style="width: 100%;">
      <a-tabs>
        <a-tab-pane key="returnGoods" title="退回商品明细">
          <a-table :data="returnGoodsList" :pagination="false" :bordered="true">
            <template #columns>
              <a-table-column title="退款状态" data-index="refundStatus">
                <template #cell="{ record }">
                  <a-tag :color="getStatusColor(record.refundStatus)">
                    {{ record.refundStatus }}
                  </a-tag>
                </template>
              </a-table-column>
              <a-table-column title="图示" data-index="image">
                <template #cell="{ record }">
                  <a-image
                    v-if="record.image"
                    :src="record.image"
                    :preview-visible="false"
                    width="60"
                    height="60"
                    @click="previewImage(record.image)"
                  />
                  <div v-else class="w-[60px] h-[60px] bg-gray-100 flex items-center justify-center text-gray-400">
                    无图片
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="订单号" data-index="orderNo" />
              <a-table-column title="系统商品信息" data-index="systemProductInfo" />
              <a-table-column title="线上宝贝信息" data-index="onlineProductInfo" />
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="exchangeGoods" title="换出商品明细">
          <a-table :data="exchangeGoodsList" :pagination="false" :bordered="true">
            <template #columns>
              <a-table-column title="图示" data-index="image">
                <template #cell="{ record }">
                  <a-image
                    v-if="record.image"
                    :src="record.image"
                    :preview-visible="false"
                    width="60"
                    height="60"
                    @click="previewImage(record.image)"
                  />
                  <div v-else class="w-[60px] h-[60px] bg-gray-100 flex items-center justify-center text-gray-400">
                    无图片
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="系统商品信息" data-index="systemProductInfo" />
              <a-table-column title="线上宝贝信息" data-index="onlineProductInfo" />
              <a-table-column title="单价" data-index="price">
                <template #cell="{ record }">
                  ¥{{ record.price.toFixed(2) }}
                </template>
              </a-table-column>
              <a-table-column title="数量" data-index="quantity" />
              <a-table-column title="金额" data-index="amount">
                <template #cell="{ record }">
                  ¥{{ record.amount.toFixed(2) }}
                </template>
              </a-table-column>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="operationRecord" title="操作记录">
          <a-table :data="operationRecordList" :pagination="false" :bordered="true">
            <template #columns>
              <a-table-column title="处理状态" data-index="status">
                <template #cell="{ record }">
                  <a-tag :color="getOperationStatusColor(record.status)">
                    {{ record.status }}
                  </a-tag>
                </template>
              </a-table-column>
              <a-table-column title="处理人" data-index="operator" />
              <a-table-column title="处理时间" data-index="operateTime" />
              <a-table-column title="处理详情描述" data-index="description" />
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="vouchers" title="凭证图片">
          <div class="grid grid-cols-4 gap-4 p-4">
            <div v-if="voucherImages.length === 0" class="col-span-4 text-center text-gray-400 py-8">
              暂无凭证图片
            </div>
            <div 
              v-for="(image, index) in voucherImages" 
              :key="index"
              class="border rounded-md overflow-hidden cursor-pointer"
              @click="previewImage(image)"
            >
              <a-image
                :src="image"
                width="100%"
                height="120"
                :preview-visible="false"
                fit="cover"
              />
            </div>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="operations" title="操作">
          <div class="flex flex-wrap gap-3">
            <a-button
              type="primary"
              status="success"
              @click="onAgreeExchange"
             
            >
              同意换货
            </a-button>
            <a-button
              type="primary"
              status="success"
              @click="onAgreeReturn"
            >
              同意退货
            </a-button>
            <a-button
              type="primary"
              status="success"
              @click="onAgreeRefund"
            >
              同意退款
            </a-button>
            <a-button
              type="primary"
              status="success"
              @click="onAgreeResend"
            >
              同意补发
            </a-button>
            <a-button
              type="primary"
              status="danger"
              @click="onRejectApplication"
            >
              拒绝申请
            </a-button>
            <div v-if="false" class="w-full text-center text-gray-400 py-8">
              当前状态下无可用操作
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </a-drawer>
  
  <a-modal
    v-model:visible="imagePreviewVisible"
    :footer="false"
    :mask-closable="true"
    @cancel="imagePreviewVisible = false"
  >
    <div class="text-center">
      <a-image
        v-if="previewImageUrl"
        :src="previewImageUrl"
        :preview-visible="false"
        style="max-width: 100%; max-height: 80vh;"
      />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

// 抽屉可见性
const visible = defineModel('visible', { default: false })

// 定义组件属性
const props = defineProps({
  afterOrderId: {
    type: [Number, String],
    default: null
  },
  orderDetail: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emits = defineEmits([
  'close', 
  'update:visible', 
  'agree-exchange', 
  'agree-return', 
  'agree-refund', 
  'agree-resend', 
  'reject-application'
])

// 数据模型
const returnGoodsList = ref([])
const exchangeGoodsList = ref([])
const operationRecordList = ref([])
const voucherImages = ref([])
const afterOrderData = ref(null)

// 图片预览
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 获取售后订单详情数据
const fetchAfterOrderDetails = async () => {
  try {
    // 如果从props中获取到了orderDetail，则直接使用，否则调用API获取
    if (props.orderDetail && Object.keys(props.orderDetail).length > 0) {
      afterOrderData.value = props.orderDetail
    }
    // 这里应该调用实际的API获取数据
    // 假设API响应结构如下：
    const mockData = {
      returnGoods: [
        {
          refundStatus: '已退款',
          image: '/assets/placeholder.png',
          orderNo: 'OR20240515001',
          systemProductInfo: '商品A - 规格1',
          onlineProductInfo: '线上商品A - 颜色：红色 尺寸：XL'
        },
        {
          refundStatus: '待退款',
          image: '/assets/placeholder.png',
          orderNo: 'OR20240515002',
          systemProductInfo: '商品B - 规格2',
          onlineProductInfo: '线上商品B - 颜色：蓝色 尺寸：L'
        }
      ],
      exchangeGoods: [
        {
          image: '/assets/placeholder.png',
          systemProductInfo: '商品C - 规格3',
          onlineProductInfo: '线上商品C - 颜色：黑色 尺寸：M',
          price: 199.99,
          quantity: 2,
          amount: 399.98
        },
        {
          image: '/assets/placeholder.png',
          systemProductInfo: '商品D - 规格4',
          onlineProductInfo: '线上商品D - 颜色：白色 尺寸：S',
          price: 99.99,
          quantity: 1,
          amount: 99.99
        }
      ],
      operationRecords: [
        {
          status: '已处理',
          operator: '张三',
          operateTime: '2024-05-15 10:30:45',
          description: '确认退款申请并处理'
        },
        {
          status: '待处理',
          operator: '李四',
          operateTime: '2024-05-14 15:20:30',
          description: '收到退款申请'
        }
      ],
      voucherImages: [
        '/assets/placeholder.png',
        '/assets/placeholder.png',
        '/assets/placeholder.png'
      ]
    }
    
    // 更新组件数据
    returnGoodsList.value = mockData.returnGoods
    exchangeGoodsList.value = mockData.exchangeGoods
    operationRecordList.value = mockData.operationRecords
    voucherImages.value = mockData.voucherImages
  } catch (error) {
    console.error('获取售后订单详情失败：', error)
  }
}

// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    '已退款': 'green',
    '待退款': 'orange',
    '已拒绝': 'red',
    '处理中': 'blue'
  }
  return statusMap[status] || 'gray'
}

// 获取操作状态颜色
const getOperationStatusColor = (status) => {
  const statusMap = {
    '已处理': 'green',
    '待处理': 'orange',
    '已拒绝': 'red',
    '处理中': 'blue'
  }
  return statusMap[status] || 'gray'
}

// 预览图片
const previewImage = (url) => {
  previewImageUrl.value = url
  imagePreviewVisible.value = true
}

// 关闭抽屉
const close = () => {
  visible.value = false
  emits('close')
  emits('update:visible', false)
}

// 判断是否可以同意换货
const canAgreeExchange = (record) => {
  if (!record) return false
  return (
    record.process_status === 'waiting_review' &&
    (record.after_type === 'exchange' || record.after_type === '')
  )
}

// 判断是否可以同意退货
const canAgreeReturn = (record) => {
  if (!record) return false
  return (
    record.process_status === 'waiting_review' &&
    (record.after_type === 'return' || record.after_type === '')
  )
}

// 判断是否可以同意退款
const canAgreeRefund = (record) => {
  if (!record) return false
  return (
    record.process_status === 'waiting_review' &&
    (record.after_type === 'refund' || record.after_type === '')
  )
}

// 判断是否可以同意补发
const canAgreeResend = (record) => {
  if (!record) return false
  return (
    record.process_status === 'waiting_review' &&
    (record.after_type === 'resend' || record.after_type === '')
  )
}

// 判断是否可以拒绝申请
const canReject = (record) => {
  if (!record) return false
  return record.process_status === 'waiting_review'
}

// 判断是否可以执行任何操作
const canPerformAnyAction = (record) => {
  return canAgreeExchange(record) || canAgreeReturn(record) || 
         canAgreeRefund(record) || canAgreeResend(record) || 
         canReject(record)
}

// 操作事件处理
const onAgreeExchange = () => {
  emits('agree-exchange', afterOrderData.value)
}

const onAgreeReturn = () => {
  emits('agree-return', afterOrderData.value)
}

const onAgreeRefund = () => {
  emits('agree-refund', afterOrderData.value)
}

const onAgreeResend = () => {
  emits('agree-resend', afterOrderData.value)
}

const onRejectApplication = () => {
  emits('reject-application', afterOrderData.value)
}

// 监听抽屉可见性变化
watch(visible, (val) => {
  if (val) {
    if (props.afterOrderId) {
      fetchAfterOrderDetails()
    } else if (props.orderDetail && Object.keys(props.orderDetail).length > 0) {
      afterOrderData.value = props.orderDetail
    }
  }
})

// 监听订单详情变化
watch(() => props.orderDetail, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    afterOrderData.value = newVal
  }
})

// 监听ID变化重新获取数据
watch(() => props.afterOrderId, (newVal) => {
  if (newVal) {
    fetchAfterOrderDetails()
  }
})
</script>

<style scoped>
.a-image {
  cursor: pointer;
}
</style>