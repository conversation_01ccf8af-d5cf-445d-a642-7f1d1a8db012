<template>
    <div class="ma-content-block p-4">
      <!-- CRUD 组件 -->
      <ma-crud :options="crud" :columns="columns" ref="crudRef" @row-click="viewDetail">
        <!-- 提醒标记 -->
        <template #reminder="{ record }">
          <a-tag color="red" v-if="record.reminder">
            <icon-exclamation-circle-fill />
            {{ record.reminder }}
          </a-tag>
        </template>
        <!-- 订单状态列 -->
        <template #order_status="{ record }">
          <a-tag :color="getStatusColor(record.order_status)">
            {{ orderStatusMap[record.order_status] || "未知" }}
          </a-tag>
        </template>
        <!-- 自定义列 - 下单时间 -->
        <template #order_time="{ record }">
          <div v-time="record.order_time"></div>
        </template>
        <!-- 下单距今 -->
        <template #days_since_order="{ record }">
          <div>{{ record.days_since_order }} 天</div>
        </template>
        <!-- 实付金额 -->
        <template #actual_payment="{ record }">
          <div>¥{{ formatPrice(record.actual_payment) }}</div>
        </template>
        <!-- 邮费 -->
        <template #shipping_fee="{ record }">
          <div>¥{{ formatPrice(record.shipping_fee) }}</div>
        </template>
        <!-- 优惠 -->
        <template #discount="{ record }">
          <div>¥{{ formatPrice(record.discount) }}</div>
        </template>
        <template #tableAfterButtons>
            <a-button type="primary" @click="openAddDialog">
              <template #icon><icon-plus /></template>新增
            </a-button>
        </template>
      </ma-crud>
  
      <!-- 详情抽屉组件 -->
      <AfterOrderDetails
        v-model:visible="drawerVisible"
        :order-detail="currentRecord"
        @cancel="drawerVisible = false"
      />
      
      <!-- 新增售后订单组件 -->
      <AddAfterOrder
        v-model:visible="formVisible"
        :edit-data="formData"
        @success="handleAddSuccess"
        @cancel="formVisible = false"
      />
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, reactive } from "vue";
  import { Message } from "@arco-design/web-vue";
  import AfterOrderDetails from "./components/AfterOrderDetails.vue";
  import AddAfterOrder from "./components/add.vue";
  
  definePageMeta({
    name: "addAfterOrder",
    path: "/master/order/afterModule/addAfterOrder",
  });
  
  const crudRef = ref();
  const formRef = ref();
  const drawerVisible = ref(false);
  const formVisible = ref(false);
  const currentRecord = ref(null);
  const formData = ref({});
  
  // 订单状态映射
  const orderStatusMap = {
    pending_payment: "待支付",
    pending_shipment: "待发货",
    shipped: "已发货",
    completed: "已完成",
    cancelled: "已取消",
    refunding: "退款中",
    refunded: "已退款"
  };
  
  // 售后类型选项
  const afterTypeOptions = [
    { label: "退货", value: "return" },
    { label: "换货", value: "exchange" },
    { label: "补发", value: "resend" },
    { label: "维修", value: "repair" },
  ];
  
  // 问题原因选项
  const problemReasonOptions = [
    { label: "质量问题", value: "quality" },
    { label: "尺寸不合适", value: "size" },
    { label: "颜色不符", value: "color" },
    { label: "功能故障", value: "malfunction" },
    { label: "其他", value: "other" },
  ];
  
  // 支付退回方式
  const refundMethodOptions = [
    { label: "原路退回", value: "original" },
    { label: "退到余额", value: "balance" },
  ];
  
  // 获取状态对应的颜色
  const getStatusColor = (status) => {
    const colorMap = {
      pending_payment: "orange",
      pending_shipment: "blue",
      shipped: "purple",
      completed: "green",
      cancelled: "red",
      refunding: "gold",
      refunded: "lime"
    };
    return colorMap[status] || "gray";
  };
  
  // 格式化价格
  const formatPrice = (price) => {
    return (price || 0).toFixed(2);
  };
  
  // 查看详情
  const viewDetail = (record, event) => {
    // 阻止事件冒泡，防止触发行点击事件
    if (event) event.stopPropagation();
    currentRecord.value = record;
    drawerVisible.value = true;
  };
  
  // 创建售后单
  const createAfterOrder = (record, event) => {
    // 阻止事件冒泡，防止触发行点击事件
    if (event) event.stopPropagation();
    // 清空表单数据
    formData.value = {
      order_no: record.order_no,
      shop_name: record.shop_name,
      buyer_id: record.buyer_id,
      recipient: record.recipient,
      contact_phone: record.contact_phone,
      shipping_address: record.shipping_address,
      order_status: record.order_status,
      actual_payment: record.actual_payment,
      original_order_id: record.id
    };
    // 打开表单弹窗
    formVisible.value = true;
  };
  
  // 添加至工单
  const addToTask = (record, event) => {
    // 阻止事件冒泡，防止触发行点击事件
    if (event) event.stopPropagation();
    // 此处添加添加至工单的逻辑
    Message.success("已添加至工单任务");
  };

  // 打开添加弹窗
  const openAddDialog = () => {
    // 初始化表单数据
    formData.value = {
      after_type: "退货", // 默认退货
      problem_reason: "",
      package_status: "",
      order_no: ""
    };
    formVisible.value = true;
  };
  
  // 处理新增成功回调
  const handleAddSuccess = (data) => {
    Message.success("售后订单创建成功");
    formVisible.value = false;
    // 刷新表格数据
    crudRef.value.refresh();
  };

  // 表单列配置
  const formColumns = reactive([
    {
      label: "售后类型",
      dataIndex: "after_type",
      formType: "radio",
      dict: { data: afterTypeOptions },
      rules: [{ required: true, message: "请选择售后类型" }]
    },
    {
      label: "包裹状态",
      dataIndex: "package_status",
      formType: "text",
      hide: true
    },
    {
      label: "问题类型",
      dataIndex: "problem_type",
      formType: "text",
      hide: true
    },
    {
      label: "问题原因",
      dataIndex: "problem_reason",
      formType: "select",
      dict: { data: problemReasonOptions },
      rules: [{ required: true, message: "请选择问题原因" }]
    },
    {
      label: "订单号",
      dataIndex: "order_no",
      formType: "text",
      addDisplay: true,
      editDisplay: false,
      disabled: true
    },
    {
      label: "退款金额",
      dataIndex: "refund_amount",
      formType: "input-number",
      rules: [{ required: true, message: "请输入退款金额" }]
    },
    {
      label: "支付退回方式",
      dataIndex: "refund_method",
      formType: "select",
      dict: { data: refundMethodOptions },
      rules: [{ required: true, message: "请选择支付退回方式" }]
    },
    {
      label: "客户收款账户",
      dataIndex: "customer_account",
      formType: "text",
      rules: [{ required: true, message: "请输入客户收款账户" }]
    },
    {
      label: "退货回邮件",
      dataIndex: "return_address",
      formType: "select",
      dict: { 
        data: [
          { label: "总部仓库", value: "headquarters" },
          { label: "分支仓库", value: "branch" }
        ] 
      },
      hide: ({ formModel }) => formModel.after_type !== "return" && formModel.after_type !== "exchange"
    },
    {
      label: "地址",
      dataIndex: "address",
      formType: "textarea",
      hide: ({ formModel }) => formModel.after_type !== "return" && formModel.after_type !== "exchange"
    },
    {
      label: "相关说明",
      dataIndex: "remark",
      formType: "textarea",
      rules: [{ required: true, message: "请输入相关说明" }]
    }
  ]);

  // 表单配置
  const formOptions = reactive({
    labelWidth: 120,
    labelPosition: "right",
    inline: false,
    size: "medium",
    submitType: "primary",
    submitStatus: "normal",
    submitText: "确定",
    resetType: "outline",
    resetStatus: "normal",
    resetText: "取消",
    formTitle: "新建售后申请单",
    showFormTitle: false,
    showButtons: true,
    customClass: "after-order-form"
  });

  // 提交表单
  const submitForm = async (data) => {
    try {
      console.log("提交的表单数据:", data);
      // 这里添加提交到后端API的代码
      // const res = await afterOrderApi.create(data);
      // if (res.code === 200) {
      //   Message.success("创建售后单成功");
      //   formVisible.value = false;
      //   crudRef.value.refresh();
      // }
      
      // 模拟提交成功
      Message.success("创建售后单成功");
      formVisible.value = false;
      // 刷新表格
      crudRef.value.refresh();
    } catch (error) {
      console.error("创建售后单失败:", error);
      Message.error("创建售后单失败");
    }
  };

  // 模拟数据
  const mockData = reactive({
    list: [
      {
        id: "1001",
        reminder: "紧急",
        shop_name: "京东官方旗舰店",
        order_no: "JD20230515001",
        order_status: "completed",
        buyer_message: "请尽快发货",
        remark: "春节前必须发货",
        buyer_id: "10086",
        recipient: "张三",
        contact_phone: "13800138000",
        shipping_address: "北京市朝阳区某某路123号",
        actual_payment: 299.99,
        shipping_fee: 10.00,
        discount: 20.00,
        order_time: "2023-01-01 12:00:00",
        days_since_order: 30,
        related_after_order: "无",
        related_task: "无",
        express_company: "顺丰速运",
        express_no: "SF1234567890",
        is_eligible_for_after: true
      },
      {
        id: "1002",
        reminder: "",
        shop_name: "天猫旗舰店",
        order_no: "TM20230520002",
        order_status: "shipped",
        buyer_message: "周末送货",
        remark: "客户要求当面验货",
        buyer_id: "10087",
        recipient: "李四",
        contact_phone: "13900139000",
        shipping_address: "上海市浦东新区张江高科技园区",
        actual_payment: 1299.00,
        shipping_fee: 0.00,
        discount: 100.00,
        order_time: "2023-05-10 09:30:00",
        days_since_order: 5,
        related_after_order: "无",
        related_task: "TK2023052001",
        express_company: "中通快递",
        express_no: "ZT9876543210",
        is_eligible_for_after: true
      },
      {
        id: "1003",
        reminder: "催发货",
        shop_name: "拼多多店铺",
        order_no: "PDD20230518003",
        order_status: "pending_shipment",
        buyer_message: "",
        remark: "客户多次催发货",
        buyer_id: "10088",
        recipient: "王五",
        contact_phone: "13700137000",
        shipping_address: "广州市天河区体育西路",
        actual_payment: 99.90,
        shipping_fee: 8.00,
        discount: 10.00,
        order_time: "2023-05-18 15:45:00",
        days_since_order: 2,
        related_after_order: "无",
        related_task: "无",
        express_company: "",
        express_no: "",
        is_eligible_for_after: false
      },
      {
        id: "1004",
        reminder: "",
        shop_name: "自营店铺",
        order_no: "ZY20230501004",
        order_status: "refunding",
        buyer_message: "申请退款",
        remark: "客户不想要了",
        buyer_id: "10089",
        recipient: "赵六",
        contact_phone: "13600136000",
        shipping_address: "成都市武侯区人民南路",
        actual_payment: 599.00,
        shipping_fee: 15.00,
        discount: 0.00,
        order_time: "2023-05-01 12:30:00",
        days_since_order: 19,
        related_after_order: "AF2023051901",
        related_task: "TK2023051902",
        express_company: "韵达快递",
        express_no: "YD1122334455",
        is_eligible_for_after: false
      },
      {
        id: "1005",
        reminder: "",
        shop_name: "苏宁易购",
        order_no: "SN20230425005",
        order_status: "completed",
        buyer_message: "",
        remark: "",
        buyer_id: "10090",
        recipient: "钱七",
        contact_phone: "13500135000",
        shipping_address: "武汉市江汉区解放大道",
        actual_payment: 2499.00,
        shipping_fee: 0.00,
        discount: 200.00,
        order_time: "2023-04-25 10:20:00",
        days_since_order: 25,
        related_after_order: "无",
        related_task: "无",
        express_company: "申通快递",
        express_no: "ST6677889900",
        is_eligible_for_after: true
      }
    ],
    total: {
      total: 5,
      currentPage: 1,
      totalPage: 1,
      pageSize: 10
    }
  });

  // CRUD 配置
  const crud = reactive({
    api: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: "200",
          message: "操作成功",
          data: {
            items: mockData.list,
            pageInfo: mockData.total,
          },
        });
      }, 300); // 模拟网络请求延迟
    });
  },
    pk: "id",
    // add: { show: true },
    // edit: { show: true },
    delete: { show: true },
    pageSize: 10,
    pageSizeOption: [10, 20, 50, 100],
    exportExcel: true,
    enableRowHighlight: true
  });
  
  // 表格列配置
  const columns = reactive([
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
    },
    {
      title: "提醒",
      dataIndex: "reminder",
      width: 80,
    },
    {
      title: "店铺",
      dataIndex: "shop_name",
      width: 150,
      search: true,
    },
    {
      title: "订单号",
      dataIndex: "order_no",
      width: 150,
      search: true,
    },
    {
      title: "订单状态",
      dataIndex: "order_status",
      width: 100,
      formType: 'select',
      dict: {
        data: [
          { label: '待支付', value: 'pending_payment' },
          { label: '待发货', value: 'pending_shipment' },
          { label: '已发货', value: 'shipped' },
          { label: '已完成', value: 'completed' },
          { label: '已取消', value: 'cancelled' },
          { label: '退款中', value: 'refunding' },
          { label: '已退款', value: 'refunded' }
        ]
      },
    },
    {
      title: "买家留言",
      dataIndex: "buyer_message",
      width: 150,
      ellipsis: true,
    },
    {
      title: "备注",
      dataIndex: "remark",
      width: 150,
      ellipsis: true,
    },
    {
      title: "买家ID",
      dataIndex: "buyer_id",
      width: 100,
    },
    {
      title: "收件人",
      dataIndex: "recipient",
      width: 100,
    },
    {
      title: "联系电话",
      dataIndex: "contact_phone",
      width: 130,
      search: true,
    },
    {
      title: "收货地址",
      dataIndex: "shipping_address",
      width: 200,
      ellipsis: true,
    },
    {
      title: "实付金额",
      dataIndex: "actual_payment",
      width: 100,
    },
    {
      title: "邮费",
      dataIndex: "shipping_fee",
      width: 80,
    },
    {
      title: "优惠",
      dataIndex: "discount",
      width: 80,
    },
    {
      title: "下单时间",
      dataIndex: "order_time",
      width: 160,
      search: true,
      formType: "daterange",
    },
    {
      title: "下单距今",
      dataIndex: "days_since_order",
      width: 100,
    },
    {
      title: "关联售后",
      dataIndex: "related_after_order",
      width: 150,
    },
    {
      title: "关联工单任务",
      dataIndex: "related_task",
      width: 150,
    },
    {
      title: "快递公司",
      dataIndex: "express_company",
      width: 120,
      search: true,
    },
    {
      title: "快递单号",
      dataIndex: "express_no",
      width: 150,
      search: true,
    },
    
  ]);
  
  // 页面加载时执行
  onMounted(() => {
    // 可以添加一些初始化逻辑
    console.log("新增售后单页面已加载");
  });
  </script>