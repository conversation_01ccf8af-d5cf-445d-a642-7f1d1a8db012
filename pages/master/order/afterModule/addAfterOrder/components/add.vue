<template>
  <a-modal
    v-model:visible="visibleValue"
    :title="modalTitle"
    :mask-closable="false"
    @cancel="handleCancel"
    :footer="false"
    :width="1100"
  >
    <div class="order-form-container">
      <!-- 第一部分：基本信息 -->
      <div class="section-title">基本信息</div>
      <a-form ref="formRef1" :model="formData" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="after_type" label="售后类型" :rules="[{ required: true, message: '请选择售后类型' }]" label-col-flex="100px">
              <a-radio-group v-model="formData.after_type">
                <a-radio v-for="option in afterTypeOptions" :key="option.value" :value="option.value">{{ option.label }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="package_status" label="包裹状态" :rules="[{ required: true, message: '请选择包裹状态' }]" label-col-flex="100px">
              <a-select v-model="formData.package_status" placeholder="请选择包裹状态">
                <a-option v-for="option in packageStatusOptions" :key="option.value" :value="option.value">{{ option.label }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="problem_reason" label="问题原因" :rules="[{ required: true, message: '请选择问题原因' }]" label-col-flex="100px">
              <a-select v-model="formData.problem_reason" placeholder="请选择问题原因">
                <a-option v-for="option in problemReasonOptions" :key="option.value" :value="option.value">{{ option.label }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="order_no" label="订单号" :rules="[{ required: true, message: '请输入订单号' }]" label-col-flex="100px">
              <a-input v-model="formData.order_no" placeholder="请输入订单号" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="refund_amount" label="退款金额" :rules="[{ required: true, message: '请输入退款金额' }]" label-col-flex="100px">
              <a-input-number v-model="formData.refund_amount" :min="0" :precision="2" prefix="¥" placeholder="请输入退款金额" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="customer_account" label="客户收款账户" :rules="[{ required: true, message: '请输入客户收款账户' }]" label-col-flex="100px">
              <a-input v-model="formData.customer_account" placeholder="请输入客户收款账户" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="return_warehouse" label="退入仓库" :rules="[{ required: true, message: '请选择退入仓库' }]" label-col-flex="100px">
              <a-select v-model="formData.return_warehouse" placeholder="请选择退入仓库">
                <a-option v-for="option in warehouseOptions" :key="option.value" :value="option.value">{{ option.label }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="buyer_return_express" label="买家退回快递" label-col-flex="100px">
              <a-input v-model="formData.buyer_return_express" placeholder="请输入买家退回快递" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="buyer_return_tracking" label="买家退回单号" label-col-flex="100px">
              <a-input v-model="formData.buyer_return_tracking" placeholder="请输入买家退回单号" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="compensation_party" label="赔付方" :rules="[{ required: true, message: '请选择赔付方' }]" label-col-flex="100px">
              <a-select v-model="formData.compensation_party" placeholder="请选择赔付方">
                <a-option v-for="option in compensationPartyOptions" :key="option.value" :value="option.value">{{ option.label }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="compensation_amount" label="赔付金额" :rules="[{ required: true, message: '请输入赔付金额' }]" label-col-flex="100px">
              <a-input-number v-model="formData.compensation_amount" :min="0" :precision="2" prefix="¥" placeholder="请输入赔付金额" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item field="problem_description" label="问题描述" :rules="[{ required: true, message: '请输入问题描述' }]" label-col-flex="100px">
              <a-input v-model="formData.problem_description" placeholder="请输入问题描述"  />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      
      <!-- 第二部分：客户信息 -->
      <div class="section-title mt-4">客户信息</div>
      <a-form ref="formRef2" :model="formData" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="shop" label="店铺" :rules="[{ required: true, message: '请输入店铺' }]" label-col-flex="100px">
              <a-input v-model="formData.shop" placeholder="请输入店铺" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="nickname" label="昵称" label-col-flex="100px">
              <a-input v-model="formData.nickname" placeholder="请输入昵称" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="name" label="姓名" :rules="[{ required: true, message: '请输入姓名' }]" label-col-flex="100px">
              <a-input v-model="formData.name" placeholder="请输入姓名" />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="mobile" label="手机" :rules="[{ required: true, message: '请输入手机号码' }]" label-col-flex="100px">
              <a-input v-model="formData.mobile" placeholder="请输入手机号码" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="telephone" label="固话" label-col-flex="100px">
              <a-input v-model="formData.telephone" placeholder="请输入固话" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="postcode" label="邮编" label-col-flex="100px">
              <a-input v-model="formData.postcode" placeholder="请输入邮编" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="province" label="省份" :rules="[{ required: true, message: '请输入省份' }]" label-col-flex="100px">
              <a-input v-model="formData.province" placeholder="请输入省份" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="city" label="市" :rules="[{ required: true, message: '请输入市' }]" label-col-flex="100px">
              <a-input v-model="formData.city" placeholder="请输入市" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="district" label="区" :rules="[{ required: true, message: '请输入区' }]" label-col-flex="100px">
              <a-input v-model="formData.district" placeholder="请输入区" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="street" label="街道" label-col-flex="100px">
              <a-input v-model="formData.street" placeholder="请输入街道" />
            </a-form-item>
          </a-col>
          <a-col :span="16">
            <a-form-item field="address" label="详细地址" :rules="[{ required: true, message: '请输入详细地址' }]" label-col-flex="100px">
              <a-input v-model="formData.address" placeholder="请输入详细地址"  />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 第三部分：详细信息Tabs -->      
      <div class="section-title mt-4">详细信息</div>
      <a-tabs type="card">
        <!-- 退款关联商品明细 -->
        <a-tab-pane key="1" title="退款关联商品明细">
          <a-table :data="relatedProductsData" :pagination="false" bordered>
            <template #columns>
              <a-table-column title="序号" align="center" width="60">
                <template #cell="{ rowIndex }">
                  {{ rowIndex + 1 }}
                </template>
              </a-table-column>
              <a-table-column title="图片" data-index="image" width="80" align="center">
                <template #cell="{ record }">
                  <a-image
                    v-if="record.image"
                    :src="record.image"
                    width="50"
                    height="50"
                    :preview="false"
                    fit="contain"
                  />
                  <div v-else style="width: 50px; height: 50px; background-color: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                    <icon-image />
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="商品编码" data-index="product_id">
                <template #cell="{ record }">
                  <a-input v-model="record.product_id" placeholder="请输入商品编码" />
                </template>
              </a-table-column>
              <a-table-column title="商品名称" data-index="product_name" />
              <a-table-column title="规格" data-index="spec" />
              <a-table-column title="单价" data-index="price" />
              <a-table-column title="折后单价" data-index="discount_price">
                <template #cell="{ record }">
                  <a-input-number v-model="record.discount_price" :min="0" :precision="2" placeholder="请输入折后单价" style="width: 100%" />
                </template>
              </a-table-column>
              <a-table-column title="数量" data-index="quantity">
                <template #cell="{ record }">
                  <a-input-number v-model="record.quantity" :min="1" :precision="0" placeholder="请输入数量" style="width: 100%" />
                </template>
              </a-table-column>
              <a-table-column title="明细应退" data-index="refund_amount">
                <template #cell="{ record }">
                  <a-input-number v-model="record.refund_amount" :min="0" :precision="2" placeholder="请输入明细应退" style="width: 100%" />
                </template>
              </a-table-column>
              <a-table-column title="备注" data-index="remark">
                <template #cell="{ record }">
                  <a-input v-model="record.remark" placeholder="请输入备注" />
                </template>
              </a-table-column>
              <a-table-column title="操作" width="80" align="center">
                <template #cell="{ record }">
                  <a-button type="text" size="small" @click="handleRemoveProduct(record.id)">删除</a-button>
                </template>
              </a-table-column>
            </template>
          </a-table>
          <div class="mt-2 text-right">
            <a-button type="primary" size="small" @click="handleAddRelatedProduct">添加商品</a-button>
          </div>
        </a-tab-pane>
        
        <!-- 换出商品明细 -->
        <a-tab-pane key="2" title="换出商品明细">
          <a-table :data="exchangeProductsData" :pagination="false" bordered>
            <template #columns>
              <a-table-column title="序号" align="center" width="60">
                <template #cell="{ rowIndex }">
                  {{ rowIndex + 1 }}
                </template>
              </a-table-column>
              <a-table-column title="图片" data-index="image" width="80" align="center">
                <template #cell="{ record }">
                  <a-image
                    v-if="record.image"
                    :src="record.image"
                    width="50"
                    height="50"
                    :preview="false"
                    fit="contain"
                  />
                  <div v-else style="width: 50px; height: 50px; background-color: #f5f5f5; display: flex; align-items: center; justify-content: center;">
                    <icon-image />
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="商品编码" data-index="product_id">
                <template #cell="{ record }">
                  <a-input v-model="record.product_id" placeholder="请输入商品编码" />
                </template>
              </a-table-column>
              <a-table-column title="商品名称" data-index="product_name" />
              <a-table-column title="规格" data-index="spec" />
              <a-table-column title="单价" data-index="price" />
              <a-table-column title="折后单价" data-index="discount_price">
                <template #cell="{ record }">
                  <a-input-number v-model="record.discount_price" :min="0" :precision="2" placeholder="请输入折后单价" style="width: 100%" />
                </template>
              </a-table-column>
              <a-table-column title="数量" data-index="quantity">
                <template #cell="{ record }">
                  <a-input-number v-model="record.quantity" :min="1" :precision="0" placeholder="请输入数量" style="width: 100%" />
                </template>
              </a-table-column>
              <a-table-column title="金额" data-index="amount">
                <template #cell="{ record }">
                  <a-input-number v-model="record.amount" :min="0" :precision="2" placeholder="请输入金额" style="width: 100%" />
                </template>
              </a-table-column>
              <a-table-column title="备注" data-index="remark">
                <template #cell="{ record }">
                  <a-input v-model="record.remark" placeholder="请输入备注" />
                </template>
              </a-table-column>
              <a-table-column title="操作" width="80" align="center">
                <template #cell="{ record }">
                  <a-button type="text" size="small" @click="handleRemoveExchangeProduct(record.id)">删除</a-button>
                </template>
              </a-table-column>
            </template>
          </a-table>
          <div class="mt-2 text-right">
            <a-button type="primary" size="small" @click="handleAddExchangeProduct">添加商品</a-button>
          </div>
        </a-tab-pane>
        
        <!-- 凭证 -->
        <a-tab-pane key="3" title="凭证">
          <a-upload
            action="/api/upload"
            list-type="picture-card"
            :file-list="fileList"
            @change="handleVoucherChange"
          >
            <div class="arco-upload-trigger-picture">
              <div class="arco-upload-trigger-picture-text">
                <icon-plus />
                <div>上传凭证</div>
              </div>
            </div>
          </a-upload>
        </a-tab-pane>
      </a-tabs>

      <!-- 按钮组 -->
      <div class="form-buttons">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="submitForm">提交</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineEmits, defineProps, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconPlus, IconImage } from '@arco-design/web-vue/es/icon';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'success', 'cancel']);

// 使用计算属性处理双向绑定
const visibleValue = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
});

const formRef1 = ref();
const formRef2 = ref();

// 退款关联商品数据
const relatedProductsData = ref([]);

// 换出商品数据
const exchangeProductsData = ref([]);

// 凭证文件列表
const fileList = ref([]);

// 表单数据
const formData = reactive({
  // 基本信息
  after_type: '退货', // 售后类型
  package_status: '', // 包裹状态
  problem_reason: '', // 问题原因
  problem_description: '', // 问题描述
  order_no: '', // 订单号
  refund_amount: 0, // 退款金额
  customer_account: '', // 客户收款账户
  return_warehouse: '', // 退入仓库
  buyer_return_express: '', // 买家退回快递
  buyer_return_tracking: '', // 买家退回单号
  compensation_party: '', // 赔付方
  compensation_amount: 0, // 赔付金额
  
  // 客户信息
  shop: '', // 店铺
  nickname: '', // 昵称
  name: '', // 姓名
  mobile: '', // 手机
  telephone: '', // 固话
  postcode: '', // 邮编
  province: '', // 省份
  city: '', // 市
  district: '', // 区
  street: '', // 街道
  address: '' // 详细地址
});

// 计算属性：模态框标题
const modalTitle = computed(() => {
  return Object.keys(props.editData).length > 0 ? '编辑售后订单' : '新增售后订单';
});



// 售后类型选项
const afterTypeOptions = [
  { label: '退货', value: '退货' },
  { label: '换货', value: '换货' },
  { label: '补发', value: '补发' },
  { label: '维修', value: '维修' }
];

// 包裹状态选项
const packageStatusOptions = [
  { label: '未收到', value: '未收到' },
  { label: '已收到', value: '已收到' },
  { label: '已签收', value: '已签收' },
  { label: '已拒收', value: '已拒收' }
];

// 问题原因选项
const problemReasonOptions = [
  { label: '质量问题', value: '质量问题' },
  { label: '尺寸不合适', value: '尺寸不合适' },
  { label: '颜色不符', value: '颜色不符' },
  { label: '功能故障', value: '功能故障' },
  { label: '发错货', value: '发错货' },
  { label: '少发货', value: '少发货' },
  { label: '收到损坏', value: '收到损坏' },
  { label: '其他', value: '其他' }
];

// 退入仓库选项
const warehouseOptions = [
  { label: '总仓', value: '总仓' },
  { label: '北京仓', value: '北京仓' },
  { label: '上海仓', value: '上海仓' },
  { label: '广州仓', value: '广州仓' }
];

// 赔付方选项
const compensationPartyOptions = [
  { label: '商家', value: '商家' },
  { label: '厂家', value: '厂家' },
  { label: '物流', value: '物流' },
  { label: '不赔付', value: '不赔付' }
];

// 添加退款关联商品
const handleAddRelatedProduct = () => {
  // 这里可以弹出商品选择器或添加表单
  relatedProductsData.value.push({
    id: Date.now(),
    image: '',
    product_id: '',
    product_name: '',
    spec: '',
    price: 0,
    discount_price: 0,
    quantity: 1,
    refund_amount: 0,
    remark: ''
  });
};

// 删除退款关联商品
const handleRemoveProduct = (id) => {
  relatedProductsData.value = relatedProductsData.value.filter(item => item.id !== id);
};

// 添加换出商品
const handleAddExchangeProduct = () => {
  // 这里可以弹出商品选择器或添加表单
  exchangeProductsData.value.push({
    id: Date.now(),
    image: '',
    product_id: '',
    product_name: '',
    spec: '',
    price: 0,
    discount_price: 0,
    quantity: 1,
    refund_amount: 0,
    remark: ''
  });
};

// 删除换出商品
const handleRemoveExchangeProduct = (id) => {
  exchangeProductsData.value = exchangeProductsData.value.filter(item => item.id !== id);
};

// 处理凭证上传
const handleVoucherChange = (info) => {
  fileList.value = info.fileList;
};







// 提交表单
const submitForm = async () => {
  try {
    // 验证表单
    await Promise.all([
      formRef1.value.validate(),
      formRef2.value.validate()
    ]);

    // 提交表单数据
    // 这里可以添加实际的API调用
    console.log('提交表单数据', formData);
    
    // 模拟API调用
    setTimeout(() => {
      Message.success('提交成功');
      handleCancel();
      emit('success', formData);
    }, 1000);
  } catch (error) {
    console.error('表单验证失败', error);
    Message.error('请填写必填项');
  }
};

// 取消操作
const handleCancel = () => {
  visibleValue.value = false;
  emit('cancel');
  // 重置表单数据
  Object.assign(formData, {
    // 基本信息
    after_type: '退货',
    package_status: '',
    problem_reason: '',
    problem_description: '',
    order_no: '',
    refund_amount: 0,
    customer_account: '',
    return_warehouse: '',
    buyer_return_express: '',
    buyer_return_tracking: '',
    compensation_party: '',
    compensation_amount: 0,
    
    // 客户信息
    shop: '',
    nickname: '',
    name: '',
    mobile: '',
    telephone: '',
    postcode: '',
    province: '',
    city: '',
    district: '',
    street: '',
    address: ''
  });
};


</script>

<style scoped>
.after-order-form {
  width: 100%;
}

.order-form-container {
  padding: 0 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-left: 8px;
  border-left: 4px solid #165DFF;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  gap: 12px;
}

.mt-4 {
  margin-top: 24px;
}

.arco-form-item-label-col {
  width: 100px;
}
</style>