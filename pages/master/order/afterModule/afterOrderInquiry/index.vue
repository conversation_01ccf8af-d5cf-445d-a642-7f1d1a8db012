<template>
    <div class="ma-content-block p-4">
      <!-- CRUD 组件 -->
      <ma-crud :options="crud" :columns="columns" ref="crudRef" @row-click="viewDetail">
        <!-- 提醒标记 -->
        <template #reminder="{ record }">
          <a-tag color="red" v-if="record.reminder">
            <icon-exclamation-circle-fill />
            {{ record.reminder }}
          </a-tag>
        </template>
        <!-- 订单状态列 -->
        <template #order_status="{ record }">
          <a-tag :color="getStatusColor(record.order_status)">
            {{ orderStatusMap[record.order_status] || "未知" }}
          </a-tag>
        </template>
        <!-- 自定义列 - 下单时间 -->
        <template #order_time="{ record }">
          <div v-time="record.order_time"></div>
        </template>
        <!-- 下单距今 -->
        <template #days_since_order="{ record }">
          <div>{{ record.days_since_order }} 天</div>
        </template>
        <!-- 实付金额 -->
        <template #actual_payment="{ record }">
          <div>¥{{ formatPrice(record.actual_payment) }}</div>
        </template>
      </ma-crud>
  
      <!-- 详情抽屉组件 -->
      <AfterOrderDetails
        v-model:visible="drawerVisible"
        :order-detail="currentRecord"
        @close="drawerVisible = false"
      />
      
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, reactive } from "vue";
  import { Message } from "@arco-design/web-vue";
  import AfterOrderDetails from "./components/AfterOrderDetails.vue";
  
  definePageMeta({
    name: "afterOrderInquiry",
    path: "/master/order/afterModule/afterOrderInquiry",
  });
  
  const crudRef = ref();
  const formRef = ref();
  const drawerVisible = ref(false);
  const formVisible = ref(false);
  const currentRecord = ref(null);
  const formData = ref({});
  
  // 售后类型选项
  const afterTypeOptions = [
    { label: "退货", value: "return" },
    { label: "换货", value: "exchange" },
    { label: "补发", value: "resend" },
    { label: "维修", value: "repair" },
  ];
  
  // 问题原因选项
  const problemReasonOptions = [
    { label: "质量问题", value: "quality" },
    { label: "尺寸不合适", value: "size" },
    { label: "颜色不符", value: "color" },
    { label: "功能故障", value: "malfunction" },
    { label: "其他", value: "other" },
  ];
  
  // 获取售后类型对应的颜色
  const getAfterTypeColor = (type) => {
    const colorMap = {
      return: "red",
      exchange: "blue",
      resend: "green",
      repair: "yellow"
    };
    return colorMap[type] || "gray";
  };
  
  // 获取售后类型对应的标签
  const getAfterTypeLabel = (type) => {
    const labelMap = {
      return: "退货",
      exchange: "换货",
      resend: "补发",
      repair: "维修"
    };
    return labelMap[type] || "未知";
  };

  // 订单状态映射
  const orderStatusMap = {
    pending: "待处理",
    processing: "处理中",
    completed: "已完成",
    cancelled: "已取消"
  };

  // 获取订单状态对应的颜色
  const getStatusColor = (status) => {
    const colorMap = {
      pending: "orange",
      processing: "blue",
      completed: "green",
      cancelled: "red"
    };
    return colorMap[status] || "gray";
  };
  
  // 获取问题原因对应的颜色
  const getProblemReasonColor = (reason) => {
    const colorMap = {
      quality: "red",
      size: "blue",
      color: "green",
      malfunction: "yellow",
      other: "gray"
    };
    return colorMap[reason] || "gray";
  };
  
  // 获取问题原因对应的标签
  const getProblemReasonLabel = (reason) => {
    const labelMap = {
      quality: "质量问题",
      size: "尺寸不合适",
      color: "颜色不符",
      malfunction: "功能故障",
      other: "其他"
    };
    return labelMap[reason] || "未知";
  };
  
  // 格式化价格
  const formatPrice = (price) => {
    return (price || 0).toFixed(2);
  };
  
  // 查看详情
  const viewDetail = (record, event) => {
    // 阻止事件冒泡，防止触发行点击事件
    if (event) event.stopPropagation();
    currentRecord.value = record;
    drawerVisible.value = true;
  };


  // 表单列配置
  const formColumns = reactive([
    {
      label: "售后类型",
      dataIndex: "after_type",
      formType: "radio",
      dict: { data: afterTypeOptions },
      rules: [{ required: true, message: "请选择售后类型" }]
    },
    {
      label: "包裹状态",
      dataIndex: "package_status",
      formType: "text",
      hide: true
    },
    {
      label: "问题类型",
      dataIndex: "problem_type",
      formType: "text",
      hide: true
    },
    {
      label: "问题原因",
      dataIndex: "problem_reason",
      formType: "select",
      dict: { data: problemReasonOptions },
      rules: [{ required: true, message: "请选择问题原因" }]
    },
    {
      label: "订单号",
      dataIndex: "order_no",
      formType: "text",
      addDisplay: true,
      editDisplay: false,
      disabled: true
    },
    {
      label: "退款金额",
      dataIndex: "refund_amount",
      formType: "input-number",
      rules: [{ required: true, message: "请输入退款金额" }]
    },
    {
      label: "支付退回方式",
      dataIndex: "refund_method",
      formType: "select",
      dict: { data: [] },
      rules: [{ required: true, message: "请选择支付退回方式" }]
    },
    {
      label: "客户收款账户",
      dataIndex: "customer_account",
      formType: "text",
      rules: [{ required: true, message: "请输入客户收款账户" }]
    },
    {
      label: "退货回邮件",
      dataIndex: "return_address",
      formType: "select",
      dict: { 
        data: [
          { label: "总部仓库", value: "headquarters" },
          { label: "分支仓库", value: "branch" }
        ] 
      },
      hide: ({ formModel }) => formModel.after_type !== "return" && formModel.after_type !== "exchange"
    },
    {
      label: "地址",
      dataIndex: "address",
      formType: "textarea",
      hide: ({ formModel }) => formModel.after_type !== "return" && formModel.after_type !== "exchange"
    },
    {
      label: "相关说明",
      dataIndex: "remark",
      formType: "textarea",
      rules: [{ required: true, message: "请输入相关说明" }]
    }
  ]);

  // 表单配置
  const formOptions = reactive({
    labelWidth: 120,
    labelPosition: "right",
    inline: false,
    size: "medium",
    submitType: "primary",
    submitStatus: "normal",
    submitText: "确定",
    resetType: "outline",
    resetStatus: "normal",
    resetText: "取消",
    formTitle: "新建售后申请单",
    showFormTitle: false,
    showButtons: true,
    customClass: "after-order-form"
  });

  // 提交表单
  const submitForm = async (data) => {
    try {
      console.log("提交的表单数据:", data);
     
      // 模拟提交成功
      Message.success("创建售后单成功");
      formVisible.value = false;
      // 刷新表格
      crudRef.value.refresh();
    } catch (error) {
      console.error("创建售后单失败:", error);
      Message.error("创建售后单失败");
    }
  };

  // 模拟数据
  const mockData = reactive({
    list: [
      {
        id: "1001",
        doc_no: "AF20250516001",
        after_type: "return",
        refund_amount: 299.99,
        problem_reason: "quality", 
        problem_detail: "商品收到后发现外观环节有磁性损坏，无法正常使用",
        return_quantity: 1,
        buyer_id: "10086",
        contact_phone: "***********",
        return_express: "顺丰快递",
        return_express_no: "SF2025051601",
        applicant: "张三",
        recipient: "张三",
        shipping_address: "北京市朝阳区某某路123号",
        create_time: "2025-05-15 10:20:30",
        buyer_apply_time: "2025-05-15 09:15:20",
        shop_name: "京东官方旗舰店",
        system_order_no: "JD20230515001", 
        online_order_no: "JD123456789",
        after_order_no: "AF20250516001",
        platform_refund: 299.99,
        should_refund: 299.99,
        service_fee: 0.00,
        shipping_fee: 10.00,
        return_warehouse: "总部仓库",
        original_order_mark: "正常订单",
        original_order_remark: "无特殊要求",
        customer_account: "微信-***********",
        compensation_party: "供应商",
        compensation_amount: 200.00,
        original_warehouse: "华东仓库",
        original_express_no: "SF1234567890",
        original_express: "顺丰快递",
        after_stage: "refunding",
        payment_time: "2025-05-01 12:30:40",
        refund_time: "",
        online_after_status: "待审核"
      },
      {
        id: "1002",
        doc_no: "AF20250516002",
        after_type: "exchange",
        refund_amount: 0.00,
        problem_reason: "size",
        problem_detail: "尺码偏小，无法穿着，申请更换更大尺码",
        return_quantity: 1,
        buyer_id: "10087",
        contact_phone: "***********",
        return_express: "中通快递",
        return_express_no: "ZT2025051602",
        applicant: "李四",
        recipient: "李四",
        shipping_address: "上海市浦东新区张江高科技园区",
        create_time: "2025-05-14 15:30:20",
        buyer_apply_time: "2025-05-14 14:25:10",
        shop_name: "天猫旗舰店",
        system_order_no: "TM20230520002",
        online_order_no: "TM987654321",
        after_order_no: "AF20250516002",
        platform_refund: 0.00,
        should_refund: 0.00,
        service_fee: 0.00,
        shipping_fee: 0.00,
        return_warehouse: "华南仓库",
        original_order_mark: "促销订单",
        original_order_remark: "参加618促销活动",
        customer_account: "支付宝-***********",
        compensation_party: "平台",
        compensation_amount: 0.00,
        original_warehouse: "华东仓库",
        original_express_no: "ZT9876543210",
        original_express: "中通快递",
        after_stage: "processing",
        payment_time: "2025-05-10 10:15:30",
        refund_time: "",
        online_after_status: "处理中"
      },
      {
        id: "1003",
        doc_no: "AF20250516003",
        after_type: "resend",
        refund_amount: 0.00,
        problem_reason: "malfunction",
        problem_detail: "收到商品后发现缺少触动开关，无法使用，要求补发",
        return_quantity: 0,
        buyer_id: "10088",
        contact_phone: "***********",
        return_express: "",
        return_express_no: "",
        applicant: "王五",
        recipient: "王五",
        shipping_address: "广州市天河区体育西路",
        create_time: "2025-05-16 08:40:30",
        buyer_apply_time: "2025-05-16 08:20:15",
        shop_name: "拼多多店铺",
        system_order_no: "PDD20230518003",
        online_order_no: "PDD135792468",
        after_order_no: "AF20250516003",
        platform_refund: 0.00,
        should_refund: 0.00,
        service_fee: 5.00,
        shipping_fee: 8.00,
        return_warehouse: "",
        original_order_mark: "团购订单",
        original_order_remark: "团购价优惠",
        customer_account: "",
        compensation_party: "厂商",
        compensation_amount: 20.00,
        original_warehouse: "华南仓库",
        original_express_no: "",
        original_express: "",
        after_stage: "applying",
        payment_time: "2025-05-15 18:30:20",
        refund_time: "",
        online_after_status: "待确认"
      },
      {
        id: "1004",
        doc_no: "AF20250516004",
        after_type: "repair",
        refund_amount: 0.00,
        problem_reason: "malfunction",
        problem_detail: "产品使用过程中出现闪屏问题，需要维修",
        return_quantity: 1,
        buyer_id: "10089",
        contact_phone: "***********",
        return_express: "韵达快递",
        return_express_no: "YD2025051604",
        applicant: "赵六",
        recipient: "赵六",
        shipping_address: "成都市武侯区人民南路",
        create_time: "2025-05-13 14:30:25",
        buyer_apply_time: "2025-05-13 13:40:15",
        shop_name: "自营店铺",
        system_order_no: "ZY20230501004",
        online_order_no: "ZY246813579",
        after_order_no: "AF20250516004",
        platform_refund: 0.00,
        should_refund: 0.00,
        service_fee: 40.00,
        shipping_fee: 15.00,
        return_warehouse: "售后维修中心",
        original_order_mark: "普通订单",
        original_order_remark: "发货前请联系确认",
        customer_account: "",
        compensation_party: "供应商",
        compensation_amount: 0.00,
        original_warehouse: "华西仓库",
        original_express_no: "YD1122334455",
        original_express: "韵达快递",
        after_stage: "completed",
        payment_time: "2025-05-01 15:20:10",
        refund_time: "",
        online_after_status: "已完成"
      },
      {
        id: "1005",
        doc_no: "AF20250516005",
        after_type: "return",
        refund_amount: 2499.00,
        problem_reason: "other",
        problem_detail: "买家发现产品与广告宣传不符，要求全额退款退货",
        return_quantity: 1,
        buyer_id: "10090",
        contact_phone: "***********",
        return_express: "申通快递",
        return_express_no: "ST2025051605",
        applicant: "钱七",
        recipient: "钱七",
        shipping_address: "武汉市江汉区解放大道",
        create_time: "2025-05-12 09:50:30",
        buyer_apply_time: "2025-05-12 09:10:20",
        shop_name: "苏宁易购",
        system_order_no: "SN20230425005",
        online_order_no: "SN369258147",
        after_order_no: "AF20250516005",
        platform_refund: 2499.00,
        should_refund: 2499.00,
        service_fee: 0.00,
        shipping_fee: 0.00,
        return_warehouse: "总部仓库",
        original_order_mark: "会员特价",
        original_order_remark: "重要客户，请优先处理",
        customer_account: "银行卡-6222********8888",
        compensation_party: "平台",
        compensation_amount: 200.00,
        original_warehouse: "华中仓库",
        original_express_no: "ST6677889900",
        original_express: "申通快递",
        after_stage: "completed",
        payment_time: "2025-04-25 12:40:50",
        refund_time: "2025-05-14 14:30:20",
        online_after_status: "已完成"
      }
    ],
    total: {
      total: 5,
      currentPage: 1,
      totalPage: 1,
      pageSize: 10
    }
  });

  // CRUD 配置
  const crud = reactive({
    api: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: "200",
          message: "操作成功",
          data: {
            items: mockData.list,
            pageInfo: mockData.total,
          },
        });
      }, 300); // 模拟网络请求延迟
    });
  },
    pk: "id",
    // add: { show: true },
    // edit: { show: true },
    delete: { show: true },
    operationWidth: 200,
    pageSize: 10,
    pageSizeOption: [10, 20, 50, 100],
    exportExcel: true,
    enableRowHighlight: true,
    searchLabelWidth: '120px',
  });

  // 表格列配置
  const columns = reactive([
    {
      title: "提醒",
      dataIndex: "reminder",
      width: 100,
      slot: true,
    },
    {
      title: "处理状态",
      dataIndex: "order_status",
      width: 120,
      slot: true,
      search: true,
      formType: 'select',
      dict: {
        data: afterTypeOptions
      },
      search: true,
    },
    {
      title: "退款金额",
      dataIndex: "refund_amount",
      width: 100,
    },
    {
      title: "问题原因",
      dataIndex: "problem_reason",
      width: 120,
      search: true,
      formType: 'select',
      dict: {
        data: problemReasonOptions
      },
    },
    {
      title: "问题详情",
      dataIndex: "problem_detail",
      width: 150,
      ellipsis: true,
    },
    {
      title: "应退数量",
      dataIndex: "return_quantity",
      width: 100,
    },
    {
      title: "已入库数量",
      dataIndex: "warehoused_quantity",
      width: 100,
    },
    {
      title: "买家ID",
      dataIndex: "buyer_id",
      width: 100,
      search: true,
    },
    {
      title: "电话",
      dataIndex: "contact_phone",
      width: 130,
      search: true,
    },
    {
      title: "退回快递",
      dataIndex: "return_express",
      width: 120,
      search: true,
    },
    {
      title: "退回单号",
      dataIndex: "return_express_no",
      width: 150,
      search: true,
    },
    {
      title: "申请人",
      dataIndex: "applicant",
      width: 100,
      search: true,
    },
    {
      title: "收件人",
      dataIndex: "recipient",
      width: 100,
    },
    {
      title: "收件地址",
      dataIndex: "shipping_address",
      width: 200,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "create_time",
      width: 160,
      search: true,
      formType: "daterange",
    },
    {
      title: "买家申请时间",
      dataIndex: "buyer_apply_time",
      width: 160,
      search: true,
      formType: "daterange",
    },
    {
      title: "完成时间",
      dataIndex: "complete_time",
      width: 160,
    },
    {
      title: "店铺",
      dataIndex: "shop_name",
      width: 120,
      search: true,
    },
    {
      title: "系统订单号",
      dataIndex: "system_order_no",
      width: 150,
      search: true,
    },
    {
      title: "线上订单号",
      dataIndex: "online_order_no",
      width: 150,
      search: true,
    },
    {
      title: "售后单号",
      dataIndex: "after_order_no",
      width: 150,
      search: true,
    },
    {
      title: "平台退款",
      dataIndex: "platform_refund",
      width: 100,
    },
    {
      title: "应退",
      dataIndex: "should_refund",
      width: 100,
    },
    {
      title: "服务费",
      dataIndex: "service_fee",
      width: 100,
    },
    {
      title: "邮费",
      dataIndex: "shipping_fee",
      width: 80,
    },
    {
      title: "退回仓库",
      dataIndex: "return_warehouse",
      width: 120,
    },
    {
      title: "原订单标记",
      dataIndex: "original_order_mark",
      width: 120,
    },
    {
      title: "售后标记",
      dataIndex: "after_order_mark",
      width: 120,
    },
    {
      title: "原订单备注",
      dataIndex: "original_order_remark",
      width: 150,
      ellipsis: true,
    },
    {
      title: "客户收款账户",
      dataIndex: "customer_account",
      width: 150,
    },
    {
      title: "业务员",
      dataIndex: "salesperson",
      width: 100,
      search: true,
    },
    {
      title: "赔付方",
      dataIndex: "compensation_party",
      width: 100,
    },
    {
      title: "赔付金额",
      dataIndex: "compensation_amount",
      width: 100,
    },
    {
      title: "原发货仓",
      dataIndex: "original_warehouse",
      width: 120,
    },
    {
      title: "原快递单号",
      dataIndex: "original_express_no",
      width: 150,
    },
    {
      title: "原快递",
      dataIndex: "original_express",
      width: 120,
    },
    {
      title: "发出订单号",
      dataIndex: "send_order_no",
      width: 150,
    },
    {
      title: "发出运单号",
      dataIndex: "send_express_no",
      width: 150,
    },
    {
      title: "售后阶段",
      dataIndex: "after_stage",
      width: 100,
      search: true,
      formType: 'select',
      dict: {
        data: [
          { label: '申请中', value: 'applying' },
          { label: '处理中', value: 'processing' },
          { label: '退款中', value: 'refunding' },
          { label: '已完成', value: 'completed' },
          { label: '已取消', value: 'cancelled' }
        ]
      },
    },
    {
      title: "付款时间",
      dataIndex: "payment_time",
      width: 160,
    },
    {
      title: "发货时间",
      dataIndex: "shipping_time",
      width: 160,
    },
    {
      title: "退款时间",
      dataIndex: "refund_time",
      width: 160,
    },
    {
      title: "线上售后单类型",
      dataIndex: "online_after_type",
      width: 140,
    },
    {
      title: "线上售后单状态",
      dataIndex: "online_after_status",
      width: 140,
    },
    {
      title: "入库单号",
      dataIndex: "warehousing_no",
      width: 150,
      search: true,
    },
  ]);
  
  // 页面加载时执行
  onMounted(() => {
    // 可以添加一些初始化逻辑
    console.log("售后订单查询页面已加载");
  });
  </script>