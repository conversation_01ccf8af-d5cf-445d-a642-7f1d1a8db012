<template>
  <a-drawer
    :visible="visible"
    :width="800"
    @cancel="close"
    unmount-on-close
    :footer="false"
    :mask-closable="true"
    title="售后订单详情"
  >
    <a-spin :loading="loading" style="width: 100%;">
      <a-tabs>
        <a-tab-pane key="returnGoods" title="退回商品明细">
          <a-table :data="returnGoodsList" :pagination="false" :bordered="true">
            <template #columns>
              <a-table-column title="退款状态" data-index="refundStatus">
                <template #cell="{ record }">
                  <a-tag :color="getStatusColor(record.refundStatus)">
                    {{ record.refundStatus }}
                  </a-tag>
                </template>
              </a-table-column>
              <a-table-column title="图示" data-index="image">
                <template #cell="{ record }">
                  <a-image
                    v-if="record.image"
                    :src="record.image"
                    :preview-visible="false"
                    width="60"
                    height="60"
                    @click="previewImage(record.image)"
                  />
                  <div v-else class="w-[60px] h-[60px] bg-gray-100 flex items-center justify-center text-gray-400">
                    无图片
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="订单号" data-index="orderNo" />
              <a-table-column title="系统商品信息" data-index="systemProductInfo" />
              <a-table-column title="线上宝贝信息" data-index="onlineProductInfo" />
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="exchangeGoods" title="换出商品明细">
          <a-table :data="exchangeGoodsList" :pagination="false" :bordered="true">
            <template #columns>
              <a-table-column title="图示" data-index="image">
                <template #cell="{ record }">
                  <a-image
                    v-if="record.image"
                    :src="record.image"
                    :preview-visible="false"
                    width="60"
                    height="60"
                    @click="previewImage(record.image)"
                  />
                  <div v-else class="w-[60px] h-[60px] bg-gray-100 flex items-center justify-center text-gray-400">
                    无图片
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="系统商品信息" data-index="systemProductInfo" />
              <a-table-column title="线上宝贝信息" data-index="onlineProductInfo" />
              <a-table-column title="单价" data-index="price">
                <template #cell="{ record }">
                  ¥{{ record.price.toFixed(2) }}
                </template>
              </a-table-column>
              <a-table-column title="数量" data-index="quantity" />
              <a-table-column title="金额" data-index="amount">
                <template #cell="{ record }">
                  ¥{{ record.amount.toFixed(2) }}
                </template>
              </a-table-column>
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="operationRecord" title="操作记录">
          <a-table :data="operationRecordList" :pagination="false" :bordered="true">
            <template #columns>
              <a-table-column title="处理状态" data-index="status">
                <template #cell="{ record }">
                  <a-tag :color="getOperationStatusColor(record.status)">
                    {{ record.status }}
                  </a-tag>
                </template>
              </a-table-column>
              <a-table-column title="处理人" data-index="operator" />
              <a-table-column title="处理时间" data-index="operateTime" />
              <a-table-column title="处理详情描述" data-index="description" />
            </template>
          </a-table>
        </a-tab-pane>
        
        <a-tab-pane key="vouchers" title="凭证图片">
          <div class="grid grid-cols-4 gap-4 p-4">
            <div v-if="voucherImages.length === 0" class="col-span-4 text-center text-gray-400 py-8">
              暂无凭证图片
            </div>
            <div 
              v-for="(image, index) in voucherImages" 
              :key="index"
              class="border rounded-md overflow-hidden cursor-pointer"
              @click="previewImage(image)"
            >
              <a-image
                :src="image"
                width="100%"
                height="120"
                :preview-visible="false"
                fit="cover"
              />
            </div>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="deliveryAddress" title="收货地址">
          <div>
            <div v-if="!deliveryAddress" class="w-full text-center text-gray-400 py-8">
              暂无收货地址信息
            </div>
            <a-descriptions v-else :column="2" bordered>
              <a-descriptions-item label="昵称" :span="1">
                {{ deliveryAddress.nickname || '暂无' }}
              </a-descriptions-item>
              <a-descriptions-item label="收货人" :span="1">
                {{ deliveryAddress.consignee || '暂无' }}
              </a-descriptions-item>
              <a-descriptions-item label="手机" :span="1">
                {{ deliveryAddress.mobile || '暂无' }}
              </a-descriptions-item>
              <a-descriptions-item label="电话" :span="1">
                {{ deliveryAddress.telephone || '暂无' }}
              </a-descriptions-item>
              <a-descriptions-item label="邮编" :span="1">
                {{ deliveryAddress.postalCode || '暂无' }}
              </a-descriptions-item>
              <a-descriptions-item label="省市区" :span="1">
                {{ getFullRegion() }}
              </a-descriptions-item>
              <a-descriptions-item label="街道" :span="1">
                {{ deliveryAddress.street || '暂无' }}
              </a-descriptions-item>
              <a-descriptions-item label="详细地址" :span="2">
                {{ deliveryAddress.detailAddress || '暂无' }}
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="serviceTickets" title="工单服务">
          <div >
            <div v-if="serviceTicketList.length !== 0" class="w-full text-center text-gray-400 py-8">
              暂无工单服务记录
            </div>
            <a-table v-else :data="serviceTicketList" :pagination="false" :bordered="true">
              <template #columns>
                <a-table-column title="工单任务编号" data-index="ticketNo" />
                <a-table-column title="任务类型" data-index="taskType">
                  <template #cell="{ record }">
                    <a-tag :color="getTaskTypeColor(record.taskType)">
                      {{ record.taskType }}
                    </a-tag>
                  </template>
                </a-table-column>
                <a-table-column title="任务状态" data-index="taskStatus">
                  <template #cell="{ record }">
                    <a-tag :color="getTaskStatusColor(record.taskStatus)">
                      {{ record.taskStatus }}
                    </a-tag>
                  </template>
                </a-table-column>
                <a-table-column title="指派给" data-index="assignee" />
                <a-table-column title="创建人" data-index="creator" />
                <a-table-column title="创建时间" data-index="createTime" />
              </template>
            </a-table>
          </div>
        </a-tab-pane>
      </a-tabs>
    </a-spin>
  </a-drawer>
  
  <a-modal
    v-model:visible="imagePreviewVisible"
    :footer="false"
    :mask-closable="true"
    @cancel="imagePreviewVisible = false"
  >
    <div class="text-center">
      <a-image
        v-if="previewImageUrl"
        :src="previewImageUrl"
        :preview-visible="false"
        style="max-width: 100%; max-height: 80vh;"
      />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

// 抽屉可见性
const visible = defineModel('visible', { default: false })

// 定义组件属性
const props = defineProps({
  afterOrderId: {
    type: [Number, String],
    default: null
  },
  orderDetail: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emits = defineEmits([
  'close', 
  'update:visible', 
  'confirm-audit', 
  'close-after-sale'
])

// 数据模型
const returnGoodsList = ref([])
const exchangeGoodsList = ref([])
const operationRecordList = ref([])
const voucherImages = ref([])
const afterOrderData = ref(null)
const deliveryAddress = ref(null)
const serviceTicketList = ref([])

// 图片预览
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

// 获取完整地址区域
const getFullRegion = () => {
  if (!deliveryAddress.value) return '暂无'
  
  const { province, city, district } = deliveryAddress.value
  return [province, city, district].filter(Boolean).join(' ') || '暂无'
}

// 获取任务类型颜色
const getTaskTypeColor = (type) => {
  const colorMap = {
    '售后退换货': 'blue',
    '质量问题': 'red',
    '维修服务': 'green',
    '订单变更': 'orange',
    '客户咨询': 'purple'
  }
  return colorMap[type] || 'gray'
}

// 获取任务状态颜色
const getTaskStatusColor = (status) => {
  const colorMap = {
    '待处理': 'orange',
    '处理中': 'blue',
    '已完成': 'green',
    '已关闭': 'gray',
    '已取消': 'red'
  }
  return colorMap[status] || 'gray'
}

// 获取售后订单详情数据
const fetchAfterOrderDetails = async () => {
  try {
    // 如果从props中获取到了orderDetail，则直接使用，否则调用API获取
    if (props.orderDetail && Object.keys(props.orderDetail).length > 0) {
      afterOrderData.value = props.orderDetail
    }
    // 这里应该调用实际的API获取数据
    // 假设API响应结构如下：
    const mockData = {
      returnGoods: [
        {
          refundStatus: '已退款',
          image: '/assets/placeholder.png',
          orderNo: 'OR20240515001',
          systemProductInfo: '商品A - 规格1',
          onlineProductInfo: '线上商品A - 颜色：红色 尺寸：XL'
        },
        {
          refundStatus: '待退款',
          image: '/assets/placeholder.png',
          orderNo: 'OR20240515002',
          systemProductInfo: '商品B - 规格2',
          onlineProductInfo: '线上商品B - 颜色：蓝色 尺寸：L'
        }
      ],
      exchangeGoods: [
        {
          image: '/assets/placeholder.png',
          systemProductInfo: '商品C - 规格3',
          onlineProductInfo: '线上商品C - 颜色：黑色 尺寸：M',
          price: 199.99,
          quantity: 2,
          amount: 399.98
        },
        {
          image: '/assets/placeholder.png',
          systemProductInfo: '商品D - 规格4',
          onlineProductInfo: '线上商品D - 颜色：白色 尺寸：S',
          price: 99.99,
          quantity: 1,
          amount: 99.99
        }
      ],
      operationRecords: [
        {
          status: '已处理',
          operator: '张三',
          operateTime: '2024-05-15 10:30:45',
          description: '确认退款申请并处理'
        },
        {
          status: '待处理',
          operator: '李四',
          operateTime: '2024-05-14 15:20:30',
          description: '收到退款申请'
        }
      ],
      voucherImages: [
        '/assets/placeholder.png',
        '/assets/placeholder.png',
        '/assets/placeholder.png'
      ],
      // 工单服务数据
      serviceTickets: [
        {
          ticketNo: 'GD202505160001',
          taskType: '售后退换货',
          taskStatus: '处理中',
          assignee: '张技术',
          creator: '系统自动',
          createTime: '2025-05-16 10:30:22'
        },
        {
          ticketNo: 'GD202505150023',
          taskType: '质量问题',
          taskStatus: '已完成',
          assignee: '王客服',
          creator: '小李',
          createTime: '2025-05-15 16:42:18'
        }
      ]
    }
    
    // 更新组件数据
    afterOrderData.value = mockData
    returnGoodsList.value = mockData.returnGoods
    exchangeGoodsList.value = mockData.exchangeGoods
    operationRecordList.value = mockData.operationRecords
    voucherImages.value = mockData.voucherImages
    serviceTicketList.value = mockData.serviceTickets
  } catch (error) {
    console.error('获取售后订单详情失败：', error)
  }
}

// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    '已退款': 'green',
    '待退款': 'orange',
    '已拒绝': 'red',
    '处理中': 'blue'
  }
  return statusMap[status] || 'gray'
}

// 获取操作状态颜色
const getOperationStatusColor = (status) => {
  const statusMap = {
    '已处理': 'green',
    '待处理': 'orange',
    '已拒绝': 'red',
    '处理中': 'blue'
  }
  return statusMap[status] || 'gray'
}

// 预览图片
const previewImage = (url) => {
  previewImageUrl.value = url
  imagePreviewVisible.value = true
}

// 关闭抽屉
const close = () => {
  visible.value = false
  emits('close')
  emits('update:visible', false)
}

// 判断是否可以同意换货
const canAgreeExchange = (record) => {
  if (!record) return false
  return (
    record.process_status === 'waiting_review' &&
    (record.after_type === 'exchange' || record.after_type === '')
  )
}

// 判断是否可以同意退货
const canAgreeReturn = (record) => {
  if (!record) return false
  return (
    record.process_status === 'waiting_review' &&
    (record.after_type === 'return' || record.after_type === '')
  )
}

// 判断是否可以同意退款
const canAgreeRefund = (record) => {
  if (!record) return false
  return (
    record.process_status === 'waiting_review' &&
    (record.after_type === 'refund' || record.after_type === '')
  )
}

// 判断是否可以同意补发
const canAgreeResend = (record) => {
  if (!record) return false
  return (
    record.process_status === 'waiting_review' &&
    (record.after_type === 'resend' || record.after_type === '')
  )
}

// 判断是否可以拒绝申请
const canReject = (record) => {
  if (!record) return false
  return record.process_status === 'waiting_review'
}

// 判断是否可以执行任何操作
const canPerformAnyAction = (record) => {
  return canAgreeExchange(record) || canAgreeReturn(record) || 
         canAgreeRefund(record) || canAgreeResend(record) || 
         canReject(record)
}


// 监听抽屉可见性变化
watch(visible, (val) => {
  if (val) {
    // 初始化收货地址数据模型
    deliveryAddress.value = {
      nickname: props.orderDetail?.deliveryNickname || '',
      consignee: props.orderDetail?.consignee || '',
      mobile: props.orderDetail?.mobile || '',
      telephone: props.orderDetail?.telephone || '',
      postalCode: props.orderDetail?.postalCode || '',
      province: props.orderDetail?.province || '',
      city: props.orderDetail?.city || '',
      district: props.orderDetail?.district || '',
      street: props.orderDetail?.street || '',
      detailAddress: props.orderDetail?.detailAddress || ''
    }
    
    // 初始化工单服务数据
    // 模拟数据，实际应从 API 获取
    serviceTicketList.value = props.orderDetail?.serviceTickets || []
    
    if (props.afterOrderId) {
      fetchAfterOrderDetails()
    } else if (props.orderDetail && Object.keys(props.orderDetail).length > 0) {
      afterOrderData.value = props.orderDetail
    }
  }
})

// 监听订单详情变化
watch(() => props.orderDetail, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    afterOrderData.value = newVal
  }
})

// 监听ID变化重新获取数据
watch(() => props.afterOrderId, (newVal) => {
  if (newVal) {
    fetchAfterOrderDetails()
  }
})
</script>

<style scoped>
.a-image {
  cursor: pointer;
}
</style>