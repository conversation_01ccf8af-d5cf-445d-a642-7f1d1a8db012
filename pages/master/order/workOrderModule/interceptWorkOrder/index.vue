<!--
 - Mine<PERSON>dmin 拦截工单列表页面
 - 包含拦截工单相关的所有字段和功能
 -->
 <template>
    <div class="ma-content-block p-4">
      <!-- CRUD 组件 -->
      <ma-crud :options="crud" :columns="columns" ref="crudRef">
        <!-- 优先级 -->
        <template #priority="{ record }">
          <a-tag :color="getPriorityColor(record.priority)">
            {{ getPriorityText(record.priority) }}
          </a-tag>
        </template>
  
        <!-- 提醒 -->
        <template #remind="{ record }">
          <a-tag color="orange" v-if="record.remind">有提醒</a-tag>
          <a-tag color="gray" v-else>无提醒</a-tag>
        </template>
  
        <!-- 标记 -->
        <template #mark="{ record }">
          <a-tag color="red" v-if="record.mark">已标记</a-tag>
          <a-tag color="gray" v-else>未标记</a-tag>
        </template>
  
        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
  
        <!-- 物流状态 -->
        <template #logistics_status="{ record }">
          <a-tag :color="getLogisticsStatusColor(record.logistics_status)">
            {{ getLogisticsStatusText(record.logistics_status) }}
          </a-tag>
        </template>
        
        <!-- 拦截状态 -->
        <template #intercept_status="{ record }">
          <a-tag :color="getInterceptStatusColor(record.intercept_status)">
            {{ getInterceptStatusText(record.intercept_status) }}
          </a-tag>
        </template>
        
        <!-- 退款状态 -->
        <template #refund_status="{ record }">
          <a-tag :color="getRefundStatusColor(record.refund_status)">
            {{ getRefundStatusText(record.refund_status) }}
          </a-tag>
        </template>
  
        <!-- 打款金额 -->
        <template #payment_amount="{ record }">
          <span class="font-medium" v-if="record.payment_amount !== undefined && record.payment_amount !== null">¥{{ record.payment_amount.toFixed(2) }}</span>
          <span class="text-gray-400" v-else>暂无金额</span>
        </template>
  
        <!-- 任务详情 -->
        <template #task_detail="{ record }">
          <a-button type="text" size="small" @click="viewTaskDetail(record)">
            <template #icon><icon-eye /></template>
            查看详情
          </a-button>
        </template>
  
        <!-- 图片/附件 -->
        <template #attachments="{ record }">
          <a-button
            type="text"
            size="small"
            @click="viewAttachments(record)"
            v-if="record.attachments && record.attachments.length > 0"
          >
            <template #icon><icon-file /></template>
            查看附件 ({{ record.attachments.length }})
          </a-button>
          <span v-else>无附件</span>
        </template>
  
        <!-- 商品图示 -->
        <template #product_image="{ record }">
          <a-image
            v-if="record.product_image"
            :src="record.product_image"
            :width="40"
            :height="40"
            fit="contain"
          />
          <span v-else>无图片</span>
        </template>
  
        <!-- 明细附件 -->
        <template #detail_attachments="{ record }">
          <a-button
            type="text"
            size="small"
            @click="viewDetailAttachments(record)"
            v-if="
              record.detail_attachments && record.detail_attachments.length > 0
            "
          >
            <template #icon><icon-file /></template>
            查看附件 ({{ record.detail_attachments.length }})
          </a-button>
          <span v-else>无附件</span>
        </template>
  
        <!-- 日期格式化 -->
        <template #deadline="{ record }">
          <div v-time="record.deadline"></div>
        </template>
        <template #updated_at="{ record }">
          <div v-time="record.updated_at"></div>
        </template>
        <template #created_at="{ record }">
          <div v-time="record.created_at"></div>
        </template>
        <template #completed_at="{ record }">
          <div v-time="record.completed_at"></div>
        </template>
  
        <!-- 表格工具栏 -->
        <template #tableAfterButtons>
          <a-button type="primary" @click="openAddModal">
            <template #icon><icon-plus /></template>
            新增工单
          </a-button>
        </template>
      </ma-crud>
  
      <!-- 新增工单组件 -->
      <WorkOrderAdd
        ref="addModalRef"
        :visible="addModalVisible"
        @update:visible="addModalVisible = $event"
        @success="handleAddSuccess"
      />
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, reactive, h } from "vue";
  import { Message, Modal } from "@arco-design/web-vue";
  import orderApi from "@/api/master/order";
  import systemApi from "@/api/master/system";
  
  // 引入工单新增组件
  import WorkOrderAdd from "../components/add.vue";
  
  definePageMeta({
    name: "interceptWorkOrder",
    path: "/master/order/workOrderModule/interceptWorkOrder",
  });
  
  // 引用CRUD组件
  const crudRef = ref();
  
  // 引用新增工单弹窗组件
  const addModalRef = ref();
  
  // 新增工单弹窗可见性状态
  const addModalVisible = ref(false);
  
  // 用户数据和供应商数据
  const userData = ref([]);
  const providerData = ref([]);
  
  // 获取用户列表
  const fetchUserList = async () => {
    try {
      const res = await systemApi.user.getList();
      if (res.code == "200") {
        userData.value = res.data.items || [];
      }
    } catch (error) {
      console.error("获取用户列表失败:", error);
    }
  };
  
  // 获取供应商列表
  const fetchProviderList = async () => {
    try {
      const res = await orderApi.provider.getList();
      if (res.code == "200") {
        providerData.value = res.data.items || [];
      }
    } catch (error) {
      console.error("获取供应商列表失败:", error);
    }
  };
  
  // 模拟数据
  const mockData = reactive({
    list: [
      {
        id: 10001,
        priority: 3,
        remind: 1,
        mark: 1,
        order_code: "WO2025051700001",
        task_title: "快递包裹拦截处理",
        task_type: 2,
        status: 1,
        assigned_to: 1,
        cc_to: [2, 3],
        logistics_status: 1,
        intercept_status: 2,
        refund_status: 1,
        intercept_settler: "物流公司",
        entry_status: 1,
        deadline: "2025-05-20 18:00:00",
        urge_count: 0,
        task_detail:
          "<p>供应商货款打款处理，请财务部门尽快审核并打款。</p>",
        attachments: [{ url: "/assets/demo/invoice.png", name: "发票.png" }],
        updated_at: "2025-05-17 10:30:25",
        created_at: "2025-05-17 10:30:25",
        completed_at: null,
        last_operator: "张三",
        creator: "李四",
        detail_attachments: [],
        outbound_serial: "SN20250517001",
        discounted_price: 1299,
      },
      {
        id: 10002,
        priority: 4,
        remind: 1,
        mark: 0,
        order_code: "WO2025051700002",
        task_title: "笔记本电脑无法开机",
        task_type: 2,
        status: 2,
        assigned_to: 3,
        cc_to: [1],
        deadline: "2025-05-18 12:00:00",
        urge_count: 2,
        task_detail:
          "<p>客户反馈笔记本电脑无法开机，充电指示灯不亮，怀疑是电源适配器故障。</p>",
        attachments: [],
        updated_at: "2025-05-17 11:20:15",
        created_at: "2025-05-16 09:15:30",
        completed_at: null,
        last_operator: "王五",
        creator: "李四",
        product_image: "/assets/demo/laptop.png",
        product_name: "14英寸轻薄笔记本",
        product_code: "LT-14-02",
        quantity: 1,
        spec_name: "银色 i5/16G/512G",
        product_remark: "2024年6月购买",
        detail_receivable: 0,
        provider_id: 2,
        product_type: 1,
        detail_attachments: [
          { url: "/assets/demo/receipt.png", name: "购买发票.png" },
        ],
        outbound_serial: "SN20250615002",
        discounted_price: 5699,
      },
      {
        id: 10003,
        priority: 2,
        remind: 0,
        mark: 1,
        order_code: "WO2025051700003",
        task_title: "软件激活问题咨询",
        task_type: 1,
        status: 3,
        assigned_to: 2,
        cc_to: [],
        deadline: "2025-05-17 17:00:00",
        urge_count: 0,
        task_detail: "<p>客户购买的设计软件无法正常激活，提示序列号错误。</p>",
        attachments: [{ url: "/assets/demo/error.png", name: "错误截图.png" }],
        updated_at: "2025-05-17 13:25:40",
        created_at: "2025-05-17 08:20:15",
        completed_at: "2025-05-17 13:25:40",
        last_operator: "赵六",
        creator: "张三",
        product_image: "/assets/demo/software.png",
        product_name: "设计大师软件",
        product_code: "SW-DM-2025",
        quantity: 1,
        spec_name: "标准版",
        product_remark: "永久授权",
        detail_receivable: 0,
        provider_id: 8,
        product_type: 2,
        detail_attachments: [],
        outbound_serial: "KEY-2025051700003",
        discounted_price: 1999,
      },
      {
        id: 10004,
        priority: 1,
        remind: 0,
        mark: 0,
        order_code: "WO2025051700004",
        task_title: "产品使用咨询",
        task_type: 1,
        status: 5,
        assigned_to: 4,
        cc_to: [5],
        deadline: "2025-05-20 10:00:00",
        urge_count: 0,
        task_detail: "<p>客户咨询新购买的智能家居产品如何连接WiFi网络。</p>",
        attachments: [],
        updated_at: "2025-05-17 12:10:05",
        created_at: "2025-05-17 11:30:00",
        completed_at: null,
        last_operator: "刘七",
        creator: "张三",
        product_image: "/assets/demo/smarthome.png",
        product_name: "智能音箱",
        product_code: "SH-SPK-01",
        quantity: 1,
        spec_name: "白色",
        product_remark: "",
        detail_receivable: 0,
        provider_id: 3,
        product_type: 1,
        detail_attachments: [],
        outbound_serial: "SN20250510005",
        discounted_price: 299,
      },
      {
        id: 10005,
        priority: 3,
        remind: 1,
        mark: 0,
        order_code: "WO2025051700005",
        task_title: "退换货申请处理",
        task_type: 4,
        status: 4,
        assigned_to: 1,
        cc_to: [2, 6],
        deadline: "2025-05-18 14:00:00",
        urge_count: 1,
        task_detail: "<p>客户申请退货，原因是产品不符合预期效果。</p>",
        attachments: [{ url: "/assets/demo/item.png", name: "产品照片.png" }],
        updated_at: "2025-05-17 14:30:20",
        created_at: "2025-05-17 09:45:10",
        completed_at: null,
        last_operator: "李四",
        creator: "王五",
        product_image: "/assets/demo/camera.png",
        product_name: "高清摄像头",
        product_code: "CAM-HD-01",
        quantity: 2,
        spec_name: "1080P标准版",
        product_remark: "客户反馈清晰度不足",
        detail_receivable: 499,
        provider_id: 4,
        product_type: 1,
        detail_attachments: [
          { url: "/assets/demo/package.png", name: "包装照片.png" },
        ],
        outbound_serial: "SN20250515009",
        discounted_price: 399,
      },
    ],
    total: {
      total: 5,
      currentPage: 1,
      totalPage: 1,
      pageSize: 10,
    },
  });
  
  // 页面初始化
  onMounted(() => {
    // 获取用户和供应商数据
    fetchUserList();
    fetchProviderList();
  });
  
  // 优先级相关方法
  const getPriorityText = (priority) => {
    const priorityMap = {
      1: "低",
      2: "中",
      3: "高",
      4: "紧急",
    };
    return priorityMap[priority] || "未知";
  };
  
  const getPriorityColor = (priority) => {
    const priorityColorMap = {
      1: "blue",
      2: "green",
      3: "orange",
      4: "red",
    };
    return priorityColorMap[priority] || "gray";
  };
  
  // 状态相关方法
  const getStatusText = (status) => {
    const statusMap = {
      1: "待处理",
      2: "处理中",
      3: "已完成",
      4: "已取消",
      5: "已关闭",
    };
    return statusMap[status] || "未知";
  };
  
  const getStatusColor = (status) => {
    const statusMap = {
      1: "orange",
      2: "blue",
      3: "green",
      4: "red",
      5: "gray",
    };
    return statusMap[status] || "gray";
  };
  
  // 物流状态相关方法
  const getLogisticsStatusText = (status) => {
    const statusMap = {
      1: "运输中",
      2: "已签收",
      3: "已退回",
      4: "已丢失",
      5: "配送异常",
    };
    return statusMap[status] || "未知状态";
  };
  
  const getLogisticsStatusColor = (status) => {
    const statusMap = {
      1: "orange",
      2: "green",
      3: "red",
      4: "gray",
      5: "blue",
    };
    return statusMap[status] || "gray";
  };
  
  // 拦截状态相关方法
  const getInterceptStatusText = (status) => {
    const statusMap = {
      1: "待拦截",
      2: "拦截中",
      3: "拦截成功",
      4: "拦截失败",
      5: "已取消拦截",
    };
    return statusMap[status] || "未知状态";
  };
  
  const getInterceptStatusColor = (status) => {
    const statusMap = {
      1: "orange",
      2: "blue",
      3: "green",
      4: "red",
      5: "gray",
    };
    return statusMap[status] || "gray";
  };
  
  // 退款状态相关方法
  const getRefundStatusText = (status) => {
    const statusMap = {
      1: "待退款",
      2: "退款中",
      3: "已退款",
      4: "退款失败",
      5: "已取消退款",
    };
    return statusMap[status] || "未知状态";
  };
  
  const getRefundStatusColor = (status) => {
    const statusMap = {
      1: "orange",
      2: "blue",
      3: "green",
      4: "red",
      5: "gray",
    };
    return statusMap[status] || "gray";
  };
  
  // 查看任务详情
  const viewTaskDetail = (record) => {
    Modal.info({
      title: "任务详情",
      content: () =>
        h("div", {
          innerHTML: record.task_detail || "暂无详情",
        }),
      okText: "关闭",
    });
  };
  
  // 查看附件
  const viewAttachments = (record) => {
    // 实现附件查看逻辑
    Message.info("查看附件功能开发中");
  };
  
  // 查看明细附件
  const viewDetailAttachments = (record) => {
    if (record.detail_attachments && record.detail_attachments.length > 0) {
      Modal.info({
        title: "明细附件",
        content: () =>
          h(
            "div",
            {},
            record.detail_attachments.map((item) =>
              h(
                "div",
                { class: "my-2" },
                h("a", { href: item.url, target: "_blank" }, item.name)
              )
            )
          ),
      });
    } else {
      Message.info("无明细附件");
    }
  };
  
  // 打开新增工单弹窗
  const openAddModal = () => {
    addModalVisible.value = true;
  };
  
  // 处理新增工单成功
  const handleAddSuccess = () => {
    Message.success("工单创建成功");
    // 刷新数据列表
    crudRef.value.refresh();
  };
  
  // CRUD配置
  const crud = reactive({
    // API配置 - 使用模拟数据
    api: () => {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            code: "200",
            message: "操作成功",
            data: {
              items: mockData.list,
              pageInfo: mockData.total,
            },
          });
        }, 300); // 模拟网络请求延迟
      });
    },
    showIndex: true,
    pageLayout: "fixed",
    rowSelection: { showCheckedAll: true },
    searchLabelWidth: "120px",
  });
  
  // 表格列配置
  const columns = reactive([
    {
      title: '优先级',
      dataIndex: 'priority',
      search: true,
      formType: 'select',
      commonRules: [{ required: true, message: '优先级必选' }],
      dict: {
        data: [
          { label: '低', value: 1 },
          { label: '中', value: 2 },
          { label: '高', value: 3 },
          { label: '紧急', value: 4 }
        ]
      }
    },
    {
      title: '提醒',
      dataIndex: 'remind',
      formType: 'radio',
      addDefaultValue: 0,
      dict: {
        data: [
          { label: '无提醒', value: 0 },
          { label: '有提醒', value: 1 }
        ]
      }
    },
    {
      title: '标记',
      dataIndex: 'mark',
      formType: 'radio',
      addDefaultValue: 0,
      dict: {
        data: [
          { label: '未标记', value: 0 },
          { label: '已标记', value: 1 }
        ]
      }
    },
    {
      title: '工单任务编号',
      dataIndex: 'order_code',
      search: true,
      commonRules: [{ required: true, message: '工单任务编号必填' }],
    },
    {
      title: '任务标题',
      dataIndex: 'task_title',
      search: true,
      commonRules: [{ required: true, message: '任务标题必填' }],
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      search: true,
      formType: 'select',
      commonRules: [{ required: true, message: '任务类型必选' }],
      dict: {
        data: [
          { label: '包裹拦截', value: 1 },
          { label: '退款处理', value: 2 },
          { label: '换货处理', value: 3 },
          { label: '异常处理', value: 4 },
          { label: '其他类型', value: 5 }
        ]
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      search: true,
      formType: 'select',
      addDefaultValue: 1,
      commonRules: [{ required: true, message: '状态必选' }],
      dict: {
        data: [
          { label: '待处理', value: 1 },
          { label: '处理中', value: 2 },
          { label: '已完成', value: 3 },
          { label: '已取消', value: 4 },
          { label: '已关闭', value: 5 }
        ]
      }
    },
    {
      title: '物流状态',
      dataIndex: 'logistics_status',
      search: true,
      formType: 'select',
      addDefaultValue: 1,
      commonRules: [{ required: true, message: '物流状态必选' }],
      dict: {
        data: [
          { label: '运输中', value: 1 },
          { label: '已签收', value: 2 },
          { label: '已退回', value: 3 },
          { label: '已丢失', value: 4 },
          { label: '配送异常', value: 5 }
        ]
      }
    },
    {
      title: '拦截状态',
      dataIndex: 'intercept_status',
      search: true,
      formType: 'select',
      addDefaultValue: 1,
      commonRules: [{ required: true, message: '拦截状态必选' }],
      dict: {
        data: [
          { label: '待拦截', value: 1 },
          { label: '拦截中', value: 2 },
          { label: '拦截成功', value: 3 },
          { label: '拦截失败', value: 4 },
          { label: '已取消拦截', value: 5 }
        ]
      }
    },
    {
      title: '退款状态',
      dataIndex: 'refund_status',
      search: true,
      formType: 'select',
      addDefaultValue: 1,
      commonRules: [{ required: true, message: '退款状态必选' }],
      dict: {
        data: [
          { label: '待退款', value: 1 },
          { label: '退款中', value: 2 },
          { label: '已退款', value: 3 },
          { label: '退款失败', value: 4 },
          { label: '已取消退款', value: 5 }
        ]
      }
    },
    {
      title: '拦截结算方',
      dataIndex: 'intercept_settler',
      search: true,
      formType: 'select',
      commonRules: [{ required: true, message: '拦截结算方必选' }],
      dict: {
        data: [
          { label: '物流公司', value: '物流公司' },
          { label: '商家', value: '商家' },
          { label: '第三方', value: '第三方' }
        ]
      }
    },
    {
      title: '入口状态',
      dataIndex: 'entry_status',
      search: true,
      formType: 'select',
      addDefaultValue: 1,
      commonRules: [{ required: true, message: '入口状态必选' }],
      dict: {
        data: [
          { label: '正常', value: 1 },
          { label: '异常', value: 2 },
          { label: '已关闭', value: 3 }
        ]
      }
    },
    {
      title: '指派给',
      dataIndex: 'assigned_to',
      search: true,
      formType: 'select',
      dict: { data: userData, props: { label: 'nickname', value: 'id' } }
    },
    {
      title: '抄送给',
      dataIndex: 'cc_to',
      formType: 'select',
      multiple: true,
      dict: { data: userData, props: { label: 'nickname', value: 'id' } }
    },
    {
      title: '截止时间',
      dataIndex: 'deadline',
      formType: 'date-picker',
      search: true,
      searchType: 'datetime-range',
    },
    {
      title: '催办次数',
      dataIndex: 'urge_count',
      addDisplay: false,
      editDisplay: false,
    },
    {
      title: '任务详情',
      dataIndex: 'task_detail',
      formType: 'textarea',
      hide: true,
    },
    {
      title: '图片/附件',
      dataIndex: 'attachments',
      formType: 'upload',
      hide: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      addDisplay: false,
      editDisplay: false,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      addDisplay: false,
      editDisplay: false,
    },
    {
      title: '完成时间',
      dataIndex: 'completed_at',
      addDisplay: false,
      editDisplay: false,
    },
    {
      title: '最近操作人',
      dataIndex: 'last_operator',
      addDisplay: false,
      editDisplay: false,
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      addDisplay: false,
      editDisplay: false,
    }
  ]);
  </script>
  
  <style scoped>
  .ma-content-block {
    background-color: var(--color-bg-2);
    border-radius: var(--border-radius-medium);
  }
  </style>
  