<template>
  <div class="select_goods">
    <!-- 商品选择表格 -->
    <a-table
      :columns="columns"
      :data="data"
      :pagination="false"
      :loading="loading"
      stripe
    >
      <template #empty>
        <div class="flex justify-center items-center py-4 text-gray-400">
          暂无数据
        </div>
      </template>
      <!-- 序号列的表头(添加按钮) -->
      <template #indexTitle>
        <a-button type="primary" size="mini" @click="addGoods">
          <template #icon>
            <icon-plus />
          </template>
        </a-button>
      </template>
      <!-- 序号列 -->
      <template #index="{ rowIndex }">
        {{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}
      </template>
      <!-- 商品图片列 -->
      <template #goodsImage="{ record }">
        <div
          class="w-12 h-12 flex items-center justify-center overflow-hidden rounded border"
        >
          <img
            v-if="record.imageUrl"
            :src="record.imageUrl"
            class="max-w-full max-h-full object-cover"
          />
          <div v-else class="text-gray-300 text-xs">无图片</div>
        </div>
      </template>
      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-button
          type="primary"
          status="danger"
          size="small"
          @click="deleteGoods(record)"
        >
          删除
        </a-button>
      </template>
    </a-table>

    <a-modal
      v-model:visible="visible"
      width="800px"
      @ok="handleOk"
      @cancel="handleCancel"
      title="选择商品"
    >
      <div>
        <a-form :model="pagination" @submit="handleSubmit" layout="inline">
          <a-form-item field="name" label="商品名称">
            <a-input v-model="pagination.name" />
          </a-form-item>
          <a-form-item>
            <a-button html-type="submit" type="primary">搜索</a-button>
          </a-form-item>
        </a-form>
        <a-table
          row-key="id"
          :columns="list_columns"
          :data="goodsList"
          :row-selection="rowSelection"
          v-model:selectedKeys="selectedKeys"
          :pagination="pagination"
          @page-change="pageChange"
          :scroll={y:200}
        >
          <template #goodsImage="{ record }">
            <a-image width="50" :src="record.mainImage" />
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import goodsApi from "@/api/master/goods";
const goodsSpuApi = goodsApi.spu;
// 表格列定义
const columns = [
  {
    title: "",
    titleSlotName: "indexTitle",
    slotName: "index",
    width: 50,
  },
  {
    title: "商品编码",
    dataIndex: "goodsCode",
  },
  {
    title: "商品图片",
    slotName: "goodsImage",
  },
  {
    title: "商品名称",
    dataIndex: "goodsName",
  },
  {
    title: "数量",
    dataIndex: "quantity",
  },
  {
    title: "操作",
    slotName: "operation",
    width: 100,
    fixed: "right",
  },
];

const selectedKeys = ref([]);
const rowSelection = reactive({
  type: "checkbox",
  showCheckedAll: true,
  onlyCurrent: false,
});
const list_columns = [
  {
    title: "商品编码",
    dataIndex: "id",
    width: 200,
  },
  {
    title: "商品图片",
    slotName: "goodsImage",
    width: 100,
  },
  {
    title: "商品名称",
    dataIndex: "name",
  },
];
// 表格数据
const data = ref([]);
// 加载状态
const loading = ref(false);

// 分页配置
let pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 100,
  showTotal: true,
  showPageSize: false,
});

// 页码变化处理
const pageChange = (page) => {
  pagination.current = page;
  getList();
};

// 获取商品列表数据
const fetchGoodsList = async () => {
  loading.value = true;
  try {
    // 模拟数据
    setTimeout(() => {
      data.value = [];
      loading.value = false;
    }, 500);
  } catch (error) {
    console.error("获取商品列表失败:", error);
    loading.value = false;
  }
};

// 删除商品
const deleteGoods = (goods) => {
  // 实现删除商品的逻辑
  console.log("删除商品:", goods);
  // 这里可以实现从表格中删除商品的逻辑
  data.value = data.value.filter((item) => item.id !== goods.id);
};
const visible = ref(false);
// 添加商品
const addGoods = () => {
  // TODO: 实现添加商品的逻辑
  console.log("点击了添加商品按钮");
  visible.value = true;
  // 这里可以打开商品添加弹窗或触发添加事件
  // emit('add');
};
let goodsList = ref([]);
const getList = async () => {
    let data = {...pagination}
    data.page = data.current
    delete data.current
    delete data.showTotal
    delete data.showPageSize
    delete data.total
  const res = await goodsSpuApi.getList(data);
  if (res.code == 200) {
    goodsList.value = res.data.items
    pagination.total = res.data.pageInfo.total;
  }
};
const handleSubmit = () =>{
    pagination.current = 1
    getList()
}
const handleOk = ()=>{
    console.log('selectedKeys',selectedKeys)
}
const handleCancel = ()=>{
    
}
// 初始化加载数据
fetchGoodsList();
getList();
</script>

<style scoped>
.select_goods {
  width: 100%;
}
</style>
