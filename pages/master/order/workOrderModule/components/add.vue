<!--
 - 工单新增表单弹窗组件
 - 包含工单相关的必要字段
 -->
<template>
  <a-modal
    :visible="visible"
    title="新增工单"
    :width="800"
    :mask-closable="false"
    @before-ok="handleSubmit"
    @cancel="handleCancel"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <a-form :model="form" ref="formRef"  :auto-label-width="true">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item field="task_type" label="任务类型"  :rules="[{ required: true, message: '请选择任务类型' }]">
            <a-select v-model="form.task_type" placeholder="请选择任务类型">
              <a-option v-for="item in taskTypeOptions" :key="item.value" :value="item.value">{{ item.label }}</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item field="priority" label="优先级"  :rules="[{ required: true, message: '请选择优先级' }]">
            <a-select v-model="form.priority" placeholder="请选择优先级">
              <a-option v-for="item in priorityOptions" :key="item.value" :value="item.value">{{ item.label }}</a-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item field="task_title" label="任务标题" :rules="[{ required: true, message: '请输入任务标题' }]">
            <a-input v-model="form.task_title" placeholder="请输入任务标题" />
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item field="assigned_to" label="指派给"  :rules="[{ required: true, message: '请选择指派人员' }]">
            <a-select v-model="form.assigned_to" placeholder="请选择指派人员">
              <a-option v-for="item in userOptions" :key="item.id" :value="item.id">{{ item.nickname }}</a-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item field="cc_to" label="抄送给" >
            <a-select v-model="form.cc_to" placeholder="请选择抄送人员" multiple>
              <a-option v-for="item in userOptions" :key="item.id" :value="item.id">{{ item.nickname }}</a-option>
            </a-select>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item field="deadline" label="截止时间" >
            <a-date-picker
              v-model="form.deadline"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择截止时间"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item field="task_detail" label="任务详情">
            <a-textarea
              v-model="form.task_detail"
              placeholder="请输入任务详情"
              :auto-size="{ minRows: 4, maxRows: 8 }"
            />
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item field="attachments" label="上传附件">
            <a-upload
              :file-list="fileList"
              :custom-request="customRequest"
              @change="handleUploadChange"
              multiple
              directory-drag
            >
              <template #upload-button>
                <a-button type="primary">
                  <template #icon><icon-upload /></template>
                  上传附件
                </a-button>
              </template>
            </a-upload>
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
    <a-divider />
    <!-- 订单相关信息 -->
    <a-form :model="form" ref="formRef" :auto-label-width="true" v-if="form.task_type">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item field="online_order_no" label="线上订单号" :rules="[{ required: true, message: '请输入线上订单号' }]">
            <a-input v-model="form.online_order_no" placeholder="请输入线上订单号" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item field="system_order_no" label="系统订单号" :rules="[{ required: true, message: '请输入系统订单号' }]">
            <a-input v-model="form.system_order_no" placeholder="请输入系统订单号" />
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16" v-if="showAfterSaleFields">
        <a-col :span="12">
          <a-form-item field="online_after_sale_no" label="线上售后单号" :rules="[{ required: true, message: '请输入线上售后单号' }]">
            <a-input v-model="form.online_after_sale_no" placeholder="请输入线上售后单号" />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item field="buyer_id" label="买家ID" :rules="[{ required: true, message: '请输入买家ID' }]">
            <a-input v-model="form.buyer_id" placeholder="请输入买家ID" />
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="24">
          <a-form-item field="order_goods" label="订单系统商品">
            <!-- <a-select v-model="form.order_goods" placeholder="请选择订单系统商品">
              <a-option v-for="item in goodsOptions" :key="item.id" :value="item.id">{{ item.name }}</a-option>
            </a-select> -->
            <selectGoods />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item field="receiver" label="收货人" :rules="[{ required: true, message: '请输入收货人' }]">
            <a-input v-model="form.receiver" placeholder="请输入收货人" />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, defineEmits, defineProps, defineExpose } from 'vue';
import { Message } from '@arco-design/web-vue';
import systemApi from '@/api/master/system';
import selectGoods from './selectGoods.vue'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'success', 'cancel']);

// 表单引用
const formRef = ref(null);

// 表单数据
const form = reactive({
  task_type: 2,
  priority: 2, // 默认中优先级
  task_title: '',
  assigned_to: undefined,
  cc_to: [],
  deadline: null,
  task_detail: '',
  attachments: [],
  // 订单相关信息
  online_order_no: '',
  system_order_no: '',
  online_after_sale_no: '',
  buyer_id: '',
  order_goods: undefined,
  receiver: ''
});

// 文件列表
const fileList = ref([]);

// 用户数据
const userOptions = ref([]);

// 商品选项数据
const goodsOptions = ref([{
  id: 1,
  name: '测试商品1'
}, {
  id: 2,
  name: '测试商品2'
}]);

// 根据任务类型判断是否显示售后相关字段
const showAfterSaleFields = computed(() => {
  return form.task_type !== 2; // 当任务类型不是维修(2)时显示售后字段
});

// 任务类型选项
const taskTypeOptions = [
  { label: '维修', value: 2 },
  { label: '退货', value: 4 }
];

// 优先级选项
const priorityOptions = [
  { label: '低', value: 1 },
  { label: '中', value: 2 },
  { label: '高', value: 3 },
  { label: '紧急', value: 4 }
];

// 获取用户列表
const fetchUserList = async () => {
  try {
    const res = await systemApi.user.getList();
    if (res.code == '200') {
      userOptions.value = res.data.items || [];
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

// 自定义上传请求
const customRequest = (options) => {
  const { file, onProgress, onSuccess, onError } = options;
  
  // 模拟上传进度
  const interval = setInterval(() => {
    const percent = Math.floor(Math.random() * 10) + 10;
    onProgress(percent);
  }, 300);
  
  // 模拟上传完成
  setTimeout(() => {
    clearInterval(interval);
    onProgress(100);
    onSuccess({
      url: URL.createObjectURL(file),
      name: file.name
    });
    
    // 实际项目中，这里应该调用上传API
    // 例如: orderApi.upload.uploadFile(file).then(res => {
    //   onSuccess(res.data);
    // }).catch(err => {
    //   onError(err);
    // });
  }, 2000);
};

// 处理上传变化
const handleUploadChange = (fileList) => {
  const uploadedFiles = fileList.filter(file => file.status === 'done');
  form.attachments = uploadedFiles.map(file => ({
    url: file.response.url,
    name: file.response.name
  }));
};

// 处理提交
const handleSubmit = (done) => {
  formRef.value.validate((errors) => {
    if (errors) {
      done(false);
      return;
    }
    
    // 模拟提交
    setTimeout(() => {
      Message.success('工单创建成功');
      emit('success', form);
      resetForm();
      done();
    }, 1000);
    
    // 实际项目中，这里应该调用创建工单API
    // 例如: orderApi.workOrder.create(form).then(res => {
    //   Message.success('工单创建成功');
    //   emit('success', res.data);
    //   resetForm();
    //   done();
    // }).catch(err => {
    //   Message.error('创建失败: ' + err.message);
    //   done(false);
    // });
  });
};

// 处理取消
const handleCancel = () => {
  resetForm();
  emit('cancel');
};

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields();
  fileList.value = [];
};

// 打开弹窗
const open = () => {
  emit('update:visible', true);
  fetchUserList();
};

// 关闭弹窗
const close = () => {
  emit('update:visible', false);
  resetForm();
};

// 暴露方法
defineExpose({
  open,
  close
});
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>