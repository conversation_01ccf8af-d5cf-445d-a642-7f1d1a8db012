<template>
  <a-drawer
    :width="700"
    :visible="visible"
    @cancel="handleCancel"
    :footer="true"
    unmountOnClose
    @ok="handleCancel"
    okText="关闭"
  >
    <template #title>
      <span class="text-lg font-bold">工单详情</span>
    </template>

    <div class="work-order-details" v-if="workOrderData">
      <!-- 标签页导航 -->
      <a-tabs position="top" default-active-key="1" size="large">
        <!-- 基本信息标签页 -->
        <a-tab-pane key="1" title="基本信息">
          <div class="base-info">
            <a-descriptions
              :column="2"
              bordered
              size="small"
            >
              <a-descriptions-item label="工单编号">{{
                workOrderData.order_code
              }}</a-descriptions-item>
              <a-descriptions-item label="任务标题">{{
                workOrderData.task_title
              }}</a-descriptions-item>
              <a-descriptions-item label="优先级">
                <a-tag :color="getPriorityColor(workOrderData.priority)">
                  {{ getPriorityText(workOrderData.priority) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-tag :color="getStatusColor(workOrderData.status)">
                  {{ getStatusText(workOrderData.status) }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="任务类型">{{
                getTaskTypeText(workOrderData.task_type)
              }}</a-descriptions-item>
              <a-descriptions-item label="指派给">{{
                workOrderData.assigned_to_name || "未指派"
              }}</a-descriptions-item>
              <a-descriptions-item label="截止时间">{{
                formatDateTime(workOrderData.deadline)
              }}</a-descriptions-item>
              <a-descriptions-item label="创建时间">{{
                formatDateTime(workOrderData.created_at)
              }}</a-descriptions-item>
              <a-descriptions-item label="创建人">{{
                workOrderData.creator
              }}</a-descriptions-item>
              <a-descriptions-item label="最近操作人">{{
                workOrderData.last_operator
              }}</a-descriptions-item>
              <a-descriptions-item label="催办次数">{{
                workOrderData.urge_count || 0
              }}</a-descriptions-item>
              <a-descriptions-item label="完成时间">{{
                formatDateTime(workOrderData.completed_at)
              }}</a-descriptions-item>
            </a-descriptions>
          </div>
        </a-tab-pane>

        <!-- 任务详情标签页 -->
        <a-tab-pane key="2" title="任务详情">
          <div class="task-detail">
            <div
              class="bg-gray-50 p-4 rounded"
              v-html="workOrderData.task_detail"
            ></div>
          </div>
        </a-tab-pane>

        <!-- 商品信息标签页 -->
        <a-tab-pane key="3" title="商品信息">
          <div class="product-info">
            <!-- 商品表格 -->
            <a-table
              :data="productTableData"
              :bordered="true"
              :pagination="false"
              size="small"
            >
              <!-- 商品编码 -->
              <template #columns>
                <a-table-column title="商品编码" data-index="product_code" />

                <!-- 商品图片 -->
                <a-table-column title="商品图片">
                  <template #cell="{ record }">
                    <a-image
                      v-if="record.product_image"
                      :src="record.product_image"
                      :width="60"
                      :height="60"
                      fit="contain"
                    />
                    <span v-else>无图片</span>
                  </template>
                </a-table-column>

                <!-- 商品名称 -->
                <a-table-column title="商品名称" data-index="product_name" />

                <!-- 数量 -->
                <a-table-column title="数量" data-index="quantity" />
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 附件信息标签页 -->
        <a-tab-pane key="4" title="附件信息">
          <div class="attachments">
            <div
              v-if="
                workOrderData.attachments &&
                workOrderData.attachments.length > 0
              "
            >
              <a-list size="small">
                <a-list-item
                  v-for="(item, index) in workOrderData.attachments"
                  :key="index"
                >
                  <a-list-item-meta>
                    <template #avatar>
                      <icon-file />
                    </template>
                    <template #title>
                      <a :href="item.url" target="_blank">{{ item.name }}</a>
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a-button type="text" size="small">
                      <template #icon><icon-download /></template>
                      下载
                    </a-button>
                  </template>
                </a-list-item>
              </a-list>
            </div>

            <div
              v-if="
                workOrderData.detail_attachments &&
                workOrderData.detail_attachments.length > 0
              "
              class="mt-6"
            >
              <div class="mb-4">
                <span class="text-lg font-medium">明细附件</span>
              </div>
              <a-list size="small">
                <a-list-item
                  v-for="(item, index) in workOrderData.detail_attachments"
                  :key="index"
                >
                  <a-list-item-meta>
                    <template #avatar>
                      <icon-file />
                    </template>
                    <template #title>
                      <a :href="item.url" target="_blank">{{ item.name }}</a>
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a-button type="text" size="small">
                      <template #icon><icon-download /></template>
                      下载
                    </a-button>
                  </template>
                </a-list-item>
              </a-list>
            </div>

            <a-empty
              description="暂无附件"
              v-if="
                (!workOrderData.attachments ||
                  workOrderData.attachments.length === 0) &&
                (!workOrderData.detail_attachments ||
                  workOrderData.detail_attachments.length === 0)
              "
            />
          </div>
        </a-tab-pane>

        <!-- 操作记录标签页 -->
        <a-tab-pane key="5" title="操作记录">
          <div class="operation-records">
            <a-table
              :data="operationRecords"
              :bordered="true"
              :pagination="false"
              size="small"
            >
              <template #columns>
                <a-table-column title="处理状态" data-index="status">
                  <template #cell="{ record }">
                    <a-tag :color="getStatusColor(record.status)">
                      {{ getStatusText(record.status) }}
                    </a-tag>
                  </template>
                </a-table-column>
                <a-table-column title="处理人" data-index="operator" />
                <a-table-column title="处理时间" data-index="operate_time">
                  <template #cell="{ record }">
                    {{ formatDateTime(record.operate_time) }}
                  </template>
                </a-table-column>
                <a-table-column title="处理详情描述" data-index="description" />
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 操作标签页 -->
        <a-tab-pane key="6" title="操作">
          <div class="operations">
            <div>
              <a-space>
                <a-button
                  type="primary"
                  v-if="workOrderData.status !== 3"
                  @click="handleUpdateStatus(3)"
                >
                  <template #icon><icon-check-circle /></template>
                  标记为已完成
                </a-button>
                <a-button
                  type="secondary"
                  v-if="workOrderData.status === 1"
                  @click="handleUpdateStatus(2)"
                >
                  <template #icon><icon-play-circle /></template>
                  开始处理
                </a-button>
                <a-button
                  type="outline"
                  status="danger"
                  v-if="workOrderData.status !== 4"
                  @click="handleUpdateStatus(4)"
                >
                  <template #icon><icon-close-circle /></template>
                  取消工单
                </a-button>
              </a-space>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
    <div v-else class="flex justify-center items-center h-full">
      <a-empty description="暂无数据" />
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed } from "vue";
import { Message } from "@arco-design/web-vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  workOrder: {
    type: Object,
    default: () => null,
  },
});

const emit = defineEmits(["update:visible"]);

// 工单数据
const workOrderData = ref(null);

// 监听工单数据变化
watch(
  () => props.workOrder,
  (newVal) => {
    if (newVal) {
      workOrderData.value = newVal;
    }
  },
  { immediate: true }
);

// 商品表格数据
const productTableData = computed(() => {
  if (!workOrderData.value) return [];

  // 将工单数据转换为表格所需格式
  return [
    {
      product_code: workOrderData.value.product_code,
      product_image: workOrderData.value.product_image,
      product_name: workOrderData.value.product_name,
      quantity: workOrderData.value.quantity,
    },
  ];
});

// 关闭抽屉
const handleCancel = () => {
  emit("update:visible", false);
};

// 更新工单状态
const handleUpdateStatus = (status) => {
  // 实际项目中应该调用API更新状态
  Message.success(`工单状态已更新为: ${getStatusText(status)}`);
  if (workOrderData.value) {
    workOrderData.value.status = status;
  }
};

// 优先级文本
const getPriorityText = (priority) => {
  const map = {
    1: "低",
    2: "中",
    3: "高",
    4: "紧急",
  };
  return map[priority] || "未知";
};

// 优先级颜色
const getPriorityColor = (priority) => {
  const map = {
    1: "blue",
    2: "green",
    3: "orange",
    4: "red",
  };
  return map[priority] || "gray";
};

// 状态文本
const getStatusText = (status) => {
  const map = {
    1: "待处理",
    2: "处理中",
    3: "已完成",
    4: "已取消",
  };
  return map[status] || "未知";
};

// 状态颜色
const getStatusColor = (status) => {
  const map = {
    1: "blue",
    2: "orange",
    3: "green",
    4: "gray",
  };
  return map[status] || "gray";
};

// 任务类型文本
const getTaskTypeText = (type) => {
  const map = {
    1: "常规任务",
    2: "售后任务",
    3: "紧急任务",
    4: "其他",
  };
  return map[type] || "未知";
};

// 商品类型文本
const getProductTypeText = (type) => {
  const map = {
    1: "正常商品",
    2: "赠品",
    3: "样品",
    4: "其他",
  };
  return map[type] || "未知";
};

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return "暂无";
  return dateTime;
};

const handleClick = () => {
  emit("update:visible", true);
};
</script>

<style scoped>
.work-order-details {
  padding: 0 16px;
}
</style>
