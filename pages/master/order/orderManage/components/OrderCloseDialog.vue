<template>
  <a-modal
    v-model:visible="visible"
    :title="'关闭订单'"
    :mask-closable="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
    width="500px"
  >
    <div class="close-order-content">
      <a-alert type="warning" show-icon style="margin-bottom: 16px;">
        <template #icon>
          <icon-exclamation-circle />
        </template>
        关闭订单后，订单状态将变为"已关闭"，可以进行退款操作。此操作不可撤销，请谨慎操作。
      </a-alert>
      
      <a-form :model="formData" ref="formRef" :label-col-props="{ span: 5 }" :wrapper-col-props="{ span: 18 }">
        <a-form-item field="closeReason" label="关闭原因" :rules="[{ required: true, message: '请输入关闭原因' }]">
          <a-select
            v-model="formData.closeReason"
            placeholder="请选择关闭原因"
            allow-clear
          >
            <a-option value="用户申请退款">用户申请退款</a-option>
            <a-option value="商品缺货">商品缺货</a-option>
            <a-option value="物流问题">物流问题</a-option>
            <a-option value="商品质量问题">商品质量问题</a-option>
            <a-option value="价格调整">价格调整</a-option>
            <a-option value="系统错误">系统错误</a-option>
            <a-option value="其他原因">其他原因</a-option>
          </a-select>
        </a-form-item>
        
        <a-form-item field="closeNote" label="备注说明">
          <a-textarea
            v-model="formData.closeNote"
            placeholder="请输入详细说明（可选）"
            :max-length="200"
            show-word-limit
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconExclamationCircle } from '@arco-design/web-vue/es/icon';
import orderApi from '@/api/master/order';

const visible = ref(false);
const formRef = ref(null);

// 表单数据
const formData = reactive({
  orderId: '',
  closeReason: '',
  closeNote: ''
});

// 回调函数
let refreshCallback = null;

// 打开弹窗
const open = (orderId, callback) => {
  formData.orderId = orderId;
  formData.closeReason = '';
  formData.closeNote = '';
  refreshCallback = callback;
  visible.value = true;
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
  formRef.value?.resetFields();
};

// 确认前验证
const handleBeforeOk = (done) => {
  formRef.value?.validate((errors) => {
    if (errors) {
      done(false);
      return;
    }
    
    // 构建关闭订单数据
    const closeData = {
      cancelReason: `${formData.closeReason}${formData.closeNote ? ': ' + formData.closeNote : ''}`
    };
    
    // 调用关闭订单接口（使用现有的取消订单接口）
    orderApi.cancelOrder(formData.orderId, closeData)
      .then(res => {
        if (res && res.code === 200) {
          Message.success('订单关闭成功');
          // 刷新订单列表
          if (refreshCallback) {
            refreshCallback();
          }
          done(); // 关闭弹窗
        } else {
          Message.error((res && res.message) || '订单关闭失败');
          done(false); // 阻止关闭弹窗
        }
      })
      .catch(err => {
        console.error('关闭订单失败', err);
        Message.error('关闭订单失败: ' + (err.message || String(err)));
        done(false); // 阻止关闭弹窗
      });
  });
};

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.close-order-content {
  padding: 8px 0;
}

.arco-form-item {
  margin-bottom: 20px;
}

.arco-alert {
  border-radius: 6px;
}
</style>
