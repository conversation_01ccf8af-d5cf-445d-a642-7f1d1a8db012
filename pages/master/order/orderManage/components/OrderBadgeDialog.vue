<template>
  <a-modal
    v-model:visible="visible"
    :title="'添加订单标注'"
    :mask-closable="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
    width="1200px"
  >
    <a-form :model="formData" ref="formRef" :wrapper-col-props="{ span: 24 }">
      <a-form-item field="remark">
        <ma-wang-editor
          v-model="formData.remark"
          style="width: 100%"
          :height="200"
        >
        </ma-wang-editor>
      </a-form-item>
    </a-form>

    <a-divider />

    <div>
      <h4 style="margin-bottom: 10px">标注记录</h4>
      <a-collapse
        :default-active-key="badgeData.length > 0 ? [badgeData[0].id] : []"
        accordion
      >
        <a-collapse-item
          v-for="item in badgeData"
          :key="item.id"
          :header="item.createdByName"
        >
          <template #extra>
            <p>
              <strong>标注时间：</strong> <span v-time="item.createdAt"></span>
            </p>
          </template>
          <p><strong>标注内容：</strong></p>
          <div v-html="item.content" style="margin-top: 10px;margin-bottom: 10px;"></div>
        </a-collapse-item>
      </a-collapse>
      <a-empty
        v-if="!badgeData || badgeData.length === 0"
        description="暂无标注记录"
        style="margin-top: 20px"
      />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import orderApi from "@/api/master/order";
import MaWangEditor from "@/components/base/ma-wangEditor/index.vue";
const visible = ref(false);
const formRef = ref(null);

// 标注记录数据 (示例，后续需要从API获取)
const badgeData = ref([]);

// 表单数据
const formData = reactive({
  orderId: "",
  remark: "",
});

// 回调函数
let refreshCallback = null;

// 打开弹窗
const open = async (orderId, callback) => {
  // 标记为 async
  try {
    const res = await orderApi.getOrderRemarkList(orderId);
    if (res && res.code === 200) {
      badgeData.value = res.data || []; // 确保res.data存在，否则设置为空数组
    } else {
      badgeData.value = [];
      Message.error((res && res.message) || '获取标注列表失败');
    }
  } catch (err) {
    badgeData.value = [];
    Message.error('获取标注列表失败: ' + (err.message || String(err)));
  }
  formData.orderId = orderId;
  formData.remark = "";
  refreshCallback = callback;
  visible.value = true;
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
  formRef.value?.resetFields();
};

// 确认前验证
const handleBeforeOk = (done) => {
  if(formData.remark.trim() === '') {
    Message.error('标注内容不能为空');
    return;
  }
  // 直接构建标注数据
  const badgeData = {
    remark: formData.remark || "",
  };

  // 调用添加标注接口
  orderApi
    .addOrderRemark(formData.orderId, badgeData)
    .then((res) => {
      if (res && res.code === 200) {
        Message.success("标注添加成功");
        // 刷新订单列表
        if (refreshCallback) {
          refreshCallback();
        }
        done(); // 关闭弹窗
      } else {
        Message.error((res && res.message) || "标注添加失败");
        done(false); // 阻止关闭弹窗
      }
    })
    .catch((err) => {
      console.error("标注添加失败", err);
      Message.error("标注添加失败: " + (err.message || String(err)));
      done(false); // 阻止关闭弹窗
    });
};

// 暴露方法给父组件
defineExpose({
  open,
});
</script>

<style scoped>
.arco-form-item {
  margin-bottom: 20px;
}
</style>
