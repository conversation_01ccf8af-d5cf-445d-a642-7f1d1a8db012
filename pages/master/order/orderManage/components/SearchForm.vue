<template>
  <div class="search-form-container">
    <!-- 搜索表单 -->
    <div class="search-form">
      <a-form :model="localForm" layout="horizontal" auto-label-width>
        <div class="search-grid" :class="{ 'advanced-search': showAdvancedSearch }">
          <div v-for="(item, index) in formItems" :key="index">
            <a-form-item 
              :field="item.field" 
              :label="item.label" 
              class="search-item"
              :style="item.isTimeRange ? { gridColumn: 'span 2' } : {}"
            >
              <!-- 输入框类型 -->
              <a-input 
                v-if="item.type === 'input'"
                v-model="localForm[item.field]" 
                :placeholder="item.placeholder || '请输入'" 
              />
              
              <!-- 选择框类型 -->
              <a-select 
                v-else-if="item.type === 'select'"
                v-model="localForm[item.field]" 
                :placeholder="item.placeholder || '请选择'"
                :style="{ width: '100%' }"
              >
                <a-option 
                  v-for="option in item.options" 
                  :key="option.value" 
                  :value="option.value"
                >
                  {{ option.label }}
                </a-option>
              </a-select>
              
              <!-- 时间范围类型 -->
              <div v-else-if="item.isTimeRange" class="time-range-controls">
                <a-date-picker 
                  v-model="localForm[item.startDateField]" 
                  class="date-picker"
                />
                <span class="time-separator">至</span>
                <a-date-picker 
                  v-model="localForm[item.endDateField]" 
                  class="date-picker"
                />
              </div>
            </a-form-item>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="search-actions">
          <div class="left-actions">
            <a-button type="primary" @click="handleSearch">搜索</a-button>
            <a-button @click="resetSearch">重置</a-button>
            <slot name="left-actions"></slot>
          </div>
          <div class="right-actions">
            <slot name="right-actions"></slot>
          </div>
        </div>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed, onMounted } from 'vue';

const props = defineProps({
  formItems: {
    type: Array,
    required: true
  },
  formData: {
    type: Object,
    required: true
  },
  showAdvancedSearch: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:formData', 'search', 'reset']);

// 创建本地表单状态的副本
const localForm = reactive({});

// 初始化本地表单
onMounted(() => {
  // 复制props.formData到localForm
  if (props.formData) {
    Object.keys(props.formData).forEach(key => {
      localForm[key] = props.formData[key];
    });
  }
  
  // 确保所有formItems中定义的字段都存在于localForm中
  props.formItems.forEach(item => {
    if (item.field && !(item.field in localForm)) {
      if (item.type === 'select' && item.options && item.options.length > 0) {
        localForm[item.field] = item.defaultValue || item.options[0].value;
      } else if (item.type === 'input') {
        localForm[item.field] = '';
      }
    }
    
    // 处理时间范围字段
    if (item.isTimeRange) {
      if (item.startDateField && !(item.startDateField in localForm)) {
        localForm[item.startDateField] = null;
      }
      if (item.endDateField && !(item.endDateField in localForm)) {
        localForm[item.endDateField] = null;
      }
      if (item.timePresetField && !(item.timePresetField in localForm)) {
        localForm[item.timePresetField] = item.defaultPreset || '7days';
      }
    }
  });
});

// 监听本地表单变化，同步到父组件
watch(localForm, (newVal) => {
  console.log('本地表单变化:', newVal);
  emit('update:formData', {...newVal});
}, { deep: true });

// 监听父组件的formData变化，同步到本地表单
watch(() => props.formData, (newVal) => {
  if (newVal) {
    console.log('父组件formData变化:', newVal);
    Object.keys(newVal).forEach(key => {
      // 避免循环更新
      if (localForm[key] !== newVal[key]) {
        localForm[key] = newVal[key];
      }
    });
  }
}, { deep: true, immediate: true });


// 搜索处理
const handleSearch = () => {
  emit('search', localForm);
};

// 重置搜索
const resetSearch = () => {
  const resetData = {};
  console.log('执行重置搜索');
  // 将所有搜索参数都清空
  props.formItems.forEach(item => {
    if (item.isTimeRange) {
      // 时间范围类型字段，重置为null
      resetData[item.timePresetField] = 'recent6m'; // 保留时间范围为近6个月
      resetData[item.startDateField] = null;
      resetData[item.endDateField] = null;
    } else if (item.type === 'select') {
      // 下拉选择类型字段，重置为'全部'
      resetData[item.field] = '全部';
    } else {
      // 其他类型字段，重置为空字符串
      resetData[item.field] = '';
    }
  });
  
  // 更新本地表单
  Object.keys(resetData).forEach(key => {
    localForm[key] = resetData[key];
  });
  
  // 发出重置事件
  emit('update:formData', resetData);
  emit('reset');
};

defineExpose({
  resetSearch
});
</script>

<style scoped lang="less">
.search-form-container {
  margin-bottom: 15px;
  width: 100%;
}

.search-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 16px;
  
  &.advanced-search {
    grid-template-columns: repeat(4, 1fr);
  }
}

.search-item {
  margin-bottom: 0;
}

.time-range-controls {
  display: flex;
  align-items: center;
  gap: 21px;
  width: 100%;
  
  .date-picker {
    flex: 1;
  }
}

.time-separator {
  margin: 0 4px;
}

.search-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .left-actions {
    display: flex;
    gap: 8px;
  }
  
  .right-actions {
    display: flex;
    align-items: center;
  }
}
</style>
