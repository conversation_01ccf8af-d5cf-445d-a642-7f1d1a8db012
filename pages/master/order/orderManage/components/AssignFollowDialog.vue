<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
-->
<template>
  <a-modal
    :visible="visible"
    title="分配跟进人员"
    @cancel="handleCancel"
    @before-ok="handleSubmit"
    @close="visible = false"
    :mask-closable="false"
    :esc-to-close="false"
  >
    <a-form :model="form" ref="formRef">
      <a-form-item field="userIds" label="跟进人员" :rules="[{ required: true, message: '请选择跟进人员' }]">
        <a-select
         @search="handleSearch"
          v-model="form.userIds"
          placeholder="请选择跟进人员"
          multiple
          allow-clear
          :loading="loading"
          :filter-option="true"
          :options="userOptions"
        />
      </a-form-item>
      <!-- <a-form-item field="remark" label="备注">
        <a-textarea
          v-model="form.remark"
          placeholder="请输入备注信息"
          :max-length="200"
          show-word-limit
        />
      </a-form-item> -->
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import systemApi from '@/api/master/system';
import orderApi from '@/api/master/order';

const props = defineProps({
  orderId: {
    type: [String, Number],
    default: ''
  }
});

const emit = defineEmits(['success', 'cancel']);

// 弹窗显示状态
const visible = ref(false);
// 表单引用
const formRef = ref(null);
// 加载状态
const loading = ref(false);
// 用户选项
const userOptions = ref([]);
// 表单数据
const form = reactive({
  userIds: [],
});

// 打开弹窗
const open = (orderId, followers) => {
  visible.value = true;

  console.log('打开分配跟进人员弹窗', followers);
  // 如果传入了订单ID，则保存到表单中
  if (orderId) {
    form.id = orderId;
  } else if (props.orderId) {
    form.id = props.orderId;
  }
  // 加载用户列表
  loadUsers(orderId);
  if(followers && followers.length > 0){
    form.userIds = followers.map(user => user.id);
  }
};

// 加载用户列表
const loadUsers = async (orderId,value) => {
  loading.value = true;
  try {
    const res = await systemApi.user.getList({page:1,pageSize:100,username:value});
    if (res.code == 200) {
      userOptions.value = res.data.items.map(user => ({
        label: user.username,
        value: user.id
      }));
    } else {
      console.error('获取用户列表失败', res);
    }
  } catch (err) {
    console.error('获取用户列表失败', err);
  } finally {
    loading.value = false;
  }
};

const handleSearch = (value) => {
  loadUsers(form.id,value);
};

// 取消
const handleCancel = () => {
  resetForm();
  emit('cancel');
};

// 提交
const handleSubmit = async () => {

  try {
    console.log('提交分配跟进人员数据', form);
    
    // 模拟API调用
    const res = await orderApi.setFollowers(form.id,{followerIds:form.userIds});
    if (res.code === 200) {
      Message.success('分配跟进人员成功');
      resetForm();
      emit('success');
      return true;
    } else {
      Message.error(res.message || '分配跟进人员失败');
      return false;
    }
    return true;
  } catch (err) {
    Message.error('分配跟进人员失败');
    return false;
  }
};

// 重置表单
const resetForm = () => {
  form.userIds = [];
  form.remark = '';
  visible.value = false;
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

// 暴露方法
defineExpose({
  open
});
</script>

<style scoped>
.arco-select {
  width: 100%;
}
</style>
