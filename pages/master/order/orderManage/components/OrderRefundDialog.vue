<template>
  <a-modal
    v-model:visible="visible"
    :title="'订单退款'"
    :mask-closable="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
    width="600px"
    :confirm-loading="loading"
  >
    <div class="refund-content">
      <a-alert type="info" show-icon style="margin-bottom: 16px;">
        <template #icon>
          <icon-info-circle />
        </template>
        退款将原路返回到用户的支付账户，一般1-3个工作日到账。请确认退款原因和金额。
      </a-alert>
      
      <!-- 订单信息 -->
      <div class="order-info" v-if="orderInfo">
        <h4>订单信息</h4>
        <a-descriptions :column="2" size="small" style="margin-bottom: 16px;">
          <a-descriptions-item label="订单编号">{{ orderInfo.id }}</a-descriptions-item>
          <a-descriptions-item label="订单金额">¥{{ orderInfo.totalAmount }}</a-descriptions-item>
          <a-descriptions-item label="支付状态">{{ orderInfo.paymentStatusText }}</a-descriptions-item>
          <a-descriptions-item label="已支付金额">¥{{ orderInfo.paidAmount || orderInfo.totalAmount }}</a-descriptions-item>
        </a-descriptions>
      </div>
      
      <a-form :model="formData" ref="formRef" :label-col-props="{ span: 6 }" :wrapper-col-props="{ span: 18 }">
        <a-form-item field="refundType" label="退款类型" :rules="[{ required: true, message: '请选择退款类型' }]">
          <a-radio-group v-model="formData.refundType" @change="handleRefundTypeChange">
            <a-radio value="full">全额退款</a-radio>
            <a-radio value="partial">部分退款</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item 
          v-if="formData.refundType === 'partial'" 
          field="refundAmount" 
          label="退款金额" 
          :rules="[
            { required: true, message: '请输入退款金额' },
            { validator: validateRefundAmount }
          ]"
        >
          <a-input-number
            v-model="formData.refundAmount"
            placeholder="请输入退款金额"
            :precision="2"
            :min="0.01"
            :max="maxRefundAmount"
            style="width: 100%"
          >
            <template #prefix>¥</template>
          </a-input-number>
          <div class="amount-tip">最大可退款金额：¥{{ maxRefundAmount }}</div>
        </a-form-item>
        
        <a-form-item field="refundReason" label="退款原因" :rules="[{ required: true, message: '请选择退款原因' }]">
          <a-select
            v-model="formData.refundReason"
            placeholder="请选择退款原因"
            allow-clear
          >
            <a-option value="用户申请退款">用户申请退款</a-option>
            <a-option value="商品质量问题">商品质量问题</a-option>
            <a-option value="商品缺货">商品缺货</a-option>
            <a-option value="物流问题">物流问题</a-option>
            <a-option value="价格调整">价格调整</a-option>
            <a-option value="重复下单">重复下单</a-option>
            <a-option value="系统错误">系统错误</a-option>
            <a-option value="其他原因">其他原因</a-option>
          </a-select>
        </a-form-item>
        
        <a-form-item field="refundNote" label="退款说明">
          <a-textarea
            v-model="formData.refundNote"
            placeholder="请输入详细的退款说明（可选）"
            :max-length="200"
            show-word-limit
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconInfoCircle } from '@arco-design/web-vue/es/icon';
import orderApi from '@/api/master/order';

const visible = ref(false);
const formRef = ref(null);
const loading = ref(false);
const orderInfo = ref(null);

// 表单数据
const formData = reactive({
  orderId: '',
  refundType: 'full',
  refundAmount: 0,
  refundReason: '',
  refundNote: ''
});

// 回调函数
let refreshCallback = null;

// 计算最大可退款金额
const maxRefundAmount = computed(() => {
  if (!orderInfo.value) return 0;
  return parseFloat(orderInfo.value.paidAmount || orderInfo.value.totalAmount || 0);
});

// 打开弹窗
const open = async (orderId, paymentStatus, callback) => {
  formData.orderId = orderId;
  formData.refundType = 'full';
  formData.refundAmount = 0;
  formData.refundReason = '';
  formData.refundNote = '';
  refreshCallback = callback || (() => {});
  
  // 获取订单详情
  try {
    const res = await orderApi.getOrderDetail(orderId);
    if (res && res.code === 200 && res.data) {
      orderInfo.value = res.data;
      // 设置默认退款金额为订单总金额
      formData.refundAmount = maxRefundAmount.value;
    }
  } catch (error) {
    console.error('获取订单详情失败:', error);
    Message.error('获取订单信息失败');
    return;
  }
  
  visible.value = true;
};

// 退款类型变化处理
const handleRefundTypeChange = (value) => {
  if (value === 'full') {
    formData.refundAmount = maxRefundAmount.value;
  } else {
    formData.refundAmount = 0;
  }
};

// 验证退款金额
const validateRefundAmount = (value, callback) => {
  if (formData.refundType === 'partial') {
    if (!value || value <= 0) {
      callback('退款金额必须大于0');
      return;
    }
    if (value > maxRefundAmount.value) {
      callback(`退款金额不能超过最大可退款金额 ¥${maxRefundAmount.value}`);
      return;
    }
  }
  callback();
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
  formRef.value?.resetFields();
  orderInfo.value = null;
};

// 确认前验证
const handleBeforeOk = (done) => {
  formRef.value?.validate((errors) => {
    if (errors) {
      done(false);
      return;
    }
    
    // 确定退款金额
    const finalRefundAmount = formData.refundType === 'full' 
      ? maxRefundAmount.value 
      : formData.refundAmount;
    
    // 构建退款数据
    const refundData = {
      refundReason: `${formData.refundReason}${formData.refundNote ? ': ' + formData.refundNote : ''}`,
      refundAmount: finalRefundAmount,
      refundType: formData.refundType
    };
    
    loading.value = true;
    
    // 调用退款接口
    orderApi.refundOrder(formData.orderId, refundData)
      .then(res => {
        if (res && res.code === 200) {
          Message.success('退款申请成功，退款将在1-3个工作日内到账');
          // 刷新订单列表
          if (refreshCallback) {
            refreshCallback();
          }
          done(); // 关闭弹窗
        } else {
          Message.error((res && res.message) || '退款申请失败');
          done(false); // 阻止关闭弹窗
        }
      })
      .catch(err => {
        console.error('退款申请失败', err);
        Message.error('退款申请失败: ' + (err.message || String(err)));
        done(false); // 阻止关闭弹窗
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.refund-content {
  padding: 8px 0;
}

.order-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.order-info h4 {
  margin: 0 0 12px 0;
  color: #1d2129;
  font-weight: 500;
}

.amount-tip {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.arco-form-item {
  margin-bottom: 20px;
}

.arco-alert {
  border-radius: 6px;
}
</style>
