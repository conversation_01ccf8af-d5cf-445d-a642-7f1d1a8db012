<template>
  <a-modal
    v-model:visible="visible"
    title="上传发票"
    width="700px"
    :mask-closable="false"
    @ok="handleUpload"
    @cancel="handleCancel"
    :confirm-loading="uploading"
    ok-text="确认上传"
    cancel-text="取消"
  >
    <div class="invoice-upload-container">
      <!-- 上传说明 -->
      <div class="upload-header">
        <div class="upload-title">
          <icon-file-image class="title-icon" />
          <span>选择发票文件</span>
        </div>
        <div class="upload-description">
          支持 JPG、PNG、PDF 格式，单个文件不超过10MB，最多可上传10个文件
        </div>
      </div>

      <!-- 上传区域 -->
      <div class="upload-area">
        <ma-upload
          v-model="uploadedFiles"
          type="file"
          :multiple="true"
          :limit="10"
          accept=".jpg,.jpeg,.png,.pdf"
          :draggable="true"
          :show-list="false"
          :size="10 * 1024 * 1024"
          tip="将文件拖拽到此处，或点击选择文件。支持格式：JPG, PNG, PDF，单个文件不超过10MB"
          return-type="url"
          :request-data="{
            dir: 'invoices',
            module: 'order',
            bizType: 'invoice',
            bizId: currentOrderId,
            isPublic: 'true'
          }"
        />
      </div>

      <!-- 文件预览列表 -->
      <div v-if="getFileCount() > 0" class="file-preview-section">
        <div class="file-preview-list">
          <div
            v-for="(fileUrl, index) in getFileUrls()"
            :key="index"
            class="file-preview-item"
          >
            <!-- 图片预览 -->
            <div v-if="isImageFile(fileUrl)" class="preview-image">
              <img :src="fileUrl" alt="发票图片" />
            </div>
            <!-- PDF文件预览 -->
            <div v-else-if="isPdfFile(fileUrl)" class="preview-pdf">
              <icon-file class="pdf-icon" />
              <div class="pdf-label">PDF发票</div>
            </div>
            <!-- 其他文件类型预览 -->
            <div v-else class="preview-other">
              <icon-file class="file-icon" />
              <div class="file-label">发票文件</div>
            </div>

            <!-- 文件名显示已隐藏，避免乱码问题 -->

            <!-- 删除按钮 -->
            <div class="file-remove-btn" @click="removeFile(index)">
              <icon-delete />
            </div>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  IconFileImage,
  IconCheckCircle,
  IconFile,
  IconDelete
} from '@arco-design/web-vue/es/icon'
import MaUpload from '~/components/base/ma-upload/index.vue'
import invoiceApi from '@/api/master/invoice'

// 弹窗显示状态
const visible = ref(false)
const uploading = ref(false)

// 上传的文件列表
const uploadedFiles = ref([])

// 当前订单ID
const currentOrderId = ref('')
const successCallback = ref(null)

// 打开弹窗
const open = (orderId, callback) => {
  currentOrderId.value = orderId
  successCallback.value = callback
  visible.value = true
  uploadedFiles.value = []
}

// 关闭弹窗
const handleCancel = () => {
  visible.value = false
  uploadedFiles.value = [] // ma-upload支持数组和字符串，重置为空数组
  currentOrderId.value = ''
  successCallback.value = null
}

// 这些函数已在下面重新定义，移除重复代码

// ma-upload组件会自动处理文件上传，无需自定义上传逻辑

// 获取文件数量
const getFileCount = () => {
  if (!uploadedFiles.value) return 0
  if (Array.isArray(uploadedFiles.value)) {
    return uploadedFiles.value.length
  }
  if (typeof uploadedFiles.value === 'string') {
    return 1
  }
  return 0
}

// 获取文件URL数组
const getFileUrls = () => {
  if (!uploadedFiles.value) return []
  if (Array.isArray(uploadedFiles.value)) {
    return uploadedFiles.value
  }
  if (typeof uploadedFiles.value === 'string') {
    return [uploadedFiles.value]
  }
  return []
}

// 移除文件
const removeFile = (index) => {
  const fileUrls = getFileUrls()
  if (fileUrls.length > index) {
    fileUrls.splice(index, 1)
    uploadedFiles.value = fileUrls.length === 0 ? [] : (fileUrls.length === 1 ? fileUrls[0] : fileUrls)
  }
}

// 获取文件URL
const getFileUrl = (file) => {
  if (typeof file === 'string') {
    return file
  }
  return file?.url || file?.fileUrl || ''
}

// 获取文件名 - 返回固定文本避免乱码
const getFileName = (file) => {
  // 直接返回固定文本，避免文件名乱码问题
  return '发票文件'
}

// 判断是否为图片文件
const isImageFile = (file) => {
  const url = typeof file === 'string' ? file : getFileUrl(file)
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(url)
}

// 判断是否为PDF文件
const isPdfFile = (file) => {
  const url = typeof file === 'string' ? file : getFileUrl(file)
  return /\.pdf$/i.test(url)
}

// 确认发票上传
const handleUpload = async () => {
  if (!uploadedFiles.value || uploadedFiles.value.length === 0) {
    Message.error('请选择要上传的发票文件')
    return
  }

  uploading.value = true

  try {
    // 处理ma-upload返回的数据格式
    let fileUrls = []

    if (Array.isArray(uploadedFiles.value)) {
      // 如果是数组，直接使用
      fileUrls = uploadedFiles.value.filter(url => url && typeof url === 'string')
    } else if (typeof uploadedFiles.value === 'string') {
      // 如果是单个字符串，转换为数组
      fileUrls = [uploadedFiles.value]
    }

    if (fileUrls.length === 0) {
      Message.error('没有有效的文件URL')
      return
    }

    console.log('准备上传的文件URLs:', fileUrls)

    // 调用发票记录API
    const result = await invoiceApi.uploadInvoiceFiles(currentOrderId.value, fileUrls)

    if (result.code === 200) {
      Message.success(result.message || '发票上传成功')

      // 调用成功回调
      if (successCallback.value) {
        successCallback.value()
      }

      // 关闭弹窗
      handleCancel()
    } else {
      Message.error(result.message || '发票上传失败')
    }
  } catch (error) {
    console.error('上传发票失败:', error)
    Message.error('发票上传失败，请稍后重试')
  } finally {
    uploading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped lang="less">
.invoice-upload-container {
  padding: 8px 0;

  .upload-header {
    margin-bottom: 24px;
    text-align: center;

    .upload-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 8px;

      .title-icon {
        font-size: 20px;
        color: #165dff;
      }
    }

    .upload-description {
      font-size: 14px;
      color: #86909c;
      line-height: 1.5;
    }
  }

  .upload-area {
    margin-bottom: 24px;
    min-height: 120px;
  }

  .upload-dragger {
    border: 2px dashed #d9d9d9;
    border-radius: 8px;
    padding: 40px 24px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      border-color: #40a9ff;
      background: #f0f8ff;
    }
  }

  .upload-icon {
    font-size: 48px;
    color: #d9d9d9;
    margin-bottom: 16px;
  }

  .upload-text {
    color: #666;
  }

  .upload-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
  }

  .upload-hint {
    font-size: 14px;
    color: #666;
    margin-bottom: 4px;
  }

  .upload-desc {
    font-size: 12px;
    color: #999;
  }

  .file-preview-section {
    .file-preview-list {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      max-height: 300px;
      overflow-y: auto;
      padding: 4px;
    }

    .file-preview-item {
      position: relative;
      width: 120px;
      height: 120px;
      border: 1px solid #e5e6eb;
      border-radius: 6px;
      overflow: hidden;
      background: #fff;
      transition: all 0.2s;
      cursor: pointer;

      &:hover {
        border-color: #165dff;
        box-shadow: 0 2px 8px rgba(22, 93, 255, 0.15);

        .file-remove-btn {
          opacity: 1;
        }
      }

      .preview-image {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;

        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }
      }

      .preview-pdf {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        color: #165dff;

        .pdf-icon {
          font-size: 32px;
          margin-bottom: 8px;
        }

        .pdf-label {
          font-size: 12px;
          font-weight: 500;
        }
      }

      .preview-other {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #f7f8fa;
        color: #86909c;

        .file-icon {
          font-size: 32px;
          margin-bottom: 8px;
        }

        .file-label {
          font-size: 12px;
          font-weight: 500;
        }
      }

      /* 文件名样式已移除，避免乱码显示 */

      .file-remove-btn {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 20px;
        height: 20px;
        background: rgba(245, 63, 63, 0.9);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.2s;

        &:hover {
          background: #f53f3f;
        }
      }
    }
  }


}

// 自定义滚动条样式
.file-preview-list::-webkit-scrollbar {
  width: 6px;
}

.file-preview-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.file-preview-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.file-preview-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
