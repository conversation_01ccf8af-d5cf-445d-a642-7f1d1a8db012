<template>
  <a-modal
    v-model:visible="visible"
    title="查看发票信息"
    width="900px"
    :footer="false"
    @cancel="handleClose"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large">加载中...</a-spin>
    </div>

    <div v-else-if="invoiceData" class="invoice-view-content">
      <!-- 发票抬头信息 -->
      <div v-if="invoiceData.application" class="section">
        <h3>发票抬头信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="发票金额">
            ¥{{ invoiceData.application.applicationAmount }}
          </a-descriptions-item>
          <a-descriptions-item label="发票类型">
            {{ invoiceData.application.applicationType === 1 ? '个人发票' : '企业发票' }}
          </a-descriptions-item>
          <a-descriptions-item v-if="invoiceData.application.snapshotHeaderName" label="发票抬头">
            {{ invoiceData.application.snapshotHeaderName }}
          </a-descriptions-item>
          <a-descriptions-item v-if="invoiceData.application.snapshotTaxNumber" label="纳税人识别号">
            {{ invoiceData.application.snapshotTaxNumber }}
          </a-descriptions-item>
          <a-descriptions-item v-if="invoiceData.application.snapshotCompanyAddress" label="公司地址">
            {{ invoiceData.application.snapshotCompanyAddress }}
          </a-descriptions-item>
          <a-descriptions-item v-if="invoiceData.application.snapshotCompanyPhone" label="公司电话">
            {{ invoiceData.application.snapshotCompanyPhone }}
          </a-descriptions-item>
          <a-descriptions-item v-if="invoiceData.application.snapshotBankName" label="开户银行">
            {{ invoiceData.application.snapshotBankName }}
          </a-descriptions-item>
          <a-descriptions-item v-if="invoiceData.application.snapshotBankAccount" label="银行账户">
            {{ invoiceData.application.snapshotBankAccount }}
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 发票文件列表 -->
      <div v-if="invoiceData.invoices && invoiceData.invoices.length > 0" class="invoice-files">
        <h3>发票文件 ({{ invoiceData.invoices.length }}个)</h3>
        <div class="files-list">
          <div v-for="(invoice, index) in invoiceData.invoices" :key="invoice.id || index" class="file-item">
            <!-- 文件图标 -->
            <div class="file-icon">
              <icon-file-pdf v-if="isPdfFile(invoice.fileUrl)" />
              <icon-image v-else-if="isImageFile(invoice.fileUrl)" />
              <icon-file v-else />
            </div>

            <!-- 文件信息 -->
            <div class="file-info">
              <div class="file-name" :title="getFileName(invoice.fileUrl)">
                {{ getFileName(invoice.fileUrl) }}
              </div>
              <div class="file-meta">
                <span class="file-type">{{ getFileTypeFromUrl(invoice.fileUrl) }}</span>
                <span class="upload-time">{{ formatTime(invoice.createdAt) }}</span>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="file-actions">
              <a-button type="text" size="small" @click="viewInvoice(invoice.fileUrl)">
                查看发票
              </a-button>
              <a-button type="text" size="small" @click="copyFileLink(invoice.fileUrl)">
                复制链接
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-else class="error-container">
      <a-result status="error" title="获取发票信息失败">
        <template #subtitle>
          {{ errorMessage || '无法获取发票信息，请稍后重试' }}
        </template>
        <template #extra>
          <a-button type="primary" @click="loadInvoiceData">重新加载</a-button>
        </template>
      </a-result>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  IconEye,
  IconCopy,
  IconFilePdf,
  IconFile,
  IconImage
} from '@arco-design/web-vue/es/icon'
import invoiceApi from '@/api/master/invoice'

const visible = ref(false)
const loading = ref(false)
const invoiceData = ref(null)
const errorMessage = ref('')
const currentOrderId = ref('')

// 打开弹窗
const open = async (orderId) => {
  currentOrderId.value = orderId
  visible.value = true
  await loadInvoiceData()
}

// 加载发票数据
const loadInvoiceData = async () => {
  if (!currentOrderId.value) return

  loading.value = true
  errorMessage.value = ''
  invoiceData.value = null

  try {
    const result = await invoiceApi.getOrderInvoices(currentOrderId.value)
    if (result.code === 200 && result.data) {
      invoiceData.value = result.data
    } else {
      errorMessage.value = result.message || '获取发票信息失败'
    }
  } catch (error) {
    console.error('获取发票信息失败:', error)
    errorMessage.value = '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  invoiceData.value = null
  errorMessage.value = ''
  currentOrderId.value = ''
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(Number(timestamp))
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-')
}

// 判断是否为PDF文件
const isPdfFile = (url) => {
  return url && url.toLowerCase().includes('.pdf')
}

// 判断是否为图片文件
const isImageFile = (url) => {
  if (!url) return false
  const extension = url.split('.').pop().toLowerCase()
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)
}

// 获取文件名 - 返回固定文本避免乱码
const getFileName = (url) => {
  // 直接返回固定文本，避免文件名乱码问题
  return '发票文件'
}

// 从URL获取文件类型
const getFileTypeFromUrl = (url) => {
  if (!url) return '其他文件'

  const extension = url.split('.').pop().toLowerCase()
  const typeMap = {
    'pdf': 'PDF文档',
    'jpg': '图片',
    'jpeg': '图片',
    'png': '图片',
    'gif': '图片',
    'bmp': '图片',
    'webp': '图片'
  }
  return typeMap[extension] || '其他文件'
}

// 查看发票 - 在新页面打开
const viewInvoice = (url) => {
  if (!url) {
    Message.error('发票文件链接无效')
    return
  }

  try {
    // 在新标签页打开发票文件
    const newWindow = window.open(url, '_blank')

    // 检查是否成功打开新窗口
    if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
      Message.error('浏览器阻止了弹出窗口，请允许弹出窗口后重试')
      return
    }

    Message.success('已在新标签页打开发票')
  } catch (error) {
    console.error('打开发票失败:', error)
    Message.error('打开发票失败，请稍后重试')
  }
}

// 复制文件链接
const copyFileLink = async (url) => {
  if (url) {
    try {
      await navigator.clipboard.writeText(url)
      Message.success('链接已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      Message.error('复制失败，请手动复制')
    }
  }
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style scoped>
.loading-container {
  text-align: center;
  padding: 60px 0;
}

.invoice-view-content {
  max-height: 75vh;
  overflow-y: auto;
}

.section {
  margin-bottom: 24px;
}

.section h3 {
  margin-bottom: 16px;
  color: #1d2129;
  font-size: 16px;
  font-weight: 600;
}

.invoice-files {
  margin-bottom: 24px;
}

.files-list {
  margin-top: 16px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  overflow: hidden;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f2f3f5;
  background: #fff;
  transition: background-color 0.2s ease;
}

.file-item:last-child {
  border-bottom: none;
}

.file-item:hover {
  background: #f7f8fa;
}

.file-icon {
  font-size: 20px;
  color: #86909c;
  margin-right: 12px;
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 12px;
  color: #86909c;
}

.file-type {
  color: #4e5969;
}

.upload-time {
  color: #86909c;
}

.file-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.error-container {
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .file-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .file-actions {
    width: 100%;
    justify-content: flex-start;
  }
}
</style>
