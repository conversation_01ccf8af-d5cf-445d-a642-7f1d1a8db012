<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block lg:p-4 p-2">
   

    <!-- 订单基本信息卡片 -->
    <a-card class="mb-4 order-card" title="订单信息">
      <template #extra>
        <a-space>
          <a-button
            v-if="canRefund"
            type="primary"
            status="danger"
            size="small"
            :loading="refundLoading"
            @click="showRefundModal"
          >
            申请退款
          </a-button>
          <a-button type="outline" size="small" @click="goBack">
            <template #icon>
              <IconArrowLeft />
            </template>
            返回列表
          </a-button>
        </a-space>
      </template>

      <a-row :gutter="[16, 16]">
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">订单编号：</span>
            <span class="info-value">{{ orderDetail.orderNumber }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">订单来源：</span>
            <span class="info-value">{{ orderDetail.orderSource }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">下单时间：</span>
            <span class="info-value">{{ orderDetail.createTime }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">支付时间：</span>
            <span class="info-value">{{ orderDetail.payTime }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">订单类型：</span>
            <span class="info-value">{{ orderDetail.orderChannel }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">订单状态：</span>
            <span class="info-value">
              <a-tag :color="getStatusColor(orderDetail.orderStatus)">{{ orderDetail.orderStatus }}</a-tag>
            </span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">支付状态：</span>
            <span class="info-value">
              <a-tag :color="getStatusColor(orderDetail.payStatus)">{{ orderDetail.payStatus }}</a-tag>
            </span>
          </div>
        </a-col>
        <a-col :span="24">
          <div class="info-item">
            <span class="info-label">订单备注：</span>
            <span class="info-value">{{ orderDetail.orderNote || '无' }}</span>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 商品信息卡片 -->
    <a-card class="mb-4 order-card" title="商品信息">
      <a-table 
        :columns="goodsColumns" 
        :data="goodsData" 
        :pagination="false" 
        :bordered="{ cell: true }"
        class="goods-table"
      >
        <template #name="{ record }">
          <div class="flex items-start">
            <a-image :src="record.imageUrl" width="60" height="60" class="mr-3 product-image" />
            <div class="product-info">
              <div class="product-name">{{ record.name }}</div>
              <div class="product-specs">{{ record.specs }}</div>
            </div>
          </div>
        </template>
        <template #vendor="{ record }">
          <span>{{ record.vendor }}</span>
        </template>
        <template #price="{ record }">
          <span class="text-price">￥{{ record.price }}</span>
        </template>
        <template #quantity="{ record }">
          <span>{{ record.quantity }}</span>
        </template>
      </a-table>

      <!-- 价格汇总 -->
      <div class="price-summary">
        <div class="price-row">
          <span>订单金额：</span>
          <span class="price-value">￥ {{ orderDetail.totalProductAmount }}</span>
        </div>
        <div class="price-row">
          <span>运费金额：</span>
          <span class="price-value">￥ {{ orderDetail.shippingAmount }}</span>
        </div>
        <div class="price-row">
          <span>优惠金额：</span>
          <span class="price-value">￥ {{ orderDetail.discountAmount }}</span>
        </div>
        <div class="price-row">
          <span>支付金额：</span>
          <span class="price-value">￥ {{ orderDetail.payAmount }}</span>
        </div>
        <div class="price-row payment-method-row">
          <span>支付方式：</span>
          <span class="payment-method">微信支付</span>
        </div>
      </div>
    </a-card>

    <!-- 收货信息卡片 -->
    <a-card class="mb-4 order-card" title="收货信息">
      <a-row :gutter="[16, 16]">
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">收货人：</span>
            <span class="info-value">{{ orderDetail.receiver }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">联系电话：</span>
            <span class="info-value">{{ orderDetail.contactPhone }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">收货地址：</span>
            <span class="info-value">{{ orderDetail.shippingAddress }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">物流公司：</span>
            <span class="info-value">{{ orderDetail.logisticsCompany || '暂无' }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">快递单号：</span>
            <span class="info-value">{{ orderDetail.trackingNumber || '暂无' }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">物流详情：</span>
            <span class="info-value">
              <a-button v-if="orderDetail.trackingNumber" type="text" status="primary" size="small">查看物流</a-button>
              <span v-else class="text-gray-400">暂无物流信息</span>
            </span>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 订单日志卡片 -->
    <a-card class="order-card" title="订单日志">
      <a-timeline class="order-timeline">
        <a-timeline-item v-for="(log, index) in orderDetail.logs" :key="index" :dot-color="getLogColor(log.type)">
          <div class="timeline-content">
            <div class="timeline-title">{{ log.title }}</div>
            <div class="timeline-info">
              <span class="timeline-operator">操作人：{{ log.operator }}</span>
              <span class="timeline-time">（{{ log.time }}）</span>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-card>

    <!-- 退款确认模态框 -->
    <a-modal
      v-model:visible="refundModalVisible"
      title="申请退款"
      :ok-loading="refundLoading"
      @ok="handleRefund"
      @cancel="cancelRefund"
    >
      <div class="refund-modal-content">
        <a-alert
          type="warning"
          message="退款提醒"
          description="退款申请提交后将直接发起微信退款，退款金额将原路返回到用户的支付账户，请谨慎操作。"
          show-icon
          class="mb-4"
        />

        <a-descriptions :column="1" bordered>
          <a-descriptions-item label="订单编号">{{ orderDetail.orderNumber }}</a-descriptions-item>
          <a-descriptions-item label="支付金额">￥{{ orderDetail.payAmount }}</a-descriptions-item>
          <a-descriptions-item label="退款金额">￥{{ orderDetail.payAmount }}</a-descriptions-item>
        </a-descriptions>

        <div class="mt-4">
          <a-form :model="refundForm" layout="vertical">
            <a-form-item label="退款原因" field="refundReason">
              <a-textarea
                v-model="refundForm.refundReason"
                placeholder="请输入退款原因（可选）"
                :max-length="200"
                show-word-limit
                :auto-size="{ minRows: 3, maxRows: 5 }"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Message, Modal } from '@arco-design/web-vue';
import { IconArrowLeft } from '@arco-design/web-vue/es/icon';
import orderApi from '@/api/master/order';

definePageMeta({
  name: "master-order-detail",
  path: "/master/order/orderManage/detail/:id",
})

const route = useRoute();
const router = useRouter();
const orderId = ref('');
const loading = ref(false);

// 退款相关状态
const refundModalVisible = ref(false);
const refundLoading = ref(false);
const refundForm = reactive({
  refundReason: ''
});

// 订单详情数据
const orderDetail = reactive({
  id: '',
  orderNumber: '',
  orderSource: '',
  orderChannel: '',
  orderStatus: '',
  payStatus: '',
  createTime: '',
  payTime: '',
  orderNote: '',
  totalProductAmount: '0.00',
  shippingAmount: '0.00',
  discountAmount: '0.00',
  payAmount: '0.00',
  payMethod: '',
  receiver: '',
  contactPhone: '',
  shippingAddress: '',
  logisticsCompany: '',
  trackingNumber: '',
  logs: []
});

// 商品数据
const goodsData = ref([]);

// 计算是否可以退款
const canRefund = computed(() => {
  // 只有已付款待发货的订单可以退款
  return orderDetail.orderStatus === '待发货' && orderDetail.payStatus === '已支付';
});

// 商品表格列定义
const goodsColumns = [
  {
    title: '商品名称',
    slotName: 'name',
    dataIndex: 'name',
  },
  {
    title: '商品规格',
    slotName: 'specs',
    dataIndex: 'specs',
  },
  {
    title: '商家',
    slotName: 'vendor',
    dataIndex: 'vendor',
  },
 
  {
    title: '单价',
    slotName: 'price',
    dataIndex: 'price',
    align: 'center'
  },
  {
    title: '购买数量',
    slotName: 'quantity',
    dataIndex: 'quantity',
    align: 'center'
  }
];

// 获取日志颜色
const getLogColor = (type) => {
  const colorMap = {
    'create': 'blue',
    'pay': 'green',
    'ship': 'orange',
    'complete': 'green',
    'cancel': 'red',
    'refund': 'red'
  };
  return colorMap[type] || 'gray';
};

// 获取订单状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    '待付款': 'orange',
    '待发货': 'blue',
    '已发货': 'purple',
    '已完成': 'green',
    '已取消': 'red',
    '已关闭': 'gray',
    '退款中': 'magenta',
    '已退款': 'red'
  };
  return statusMap[status] || '';
};

// 返回列表
const goBack = () => {
  router.push('/master/order/orderManage');
};

// 显示退款模态框
const showRefundModal = () => {
  refundForm.refundReason = '';
  refundModalVisible.value = true;
};

// 取消退款
const cancelRefund = () => {
  refundModalVisible.value = false;
  refundForm.refundReason = '';
};

// 处理退款
const handleRefund = async () => {
  try {
    refundLoading.value = true;

    const res = await orderApi.refundOrder(orderId.value, {
      refundReason: refundForm.refundReason || '管理员申请退款'
    });

    if (res && res.code === 200) {
      Message.success('退款申请成功，退款将在1-3个工作日内到账');
      refundModalVisible.value = false;

      // 重新获取订单详情以更新状态
      await getOrderDetail(orderId.value);
    }
  } catch (error) {
    console.error('退款申请失败:', error);
    Message.error(error.message || '退款申请失败，请稍后重试');
  } finally {
    refundLoading.value = false;
  }
};

// 获取订单详情
const getOrderDetail = async (id) => {
  loading.value = true;
  try {
    // 调用API获取订单详情
    const res = await orderApi.getOrderDetail(id);
    if (res && res.code === 200) {
      const data = res.data;
      
      // 处理订单状态
      const orderStatusMap = {
        0: '待付款',
        1: '待发货',
        2: '已发货',
        3: '已完成',
        4: '已取消',
        5: '已退款'
      };

      // 处理支付状态
      const paymentStatusMap = {
        0: '未支付',
        1: '部分支付',
        2: '已支付',
        3: '退款中',
        4: '已退款'
      };
      
      // 处理配送状态
      const shippingStatusMap = {
        0: '未发货',
        1: '已发货',
        2: '已收货'
      };
      
      // 处理订单来源
      const orderSourceMap = {
        1: '官方商城',
        2: '微信小程序',
        3: '手机应用'
      };
      
      // 处理订单类型
      const orderTypeMap = {
        1: '普通订单',
        2: '团购订单',
        3: '秒杀订单'
      };
      
      // 格式化时间
      const formatTimestamp = (timestamp) => {
        if (!timestamp) return '';
        const date = new Date(parseInt(timestamp));
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }).replace(/\//g, '-');
      };
      
      // 更新订单详情数据
      Object.assign(orderDetail, {
        id: data.id,
        orderNumber: data.id, // 使用id替代orderSn作为订单编号
        orderSource: orderSourceMap[data.orderSource] || '未知',
        orderChannel: orderTypeMap[data.orderType] || '普通订单',
        orderStatus: orderStatusMap[data.orderStatus] || '未知状态',
        payStatus: paymentStatusMap[data.paymentStatus] || '未支付',
        createTime: formatTimestamp(data.createdAt),
        payTime: formatTimestamp(data.paidAt),
        orderNote: data.remark || '',
        totalProductAmount: data.totalProductAmount,
        shippingAmount: data.shippingFee,
        discountAmount: data.discountAmount,
        payAmount: data.totalAmount,
        payMethod: data.paymentMethod || '未知',
        receiver: data.shipping?.recipientName || '',
        contactPhone: data.shipping?.recipientPhone || '',
        shippingAddress: data.shipping?.streetAddress || '',
        logisticsCompany: data.shipping?.shippingCompanyName || '',
        trackingNumber: data.shipping?.trackingNumber || '',
        logs: data.logs || []
      });
      
      // 处理商品数据
      if (data.items && Array.isArray(data.items)) {
        goodsData.value = data.items.map(item => ({
          id: item.id,
          imageUrl: item.productImage || '/placeholder-image.jpg',
          name: item.productName,
          specs: item.skuSpecifications,
          price: item.unitPrice,
          quantity: item.quantity,
          subtotal: item.totalPrice,
          vendor: item.thirdPartyProductCode ? `第三方商品编码: ${item.thirdPartyProductCode}` : '自营商品'
        }));
      }
    }
    // 注意：不再显示错误消息，因为request.js中的响应拦截器已经处理了错误
  } catch (error) {
    // 仅在控制台记录错误，不显示给用户
    console.error('获取订单详情失败', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  // 从路由参数获取订单ID
  orderId.value = route.params.id;
  if (orderId.value) {
    getOrderDetail(orderId.value);
  } else {
    // 如果没有订单ID，静默返回列表页
    router.push('/master/order/orderManage');
  }
});
</script>

<style scoped>
/* 卡片样式 */
.order-card {
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.order-card :deep(.arco-card-header) {
  border-bottom: 1px solid #f2f3f5;
  padding: 12px 20px;
}

.order-card :deep(.arco-card-body) {
  padding: 16px 20px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  align-items: flex-start;
  line-height: 1.6;
}

.info-label {
  color: #86909c;
  min-width: 80px;
  margin-right: 8px;
  font-size: 14px;
}

.info-value {
  color: #1d2129;
  font-size: 14px;
  word-break: break-all;
}

/* 商品表格样式 */
.goods-table {
  margin-bottom: 16px;
}

.goods-table :deep(.arco-table-th) {
  background-color: #f7f8fa;
  font-weight: 500;
  color: #1d2129;
  padding: 12px 16px;
  border-color: #e5e6eb;
}

.goods-table :deep(.arco-table-td) {
  padding: 16px;
  border-color: #e5e6eb;
}

.goods-table :deep(.arco-table-tr:hover) {
  background-color: #f7f8fa;
}

.product-image {
  border-radius: 4px;
  border: 1px solid #f2f3f5;
  background-color: #f7f8fa;
}

.product-info {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  color: #1d2129;
  line-height: 1.5;
  margin-bottom: 4px;
}

.product-specs {
  font-size: 13px;
  color: #86909c;
}

.text-price {
  color: #1d2129;
  font-size: 14px;
}

/* 价格汇总样式 */
.price-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 16px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  /* width: 200px; */
  margin-bottom: 8px;
  font-size: 14px;
  color: #1d2129;
}

.price-value {
  font-weight: 500;
}

.payment-method-row {
  margin-top: 8px;
}

.payment-method {
  color: #ff7d00;
}
</style>
