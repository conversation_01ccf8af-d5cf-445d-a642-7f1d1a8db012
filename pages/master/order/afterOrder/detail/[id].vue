<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block lg:p-4 p-2">

    <!-- 订单基本信息卡片 -->
    <a-card class="mb-4 order-card" title="售后信息">
      <a-row :gutter="[16, 16]">
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">售后编号：</span>
            <span class="info-value">{{ orderDetail.orderNumber }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">申请时间：</span>
            <span class="info-value">{{ orderDetail.createTime }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">退款类型：</span>
            <span class="info-value">{{ orderDetail.orderChannel }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">退款金额：</span>
            <span class="info-value">{{ orderDetail.orderChannel }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">退款原因：</span>
            <span class="info-value">不喜欢</span>
          </div>
        </a-col>
      
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">退款状态：</span>
            <span class="info-value">
              <a-tag :color="getStatusColor(orderDetail.orderStatus)">{{ orderDetail.orderStatus }}</a-tag>
            </span>
          </div>
        </a-col>
        <a-col :span="24">
          <div class="info-item">
            <span class="info-label">退款备注：</span>
            <span class="info-value">{{ orderDetail.orderNote || '无' }}</span>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 收货信息卡片 -->
    <a-card class="mb-4 order-card" title="订单信息">
      <a-row :gutter="[16, 16]">
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">订单编号：</span>
            <span class="info-value">{{ orderDetail.orderNumber }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">订单金额：</span>
            <span class="info-value">{{ orderDetail.payAmount }}</span>
          </div>
        </a-col>
      
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">订单类型：</span>
            <span class="info-value">{{ orderDetail.orderChannel }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">支付时间：</span>
            <span class="info-value">{{ orderDetail.payTime }}</span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">买家/收货人</span>
            <span class="info-value">
              {{ orderDetail.receiver }}
            </span>
          </div>
        </a-col>
        <a-col :span="8" :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <span class="info-label">收货地址：</span>
            <span class="info-value">{{ orderDetail.shippingAddress }}</span>
          </div>
        </a-col>
      </a-row>
    </a-card>
  <!-- 售后商品卡片 -->
  <a-card class="mb-4 order-card" title="退款商品">
      <a-table 
        :columns="goodsColumns" 
        :data="goodsData" 
        :pagination="false" 
        :bordered="{ cell: true }"
        class="goods-table"
      >
        <template #name="{ record }">
          <div class="flex items-start">
            <a-image :src="record.imageUrl" width="60" height="60" class="mr-3 product-image" />
            <div class="product-info">
              <div class="product-name">{{ record.name }}</div>
              <div class="product-specs">{{ record.specs }}</div>
            </div>
          </div>
        </template>
        <template #vendor="{ record }">
          <span>{{ record.vendor }}</span>
        </template>
        <template #price="{ record }">
          <span class="text-price">￥{{ record.price }}</span>
        </template>
        <template #quantity="{ record }">
          <span>{{ record.quantity }}</span>
        </template>
      </a-table>

    </a-card>
    <!-- 售后日志卡片 -->
    <a-card class="order-card" title="售后日志">
      <a-timeline class="order-timeline">
        <a-timeline-item v-for="(log, index) in orderDetail.logs" :key="index" :dot-color="getLogColor(log.type)">
          <div class="timeline-content">
            <div class="timeline-title">{{ log.title }}</div>
            <div class="timeline-info">
              <span class="timeline-operator">操作人：{{ log.operator }}</span>
              <span class="timeline-time">（{{ log.time }}）</span>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import { IconArrowLeft } from '@arco-design/web-vue/es/icon';

definePageMeta({
  name: "master-after-order-detail",
  path: "/master/order/afterOrder/detail/:id",
})

const route = useRoute();
const router = useRouter();
const orderId = ref('');
const loading = ref(false);

// 订单详情数据
const orderDetail = reactive({
  id: '',
  orderNumber: '202504020951123456',
  orderSource: '官方商城',
  orderChannel: '电脑订单',
  orderStatus: '已完成',
  payStatus: '已支付',
  createTime: '2025-04-02 12:18:28',
  payTime: '2025-04-02 10:30:45',
  orderNote: '',
  goodsAmount: '2000.00',
  shippingAmount: '0.00',
  discountAmount: '0.00',
  payAmount: '2000.00',
  payMethod: '微信支付',
  receiver: '张小强',
  contactPhone: '13800138000',
  shippingAddress: '广州市荔湾区芳村大道北111号',
  logisticsCompany: '顺丰快递',
  trackingNumber: 'SF123456789123',
  logs: [
    { type: 'create', title: '订单创建', operator: '系统', time: '2025-04-02 12:18:28' },
    { type: 'pay', title: '订单支付', operator: '张小强', time: '2025-04-02 12:40:00' },
    { type: 'ship', title: '订单发货', operator: '系统管理员', time: '2025-04-03 10:00:00' },
    { type: 'complete', title: '订单完成', operator: '张小强', time: '2025-04-09 09:55:00' }
  ]
});

// 商品数据
const goodsData = ref([
  {
    id: 1,
    imageUrl: '/placeholder-image.jpg',
    name: 'EARACTOR行政椅 MD3椅脚 杆调节高度 扶手可升降功能座椅',
    specs: '黑色',
    price: '1000.00',
    quantity: 2,
    subtotal: '2000.00',
    vendor: '广州汇名家具有限公司'
  }
]);

// 商品表格列定义
const goodsColumns = [
  {
    title: '商品名称',
    slotName: 'name',
    dataIndex: 'name',
    width: '40%'
  },
  {
    title: '商家',
    slotName: 'vendor',
    dataIndex: 'vendor',
    width: '20%'
  },
  {
    title: '单价',
    slotName: 'price',
    dataIndex: 'price',
    width: '20%',
    align: 'center'
  },
  {
    title: '购买数量',
    slotName: 'quantity',
    dataIndex: 'quantity',
    width: '20%',
    align: 'center'
  }
];

// 获取日志颜色
const getLogColor = (type) => {
  const colorMap = {
    'create': 'blue',
    'pay': 'green',
    'ship': 'orange',
    'complete': 'green',
    'cancel': 'red',
    'refund': 'red'
  };
  return colorMap[type] || 'gray';
};

// 获取订单状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    '待付款': 'orange',
    '待发货': 'blue',
    '已发货': 'purple',
    '已完成': 'green',
    '已取消': 'red',
    '已关闭': 'gray',
    '退款中': 'magenta',
    '已退款': 'red'
  };
  return statusMap[status] || '';
};

// 返回列表
const goBack = () => {
  router.push('/master/order/orderManage');
};

// 获取订单详情
const getOrderDetail = async (id) => {
  loading.value = true;
  try {
    // 这里应该调用实际的API获取订单详情
    // const res = await api.getOrderDetail(id);
    // if (res.code === 200) {
    //   Object.assign(orderDetail, res.data);
    //   goodsData.value = res.data.goods;
    // }
    
    // 模拟数据已经在reactive对象中设置
    loading.value = false;
    
  } catch (error) {
    console.error('获取订单详情失败', error);
    Message.error('获取订单详情失败');
    loading.value = false;
  }
};

onMounted(() => {
  // 从路由参数获取订单ID
  orderId.value = route.params.id;
  if (orderId.value) {
    getOrderDetail(orderId.value);
  } else {
    Message.error('订单ID不能为空');
    router.push('/master/order/orderManage');
  }
});
</script>

<style scoped>
/* 卡片样式 */
.order-card {
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.order-card :deep(.arco-card-header) {
  border-bottom: 1px solid #f2f3f5;
  padding: 12px 20px;
}

.order-card :deep(.arco-card-body) {
  padding: 16px 20px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  align-items: flex-start;
  line-height: 1.6;
}

.info-label {
  color: #86909c;
  min-width: 80px;
  margin-right: 8px;
  font-size: 14px;
}

.info-value {
  color: #1d2129;
  font-size: 14px;
  word-break: break-all;
}

/* 商品表格样式 */
.goods-table {
  margin-bottom: 16px;
}

.goods-table :deep(.arco-table-th) {
  background-color: #f7f8fa;
  font-weight: 500;
  color: #1d2129;
  padding: 12px 16px;
  border-color: #e5e6eb;
}

.goods-table :deep(.arco-table-td) {
  padding: 16px;
  border-color: #e5e6eb;
}

.goods-table :deep(.arco-table-tr:hover) {
  background-color: #f7f8fa;
}

.product-image {
  border-radius: 4px;
  border: 1px solid #f2f3f5;
  background-color: #f7f8fa;
}

.product-info {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  color: #1d2129;
  line-height: 1.5;
  margin-bottom: 4px;
}

.product-specs {
  font-size: 13px;
  color: #86909c;
}

.text-price {
  color: #1d2129;
  font-size: 14px;
}

/* 价格汇总样式 */
.price-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 16px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  width: 200px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #1d2129;
}

.price-value {
  font-weight: 500;
}

.payment-method-row {
  margin-top: 8px;
}

.payment-method {
  color: #ff7d00;
}
</style>
