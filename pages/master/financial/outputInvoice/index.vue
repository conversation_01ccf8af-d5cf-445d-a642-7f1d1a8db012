<template>
  <div class="ma-content-block p-4">
    <ma-crud
      :options="crudOptions"
      :columns="columns"
      ref="crudRef"
      @row-click="handleView"
    >
      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space @click.stop>
          <a-link @click.stop="handleDownload(record)">下载发票</a-link>
        </a-space>
      </template>

      <!-- 开票状态 -->
      <template #invoiceStatus="{ record }">
        <a-tag :color="getInvoiceStatusColor(record.invoiceStatus)">
          {{ getInvoiceStatusText(record.invoiceStatus) }}
        </a-tag>
      </template>

      <!-- 发票状态 -->
      <template #invoiceType="{ record }">
        <a-tag :color="getInvoiceTypeColor(record.invoiceType)">
          {{ getInvoiceTypeText(record.invoiceType) }}
        </a-tag>
      </template>

      <!-- 开票方式 -->
      <template #invoiceMethod="{ record }">
        <a-tag :color="getInvoiceMethodColor(record.invoiceMethod)">
          {{ getInvoiceMethodText(record.invoiceMethod) }}
        </a-tag>
      </template>
      
      <!-- 是否勾兑 -->
      <template #isReconciled="{ record }">
        <a-tag :color="getReconciledColor(record)">
          {{ record.isReconciled ? '是' : '否' }}
        </a-tag>
      </template>
      
      <!-- 勾兑金额 -->
      <template #reconciledAmount="{ record }">
        <span :class="getReconciledAmountClass(record)">
          {{ (record.reconciledAmount || 0).toFixed(2) }}/{{ (record.totalReconciledAmount || 0).toFixed(2) }}
        </span>
      </template>
    </ma-crud>
    
    <!-- 发票详情抽屉 -->
    <InvoiceDetailDrawer
      v-model:visible="detailVisible"
      :detailData="currentDetailData"
      @download="handleDownload"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import InvoiceDetailDrawer from "./components/InvoiceDetailDrawer.vue";
import { useOutputInvoiceStore } from "@/store/master/financial";

// 定义页面路由元信息
definePageMeta({
  name: "outputInvoice",
  path: "/master/financial/outputInvoice",
});

const crudRef = ref();

// 详情抽屉控制
const detailVisible = ref(false);
const currentDetailData = ref({});

// 从store中获取销项发票数据
const outputInvoiceStore = useOutputInvoiceStore();
const mockData = computed(() => outputInvoiceStore.mockData);

// 开票状态颜色和文本
const getInvoiceStatusColor = (status) => {
  const statusMap = {
    processing: "blue",
    completed: "green",
    cancelled: "red"
  };
  return statusMap[status] || "gray";
};

const getInvoiceStatusText = (status) => {
  const statusMap = {
    completed: "已开票",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 发票类型颜色和文本
const getInvoiceTypeColor = (type) => {
  const typeMap = {
    blue: "blue",
    red: "red",
    void: "gray"
  };
  return typeMap[type] || "gray";
};

const getInvoiceTypeText = (type) => {
  const typeMap = {
    blue: "蓝字发票",
    red: "红字发票",
    void: "作废发票"
  };
  return typeMap[type] || "未知类型";
};

// 开票方式颜色和文本
const getInvoiceMethodColor = (method) => {
  const methodMap = {
    manual: "orange",
    auto: "green"
  };
  return methodMap[method] || "gray";
};

const getInvoiceMethodText = (method) => {
  const methodMap = {
    manual: "手动上传",
    auto: "一键开票"
  };
  return methodMap[method] || "未知方式";
};

// 勾兑状态颜色
const getReconciledColor = (record) => {
  if (!record || record.isReconciled === undefined || !record.isReconciled) {
    return "gray";
  }
  const reconciledAmount = record.reconciledAmount || 0;
  const totalReconciledAmount = record.totalReconciledAmount || 0;
  return reconciledAmount >= totalReconciledAmount ? "green" : "orange";
};

// 勾兑金额样式
const getReconciledAmountClass = (record) => {
  if (!record || record.isReconciled === undefined || !record.isReconciled) {
    return "text-gray-500";
  }
  const reconciledAmount = record.reconciledAmount || 0;
  const totalReconciledAmount = record.totalReconciledAmount || 0;
  return reconciledAmount >= totalReconciledAmount ? "text-success " : "text-warning ";
};

// 操作处理函数
const handleView = (record) => {
  // 使用store获取发票详情
  const invoice = outputInvoiceStore.getInvoiceDetail(record.id);
  
  if (!invoice) {
    Message.error("未找到发票详情");
    return;
  }
  
  // 准备详情数据
  currentDetailData.value = {
    ...invoice,
    // 添加模拟的详细信息
    invoiceSource: "系统生成",
    invoiceRemark: "销售商品开具发票",
    sellerName: "本公司名称",
    sellerTaxNo: "91110105MA01ABCD1X",
    sellerAddress: "北京市朝阳区某某路88号",
    sellerPhone: "010-********",
    sellerBank: "中国工商银行",
    sellerBankAccount: "6212 2601 0201 2345 678",
    buyerName: invoice.invoiceTitle,
    buyerTaxNo: "91110105MA01WXYZ2Y",
    buyerAddress: "北京市海淀区某某街99号",
    buyerPhone: "010-********",
    buyerBank: "中国建设银行",
    buyerBankAccount: "6217 0001 8888 8888 888",
    invoiceNo: `FP${invoice.id}`,
    invoiceDate: invoice.invoiceTime,
    goods: [
      {
        id: 1,
        name: "高端商务笔记本电脑",
        specification: "16GB/512GB SSD",
        unit: "台",
        quantity: 1,
        unitPrice: invoice.totalAmount * 0.8,
        amount: invoice.totalAmount * 0.8,
        taxRate: "13%",
        taxAmount: invoice.totalAmount * 0.8 * 0.13
      },
      {
        id: 2,
        name: "电脑配件",
        specification: "标准套装",
        unit: "套",
        quantity: 1,
        unitPrice: invoice.totalAmount * 0.2,
        amount: invoice.totalAmount * 0.2,
        taxRate: "13%",
        taxAmount: invoice.totalAmount * 0.2 * 0.13
      }
    ],
    totalAmount: invoice.totalAmount,
    totalTaxAmount: invoice.totalAmount * 0.13,
    amountWithTax: invoice.totalAmount * 1.13,
    paymentMethod: "微信支付",
    logs: [
      {
        content: "系统生成销项发票",
        time: invoice.invoiceTime
      },
      {
        content: "发票开具完成",
        time: new Date(new Date(invoice.invoiceTime).getTime() + 5 * 60 * 1000).toLocaleString()
      }
    ]
  };

  // 显示详情抽屉
  detailVisible.value = true;
};

// 下载发票
const handleDownload = (record, event) => {
  if (event) {
    event.stopPropagation();
  }
  
  // 获取发票ID
  const { id, invoiceNo } = record;
  
  // 显示提示信息
  Message.success(`开始下载发票：ID ${id}，发票号 ${invoiceNo || `FP${id}`}`);
  
  // 如果是从详情页调用，不关闭抽屉
  // 实际下载逻辑应该调用后端API
};

// ma-crud 配置
const crudOptions = reactive({
  // 模拟API，实际使用时替换为真实API
  api: async (params) => {
    // 使用outputInvoiceStore的方法获取数据
    return outputInvoiceStore.getOutputInvoiceData(params);
  },
  // 搜索配置
  searchColNumber: 3,
  // 表格配置
  showIndex: false, // 不显示序号列
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 处理开票时间范围
    if (params.invoiceTime && params.invoiceTime.length > 0) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.invoiceTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startInvoiceTime = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.invoiceTime[1]);
      endDate.setHours(23, 59, 59, 0);
      params.endInvoiceTime = endDate.getTime();

      delete params.invoiceTime;
    } else {
      delete params.startInvoiceTime;
      delete params.endInvoiceTime;
    }

    // 处理订单时间范围
    if (params.orderTime && params.orderTime.length > 0) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.orderTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startOrderTime = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.orderTime[1]);
      endDate.setHours(23, 59, 59, 0);
      params.endOrderTime = endDate.getTime();

      delete params.orderTime;
    } else {
      delete params.startOrderTime;
      delete params.endOrderTime;
    }
    
    return params;
  },
});

// 发票类型选项
const invoiceTypeOptions = [
  { label: "全部", value: "" },
  { label: "蓝字发票", value: "blue" },
  { label: "红字发票", value: "red" },
  { label: "作废发票", value: "void" },
];

// 开票状态选项
const invoiceStatusOptions = [
  { label: "全部", value: "" },
  { label: "已开票", value: "completed" },
  { label: "已取消", value: "cancelled" },
];

// 开票方式选项
const invoiceMethodOptions = [
  { label: "全部", value: "" },
  { label: "手动上传", value: "manual" },
  { label: "一键开票", value: "auto" },
];

// 表格列定义
const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    width: 80,
    align: "center",
    fixed: "left",
  },
  {
    title: "商户名称",
    dataIndex: "merchantName",
    width: 180,
    search: true,
    formType: "input",
  },
  {
    title: "发票抬头",
    dataIndex: "invoiceTitle",
    width: 180,
    search: true,
    formType: "input",
  },
  {
    title: "开票时间",
    dataIndex: "invoiceTime",
    width: 170,
    align: "center",
    search: true,
    formType: "range",
  },
  {
    title: "发票代码",
    dataIndex: "invoiceCode",
    width: 150,
    search: true,
    formType: "input",
  },
  {
    title: "订单时间",
    dataIndex: "orderTime",
    width: 170,
    align: "center",
    search: true,
    formType: "range",
  },
  {
    title: "价税合计",
    dataIndex: "totalAmount",
    width: 120,
    align: "right",
    search: true,
    formType: "number",
    render: ({ record }) => `¥${(record.totalAmount * 1.13).toFixed(2)}`,
  },
  {
    title: "系统订单号",
    dataIndex: "systemOrderNo",
    width: 160,
    search: true,
    formType: "input",
  },
  {
    title: "第三方订单号",
    dataIndex: "thirdPartyOrderNo",
    width: 160,
    search: true,
    formType: "input",
  },
  {
    title: "开票状态",
    dataIndex: "invoiceStatus",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: invoiceStatusOptions },
    slotName: "invoiceStatus",
  },
  {
    title: "发票状态",
    dataIndex: "invoiceType",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: invoiceTypeOptions },
    slotName: "invoiceType",
  },
  {
    title: "开票方式",
    dataIndex: "invoiceMethod",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: invoiceMethodOptions },
    slotName: "invoiceMethod",
  },
  {
    title: "是否勾兑",
    dataIndex: "isReconciled",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: [
      { label: "全部", value: "" },
      { label: "是", value: true },
      { label: "否", value: false },
    ] },
    slotName: "isReconciled",
  },
  {
    title: "勾兑金额",
    dataIndex: "reconciledAmount",
    width: 150,
    align: "right",
    slotName: "reconciledAmount",
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 200,
    align: "center",
    fixed: "right",
    slotName: "operation",
  },
]);

// 生命周期钩子
onMounted(() => {
  // 页面加载时可以执行一些初始化操作
});
</script>

<script>
export default { name: "master-financial-output-invoice" };
</script>

<style scoped>
/* 自定义样式 */
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}
</style>
