<template>
  <a-drawer
    :width="'65%'"
    :visible="visible"
    @cancel="onClose"
    unmountOnClose
  >
    <template #title>
      销项发票详情
    </template>

    <div class="invoice-detail-container">
      <!-- 发票基本信息 -->
      <div class="invoice-basic-section mb-4 pb-3 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h4 class="text-base font-medium mb-3 text-blue-500">发票基本信息</h4>
          <div>
            <a-button type="primary" class="mr-2" @click="onDownload">
              <template #icon><icon-download /></template>
              下载发票
            </a-button>
          </div>
        </div>
        <div class="grid grid-cols-3 gap-3">
          <div class="info-item">
            <div class="text-gray-500">发票号码：</div>
            <div>{{ detailData.invoiceNo || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">发票代码：</div>
            <div>{{ detailData.invoiceCode || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">开票日期：</div>
            <div>{{ detailData.invoiceDate || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">系统订单号：</div>
            <div>{{ detailData.systemOrderNo || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">第三方订单号：</div>
            <div>{{ detailData.thirdPartyOrderNo || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">订单时间：</div>
            <div>{{ detailData.orderTime || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">发票类型：</div>
            <div>
              <a-tag :color="getInvoiceTypeColor(detailData.invoiceType)">
                {{ getInvoiceTypeText(detailData.invoiceType) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">开票状态：</div>
            <div>
              <a-tag :color="getInvoiceStatusColor(detailData.invoiceStatus)">
                {{ getInvoiceStatusText(detailData.invoiceStatus) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">开票方式：</div>
            <div>
              <a-tag :color="getInvoiceMethodColor(detailData.invoiceMethod)">
                {{ getInvoiceMethodText(detailData.invoiceMethod) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item col-span-3">
            <div class="text-gray-500">发票备注：</div>
            <div>{{ detailData.invoiceRemark || '无' }}</div>
          </div>
        </div>
      </div>

      <!-- 销售方信息 -->
      <div class="seller-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">销售方信息</h4>
        <div class="grid grid-cols-3 gap-3">
          <div class="info-item">
            <div class="text-gray-500">销售方名称：</div>
            <div>{{ detailData.sellerName || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方税号：</div>
            <div>{{ detailData.sellerTaxNo || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方地址：</div>
            <div>{{ detailData.sellerAddress || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方电话：</div>
            <div>{{ detailData.sellerPhone || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方开户银行：</div>
            <div>{{ detailData.sellerBank || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方银行账号：</div>
            <div>{{ detailData.sellerBankAccount || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 购买方信息 -->
      <div class="buyer-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">购买方信息</h4>
        <div class="grid grid-cols-3 gap-3">
          <div class="info-item">
            <div class="text-gray-500">购买方名称：</div>
            <div>{{ detailData.buyerName || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方税号：</div>
            <div>{{ detailData.buyerTaxNo || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方地址：</div>
            <div>{{ detailData.buyerAddress || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方电话：</div>
            <div>{{ detailData.buyerPhone || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方开户银行：</div>
            <div>{{ detailData.buyerBank || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方银行账号：</div>
            <div>{{ detailData.buyerBankAccount || '-' }}</div>
          </div>
        </div>
      </div>

      <!-- 发票明细 -->
      <div class="goods-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">发票明细</h4>
        <a-table
          :data="detailData.goods || []"
          :pagination="false"
          size="small"
          :bordered="true"
          :stripe="true"
        >
          <template #columns>
            <a-table-column title="序号" :width="60" align="center">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column title="货物或应税劳务服务名称" data-index="name" />
            <a-table-column title="规格型号" data-index="specification" :width="120" />
            <a-table-column title="单位" data-index="unit" :width="60" align="center" />
            <a-table-column title="数量" data-index="quantity" :width="80" align="center" />
            <a-table-column title="单价" data-index="unitPrice" :width="100" align="right">
              <template #cell="{ record }">
                ¥{{ record.unitPrice.toFixed(2) }}
              </template>
            </a-table-column>
            <a-table-column title="金额" data-index="amount" :width="120" align="right">
              <template #cell="{ record }">
                ¥{{ record.amount.toFixed(2) }}
              </template>
            </a-table-column>
            <a-table-column title="税率" data-index="taxRate" :width="80" align="center" />
            <a-table-column title="税额" data-index="taxAmount" :width="120" align="right">
              <template #cell="{ record }">
                ¥{{ record.taxAmount.toFixed(2) }}
              </template>
            </a-table-column>
          </template>
        </a-table>
        
        <div class="flex justify-end mt-2">
          <div class="text-right grid grid-cols-1 gap-0">
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">金额合计：</span>
              <span>¥{{ (detailData.totalAmount || 0).toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">税额合计：</span>
              <span>¥{{ (detailData.totalTaxAmount || 0).toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">价税合计：</span>
              <span class="text-red-500 font-bold">¥{{ (detailData.amountWithTax || 0).toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">支付方式：</span>
              <span>{{ detailData.paymentMethod || '微信支付' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 关联发票 -->
      <div v-if="detailData.invoiceType === 'red' && relatedInvoiceIds.length > 0" class="related-invoices-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">关联发票</h4>
        <a-table
          :data="relatedInvoices"
          :pagination="false"
          size="small"
          :bordered="true"
          :stripe="true"
        >
          <template #columns>
            <a-table-column title="发票状态" data-index="invoiceStatus" :width="100" align="center">
              <template #cell="{ record }">
                <a-tag :color="getInvoiceStatusColor(record.invoiceStatus)">
                  {{ getInvoiceStatusText(record.invoiceStatus) }}
                </a-tag>
              </template>
            </a-table-column>
            <a-table-column title="开票时间" data-index="invoiceTime" :width="170" align="center" />
            <a-table-column title="发票抬头" data-index="invoiceTitle" :width="180" />
            <a-table-column title="发票号" data-index="invoiceNo" :width="120" />
            <a-table-column title="价税合计" data-index="amountWithTax" :width="120" align="right">
              <template #cell="{ record }">
                ¥{{ record.amountWithTax.toFixed(2) }}
              </template>
            </a-table-column>
            <a-table-column title="操作" :width="150" align="center">
              <template #cell="{ record }">
                <a-space>
                  <a-button type="text" size="small" @click="handleViewRelated(record)">
                    <template #icon><icon-eye /></template>
                    查看
                  </a-button>
                  <a-button type="text" size="small" @click="handleDownloadRelated(record)">
                    <template #icon><icon-download /></template>
                    下载
                  </a-button>
                </a-space>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>

      <!-- 操作日志 -->
      <div class="log-section mb-4">
        <h4 class="text-base font-medium mb-3 text-blue-500">操作日志</h4>
        <a-timeline>
          <a-timeline-item
            v-for="(log, index) in detailData.logs"
            :key="index"
            :label="log.time"
          >
            {{ log.content }}
          </a-timeline-item>
        </a-timeline>
      </div>
    </div>
  </a-drawer>
  
  <!-- 关联发票模态框 -->
  <RelatedInvoiceModal
    v-model:visible="relatedInvoiceVisible"
    :detailData="relatedInvoiceData"
  />
</template>

<script setup>
import { ref, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconDownload, IconEye } from "@arco-design/web-vue/es/icon";
import RelatedInvoiceModal from "./RelatedInvoiceModal.vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    default: () => ({})
  }
});

// 解析关联发票ID
const relatedInvoiceIds = computed(() => {
  if (!props.detailData.relatedInvoiceId) return [];
  return props.detailData.relatedInvoiceId.split(',').filter(id => id.trim() !== '');
});

// 模拟关联发票数据
const relatedInvoices = computed(() => {
  // 实际应该调用API获取关联发票数据
  return relatedInvoiceIds.value.map(id => {
    // 根据ID生成模拟数据
    return {
      id,
      invoiceStatus: 'completed',
      invoiceTime: id === '10001' ? '2025-05-20 10:30:25' : 
                   id === '10002' ? '2025-05-27 14:22:10' : 
                   id === '10003' ? '2025-05-26 09:15:30' : 
                   id === '10007' ? '2025-05-22 15:40:12' : '2025-05-20 10:30:25',
      invoiceTitle: id === '10001' ? '北京科技有限公司' : 
                    id === '10002' ? '上海贸易有限公司' : 
                    id === '10003' ? '广州电子科技有限公司' : 
                    id === '10007' ? '武汉科技有限公司' : '北京科技有限公司',
      invoiceNo: `FP${id}`,
      invoiceCode: id === '10001' ? '1234567890' : 
                   id === '10002' ? '1234567891' : 
                   id === '10003' ? '1234567892' : 
                   id === '10007' ? '1234567896' : '1234567890',
      totalAmount: id === '10001' ? 1299.00 : 
                   id === '10002' ? 2499.50 : 
                   id === '10003' ? 899.00 : 
                   id === '10007' ? 2199.00 : 1000.00,
      amountWithTax: id === '10001' ? 1299.00 * 1.13 : 
                     id === '10002' ? 2499.50 * 1.13 : 
                     id === '10003' ? 899.00 * 1.13 : 
                     id === '10007' ? 2199.00 * 1.13 : 1000.00 * 1.13,
      invoiceType: 'blue'
    };
  });
});

// 开票状态颜色和文本
const getInvoiceStatusColor = (status) => {
  const statusMap = {
    processing: "blue",
    completed: "green",
    cancelled: "red"
  };
  return statusMap[status] || "gray";
};

const getInvoiceStatusText = (status) => {
  const statusMap = {
    completed: "已开票",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 发票类型颜色和文本
const getInvoiceTypeColor = (type) => {
  const typeMap = {
    blue: "blue",
    red: "red",
    void: "gray"
  };
  return typeMap[type] || "gray";
};

const getInvoiceTypeText = (type) => {
  const typeMap = {
    blue: "蓝字发票",
    red: "红字发票",
    void: "作废发票"
  };
  return typeMap[type] || "未知类型";
};

// 开票方式颜色和文本
const getInvoiceMethodColor = (method) => {
  const methodMap = {
    manual: "orange",
    auto: "green"
  };
  return methodMap[method] || "gray";
};

const getInvoiceMethodText = (method) => {
  const methodMap = {
    manual: "手动上传",
    auto: "一键开票"
  };
  return methodMap[method] || "未知方式";
};

const emit = defineEmits(['update:visible', 'download']);

// 关联发票模态框控制
const relatedInvoiceVisible = ref(false);
const relatedInvoiceData = ref({});

// 查看关联发票详情
const handleViewRelated = (record) => {
  // 模拟获取关联发票详细数据
  // 实际应该调用API获取关联发票数据
  
  // 模拟数据，实际应该从后端获取
  relatedInvoiceData.value = {
    id: record.id,
    merchantName: record.invoiceTitle,
    invoiceTitle: record.invoiceTitle,
    invoiceTime: record.invoiceTime,
    invoiceCode: record.invoiceCode,
    orderTime: props.detailData.orderTime,
    systemOrderNo: props.detailData.systemOrderNo,
    thirdPartyOrderNo: props.detailData.thirdPartyOrderNo,
    invoiceStatus: record.invoiceStatus,
    invoiceType: record.invoiceType,
    invoiceMethod: 'manual',
    invoiceNo: record.invoiceNo,
    invoiceDate: record.invoiceTime,
    totalAmount: record.totalAmount,
    totalTaxAmount: record.totalAmount * 0.13,
    amountWithTax: record.amountWithTax,
    goods: [
      {
        id: 1,
        name: '高端商务笔记本电脑',
        specification: '16GB/512GB SSD',
        unit: '台',
        quantity: 1,
        unitPrice: record.totalAmount * 0.8,
        amount: record.totalAmount * 0.8,
        taxRate: '13%',
        taxAmount: record.totalAmount * 0.8 * 0.13
      },
      {
        id: 2,
        name: '电脑配件',
        specification: '标准套装',
        unit: '套',
        quantity: 1,
        unitPrice: record.totalAmount * 0.2,
        amount: record.totalAmount * 0.2,
        taxRate: '13%',
        taxAmount: record.totalAmount * 0.2 * 0.13
      }
    ]
  };
  
  // 显示关联发票模态框
  relatedInvoiceVisible.value = true;
};

// 关闭抽屉
const onClose = () => {
  emit('update:visible', false);
};

// 下载发票
const onDownload = () => {
  emit('download', props.detailData);
};

// 下载关联发票
const handleDownloadRelated = (record) => {
  Message.success(`开始下载关联发票：${record.invoiceNo}`);
  // 实际下载逻辑应该调用后端API
};
</script>

<style scoped>
.invoice-detail-container {
  padding: 0 16px;
}

.info-item {
  display: flex;
  margin-bottom: 2px; /* 缩小行间距 */
}

.info-item .text-gray-500 {
  min-width: 100px;
  flex-shrink: 0;
}

.text-blue-500 {
  color: var(--color-primary-6);
}

.border-gray-200 {
  border-color: var(--color-border-2);
}

.text-red-500 {
  color: var(--color-danger-6);
}

/* 减小标题和内容的间距 */
h4.text-base {
  margin-bottom: 6px;
}

/* 减小各部分之间的间距 */
.mb-4 {
  margin-bottom: 8px;
}

.pb-3 {
  padding-bottom: 8px;
}

/* 减小表格行高 */
:deep(.arco-table-tr) {
  height: 32px;
}

:deep(.arco-table-td) {
  padding-top: 4px;
  padding-bottom: 4px;
}

/* 减小网格间距 */
.gap-3 {
  gap: 0;
}
</style>
