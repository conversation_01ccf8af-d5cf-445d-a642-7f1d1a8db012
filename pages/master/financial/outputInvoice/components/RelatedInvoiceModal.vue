<template>
  <a-modal
    :visible="visible"
    @cancel="onClose"
    :footer="false"
    :mask-closable="false"
    :width="1200"
    title="关联发票详情"
  >
    <div class="related-invoice-container">
      <!-- 发票基本信息 -->
      <div class="invoice-basic-section mb-4 pb-3 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h4 class="text-base font-medium mb-3 text-blue-500">发票基本信息</h4>
          <a-button type="primary" size="small" @click="onDownload">
            <template #icon><icon-download /></template>
            下载发票
          </a-button>
        </div>
        <div class="grid grid-cols-3 gap-3">
          <div class="info-item">
            <div class="text-gray-500">发票号码：</div>
            <div>{{ detailData.invoiceNo || 'FP10006' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">发票代码：</div>
            <div>{{ detailData.invoiceCode || '1234567895' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">开票日期：</div>
            <div>{{ detailData.invoiceDate || '2025-05-23 09:20:35' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">系统订单号：</div>
            <div>{{ detailData.systemOrderNo || 'ORD20250523006' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">第三方订单号：</div>
            <div>{{ detailData.thirdPartyOrderNo || 'JD20250523006' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">订单时间：</div>
            <div>{{ detailData.orderTime || '2025-05-22 10:15:27' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">发票类型：</div>
            <div>
              <a-tag :color="getInvoiceTypeColor(detailData.invoiceType || 'blue')">
                {{ getInvoiceTypeText(detailData.invoiceType || 'blue') }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">开票状态：</div>
            <div>
              <a-tag :color="getInvoiceStatusColor(detailData.invoiceStatus || 'completed')">
                {{ getInvoiceStatusText(detailData.invoiceStatus || 'completed') }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">开票方式：</div>
            <div>
              <a-tag :color="getInvoiceMethodColor(detailData.invoiceMethod || 'manual')">
                {{ getInvoiceMethodText(detailData.invoiceMethod || 'manual') }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">发票来源：</div>
            <div>销售商品开具发票</div>
          </div>
        </div>
      </div>

      <!-- 销售方信息 -->
      <div class="seller-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">销售方信息</h4>
        <div class="grid grid-cols-3 gap-3">
          <div class="info-item">
            <div class="text-gray-500">销售方名称：</div>
            <div>公司形象</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方税号：</div>
            <div>91110105MA01ABCD1X</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方地址：</div>
            <div>北京市朝阳区某某路88号</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方电话：</div>
            <div>010-12345678</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方开户银行：</div>
            <div>中国工商银行</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">销售方银行账号：</div>
            <div>6212 2601 0201 2345 678</div>
          </div>
        </div>
      </div>

      <!-- 购买方信息 -->
      <div class="buyer-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">购买方信息</h4>
        <div class="grid grid-cols-3 gap-3">
          <div class="info-item">
            <div class="text-gray-500">购买方名称：</div>
            <div>武汉科技有限公司</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方税号：</div>
            <div>91110105MA01WXYZ2Y</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方地址：</div>
            <div>北京市海淀区某某街99号</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方电话：</div>
            <div>010-87654321</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方开户银行：</div>
            <div>中国建设银行</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方银行账号：</div>
            <div>6217 0001 8888 8888 888</div>
          </div>
        </div>
      </div>

      <!-- 发票明细 -->
      <div class="goods-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">发票明细</h4>
        <a-table
          :data="detailData.goods || []"
          :pagination="false"
          size="small"
          :bordered="true"
          :stripe="true"
        >
          <template #columns>
            <a-table-column title="序号" :width="60" align="center">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column title="货物或应税劳务服务名称" data-index="name" />
            <a-table-column title="规格型号" data-index="specification" :width="120" />
            <a-table-column title="单位" data-index="unit" :width="60" align="center" />
            <a-table-column title="数量" data-index="quantity" :width="80" align="center" />
            <a-table-column title="单价" data-index="unitPrice" :width="100" align="right">
              <template #cell="{ record }">
                ¥{{ record.unitPrice ? record.unitPrice.toFixed(2) : '1759.20' }}
              </template>
            </a-table-column>
            <a-table-column title="金额" data-index="amount" :width="120" align="right">
              <template #cell="{ record }">
                ¥{{ record.amount ? record.amount.toFixed(2) : '1759.20' }}
              </template>
            </a-table-column>
            <a-table-column title="税率" data-index="taxRate" :width="80" align="center" />
            <a-table-column title="税额" data-index="taxAmount" :width="120" align="right">
              <template #cell="{ record }">
                ¥{{ record.taxAmount ? record.taxAmount.toFixed(2) : '228.70' }}
              </template>
            </a-table-column>
          </template>
        </a-table>
        
        <div class="flex justify-end mt-2">
          <div class="text-right grid grid-cols-1 gap-0">
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">金额合计：</span>
              <span>¥{{ (detailData.totalAmount || 2199.00).toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">税额合计：</span>
              <span>¥{{ (detailData.totalTaxAmount || 285.87).toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">价税合计：</span>
              <span class="text-red-500 font-bold">¥{{ (detailData.amountWithTax || 2484.87).toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">支付方式：</span>
              <span>{{ detailData.paymentMethod || '微信支付' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="log-section mb-4">
        <h4 class="text-base font-medium mb-3 text-blue-500">操作日志</h4>
        <a-timeline>
          <a-timeline-item
            label="2025-05-22 15:40:12"
          >
            系统生成销项发票
          </a-timeline-item>
          <a-timeline-item
            label="2025-05-22 15:45:35"
          >
            发票开具完成
          </a-timeline-item>
        </a-timeline>
      </div>
      
      <div class="flex justify-end">
        <a-button type="primary" @click="onClose">关闭</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconDownload } from "@arco-design/web-vue/es/icon";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    default: () => ({})
  }
});

// 开票状态颜色和文本
const getInvoiceStatusColor = (status) => {
  const statusMap = {
    processing: "blue",
    completed: "green",
    cancelled: "red"
  };
  return statusMap[status] || "gray";
};

const getInvoiceStatusText = (status) => {
  const statusMap = {
    completed: "已开票",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 发票类型颜色和文本
const getInvoiceTypeColor = (type) => {
  const typeMap = {
    blue: "blue",
    red: "red",
    void: "gray"
  };
  return typeMap[type] || "gray";
};

const getInvoiceTypeText = (type) => {
  const typeMap = {
    blue: "蓝字发票",
    red: "红字发票",
    void: "作废发票"
  };
  return typeMap[type] || "未知类型";
};

// 开票方式颜色和文本
const getInvoiceMethodColor = (method) => {
  const methodMap = {
    manual: "orange",
    auto: "green"
  };
  return methodMap[method] || "gray";
};

const getInvoiceMethodText = (method) => {
  const methodMap = {
    manual: "手动上传",
    auto: "一键开票"
  };
  return methodMap[method] || "未知方式";
};

const emit = defineEmits(['update:visible']);

// 关闭模态框
const onClose = () => {
  emit('update:visible', false);
};

// 下载发票
const onDownload = () => {
  Message.success('开始下载发票');
  // 实际下载逻辑应该调用后端API
};
</script>

<style scoped>
.related-invoice-container {
  padding: 0 16px;
}

.info-item {
  display: flex;
  margin-bottom: 2px; /* 缩小行间距 */
}

.info-item .text-gray-500 {
  min-width: 100px;
  flex-shrink: 0;
}

.text-blue-500 {
  color: var(--color-primary-6);
}

.border-gray-200 {
  border-color: var(--color-border-2);
}

.text-red-500 {
  color: var(--color-danger-6);
}

/* 减小标题和内容的间距 */
h4.text-base {
  margin-bottom: 6px;
}

/* 减小各部分之间的间距 */
.mb-4 {
  margin-bottom: 8px;
}

.pb-3 {
  padding-bottom: 8px;
}

/* 减小表格行高 */
:deep(.arco-table-tr) {
  height: 32px;
}

:deep(.arco-table-td) {
  padding-top: 4px;
  padding-bottom: 4px;
}

/* 减小网格间距 */
.gap-3 {
  gap: 0;
}
</style>
