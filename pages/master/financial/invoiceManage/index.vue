<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义列 - 开票状态 -->
      <template #invoiceStatus="{ record }">
        <a-tag :color="record.invoiceStatus === '已开票' ? 'green' : 'orange'">
          {{ record.invoiceStatus }}
        </a-tag>
      </template>
      <!-- 自定义列 - 开票时间 -->
      <template #issueTime="{ record }">
        <div v-time="record.issueTime"></div>
      </template>
      <!-- 自定义操作列 -->
      <template #operation="{ record }">
        <a-button v-if="record.invoiceStatus === '未开票'" type="text" size="small" @click="handleIssue(record)">
          开票
        </a-button>
        <a-button v-if="record.invoiceStatus === '已开票'" type="text" size="small" @click="handleDownload(record)">
          下载发票
        </a-button>
      </template>
    </MaCrud>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconEdit, IconDelete, IconPlus } from '@arco-design/web-vue/es/icon';
// import financialApi from '@/api/master/financial';
// const invoiceApi = financialApi?.invoice || {
//   getList: () => Promise.resolve({ rows: mockData.value, pageInfo: { total: mockData.value.length } }),
//   create: (data) => Promise.resolve(data),
//   update: (data) => Promise.resolve(data),
//   delete: (ids) => Promise.resolve(ids)
// };

definePageMeta({
  name: "master-invoiceManage",
  path: "/master/financial/invoiceManage",
})

const crudRef = ref();

// 模拟数据
const mockData = ref([
  {
    id: 1,
    invoiceTitle: '中国人民银行深圳分行',
    invoiceContent: '电子发票池归集清理费',
    orderNo: '*****************',
    totalAmount: '¥1200.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963422',
    invoiceStatus: '未开票',
    issueTime: '2025-04-02 10:18:28',
    bankInfo: '中国银行',
    receiverInfo: '张三 ***********'
  },
  {
    id: 2,
    invoiceTitle: '深圳市腾讯计算机系统有限公司',
    invoiceContent: '技术服务费',
    orderNo: '*****************',
    totalAmount: '¥3500.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963423',
    invoiceStatus: '未开票',
    issueTime: '2025-04-03 14:25:36',
    bankInfo: '招商银行',
    receiverInfo: '李四 ***********'
  },
  {
    id: 3,
    invoiceTitle: '阿里巴巴（中国）有限公司',
    invoiceContent: '云服务器租赁费',
    orderNo: '*****************',
    totalAmount: '¥2800.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963424',
    invoiceStatus: '已开票',
    issueTime: '2025-04-04 09:32:15',
    bankInfo: '建设银行',
    receiverInfo: '王五 ***********'
  },
  {
    id: 4,
    invoiceTitle: '百度在线网络技术（北京）有限公司',
    invoiceContent: '广告投放服务费',
    orderNo: '*****************',
    totalAmount: '¥5600.00',
    invoiceType: '纸质',
    invoiceNo: '2411200000022963425',
    invoiceStatus: '已开票',
    issueTime: '2025-04-05 16:48:22',
    bankInfo: '工商银行',
    receiverInfo: '赵六 ***********'
  },
  {
    id: 5,
    invoiceTitle: '华为技术有限公司',
    invoiceContent: '设备采购费',
    orderNo: '*****************',
    totalAmount: '¥12800.00',
    invoiceType: '纸质',
    invoiceNo: '2411200000022963426',
    invoiceStatus: '未开票',
    issueTime: '2025-04-06 11:15:43',
    bankInfo: '农业银行',
    receiverInfo: '孙七 ***********'
  },
  {
    id: 6,
    invoiceTitle: '小米科技有限责任公司',
    invoiceContent: '智能硬件采购',
    orderNo: '*****************',
    totalAmount: '¥4300.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963427',
    invoiceStatus: '已开票',
    issueTime: '2025-04-07 13:27:58',
    bankInfo: '交通银行',
    receiverInfo: '周八 ***********'
  },
  {
    id: 7,
    invoiceTitle: '字节跳动有限公司',
    invoiceContent: '短视频推广费',
    orderNo: '*****************',
    totalAmount: '¥7800.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963428',
    invoiceStatus: '未开票',
    issueTime: '2025-04-08 15:36:12',
    bankInfo: '邮政储蓄银行',
    receiverInfo: '吴九 ***********'
  },
  {
    id: 8,
    invoiceTitle: '京东集团',
    invoiceContent: '平台服务费',
    orderNo: '*****************',
    totalAmount: '¥3200.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963429',
    invoiceStatus: '已开票',
    issueTime: '2025-04-09 10:42:37',
    bankInfo: '民生银行',
    receiverInfo: '郑十 ***********'
  },
  {
    id: 9,
    invoiceTitle: '网易（杭州）网络有限公司',
    invoiceContent: '游戏充值服务',
    orderNo: '*****************',
    totalAmount: '¥1800.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963430',
    invoiceStatus: '未开票',
    issueTime: '2025-04-10 09:18:45',
    bankInfo: '浦发银行',
    receiverInfo: '冯十一 ***********'
  },
  {
    id: 10,
    invoiceTitle: '美团点评',
    invoiceContent: '外卖配送服务费',
    orderNo: '*****************',
    totalAmount: '¥960.00',
    invoiceType: '纸质',
    invoiceNo: '2411200000022963431',
    invoiceStatus: '已开票',
    issueTime: '2025-04-11 12:53:29',
    bankInfo: '光大银行',
    receiverInfo: '陈十二 ***********'
  },
  {
    id: 11,
    invoiceTitle: '上海拼多多网络科技有限公司',
    invoiceContent: '平台佣金',
    orderNo: '*****************',
    totalAmount: '¥2400.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963432',
    invoiceStatus: '未开票',
    issueTime: '2025-04-12 14:28:56',
    bankInfo: '兴业银行',
    receiverInfo: '林十三 ***********'
  },
  {
    id: 12,
    invoiceTitle: '北京滴滴出行科技有限公司',
    invoiceContent: '出行服务费',
    orderNo: '*****************',
    totalAmount: '¥520.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963433',
    invoiceStatus: '已开票',
    issueTime: '2025-04-13 16:37:41',
    bankInfo: '中信银行',
    receiverInfo: '黄十四 ***********'
  },
  {
    id: 13,
    invoiceTitle: '深圳市顺丰速运有限公司',
    invoiceContent: '物流配送费',
    orderNo: '*****************',
    totalAmount: '¥780.00',
    invoiceType: '纸质',
    invoiceNo: '2411200000022963434',
    invoiceStatus: '未开票',
    issueTime: '2025-04-14 11:45:23',
    bankInfo: '广发银行',
    receiverInfo: '徐十五 ***********'
  },
  {
    id: 14,
    invoiceTitle: '中国移动通信集团有限公司',
    invoiceContent: '通信服务费',
    orderNo: '*****************',
    totalAmount: '¥399.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963435',
    invoiceStatus: '已开票',
    issueTime: '2025-04-15 09:22:18',
    bankInfo: '平安银行',
    receiverInfo: '朱十六 ***********'
  },
  {
    id: 15,
    invoiceTitle: '中国联合网络通信有限公司',
    invoiceContent: '宽带服务费',
    orderNo: '*****************',
    totalAmount: '¥299.00',
    invoiceType: '电子',
    invoiceNo: '2411200000022963436',
    invoiceStatus: '未开票',
    issueTime: '2025-04-16 13:58:37',
    bankInfo: '招商银行',
    receiverInfo: '秦十七 ***********'
  }
]);

// CRUD 配置
const crud = reactive({
  // 使用模拟数据作为API响应
  api: () => Promise.resolve({ rows: mockData.value, pageInfo: { total: mockData.value.length } }),
  // 实际项目中应该使用真实API
  // api: invoiceApi.getList,
  pageLayout: 'fixed',
  operationColumn: true,
  operationColumnWidth: 150,
  searchLabelWidth: '90px',
  add: { 
    show: false // 不显示新增按钮
  },
  edit: { 
    show: false // 不显示编辑按钮
  },
  delete: { 
    show: false // 不显示删除按钮
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有开票时间参数，转换为毫秒级时间戳
    if(params.issueTime){
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.issueTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startTime = startDate.getTime() / 1000;
      
      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.issueTime[1]);
      endDate.setHours(23, 59, 59, 999);
      params.endTime = endDate.getTime() / 1000;
      
      delete params.issueTime
    }else{
      delete params.startTime
      delete params.endTime
    }
    return params;
  },
});

// 表格列配置
const columns = reactive([
  {
    title: 'ID',
    dataIndex: 'id',
    width: 60,
    addDisplay: false,
    editDisplay: false
  },
  {
    title: '发票抬头',
    dataIndex: 'invoiceTitle',
    search: true,
    commonRules: [{ required: true, message: '发票抬头必填' }]
  },
  {
    title: '发票项目内容',
    dataIndex: 'invoiceContent',
    ellipsis: true
  },
  {
    title: '订单编号',
    dataIndex: 'orderNo',
    search: true
  },
  {
    title: '价税合计',
    dataIndex: 'totalAmount',
    width: 100
  },
  {
    title: '发票类型',
    dataIndex: 'invoiceType',
    width: 80,
    search: true,
    formType: 'select',
    options: [
      { label: '电子', value: '电子' },
      { label: '纸质', value: '纸质' }
    ]
  },
  {
    title: '发票号码',
    dataIndex: 'invoiceNo',
    width: 160
  },
  {
    title: '开票状态',
    dataIndex: 'invoiceStatus',
    width: 100,
    search: true,
    formType: 'select',
    options: [
      { label: '未开票', value: '未开票' },
      { label: '已开票', value: '已开票' }
    ],
    slotName: 'invoiceStatus'
  },
  {
    title: '开票时间',
    dataIndex: 'issueTime',
    width: 140,
    formType: "range",
    search: true,
    addDisplay: false,
    editDisplay: false,
    slotName: 'issueTime'
  },
  {
    title: '银行信息',
    dataIndex: 'bankInfo',
    width: 120
  },
  {
    title: '收票人信息',
    dataIndex: 'receiverInfo',
    width: 150
  }
]);

// 处理开票
function handleIssue(record) {
  Message.info(`开票功能待实现，当前ID：${record.id}`)
}

// 处理下载发票
function handleDownload(record) {
  Message.info(`下载发票功能待实现，当前ID：${record.id}`)
}
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>
