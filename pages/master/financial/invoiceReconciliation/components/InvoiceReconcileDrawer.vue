<template>
  <a-drawer
    :visible="visible"
    :width="1000"
    @cancel="handleClose"
    unmountOnClose
  >
    <template #title>
      <div class="text-lg font-medium">正在为销项发票 {{ invoiceData.invoiceCode }} 进行勾兑</div>
    </template>

    <div class="p-6">
      <!-- 销项发票信息 -->
      <div class="mb-6 bg-gray-50 p-4 rounded-md">
        <div class="grid grid-cols-4 gap-x-6 gap-y-2">
          <div>
            <div class="text-gray-500 text-sm mb-1">发票号码：</div>
            <div class="font-medium">{{ invoiceData.invoiceCode }}</div>
          </div>
          <div>
            <div class="text-gray-500 text-sm mb-1">开票日期：</div>
            <div class="font-medium">{{ invoiceData.invoiceTime }}</div>
          </div>
          <div>
            <div class="text-gray-500 text-sm mb-1">客户名称：</div>
            <div class="font-medium">{{ invoiceData.invoiceTitle }}</div>
          </div>
          <div>
            <div class="text-gray-500 text-sm mb-1">开票金额：</div>
            <div class="font-medium text-red-500">¥{{ (invoiceData.totalAmount || 0).toFixed(2) }}</div>
          </div>
          <div>
            <div class="text-gray-500 text-sm mb-1">已勾兑金额：</div>
            <div class="font-medium text-green-500">¥{{ (invoiceData.reconciledAmount || 0).toFixed(2) }}</div>
          </div>
          <div>
            <div class="text-gray-500 text-sm mb-1">未勾兑金额：</div>
            <div class="font-medium text-orange-500">¥{{ ((invoiceData.totalAmount || 0) - (invoiceData.reconciledAmount || 0)).toFixed(2) }}</div>
          </div>
        </div>
      </div>

      <!-- 销项发票商品明细 -->
      <div class="mb-6">
        <div class="text-base font-medium mb-2">销项发票商品明细</div>
        <a-table
          :columns="goodsColumns"
          :data="goodsData"
          :pagination="false"
          :bordered="{ cell: true }"
          :stripe="true"
          size="mini"
        >
          <template #operation="{ record }">
            <a-button 
              type="primary" 
              size="mini" 
              @click="handleSelectGoodsItem(record)"
            >
              选择
            </a-button>
          </template>
        </a-table>
      </div>

      <!-- 可用进项发票 -->
      <div class="mb-6" v-if="selectedGoodsItem">
        <div class="flex justify-between items-center mb-2">
          <div class="text-base font-medium">
            可用进项发票商品
          </div>
          <div class="flex items-center">
            <a-input-search
              v-model="searchInputInvoice"
              placeholder="输入发票号码搜索"
              search-button
              @search="handleSearch"
              style="width: 250px"
            />
            <a-button type="outline" size="small" class="ml-2" @click="handleReset">重置</a-button>
          </div>
        </div>
        
        <div class="mb-4 bg-blue-50 p-3 rounded-md">
          <div class="font-medium">当前选中的销项商品</div>
          <div class="mt-2 grid grid-cols-5 gap-4 text-sm">
            <div>
              <div class="text-gray-500">商品名称：</div>
              <div>{{ selectedGoodsItem.name }}</div>
            </div>
            <div>
              <div class="text-gray-500">规格型号：</div>
              <div>{{ selectedGoodsItem.model }}</div>
            </div>
            <div>
              <div class="text-gray-500">单位：</div>
              <div>{{ selectedGoodsItem.unit }}</div>
            </div>
            <div>
              <div class="text-gray-500">总数量：</div>
              <div>{{ selectedGoodsItem.quantity }}</div>
            </div>
            <div>
              <div class="text-gray-500">总金额：</div>
              <div class="text-red-500">¥{{ selectedGoodsItem.amount.toFixed(2) }}</div>
            </div>
          </div>
        </div>
        
        <div class="mb-2 text-base font-medium">匹配的进项发票列表 (共{{ inputInvoiceData.length }}张)</div>
        
        <a-table
          :columns="inputInvoiceColumns"
          :data="inputInvoiceData"
          :pagination="false"

          :bordered="{ cell: true }"
          :stripe="true"
          size="mini"
        >
          <template #selection="{ record }">
            <a-checkbox 
              :model-value="selectedRowKeys.includes(record.id)" 
              @change="(checked) => handleCheckboxChange(checked, record)"
            />
          </template>
          <template #reconcileQuantity="{ record }">
            <a-input-number
              v-model="record.reconcileQuantity"
              :min="0"
              :max="record.availableQuantity"
              :disabled="!selectedRowKeys.includes(record.id)"
              @change="(value) => handleQuantityChange(value, record)"
              style="width: 80px"
              size="mini"
            />
          </template>
        </a-table>
      </div>

      <!-- 勾兑信息 -->
      <div class="mb-6 bg-blue-50 p-4 rounded-md">
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div>
            <div class="text-gray-500 text-sm mb-1">已选进项发票：</div>
            <div class="font-medium">{{ selectedInvoices.length }}张</div>
          </div>
          <div>
            <div class="text-gray-500 text-sm mb-1">勾兑金额：</div>
            <div class="font-medium text-blue-600 text-lg">¥{{ totalReconciledAmount.toFixed(2) }}</div>
          </div>
        </div>
        
        <a-alert v-if="showAlert" :type="alertType" :message="alertMessage" class="mb-4" />
        
        <div class="flex justify-end gap-2">
          <a-button @click="handleClose">取消</a-button>
          <a-button type="primary" status="success" @click="handleConfirmReconcile" :disabled="!canReconcile">
            确认勾兑
          </a-button>
        </div>
      </div>
    </div>


  </a-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch, h } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconCheckCircleFill } from '@arco-design/web-vue/es/icon';
import { useInputInvoiceStore } from '~/store/master/financial';

// 接收参数
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  invoiceData: {
    type: Object,
    default: () => ({})
  }
});

// 事件
const emit = defineEmits(['update:visible', 'reconcile-success']);

// 关闭抽屉
const handleClose = () => {
  // 重置状态
  selectedGoodsItem.value = null;
  selectedGoodsKeys.value = [];
  selectedRowKeys.value = [];
  selectedInvoices.value = [];
  inputInvoiceData.value = [];
  searchInputInvoice.value = '';
  showAlert.value = false;
  
  emit('update:visible', false);
};

// 销项发票商品列定义
const goodsColumns = [
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
  },
  {
    title: '商品名称',
    dataIndex: 'name',
    width: 180,
  },
  {
    title: '规格型号',
    dataIndex: 'model',
    width: 120,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 60,
    align: 'center',
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    width: 60,
    align: 'center',
  },
  {
    title: '单价',
    dataIndex: 'price',
    width: 100,
    align: 'right',
    render: ({ record }) => `¥${record.price.toFixed(2)}`,
  },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 120,
    align: 'right',
    render: ({ record }) => `¥${record.amount.toFixed(2)}`,
  },
  {
    title: '操作',
    slotName: 'operation',
    width: 80,
    fixed: 'right',
    align: 'center',
  }
];

// 销项发票商品数据
const goodsData = computed(() => {
  if (!props.invoiceData.id) return [];
  
  // 使用传入的商品明细数据
  if (props.invoiceData.goodsItems && props.invoiceData.goodsItems.length > 0) {
    // 给每个商品项添加序号
    return props.invoiceData.goodsItems.map((item, index) => ({
      ...item,
      index: index + 1
    }));
  }
  
  // 如果没有商品明细，使用默认模拟数据
  return [
    {
      id: '1',
      index: 1,
      name: '机械键盘',
      model: 'G913',
      unit: '个',
      quantity: 1,
      price: 899.00,
      amount: 899.00
    },
    {
      id: '2',
      index: 2,
      name: '无线鼠标',
      model: 'M590',
      unit: '个',
      quantity: 2,
      price: 199.00,
      amount: 398.00
    }
  ];
});

// 选中的销项发票商品项
const selectedGoodsItem = ref(null);

// 销项发票商品选择配置
const selectedGoodsKeys = ref([]);
const goodsRowSelection = reactive({
  type: 'radio',
  selectedRowKeys: selectedGoodsKeys,
  onChange: (rowKeys) => {
    selectedGoodsKeys.value = rowKeys;
    if (rowKeys.length > 0) {
      const selectedItem = goodsData.value.find(item => item.id === rowKeys[0]);
      if (selectedItem) {
        selectedGoodsItem.value = selectedItem;
        // 重置进项发票选择
        selectedRowKeys.value = [];
        rowSelection.onChange([]);
        // 根据选择的商品过滤可用的进项发票
        filterInputInvoiceByGoods(selectedItem);
      }
    } else {
      selectedGoodsItem.value = null;
    }
  }
});

// 选择销项发票商品
const handleSelectGoodsItem = (record) => {
  selectedGoodsKeys.value = [record.id];
  goodsRowSelection.onChange([record.id]);
  Message.success(`已选择商品: ${record.name}`);
};

// 根据选择的商品显示所有可用的进项发票
const filterInputInvoiceByGoods = (goodsItem) => {
  // 如果有关联的进项发票详情，优先使用
  if (props.invoiceData.relatedInputInvoiceDetails && props.invoiceData.relatedInputInvoiceDetails.length > 0) {
    // 如果已经勾兑，显示已勾兑的进项发票
    if (props.invoiceData.isReconciled) {
      inputInvoiceData.value = props.invoiceData.relatedInputInvoiceDetails.map(invoice => ({
        id: invoice.id,
        invoiceCode: invoice.invoiceCode,
        merchantName: invoice.supplierName,
        goodsName: goodsItem.name,
        model: goodsItem.model,
        unit: goodsItem.unit,
        availableQuantity: invoice.reconcileQuantity,
        price: invoice.reconcileAmount / invoice.reconcileQuantity,
        availableAmount: invoice.availableAmount,
        reconcileQuantity: invoice.reconcileQuantity,
        reconcileAmount: invoice.reconcileAmount,
        invoiceTime: invoice.invoiceTime,
        seller: invoice.supplierName
      }));
      return;
    }
  }
  
  // 使用 store 中的进项发票数据
  const inputInvoiceStore = useInputInvoiceStore();
  const storeData = inputInvoiceStore.mockData;
  
  // 过滤出未勾兑或部分勾兑的发票，不根据商品类型过滤
  const availableInvoices = storeData.filter(invoice => {
    // 如果发票未勾兑或部分勾兑，则可用
    return !invoice.isReconciled || 
      (invoice.reconciledAmount && invoice.totalAmount && invoice.reconciledAmount < invoice.totalAmount);
  });
  
  // 将 store 中的数据转换为需要的格式
  const formattedData = availableInvoices.map(invoice => {
    // 计算可用金额
    const availableAmount = invoice.totalAmount - (invoice.reconciledAmount || 0);
    
    // 使用发票的实际单价，而不是商品的单价
    const price = availableAmount / (invoice.productCount || 1);
    
    // 计算可用数量
    const availableQuantity = invoice.productCount || 1;
    
    return {
      id: invoice.id,
      invoiceCode: invoice.invoiceCode,
      merchantName: invoice.merchantName || invoice.invoiceTitle,
      goodsName: invoice.goodsItems && invoice.goodsItems.length > 0 ? invoice.goodsItems[0].name : '商品',
      model: invoice.goodsItems && invoice.goodsItems.length > 0 ? invoice.goodsItems[0].model : '-',
      unit: invoice.goodsItems && invoice.goodsItems.length > 0 ? invoice.goodsItems[0].unit : '件',
      availableQuantity: availableQuantity,
      price: price,
      availableAmount: availableAmount,
      reconcileQuantity: 0,
      reconcileAmount: 0,
      invoiceTime: invoice.invoiceTime,
      seller: invoice.merchantName || invoice.invoiceTitle
    };
  });
  
  // 如果没有可用的发票，使用模拟数据
  if (formattedData.length === 0) {
    const mockData = [];
    
    mockData.push({
      id: 'mock-1',
      invoiceCode: 'JP20250526001',
      merchantName: '北京科技有限公司',
      goodsName: '通用商品',
      model: '-',
      unit: '件',
      availableQuantity: 5,
      price: 200,
      availableAmount: 1000,
      reconcileQuantity: 0,
      reconcileAmount: 0,
      invoiceTime: '2025-05-19',
      seller: '北京科技有限公司'
    });
    
    inputInvoiceData.value = mockData;
  } else {
    inputInvoiceData.value = formattedData;
  }
};

// 重置搜索和筛选
const handleReset = () => {
  searchInputInvoice.value = '';
  if (selectedGoodsItem.value) {
    filterInputInvoiceByGoods(selectedGoodsItem.value);
  }
  Message.info('已重置筛选条件');
};

// 进项发票搜索
const searchInputInvoice = ref('');
const handleSearch = () => {
  // 模拟搜索逻辑
  console.log('搜索进项发票:', searchInputInvoice.value);
  
  if (selectedGoodsItem.value) {
    // 实际应用中，这里应该调用API搜索匹配的进项发票
    // 这里我们简单模拟一下搜索结果
    if (searchInputInvoice.value) {
      const searchText = searchInputInvoice.value.toLowerCase();
      filterInputInvoiceByGoods(selectedGoodsItem.value);
      inputInvoiceData.value = inputInvoiceData.value.filter(item => 
        item.invoiceCode.toLowerCase().includes(searchText) || 
        item.merchantName.toLowerCase().includes(searchText)
      );
    } else {
      // 如果搜索框为空，重新加载所有匹配的进项发票
      filterInputInvoiceByGoods(selectedGoodsItem.value);
    }
  }
};

// 进项发票列定义
const inputInvoiceColumns = [
  {
    title: '选择',
    slotName: 'selection',
    width: 60,
    align: 'center',
  },
  {
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    render: ({ rowIndex }) => rowIndex + 1,
  },
  {
    title: '商品名称',
    dataIndex: 'goodsName',
    width: 120,
  },
  {
    title: '规格型号',
    dataIndex: 'model',
    width: 100,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 60,
    align: 'center',
  },
  {
    title: '可用数量',
    dataIndex: 'availableQuantity',
    width: 80,
    align: 'center',
  },
  {
    title: '单价',
    dataIndex: 'price',
    width: 90,
    align: 'right',
    render: ({ record }) => `¥${record.price.toFixed(2)}`,
  },
  {
    title: '金额',
    dataIndex: 'availableAmount',
    width: 100,
    align: 'right',
    render: ({ record }) => `¥${record.availableAmount.toFixed(2)}`,
  },
  {
    title: '本次勾兑数量',
    slotName: 'reconcileQuantity',
    width: 120,
    align: 'center',
  },
  {
    title: '进项发票号',
    dataIndex: 'invoiceCode',
    width: 150,
  },
  {
    title: '开票日期',
    dataIndex: 'invoiceTime',
    width: 100,
    align: 'center',
  },
  {
    title: '销售方',
    dataIndex: 'seller',
    width: 150,
  }
];

// 进项发票数据，初始为空，等待用户选择销项发票商品后加载
const inputInvoiceData = ref([]);

// 表格选择配置
const selectedRowKeys = ref([]);
const selectedInvoices = ref([]);

const rowSelection = reactive({
  type: 'checkbox',
  showCheckedAll: true,
  selectedRowKeys: selectedRowKeys,
  columnWidth: 60,
  onChange: (rowKeys) => {
    selectedRowKeys.value = rowKeys;
    selectedInvoices.value = inputInvoiceData.value.filter(item => 
      rowKeys.includes(item.id)
    );
    
    // 更新未选中的发票，重置其勾兑数量
    inputInvoiceData.value.forEach(item => {
      if (!rowKeys.includes(item.id)) {
        item.reconcileQuantity = 0;
        item.reconcileAmount = 0;
      } else if (item.reconcileQuantity === 0) {
        // 如果是新选中的且勾兑数量为0，设置默认勾兑数量
        item.reconcileQuantity = item.availableQuantity;
        item.reconcileAmount = item.availableAmount;
      }
    });
  }
});

// 处理复选框变更
const handleCheckboxChange = (checked, record) => {
  const index = selectedRowKeys.value.indexOf(record.id);
  if (checked && index === -1) {
    selectedRowKeys.value.push(record.id);
    // 默认勾兑全部数量
    record.reconcileQuantity = record.availableQuantity;
    record.reconcileAmount = record.availableAmount;
    Message.success(`已选择发票 ${record.invoiceCode}`);
  } else if (!checked && index > -1) {
    selectedRowKeys.value.splice(index, 1);
    // 清空勾兑数量
    record.reconcileQuantity = 0;
    record.reconcileAmount = 0;
    Message.info(`已取消选择发票 ${record.invoiceCode}`);
  }
  
  // 触发选择变更
  rowSelection.onChange(selectedRowKeys.value);
};

// 处理数量变更
const handleQuantityChange = (value, record) => {
  // 计算该发票的勾兑金额（数量 * 单价）
  const reconcileAmount = value * record.price;
  record.reconcileAmount = reconcileAmount;
  
  // 触发选择变更，重新计算总金额
  rowSelection.onChange(selectedRowKeys.value);
  
  // 提示用户已更新勾兑数量
  if (value > 0) {
    Message.info(`已设置发票 ${record.invoiceCode} 勾兑数量为 ${value}`); 
  }
};

// 计算总勾兑金额
const totalReconciledAmount = computed(() => {
  return selectedInvoices.value.reduce((sum, invoice) => {
    // 如果设置了勾兑数量和勾兑金额，使用勾兑金额
    if (invoice.reconcileQuantity > 0 && invoice.reconcileAmount) {
      return sum + invoice.reconcileAmount;
    }
    // 否则使用可用金额
    return sum + invoice.availableAmount;
  }, 0);
});

// 勾兑校验
const showAlert = ref(false);
const alertType = ref('info');
const alertMessage = ref('');
const canReconcile = computed(() => {
  if (!selectedGoodsItem.value || selectedInvoices.value.length === 0) {
    return false;
  }
  
  const remainingAmount = (props.invoiceData.totalAmount || 0) - (props.invoiceData.reconciledAmount || 0);
  
  if (totalReconciledAmount.value > remainingAmount) {
    showAlert.value = true;
    alertType.value = 'warning';
    alertMessage.value = `勾兑金额(¥${totalReconciledAmount.value.toFixed(2)})超过了未勾兑金额(¥${remainingAmount.toFixed(2)})`;
    return false;
  } else if (totalReconciledAmount.value < remainingAmount) {
    showAlert.value = true;
    alertType.value = 'info';
    alertMessage.value = `勾兑金额(¥${totalReconciledAmount.value.toFixed(2)})小于未勾兑金额(¥${remainingAmount.toFixed(2)})，将进行部分勾兑`;
    return true;
  } else {
    showAlert.value = true;
    alertType.value = 'success';
    alertMessage.value = `勾兑金额(¥${totalReconciledAmount.value.toFixed(2)})等于未勾兑金额(¥${remainingAmount.toFixed(2)})，可以完成勾兑`;
    return true;
  }
});

// 监听选择变化
watch(selectedInvoices, () => {
  if (selectedInvoices.value.length === 0) {
    showAlert.value = false;
  }
}, { deep: true });

// 确认勾兑
const handleConfirmReconcile = () => {
  if (!canReconcile.value || !selectedGoodsItem.value) {
    return;
  }
  
  // 构建勾兑数据
  const reconcileData = {
    invoiceId: props.invoiceData.id,
    invoiceCode: props.invoiceData.invoiceCode,
    reconciledAmount: totalReconciledAmount.value,
    goodsItem: {
      id: selectedGoodsItem.value.id,
      name: selectedGoodsItem.value.name,
      model: selectedGoodsItem.value.model,
      quantity: selectedGoodsItem.value.quantity,
      amount: selectedGoodsItem.value.amount
    },
    inputInvoices: selectedInvoices.value.map(item => ({
      id: item.id,
      invoiceCode: item.invoiceCode,
      quantity: item.reconcileQuantity,
      amount: item.reconcileAmount || item.availableAmount
    }))
  };
  
  console.log('勾兑数据:', reconcileData);
  
  // 显示加载状态
  const loadingMessage = Message.loading({
    content: '正在提交勾兑数据...',
    duration: 0
  });
  
  // 模拟API调用
  setTimeout(() => {
    loadingMessage.close();
    Message.success({
      content: '勾兑成功',
      icon: () => h(IconCheckCircleFill, { style: { color: '#00b42a' } })
    });
    emit('reconcile-success', reconcileData);
    handleClose();
  }, 800);
};
</script>

<style scoped>
/* 自定义样式 */
</style>
