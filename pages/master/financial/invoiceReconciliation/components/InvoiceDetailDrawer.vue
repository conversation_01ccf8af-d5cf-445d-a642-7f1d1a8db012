<template>
  <a-drawer
    :visible="visible"
    :width="700"
    @cancel="handleClose"
    @ok="handleClose"
    unmountOnClose
  >
    <template #title>
      <div class="text-lg font-medium">发票勾兑详情</div>
    </template>

    <div class="p-4">
      <!-- 发票信息 -->
      <div class="mb-6">
        <div class="text-lg font-medium mb-4">发票信息</div>
        <div class="grid grid-cols-3 gap-4">
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm mb-1">发票ID</span>
            <span class="text-base">{{ detailData.id || '-' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm mb-1">销项发票</span>
            <span class="text-base">{{ detailData.invoiceTitle || '-' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm mb-1">开票日期</span>
            <span class="text-base">{{ detailData.invoiceTime || '-' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm mb-1">价税合计</span>
            <span class="text-base">¥{{ detailData.totalAmount ? detailData.totalAmount.toFixed(2) : '0.00' }}</span>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm mb-1">勾兑状态</span>
            <a-tag :color="detailData.isReconciled ? 'green' : 'gray'">
              {{ detailData.isReconciled ? '已勾兑' : '未勾兑' }}
            </a-tag>
          </div>
          <div class="flex flex-col">
            <span class="text-gray-500 text-sm mb-1">勾兑金额</span>
            <span :class="getReconciledAmountClass(detailData)">
              ¥{{ (detailData.reconciledAmount || 0).toFixed(2) }}/{{ (detailData.totalAmount || 0).toFixed(2) }}
            </span>
          </div>
        </div>
      </div>

      <!-- 勾兑明细 -->
      <div class="mb-6" v-if="detailData.isReconciled">
        <div class="text-lg font-medium mb-4">勾兑明细</div>
        <a-table
          :columns="reconciledColumns"
          :data="reconciledRecords"
          :pagination="false"
          border
          size="small"
        />
      </div>

      <!-- 操作日志 -->
      <div class="mb-6">
        <div class="text-lg font-medium mb-4">操作日志</div>
        <a-timeline>
          <a-timeline-item>
            <div class="flex items-center">
              <a-avatar :size="24" class="bg-blue-500">
                <icon-file />
              </a-avatar>
              <span class="ml-2">发票上传成功</span>
              <span class="ml-auto text-gray-400 text-sm">{{ detailData.invoiceTime }}</span>
            </div>
          </a-timeline-item>
          <a-timeline-item v-if="detailData.isReconciled">
            <div class="flex items-center">
              <a-avatar :size="24" class="bg-green-500">
                <icon-check />
              </a-avatar>
              <span class="ml-2">发票勾兑完成</span>
              <span class="ml-auto text-gray-400 text-sm">{{ getReconcileTime() }}</span>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <a-button @click="handleClose">关闭</a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, computed } from 'vue';

// 接收参数
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    default: () => ({})
  }
});

// 事件
const emit = defineEmits(['update:visible']);

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false);
};

// 勾兑金额样式
const getReconciledAmountClass = (record) => {
  if (!record.isReconciled) {
    return "text-gray-400";
  } else if (record.reconciledAmount >= record.totalAmount) {
    return "text-green-500 font-medium";
  } else {
    return "text-yellow-500 font-medium";
  }
};

// 获取勾兑时间
const getReconcileTime = () => {
  // 如果有勾兑时间，直接使用
  if (props.detailData.reconciledTime) {
    return props.detailData.reconciledTime;
  }
  
  // 如果没有勾兑时间但有发票时间，模拟一个勾兑时间
  if (props.detailData.invoiceTime) {
    const invoiceDate = new Date(props.detailData.invoiceTime);
    invoiceDate.setDate(invoiceDate.getDate() + 1);
    return invoiceDate.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/\//g, '-');
  }
  return '-';
};

// 勾兑记录列定义
const reconciledColumns = [
  {
    title: '勾兑时间',
    dataIndex: 'reconciledTime',
    width: 180,
  },
  {
    title: '勾兑金额',
    dataIndex: 'amount',
    width: 120,
    render: ({ record }) => `¥${record.amount.toFixed(2)}`,
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    width: 120,
  },
  {
    title: '备注',
    dataIndex: 'remark',
  },
];

// 勾兑记录
const reconciledRecords = computed(() => {
  if (!props.detailData.isReconciled) {
    return [];
  }
  
  // 如果有关联的进项发票详情，使用这些数据
  if (props.detailData.relatedInputInvoiceDetails && props.detailData.relatedInputInvoiceDetails.length > 0) {
    return props.detailData.relatedInputInvoiceDetails.map(invoice => ({
      reconciledTime: getReconcileTime(),
      amount: invoice.reconcileAmount,
      operator: '管理员',
      remark: `与进项发票 ${invoice.invoiceCode}(${invoice.supplierName}) 勾兑`
    }));
  }
  
  // 如果没有关联的进项发票详情，使用默认记录
  return [
    {
      reconciledTime: getReconcileTime(),
      amount: props.detailData.reconciledAmount || 0,
      operator: '管理员',
      remark: '正常勾兑'
    }
  ];
});
</script>

<style scoped>
/* 自定义样式 */
</style>
