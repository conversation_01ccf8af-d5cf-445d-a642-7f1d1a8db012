<template>
  <div class="ma-content-block p-4">
    <ma-crud
      :options="crudOptions"
      :columns="columns"
      :data="mockData"
      ref="crudRef"
      @row-click="handleView"
    >
      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space @click.stop>
          <a-button
            v-if="!record.isReconciled"
            type="primary"
            size="small"
            status="success"
            @click.stop="handleReconcile(record)"
          >
            勾兑
          </a-button>
          <a-tag v-else color="green">已完成</a-tag>
          <a-popconfirm
            content="确定要删除该记录吗？"
            @ok="handleDelete(record)"
          >
            <a-button type="text" status="danger" size="small">
              删除
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>

      <!-- 是否勾兑 -->
      <template #isReconciled="{ record }">
        <a-tag :color="getReconciledColor(record)">
          {{ record.isReconciled ? '是' : '否' }}
        </a-tag>
      </template>
      
      <!-- 勾兑金额 -->
      <template #reconciledAmount="{ record }">
        <span :class="getReconciledAmountClass(record)">
          {{ (record.reconciledAmount || 0).toFixed(2) }}/{{ (record.totalAmount || 0).toFixed(2) }}
        </span>
      </template>
    </ma-crud>
    
    <!-- 发票详情抽屉 -->
    <InvoiceDetailDrawer
      v-model:visible="detailVisible"
      :detailData="currentDetailData"
    />
    
    <!-- 发票勾兑抽屉 -->
    <InvoiceReconcileDrawer
      v-model:visible="reconcileDrawerVisible"
      :invoiceData="currentReconcileData"
      @reconcile-success="handleReconcileSuccess"
    />
    

  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import InvoiceDetailDrawer from "./components/InvoiceDetailDrawer.vue";
import InvoiceReconcileDrawer from "./components/InvoiceReconcileDrawer.vue";
import { useInvoiceReconciliationStore, getInvoiceDetail, reconcileInvoice } from "~/store/master/financial";

// 定义页面路由元信息
definePageMeta({
  name: "invoiceReconciliation",
  path: "/master/financial/invoiceReconciliation",
});

const crudRef = ref();

// 详情抽屉控制
const detailVisible = ref(false);
const currentDetailData = ref({});

// 勾兑抽屉控制
const reconcileDrawerVisible = ref(false);
const currentReconcileData = ref({});

// 勾兑相关状态
const isAdd = ref(false);

// 使用 store 中的数据
const reconciliationStore = useInvoiceReconciliationStore();

// 获取模拟数据
const mockData = computed(() => reconciliationStore.mockData);

// 勾兑状态颜色
const getReconciledColor = (record) => {
  if (record.isReconciled) {
    return "green";
  } else {
    return "gray";
  }
};

// 勾兑金额样式
const getReconciledAmountClass = (record) => {
  if (!record.isReconciled) {
    return "text-gray-400";
  } else if (record.reconciledAmount >= record.totalAmount) {
    return "text-green-500 font-medium";
  } else {
    return "text-yellow-500 font-medium";
  }
};

// 查看详情
const handleView = (record) => {
  currentDetailData.value = record;
  detailVisible.value = true;
};

// 新增勾兑
const handleAdd = () => {
  isAdd.value = true;
  currentReconcileRecord.value = null;
  reconcileForm.invoiceId = '';
  reconcileForm.invoiceCode = '';
  reconcileForm.invoiceTitle = '';
  reconcileForm.totalAmount = 0;
  reconcileForm.reconciledAmount = 0;
  reconcileForm.relatedInputInvoices = '';
  reconcileModalVisible.value = true;
};

// 勾兑操作
const handleReconcile = (record) => {
  currentReconcileData.value = { ...record };
  reconcileDrawerVisible.value = true;
};

// 勾兑成功回调
const handleReconcileSuccess = (reconcileData) => {
  // 调用 store 中的勾兑方法
  const result = reconcileInvoice(reconcileData);
  
  if (result) {
    Message.success('勾兑成功');
    reconcileDrawerVisible.value = false;
    
    // 刷新列表
    if (crudRef.value) {
      crudRef.value.refresh();
    }
  } else {
    Message.error('勾兑失败');
  }
};

// 删除操作
const handleDelete = (record) => {
  // 这里可以添加删除逻辑，但目前 store 中没有提供删除方法
  // 模拟API调用
  setTimeout(() => {
    Message.success('删除成功');
    
    // 刷新列表
    if (crudRef.value) {
      crudRef.value.refresh();
    }
  }, 500);
};

// ma-crud 配置
const crudOptions = reactive({
  // 使用 store 中的方法获取数据
  api(params) {
    console.log('查询参数:', params);
    
    // 调用 store 中的方法获取数据
    return reconciliationStore.getReconciliationData(params);
  },
  
  // 搜索配置
  searchColNumber: 3,
  
  // 表格配置
  showIndex: false,
  
  // 添加按钮
  add: {
    show: true,
    thin: true,
    text: '新增勾兑',
    click: () => {
      handleAdd();
    },
  },
  
  // 搜索前处理参数
  beforeSearch(params) {
    console.log('搜索参数:', params);
    
    // 处理发票时间范围
    if (params.invoiceTime && params.invoiceTime.length > 0) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.invoiceTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startInvoiceTime = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.invoiceTime[1]);
      endDate.setHours(23, 59, 59, 0);
      params.endInvoiceTime = endDate.getTime();

      delete params.invoiceTime;
    } else {
      delete params.startInvoiceTime;
      delete params.endInvoiceTime;
    }
    
    return params;
  },
});

// 表格列定义
const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    width: 80,
    align: "center",
    fixed: "left",
  },
  {
    title: "销项发票号",
    dataIndex: "invoiceCode",
    width: 150,
    search: true,
    formType: "input",
  },
  // {
  //   title: "发票抬头",
  //   dataIndex: "invoiceTitle",
  //   width: 180,
  //   search: true,
  //   formType: "input",
  // },
  {
    title: "关联进项发票号",
    dataIndex: "relatedInputInvoices",
    width: 200,
    ellipsis: true,
    tooltip: true,
  },
  {
    title: "开票日期",
    dataIndex: "invoiceTime",
    width: 170,
    align: "center",
    search: true,
    formType: "range",
  },
  {
    title: "价税合计",
    dataIndex: "totalAmount",
    width: 120,
    align: "right",
    search: true,
    formType: "number",
    render: ({ record }) => `¥${record.totalAmount.toFixed(2)}`,
  },
  {
    title: "是否勾兑",
    dataIndex: "isReconciled",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: [
      { label: "全部", value: "" },
      { label: "是", value: true },
      { label: "否", value: false },
    ] },
    slotName: "isReconciled",
  },
  {
    title: "勾兑金额",
    dataIndex: "reconciledAmount",
    width: 150,
    align: "right",
    slotName: "reconciledAmount",
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 200,
    align: "center",
    fixed: "right",
    slotName: "operation",
  },
]);

// 生命周期钩子
onMounted(() => {
  // 页面加载时可以执行一些初始化操作
});
</script>

<script>
export default { name: "master-financial-invoice-reconciliation" };
</script>

<style scoped>
/* 自定义样式 */
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}
</style>
