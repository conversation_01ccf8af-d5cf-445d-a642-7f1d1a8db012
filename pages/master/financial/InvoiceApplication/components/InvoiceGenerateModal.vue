<template>
  <a-modal
    :visible="modelValue"
    @update:visible="handleVisibleChange"
    title="增值税电子普通发票（预览）"
    :width="900"
    :footer="false"
    unmountOnClose
  >
    <div class="invoice-preview">
      <!-- 发票内容 -->
      <div class="invoice-content">


        <div class="w-full flex justify-start">
          <div class="flex justify-end mb-2">
            <div>开票主体：</div>
            <a-select 
              placeholder="选择销售方模板" 
              style="width: 200px" 
              size="small"
              allow-clear
              @change="handleSellerTemplateChange"
            >
              <a-option v-for="(seller, index) in sellerTemplates" :key="index" :value="index">
                {{ seller.name }}
              </a-option>
            </a-select>
          </div>
        </div>
        <!-- 购买方信息 -->
        <div class="buyer-info border border-gray-300 mb-4">
          <table class="w-full">
            <tbody>
              <tr>
                <td rowspan="4" class="w-10 text-center align-middle border-r border-gray-300">
                  购<br>买<br>方
                </td>
                <td class="w-32 px-2 py-1 border-r border-b border-gray-300 whitespace-nowrap">名称：</td>
                <td class="px-2 py-1 border-b border-gray-300">
                  {{ invoiceData.buyerName }}
                </td>
              </tr>
              <tr>
                <td class="w-32 px-2 py-1 border-r border-b border-gray-300 whitespace-nowrap">纳税人识别号：</td>
                <td class="px-2 py-1 border-b border-gray-300">
                  {{ invoiceData.buyerTaxNumber }}
                </td>
              </tr>
              <tr>
                <td class="w-32 px-2 py-1 border-r border-b border-gray-300 whitespace-nowrap">地址、电话：</td>
                <td class="px-2 py-1 border-b border-gray-300">
                  {{ invoiceData.buyerAddressPhone }}
                </td>
              </tr>
              <tr>
                <td class="w-32 px-2 py-1 border-r border-gray-300 whitespace-nowrap">开户行及账号：</td>
                <td class="px-2 py-1">
                  {{ invoiceData.buyerBankAccount }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 商品信息 -->
        <div class="goods-info mb-4">
          <table class="w-full border border-gray-300">
            <thead>
              <tr class="bg-gray-100">
                <th class="px-2 py-1 border-r border-b border-gray-300 text-center w-10">操作</th>
                <th class="px-2 py-1 border-r border-b border-gray-300 text-center">货物或应税劳务、服务名称</th>
                <th class="px-2 py-1 border-r border-b border-gray-300 text-center">规格型号</th>
                <th class="px-2 py-1 border-r border-b border-gray-300 text-center">单位</th>
                <th class="px-2 py-1 border-r border-b border-gray-300 text-center">数量</th>
                <th class="px-2 py-1 border-r border-b border-gray-300 text-center">单价</th>
                <th class="px-2 py-1 border-r border-b border-gray-300 text-center">金额</th>
                <th class="px-2 py-1 border-r border-b border-gray-300 text-center">税率</th>
                <th class="px-2 py-1 border-b border-gray-300 text-center">税额</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in invoiceItems" :key="index" class="editable-row">
                <td class="px-2 py-1 border-r border-gray-300 text-center">
                  <a-button v-if="isEditable && invoiceItems.length > 1" type="text" size="mini" status="danger" @click="removeItem(index)">
                    <template #icon><icon-delete /></template>
                  </a-button>
                </td>
                <td class="px-2 py-1 border-r border-gray-300">
                  <div class="relative flex items-center">
                    <tax-classification-suggestion
                      v-model="item.name"
                      placeholder="请输入名称"
                      size="small"
                      :readonly="!isEditable"
                      :disabled="!isEditable"
                      :bordered="isEditable"
                      class="edit-field flex-1"
                      @select="handleTaxSuggestionSelect(index, $event)"
                    />
                    <a-button 
                      v-if="isEditable" 
                      type="text" 
                      size="mini" 
                      class="ml-1 flex-shrink-0"
                      @click="openTaxSelector(index)"
                    >
                      <template #icon><icon-search /></template>
                    </a-button>
                  </div>
                </td>
                <td class="px-2 py-1 border-r border-gray-300">
                  <a-input v-model="item.specification" placeholder="规格型号" size="small" :readonly="!isEditable" :disabled="!isEditable" :bordered="isEditable" class="edit-field" />
                </td>
                <td class="px-2 py-1 border-r border-gray-300">
                  <a-input v-model="item.unit" placeholder="单位" size="small" :readonly="!isEditable" :disabled="!isEditable" :bordered="isEditable" class="edit-field" />
                </td>
                <td class="px-2 py-1 border-r border-gray-300">
                  <a-input-number v-model="item.quantity" placeholder="数量" size="small" :min="1" @change="calculateItemAmount(index)" :readonly="!isEditable" :disabled="!isEditable" class="edit-field" />
                </td>
                <td class="px-2 py-1 border-r border-gray-300">
                  <a-input-number v-model="item.price" placeholder="单价" size="small" :min="0" :precision="2" @change="calculateItemAmount(index)" :readonly="!isEditable" :disabled="!isEditable" class="edit-field" />
                </td>
                <td class="px-2 py-1 border-r border-gray-300 text-right">
                  {{ item.amount?.toFixed(2) || '0.00' }}
                </td>
                <td class="px-2 py-1 border-r border-gray-300 text-center">
                  <div class="flex items-center justify-center">
                    <a-input-number 
                      v-model="item.taxRate" 
                      size="small" 
                      style="width: 60px" 
                      :min="0" 
                      :max="100" 
                      :precision="0" 
                      @change="(value) => handleTaxRateChange(index, value)" 
                      :readonly="!isEditable"
                      :disabled="!isEditable"
                      class="edit-field"
                    />
                    <span class="ml-1">%</span>
                  </div>
                </td>
                <td class="px-2 py-1 text-right">
                  {{ item.taxAmount?.toFixed(2) || '0.00' }}
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="6" class="px-2 py-1 border-t border-gray-300 text-right font-bold">合计</td>
                <td class="px-2 py-1 border-t border-r border-gray-300 text-right font-bold">{{ totalAmount.toFixed(2) }}</td>
                <td class="px-2 py-1 border-t border-r border-gray-300"></td>
                <td class="px-2 py-1 border-t border-gray-300 text-right font-bold">{{ totalTax.toFixed(2) }}</td>
              </tr>
              <tr>
                <td colspan="9" class="px-2 py-1 border-t border-gray-300 text-center">
                  <div class="flex justify-center items-center space-x-4">
                    <a-button v-if="isEditable" type="text" size="small" @click="addEmptyRow">
                      <template #icon><icon-plus /></template>
                      添加商品
                    </a-button>
                    <a-tooltip position="top" content="切换编辑/查看模式，编辑模式可以修改商品信息">
                      <a-button 
                        :type="isEditable ? 'primary' : 'outline'" 
                        size="small" 
                        @click="toggleEditMode"
                      >
                        <template #icon>
                          <icon-edit v-if="isEditable" />
                          <icon-eye v-else />
                        </template>
                        {{ isEditable ? '保存' : '编辑' }}
                      </a-button>
                    </a-tooltip>
                  </div>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>

        <!-- 价税合计 -->
        <div class="total-amount border border-gray-300 mb-4 bg-green-50">
          <div class="flex">
            <div class="flex-1 px-2 py-1 border-r border-gray-300">
              价税合计（大写）：{{ amountInWords }}
            </div>
            <div class="w-32 px-2 py-1">
              （小写）：¥{{ totalWithTax.toFixed(2) }}
            </div>
          </div>
        </div>

        <!-- 销售方信息 -->
        <div class="seller-info border border-gray-300 mb-4">
          <table class="w-full">
            <tbody>
              <tr>
                <td rowspan="4" class="w-10 text-center align-middle border-r border-gray-300">
                  销<br>售<br>方
                </td>
                <td class="w-32 px-2 py-1 border-r border-b border-gray-300 whitespace-nowrap">名称：</td>
                <td class="px-2 py-1 border-b border-gray-300">
                  <a-input v-model="invoiceData.sellerName" placeholder="请输入销售方名称" size="small" bordered={false} />
                </td>
                <td rowspan="4" class="w-10 text-center align-middle border-l border-gray-300">
                  备注
                </td>
              </tr>
              <tr>
                <td class="w-32 px-2 py-1 border-r border-b border-gray-300 whitespace-nowrap">纳税人识别号：</td>
                <td class="px-2 py-1 border-b border-gray-300">
                  <a-input v-model="invoiceData.sellerTaxNumber" placeholder="请输入纳税人识别号" size="small" bordered={false} />
                </td>
              </tr>
              <tr>
                <td class="w-32 px-2 py-1 border-r border-b border-gray-300 whitespace-nowrap">地址、电话：</td>
                <td class="px-2 py-1 border-b border-gray-300">
                  <a-input v-model="invoiceData.sellerAddressPhone" placeholder="请输入地址、电话" size="small" bordered={false} />
                </td>
              </tr>
              <tr>
                <td class="w-32 px-2 py-1 border-r border-gray-300 whitespace-nowrap">开户行及账号：</td>
                <td class="px-2 py-1">
                  <a-input v-model="invoiceData.sellerBankAccount" placeholder="请输入开户行及账号" size="small" bordered={false} />
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 底部信息 -->
        <div class="invoice-footer">
          <div class="flex justify-between">
            <div>收款人：</div>
            <div>复核：</div>
            <div>开票人：</div>
            <div>销售方：（章）</div>
          </div>
        </div>
      </div>

      <!-- 按钮区域 -->
      <div class="flex justify-center mt-4 space-x-4">
        <a-button type="primary" @click="handleConfirm">
          确认开票
        </a-button>
        <a-button @click="handleCancel">
          取消
        </a-button>
      </div>
    </div>
    
    <!-- 税收分类选择器 -->
    <TaxClassificationSelector
      v-model="showTaxSelector"
      @select="handleTaxClassificationSelect"
    />
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { getSellerTemplates } from '@/store/master/financial';
import useMainMaintenanceStore from '@/store/master/financial/mainMaintenance';
import TaxClassificationSelector from './TaxClassificationSelector.vue';
import TaxClassificationSuggestion from './TaxClassificationSuggestion.vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

// 发票基本信息
// 编辑模式
const isEditable = ref(false);

// 销售方模板从store中获取
const mainMaintenanceStore = useMainMaintenanceStore();
const sellerTemplates = computed(() => {
  return mainMaintenanceStore.mockData.map(item => ({
    name: item.name,
    taxNumber: item.taxId,
    addressPhone: `${item.address} ${item.phone}`,
    bankAccount: `${item.bank} ${item.bankAccount}`
  }));
});

const invoiceData = ref({
  buyerName: props.detailData.buyerName || '某某公司',
  buyerTaxNumber: props.detailData.taxNumber || '91440300MA5EYKUW1X',
  buyerAddressPhone: props.detailData.buyerAddress || '广东省深圳市南山区科技园 0755-********',
  buyerBankAccount: props.detailData.buyerBank ? `${props.detailData.buyerBank} ${props.detailData.buyerBankAccount || ''}` : '中国工商银行深圳分行 622********9012345',
  sellerName: '',
  sellerTaxNumber: '',
  sellerAddressPhone: '',
  sellerBankAccount: ''
});

// 发票明细项
const invoiceItems = ref([]);

// 税收分类选择器
const showTaxSelector = ref(false);
const currentEditingIndex = ref(null);

// 初始化发票明细项
const initInvoiceItems = () => {
  // 如果有商品数据，则使用商品数据初始化
  if (props.detailData.goods && props.detailData.goods.length > 0) {
    invoiceItems.value = props.detailData.goods.map(item => ({
      name: item.name,
      specification: item.specification || '',
      unit: item.unit || '个',
      quantity: item.quantity || 1,
      price: item.price || 0,
      amount: item.amount || 0,
      taxRate: 13, // 默认13%
      taxAmount: item.amount ? item.amount * 0.13 : 0
    }));
  }
  
  // 添加一个空行用于新增
  addEmptyRow();
};

// 添加空行
const addEmptyRow = () => {
  invoiceItems.value.push({
    name: '',
    specification: '',
    unit: '',
    quantity: null,
    price: null,
    amount: 0,
    taxRate: 13,
    taxAmount: 0
  });
};

// 删除行
const removeItem = (index) => {
  invoiceItems.value.splice(index, 1);
};

// 计算单项金额
const calculateItemAmount = (index) => {
  const item = invoiceItems.value[index];
  if (item.quantity && item.price) {
    item.amount = item.quantity * item.price;
    calculateItemTax(index);
  }
  
  // 检查是否需要添加新行
  checkAddNewRow();
};

// 计算单项税额
const calculateItemTax = (index) => {
  const item = invoiceItems.value[index];
  if (item.amount) {
    item.taxAmount = item.amount * (item.taxRate / 100);
  }
};

// 处理税率变化
const handleTaxRateChange = (index, value) => {
  const item = invoiceItems.value[index];
  item.taxRate = value;
  calculateItemTax(index);
};

// 检查是否需要添加新行
const checkAddNewRow = () => {
  const lastItem = invoiceItems.value[invoiceItems.value.length - 1];
  if (lastItem && lastItem.name && lastItem.quantity && lastItem.price) {
    addEmptyRow();
  }
};

// 计算总金额
const totalAmount = computed(() => {
  return invoiceItems.value.reduce((sum, item) => sum + (item.amount || 0), 0);
});

// 计算总税额
const totalTax = computed(() => {
  return invoiceItems.value.reduce((sum, item) => sum + (item.taxAmount || 0), 0);
});

// 计算价税合计
const totalWithTax = computed(() => {
  return totalAmount.value + totalTax.value;
});

// 金额转大写
const amountInWords = computed(() => {
  const digitUppercase = (n) => {
    const fraction = ['角', '分'];
    const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const unit = [
      ['元', '万', '亿'],
      ['', '拾', '佰', '仟']
    ];
    
    n = Math.abs(n);
    let s = '';
    
    for (let i = 0; i < fraction.length; i++) {
      s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }
    
    s = s || '整';
    n = Math.floor(n);
    
    for (let i = 0; i < unit[0].length && n > 0; i++) {
      let p = '';
      for (let j = 0; j < unit[1].length && n > 0; j++) {
        p = digit[n % 10] + unit[1][j] + p;
        n = Math.floor(n / 10);
      }
      s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }
    
    return s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
  };
  
  return digitUppercase(totalWithTax.value);
});

// 确认开票
const handleConfirm = () => {
  // 过滤掉空行
  const validItems = invoiceItems.value.filter(item => item.name && item.quantity && item.price);
  
  emit('confirm', {
    invoiceData: invoiceData.value,
    invoiceItems: validItems,
    totalAmount: totalAmount.value,
    totalTax: totalTax.value,
    totalWithTax: totalWithTax.value
  });
  
  emit('update:visible', false);
};

// 取消
const handleCancel = () => {
  emit('update:visible', false);
};

// 处理弹窗可见性变化
const handleVisibleChange = (visible) => {
  emit('update:visible', visible);
};

// 切换编辑模式
const toggleEditMode = () => {
  isEditable.value = !isEditable.value;
  // 当切换到编辑模式时显示提示
  if (isEditable.value) {
    Message.info('已切换到编辑模式，可以修改商品信息');
  } else {
    Message.info('已切换到查看模式，商品信息不可编辑');
  }
};



// 处理销售方模板选择
const handleSellerTemplateChange = (index) => {
  if (index !== undefined && index !== null) {
    const template = sellerTemplates.value[index];
    invoiceData.value.sellerName = template.name;
    invoiceData.value.sellerTaxNumber = template.taxNumber;
    invoiceData.value.sellerAddressPhone = template.addressPhone;
    invoiceData.value.sellerBankAccount = template.bankAccount;
  }
};

// 打开税收分类选择器
const openTaxSelector = (index) => {
  currentEditingIndex.value = index;
  showTaxSelector.value = true;
};

// 处理税收分类选择
const handleTaxClassificationSelect = (taxItem) => {
  if (currentEditingIndex.value !== null) {
    // 更新商品名称为选择的税收分类名称
    invoiceItems.value[currentEditingIndex.value].name = taxItem.label;
    // 可以保存税收分类的编码等信息，用于后续处理
    invoiceItems.value[currentEditingIndex.value].taxCode = taxItem.codeid;
    invoiceItems.value[currentEditingIndex.value].taxNumber = taxItem.num;
    invoiceItems.value[currentEditingIndex.value].taxPath = taxItem.path;
    
    // 提示用户选择成功
    Message.success(`已选择税收分类：${taxItem.label}`);
    console.log("已选择税收分类：", taxItem);
  }
};

// 处理税收分类建议选择
const handleTaxSuggestionSelect = (index, taxItem) => {
  // 设置当前编辑的索引
  currentEditingIndex.value = index;
  
  // 更新商品名称为选择的税收分类名称
  invoiceItems.value[index].name = taxItem.label;
  // 保存税收分类的编码等信息
  invoiceItems.value[index].taxCode = taxItem.codeid;
  invoiceItems.value[index].taxNumber = taxItem.num;
  invoiceItems.value[index].taxPath = taxItem.path;
  
  // 提示用户选择成功
  Message.success(`已选择税收分类：${taxItem.label}`);
  console.log("已选择税收分类：", taxItem);
};

// 监听modelValue变化，初始化数据
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    initInvoiceItems();
  }
});
</script>

<style scoped>
.invoice-preview {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0 16px;
}

.editable-row :deep(.arco-input),
.editable-row :deep(.arco-input-number),
.editable-row :deep(.arco-select) {
  border: none;
  border-radius: 0;
  padding: 2px 4px;
}

.editable-row :deep(.arco-input:hover),
.editable-row :deep(.arco-input-number:hover),
.editable-row :deep(.arco-select:hover) {
  background-color: #f9f9f9;
}

/* 编辑字段样式 */
.edit-field {
  width: 100%;
}

.edit-field:deep(.arco-input-disabled),
.edit-field:deep(.arco-input-number-disabled) {
  background-color: transparent;
  color: var(--color-text-1);
  cursor: default;
}

.edit-field:deep(.arco-input-disabled),
.edit-field:deep(.arco-input-number-disabled) .arco-input-number-input {
  -webkit-text-fill-color: var(--color-text-1);
  opacity: 1;
}

.edit-field:deep(.arco-input-number-disabled) .arco-input-number-step {
  display: none;
}
</style>
