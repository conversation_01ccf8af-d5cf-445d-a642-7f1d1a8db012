<template>
  <a-modal
    :visible="modelValue"
    @update:visible="handleVisibleChange"
    title="税收分类选择"
    :width="800"
    :footer="false"
    unmountOnClose
  >
    <div class="tax-classification-selector">
      <div class="flex">
        <!-- 左侧分类树 -->
        <div class="w-1/2 pr-4 border-r border-gray-200">
          <div class="tree-container max-h-96 overflow-y-auto">
            <a-tree
              :data="treeData"
              :default-expanded-keys="defaultExpandedKeys"
              :field-names="{
                key: 'codeid',
                title: 'label',
                children: 'children'
              }"
              @select="handleSelect"
              :show-line="true"
              :auto-expand-parent="true"
            />
          </div>
        </div>
        
        <!-- 右侧选择详情 -->
        <div class="w-1/2 pl-4">
          <div v-if="selectedNode" class="selected-info">
            <h3 class="text-lg font-medium mb-4">已选择分类</h3>
            
            <div class="info-item mb-2">
              <span class="text-gray-500">分类名称：</span>
              <span class="font-medium">{{ selectedNode.label }}</span>
            </div>
            
            <div class="info-item mb-2">
              <span class="text-gray-500">分类编码：</span>
              <span class="font-medium">{{ selectedNode.codeid }}</span>
            </div>
            
            <div class="info-item mb-2">
              <span class="text-gray-500">完整编码：</span>
              <span class="font-medium">{{ selectedNode.num }}</span>
            </div>
            
            <div class="info-item mb-4">
              <span class="text-gray-500">上级分类：</span>
              <span class="font-medium">{{ parentNodeName }}</span>
            </div>
            
            <div class="path-info p-3 bg-gray-50 rounded mb-4">
              <div class="text-gray-500 mb-1">分类路径：</div>
              <div class="text-sm">
                <template v-for="(item, index) in nodePath" :key="index">
                  <span v-if="index > 0" class="mx-1 text-gray-400">/</span>
                  <span>{{ item.label }}</span>
                </template>
              </div>
            </div>
          </div>
          
          <div v-else class="flex items-center justify-center h-64 text-gray-400">
            请从左侧选择一个税收分类
          </div>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="flex justify-end mt-6 space-x-4">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :disabled="!selectedNode" @click="handleConfirm">
          确认选择
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useInvoiceReconciliationStore } from '@/store/master/financial/invoiceReconciliation';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'select']);

// 获取税收分类数据
const invoiceReconciliationStore = useInvoiceReconciliationStore();
const taxClassification = computed(() => invoiceReconciliationStore.taxClassification);

// 选中的节点
const selectedNode = ref(null);
const nodePath = ref([]);

// 默认展开的节点
const defaultExpandedKeys = ref(['1']); // 默认展开"货物"节点

// 构建树形数据
const treeData = computed(() => {
  return taxClassification.value?.slide_data || [];
});



// 获取父节点名称
const parentNodeName = computed(() => {
  if (!selectedNode.value || !selectedNode.value.codepid || selectedNode.value.codepid === '0') {
    return '无';
  }
  
  // 查找父节点
  const findParent = (nodes, pid) => {
    for (const node of nodes) {
      if (node.codeid === pid) {
        return node;
      }
      
      if (node.children && node.children.length) {
        const found = findParent(node.children, pid);
        if (found) return found;
      }
    }
    
    return null;
  };
  
  const parent = findParent(treeData.value, selectedNode.value.codepid);
  return parent ? parent.label : '无';
});

// 处理选择节点
const handleSelect = (selectedKeys, { selectedNodes }) => {
  if (selectedKeys.length === 0 || !selectedNodes || selectedNodes.length === 0) {
    selectedNode.value = null;
    nodePath.value = [];
    return;
  }
  
  selectedNode.value = selectedNodes[0];
  
  // 计算节点路径
  const path = [];
  const findPath = (nodes, targetId, currentPath = []) => {
    for (const node of nodes) {
      const newPath = [...currentPath, node];
      
      if (node.codeid === targetId) {
        return newPath;
      }
      
      if (node.children && node.children.length) {
        const found = findPath(node.children, targetId, newPath);
        if (found) return found;
      }
    }
    
    return null;
  };
  
  nodePath.value = findPath(treeData.value, selectedNode.value.codeid) || [];
};

// 确认选择
const handleConfirm = () => {
  if (selectedNode.value) {
    // 剔除 children 属性
    const { children, ...nodeWithoutChildren } = selectedNode.value;
    
    emit('select', {
      ...nodeWithoutChildren,
      path: nodePath.value.map(item => item.label).join(' / ')
    });
  }
  
  emit('update:modelValue', false);
};

// 取消选择
const handleCancel = () => {
  emit('update:modelValue', false);
};

// 处理弹窗可见性变化
const handleVisibleChange = (visible) => {
  emit('update:modelValue', visible);
  
  // 重置选择状态
  if (!visible) {
    selectedNode.value = null;
    nodePath.value = [];
    searchKeyword.value = '';
  }
};
</script>

<style scoped>
.tax-classification-selector {
  max-height: 70vh;
  overflow-y: auto;
}

.tree-container :deep(.arco-tree-node-title) {
  white-space: normal;
  word-break: break-all;
}

.info-item {
  display: flex;
}

.info-item .text-gray-500 {
  width: 80px;
  flex-shrink: 0;
}
</style>
