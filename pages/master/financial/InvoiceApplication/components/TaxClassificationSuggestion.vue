<template>
  <div class="tax-suggestion">
    <a-input 
      :model-value="modelValue" 
      :placeholder="placeholder" 
      :size="size" 
      :readonly="readonly" 
      :disabled="disabled" 
      :bordered="bordered" 
      class="suggestion-input"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
    />
    
    <div v-if="showSuggestions && suggestions.length > 0" class="suggestion-list">
      <a-list size="small" :bordered="false">
        <a-list-item 
          v-for="(item, index) in suggestions" 
          :key="index"
          class="suggestion-item"
          @click="selectSuggestion(item)"
        >
          <div class="suggestion-content">
            <div class="suggestion-title">{{ item.label }}</div>
            <div class="suggestion-info">
              <div class="suggestion-code">
                <span class="text-gray-500">编码：</span>{{ item.codeid }}
              </div>
              <div class="suggestion-path">
                <span class="text-gray-500">路径：</span>{{ item.path }}
              </div>
            </div>
          </div>
        </a-list-item>
      </a-list>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, computed } from 'vue';
import { useInvoiceReconciliationStore } from '@/store/master/financial/invoiceReconciliation';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入名称'
  },
  size: {
    type: String,
    default: 'small'
  },
  readonly: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  bordered: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'select']);

// 获取税收分类数据
const invoiceReconciliationStore = useInvoiceReconciliationStore();

// 搜索建议
const suggestions = ref([]);
const showSuggestions = ref(false);
const searchTimeout = ref(null);

// 处理输入
const handleInput = (value) => {
  // 使用 update:modelValue 事件更新父组件中的值
  emit('update:modelValue', value);
  
  // 清除之前的定时器
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }
  
  // 设置新的定时器，防止频繁搜索
  searchTimeout.value = setTimeout(() => {
    if (value && value.trim() !== '') {
      // 搜索税收分类
      suggestions.value = invoiceReconciliationStore.searchTaxClassification(value);
      showSuggestions.value = true;
    } else {
      suggestions.value = [];
      showSuggestions.value = false;
    }
  }, 300);
};

// 处理获取焦点
const handleFocus = () => {
  if (props.modelValue && props.modelValue.trim() !== '') {
    suggestions.value = invoiceReconciliationStore.searchTaxClassification(props.modelValue);
    showSuggestions.value = true;
  }
};

// 处理失去焦点
const handleBlur = () => {
  // 延迟隐藏建议列表，以便用户可以点击建议项
  setTimeout(() => {
    showSuggestions.value = false;
  }, 200);
};

// 选择建议
const selectSuggestion = (item) => {
  emit('update:modelValue', item.label);
  
  // 创建一个新对象，剔除 children 属性
  const { children, ...itemWithoutChildren } = item;
  
  // 发送剔除了 children 属性的数据
  emit('select', itemWithoutChildren);
  showSuggestions.value = false;
};

// 点击外部关闭建议列表
const handleClickOutside = (event) => {
  const element = event.target;
  if (!element.closest('.tax-suggestion')) {
    showSuggestions.value = false;
  }
};

// 监听点击事件
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

// 移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.tax-suggestion {
  position: relative;
  width: 100%;
}

.suggestion-list {
  position: absolute;
  top: 100%;
  left: 0;
  width: 300px; /* 增加宽度 */
  max-height: 300px; /* 增加高度 */
  overflow-y: auto;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.suggestion-item {
  cursor: pointer;
  padding: 6px 12px;
}

.suggestion-item:hover {
  background-color: #f3f4f6;
}

.suggestion-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.suggestion-title {
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1.4;
  word-break: break-word; /* 允许在任意字符间断行 */
}

.suggestion-info {
  display: flex;
  flex-direction: column; /* 改为纵向排列 */
  font-size: 12px;
  color: #6b7280;
}

.suggestion-code {
  margin-bottom: 2px;
}

.suggestion-path {
  word-break: break-word; /* 允许在任意字符间断行 */
  line-height: 1.3;
}
</style>
