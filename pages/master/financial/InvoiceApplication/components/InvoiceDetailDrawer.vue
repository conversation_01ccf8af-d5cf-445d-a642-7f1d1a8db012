<template>
  <a-drawer
    :width="'65%'"
    :visible="visible"
    @cancel="onClose"
    unmountOnClose
  >
    <template #title>
      发票申请详情
    </template>

    <div class="invoice-detail-container">
      <!-- 订单信息 -->
      <div class="order-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">订单信息</h4>
        <div class="grid grid-cols-3 gap-3">
          <div class="info-item">
            <div class="text-gray-500">订单编号：</div>
            <div>{{ detailData.orderNumber || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">渠道订单编号：</div>
            <div>{{ detailData.channelOrderNumber || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">订单来源：</div>
            <div>{{ detailData.orderSource || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">下单时间：</div>
            <div>{{ detailData.orderTime || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">支付时间：</div>
            <div>{{ detailData.payTime || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">开票申请时间：</div>
            <div>{{ detailData.invoiceApplyTime || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">订单类型：</div>
            <div>{{ detailData.orderType || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">订单状态：</div>
            <div>
              <a-tag :color="getOrderStatusColor(detailData.orderStatus)">
                {{ getOrderStatusText(detailData.orderStatus) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">发票状态：</div>
            <div>
              <a-tag :color="getApplyStatusColor(detailData.applyStatus)">
                {{ getApplyStatusText(detailData.applyStatus) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item col-span-3">
            <div class="text-gray-500">订单备注：</div>
            <div>{{ detailData.orderRemark || '无' }}</div>
          </div>
        </div>
      </div>
      <!-- 商品信息 -->
      <div class="goods-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">商品信息</h4>
        <a-table
          :data="detailData.goods || []"
          :pagination="false"
          size="small"
          :bordered="true"
          :stripe="true"
        >
          <template #columns>
            <a-table-column title="商品图片" data-index="image" :width="120" align="center">
              <template #cell="{ record }">
                <a-image
                  :src="record.image"
                  width="50"
                  height="50"
                  fit="cover"
                  class="rounded"
                />
              </template>
            </a-table-column>
            <a-table-column title="商品名称" data-index="name" />
            <a-table-column title="商家" data-index="merchant" :width="160" />
            <a-table-column title="单价" data-index="price" :width="100" align="right">
              <template #cell="{ record }">
                ¥{{ record.price.toFixed(2) }}
              </template>
            </a-table-column>
            <a-table-column title="数量" data-index="quantity" :width="80" align="center" />
            <a-table-column title="参考金额" data-index="amount" :width="120" align="right">
              <template #cell="{ record }">
                ¥{{ record.amount.toFixed(2) }}
              </template>
            </a-table-column>
          </template>
        </a-table>
        
        <div class="flex justify-end mt-2">
          <div class="text-right grid grid-cols-1 gap-0">
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">商品金额：</span>
              <span class="font-medium">¥{{ calculateTotalAmount().toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">运费金额：</span>
              <span class="font-medium">¥{{ detailData.shippingFee?.toFixed(2) || '0.00' }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">优惠金额：</span>
              <span class="font-medium">¥{{ detailData.discountAmount?.toFixed(2) || '0.00' }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">支付金额：</span>
              <span class="font-medium text-red-500">¥{{ calculatePayAmount().toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">支付方式：</span>
              <span>{{ detailData.paymentMethod || '微信支付' }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- 发票信息 -->
      <div class="invoice-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">发票信息</h4>
        <div class="grid grid-cols-3 gap-3">
          <div class="info-item">
            <div class="text-gray-500">申请来源：</div>
            <div>{{ detailData.applySource || '下单' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">申请发票类型：</div>
            <div>{{ detailData.invoiceType || '普通发票' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">发票内容：</div>
            <div>{{ detailData.invoiceContent || '明细' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">抬头名称：</div>
            <div>{{ detailData.invoiceTitle || '个人' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">纳税人识别号：</div>
            <div>{{ detailData.taxNumber || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方注册地址：</div>
            <div>{{ detailData.buyerAddress || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方注册电话：</div>
            <div>{{ detailData.buyerPhone || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方开户银行：</div>
            <div>{{ detailData.buyerBank || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">购买方银行账号：</div>
            <div>{{ detailData.buyerBankAccount || '-' }}</div>
          </div>
        </div>
      </div>
      <!-- 发票详情 -->
      <div class="invoice-detail-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">发票详情</h4>
        <a-table
          :data="detailData.goods || []"
          :pagination="false"
          size="small"
          :bordered="true"
          :stripe="true"
        >
          <template #columns>
            <a-table-column title="货物或应税劳务服务名称" data-index="name" />
            <a-table-column title="规格型号" data-index="specification" :width="120" />
            <a-table-column title="单位" data-index="unit" :width="60" align="center" />
            <a-table-column title="数量" data-index="quantity" :width="80" align="center" />
            <a-table-column title="单价" data-index="price" :width="100" align="right">
              <template #cell="{ record }">
                ¥{{ record.price.toFixed(2) }}
              </template>
            </a-table-column>
            <a-table-column title="金额" data-index="amount" :width="100" align="right">
              <template #cell="{ record }">
                ¥{{ record.amount.toFixed(2) }}
              </template>
            </a-table-column>
            <a-table-column title="税率" data-index="taxRate" :width="80" align="center">
              <template #cell="{ record }">
                {{ record.taxRate || '13%' }}
              </template>
            </a-table-column>
            <a-table-column title="税额" data-index="taxAmount" :width="100" align="right">
              <template #cell="{ record }">
                ¥{{ calculateTaxAmount(record).toFixed(2) }}
              </template>
            </a-table-column>
          </template>
        </a-table>
        
        <div class="flex justify-end mt-2">
          <div class="text-right grid grid-cols-1 gap-0">
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">商品金额：</span>
              <span class="font-medium">¥{{ calculateTotalAmount().toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">税额合计：</span>
              <span class="font-medium">¥{{ calculateTotalTax().toFixed(2) }}</span>
            </div>
            <div class="info-item justify-end">
              <span class="text-gray-500 mr-4">价税合计：</span>
              <span class="font-medium text-red-500">¥{{ (calculateTotalAmount() + calculateTotalTax()).toFixed(2) }}</span>
            </div>
          </div>
        </div>
        
        <div class="flex justify-center mt-4 space-x-4">
          <a-button type="primary">
            <template #icon>
              <icon-upload />
            </template>
            上传发票
          </a-button>
          <a-button type="primary" status="success" @click="invoiceGenerateVisible = true">
            <template #icon>
              <icon-file />
            </template>
            一键开票
          </a-button>
        </div>
        
        <!-- 一键开票弹窗 -->
        <invoice-generate-modal
          v-model:visible="invoiceGenerateVisible"
          :detail-data="detailData"
          @confirm="handleInvoiceGenerate"
        />
        
        <!-- <div class="flex justify-end mt-4">
          <div class="text-right">
            <div class="mb-2">
              <span class="text-gray-500 mr-4">商品金额：</span>
              <span class="font-medium">¥{{ calculateTotalAmount().toFixed(2) }}</span>
            </div>
            <div class="mb-2">
              <span class="text-gray-500 mr-4">税额合计：</span>
              <span class="font-medium">¥{{ calculateTotalTax().toFixed(2) }}</span>
            </div>
            <div class="mb-2">
              <span class="text-gray-500 mr-4">优惠金额：</span>
              <span class="font-medium">¥{{ detailData.discountAmount?.toFixed(2) || '0.00' }}</span>
            </div>
            <div>
              <span class="text-gray-500 mr-4">发票金额：</span>
              <span class="text-lg font-medium text-red-500">¥{{ calculateInvoiceAmount().toFixed(2) }}</span>
            </div>
          </div>
        </div> -->
      </div>


      <!-- 收票人信息 -->
      <div class="receiver-info-section mb-4 pb-3 border-b border-gray-200">
        <h4 class="text-base font-medium mb-3 text-blue-500">收票人信息</h4>
        
        <div v-if="detailData.receiveType === 'email'" class="grid grid-cols-3 gap-0">
          <div class="info-item">
            <div class="text-gray-500">电子发票：</div>
            <div>是</div>
          </div>
          <div class="info-item col-span-2">
            <div class="text-gray-500">E-Mail：</div>
            <div>{{ detailData.email || '-' }}</div>
          </div>
        </div>
        
        <div v-if="detailData.receiveType === 'express'" class="grid grid-cols-3 gap-0">
          <div class="info-item">
            <div class="text-gray-500">线下快递：</div>
            <div>是</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">收件人姓名：</div>
            <div>{{ detailData.receiverName || '-' }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">联系电话：</div>
            <div>{{ detailData.receiverPhone || '-' }}</div>
          </div>
          <div class="info-item col-span-3">
            <div class="text-gray-500">收件地址：</div>
            <div>{{ detailData.receiverAddress || '-' }}</div>
          </div>
        </div>
      </div>
      
      <!-- 订单日志 -->
      <div class="order-log-section mb-4">
        <h4 class="text-base font-medium mb-3 text-blue-500">订单日志</h4>
        <a-timeline>
          <a-timeline-item v-for="(log, index) in orderLogs" :key="index">
            <div class="flex justify-between">
              <span>{{ log.content }}</span>
              <span class="text-gray-500">{{ log.time }}</span>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
      
      <!-- 操作按钮 -->
      <div class="action-buttons mt-4 flex justify-end">
        <a-space>
          <a-button @click="onSync" type="primary">同步至销项</a-button>
          <a-button @click="onIgnore">忽略提醒</a-button>
          <a-button @click="onClose">关闭</a-button>
        </a-space>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import InvoiceGenerateModal from './InvoiceGenerateModal.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  detailData: {
    type: Object,
    default: () => ({})
  }
});

// 订单状态颜色和文本
const getOrderStatusColor = (status) => {
  const statusMap = {
    processing: "blue",
    completed: "green",
    cancelled: "red"
  };
  return statusMap[status] || "gray";
};

const getOrderStatusText = (status) => {
  const statusMap = {
    processing: "处理中",
    completed: "已完成",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 申请状态颜色和文本
const getApplyStatusColor = (status) => {
  const statusMap = {
    pending: "orange",
    approved: "green",
    rejected: "red",
    completed: "blue",
    cancelled: "gray"
  };
  return statusMap[status] || "gray";
};

const getApplyStatusText = (status) => {
  const statusMap = {
    pending: "待审核",
    approved: "已审核",
    rejected: "已拒绝",
    completed: "已开票",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 模拟订单日志数据
const orderLogs = [
  {
    content: "用户提交发票申请",
    time: "2025-05-28 09:15:30"
  },
  {
    content: "系统自动审核通过",
    time: "2025-05-28 09:15:35"
  },
  {
    content: "财务人员确认申请信息",
    time: "2025-05-28 10:20:15"
  },
  {
    content: "发票开具完成",
    time: "2025-05-28 10:45:22"
  }
];

const emit = defineEmits(['update:visible', 'sync', 'ignore']);

// 一键开票弹窗
const invoiceGenerateVisible = ref(false);

// 计算商品总金额
const calculateTotalAmount = () => {
  if (!props.detailData.goods || !Array.isArray(props.detailData.goods)) {
    return 0;
  }
  return props.detailData.goods.reduce((sum, item) => sum + (item.amount || 0), 0);
};

// 计算单个商品的税额
const calculateTaxAmount = (record) => {
  const amount = record.amount || 0;
  const taxRateStr = record.taxRate || '13%';
  const taxRate = parseFloat(taxRateStr) / 100;
  return amount * taxRate;
};

// 计算税额合计
const calculateTotalTax = () => {
  if (!props.detailData.goods || !Array.isArray(props.detailData.goods)) {
    return 0;
  }
  return props.detailData.goods.reduce((sum, item) => sum + calculateTaxAmount(item), 0);
};

// 计算发票金额
const calculateInvoiceAmount = () => {
  const totalAmount = calculateTotalAmount();
  const totalTax = calculateTotalTax();
  const discountAmount = props.detailData.discountAmount || 0;
  return totalAmount + totalTax - discountAmount;
};

// 计算支付金额
const calculatePayAmount = () => {
  const totalAmount = calculateTotalAmount();
  const shippingFee = props.detailData.shippingFee || 0;
  const discountAmount = props.detailData.discountAmount || 0;
  return totalAmount + shippingFee - discountAmount;
};

// 关闭抽屉
const onClose = () => {
  emit('update:visible', false);
};

// 同步至销项
const onSync = () => {
  // 只发出事件，不显示提示信息，由父组件处理
  emit('sync', props.detailData);
  // 不在这里关闭抽屉，由父组件统一处理
};

// 忽略提醒
const onIgnore = () => {
  // 只发出事件，不显示提示信息，由父组件处理
  emit('ignore', props.detailData);
  // 不在这里关闭抽屉，由父组件统一处理
};

// 处理一键开票确认
const handleInvoiceGenerate = (invoiceData) => {
  Message.success('发票开具成功');
  console.log('发票数据:', invoiceData);
  // 这里可以添加发送发票数据到后端的逻辑
};
</script>

<style scoped>
.invoice-detail-container {
  padding: 0 16px;
}

.info-item {
  display: flex;
  margin-bottom: 2px; /* 缩小行间距 */
}

.info-item .text-gray-500 {
  min-width: 100px;
  flex-shrink: 0;
}

.text-blue-500 {
  color: var(--color-primary-6);
}

.border-gray-200 {
  border-color: var(--color-border-2);
}

.text-red-500 {
  color: var(--color-danger-6);
}

/* 减小标题和内容的间距 */
h4.text-base {
  margin-bottom: 6px;
}

/* 减小各部分之间的间距 */
.mb-4 {
  margin-bottom: 8px;
}

.pb-3 {
  padding-bottom: 8px;
}

/* 减小表格行高 */
:deep(.arco-table-tr) {
  height: 32px;
}

:deep(.arco-table-td) {
  padding-top: 4px;
  padding-bottom: 4px;
}

/* 减小网格间距 */
.gap-3 {
  gap: 0;
}
</style>
