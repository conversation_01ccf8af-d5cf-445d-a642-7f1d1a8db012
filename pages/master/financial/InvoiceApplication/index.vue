<template>
  <div class="ma-content-block p-4">
    <ma-crud
      :options="crudOptions"
      :columns="columns"
      ref="crudRef"
      @row-click="handleView"
    >
      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space @click.stop>
          <a-link @click.stop="handleSync(record)">同步至销项</a-link>
          <a-link @click.stop="handleIgnore(record)">忽略提醒</a-link>
        </a-space>
      </template>

      <!-- 订单状态 -->
      <template #orderStatus="{ record }">
        <a-tag :color="getOrderStatusColor(record.orderStatus)">
          {{ getOrderStatusText(record.orderStatus) }}
        </a-tag>
      </template>

      <!-- 申请状态 -->
      <template #applyStatus="{ record }">
        <a-tag :color="getApplyStatusColor(record.applyStatus)">
          {{ getApplyStatusText(record.applyStatus) }}
        </a-tag>
      </template>
    </ma-crud>
    
    <!-- 发票申请详情抽屉 -->
    <InvoiceDetailDrawer
      v-model:visible="detailVisible"
      :detailData="currentDetailData"
      @sync="handleSync"
      @ignore="handleIgnore"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import InvoiceDetailDrawer from "./components/InvoiceDetailDrawer.vue";

// 定义页面路由元信息
definePageMeta({
  name: "InvoiceApplication",
  path: "/master/financial/InvoiceApplication",
});

const crudRef = ref();

// 详情抽屉控制
const detailVisible = ref(false);
const currentDetailData = ref({});

// 模拟数据
const mockData = [
  {
    id: "10001",
    orderAmount: 1299.00,
    invoiceTitle: "北京科技有限公司",
    orderSource: "线上商城",
    orderNumber: "ORD20250528001",
    orderTime: "2025-05-20 10:30:25",
    orderStatus: "completed",
    applyStatus: "pending"
  },
  {
    id: "10002",
    orderAmount: 2499.50,
    invoiceTitle: "上海贸易有限公司",
    orderSource: "线下门店",
    orderNumber: "ORD20250527002",
    orderTime: "2025-05-27 14:22:10",
    orderStatus: "completed",
    applyStatus: "approved"
  },
  {
    id: "10003",
    orderAmount: 899.00,
    invoiceTitle: "广州电子科技有限公司",
    orderSource: "线上商城",
    orderNumber: "ORD20250526003",
    orderTime: "2025-05-26 09:15:30",
    orderStatus: "processing",
    applyStatus: "rejected"
  },
  {
    id: "10004",
    orderAmount: 3699.00,
    invoiceTitle: "深圳数码科技有限公司",
    orderSource: "线上商城",
    orderNumber: "ORD20250525004",
    orderTime: "2025-05-25 16:40:18",
    orderStatus: "completed",
    applyStatus: "completed"
  },
  {
    id: "10005",
    orderAmount: 1599.00,
    invoiceTitle: "杭州网络科技有限公司",
    orderSource: "线下门店",
    orderNumber: "ORD20250524005",
    orderTime: "2025-05-24 11:05:42",
    orderStatus: "cancelled",
    applyStatus: "cancelled"
  }
];

// 订单状态颜色和文本
const getOrderStatusColor = (status) => {
  const statusMap = {
    processing: "blue",
    completed: "green",
    cancelled: "red"
  };
  return statusMap[status] || "gray";
};

const getOrderStatusText = (status) => {
  const statusMap = {
    processing: "处理中",
    completed: "已完成",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 申请状态颜色和文本
const getApplyStatusColor = (status) => {
  const statusMap = {
    pending: "orange",
    approved: "green",
    rejected: "red",
    completed: "blue",
    cancelled: "gray"
  };
  return statusMap[status] || "gray";
};

const getApplyStatusText = (status) => {
  const statusMap = {
    pending: "待审核",
    approved: "已审核",
    rejected: "已拒绝",
    completed: "已开票",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 操作处理函数
const handleView = (record) => {
  // 准备详情数据
  currentDetailData.value = {
    ...record,
    // 添加模拟的详细信息
    applySource: "下单",
    invoiceType: "普通发票",
    invoiceContent: "明细",
    taxNumber: record.id === "10001" ? "91110105MA00B7EX5G" : "",
    buyerAddress: record.id === "10001" ? "北京市朝阳区建国路88号" : "",
    buyerPhone: record.id === "10001" ? "010-********" : "",
    buyerBank: record.id === "10001" ? "中国银行北京分行" : "",
    buyerBankAccount: record.id === "10001" ? "6222020200123456789" : "",
    receiveType: record.id % 2 === 0 ? "email" : "express",
    email: record.id % 2 === 0 ? "<EMAIL>" : "",
    receiverName: record.id % 2 !== 0 ? "张三" : "",
    receiverPhone: record.id % 2 !== 0 ? "***********" : "",
    receiverAddress: record.id % 2 !== 0 ? "北京市海淀区中关村南大街5号" : "",
    orderType: "商品订单",
    payTime: record.orderTime ? record.orderTime.replace(/\d{2}:\d{2}:\d{2}$/, "10:15:30") : "",
    orderRemark: record.id === "10001" ? "请尽快发货" : "无",
    channelOrderNumber: `CH${record.id}${Math.floor(Math.random() * 10000)}`,
    invoiceApplyTime: new Date(new Date(record.orderTime).getTime() + 3600000).toLocaleString('zh-CN', { hour12: false }).replace(/\//g, '-'),
    shippingFee: 10.00,
    discountAmount: 50.00,
    paymentMethod: "微信支付",
    // 模拟商品数据
    goods: [
      {
        name: "高性能商务笔记本电脑",
        specification: "i7/16G/512G",
        unit: "台",
        quantity: 1,
        price: 5999.00,
        amount: 5999.00,
        taxRate: "13%",
        merchant: "官方旗舰店",
        image: "https://dummyimage.com/600x400/000/fff&text=商品图片"
      },
      {
        name: "无线蓝牙鼠标",
        specification: "G304",
        unit: "个",
        quantity: 2,
        price: 129.00,
        amount: 258.00,
        taxRate: "13%",
        merchant: "电子周边专营店",
        image: "https://dummyimage.com/600x400/000/fff&text=商品图片"
      },
      {
        name: "Type-C转接器",
        specification: "USB3.1",
        unit: "个",
        quantity: 1,
        price: 99.00,
        amount: 99.00,
        taxRate: "13%",
        merchant: "数码配件专营店",
        image: "https://dummyimage.com/600x400/000/fff&text=商品图片"
      }
    ]
  };
  
  // 打开详情抽屉
  detailVisible.value = true;
};

const handleSync = (record, event) => {
  // 阻止事件冒泡，防止触发行点击事件
  if (event) {
    event.stopPropagation();
  }
  
  const id = record.id || (currentDetailData.value && currentDetailData.value.id);
  const orderNumber = record.orderNumber || (currentDetailData.value && currentDetailData.value.orderNumber) || '';
  const invoiceTitle = record.invoiceTitle || (currentDetailData.value && currentDetailData.value.invoiceTitle) || '';
  const orderAmount = record.orderAmount || (currentDetailData.value && currentDetailData.value.orderAmount) || 0;
  
  // 显示提示信息，使用模拟数据拼接
  Message.success(`已同步发票申请至销项：订单编号 ${orderNumber}，发票抬头 ${invoiceTitle}，金额 ¥${orderAmount.toFixed(2)}`); 
  
  // 如果是从详情页调用，关闭抽屉
  if (detailVisible.value) {
    detailVisible.value = false;
  }
  // 刷新列表
  crudRef.value.refresh();
};

const handleIgnore = (record, event) => {
  // 阻止事件冒泡，防止触发行点击事件
  if (event) {
    event.stopPropagation();
  }
  
  const id = record.id || (currentDetailData.value && currentDetailData.value.id);
  const orderNumber = record.orderNumber || (currentDetailData.value && currentDetailData.value.orderNumber) || '';
  const applyStatus = record.applyStatus || (currentDetailData.value && currentDetailData.value.applyStatus) || '';
  
  // 显示提示信息，使用模拟数据拼接
  Message.success(`已忽略发票申请提醒：ID ${id}，订单编号 ${orderNumber}，申请状态 ${getApplyStatusText(applyStatus)}`); 
  
  // 如果是从详情页调用，关闭抽屉
  if (detailVisible.value) {
    detailVisible.value = false;
  }
  // 刷新列表
  crudRef.value.refresh();
};

// ma-crud 配置
const crudOptions = reactive({
  // 模拟API，实际使用时替换为真实API
  api: async (params) => {
    console.log("查询参数:", params);
    // 模拟API响应
    return {
      code: 0,
      message: "success",
      data: {
        items: mockData,
        total: mockData.length,
        page: params.page || 1,
        pageSize: params.pageSize || 10
      }
    };
  },
  // 搜索配置
  searchColNumber: 3,
  // 表格配置
  showIndex: false,
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有订单时间参数，转换为时间戳
    if (params.orderTime && params.orderTime.length > 0) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.orderTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startTime = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.orderTime[1]);
      endDate.setHours(23, 59, 59, 0);
      params.endTime = endDate.getTime();

      delete params.orderTime;
    } else {
      delete params.startTime;
      delete params.endTime;
    }
    return params;
  },
});

// 订单来源选项
const orderSourceOptions = [
  { label: "全部", value: "" },
  { label: "线上商城", value: "线上商城" },
  { label: "线下门店", value: "线下门店" },
];

// 订单状态选项
const orderStatusOptions = [
  { label: "全部", value: "" },
  { label: "处理中", value: "processing" },
  { label: "已完成", value: "completed" },
  { label: "已取消", value: "cancelled" },
];

// 申请状态选项
const applyStatusOptions = [
  { label: "全部", value: "" },
  { label: "待审核", value: "pending" },
  { label: "已审核", value: "approved" },
  { label: "已拒绝", value: "rejected" },
  { label: "已开票", value: "completed" },
  { label: "已取消", value: "cancelled" },
];

// 表格列定义
const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    width: 80,
    align: "center",
    fixed: "left",
  },
  {
    title: "订单金额",
    dataIndex: "orderAmount",
    width: 120,
    align: "right",
    search: true,
    formType: "number",
    render: ({ record }) => `¥${record.orderAmount.toFixed(2)}`,
  },
  {
    title: "发票抬头",
    dataIndex: "invoiceTitle",
    width: 180,
    search: true,
    formType: "input",
  },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    width: 120,
    search: true,
    formType: "select",
    dict: { data: orderSourceOptions },
  },
  {
    title: "订单编号",
    dataIndex: "orderNumber",
    width: 160,
    search: true,
    formType: "input",
  },
  {
    title: "订单时间",
    dataIndex: "orderTime",
    width: 170,
    align: "center",
    search: true,
    formType: "range",
  },
  {
    title: "订单状态",
    dataIndex: "orderStatus",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: orderStatusOptions },
    slotName: "orderStatus",
  },
  {
    title: "申请状态",
    dataIndex: "applyStatus",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: applyStatusOptions },
    slotName: "applyStatus",
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 200,
    align: "center",
    fixed: "right",
    slotName: "operation",
  },
]);

// 生命周期钩子
onMounted(() => {
  // 页面加载时可以执行一些初始化操作
});
</script>

<script>
export default { name: "master-financial-invoice-application" };
</script>

<style scoped>
/* 自定义样式 */
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}
</style>
