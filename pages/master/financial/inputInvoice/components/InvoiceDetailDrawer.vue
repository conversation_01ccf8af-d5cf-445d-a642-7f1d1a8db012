<template>
  <a-drawer
    :width="'65%'"
    :visible="visible"
    @cancel="onClose"
    unmountOnClose
  >
    <template #title>
      进项发票详情
    </template>

    <div class="invoice-detail-container">
      <!-- 发票基本信息 -->
      <div class="invoice-info-section">
        <a-card class="mb-4" title="基本信息">
          <a-descriptions
            :column="{ xs: 1, sm: 2, md: 3 }"
            :data="basicInfoData"
            layout="inline-horizontal"
            :label-style="{ 'font-weight': 'bold', width: '100px' }"
          />
        </a-card>
      </div>

      <!-- 发票信息 -->
      <div class="invoice-type-section mb-4">
        <a-card title="发票信息">
          <div class="grid grid-cols-3 gap-3 mb-4">
            <div class="info-item">
              <div class="text-gray-500">发票代码：</div>
              <div>{{ detailData.invoiceCode || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="text-gray-500">开票时间：</div>
              <div>{{ detailData.invoiceTime || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="text-gray-500">上传时间：</div>
              <div>{{ detailData.uploadTime || '-' }}</div>
            </div>
            <div class="info-item">
              <div class="text-gray-500">商品数量：</div>
              <div>{{ detailData.productCount || '0' }}</div>
            </div>
            <div class="info-item">
              <div class="text-gray-500">价税合计：</div>
              <div>{{ detailData.totalAmount ? `¥${(detailData.totalAmount * 1.13).toFixed(2)}` : '¥0.00' }}</div>
            </div>
          </div>
          
          <div class="invoice-status-tags">
            <a-tag :color="getInvoiceTypeColor(detailData.invoiceType)">
              {{ getInvoiceTypeText(detailData.invoiceType) }}
            </a-tag>
            <a-tag :color="getInvoiceStatusColor(detailData.invoiceStatus)">
              {{ getInvoiceStatusText(detailData.invoiceStatus) }}
            </a-tag>
            <a-tag :color="getReconciledColor(detailData)">
              勾兑状态: {{ detailData.isReconciled ? '已勾兑' : '未勾兑' }}
            </a-tag>
            <span :class="getReconciledAmountClass(detailData)">
              勾兑金额: {{ (detailData.reconciledAmount || 0).toFixed(2) }}/{{ (detailData.totalReconciledAmount || 0).toFixed(2) }}
            </span>
          </div>
        </a-card>
      </div>

      <!-- 发票明细 -->
      <div class="invoice-products-section mb-4">
        <a-card>
          <template #title>
            <div class="flex items-center">
              <span class="text-base font-medium">发票明细</span>
            </div>
          </template>
          <a-table
            :data="detailData.goodsItems || []"
            :pagination="false"
            :bordered="true"
            :scroll="{ x: '100%' }"
            stripe
            size="small"
          >
            <template #columns>
              <a-table-column title="序号" :width="60" align="center">
                <template #cell="{ rowIndex }">
                  {{ rowIndex + 1 }}
                </template>
              </a-table-column>
              <a-table-column title="货物或应税劳务服务名称" data-index="name" />
              <a-table-column title="规格型号" data-index="specification" :width="120" />
              <a-table-column title="单位" data-index="unit" :width="60" align="center" />
              <a-table-column title="数量" data-index="quantity" :width="80" align="center" />
              <a-table-column title="单价" data-index="unitPrice" :width="100" align="right">
                <template #cell="{ record }">
                  ¥{{ (record.price || 0).toFixed(2) }}
                </template>
              </a-table-column>
              <a-table-column title="金额" data-index="amount" :width="120" align="right">
                <template #cell="{ record }">
                  ¥{{ (record.amount || 0).toFixed(2) }}
                </template>
              </a-table-column>
              <a-table-column title="税率" data-index="taxRate" :width="80" align="center">
                <template #cell="{ record }">
                  {{ ((record.taxRate || 0) * 100).toFixed(0) }}%
                </template>
              </a-table-column>
              <a-table-column title="税额" data-index="taxAmount" :width="120" align="right">
                <template #cell="{ record }">
                  ¥{{ (record.taxAmount || 0).toFixed(2) }}
                </template>
              </a-table-column>
            </template>
          </a-table>
          
          <div class="flex justify-end mt-2">
            <div class="text-right" style="min-width: 280px">
              <div class="info-item-horizontal">
                <span class="text-gray-500 mr-4">金额合计：</span>
                <span>¥{{ calculateTotalAmount().toFixed(2) }}</span>
              </div>
              <div class="info-item-horizontal">
                <span class="text-gray-500 mr-4">税额合计：</span>
                <span>¥{{ calculateTotalTaxAmount().toFixed(2) }}</span>
              </div>
              <div class="info-item-horizontal">
                <span class="text-gray-500 mr-4">价税合计：</span>
                <span class="text-red-500 font-bold">¥{{ calculateAmountWithTax().toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </a-card>
      </div>
      
      <!-- 操作日志 -->
      <div class="log-section mb-4">
        <a-card>
          <template #title>
            <div class="flex items-center">
              <span class="text-base font-medium">操作日志</span>
            </div>
          </template>
          <a-timeline>
            <a-timeline-item time="2025-05-28 10:30:25">
              <template #dot>
                <icon-check-circle-fill style="color: #00b42a" />
              </template>
              <div class="font-medium">发票上传成功</div>
              <div class="text-gray-500 text-sm">{{ detailData.merchantName || '系统管理员' }}</div>
            </a-timeline-item>
            <a-timeline-item v-if="detailData.isReconciled" time="2025-05-28 14:15:30">
              <template #dot>
                <icon-check-circle-fill style="color: #165dff" />
              </template>
              <div class="font-medium">发票勾兑完成</div>
              <div class="text-gray-500 text-sm">财务部 - 王经理</div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </div>

      <!-- 底部操作按钮 -->
      <div class="invoice-actions-section mt-4">
        <a-space>
          <a-button type="primary" @click="handleDownload">下载发票</a-button>
          <a-button @click="handlePrint">打印发票</a-button>
          <a-button @click="onClose">关闭</a-button>
        </a-space>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { computed } from "vue";

// 定义属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  detailData: {
    type: Object,
    default: () => ({}),
  },
});

// 定义事件
const emit = defineEmits(["update:visible", "download", "print"]);

// 关闭抽屉
const onClose = () => {
  emit("update:visible", false);
};

// 基本信息数据
const basicInfoData = computed(() => {
  return [
    {
      label: "商户名称",
      value: props.detailData.merchantName || "-",
    },
    {
      label: "发票抬头",
      value: props.detailData.invoiceTitle || "-",
    },
    {
      label: "发票代码",
      value: props.detailData.invoiceCode || "-",
    },
    {
      label: "开票时间",
      value: props.detailData.invoiceTime || "-",
    },
    {
      label: "上传时间",
      value: props.detailData.uploadTime || "-",
    },
    {
      label: "商品数量",
      value: props.detailData.productCount || "0",
    },
    {
      label: "价税合计",
      value: props.detailData.totalAmount
        ? `¥${(props.detailData.totalAmount * 1.13).toFixed(2)}`
        : "¥0.00",
    },
  ];
});

// 发票类型颜色和文本
const getInvoiceTypeColor = (type) => {
  const typeMap = {
    blue: "blue",
    red: "red",
    void: "gray",
  };
  return typeMap[type] || "gray";
};

const getInvoiceTypeText = (type) => {
  const typeMap = {
    blue: "蓝字发票",
    red: "红字发票",
    void: "作废发票",
  };
  return typeMap[type] || "未知类型";
};

// 开票状态颜色和文本
const getInvoiceStatusColor = (status) => {
  const statusMap = {
    completed: "green",
    cancelled: "red",
  };
  return statusMap[status] || "gray";
};

const getInvoiceStatusText = (status) => {
  const statusMap = {
    completed: "已开票",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 勾兑状态颜色
const getReconciledColor = (record) => {
  if (!record || record.isReconciled === undefined || !record.isReconciled) {
    return "gray";
  }
  const reconciledAmount = record.reconciledAmount || 0;
  const totalReconciledAmount = record.totalReconciledAmount || 0;
  return reconciledAmount >= totalReconciledAmount ? "green" : "orange";
};

// 勾兑金额样式
const getReconciledAmountClass = (record) => {
  if (!record || record.isReconciled === undefined || !record.isReconciled) {
    return "text-gray-500";
  }
  const reconciledAmount = record.reconciledAmount || 0;
  const totalReconciledAmount = record.totalReconciledAmount || 0;
  return reconciledAmount >= totalReconciledAmount ? "text-success" : "text-warning";
};

// 计算金额合计
const calculateTotalAmount = () => {
  if (!props.detailData.goodsItems || !props.detailData.goodsItems.length) {
    return 0;
  }
  return props.detailData.goodsItems.reduce((total, item) => {
    return total + (item.amount || 0);
  }, 0);
};

// 计算税额合计
const calculateTotalTaxAmount = () => {
  if (!props.detailData.goodsItems || !props.detailData.goodsItems.length) {
    return 0;
  }
  return props.detailData.goodsItems.reduce((total, item) => {
    return total + (item.taxAmount || 0);
  }, 0);
};

// 计算价税合计
const calculateAmountWithTax = () => {
  return calculateTotalAmount() + calculateTotalTaxAmount();
};

// 下载发票
const handleDownload = () => {
  emit("download", props.detailData);
};

// 打印发票
const handlePrint = () => {
  emit("print", props.detailData);
};
</script>

<style scoped>
.invoice-detail-container {
  padding: 0 16px;
}

.invoice-info-section,
.invoice-type-section,
.invoice-products-section,
.invoice-actions-section {
  margin-bottom: 20px;
}

.mt-4 {
  margin-top: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.text-success {
  color: var(--color-success-6);
}

.text-warning {
  color: var(--color-warning-6);
}

.text-gray-500 {
  color: var(--color-text-3);
}

/* 网格布局 */
.grid {
  display: grid;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-3 {
  gap: 0.75rem;
}

/* 信息项样式 */
.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
}

.info-item > div:first-child {
  font-size: 13px;
  margin-bottom: 4px;
}

.info-item > div:last-child {
  font-size: 14px;
  font-weight: 500;
}

/* 水平布局的信息项 */
.info-item-horizontal {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  white-space: nowrap;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item-horizontal span:last-child {
  font-weight: 500;
  min-width: 100px;
  text-align: right;
}

/* 状态标签样式 */
.invoice-status-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}
</style>
