<template>
  <div class="ma-content-block p-4">
    <!-- 状态卡片 -->
    <div class="stats-cards-container">
      <div class="stats-scroll-btn stats-scroll-left" @click="scrollStatsCards('left')">
        <icon-left />
      </div>
      <div class="stats-cards" ref="statsCardsRef">
        <div
          v-for="card in statusCards"
          :key="card.key"
          class="stats-card"
          :class="{ 'active': currentStatusCard === card.key }"
          @click="handleStatusCardClick(card.key)"
        >
          <div class="stats-corner-mark" v-if="currentStatusCard === card.key">
            <icon-check class="check-icon" />
          </div>
          <div class="stats-title">{{ card.title }}</div>
          <div class="stats-value">{{ card.count }}</div>
        </div>
      </div>
      <div class="stats-scroll-btn stats-scroll-right" @click="scrollStatsCards('right')">
        <icon-right />
      </div>
    </div>
    
    <ma-crud
      :options="crudOptions"
      :columns="columns"
      :data="mockData"
      ref="crudRef"
      @row-click="handleView"
    >
      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space @click.stop>
          <a-link @click.stop="handleDownload(record)">下载发票</a-link>
        </a-space>
      </template>

      <!-- 开票状态 -->
      <template #invoiceStatus="{ record }">
        <a-tag :color="getInvoiceStatusColor(record.invoiceStatus)">
          {{ getInvoiceStatusText(record.invoiceStatus) }}
        </a-tag>
      </template>

      <!-- 发票状态 -->
      <template #invoiceType="{ record }">
        <a-tag :color="getInvoiceTypeColor(record.invoiceType)">
          {{ getInvoiceTypeText(record.invoiceType) }}
        </a-tag>
      </template>

      <!-- 是否勾兑 -->
      <template #isReconciled="{ record }">
        <a-tag :color="getReconciledColor(record)">
          {{ record.isReconciled ? '是' : '否' }}
        </a-tag>
      </template>
      
      <!-- 勾兑金额 -->
      <template #reconciledAmount="{ record }">
        <span :class="getReconciledAmountClass(record)">
          {{ (record.reconciledAmount || 0).toFixed(2) }}/{{ (record.totalReconciledAmount || 0).toFixed(2) }}
        </span>
      </template>
    </ma-crud>
    
    <!-- 发票详情抽屉 -->
    <InvoiceDetailDrawer
      v-model:visible="detailVisible"
      :detailData="currentDetailData"
      @download="handleDownload"
      @print="handlePrint"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import InvoiceDetailDrawer from "./components/InvoiceDetailDrawer.vue";
import { useInputInvoiceStore } from "~/store/master/financial";

// 定义页面路由元信息
definePageMeta({
  name: "inputInvoice",
  path: "/master/financial/inputInvoice",
});

const crudRef = ref();

// 详情抽屉控制
const detailVisible = ref(false);
const currentDetailData = ref({});

// 初始化 store
const inputInvoiceStore = useInputInvoiceStore();

// 状态卡片数据
const currentStatusCard = ref('all');
const statusCards = computed(() => {
  // 从 store 获取统计数据
  const { all, reconciled, unreconciled } = inputInvoiceStore.statistics;
  
  return [
    { key: 'all', title: '全部发票', count: all },
    { key: 'reconciled', title: '已勾兑', count: reconciled },
    { key: 'unreconciled', title: '未勾兑', count: unreconciled },
  ];
});

// 状态卡片滚动相关
const statsCardsRef = ref(null);

// 滚动状态卡片
const scrollStatsCards = (direction) => {
  if (!statsCardsRef.value) return;

  const scrollAmount = 200; // 每次滚动的像素数
  const currentScroll = statsCardsRef.value.scrollLeft;

  if (direction === "left") {
    statsCardsRef.value.scrollTo({
      left: Math.max(0, currentScroll - scrollAmount),
      behavior: "smooth"
    });
  } else {
    statsCardsRef.value.scrollTo({
      left: currentScroll + scrollAmount,
      behavior: "smooth"
    });
  }
};

// 处理状态卡片点击
const handleStatusCardClick = (key) => {
  currentStatusCard.value = key;
  inputInvoiceStore.setStatus(key);
  
  // 根据选中的状态卡片过滤数据
  if (crudRef.value) {
    if (key === 'all') {
      crudRef.value.search({ isReconciled: '' });
    } else if (key === 'reconciled') {
      crudRef.value.search({ isReconciled: true });
    } else if (key === 'unreconciled') {
      crudRef.value.search({ isReconciled: false });
    }
  }
};

// 使用 store 中的模拟数据
const mockData = computed(() => inputInvoiceStore.mockData.map(item => ({
  ...item,
  // 确保数据格式一致，添加totalReconciledAmount字段（如果store中没有）
  totalReconciledAmount: item.totalReconciledAmount || item.totalAmount
})));

// 开票状态颜色和文本
const getInvoiceStatusColor = (status) => {
  const statusMap = {
    completed: "green",
    cancelled: "red"
  };
  return statusMap[status] || "gray";
};

const getInvoiceStatusText = (status) => {
  const statusMap = {
    completed: "已开票",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 发票类型颜色和文本
const getInvoiceTypeColor = (type) => {
  const typeMap = {
    blue: "blue",
    red: "red",
    void: "gray"
  };
  return typeMap[type] || "gray";
};

const getInvoiceTypeText = (type) => {
  const typeMap = {
    blue: "蓝字发票",
    red: "红字发票",
    void: "作废发票"
  };
  return typeMap[type] || "未知类型";
};

// 勾兑状态颜色
const getReconciledColor = (record) => {
  if (!record || record.isReconciled === undefined || !record.isReconciled) {
    return "gray";
  }
  const reconciledAmount = record.reconciledAmount || 0;
  const totalReconciledAmount = record.totalReconciledAmount || 0;
  return reconciledAmount >= totalReconciledAmount ? "green" : "orange";
};

// 勾兑金额样式
const getReconciledAmountClass = (record) => {
  if (!record || record.isReconciled === undefined || !record.isReconciled) {
    return "text-gray-500";
  }
  const reconciledAmount = record.reconciledAmount || 0;
  const totalReconciledAmount = record.totalReconciledAmount || 0;
  return reconciledAmount >= totalReconciledAmount ? "text-success " : "text-warning ";
};

// 操作处理函数
const handleView = (record) => {
  // 使用 store 中的方法获取发票详情
  const getInvoiceDetail = (id) => {
    return new Promise((resolve) => {
      const invoice = inputInvoiceStore.getInvoiceDetail(id);
      resolve(invoice);
    });
  };

  getInvoiceDetail(record.id).then((invoice) => {
    // 准备详情数据
    currentDetailData.value = {
      ...record,
      // 模拟发票商品明细数据
      products: invoice.products,
    };
    
    // 显示详情抽屉
    detailVisible.value = true;
  });
};

// 下载发票
const handleDownload = (record, event) => {
  if (event) {
    event.stopPropagation();
  }
  // 模拟下载操作
  Message.success(`正在下载发票：${record.invoiceCode}`);
};

// 打印发票
const handlePrint = (record) => {
  // 模拟打印操作
  Message.success(`正在打印发票：${record.invoiceCode}`);
};

// ma-crud 配置
const crudOptions = reactive({
  // 使用 store 中的 API 方法
  api(params) {
    console.log('查询参数:', params);
    return inputInvoiceStore.getInputInvoiceData(params);
  },
  
  // 搜索配置
  searchColNumber: 3,
  // 表格配置
  showIndex: true,
  
  // 搜索前处理参数
  beforeSearch(params) {
    // 处理日期范围
    if (params.invoiceTime && params.invoiceTime.length === 2) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.invoiceTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startInvoiceTime = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.invoiceTime[1]);
      endDate.setHours(23, 59, 59, 0);
      params.endInvoiceTime = endDate.getTime();

      delete params.invoiceTime;
    } else {
      delete params.startInvoiceTime;
      delete params.endInvoiceTime;
    }
    
    // 处理上传时间范围
    if (params.uploadTime && params.uploadTime.length === 2) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.uploadTime[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startUploadTime = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.uploadTime[1]);
      endDate.setHours(23, 59, 59, 0);
      params.endUploadTime = endDate.getTime();

      delete params.uploadTime;
    } else {
      delete params.startUploadTime;
      delete params.endUploadTime;
    }
    
    return params;
  },
});

// 发票类型选项
const invoiceTypeOptions = [
  { label: "全部", value: "" },
  { label: "蓝字发票", value: "blue" },
  { label: "红字发票", value: "red" },
  { label: "作废发票", value: "void" },
];

// 开票状态选项
const invoiceStatusOptions = [
  { label: "全部", value: "" },
  { label: "已开票", value: "completed" },
  { label: "已取消", value: "cancelled" },
];

// 表格列定义
const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    width: 80,
    align: "center",
    fixed: "left",
  },
  {
    title: "商户名称",
    dataIndex: "merchantName",
    width: 180,
    search: true,
    formType: "input",
  },
  {
    title: "发票抬头",
    dataIndex: "invoiceTitle",
    width: 180,
    search: true,
    formType: "input",
  },
  {
    title: "商品数量",
    dataIndex: "productCount",
    width: 100,
    align: "center",
  },
  {
    title: "开票时间",
    dataIndex: "invoiceTime",
    width: 170,
    align: "center",
    search: true,
    formType: "range",
  },
  {
    title: "上传时间",
    dataIndex: "uploadTime",
    width: 170,
    align: "center",
    search: true,
    formType: "range",
  },
  {
    title: "发票代码",
    dataIndex: "invoiceCode",
    width: 150,
    search: true,
    formType: "input",
  },
  {
    title: "价税合计",
    dataIndex: "totalAmount",
    width: 120,
    align: "right",
    search: true,
    formType: "number",
    render: ({ record }) => `¥${(record.totalAmount * 1.13).toFixed(2)}`,
  },
  {
    title: "发票状态",
    dataIndex: "invoiceStatus",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: invoiceStatusOptions },
    slotName: "invoiceStatus",
  },
  {
    title: "发票类型",
    dataIndex: "invoiceType",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: invoiceTypeOptions },
    slotName: "invoiceType",
  },
  {
    title: "是否勾兑",
    dataIndex: "isReconciled",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: { data: [
      { label: "全部", value: "" },
      { label: "是", value: true },
      { label: "否", value: false },
    ] },
    slotName: "isReconciled",
  },
  {
    title: "勾兑金额",
    dataIndex: "reconciledAmount",
    width: 150,
    align: "right",
    slotName: "reconciledAmount",
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 200,
    align: "center",
    fixed: "right",
    slotName: "operation",
  },
]);

// 生命周期钩子
onMounted(() => {
  // 页面加载时主动加载数据
  crudRef.value && crudRef.value.refresh();
  
  // 设置当前状态卡片
  currentStatusCard.value = inputInvoiceStore.currentStatus || 'all';
});
</script>

<script>
export default { name: "master-financial-input-invoice" };
</script>

<style scoped>
/* 自定义样式 */
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}

/* 状态卡片样式 */
.stats-cards-container {
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.stats-cards {
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  background-color: #fff;
  border-radius: 4px;
  flex: 1;
  
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.stats-cards::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.stats-card {
  flex: 0 0 auto;
  width: 250px;
  margin: 5px;
  padding: 18px;
  border-radius: 4px;
  text-align: left;
  border: 1px solid #e5e6eb;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.stats-card:hover {
  border-color: #d9e1ff;
  background-color: #f9fafc;
}

.stats-card.active {
  border: 1px solid #165dff;
  background-color: #F0F6FF;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
}

.stats-card.active .stats-title {
  color: #165dff;
  font-weight: 500;
}

.stats-card.active .stats-value {
  color: black;
  font-weight: 500;
}

.stats-corner-mark {
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 24px 24px 0;
  border-color: transparent #165dff transparent transparent;
}

.check-icon {
  position: absolute;
  top: 1px;
  right: -23px;
  color: white;
  font-size: 14px;
}

.stats-title {
  font-size: 12px;
  margin-bottom: 4px;
}

.stats-value {
  font-size: 20px;
  font-weight: 500;
  color: #1d2129;
}

.stats-scroll-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  z-index: 1;
}

.stats-scroll-btn:hover {
  background-color: #f2f3f5;
}

.stats-scroll-btn.stats-scroll-left {
  margin-right: 8px;
}

.stats-scroll-btn.stats-scroll-right {
  margin-left: 8px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
