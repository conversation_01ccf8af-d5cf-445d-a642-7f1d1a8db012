<template>
  <div class="ma-content-block p-4">
    <div class="page-title mb-4">主体维护</div>
    <ma-crud
      :options="crud"
      :columns="columns"
      :data="mockData"
      ref="crudRef"
    >
    </ma-crud>
    
    <!-- 详情抽屉 -->
    <a-drawer
      :width="'500px'"
      :visible="drawerVisible"
      :title="drawerTitle"
      @cancel="closeDrawer"
      unmountOnClose
    >
      <a-form
        ref="formRef"
        :model="formData"
        layout="vertical"
      >
        <a-form-item field="name" label="名称" :rules="[{ required: true, message: '请输入名称' }]">
          <a-input v-model="formData.name" placeholder="请输入名称" />
        </a-form-item>
        <a-form-item field="taxId" label="纳税人识别号" :rules="[{ required: true, message: '请输入纳税人识别号' }]">
          <a-input v-model="formData.taxId" placeholder="请输入纳税人识别号" />
        </a-form-item>
        <a-form-item field="addressAndPhone" label="地址及电话" :rules="[{ required: true, message: '请输入地址及电话' }]">
          <a-textarea v-model="formData.addressAndPhone" placeholder="请输入地址及电话" />
        </a-form-item>
        <a-form-item field="bankInfo" label="开户行及帐号" :rules="[{ required: true, message: '请输入开户行及帐号' }]">
          <a-textarea v-model="formData.bankInfo" placeholder="请输入开户行及帐号" />
        </a-form-item>
        <a-form-item field="invoiceAmount" label="开票金额" :rules="[{ required: true, message: '请输入开票金额' }]">
          <a-input-number v-model="formData.invoiceAmount" placeholder="请输入开票金额" mode="button" :precision="2" :min="0" style="width: 100%" />
        </a-form-item>
      </a-form>
      <div class="drawer-footer">
        <a-space>
          <a-button @click="closeDrawer">取消</a-button>
          <a-button type="primary" @click="handleSubmit">确定</a-button>
        </a-space>
      </div>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { Message, Modal } from "@arco-design/web-vue";

// 定义页面路由元信息
definePageMeta({
  name: "mainMaintenance",
  path: "/master/financial/mainMaintenance",
});

const crudRef = ref();
const formRef = ref();

// 抽屉控制
const drawerVisible = ref(false);
const drawerTitle = ref('新增主体信息');
const isEdit = ref(false);
const formData = reactive({
  id: '',
  name: '',
  taxId: '',
  addressAndPhone: '',
  bankInfo: '',
  invoiceAmount: 0
});

// 页面标题
const pageTitle = ref('主体维护');



// 模拟数据
const mockData = [
  {
    id: "10001",
    name: "北京科技有限公司",
    taxId: "91110105MA01ABCD1X",
    addressAndPhone: "北京市朝阳区科技园区1号楼 010-********",
    bankInfo: "中国银行北京分行 621700********90123",
    invoiceAmount: 15000.00
  },
  {
    id: "10002",
    name: "上海贸易有限公司",
    taxId: "91310115MA02EFGH2Y",
    addressAndPhone: "上海市浦东新区张江高科技园区2号楼 021-********",
    bankInfo: "工商银行上海分行 622202********90123",
    invoiceAmount: 8500.00
  },
  {
    id: "10003",
    name: "广州电子科技有限公司",
    taxId: "91440101MA03IJKL3Z",
    addressAndPhone: "广州市天河区软件园3号楼 020-********",
    bankInfo: "建设银行广州分行 622700********90123",
    invoiceAmount: 23000.00
  },
  {
    id: "10004",
    name: "深圳数码科技有限公司",
    taxId: "91440301MA04MNOP4A",
    addressAndPhone: "深圳市南山区科技园4号楼 0755-********",
    bankInfo: "招商银行深圳分行 622588********90123",
    invoiceAmount: 5600.00
  },
  {
    id: "10005",
    name: "杭州网络科技有限公司",
    taxId: "91330101MA05QRST5B",
    addressAndPhone: "杭州市西湖区文三路5号楼 0571-********",
    bankInfo: "中信银行杭州分行 622600********90123",
    invoiceAmount: 12800.00
  }
];

// 操作处理函数
const handleView = (record) => {
  isEdit.value = false;
  drawerTitle.value = '查看主体信息';
  
  // 复制记录数据到表单
  Object.keys(formData).forEach(key => {
    if (record[key] !== undefined) {
      formData[key] = record[key];
    }
  });
  
  drawerVisible.value = true;
};

// 编辑主体
const handleEdit = (record, event) => {
  if (event) {
    event.stopPropagation();
  }
  
  isEdit.value = true;
  drawerTitle.value = '编辑主体信息';
  
  // 复制记录数据到表单
  Object.keys(formData).forEach(key => {
    if (record[key] !== undefined) {
      formData[key] = record[key];
    }
  });
  
  drawerVisible.value = true;
};

// 删除主体
const handleDelete = (record, event) => {
  if (event) {
    event.stopPropagation();
  }
  
  Modal.warning({
    title: '确认删除',
    content: `确定要删除"${record.name}"吗？`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      // 模拟删除API调用
      const index = mockData.findIndex(item => item.id === record.id);
      if (index !== -1) {
        mockData.splice(index, 1);
        Message.success('删除成功');
      } else {
        Message.error('未找到要删除的记录');
      }
      // 刷新表格数据
      crudRef.value && crudRef.value.refresh();
    }
  });
};

// 关闭抽屉
const closeDrawer = () => {
  drawerVisible.value = false;
  // 重置表单
  formRef.value && formRef.value.resetFields();
};

// 提交表单
const handleSubmit = async () => {
  const { validate, getFieldsValue } = formRef.value;
  try {
    await validate();
    const values = getFieldsValue();
    
    if (isEdit.value) {
      // 编辑模式，应调用更新API
      // 模拟API调用
      const index = mockData.findIndex(item => item.id === formData.id);
      if (index !== -1) {
        // 更新数据
        Object.assign(mockData[index], values);
        Message.success('更新成功');
      } else {
        Message.error('未找到要更新的记录');
      }
    } else {
      // 新增模式，应调用创建API
      // 模拟API调用 - 生成新ID并添加到数据中
      const newId = 'ID' + (Math.floor(Math.random() * 90000) + 10000);
      const newRecord = {
        id: newId,
        ...values
      };
      mockData.push(newRecord);
      Message.success('创建成功');
    }
    
    // 关闭抽屉
    closeDrawer();
    // 刷新表格数据
    crudRef.value && crudRef.value.refresh();
  } catch (error) {
    console.error('表单验证失败', error);
  }
};

// ma-crud 配置
const crud = reactive({
  // 数据源
  api: mockData,
  pk: 'id',
  
  // 搜索配置
  searchColNumber: 3,
  
  // 表格配置
  showIndex: false,
  pageLayout: 'fixed',
  operationColumn: true,
  operationColumnWidth: 200,
  
  // 添加功能
  add: { 
    show: true,
    api: (params) => {
      return new Promise((resolve) => {
        // 模拟添加API
        const newId = 'ID' + (Math.floor(Math.random() * 90000) + 10000);
        const newRecord = {
          id: newId,
          ...params
        };
        mockData.push(newRecord);
        
        setTimeout(() => {
          resolve({
            code: 200,
            message: '添加成功',
            data: newRecord
          });
        }, 300);
      });
    }
  },
  
  // 编辑功能
  edit: { 
    show: true,
    api: (params) => {
      return new Promise((resolve) => {
        // 模拟编辑API
        const index = mockData.findIndex(item => item.id === params.id);
        if (index !== -1) {
          // 更新数据
          Object.assign(mockData[index], params);
          setTimeout(() => {
            resolve({
              code: 200,
              message: '更新成功',
              data: mockData[index]
            });
          }, 300);
        } else {
          setTimeout(() => {
            resolve({
              code: 400,
              message: '未找到要更新的记录'
            });
          }, 300);
        }
      });
    }
  },
  
  // 删除功能
  delete: {
    show: true,
    api: (params) => {
      return new Promise((resolve) => {
        // 模拟删除API
        const ids = Array.isArray(params) ? params : [params.id];
        ids.forEach(id => {
          const index = mockData.findIndex(item => item.id === id);
          if (index !== -1) {
            mockData.splice(index, 1);
          }
        });
        
        setTimeout(() => {
          resolve({
            code: 200,
            message: '删除成功'
          });
        }, 300);
      });
    }
  },
  
  // 搜索前处理参数
  beforeSearch(params) {
    console.log('搜索参数处理前:', params);
    
    // 处理金额范围
    if (params.invoiceAmount && params.invoiceAmount.length === 2) {
      params.invoiceAmountRange = params.invoiceAmount;
      delete params.invoiceAmount;
    }
    
    console.log('搜索参数处理后:', params);
    return params;
  },
  
  // 添加前处理参数
  beforeAdd(params) {
    console.log('添加前原始参数:', params);
    // 处理参数
    return params;
  },
  
  // 编辑前处理参数
  beforeEdit(params) {
    console.log('编辑前原始参数:', params);
    // 处理参数
    return params;
  }
});

// 表格列定义
const columns = reactive([
{
    title: "id",
    dataIndex: "id",
    width: 80,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '请输入id' }],
  },
  {
    title: "名称",
    dataIndex: "name",
    width: 180,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '请输入名称' }],
  },
  {
    title: "纳税人识别号",
    dataIndex: "taxId",
    width: 180,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '请输入纳税人识别号' }],
  },
  {
    title: "地址及电话",
    dataIndex: "addressAndPhone",
    width: 250,
    formType: "textarea",
    commonRules: [{ required: true, message: '请输入地址及电话' }],
  },
  {
    title: "开户行及帐号",
    dataIndex: "bankInfo",
    width: 250,
    formType: "textarea",
    commonRules: [{ required: true, message: '请输入开户行及帐号' }],
  },
  {
    title: "开票金额",
    dataIndex: "invoiceAmount",
    width: 120,
    align: "right",
    search: true,
    formType: "number",
    commonRules: [{ required: true, message: '请输入开票金额' }],
    render: ({ record }) => `¥${record.invoiceAmount.toFixed(2)}`,
  },
]);

// 生命周期钩子
onMounted(() => {
  // 页面加载时主动加载数据
  crudRef.value && crudRef.value.refresh();
});
</script>

<script>
export default { name: "master-financial-main-maintenance" };
</script>

<style scoped>
/* 自定义样式 */
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}

/* 页面标题样式 */
.page-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  color: var(--color-text-1);
}

.drawer-footer {
  position: absolute;
  right: 24px;
  bottom: 24px;
}
</style>
