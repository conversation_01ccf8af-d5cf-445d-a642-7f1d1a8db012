<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud
      :options="crud"
      :columns="columns"
      ref="crudRef"
      :row-class="() => ''"
      :row-selection="{ type: 'none' }"
    >
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
      </template>

      <!-- 申请金额插槽 -->
      <template #application_amount="{ record }">¥{{ formatAmount(record.application_amount) }}</template>

      <!-- 操作列 -->
      <template #operationBeforeExtend="{ record }">
        <div class="operation-buttons">
          <!-- 详情按钮，所有状态都显示 -->
          <a-button type="text" @click="openDetails(record)">详情</a-button>
        </div>
      </template>
    </ma-crud>

    <!-- 认款申请详情抽屉 -->
    <a-drawer
      :width="'70%'"
      :visible="detailVisible"
      @update:visible="(val) => detailVisible = val"
      @cancel="closeDetail"
      unmountOnClose
      :footer="true"
    >
      <template #title>认款申请详情</template>

      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <a-button @click="closeDetail" class="mr-2">取消</a-button>

          <!-- 待审核 -->
          <template v-if="detailRecord.status === 0">
            <a-button type="primary" @click="submitAudit(detailRecord)" class="mr-2">确认</a-button>
          </template>
        </div>
      </template>

      <div class="payment-detail-container">
        <!-- 认款申请信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">认款申请信息</div>
          <div class="info-table mb-6">
            <table class="w-full">
              <tbody>
                <tr>
                  <td class="info-label-cell">认款申请单号</td>
                  <td class="info-value-cell">{{ detailRecord.application_no || '-' }}</td>
                  <td class="info-label-cell">订单来源</td>
                  <td class="info-value-cell">{{ detailRecord.order_source || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">申请时间</td>
                  <td class="info-value-cell">{{ formatDate(detailRecord.apply_date) || '-' }}</td>
                  <td class="info-label-cell">商户名称</td>
                  <td class="info-value-cell">{{ detailRecord.merchant_name || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">申请人</td>
                  <td class="info-value-cell">{{ detailRecord.applicant || '-' }}</td>
                  <td class="info-label-cell">认款申请备注</td>
                  <td class="info-value-cell">{{ detailRecord.remark || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">附件</td>
                  <td class="info-value-cell" colspan="3">
                    <a-space v-if="detailRecord.attachments && detailRecord.attachments.length > 0">
                      <a-button
                        v-for="(item, index) in detailRecord.attachments"
                        :key="index"
                        type="text"
                        @click="downloadAttachment(item)"
                      >
                        <template #icon>
                          <icon-file />
                        </template>
                        {{ item.name }}
                      </a-button>
                    </a-space>
                    <span v-else>-</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 订单信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">订单信息</div>
          <div class="info-table mb-6">
            <a-table
              :data="detailRecord.orders || []"
              :bordered="{cell:true}"
              :pagination="false"
              class="order-detail-table"
              :span-method="dataSpanMethod"
            >
              <template #columns>
                <a-table-column title="订单编号" data-index="order_id" />
                <a-table-column title="下单时间" data-index="order_date">
                  <template #cell="{ record }">{{ formatDate(record.order_date) }}</template>
                </a-table-column>
                <!-- <a-table-column title="订单金额" data-index="total_amount">
                  <template #cell="{ record }">¥{{ formatAmount(record.total_amount) }}</template>
                </a-table-column> -->
                <a-table-column title="收货人" data-index="recipient" />
                <a-table-column title="收货地址" data-index="shipping_address" />
                <a-table-column title="商品编号" data-index="product_code" />
                <a-table-column title="商品图片" data-index="product_image" width="100">
                    <template #cell="{ record }">
                      <a-avatar
                        :size="60"
                        shape="square"
                        :image-url="record.product_image || '/assets/images/default-product.png'"
                      />
                    </template>
                  </a-table-column>
                
                  <a-table-column title="商品名称" data-index="product_name">
                    <template #cell="{ record }">
                      <div class="product-detail-cell">
                        <div class="product-detail-name">
                          <div>{{ record.product_name }}</div>
                          <!-- <div class="text-gray-400 text-xs mt-1">商品编号：{{ record.product_code }}</div> -->
                          <div
                            class="text-gray-400 text-xs mt-1"
                          >规格：{{ record.specification || '-' }}</div>
                        </div>
                      </div>
                    </template>
                  </a-table-column>
                  <a-table-column title="成交单价" data-index="unit_price" align="center" width="100">
                    <template #cell="{ record }">¥{{ formatAmount(record.unit_price) }}</template>
                  </a-table-column>
                  <a-table-column title="数量" data-index="quantity" align="center" width="80" />
                  <a-table-column title="实付金额" data-index="subtotal" align="center" width="120">
                    <template #cell="{ record }">¥{{ formatAmount(record.subtotal) }}</template>
                  </a-table-column>
              </template>
              <template #footer>
                  <tr class="summary-row">
                    <td colspan="2">
                      <div class="summary-label">合计</div>
                    </td>
                    <td class="text-right">
                      <!-- <div class="summary-value">¥{{ orders }}</div> -->
                      
                      <div class="summary-value">¥{{ formatAmount(totalSubtotal) }}</div>
                      <!-- <div class="summary-value">¥{{ formatAmount(order.total_amount) }}</div> -->
                    </td>
                  </tr>
                </template>
            </a-table>
          </div>

          <!-- 订单商品信息 -->
          <!-- <div v-for="(order, orderIndex) in detailRecord.orders" :key="orderIndex" class="mb-4">
            <div class="order-header" style="margin-bottom: 16px;">订单编号：{{ order.order_id }}</div>
            <div class="info-table mb-4">
              <a-table
                :data="order.products || []"
                :bordered="{cell:true}"
                :pagination="false"
                class="product-detail-table"
              >
                <template #columns>
                  <a-table-column title="商品图片" data-index="product_image" width="100">
                    <template #cell="{ record }">
                      <a-avatar
                        :size="60"
                        shape="square"
                        :image-url="record.product_image || '/assets/images/default-product.png'"
                      />
                    </template>
                  </a-table-column>
                  <a-table-column title="商品名称" data-index="product_name">
                    <template #cell="{ record }">
                      <div class="product-detail-cell">
                        <div class="product-detail-name">
                          <div>{{ record.product_name }}</div>
                          <div class="text-gray-400 text-xs mt-1">商品编号：{{ record.product_code }}</div>
                          <div
                            class="text-gray-400 text-xs mt-1"
                          >规格：{{ record.specification || '-' }}</div>
                        </div>
                      </div>
                    </template>
                  </a-table-column>
                  <a-table-column title="单价" data-index="unit_price" align="center" width="100">
                    <template #cell="{ record }">¥{{ formatAmount(record.unit_price) }}</template>
                  </a-table-column>
                  <a-table-column title="数量" data-index="quantity" align="center" width="80" />
                </template>
                <template #footer>
                  <tr class="summary-row">
                    <td colspan="3">
                      <div class="summary-label">实付金额</div>
                    </td>
                    <td class="text-right">
                      <div class="summary-value">¥{{ formatAmount(order.total_amount) }}</div>
                    </td>
                  </tr>
                </template>
              </a-table>
            </div>
          </div> -->
        </div>

        <!-- 非本司订单信息 -->
        <div
          class="mb-6"
          v-if="detailRecord.external_orders && detailRecord.external_orders.length > 0"
        >
          <div class="text-lg font-bold mb-4">非本司订单信息</div>
          <a-table
            :data="detailRecord.external_orders || []"
            :bordered="true"
            :pagination="false"
            class="order-detail-table mb-6"
          >
            <template #columns>
              <a-table-column title="订单编号" data-index="order_id" />
              <a-table-column title="订单总价" data-index="order_amount">
                <template #cell="{ record }">¥{{ formatAmount(record.order_amount) }}</template>
              </a-table-column>
            </template>
          </a-table>
        </div>

        <!-- 商家申请认款信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">商家申请认款信息</div>
          <div class="info-table mb-6">
            <table class="w-full">
              <tbody>
                <tr>
                  <td class="info-label-cell">收款记录类型</td>
                  <td class="info-value-cell">{{ detailRecord.payment_type || '-' }}</td>
                  <td class="info-label-cell">收款单号</td>
                  <td class="info-value-cell">{{ detailRecord.receipt_id || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">交易日期</td>
                  <td class="info-value-cell">{{ formatDate(detailRecord.transaction_date) || '-' }}</td>
                  <td class="info-label-cell">付款人名称</td>
                  <td class="info-value-cell">{{ detailRecord.payer_name || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">付款人账号</td>
                  <td class="info-value-cell">{{ detailRecord.payer_account || '-' }}</td>
                  <td class="info-label-cell">付款人开户行</td>
                  <td class="info-value-cell">{{ detailRecord.payer_bank || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">交易流水号</td>
                  <td class="info-value-cell">{{ detailRecord.transaction_id || '-' }}</td>
                  <td class="info-label-cell">收款金额</td>
                  <td
                    class="info-value-cell amount-value"
                  >¥{{ formatAmount(detailRecord.application_amount) || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">收款备注</td>
                  <td class="info-value-cell" colspan="3">{{ detailRecord.payment_remark || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">收款凭证</td>
                  <td class="info-value-cell" colspan="3">
                    <a-space
                      v-if="detailRecord.payment_vouchers && detailRecord.payment_vouchers.length > 0"
                    >
                      <a-image
                        v-for="(item, index) in detailRecord.payment_vouchers"
                        :key="index"
                        :src="item.url"
                        :width="80"
                        :height="80"
                        :preview-visible="false"
                        class="mr-2 mb-2"
                      />
                    </a-space>
                    <span v-else>-</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <!-- 订单信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">审核节点</div>
          <div class="info-table mb-6">
            <a-table
              :data="detailRecord.nodes || []"
              :bordered="{cell:true}"
              :pagination="false"
              class="order-detail-table"
            >
              <template #columns>
                <a-table-column title="当前节点" data-index="nodes_current" />
                <a-table-column title="审核人" data-index="nodes_name" />
                <a-table-column title="审核状态" data-index="nodes_status">
                  <template #cell="{ record }">
                    <a-tag
                      :color="record.nodes_status === 1 ? 'green' : 'orange'"
                    >{{ record.nodes_status === 1 ? '审核通过' : '审核不通过' }}</a-tag>
                  </template>
                </a-table-column>
                <a-table-column title="审核操作时间" data-index="nodes_date">
                  <template #cell="{ record }">{{ formatDate(record.nodes_date) }}</template>
                </a-table-column>
              </template>
            </a-table>
          </div>
        </div>
        <!-- 审核操作 -->
        <div class="mb-6" v-if="detailRecord.status === 0">
          <div class="text-lg font-bold mb-4">审核操作</div>
          <div class="audit-operation-container">
            <a-space direction="vertical" class="w-full">
              <div class="mb-2">
                <span class="text-red-500">*</span>
                <span class="required-label">审核结果</span>
              </div>
              <a-radio-group v-model="auditForm.result">
                <a-radio value="approve">审核通过</a-radio>
                <a-radio value="reject">审核拒绝</a-radio>
              </a-radio-group>

              <div class="mb-2 mt-4">审核意见</div>
              <a-textarea
                v-model="auditForm.comment"
                placeholder="请输入审核意见"
                :rows="4"
                :max-length="500"
                show-word-limit
              />
            </a-space>
          </div>
        </div>

        <!-- 审核信息 -->
        <div class="mb-6" v-if="detailRecord.status !== 0">
          <div class="text-lg font-bold mb-4">审核信息</div>
          <a-table
            :data="getAuditRecords()"
            :bordered="true"
            :pagination="false"
            class="audit-record-table mb-4"
          >
            <template #columns>
              <a-table-column title="审核人" data-index="auditor" />
              <a-table-column title="审核状态" data-index="result">
                <template #cell="{ record }">
                  <a-tag :color="record.color">{{ record.result }}</a-tag>
                </template>
              </a-table-column>
              <a-table-column title="审核操作时间" data-index="audit_time">
                <template #cell="{ record }">{{ formatDate(record.audit_time) }}</template>
              </a-table-column>
              <a-table-column title="备注" data-index="comment" />
            </template>
          </a-table>
        </div>
      </div>
    </a-drawer>

    <!-- 认款审核抽屉 -->
    <a-drawer
      :width="'70%'"
      :visible="auditVisible"
      @update:visible="(val) => auditVisible = val"
      @cancel="closeAudit"
      unmountOnClose
      :footer="true"
    >
      <template #title>认款审核</template>

      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <a-button @click="closeAudit" class="mr-2">关闭</a-button>
          <a-button type="primary" @click="handleApprove(auditRecord)" class="mr-2">审核通过</a-button>
          <a-button type="primary" status="danger" @click="handleReject(auditRecord)">驳回</a-button>
        </div>
      </template>

      <div class="payment-audit-container">
        <!-- 认款申请基本信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">基本信息</div>
          <div class="info-table mb-6">
            <table class="w-full">
              <tbody>
                <tr>
                  <td class="info-label-cell">认款申请单号</td>
                  <td class="info-value-cell">{{ auditRecord.application_no || '-' }}</td>
                  <td class="info-label-cell">订单来源</td>
                  <td class="info-value-cell">{{ auditRecord.order_source || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">申请时间</td>
                  <td class="info-value-cell">{{ formatDate(auditRecord.apply_date) || '-' }}</td>
                  <td class="info-label-cell">商户名称</td>
                  <td class="info-value-cell">{{ auditRecord.merchant_name || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">申请人</td>
                  <td class="info-value-cell">{{ auditRecord.applicant || '-' }}</td>
                  <td class="info-label-cell">申请认款金额</td>
                  <td
                    class="info-value-cell amount-value"
                  >¥{{ formatAmount(auditRecord.application_amount) || '-' }}</td>
                </tr>
                <tr>
                  <td class="info-label-cell">备注</td>
                  <td class="info-value-cell" colspan="3">{{ auditRecord.remark || '-' }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 订单信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">订单信息</div>
          <a-table
            :data="auditRecord.orders || []"
            :bordered="true"
            :pagination="false"
            class="order-detail-table mb-6"
          >
            <template #columns>
              <a-table-column title="订单编号" data-index="order_id" />
              <a-table-column title="下单时间" data-index="order_date">
                <template #cell="{ record }">{{ formatDate(record.order_date) }}</template>
              </a-table-column>
              <a-table-column title="订单金额" data-index="order_amount">
                <template #cell="{ record }">¥{{ formatAmount(record.order_amount) }}</template>
              </a-table-column>
              <a-table-column title="支付方式" data-index="payment_method" />
              <a-table-column title="订单状态" data-index="order_status">
                <template #cell="{ record }">
                  <a-tag
                    :color="getOrderStatusColor(record.order_status)"
                  >{{ getOrderStatusText(record.order_status) }}</a-tag>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </div>
    </a-drawer>

    <!-- 审核记录抽屉 -->
    <a-drawer
      :visible="auditRecordsVisible"
      @cancel="closeAuditRecords"
      title="审核记录"
      width="600px"
      :footer="false"
      unmountOnClose
    >
      <div class="audit-records-container">
        <!-- 审核记录列表 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">审核记录</div>
          <a-table
            :data="getAuditRecordsForDrawer()"
            :bordered="true"
            :pagination="false"
            class="audit-record-table mb-4"
          >
            <template #columns>
              <a-table-column title="审核人" data-index="auditor" />
              <a-table-column title="审核状态" data-index="result">
                <template #cell="{ record }">
                  <a-tag :color="record.color">{{ record.result }}</a-tag>
                </template>
              </a-table-column>
              <a-table-column title="审核操作时间" data-index="audit_time">
                <template #cell="{ record }">{{ formatDate(record.audit_time) }}</template>
              </a-table-column>
              <a-table-column title="备注" data-index="comment" />
            </template>
          </a-table>
        </div>
      </div>
    </a-drawer>

    <!-- 驳回弹窗 -->
    <a-modal
      :visible="rejectModalVisible"
      @cancel="closeRejectModal"
      @ok="submitReject"
      title="驳回申请"
      :mask-closable="false"
      :unmount-on-close="true"
    >
      <a-form :model="rejectForm" layout="vertical">
        <a-form-item field="reason" label="驳回原因" required>
          <a-textarea v-model="rejectForm.reason" placeholder="请输入驳回原因" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  name: "master-cooperative-ordermanagement-orderpaymentapplication",
  path: "/master/cooperative/ordermanagement/orderpaymentapplication"
});

import { ref, onMounted,computed,reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import dayjs from "dayjs";

// 状态变量
const crudRef = ref(null);
const detailVisible = ref(false);
const auditVisible = ref(false);
const auditRecordsVisible = ref(false);
const rejectModalVisible = ref(false);
const detailRecord = ref({});
const auditRecord = ref({});
const currentRecord = ref(null);
const rejectForm = ref({
  reason: ""
});

const auditForm = ref({
  result: "approve",
  comment: ""
});

// CRUD 配置
const crud = ref({
  title: "认款申请管理",
  api: getApplicationList,
  searchLabelWidth: "140px",
  searchForm: {
    application_no: "",
    status: "",
    audit_date: [],
    order_source: "",
    merchant_name: "",
    application_amount: "",
    applicant: ""
  },
  columns: [
    {
      title: "申请时间",
      dataIndex: "apply_date",
      search: false,
      width: 180,
      align: "center",
      ellipsis: true,
      tooltip: true
    },
    {
      title: "认款申请单号",
      dataIndex: "application_no",
      search: true,
      width: 180,
      ellipsis: true,
      tooltip: true
    },
    {
      title: "认款申请单状态",
      dataIndex: "status",
      search: true,
      width: 120,
      slot: "status",
      formType: "select",
      dict: {
        data: [
          { label: "待审核", value: 0 },
          { label: "已审核", value: 1 },
          { label: "已驳回", value: 2 }
        ]
      }
    },
    {
      title: "审核时间",
      dataIndex: "audit_date",
      search: true,
      width: 180,
      formType: "daterange"
    },
    {
      title: "订单来源",
      dataIndex: "order_source",
      search: true,
      width: 120,
      formType: "select",
      dict: {
        data: [
          { label: "京东", value: "京东" },
          { label: "淘宝", value: "淘宝" },
          { label: "天猫", value: "天猫" },
          { label: "拼多多", value: "拼多多" },
          { label: "抖店", value: "抖店" },
          { label: "微信", value: "微信" }
        ]
      }
    },
    {
      title: "商户名称",
      dataIndex: "merchant_name",
      search: true,
      width: 180
    },
    {
      title: "申请认款金额",
      dataIndex: "application_amount",
      search: true,
      width: 150,
      slot: "application_amount"
    },
    {
      title: "申请人",
      dataIndex: "applicant",
      search: true,
      width: 120
    },
    {
      title: "备注",
      dataIndex: "remark",
      width: 200,
      ellipsis: true,
      tooltip: true
    },
    {
      title: "操作",
      dataIndex: "operationBeforeExtend",
      width: 30,
      align: "center",
      fixed: "right"
    }
  ]
});

// 列配置
const columns = ref(crud.value.columns);

// 状态颜色映射
const getStatusColor = status => {
  const statusMap = {
    0: "blue", // 待审核
    1: "green", // 审核通过
    2: "red", // 已驳回
    3: "grey" // 已取消
  };
  return statusMap[status] || "default";
};

// 状态文本映射
const getStatusText = status => {
  const statusMap = {
    0: "待审核",
    1: "审核通过",
    2: "已驳回",
    3: "已取消"
  };
  return statusMap[status] || "未知状态";
};

// 订单状态颜色映射
const getOrderStatusColor = status => {
  const statusMap = {
    0: "orange", // 待付款
    1: "blue", // 已付款
    2: "green", // 已发货
    3: "green", // 已完成
    4: "red", // 已取消
    5: "grey" // 已关闭
  };
  return statusMap[status] || "default";
};

// 订单状态文本映射
const getOrderStatusText = status => {
  const statusMap = {
    0: "待付款",
    1: "已付款",
    2: "已发货",
    3: "已完成",
    4: "已取消",
    5: "已关闭"
  };
  return statusMap[status] || "未知状态";
};

// 打开详情抽屉
const openDetails = record => {
  detailRecord.value = { ...record };
  detailVisible.value = true;
};

// 关闭详情抽屉
const closeDetail = () => {
  detailVisible.value = false;
  detailRecord.value = {};
};

// 打开审核抽屉
const openPaymentAudit = record => {
  auditRecord.value = { ...record };
  auditVisible.value = true;
};

// 关闭审核抽屉
const closeAudit = () => {
  auditVisible.value = false;
  auditRecord.value = {};
};

// 打开审核记录抽屉
const openAuditRecords = record => {
  auditRecord.value = { ...record };
  auditRecordsVisible.value = true;
};

// 关闭审核记录抽屉
const closeAuditRecords = () => {
  auditRecordsVisible.value = false;
  auditRecord.value = {};
};

// 审核通过处理
const handleApprove = record => {
  // 实际项目中这里应该调用API
  Message.success("审核通过成功");

  // 模拟更新数据
  if (record) {
    record.status = 1; // 更新为审核通过状态
    record.audit_date = dayjs().format("YYYY-MM-DD HH:mm:ss");
    record.auditor = "管理员";

    // 如果当前详情页打开，也更新详情数据
    if (detailVisible.value && detailRecord.value.id === record.id) {
      detailRecord.value = { ...record };
    }
  }

  // 关闭审核抽屉
  if (auditVisible.value) {
    closeAudit();
  }

  // 刷新表格
  crudRef.value.refresh();
};

// 打开驳回弹窗
const handleReject = record => {
  currentRecord.value = record;
  rejectForm.value.reason = "";
  rejectModalVisible.value = true;
};

// 关闭驳回弹窗
const closeRejectModal = () => {
  rejectModalVisible.value = false;
  currentRecord.value = null;
};

// 提交驳回
const submitReject = () => {
  // 表单验证
  if (!rejectForm.value.reason) {
    Message.error("请输入驳回原因");
    return;
  }

  // 实际项目中这里应该调用API
  Message.success("驳回申请成功");
  rejectModalVisible.value = false;

  // 模拟更新数据
  if (currentRecord.value) {
    currentRecord.value.status = 2; // 更新为已驳回状态
    currentRecord.value.reject_reason = rejectForm.value.reason;
    currentRecord.value.reject_date = dayjs().format("YYYY-MM-DD HH:mm:ss");

    // 如果当前详情页打开，也更新详情数据
    if (
      detailVisible.value &&
      detailRecord.value.id === currentRecord.value.id
    ) {
      detailRecord.value = { ...currentRecord.value };
    }

    // 如果当前审核页打开，也更新审核数据
    if (auditVisible.value && auditRecord.value.id === currentRecord.value.id) {
      closeAudit();
    }
  }

  // 刷新表格
  crudRef.value.refresh();
  currentRecord.value = null;
};

// 提交审核
const submitAudit = record => {
  // 表单验证
  if (auditForm.value.result === "reject" && !auditForm.value.comment) {
    Message.error("审核拒绝时请输入审核意见");
    return;
  }

  // 实际项目中这里应该调用API
  if (auditForm.value.result === "approve") {
    // 审核通过
    Message.success("审核通过成功");
    record.status = 1; // 更新为审核通过状态
    record.audit_date = dayjs().format("YYYY-MM-DD HH:mm:ss");
    record.auditor = "管理员";
    record.audit_comment = auditForm.value.comment;
  } else {
    // 审核拒绝
    Message.success("审核拒绝成功");
    record.status = 2; // 更新为已驳回状态
    record.reject_date = dayjs().format("YYYY-MM-DD HH:mm:ss");
    record.reject_auditor = "管理员";
    record.reject_reason = auditForm.value.comment;
  }

  // 关闭详情抽屉
  closeDetail();

  // 重置审核表单
  auditForm.value = {
    result: "approve",
    comment: ""
  };

  // 刷新表格
  crudRef.value.refresh();
};

// 工具方法
const formatDate = date => {
  if (!date) return "-";
  console.log(dayjs(date).format("YYYY-MM-DD HH:mm:ss"));
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
};

// 获取审核记录数据
const getAuditRecords = () => {
  // 如果有真实数据，使用真实数据
  const realRecords = [];

  // 申请记录
  if (auditRecord.value.apply_date) {
    realRecords.push({
      audit_time: auditRecord.value.apply_date,
      auditor: auditRecord.value.applicant || "未知",
      result: "认款申请",
      color: "blue",
      comment: ""
    });
  }

  // 审核通过记录
  if (auditRecord.value.audit_date) {
    realRecords.push({
      audit_time: auditRecord.value.audit_date,
      auditor: auditRecord.value.auditor || "未知",
      result: "审核通过",
      color: "green",
      comment: auditRecord.value.audit_comment || ""
    });
  }

  // 驳回记录
  if (auditRecord.value.reject_date) {
    realRecords.push({
      audit_time: auditRecord.value.reject_date,
      auditor: auditRecord.value.reject_auditor || "未知",
      result: "申请驳回",
      color: "red",
      comment: auditRecord.value.reject_reason || ""
    });
  }

  // 如果没有真实数据，使用模拟数据
  if (realRecords.length === 0) {
    return [
      {
        audit_time: "2025-05-15 09:30:25",
        auditor: "张三",
        result: "认款申请",
        color: "blue",
        comment: ""
      },
      {
        audit_time: "2025-05-16 10:15:36",
        auditor: "李四",
        result: "审核通过",
        color: "green",
        comment: "订单信息无误，金额正确，同意认款"
      }
    ];
  }

  // 按时间排序
  return realRecords.sort(
    (a, b) => new Date(a.audit_time) - new Date(b.audit_time)
  );
};

// 获取审核记录抽屉数据
const getAuditRecordsForDrawer = () => {
  // 如果有真实数据，使用真实数据
  const realRecords = [];

  // 申请记录
  if (auditRecord.value.apply_date) {
    realRecords.push({
      audit_time: auditRecord.value.apply_date,
      auditor: auditRecord.value.applicant || "未知",
      result: "认款申请",
      color: "blue",
      comment: ""
    });
  }

  // 审核通过记录
  if (auditRecord.value.audit_date) {
    realRecords.push({
      audit_time: auditRecord.value.audit_date,
      auditor: auditRecord.value.auditor || "未知",
      result: "审核通过",
      color: "green",
      comment: auditRecord.value.audit_comment || ""
    });
  }

  // 驳回记录
  if (auditRecord.value.reject_date) {
    realRecords.push({
      audit_time: auditRecord.value.reject_date,
      auditor: auditRecord.value.reject_auditor || "未知",
      result: "申请驳回",
      color: "red",
      comment: auditRecord.value.reject_reason || ""
    });
  }

  // 如果没有真实数据，使用模拟数据
  if (realRecords.length === 0) {
    return [
      {
        audit_time: "2025-05-15 09:30:25",
        auditor: "张三",
        result: "认款申请",
        color: "blue",
        comment: ""
      },
      {
        audit_time: "2025-05-16 10:15:36",
        auditor: "李四",
        result: "审核通过",
        color: "green",
        comment: "订单信息无误，金额正确，同意认款"
      }
    ];
  }

  // 按时间排序
  return realRecords.sort(
    (a, b) => new Date(a.audit_time) - new Date(b.audit_time)
  );
};

const formatAmount = amount => {
  console.log(amount);

  if (amount === undefined || amount === null) return "-";
  return Number(amount).toFixed(2);
};

// 计算总实付金额
const totalSubtotal = computed(() => {
  let total = 0;
  if(detailRecord.value?.orders?.length>0){
    detailRecord.value.orders.forEach(item => {
    total += item.unit_price * item.quantity;
  });
  }
 
  return total;
});

// 获取认款申请列表API（模拟）
async function getPaymentList(params) {
  // 实际项目中这里应该调用真实API
  console.log("查询参数:", params);

  // 模拟数据
  const mockData = [
    {
      id: 1,
      payment_id: "RK20250001",
      order_source: "京东",
      merchant_name: "北京科技有限公司",
      payment_amount: 19998.0,
      applicant: "张三",
      apply_date: "2025-05-15 09:30:25",
      status: 0,
      remark: "京东平台5月份订单认款",
      orders: [
        {
          order_id: "DD20250101",
          order_date: "2025-05-10 10:30:00",
          order_amount: 9999.0,
          payment_method: "微信支付",
          order_status: 3
        },
        {
          order_id: "DD20250102",
          order_date: "2025-05-11 14:20:00",
          order_amount: 9999.0,
          payment_method: "支付宝",
          order_status: 3
        }
      ],
      nodes: [
        {
          nodes_current: "一审",
          nodes_name: "张三",
          nodes_status: 3,
          nodes_date: "2025-05-11 14:20:00"
        },
        {
          nodes_current: "二审",
          nodes_name: "李四",
          nodes_status: 2,
          nodes_date: "2025-05-11 14:20:00"
        }
      ]
    },
    {
      id: 2,
      payment_id: "RK20250002",
      order_source: "天猫",
      merchant_name: "上海贸易有限公司",
      payment_amount: 12799.0,
      applicant: "李四",
      apply_date: "2025-05-16 11:25:40",
      status: 1,
      audit_date: "2025-05-16 14:30:15",
      auditor: "王五",
      audit_comment: "订单信息无误，金额正确，同意认款",
      remark: "天猫平台5月份订单认款",
      orders: [
        {
          order_id: "DD20250201",
          order_date: "2025-05-12 09:15:00",
          order_amount: 8999.0,
          payment_method: "微信支付",
          order_status: 3
        },
        {
          order_id: "DD20250202",
          order_date: "2025-05-13 16:40:00",
          order_amount: 3800.0,
          payment_method: "支付宝",
          order_status: 3
        }
      ]
    },
    {
      id: 3,
      payment_id: "RK20250003",
      order_source: "拼多多",
      merchant_name: "广州电子科技有限公司",
      payment_amount: 5999.0,
      applicant: "赵六",
      apply_date: "2025-05-17 10:20:30",
      status: 2,
      reject_date: "2025-05-17 11:45:20",
      reject_auditor: "王五",
      reject_reason: "订单金额与申请认款金额不符，请核实后重新提交",
      remark: "拼多多平台5月份订单认款",
      orders: [
        {
          order_id: "DD20250301",
          order_date: "2025-05-14 11:30:00",
          order_amount: 5999.0,
          payment_method: "微信支付",
          order_status: 3
        }
      ]
    },
    {
      id: 4,
      payment_id: "RK20250004",
      order_source: "抖音",
      merchant_name: "深圳电子商务有限公司",
      payment_amount: 7599.0,
      applicant: "钱七",
      apply_date: "2025-05-18 09:10:15",
      status: 3,
      remark: "抖音平台5月份订单认款",
      orders: [
        {
          order_id: "DD20250401",
          order_date: "2025-05-15 14:25:00",
          order_amount: 4599.0,
          payment_method: "微信支付",
          order_status: 3
        },
        {
          order_id: "DD20250402",
          order_date: "2025-05-16 15:30:00",
          order_amount: 3000.0,
          payment_method: "支付宝",
          order_status: 3
        }
      ]
    }
  ];

  // 模拟筛选逻辑
  let filteredData = [...mockData];

  // 根据状态筛选
  if (
    params.status !== undefined &&
    params.status !== "" &&
    params.status !== "-1"
  ) {
    filteredData = filteredData.filter(
      item => item.status === parseInt(params.status)
    );
  }

  // 根据认款申请单号筛选
  if (params.payment_id) {
    filteredData = filteredData.filter(item =>
      item.payment_id.includes(params.payment_id)
    );
  }

  // 根据订单来源筛选
  if (params.order_source) {
    filteredData = filteredData.filter(item =>
      item.order_source.includes(params.order_source)
    );
  }

  // 根据商户名称筛选
  if (params.merchant_name) {
    filteredData = filteredData.filter(item =>
      item.merchant_name.includes(params.merchant_name)
    );
  }

  // 根据申请人筛选
  if (params.applicant) {
    filteredData = filteredData.filter(item =>
      item.applicant.includes(params.applicant)
    );
  }

  // 模拟分页
  const pageSize = params.pageSize || 10;
  const current = params.current || 1;
  const total = filteredData.length;
  const start = (current - 1) * pageSize;
  const end = start + pageSize;
  const records = filteredData.slice(start, end);

  // 模拟延迟
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        success: true,
        data: {
          records,
          total,
          size: pageSize,
          current
        }
      });
    }, 300);
  });
}

//表格合并
const  dataSpanMethod= ({record, column}) => {

  console.log(record, column,'dataSpanMethod');
      if (column.dataIndex === 'order_id'||column.dataIndex === 'order_date'||column.dataIndex === 'total_amount'||column.dataIndex === 'subtotal') {
        return {
          rowspan: record.products.length,
          colspan: 1
        }
      }
    };

// 获取认款申请列表API（模拟）
async function getApplicationList(params) {
  console.log("查询参数:", params);
  // 模拟分页和筛选逻辑
  const { page = 1, limit = 10, ...filters } = params || {};

  // 模拟数据
  const allData = [];
  for (let i = 1; i <= 100; i++) {
    // 确保数据均匀分布在各个状态
    const status = i % 3; // 0-2循环状态：0-待审核，1-已审核，2-已驳回
    const orderSources = ["京东", "淘宝", "天猫", "拼多多", "抖店", "微信"];
    const merchantNames = [
      "京东自营",
      "淘宝旗舰店",
      "天猫旗舰店",
      "拼多多旗舰店",
      "抖店官方旗舰店",
      "微信小商店",
      "亚马逊自营",
      "唯品会自营",
      "苏宁易购",
      "国美电器"
    ];
    const applicants = [
      "张三",
      "李四",
      "王五",
      "赵六",
      "钱七",
      "孙八",
      "周九",
      "吴十"
    ];
    const paymentTypes = [
      "银行转账",
      "支付宝转账",
      "微信支付",
      "现金支付",
      "其他方式"
    ];
    const banks = [
      "中国工商银行",
      "中国建设银行",
      "中国农业银行",
      "中国银行",
      "交通银行",
      "招商银行",
      "浦发银行",
      "中信银行"
    ];

    // 生成随机金额 1000-10000之间
    const amount = (Math.random() * 9000 + 1000).toFixed(2);

    // 生成订单数据
    const orderCount = Math.floor(Math.random() * 2) + 1; // 1-2个订单
    const orders = [];
    let totalAmount = 0;

    for (let j = 1; j <= orderCount; j++) {
      const productCount =2; // 1-3个商品
      const products = [];
      let orderTotal = 0;
     const productPrice = (Math.random() * 2000 + 100).toFixed(2);
        const quantity = Math.floor(Math.random() * 5) + 1;
        const productTotal = productPrice * quantity;
        orderTotal += productTotal;
      for (let k = 1; k <= productCount; k++) {
        const productPrice = (Math.random() * 2000 + 100).toFixed(2);
        const quantity = Math.floor(Math.random() * 5) + 1;
        const productTotal = productPrice * quantity;
        orderTotal += productTotal;

        products.push({
          product_name: `商品${k}${i}${j}`,
          product_code: `PRD-${String(i).padStart(3, "0")}-${String(j).padStart(
            2,
            "0"
          )}-${String(k).padStart(2, "0")}`,
          product_image: "/assets/images/default-product.png",
          specification: `规格${k}`,
          unit_price: parseFloat(productPrice),
          quantity: quantity
        });
      }

      totalAmount += orderTotal;

      orders.push({
        order_id: `ORD${String(i).padStart(6, "0")}${j}`,
        order_date: dayjs()
          .subtract(i % 30, "day")
          .format("YYYY-MM-DD HH:mm:ss"),
        recipient: applicants[Math.floor(Math.random() * applicants.length)],
        shipping_address: `测试地址${i}${j}号`,
        products: products,
        total_amount: orderTotal,
            product_image: "/assets/images/default-product.png",
              product_name: `商品${j}${i}${j}`,
          product_code: `PRD-${String(i).padStart(3, "0")}-${String(j).padStart(
            2,
            "0"
          )}-${String(j).padStart(2, "0")}`,
             specification: `规格${j}`,
          unit_price: parseFloat(productPrice),
          quantity: quantity,
          subtotal:totalSubtotal

      });
    }

    // 申请日期始终存在
    const applyDate = dayjs()
      .subtract(i % 15, "day")
      .format("YYYY-MM-DD HH:mm:ss");

    // 审核日期和驳回日期根据状态决定
    let auditDate = "";
    let rejectDate = "";
    let auditComment = "";
    let rejectReason = "";
    let auditor = "";

    if (status === 1) {
      // 已审核
      auditDate = dayjs()
        .subtract(i % 10, "day")
        .format("YYYY-MM-DD HH:mm:ss");
      auditor = applicants[Math.floor(Math.random() * applicants.length)];
      auditComment = `审核通过，金额核对无误 - ${i}`;
    } else if (status === 2) {
      // 已驳回
      rejectDate = dayjs()
        .subtract(i % 10, "day")
        .format("YYYY-MM-DD HH:mm:ss");
      auditor = applicants[Math.floor(Math.random() * applicants.length)];
      rejectReason = `驳回原因${i}：付款凭证与订单金额不符`;
    }

    // 生成支付凭证
    const voucherCount = Math.floor(Math.random() * 3) + 1; // 1-3张凭证
    const vouchers = [];
    for (let v = 1; v <= voucherCount; v++) {
      vouchers.push({
        url: "/assets/images/default-voucher.png"
      });
    }

    // 生成一条认款申请记录
    allData.push({
      id: i,
      application_no: `APP${String(i).padStart(6, "0")}`,
      status: status,
      order_source: orderSources[i % orderSources.length],
      merchant_name: merchantNames[i % merchantNames.length],
      application_amount: parseFloat(amount),
      applicant: applicants[i % applicants.length],
      apply_date: applyDate,
      audit_date: auditDate,
      auditor: auditor,
      audit_comment: auditComment,
      reject_date: rejectDate,
      reject_reason: rejectReason,
      remark: `认款申请备注${i}`,
      payment_type: paymentTypes[i % paymentTypes.length],
      receipt_id: `REC${String(i).padStart(6, "0")}`,
      transaction_date: dayjs()
        .subtract(i % 20, "day")
        .format("YYYY-MM-DD"),
      payer_name: `付款方${i}`,
      payer_account: `6222 **** **** ${String(i).padStart(4, "0")}`,
      payer_bank: `${banks[i % banks.length]}北京分行`,
      transaction_id: `TR${String(
        Math.floor(Math.random() * ***********)
      ).padStart(12, "0")}`,
      payment_amount: parseFloat(amount),
      payment_remark: `付款备注${i}`,
      payment_vouchers: vouchers,
      orders: orders
    });
  }

  // 过滤数据
  let filteredData = [...allData];

  // 应用搜索条件筛选
  if (filters) {
    if (filters.application_no) {
      filteredData = filteredData.filter(item =>
        item.application_no.includes(filters.application_no)
      );
    }
    if (filters.status !== undefined && filters.status !== "") {
      filteredData = filteredData.filter(
        item => item.status === Number(filters.status)
      );
    }
    if (filters.order_source) {
      filteredData = filteredData.filter(
        item => item.order_source === filters.order_source
      );
    }
    if (filters.merchant_name) {
      filteredData = filteredData.filter(item =>
        item.merchant_name.includes(filters.merchant_name)
      );
    }
    if (filters.application_amount) {
      filteredData = filteredData.filter(
        item => item.application_amount === Number(filters.application_amount)
      );
    }
    if (filters.applicant) {
      filteredData = filteredData.filter(item =>
        item.applicant.includes(filters.applicant)
      );
    }
    if (filters.audit_date && filters.audit_date.length === 2) {
      const [startDate, endDate] = filters.audit_date.map(
        date => new Date(date)
      );
      endDate.setHours(23, 59, 59, 999); // 设置结束日期为当天的最后一刻

      filteredData = filteredData.filter(item => {
        if (!item.audit_date) return false;
        const itemDate = new Date(item.audit_date);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }
  }

  // 计算总数
  const total = filteredData.length;

  // 分页
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  return Promise.resolve({
    success: true,
    message: "获取认款申请列表成功",
    code: 200,
    data: {
      items: filteredData.slice(startIndex, endIndex),
      pageInfo: {
        total: total,
        currentPage: page,
        totalPage: Math.ceil(total / limit)
      }
    }
  });
}



// 初始化时调用API
onMounted(() => {
  if (crudRef.value) {
    crudRef.value.refresh();
  }
});
</script>

<style lang="less" scoped>
.payment-detail-container,
.payment-audit-container,
.audit-records-container {
  padding: 0 16px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;

  .arco-btn {
    padding: 0 8px;
    height: 28px;
    line-height: 28px;
    font-size: 13px;
  }
}

.order-info-table {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0;
}

.info-cell {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
}

.info-cell:nth-child(even) {
  border-right: none;
}

.info-cell:nth-last-child(-n + 2) {
  border-bottom: none;
}

.info-cell.col-span-2 {
  grid-column: span 2;
  border-right: none;
}

.info-label {
  color: #86909c;
  font-size: 14px;
  margin-bottom: 8px;
}

.info-value {
  font-size: 14px;
}

/* 新增表格样式 */
.info-table {
  //   border: 1px solid #f0f0f0;
  //   border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

.info-table table {
  border-collapse: collapse;
  width: 100%;
}

.info-label-cell {
  width: 120px;
  padding: 12px 16px;
  background-color: #fafafa;
  border: 1px solid #f0f0f0;
  color: #86909c;
  font-size: 14px;
  vertical-align: top;
}

.info-value-cell {
  padding: 12px 16px;
  border: 1px solid #f0f0f0;
  font-size: 14px;
  vertical-align: top;
}

.info-table tr:last-child .info-label-cell,
.info-table tr:last-child .info-value-cell {
  border-bottom: 1px solid #f0f0f0;
}

.required-label {
  font-weight: 500;
}

.amount-value {
  color: #ff4d4f;
  font-weight: 500;
}

.order-detail-table {
  margin-top: 16px;
}

.audit-info-row {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.audit-info-time,
.audit-info-person {
  font-size: 14px;
  line-height: 1.5;
  white-space: nowrap;
}

.audit-info-comment {
  font-size: 14px;
  line-height: 1.5;
  color: #4e5969;
}

.summary-row {
  //   background-color: #f9f9f9;
  align-items: center;
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.summary-label {
  // font-weight: 500;
  text-align: center;
  padding: 12px 16px;
  margin-right: 8px;
}

.summary-value {
  // font-weight: 600;
  color: #ff4d4f;
  // font-size: 16px;
}
</style>