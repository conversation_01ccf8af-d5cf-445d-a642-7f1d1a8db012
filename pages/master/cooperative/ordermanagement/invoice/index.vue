<template>
  <div class="ma-content-block p-4">
    <!-- 状态标签页 -->
    <div class="mb-4">
      <a-tabs :active-key="activeTab" @change="handleTabChange">
        <a-tab-pane key="-1" title="全部"></a-tab-pane>
        <a-tab-pane key="0" title="待审核"></a-tab-pane>
        <a-tab-pane key="1" title="商务审核通过"></a-tab-pane>
        <a-tab-pane key="2" title="财务审核通过"></a-tab-pane>
        <a-tab-pane key="3" title="已开票"></a-tab-pane>
        <a-tab-pane key="4" title="已驳回"></a-tab-pane>
        <a-tab-pane key="5" title="已线下处理"></a-tab-pane>
      </a-tabs>
    </div>

    <!-- CRUD 组件 -->
    <ma-crud
      :options="crud"
      :columns="columns"
      ref="crudRef"
      :row-class="() => ''"
      :row-selection="{ type: 'none' }"
    >
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
      </template>

      <!-- 开票状态列 -->
      <template #invoiceStatus="{ record }">
        <a-tag
          :color="record.invoice_status === 1 ? 'green' : 'orange'"
        >{{ record.invoice_status === 1 ? '已开票' : '未开票' }}</a-tag>
      </template>

      <!-- 操作列 -->
      <template #operationBeforeExtend="{ record }">
        <div class="operation-buttons">
          <!-- 详情按钮，所有状态都显示 -->
          <!-- <a-button type="text" @click="openDetails(record)">详情</a-button> -->

          <!-- 开票审核按钮，只在待审核和商务审核通过状态显示 -->
          <a-button
           
            type="text"
            @click="openInvoiceAudit(record)"
          >查看详情</a-button>

          <!-- 审核记录按钮，所有状态都显示 -->
          <!-- <a-button type="text" @click="openAuditRecords(record)">审核记录</a-button> -->

          <!-- 下载发票按钮，只在已开票状态显示 -->
          <!-- <a-button v-if="record.status === 3" type="text" @click="downloadInvoice(record)">下载发票</a-button> -->
        </div>
      </template>
    </ma-crud>

    <!-- 发票详情抽屉 -->
    <a-drawer
      :width="'70%'"
      :visible="detailVisible"
      @update:visible="(val) => detailVisible = val"
      @cancel="closeDetail"
      unmountOnClose
      :footer="true"
    >
      <template #title>发票详情</template>

      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <a-button @click="closeDetail" class="mr-2">关闭</a-button>

          <template v-if="showAuditOperations">
            <!-- 待审核 -->
            <template v-if="detailRecord.status === 0">
              <a-button type="primary" @click="handleBusinessApprove(detailRecord)" class="mr-2">商务审核</a-button>
              <a-button
                type="primary"
                status="danger"
                @click="handleReject(detailRecord)"
                class="mr-2"
              >驳回</a-button>
              <a-button type="primary" status="warning" @click="handleOffline(detailRecord)">线下处理</a-button>
            </template>

            <!-- 商务审核通过 -->
            <template v-if="detailRecord.status === 1">
              <a-button type="primary" @click="handleFinanceApprove(detailRecord)" class="mr-2">财务审核</a-button>
              <a-button type="primary" status="danger" @click="handleReject(detailRecord)">驳回</a-button>
            </template>

            <!-- 财务审核通过 -->
            <template v-if="detailRecord.status === 2">
              <a-button
                type="primary"
                status="success"
                @click="handleInvoice(detailRecord)"
                class="mr-2"
              >确认开票</a-button>
              <a-button type="primary" status="danger" @click="handleReject(detailRecord)">驳回</a-button>
            </template>
          </template>
          
          <!-- 已开票状态显示下载发票按钮 -->
          <template v-if="detailRecord.status === 3">
            <a-button type="primary" @click="downloadInvoice(detailRecord)">下载发票</a-button>
          </template>
        </div>
      </template>

      <div class="invoice-detail-container">
        <!-- 发票基本信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">基本信息</div>
          <div class="order-info-table mb-6">
            <div class="order-info-grid">
              <div class="info-cell">
                <div class="info-label">订单编号</div>
                <div class="info-value">{{ detailRecord.order_id || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">订单来源</div>
                <div class="info-value">{{ detailRecord.order_source || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">下单时间</div>
                <div class="info-value">{{ formatDate(detailRecord.order_date) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">发票申请时间</div>
                <div class="info-value">{{ formatDate(detailRecord.apply_date) || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">发票抬头</div>
                <div class="info-value">{{ detailRecord.invoice_title || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">发票类型</div>
                <div class="info-value">{{ detailRecord.invoice_type || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">发票号</div>
                <div class="info-value">{{ detailRecord.invoice_no || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">开票金额</div>
                <div
                  class="info-value amount-value"
                >¥{{ formatAmount(detailRecord.invoice_amount) || '-' }}</div>
              </div>
              <div class="info-cell col-span-2">
                <div class="info-label">备注</div>
                <div class="info-value">{{ detailRecord.remark || '-' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">商品信息</div>
          <a-table
            :data="detailRecord.products || []"
            :bordered="true"
            :pagination="false"
            class="product-detail-table mb-6"
          >
            <template #footer>
              <tr class="summary-row">
                <td>
                  <div class="summary-label">合计</div>
                </td>

                <td class="text-center" style="width: 142px;">
                  <div
                    class="summary-value"
                  >¥{{ formatAmount(calculateTotal(detailRecord.products)) }}</div>
                </td>
              </tr>
            </template>

            <template #columns>
              <a-table-column title="商品图片" data-index="product_image" width="120">
                <template #cell="{ record }">
                  <a-avatar
                    :size="60"
                    shape="square"
                    :image-url="record.product_image || '/assets/images/default-product.jpg'"
                  />
                </template>
              </a-table-column>
              <a-table-column title="商品名称" data-index="product_name">
                <template #cell="{ record }">
                  <div class="product-detail-cell">
                    <div class="product-detail-name">
                      <div>{{ record.product_name }}</div>
                      <div class="text-gray-400 text-xs mt-1">商品编号：{{ record.product_code }}</div>
                    </div>
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="商品规格" data-index="specification" />
              <a-table-column title="商家" data-index="merchant" />
              <a-table-column title="单价" data-index="unit_price" align="center">
                <template #cell="{ record }">¥{{ formatAmount(record.unit_price) }}</template>
              </a-table-column>
              <a-table-column title="数量" data-index="quantity" align="center" />
              <a-table-column title="小计" data-index="subtotal" align="center">
                <template #cell="{ record }">¥{{ formatAmount(record.subtotal) }}</template>
              </a-table-column>
            </template>
          </a-table>
        </div>

        <!-- 收货信息 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">收货信息</div>
          <div class="order-info-table mb-6">
            <div class="order-info-grid">
              <div class="info-cell">
                <div class="info-label">收货人</div>
                <div class="info-value">{{ detailRecord.receiver_name || '-' }}</div>
              </div>
              <div class="info-cell">
                <div class="info-label">联系电话</div>
                <div class="info-value">{{ detailRecord.receiver_phone || '-' }}</div>
              </div>
              <div class="info-cell col-span-2">
                <div class="info-label">收货地址</div>
                <div class="info-value">{{ detailRecord.receiver_address || '-' }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-drawer>

    <!-- 商务审核弹窗 -->
    <a-modal
      :visible="businessApproveModalVisible"
      @cancel="closeBusinessApproveModal"
      @ok="submitBusinessApprove"
      title="商务审核"
      :mask-closable="false"
      :unmount-on-close="true"
    >
      <a-form :model="businessApproveForm" layout="vertical">
        <a-form-item field="comment" label="审核意见">
          <a-textarea v-model="businessApproveForm.comment" placeholder="请输入审核意见" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 财务审核弹窗 -->
    <a-modal
      :visible="financeApproveModalVisible"
      @cancel="closeFinanceApproveModal"
      @ok="submitFinanceApprove"
      title="财务审核"
      :mask-closable="false"
      :unmount-on-close="true"
    >
      <a-form :model="financeApproveForm" layout="vertical">
        <a-form-item field="comment" label="审核意见">
          <a-textarea v-model="financeApproveForm.comment" placeholder="请输入审核意见" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 确认开票弹窗 -->
    <a-modal
      :visible="invoiceModalVisible"
      @cancel="closeInvoiceModal"
      @ok="submitInvoice"
      title="确认开票"
      :mask-closable="false"
      :unmount-on-close="true"
      width="650px"
    >
      <a-form :model="invoiceForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item field="invoice_no" label="发票号码" required>
              <a-input v-model="invoiceForm.invoice_no" placeholder="请输入发票号码" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item field="invoice_date" label="开票日期" required>
              <a-date-picker v-model="invoiceForm.invoice_date" style="width: 100%" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item field="invoice_type" label="发票类型" required>
              <a-select v-model="invoiceForm.invoice_type" placeholder="请选择发票类型">
                <a-option value="电子普票">电子普票</a-option>
                <a-option value="电子专票">电子专票</a-option>
                <a-option value="纸质普票">纸质普票</a-option>
                <a-option value="纸质专票">纸质专票</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item field="invoice_amount" label="税价合计" required>
              <a-input-number
                v-model="invoiceForm.invoice_amount"
                placeholder="请输入税价合计"
                mode="button"
                style="width: 100%"
                :precision="2"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item field="bank_info" label="银行信息">
          <a-textarea v-model="invoiceForm.bank_info" placeholder="请输入银行信息，如开户银行、账号等" :rows="2" />
        </a-form-item>
        <a-form-item field="receiver_info" label="收票人信息">
          <a-textarea
            v-model="invoiceForm.receiver_info"
            placeholder="请输入收票人信息，如姓名、电话、地址等"
            :rows="2"
          />
        </a-form-item>
        <a-form-item field="remark" label="备注">
          <a-textarea v-model="invoiceForm.remark" placeholder="请输入备注信息" :rows="2" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 开票审核抽屉 -->
    <a-drawer
      :visible="invoiceAuditVisible"
      @cancel="closeInvoiceAudit"
      title="详情"
      width="85%"
      :footer="true"
      unmountOnClose
    >
      <div class="invoice-audit-container">
        <!-- 1. 订单信息模块 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">订单信息</div>
          <a-descriptions :column="2" size="medium" bordered title="订单基本信息" class="mb-4">
            <!-- 左侧基本信息 -->
            <a-descriptions-item label="订单编号">{{ auditRecord.order_id || '-' }}</a-descriptions-item>
            <a-descriptions-item label="订单来源">{{ auditRecord.order_source || '-' }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ formatDate(auditRecord.order_date) }}</a-descriptions-item>
            <a-descriptions-item label="订单状态">
              <a-tag :color="getStatusColor(auditRecord.status)">
                {{ getStatusText(auditRecord.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="订单金额">
              <span class="amount-value">¥{{ formatAmount(auditRecord.invoice_amount) }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="平台费率">{{ auditRecord.platform_rate || '0' }}%</a-descriptions-item>
            
            <!-- 右侧供应商信息 -->
            <a-descriptions-item label="供应商名称">{{ auditRecord.supplier_name || '-' }}</a-descriptions-item>
            <a-descriptions-item label="联系人">{{ auditRecord.contact_person || '-' }}</a-descriptions-item>
            <a-descriptions-item label="联系电话">{{ auditRecord.contact_phone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="跟单员">{{ auditRecord.order_follower || '-' }}</a-descriptions-item>
            <a-descriptions-item label="收货地址" :span="2">{{ auditRecord.shipping_address || auditRecord.receiver_address || auditRecord.receiver_info || '-' }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 2. 商品信息模块 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">商品信息</div>
          <a-table
            :data="auditRecord.products || []"
            :pagination="false"
            class="product-detail-table"
            bordered
          >
            <template #columns>
              <a-table-column title="商品图片" data-index="product_image" width="150">
                <template #cell="{ record }">
                  <a-avatar
                    :size="60"
                    shape="square"
                    :image-url="record.product_image || '/assets/images/default-product.jpg'"
                  />
                </template>
              </a-table-column>
              <a-table-column title="商品名称" data-index="product_name">
                <template #cell="{ record }">
                  <div class="product-detail-cell">
                    <div class="product-detail-name">
                      <div>{{ record.product_name }}</div>
                      <div class="text-gray-400 text-xs mt-1">商品编号：{{ record.product_code }}</div>
                    </div>
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="规格" data-index="specification" width="120" />
              <a-table-column title="编码" data-index="product_code" width="120" />
              <a-table-column title="数量" data-index="quantity" align="center" width="80" />
              <a-table-column title="含税单价" data-index="unit_price" align="center" width="100">
                <template #cell="{ record }">¥{{ formatAmount(record.unit_price) }}</template>
              </a-table-column>
              <a-table-column title="合计" data-index="subtotal" align="center" width="100">
                <template #cell="{ record }">¥{{ formatAmount(record.subtotal) }}</template>
              </a-table-column>
            </template>
            <template #footer>
              <tr class="summary-row">
                <td colspan="6" class="text-right pr-4" style="text-align:center;width: 86px;">合计：</td>
                <td class="text-center">
                  <div
                    class="amount-value"
                  >¥{{ formatAmount(calculateTotal(auditRecord.products || [])) }}</div>
                </td>
              </tr>
            </template>
          </a-table>
        </div>

        <!-- 3. 开票信息模块 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">开票信息</div>
          <!-- 表格1：基本开票信息 -->
          <a-descriptions :column="2" size="medium" bordered title="基本开票信息" class="mb-4">
            <a-descriptions-item label="发票类型">{{ auditRecord.invoice_type || '-' }}</a-descriptions-item>
            <a-descriptions-item label="申请时间">{{ formatDate(auditRecord.apply_date) }}</a-descriptions-item>
            <a-descriptions-item label="发票抬头">{{ auditRecord.invoice_title || '-' }}</a-descriptions-item>
            <a-descriptions-item label="纳税人识别号">{{ auditRecord.tax_id || '-' }}</a-descriptions-item>
            <a-descriptions-item label="地址及电话">{{ auditRecord.address_phone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="开户行及账号">{{ auditRecord.bank_info || '-' }}</a-descriptions-item>
            <a-descriptions-item label="收票人">{{ auditRecord.receiver_name || '-' }}</a-descriptions-item>
            <a-descriptions-item label="收票人电话">{{ auditRecord.receiver_phone || '-' }}</a-descriptions-item>
            <a-descriptions-item label="收票人邮箱">{{ auditRecord.receiver_email || '-' }}</a-descriptions-item>
            <a-descriptions-item label="收票人区域">{{ auditRecord.receiver_area || '-' }}</a-descriptions-item>
            <a-descriptions-item label="收票人详细地址" :span="2">{{ auditRecord.receiver_address || '-' }}</a-descriptions-item>
            <a-descriptions-item label="开票栏备注">{{ auditRecord.invoice_remark || '-' }}</a-descriptions-item>
            <a-descriptions-item label="内部备注">{{ auditRecord.internal_remark || '-' }}</a-descriptions-item>
          </a-descriptions>

          <!-- 表格2：含税商品信息 -->
          <a-table
            :data="auditRecord.tax_products || []"
            :pagination="false"
            bordered
            title="含税商品信息"
          >
            <template #columns>
              <a-table-column title="采购税收分类编码" data-index="tax_category_code" width="150" />
              <a-table-column title="税收编码" data-index="tax_code" width="120" />
              <a-table-column title="税收简称" data-index="tax_short_name" width="120" />
              <a-table-column title="税收名称" data-index="tax_name" width="150" />
              <a-table-column title="商品编码" data-index="product_code" width="120" />
              <a-table-column title="开票名称" data-index="invoice_name" />
              <a-table-column title="单位" data-index="unit" width="80" />
              <a-table-column title="数量" data-index="quantity" width="80" align="center" />
              <a-table-column title="未税单价" data-index="price_without_tax" width="100" align="right">
                <template #cell="{ record }">¥{{ formatAmount(record.price_without_tax) }}</template>
              </a-table-column>
              <a-table-column
                title="未税金额"
                data-index="amount_without_tax"
                width="100"
                align="right"
              >
                <template #cell="{ record }">¥{{ formatAmount(record.amount_without_tax) }}</template>
              </a-table-column>
              <a-table-column title="税额" data-index="tax_amount" width="100" align="right">
                <template #cell="{ record }">¥{{ formatAmount(record.tax_amount) }}</template>
              </a-table-column>
              <a-table-column title="税率(%)" data-index="tax_rate" width="80" align="center" />
              <a-table-column title="含税金额" data-index="amount_with_tax" width="100" align="right">
                <template #cell="{ record }">¥{{ formatAmount(record.amount_with_tax) }}</template>
              </a-table-column>
            </template>
          </a-table>
        </div>

        <!-- 4. 审核信息模块 -->
        <div class="mb-6">
          <div class="text-lg font-bold mb-4">审核信息</div>
          <a-table
            :data="getAuditRecords()"
            :bordered="true"
            :pagination="false"
            class="audit-record-table mb-4"
          >
            <template #columns>
              <a-table-column title="审核人" data-index="auditor" />
              <a-table-column title="审核状态" data-index="result">
                <template #cell="{ record }">
                  <a-tag :color="record.color">{{ record.result }}</a-tag>
                </template>
              </a-table-column>
              <a-table-column title="审核操作时间" data-index="audit_time">
                <template #cell="{ record }">
                  {{ formatDate(record.audit_time) }}
                </template>
              </a-table-column>
              <a-table-column title="备注" data-index="comment" />
            </template>
          </a-table>

        </div>

        <!-- 5. 审核操作 -->
        <div class="mb-6" v-if="showAuditOperations">
          <div class="text-lg font-bold mb-4">审核操作</div>
          <a-form :model="auditForm" layout="vertical">
            <a-form-item field="action" label="审核结果" required>
              <a-radio-group v-model="auditForm.action">
                <a-radio value="approve">同意</a-radio>
                <a-radio value="reject">驳回</a-radio>
                <a-radio v-if="auditRecord.status === 0" value="offline">线下处理</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item field="comment" label="审核意见" required>
              <a-textarea v-model="auditForm.comment" placeholder="请输入审核意见" :rows="4" />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end">
          <a-button @click="closeInvoiceAudit" class="mr-2">取消</a-button>
          <a-button type="primary" @click="submitAudit" class="mr-2">提交</a-button>
          <!-- 已开票状态或者标签页是已开票时显示下载发票按钮 -->
          <template v-if="auditRecord.status === 3 || activeTab === '3'">
            <a-button type="primary" @click="downloadInvoice(auditRecord)">下载发票</a-button>
          </template>
        </div>
      </template>
    </a-drawer>

    <!-- 审核记录抽屉 -->
    <a-drawer
      :visible="auditRecordsVisible"
      @cancel="closeAuditRecords"
      title="审核记录"
      width="50%"
      :footer="false"
      unmountOnClose
    >
      <div class="audit-records-container">
        <!-- 审核记录列表 -->
        <div class="mb-6">
    
          <a-table
            :data="getAuditRecordsForDrawer()"
            :bordered="true"
            :pagination="false"
            class="audit-record-table mb-4"
          >
            <template #columns>
              <a-table-column title="审核人" data-index="auditor" />
              <a-table-column title="审核状态" data-index="result">
                <template #cell="{ record }">
                  <a-tag :color="record.color">{{ record.result }}</a-tag>
                </template>
              </a-table-column>
              <a-table-column title="审核操作时间" data-index="audit_time">
                <template #cell="{ record }">
                  {{ formatDate(record.audit_time) }}
                </template>
              </a-table-column>
              <a-table-column title="备注" data-index="comment" />
            </template>
          </a-table>
        </div>
      </div>
    </a-drawer>

    <!-- 驳回弹窗 -->
    <a-modal
      :visible="rejectModalVisible"
      @cancel="closeRejectModal"
      @ok="submitReject"
      title="驳回申请"
      :mask-closable="false"
      :unmount-on-close="true"
    >
      <a-form :model="rejectForm" layout="vertical">
        <a-form-item field="reason" label="驳回原因" required>
          <a-textarea v-model="rejectForm.reason" placeholder="请输入驳回原因" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 线下处理弹窗 -->
    <a-modal
      :visible="offlineModalVisible"
      @cancel="closeOfflineModal"
      @ok="submitOffline"
      title="线下处理"
      :mask-closable="false"
      :unmount-on-close="true"
    >
      <a-form :model="offlineForm" layout="vertical">
        <a-form-item field="comment" label="处理说明" required>
          <a-textarea v-model="offlineForm.comment" placeholder="请输入线下处理说明" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import dayjs from "dayjs";

// 页面元数据
definePageMeta({
  name: "master-cooperative-ordermanagement-invoice",
  path: "/master/cooperative/ordermanagement/invoice"
});
// 状态标签页
const activeTab = ref("-1");
const crudRef = ref(null);

// 详情抽屉
const detailVisible = ref(false);
const detailRecord = ref({});

// 开票审核抽屉
const invoiceAuditVisible = ref(false);
const auditRecord = ref({});
const auditForm = reactive({
  action: "approve",
  comment: ""
});

// 审核记录抽屉
const auditRecordsVisible = ref(false);

// 商务审核弹窗
const businessApproveModalVisible = ref(false);
const businessApproveForm = reactive({
  comment: ""
});
const currentRecord = ref(null);

// 财务审核弹窗
const financeApproveModalVisible = ref(false);
const financeApproveForm = reactive({
  comment: ""
});

// 确认开票弹窗
const invoiceModalVisible = ref(false);
const invoiceForm = reactive({
  invoice_no: "",
  invoice_date: null,
  invoice_type: "",
  invoice_amount: null,
  bank_info: "",
  receiver_info: "",
  remark: ""
});

// 驳回弹窗
const rejectModalVisible = ref(false);
const rejectForm = reactive({
  reason: ""
});

// 线下处理弹窗
const offlineModalVisible = ref(false);
const offlineForm = reactive({
  comment: ""
});

// 模拟API函数
function getInvoiceList(params) {
  console.log("查询参数:", params); // 添加日志，查看传入的参数
  // 模拟分页和筛选逻辑
  const { page = 1, limit = 10, ...filters } = params || {};

  // 模拟数据
  const allData = [];
  for (let i = 1; i <= 100; i++) {
    // 确保数据均匀分布在各个状态
    const status = i % 6; // 0-5循环状态，确保每种状态都有数据
    const invoiceStatus = status === 3 ? 1 : 0; // 当状态为已开票时，开票状态为已开票
    const invoiceTypes = ["电子普票", "电子专票", "纸质普票", "纸质专票"];

    allData.push({
      id: i,
      order_id: `ORD${String(i).padStart(6, "0")}`,
      invoice_title: `公司${(i % 10) + 1}`,
      invoice_content: `商品销售${(i % 5) + 1}`,
      invoice_no: status === 3 ? `INV${String(i).padStart(8, "0")}` : "",
      status,
      invoice_status: invoiceStatus,
      order_source: ["京东", "淘宝", "天猫", "拼多多", "抖店", "微信"][i % 6],
      order_date: dayjs()
        .subtract(i % 30, "day")
        .format("YYYY-MM-DD HH:mm:ss"),
      invoice_type: invoiceTypes[i % 4],
      invoice_amount: (Math.random() * 10000).toFixed(2),
      apply_date:
        status > 0
          ? dayjs()
              .subtract(i % 15, "day")
              .format("YYYY-MM-DD HH:mm:ss")
          : "",
      business_approve_date:
        status > 1
          ? dayjs()
              .subtract(i % 10, "day")
              .format("YYYY-MM-DD HH:mm:ss")
          : "",
      finance_approve_date:
        status > 2
          ? dayjs()
              .subtract(i % 5, "day")
              .format("YYYY-MM-DD HH:mm:ss")
          : "",
      invoice_date:
        status === 3
          ? dayjs()
              .subtract(i % 3, "day")
              .format("YYYY-MM-DD HH:mm:ss")
          : "",
      reject_date:
        status === 4
          ? dayjs()
              .subtract(i % 7, "day")
              .format("YYYY-MM-DD HH:mm:ss")
          : "",
      reject_reason: status === 4 ? `驳回原因${i}` : "",
      business_comment: status > 1 ? `商务审核意见${i}` : "",
      finance_comment: status > 2 ? `财务审核意见${i}` : "",
      bank_info: `开户银行: 中国银行北京分行\n账号: 6222 **** **** ${String(
        i
      ).padStart(4, "0")}`,
      receiver_info: `收票人: 张三${(i % 5) + 1}\n电话: 138${String(
        Math.floor(Math.random() * *********)
      ).padStart(8, "0")}\n地址: 北京市海淀区西三环北路${i}号`
    });
  }

  // 过滤数据
  let filteredData = [...allData];

  // 注意：这里不再使用activeTab.value进行过滤，而是使用传入的params参数

  // 应用搜索条件筛选
  if (filters) {
    if (filters.order_id) {
      filteredData = filteredData.filter(item =>
        item.order_id.includes(filters.order_id)
      );
    }
    if (filters.invoice_title) {
      filteredData = filteredData.filter(
        item =>
          item.invoice_title &&
          item.invoice_title.includes(filters.invoice_title)
      );
    }
    if (filters.invoice_no) {
      filteredData = filteredData.filter(
        item => item.invoice_no && item.invoice_no.includes(filters.invoice_no)
      );
    }
    if (filters.status !== undefined && filters.status !== "") {
      console.log("按状态过滤:", filters.status); // 添加日志
      filteredData = filteredData.filter(
        item => item.status === Number(filters.status)
      );
      console.log("过滤后数据条数:", filteredData.length); // 添加日志
    }
    if (filters.order_source) {
      filteredData = filteredData.filter(
        item => item.order_source === filters.order_source
      );
    }
    if (filters.order_date && filters.order_date.length === 2) {
      const [startDate, endDate] = filters.order_date.map(
        date => new Date(date)
      );
      endDate.setHours(23, 59, 59, 999); // 设置结束日期为当天的最后一刻

      filteredData = filteredData.filter(item => {
        const itemDate = new Date(item.order_date);
        return itemDate >= startDate && itemDate <= endDate;
      });
    }
  }

  // 计算总数
  const total = filteredData.length;

  // 分页
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  return Promise.resolve({
    success: true,
    message: "获取发票列表成功",
    code: 200,
    data: {
      items: filteredData.slice(startIndex, endIndex),
      pageInfo: {
        total: total,
        currentPage: page,
        totalPage: Math.ceil(total / limit)
      }
    }
  });
}

// CRUD 配置
const crud = reactive({
  title: "发票管理",
  api: getInvoiceList,
  searchLabelWidth: "100px",
  searchColNumber: 4,
  pageLayout: "total, sizes, prev, pager, next, jumper",
  rowSelection: {
    showCheckedAll: true
  },
  operationWidth: 170,
  add: false,
  edit: false,
  delete: false,
  export: false,
  showOperation: true,
  operationWidth: 200
});

// 表格列配置
const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    width: 80
  },
  {
    title: "发票抬头",
    dataIndex: "invoice_title",
    search: true,
    width: 180
  },
  {
    title: "发票项目内容",
    dataIndex: "invoice_content",
    width: 180
  },
  {
    title: "订单编号",
    dataIndex: "order_id",
    search: true,
    width: 150
  },
  {
    title: "税价合计",
    dataIndex: "invoice_amount",
    width: 120,
    render: (_, record) => `¥${formatAmount(record.invoice_amount)}`
  },
  {
    title: "发票类型",
    dataIndex: "invoice_type",
    search: true,
    width: 120,
    formType: "select",
    dict: {
      data: [
        { label: "电子普票", value: "电子普票" },
        { label: "电子专票", value: "电子专票" },
        { label: "纸质普票", value: "纸质普票" },
        { label: "纸质专票", value: "纸质专票" }
      ]
    }
  },
  {
    title: "发票号码",
    dataIndex: "invoice_no",
    search: true,
    width: 150
  },
  {
    title: "开票状态",
    dataIndex: "invoice_status",
    search: true,
    width: 100,
    formType: "select",
    dict: {
      data: [
        { label: "未开票", value: 0 },
        { label: "已开票", value: 1 }
      ]
    },
    slots: {
      customRender: "invoiceStatus"
    }
  },
  {
    title: "开票时间",
    dataIndex: "invoice_date",
    width: 150,
    render: (_, record) => formatDate(record.invoice_date)
  },
  {
    title: "银行信息",
    dataIndex: "bank_info",
    width: 180
  },
  {
    title: "收票人信息",
    dataIndex: "receiver_info",
    width: 180
  },
  {
    title: "申请状态",
    dataIndex: "status",
    search: true,
    width: 140,
    formType: "select",
    dict: {
      data: [
        { label: "待审核", value: 0 },
        { label: "商务审核通过", value: 1 },
        { label: "财务审核通过", value: 2 },
        { label: "已开票", value: 3 },
        { label: "已驳回", value: 4 },
        { label: "已线下处理", value: 5 }
      ]
    },
    slots: { customRender: "status" }
  },
  {
    title: "订单来源",
    dataIndex: "order_source",
    search: true,
    width: 120,
    formType: "select",
    dict: {
      data: [
        { label: "京东", value: "京东" },
        { label: "淘宝", value: "淘宝" },
        { label: "天猫", value: "天猫" },
        { label: "拼多多", value: "拼多多" },
        { label: "抖店", value: "抖店" },
        { label: "微信", value: "微信" }
      ]
    }
  },
  {
    title: "订单时间",
    dataIndex: "order_date",
    search: true,
    width: 180,
    formType: "dateRange",
    render: (_, record) => formatDate(record.order_date)
  },
  {
    title: "申请时间",
    dataIndex: "apply_date",
    width: 180,
    render: (_, record) => formatDate(record.apply_date)
  },
  {
    title: "操作",
    dataIndex: "operationBeforeExtend",
    fixed: "right",
    align: "center",
    width: 100
  }
]);

// 状态相关方法
const getStatusText = status => {
  const statusMap = {
    0: "待审核",
    1: "商务审核通过",
    2: "财务审核通过",
    3: "已开票",
    4: "已驳回",
    5: "已线下处理"
  };
  return statusMap[status] || "未知状态";
};

const getStatusColor = status => {
  const colorMap = {
    0: "blue", // 待审核
    1: "orange", // 商务审核通过
    2: "cyan", // 财务审核通过
    3: "green", // 已开票
    4: "red", // 已驳回
    5: "purple" // 已线下处理
  };
  return colorMap[status] || "gray";
};

// 标签页切换
const handleTabChange = key => {
  activeTab.value = key;
  if (crudRef.value) {
    // 设置查询参数
    const params = key === "-1" ? {} : { status: parseInt(key) };
    // 清除之前的搜索条件，只保留状态筛选
    crudRef.value.clearSearch();
    crudRef.value.setSearchParams(params);
    crudRef.value.refresh();
  }
};

// 详情抽屉相关方法
const openDetails = record => {
  detailRecord.value = { ...record };
  detailVisible.value = true;
  console.log('打开详情抽屉：', {
    activeTab: activeTab.value,
    recordStatus: record.status,
    showAuditOperations: ["0", "1", "2"].includes(activeTab.value) || [0, 1, 2].includes(record.status)
  });
};

const closeDetail = () => {
  detailVisible.value = false;
  detailRecord.value = {};
};

// 开票审核抽屉相关方法
const openInvoiceAudit = record => {
  // 添加模拟商品数据
  const mockProducts = [
    {
      product_id: 1001,
      product_name: "苹果 iPhone 15 Pro Max",
      product_image: "https://placeholder.pics/svg/200/DEDEDE/555555/iPhone",
      product_code: "IP15PM-256G-BLK",
      specification: "256GB 深空黑",
      unit_price: 9999.0,
      quantity: 1,
      subtotal: 9999.0
    },
    {
      product_id: 1002,
      product_name: "华为 MateBook X Pro",
      product_image: "https://placeholder.pics/svg/200/DEDEDE/555555/MateBook",
      product_code: "MBXP-i7-16G-512G",
      specification: "i7/16GB/512GB/陶瓷白",
      unit_price: 8999.0,
      quantity: 1,
      subtotal: 8999.0
    },
    {
      product_id: 1003,
      product_name: "Apple AirPods Pro 2",
      product_image: "https://placeholder.pics/svg/200/DEDEDE/555555/AirPods",
      product_code: "APP2-2023",
      specification: "白色/降噪/USB-C",
      unit_price: 1799.0,
      quantity: 2,
      subtotal: 3598.0
    }
  ];

  // 添加模拟含税商品信息
  const mockTaxProducts = [
    {
      tax_category_code: "1010101020000000000",
      tax_code: "307040*********0000",
      tax_short_name: "手机",
      tax_name: "移动通信终端设备",
      product_code: "IP15PM-256G-BLK",
      invoice_name: "智能手机",
      unit: "台",
      quantity: 1,
      price_without_tax: 8849.56,
      amount_without_tax: 8849.56,
      tax_amount: 1149.44,
      tax_rate: 13,
      amount_with_tax: 9999.0
    },
    {
      tax_category_code: "1020202020000000000",
      tax_code: "3070299000000000000",
      tax_short_name: "笔记本电脑",
      tax_name: "便携式计算机",
      product_code: "MBXP-i7-16G-512G",
      invoice_name: "笔记本电脑",
      unit: "台",
      quantity: 1,
      price_without_tax: 7963.72,
      amount_without_tax: 7963.72,
      tax_amount: 1035.28,
      tax_rate: 13,
      amount_with_tax: 8999.0
    },
    {
      tax_category_code: "1030303030000000000",
      tax_code: "3070499000000000000",
      tax_short_name: "耳机",
      tax_name: "无线耳机",
      product_code: "APP2-2023",
      invoice_name: "蓝牙耳机",
      unit: "个",
      quantity: 2,
      price_without_tax: 1592.04,
      amount_without_tax: 3184.07,
      tax_amount: 413.93,
      tax_rate: 13,
      amount_with_tax: 3598.0
    }
  ];

  // 添加模拟开票信息
  const mockInvoiceInfo = {
    invoice_type: "电子专票",
    tax_id: "91110105MA00B7GT2R",
    address_phone: "北京市海淀区中关村大街 11 号 / 010-********",
    bank_info: "建设银行北京分行 / 1100 1016 2000 5300 2786",
    receiver_name: "张三",
    receiver_phone: "***********",
    receiver_email: "<EMAIL>",
    receiver_area: "北京市海淀区",
    receiver_address: "中关村软件园区 10 号楼 2 单元 1001 室",
    invoice_remark: "电子产品销售",
    internal_remark: "重要客户，优先处理",
    applicant: "王五",
    business_auditor: "李四",
    finance_auditor: "赵六",
    order_follower: "刘七",
    supplier_name: "北京科技有限公司",
    contact_person: "陈八",
    contact_phone: "***********",
    platform_rate: "3.5"
  };

  // 合并数据
  auditRecord.value = {
    ...record,
    products: mockProducts,
    tax_products: mockTaxProducts,
    ...mockInvoiceInfo,
    order_date: dayjs()
      .subtract(5, "day")
      .format("YYYY-MM-DD HH:mm:ss")
  };

  auditForm.action = "approve";
  auditForm.comment = "";
  invoiceAuditVisible.value = true;
};

const closeInvoiceAudit = () => {
  invoiceAuditVisible.value = false;
  auditRecord.value = {};
};

const submitAudit = () => {
  // 表单验证
  if (!auditForm.comment) {
    Message.error("请输入审核意见");
    return;
  }

  // 根据不同的操作执行不同的逻辑
  if (auditForm.action === "approve") {
    // 如果是待审核状态，变为商务审核通过
    if (auditRecord.value.status === 0) {
      auditRecord.value.status = 1;
      auditRecord.value.business_approve_date = dayjs().format(
        "YYYY-MM-DD HH:mm:ss"
      );
      auditRecord.value.business_comment = auditForm.comment;
      Message.success("商务审核通过");
    }
    // 如果是商务审核通过状态，变为财务审核通过
    else if (auditRecord.value.status === 1) {
      auditRecord.value.status = 2;
      auditRecord.value.finance_approve_date = dayjs().format(
        "YYYY-MM-DD HH:mm:ss"
      );
      auditRecord.value.finance_comment = auditForm.comment;
      Message.success("财务审核通过");
    }
  } else if (auditForm.action === "reject") {
    // 驳回操作
    auditRecord.value.status = 4;
    auditRecord.value.reject_date = dayjs().format("YYYY-MM-DD HH:mm:ss");
    auditRecord.value.reject_reason = auditForm.comment;
    Message.success("驳回成功");
  } else if (auditForm.action === "offline") {
    // 线下处理操作
    auditRecord.value.status = 5;
    auditRecord.value.offline_date = dayjs().format("YYYY-MM-DD HH:mm:ss");
    auditRecord.value.offline_comment = auditForm.comment;
    Message.success("线下处理成功");
  }

  // 如果当前详情页打开，也更新详情数据
  if (detailVisible.value && detailRecord.value.id === auditRecord.value.id) {
    detailRecord.value = { ...auditRecord.value };
  }

  // 刷新表格
  crudRef.value.refresh();
  invoiceAuditVisible.value = false;
};

// 审核记录抽屉相关方法
const openAuditRecords = record => {
  auditRecord.value = { ...record };
  auditRecordsVisible.value = true;
};

const closeAuditRecords = () => {
  auditRecordsVisible.value = false;
  auditRecord.value = {};
};

// 下载发票相关方法
const downloadInvoice = record => {
  if (!record.invoice_no) {
    Message.error("发票号码不存在，无法下载");
    return;
  }

  // 实际项目中这里应该调用API下载发票
  Message.success(`正在下载发票：${record.invoice_no}`);
};

// 商务审核相关方法
const handleBusinessApprove = record => {
  currentRecord.value = record;
  businessApproveForm.comment = "";
  businessApproveModalVisible.value = true;
};

const closeBusinessApproveModal = () => {
  businessApproveModalVisible.value = false;
  currentRecord.value = null;
};

const submitBusinessApprove = () => {
  // 实际项目中这里应该调用API
  Message.success("商务审核通过");
  businessApproveModalVisible.value = false;

  // 模拟更新数据
  if (currentRecord.value) {
    currentRecord.value.status = 1;
    currentRecord.value.business_approve_date = dayjs().format(
      "YYYY-MM-DD HH:mm:ss"
    );
    currentRecord.value.business_approve_comment = businessApproveForm.comment;

    // 如果当前详情页打开，也更新详情数据
    if (
      detailVisible.value &&
      detailRecord.value.id === currentRecord.value.id
    ) {
      detailRecord.value = { ...currentRecord.value };
    }
  }

  // 刷新表格
  crudRef.value.refresh();
  currentRecord.value = null;
};

// 财务审核相关方法
const handleFinanceApprove = record => {
  currentRecord.value = record;
  financeApproveForm.comment = "";
  financeApproveModalVisible.value = true;
};

const closeFinanceApproveModal = () => {
  financeApproveModalVisible.value = false;
  currentRecord.value = null;
};

const submitFinanceApprove = () => {
  // 实际项目中这里应该调用API
  Message.success("财务审核通过");
  financeApproveModalVisible.value = false;

  // 模拟更新数据
  if (currentRecord.value) {
    currentRecord.value.status = 2;
    currentRecord.value.finance_approve_date = dayjs().format(
      "YYYY-MM-DD HH:mm:ss"
    );
    currentRecord.value.finance_approve_comment = financeApproveForm.comment;

    // 如果当前详情页打开，也更新详情数据
    if (
      detailVisible.value &&
      detailRecord.value.id === currentRecord.value.id
    ) {
      detailRecord.value = { ...currentRecord.value };
    }
  }

  // 刷新表格
  crudRef.value.refresh();
  currentRecord.value = null;
};

// 线下处理相关方法
const handleOffline = record => {
  currentRecord.value = record;
  offlineForm.comment = "";
  offlineModalVisible.value = true;
};

const closeOfflineModal = () => {
  offlineModalVisible.value = false;
  currentRecord.value = null;
};

const submitOffline = () => {
  // 实际项目中这里应该调用API
  Message.success("线下处理成功");
  offlineModalVisible.value = false;

  // 模拟更新数据
  if (currentRecord.value) {
    currentRecord.value.status = 5;
    currentRecord.value.offline_date = dayjs().format("YYYY-MM-DD HH:mm:ss");
    currentRecord.value.offline_comment = offlineForm.comment;

    // 如果当前详情页打开，也更新详情数据
    if (
      detailVisible.value &&
      detailRecord.value.id === currentRecord.value.id
    ) {
      detailRecord.value = { ...currentRecord.value };
    }
  }

  // 刷新表格
  crudRef.value.refresh();
  currentRecord.value = null;
};

// 确认开票相关方法
const handleInvoice = record => {
  currentRecord.value = record;
  invoiceForm.invoice_no = "";
  invoiceForm.invoice_date = dayjs().valueOf();
  invoiceForm.invoice_type = record.invoice_type || "";
  invoiceForm.invoice_amount = record.invoice_amount || null;
  invoiceForm.bank_info = record.bank_info || "";
  invoiceForm.receiver_info = record.receiver_info || "";
  invoiceForm.remark = "";
  invoiceModalVisible.value = true;
};

const closeInvoiceModal = () => {
  invoiceModalVisible.value = false;
  currentRecord.value = null;
};

const submitInvoice = () => {
  // 表单验证
  if (!invoiceForm.invoice_no) {
    Message.error("请输入发票号码");
    return;
  }
  if (!invoiceForm.invoice_date) {
    Message.error("请选择开票日期");
    return;
  }
  if (!invoiceForm.invoice_type) {
    Message.error("请选择发票类型");
    return;
  }
  if (!invoiceForm.invoice_amount) {
    Message.error("请输入税价合计");
    return;
  }

  // 实际项目中这里应该调用API
  Message.success("确认开票成功");
  invoiceModalVisible.value = false;

  // 模拟更新数据
  if (currentRecord.value) {
    currentRecord.value.status = 3; // 更新为已开票状态
    currentRecord.value.invoice_status = 1; // 更新为已开票状态
    currentRecord.value.invoice_no = invoiceForm.invoice_no;
    currentRecord.value.invoice_date = dayjs(invoiceForm.invoice_date).format(
      "YYYY-MM-DD HH:mm:ss"
    );
    currentRecord.value.invoice_type = invoiceForm.invoice_type;
    currentRecord.value.invoice_amount = invoiceForm.invoice_amount;
    currentRecord.value.bank_info = invoiceForm.bank_info;
    currentRecord.value.receiver_info = invoiceForm.receiver_info;
    currentRecord.value.remark = invoiceForm.remark;

    // 如果当前详情页打开，也更新详情数据
    if (
      detailVisible.value &&
      detailRecord.value.id === currentRecord.value.id
    ) {
      detailRecord.value = { ...currentRecord.value };
    }
  }

  // 刷新表格
  crudRef.value.refresh();
  currentRecord.value = null;
};

// 驳回相关方法
const handleReject = record => {
  currentRecord.value = record;
  rejectForm.reason = "";
  rejectModalVisible.value = true;
};

const closeRejectModal = () => {
  rejectModalVisible.value = false;
  currentRecord.value = null;
};

const submitReject = () => {
  // 表单验证
  if (!rejectForm.reason) {
    Message.error("请输入驳回原因");
    return;
  }

  // 实际项目中这里应该调用API
  Message.success("驳回申请成功");
  rejectModalVisible.value = false;

  // 模拟更新数据
  if (currentRecord.value) {
    currentRecord.value.status = 4; // 更新为已驳回状态
    currentRecord.value.reject_reason = rejectForm.reason;
    currentRecord.value.reject_date = dayjs().format("YYYY-MM-DD HH:mm:ss");

    // 如果当前详情页打开，也更新详情数据
    if (
      detailVisible.value &&
      detailRecord.value.id === currentRecord.value.id
    ) {
      detailRecord.value = { ...currentRecord.value };
    }
  }

  // 刷新表格
  crudRef.value.refresh();
  currentRecord.value = null;
};

// 工具方法
const formatDate = (date) => {
  if (!date) return "-";
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
};

// 获取审核记录数据
const getAuditRecords = () => {
  // 如果有真实数据，使用真实数据
  const realRecords = [];
  
  // 申请记录
  if (auditRecord.apply_date) {
    realRecords.push({
      audit_time: auditRecord.apply_date,
      auditor: auditRecord.applicant || '未知',
      result: '发票申请',
      color: 'blue',
      comment: ''
    });
  }
  
  // 商务审核记录
  if (auditRecord.business_approve_date) {
    realRecords.push({
      audit_time: auditRecord.business_approve_date,
      auditor: auditRecord.business_auditor || '未知',
      result: '商务审核通过',
      color: 'green',
      comment: auditRecord.business_comment || ''
    });
  }
  
  // 财务审核记录
  if (auditRecord.finance_approve_date) {
    realRecords.push({
      audit_time: auditRecord.finance_approve_date,
      auditor: auditRecord.finance_auditor || '未知',
      result: '财务审核通过',
      color: 'green',
      comment: auditRecord.finance_comment || ''
    });
  }
  
  // 驳回记录
  if (auditRecord.reject_date) {
    realRecords.push({
      audit_time: auditRecord.reject_date,
      auditor: auditRecord.reject_auditor || '未知',
      result: '申请驳回',
      color: 'red',
      comment: auditRecord.reject_reason || ''
    });
  }
  
  // 线下处理记录
  if (auditRecord.offline_date) {
    realRecords.push({
      audit_time: auditRecord.offline_date,
      auditor: auditRecord.offline_processor || '未知',
      result: '线下处理',
      color: 'orange',
      comment: auditRecord.offline_comment || ''
    });
  }
  
  // 如果没有真实数据，使用模拟数据
  if (realRecords.length === 0) {
    return [
      {
        audit_time: '2025-05-15 09:30:25',
        auditor: '张三',
        result: '发票申请',
        color: 'blue',
        comment: ''
      },
      {
        audit_time: '2025-05-16 10:15:36',
        auditor: '李四',
        result: '商务审核通过',
        color: 'green',
        comment: '商品信息无误，金额正确，同意开票'
      },
      {
        audit_time: '2025-05-17 14:25:18',
        auditor: '王五',
        result: '财务审核通过',
        color: 'green',
        comment: '税率计算无误，可以开票'
      }
    ];
  }
  
  // 按时间排序
  return realRecords.sort((a, b) => new Date(a.audit_time) - new Date(b.audit_time));
};

// 获取审核记录抽屉数据
const getAuditRecordsForDrawer = () => {
  // 如果有真实数据，使用真实数据
  const realRecords = [];
  
  // 申请记录
  if (auditRecord.apply_date) {
    realRecords.push({
      audit_time: auditRecord.apply_date,
      auditor: auditRecord.applicant || '未知',
      result: '发票申请',
      color: 'blue',
      comment: ''
    });
  }
  
  // 商务审核记录
  if (auditRecord.business_approve_date) {
    realRecords.push({
      audit_time: auditRecord.business_approve_date,
      auditor: auditRecord.business_auditor || '未知',
      result: '商务审核通过',
      color: 'green',
      comment: auditRecord.business_comment || ''
    });
  }
  
  // 财务审核记录
  if (auditRecord.finance_approve_date) {
    realRecords.push({
      audit_time: auditRecord.finance_approve_date,
      auditor: auditRecord.finance_auditor || '未知',
      result: '财务审核通过',
      color: 'green',
      comment: auditRecord.finance_comment || ''
    });
  }
  
  // 开票记录
  if (auditRecord.invoice_date) {
    realRecords.push({
      audit_time: auditRecord.invoice_date,
      auditor: auditRecord.invoice_processor || '未知',
      result: '确认开票',
      color: 'green',
      comment: `发票号码：${auditRecord.invoice_no || '-'}`
    });
  }
  
  // 驳回记录
  if (auditRecord.reject_date) {
    realRecords.push({
      audit_time: auditRecord.reject_date,
      auditor: auditRecord.reject_auditor || '未知',
      result: '申请驳回',
      color: 'red',
      comment: auditRecord.reject_reason || ''
    });
  }
  
  // 线下处理记录
  if (auditRecord.offline_date) {
    realRecords.push({
      audit_time: auditRecord.offline_date,
      auditor: auditRecord.offline_processor || '未知',
      result: '线下处理',
      color: 'orange',
      comment: auditRecord.offline_comment || ''
    });
  }
  
  // 如果没有真实数据，使用模拟数据
  if (realRecords.length === 0) {
    return [
      {
        audit_time: '2025-05-15 09:30:25',
        auditor: '张三',
        result: '发票申请',
        color: 'blue',
        comment: ''
      },
      {
        audit_time: '2025-05-16 10:15:36',
        auditor: '李四',
        result: '商务审核通过',
        color: 'green',
        comment: '商品信息无误，金额正确，同意开票'
      },
      {
        audit_time: '2025-05-17 14:25:18',
        auditor: '王五',
        result: '财务审核通过',
        color: 'green',
        comment: '税率计算无误，可以开票'
      },
      {
        audit_time: '2025-05-18 16:40:52',
        auditor: '赵六',
        result: '确认开票',
        color: 'green',
        comment: '发票号码：FP20250518001'
      }
    ];
  }
  
  // 按时间排序
  return realRecords.sort((a, b) => new Date(a.audit_time) - new Date(b.audit_time));
};

const formatAmount = amount => {
  if (amount === undefined || amount === null) return "-";
  return Number(amount).toFixed(2);
};

const calculateTotal = (products) => {
  if (!products || !products.length) return 0;
  return products.reduce((sum, product) => sum + parseFloat(product.subtotal || 0), 0);
};

// 判断是否显示审核操作
const showAuditOperations = computed(() => {
  // 当标签页选中待审核、商务审核通过、财务审核通过或者申请状态是待审核、商务审核通过、财务审核通过时显示
  return ["0", "1", "2"].includes(activeTab.value) || 
         (detailRecord.value && [0, 1, 2].includes(detailRecord.value.status));
});

// 模拟数据
const mockData = [
  {
    id: 1,
    order_id: "DD20250001",
    order_source: "京东",
    order_date: "2025-05-10 10:30:00",
    invoice_title: "北京科技有限公司",
    invoice_type: "增值税专用发票",
    invoice_no: "FP20250001",
    invoice_amount: 5999.0,
    apply_date: "2025-05-11 14:20:00",
    invoice_date: "2025-05-12",
    status: 2,
    remark: "请尽快开票",
    receiver_name: "张三",
    receiver_phone: "***********",
    receiver_address: "北京市朝阳区建国路88号",
    products: [
      {
        product_name: "高性能办公电脑",
        specification: "16GB+512GB",
        product_code: "PC-001",
        product_image: "/assets/images/products/computer.jpg",
        merchant: "京东自营",
        quantity: 1,
        unit_price: 5999.0,
        subtotal: 5999.0
      }
    ]
  },
  {
    id: 2,
    order_id: "DD20250002",
    order_source: "淘宝",
    order_date: "2025-05-12 15:45:00",
    invoice_title: "上海贸易有限公司",
    invoice_type: "增值税普通发票",
    invoice_no: "",
    invoice_amount: 3299.0,
    apply_date: "2025-05-13 09:10:00",
    status: 1,
    remark: "",
    receiver_name: "李四",
    receiver_phone: "13900139001",
    receiver_address: "上海市浦东新区陆家嘴金融中心",
    products: [
      {
        product_name: "智能手机",
        specification: "8GB+256GB 黑色",
        product_code: "SP-002",
        product_image: "/assets/images/products/phone.jpg",
        merchant: "官方旗舰店",
        quantity: 1,
        unit_price: 3299.0,
        subtotal: 3299.0
      }
    ]
  },
  {
    id: 3,
    order_id: "DD20250003",
    order_source: "天猫",
    order_date: "2025-05-14 11:20:00",
    invoice_title: "",
    invoice_type: "",
    invoice_no: "",
    invoice_amount: 8798.0,
    apply_date: "",
    status: 0,
    remark: "",
    receiver_name: "王五",
    receiver_phone: "13600136001",
    receiver_address: "广州市天河区体育西路191号",
    products: [
      {
        product_name: "笔记本电脑",
        specification: "银色 16GB+512GB",
        product_code: "LT-003",
        product_image: "/assets/images/products/laptop.jpg",
        merchant: "天猫超市",
        quantity: 1,
        unit_price: 6999.0,
        subtotal: 6999.0
      },
      {
        product_name: "无线鼠标",
        specification: "黑色",
        product_code: "MS-001",
        product_image: "/assets/images/products/mouse.jpg",
        merchant: "天猫超市",
        quantity: 1,
        unit_price: 199.0,
        subtotal: 199.0
      },
      {
        product_name: "蓝牙耳机",
        specification: "白色",
        product_code: "HP-001",
        product_image: "/assets/images/products/headphone.jpg",
        merchant: "天猫超市",
        quantity: 1,
        unit_price: 1600.0,
        subtotal: 1600.0
      }
    ]
  },
  {
    id: 4,
    order_id: "DD20250004",
    order_source: "拼多多",
    order_date: "2025-05-15 16:30:00",
    invoice_title: "广州贸易有限公司",
    invoice_type: "电子发票",
    invoice_no: "",
    invoice_amount: 2499.0,
    apply_date: "2025-05-16 10:15:00",
    status: 3,
    reject_reason: "发票抬头信息有误，请修改后重新申请",
    reject_date: "2025-05-16 14:30:00",
    remark: "",
    receiver_name: "赵六",
    receiver_phone: "13700137001",
    receiver_address: "深圳市南山区科技园",
    products: [
      {
        product_name: "平板电脑",
        specification: "灰色 128GB",
        product_code: "TB-001",
        product_image: "/assets/images/products/tablet.jpg",
        merchant: "拼多多自营",
        quantity: 1,
        unit_price: 2499.0,
        subtotal: 2499.0
      }
    ]
  }
];

// 初始化时调用API
onMounted(() => {
  // 实际项目中需要调用API
  // 这里使用模拟数据，已经通过 getInvoiceList API 函数提供
  if (crudRef.value) {
    crudRef.value.refresh();
  }
});
</script>

<style lang="less" scoped>
.invoice-detail-container,
.invoice-audit-container,
.audit-records-container {
  padding: 0 16px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;

  .arco-btn {
    padding: 0 8px;
    height: 28px;
    line-height: 28px;
    font-size: 13px;
  }
}

.order-info-table {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.order-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0;
}

.info-cell {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
}

.info-cell:nth-child(even) {
  border-right: none;
}

.info-cell:nth-last-child(-n + 2) {
  border-bottom: none;
}

.info-cell.col-span-2 {
  grid-column: span 2;
  border-right: none;
}

.info-label {
  color: #86909c;
  font-size: 14px;
  margin-bottom: 8px;
}

/* 新增审核订单信息样式 */
.audit-order-info {
  display: flex;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
}

.audit-order-info-left,
.audit-order-info-right {
  flex: 1;
  padding: 16px;
}

.audit-order-info-left {
  border-right: 1px solid #f0f0f0;
}

.audit-info-item {
  margin-bottom: 12px;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
}

.audit-info-item:last-child {
  margin-bottom: 0;
}

.audit-info-label {
  color: #86909c;
  min-width: 90px;
  display: inline-block;
  font-weight: 500;
}

.audit-info-value {
  flex: 1;
  word-break: break-all;
}

.audit-info-item.full-width {
  margin-top: 16px;
}

.info-value {
  font-size: 14px;
}

.amount-value {
  color: #ff4d4f;
  font-weight: 500;
}

.product-detail-table {
  margin-top: 16px;
}

.product-detail-cell {
  display: flex;
  align-items: flex-start;
}

.product-detail-image {
  margin-right: 12px;
}

.product-detail-name {
  flex: 1;
}

.summary-row {
//   background-color: #f9f9f9;
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.summary-label {
  font-weight: 500;
  text-align: right;
  padding: 12px 16px;
  margin-right: 8px;
}

.summary-value {
  font-weight: 600;
  color: #ff4d4f;
  font-size: 16px;
}

/* 审核信息样式 */
.audit-info-row {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.audit-info-time,
.audit-info-person {
  font-size: 14px;
  line-height: 1.5;
  white-space: nowrap;
}

.audit-info-comment {
  font-size: 14px;
  line-height: 1.5;
  color: #4e5969;
}
</style>