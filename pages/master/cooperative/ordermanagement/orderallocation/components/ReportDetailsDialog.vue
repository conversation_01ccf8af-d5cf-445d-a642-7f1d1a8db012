<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => $emit('update:visible', val)"
    :width="700"
    title="报备详情"
    @cancel="$emit('cancel')"
    :footer="false"
    unmountOnClose
  >
    <div class="report-details-container">
      <!-- 报备基本信息 -->
      <div class="mb-6">
        <div class="text-lg font-bold mb-4">基本信息</div>
        <div class="report-info-grid">
          <div class="report-info-item">
            <span class="label">服务商名称：</span>
            <span class="value">{{ reportData.companyName || '-' }}</span>
          </div>
          <div class="report-info-item">
            <span class="label">客户姓名：</span>
            <span class="value">{{ reportData.reporter || '-' }}</span>
          </div>
          <div class="report-info-item">
            <span class="label">客户电话：</span>
            <span class="value">{{ reportData.customerPhone || '-' }}</span>
          </div>
          <div class="report-info-item">
            <span class="label">客户地址：</span>
            <span class="value">{{ reportData.customerAddress || '-' }}</span>
          </div>
          <div class="report-info-item">
            <span class="label">报备金额：</span>
            <span class="value">{{ reportData.reportAmount || '-' }}</span>
          </div>
          <div class="report-info-item">
            <span class="label">报备时间：</span>
            <span class="value">{{ reportData.report_time || '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 报备详细内容 -->
      <div class="mb-6">
        <div class="text-lg font-bold mb-4">报备内容</div>
        <div class="report-content-box">
          <p>{{ reportData.content || '暂无报备内容' }}</p>
        </div>
      </div>

      <!-- 附件信息 -->
      <div class="mb-6" v-if="reportData.attachments && reportData.attachments.length > 0">
        <div class="text-lg font-bold mb-4">附件信息</div>
        <div class="attachments-list">
          <div v-for="(attachment, index) in reportData.attachments" :key="index" class="attachment-item">
            <a-image
              :src="attachment.url"
              :width="100"
              :height="100"
              fit="cover"
            />
            <div class="attachment-name">{{ attachment.name }}</div>
          </div>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="dialog-footer">
        <a-button @click="$emit('cancel')">关闭</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  reportData: {
    type: Object,
    default: () => ({})
  }
});

// 定义组件事件
defineEmits(['update:visible', 'cancel']);
</script>

<style scoped>
.report-details-container {
  padding: 0 16px;
}

.report-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
}

.report-info-item {
  display: flex;
}

.label {
  color: #666;
  min-width: 100px;
}

.value {
  font-weight: 500;
}

.report-content-box {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 4px;
  min-height: 100px;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.attachment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.attachment-name {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}
</style>
