<!--
 - 订单管理页面
 - 包含订单列表、订单详情、订单操作等功能
-->
<template>
  <div class="order-list-container">
    <div class="search-area">
      <!-- 顶部统计卡片 -->
      <!-- <div class="stats-cards-container">
          <div class="stats-scroll-btn stats-scroll-left" @click="scrollStatsCards('left')">
            <icon-left />
          </div>
          <div class="stats-cards" ref="statsCardsRef">
            <div 
              v-for="(status, key) in statusMap" 
              :key="key"
              class="stats-card" 
              :class="{ active: activeStatsCard === key }"
              @click="setActiveStatsCard(key)"
            >
              <div class="stats-corner-mark" v-if="activeStatsCard === key">
                <icon-check class="check-icon" />
              </div>
              <div class="stats-title">{{ status.text }}</div>
              <div class="stats-value">{{ getStatusCount(key) }}</div>
            </div>
          </div>
          <div class="stats-scroll-btn stats-scroll-right" @click="scrollStatsCards('right')">
            <icon-right />
          </div>
      </div>-->

      <!-- 搜索表单 -->
      <div class="search-form">
        <a-form :model="search" layout="inline">
          <a-row :gutter="16" style="width: 100%">
            <a-col :span="6">
              <a-form-item field="order_no" label="订单编号">
                <a-input v-model="search.order_no" placeholder="请输入订单编号" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item field="order_source" label="订单来源">
                <a-select v-model="search.order_source" placeholder="请选择订单来源" allow-clear>
                  <a-option
                    v-for="item in orderSourceOptions"
                    :key="item.value"
                    :value="item.value"
                  >{{ item.label }}</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item field="order_type" label="订单类型">
                <a-select v-model="search.order_type" placeholder="请选择订单类型" allow-clear>
                  <a-option
                    v-for="item in orderTypeOptions"
                    :key="item.value"
                    :value="item.value"
                  >{{ item.label }}</a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item field="merchant_name" label="商户名称">
                <a-input v-model="search.merchant_name" placeholder="请输入商户名称" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row :gutter="16" style="width: 100%; margin-top: 16px;">
            <a-col :span="6">
              <a-form-item field="salesman_name" label="业务员">
                <a-input v-model="search.salesman_name" placeholder="请输入业务员姓名" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item field="product_name" label="商品名称">
                <a-input v-model="search.product_name" placeholder="请输入商品名称" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item field="order_time" label="下单时间">
                <a-range-picker v-model="search.order_time" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item field="receiver_name" label="收货人">
                <a-input v-model="search.receiver_name" placeholder="请输入收货人姓名" allow-clear />
              </a-form-item>
            </a-col>
          </a-row>

          <div class="search-actions">
            <a-space>
              <a-button type="primary" @click="handleSearch">查询</a-button>
              <a-button @click="resetSearch">重置</a-button>
              <a-button type="primary" status="success">导出订单</a-button>
              <a-button type="primary" status="warning">导出记录</a-button>
              <a-button type="primary" @click="showAddOrderModal">添加订单(手工录入)</a-button>
              <a-button type="primary" @click="showTransferOrderModal">转移订单</a-button>
            </a-space>
          </div>
        </a-form>
      </div>
    </div>
    <!-- 顶部标签页 -->
    <div class="order-tabs">
      <div
        v-for="(tab, key) in statusMap"
        :key="key"
        class="tab-item"
        :class="{ active: activeTab === key }"
        @click="activeTab = key; handleTabChange(key)"
      >{{ tab.text }}</div>
    </div>
    <!-- 表格部分 -->
    <a-table
      :columns="columns"
      :data="tableData"
      :pagination="false"
      :span-method="spanMethod"
      :selected-keys="selectedRowKeys"
      @update:selected-keys="(keys) => selectedRowKeys = keys"
      row-key="rowId"
      class="order-table"
      :hoverable="false"
    >
      <template #productInfo-cell="{ record }">
        <div v-if="record.type === 'header'" class="order-header-cell">
          <div class="order-header-left">
            <!-- 单个订单选择复选框 -->
            <a-checkbox
              :model-value="selectedRowKeys.includes(record.orderId)"
              @change="(checked) => handleSingleOrderSelect(checked, record.orderId)"
              class="order-checkbox"
            />
            <span class="order-id-text">订单编号 {{ record.order_no }}</span>
            <a-button
              shape="circle"
              size="mini"
              class="copy-btn-inline"
              @click="copyOrderId(record.order_no)"
            >
              <icon-copy />
            </a-button>
            <span>下单时间 {{ record.order_time }}</span>
            <span>订单来源 {{ record.order_source_text }}</span>
          </div>
          <div class="order-header-right">
            <a-button type="text" size="small" class="add-btn">
              <icon-plus />添加标注
            </a-button>
          </div>
        </div>
        <div v-else class="product-info-cell-content">
          <img :src="record.imageUrl || '/assets/images/default-product.png'" class="product-image" />
          <div class="product-details">
            <div class="product-name">{{ record.product_name }}</div>
            <div class="product-meta">
              <div>规格：{{ record.spec }}</div>
              <div>商品编号：{{ record.product_id || '-' }}</div>
              <div class="product-tags">
                <span class="risk-tag" v-if="record.isRisk">风险商品</span>
                <span class="days-tag">支持30天退换</span>
              </div>
              <div class="store-tags">
                <span class="store-tag">小店自营</span>
                <span class="goods-tag">商品卡</span>
              </div>
            </div>
          </div>
        </div>
      </template>

      <template #price-quantity-cell="{ record }">
        <div v-if="record.type === 'details'" class="price-cell-content">
          <div class="price">¥{{ record.price }}</div>
          <div class="quantity">x{{ record.quantity }}</div>
        </div>
      </template>

      <template #order-type-cell="{ record }">
        <div
          v-if="record.type === 'details'"
          class="order-type-cell-content"
        >{{ record.order_type_text || getOrderTypeText(record.order_type) }}</div>
      </template>

      <template #rate-cell="{ record }">
        <div v-if="record.type === 'details'" class="rate-cell-content">{{ record.rate || 0 }}%</div>
      </template>

      <template #payment-cell="{ record }">
        <div v-if="record.type === 'details'" class="payment-cell-content">
          <div class="price highlight">¥{{ record.actual_amount }}</div>
        </div>
      </template>

      <template #consumer-cell="{ record }">
        <div v-if="record.type === 'details'" class="consumer-info">{{ record.receiver_name }}</div>
      </template>

      <template #address-cell="{ record }">
        <div v-if="record.type === 'details'" class="address">{{ record.receiver_address }}</div>
      </template>

      <template #salesman-cell="{ record }">
        <div
          v-if="record.type === 'details'"
          class="salesman-cell-content"
        >{{ record.salesman_name }}</div>
      </template>

      <template #created-time-cell="{ record }">
        <div
          v-if="record.type === 'details'"
          class="created-time-cell-content"
        >{{ record.created_at }}</div>
      </template>

      <template #status-cell="{ record }">
        <div
          v-if="record.type === 'details'"
          class="status-tag"
          :class="getStatusClass(record.order_status)"
        >{{ getStatusText(record.order_status) }}</div>
      </template>

      <template #operations-cell="{ record }">
        <div
          v-if="record.type === 'details' && record.productIndex === 0"
          class="operations-cell-content"
        >
          <div class="operation-buttons">
            <!-- 所有订单状态下都可以查看订单详情 -->
            <a-button type="text" size="small" @click="viewOrderDetail(record.originalId)">订单详情</a-button>

            <!-- 待发货状态 -->
            <template v-if="record.order_status === 'toShip'">
              <a-button type="text" size="small" status="success">发货</a-button>
            </template>

            <!-- 待付款状态 -->
            <template v-if="record.order_status === 'toPay'">
              <a-button type="text" size="small" status="success">确认付款</a-button>
            </template>

            <!-- 待接单状态 -->
            <!-- <template v-if="record.order_status === 'pending'">
                <a-button type="text" size="small" status="success">接单</a-button>
                <a-button type="text" size="small" status="warning">拒绝</a-button>
            </template>-->

            <!-- 待收货状态 -->
            <template v-if="record.order_status !== 'pending'">
              <a-button
                type="text"
                size="small"
                @click="openUploadModal(record.id, record.order_no)"
              >上传合同附件</a-button>
            </template>

            <template v-if="record.order_status === 'success'">
              <a-button type="text" size="small" @click="viewContract(record.id)">查看合同</a-button>
            </template>

            <!-- 查看合同按钮 -->
            <a-button type="text" size="small" @click="viewContract(record.originalId)">查看合同</a-button>
            <a-button type="text" size="small" @click="viewContract(record.originalId)">订单分配</a-button>

            <!-- 废止按钮 -->
            <a-button
              type="text"
              size="small"
              status="danger"
              @click="cancelOrder(record.originalId)"
            >废止</a-button>
          </div>
        </div>
      </template>
    </a-table>

    <!-- 分页组件 -->
    <div class="pagination-container fixed-pagination">
      <a-pagination
        :total="total"
        :current="currentPage"
        :page-size="pageSize"
        show-total
        show-jumper
        show-page-size
        @change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- 合同附件上传弹窗 -->
    <a-modal
      :visible="uploadModalVisible"
      @update:visible="(val) => uploadModalVisible = val"
      title="上传合同附件"
      @cancel="() => { uploadModalVisible = false; fileList = []; }"
      :footer="false"
      width="500px"
    >
      <div class="mb-4">
        <p class="text-gray-600 mb-2">订单编号：{{ currentUploadOrderNo }}</p>
        <a-upload
          :fileList="fileList"
          @change="(files) => fileList = files"
          :custom-request="handleUpload"
          :before-upload="beforeUpload"
          :limit="1"
          :show-remove-button="true"
          accept=".pdf,.doc,.docx"
          drag
        >
          <template #upload-button>
            <div class="arco-upload-slide-button">
              <div class="arco-upload-slide-button-text">
                <icon-upload />
                <p>点击或拖拽文件到此处上传</p>
                <p class="text-gray-400 text-xs">支持 PDF、Word 格式，不超过20MB</p>
              </div>
            </div>
          </template>
        </a-upload>
      </div>
    </a-modal>

    <!-- 订单详情抽屉 -->
    <a-drawer
      :visible="detailDrawerVisible"
      @update:visible="(val) => detailDrawerVisible = val"
      title="订单详情"
      width="600"
      :footer="false"
    >
      <div class="order-detail">
        <a-card title="基本信息">
          <a-descriptions :data="orderBaseInfo" layout="inline-horizontal" bordered />
        </a-card>

        <a-card title="收货人信息">
          <a-descriptions :data="receiverInfo" layout="inline-horizontal" bordered />
        </a-card>

        <a-card title="商品信息">
          <a-table
            :columns="productColumns"
            :data="currentOrder && currentOrder.products ? currentOrder.products : []"
            :pagination="false"
          />
        </a-card>

        <a-card v-if="currentOrder && currentOrder.logistics" title="物流信息">
          <a-descriptions :data="logisticsInfo" layout="inline-horizontal" bordered />
        </a-card>

        <a-card title="操作日志">
          <a-table
            :columns="logColumns"
            :data="currentOrder && currentOrder.logs ? currentOrder.logs : []"
            :pagination="false"
          />
        </a-card>
      </div>
    </a-drawer>

    <!-- 转移订单弹窗 -->
    <a-modal
      :visible="transferModalVisible"
      @update:visible="(val) => transferModalVisible = val"
      title="转移订单"
      @ok="confirmTransferOrder"
    >
      <a-form :model="transferForm">
        <a-form-item field="salesmanId" label="接收业务员" required>
          <a-select v-model="transferForm.salesmanId" placeholder="请选择接收业务员">
            <a-option v-for="item in salesmanList" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="remark" label="转移备注">
          <a-textarea v-model="transferForm.remark" placeholder="请输入转移备注" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 废止订单弹窗 -->
    <a-modal
      :visible="cancelModalVisible"
      @update:visible="(val) => cancelModalVisible = val"
      title="废止订单"
      @ok="confirmCancelOrder"
    >
      <a-form :model="cancelForm">
        <a-form-item field="reason" label="废止原因" required>
          <a-select v-model="cancelForm.reason" placeholder="请选择废止原因">
            <a-option
              v-for="(reason, index) in cancelReasons"
              :key="index"
              :value="reason"
            >{{ reason }}</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="remark" label="废止备注">
          <a-textarea v-model="cancelForm.remark" placeholder="请输入废止备注" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 创建订单弹窗 -->
    <a-modal
      :visible="createOrderModalVisible"
      @update:visible="(val) => createOrderModalVisible = val"
      title="创建订单"
      @ok="confirmCreateOrder"
      width="50%"
    >
      <a-form :model="orderForm" layout="vertical">
        <a-divider>基本信息</a-divider>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item field="order_source" label="订单来源" required>
              <a-select v-model="orderForm.order_source" placeholder="请选择订单来源">
                <a-option
                  v-for="item in orderSourceOptions"
                  :key="item.value"
                  :value="item.value"
                >{{ item.label }}</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item field="order_no" label="订单编号" required>
              <a-input v-model="orderForm.order_no" placeholder="请输入订单编号" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item field="order_time" label="下单时间" required>
              <a-date-picker
                v-model="orderForm.order_time"
                style="width: 100%"
                show-time
                placeholder="请选择下单时间"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item field="actual_amount" label="支付金额" required>
              <a-input-number
                v-model="orderForm.actual_amount"
                placeholder="请输入支付金额"
                prefix="￥"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item field="payment_method" label="支付方式" required>
              <a-select v-model="orderForm.payment_method" placeholder="请选择支付方式">
                <a-option value="alipay">支付宝</a-option>
                <a-option value="wechat">微信支付</a-option>
                <a-option value="bank">银行转账</a-option>
                <a-option value="cash">现金</a-option>
                <a-option value="other">其他</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-divider>商品信息</a-divider>
        <a-table :columns="createProductColumns" :data="orderForm.products" :pagination="false">
          <template #empty>
            <div class="empty-wrapper">
              <div class="empty-content">
                <icon-inbox style="fontSize: 48px; color: rgb(var(--gray-3));" />
                <div class="empty-text">暂无商品</div>
              </div>
            </div>
          </template>
          <!-- 商品名称列 -->
          <template #product_name="{ record, rowIndex }">
            <a-input
              v-model="record.product_name"
              placeholder="请输入商品名称"
              @change="() => updateProductInfo(rowIndex)"
            />
          </template>
          <!-- 商品SKU列 -->
          <template #sku="{ record, rowIndex }">
            <a-input
              v-model="record.sku"
              placeholder="请输入商品SKU"
              @change="() => updateProductInfo(rowIndex)"
            />
          </template>
          <!-- 数量列 -->
          <template #quantity="{ record }">
            <a-input-number v-model="record.quantity" min="1" style="width: 100%" />
          </template>
          <!-- 单价列 -->
          <template #price="{ record }">
            <a-input-number v-model="record.price" min="0" prefix="￥" style="width: 100%" />
          </template>
          <!-- 删除操作列 -->
          <template #operation="{ record, rowIndex }">
            <a-button type="text" status="danger" size="small" @click="removeProduct(rowIndex)">
              <template #icon>
                <icon-delete />
              </template>
              删除
            </a-button>
          </template>
        </a-table>
        <div style="margin-top: 16px;">
          <a-button type="dashed" long @click="addProduct">
            <template #icon>
              <icon-plus />
            </template>
            添加商品
          </a-button>
        </div>

        <a-divider>收货信息</a-divider>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item field="receiver_name" label="收货人" required>
              <a-input v-model="orderForm.receiver_name" placeholder="请输入收货人姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item field="receiver_phone" label="手机号" required>
              <a-input v-model="orderForm.receiver_phone" placeholder="请输入收货人手机号" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item field="receiver_region" label="省市区" required>
              <a-space>
                <a-select
                  v-model="orderForm.receiver_province"
                  placeholder="省"
                  style="width: 120px"
                >
                  <a-option value="北京市">北京市</a-option>
                  <a-option value="上海市">上海市</a-option>
                  <a-option value="广东省">广东省</a-option>
                  <!-- 其他省份选项 -->
                </a-select>
                <a-select v-model="orderForm.receiver_city" placeholder="市" style="width: 120px">
                  <a-option value="北京市">北京市</a-option>
                  <a-option value="上海市">上海市</a-option>
                  <a-option value="广州市">广州市</a-option>
                  <!-- 其他城市选项 -->
                </a-select>
                <a-select
                  v-model="orderForm.receiver_district"
                  placeholder="区/县"
                  style="width: 120px"
                >
                  <a-option value="海淀区">海淀区</a-option>
                  <a-option value="朝阳区">朝阳区</a-option>
                  <a-option value="天河区">天河区</a-option>
                  <!-- 其他区县选项 -->
                </a-select>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item field="receiver_address" label="收货地址" required>
              <a-textarea v-model="orderForm.receiver_address" placeholder="请输入详细收货地址" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>
  
  <script>
export default { name: "master-customer-ordermanagement" };
</script>
  
  <script setup>
import { ref, reactive, computed, onMounted, watch, nextTick, h } from "vue";
import { Checkbox as ACheckbox } from "@arco-design/web-vue";
import { Message, Modal } from "@arco-design/web-vue";
import { useRouter } from "vue-router";
import cooperativeApi from "@/api/master/cooperative";

const router = useRouter();

// 页面元数据
definePageMeta({
  name: "master-cooperative-ordermanagement-orderlist",
  path: "/master/cooperative/ordermanagement/orderlist"
});

// 引用
const statsCardsRef = ref(null);
const activeTab = ref("pending"); // 默认选中待接单标签页
const activeStatsCard = ref("pending"); // 默认选中待接单统计卡片
const selectedRowKeys = ref([]);
const detailDrawerVisible = ref(false);
const transferModalVisible = ref(false);
const cancelModalVisible = ref(false);
const createOrderModalVisible = ref(false);
const currentOrder = ref(null);
const currentOrderId = ref(null);
const search = ref({
  order_no: "",
  order_source: undefined,
  order_type: undefined,
  merchant_name: "",
  salesman_name: "",
  product_name: "",
  order_time: [],
  receiver_name: ""
});
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const tableData = ref([]);

// 所有可选行的key
const selectableRowKeysOnPage = computed(() => {
  // 收集所有订单的ID
  const orderIds = new Set();
  tableData.value
    .filter(row => row.type === "header")
    .forEach(row => {
      if (row.orderId) {
        orderIds.add(row.orderId);
      }
    });
  return Array.from(orderIds);
});

// 刷新页面时重置选中状态
watch(tableData, () => {
  // 清除已选中的行
  selectedRowKeys.value = [];
});

// 是否全选当前页
const isAllSelectedOnPage = computed(() => {
  const pageKeys = selectableRowKeysOnPage.value;
  if (pageKeys.length === 0) return false;
  return pageKeys.every(key => selectedRowKeys.value.includes(key));
});

// 是否部分选中
const isIndeterminate = computed(() => {
  const pageKeys = selectableRowKeysOnPage.value;
  const selectedOnPageCount = pageKeys.filter(key =>
    selectedRowKeys.value.includes(key)
  ).length;
  return selectedOnPageCount > 0 && selectedOnPageCount < pageKeys.length;
});

// 表单数据
const transferForm = reactive({
  salesmanId: "",
  remark: ""
});

const cancelForm = reactive({
  reason: "",
  remark: ""
});

const orderForm = reactive({
  // 基本信息
  order_source: "",
  order_no: "",
  order_time: "",
  actual_amount: 0,
  payment_method: "",

  // 收货信息
  receiver_name: "",
  receiver_phone: "",
  receiver_province: "",
  receiver_city: "",
  receiver_district: "",
  receiver_address: "",

  // 商品信息
  products: []
});

// 选项数据
const salesmanList = ref([]);
const cancelReasons = [
  "客户取消订单",
  "商品缺货",
  "价格变动",
  "物流原因",
  "质量问题",
  "其他原因"
];

const orderSourceOptions = [
  { label: "线上商城", value: "online" },
  { label: "线下门店", value: "offline" },
  { label: "电话订购", value: "phone" },
  { label: "微信小程序", value: "wechat" },
  { label: "平台导入", value: "platform" }
];

const orderTypeOptions = [
  { label: "自然单", value: "normal" },
  { label: "商城报备单", value: "presale" },
  { label: "竞价自然单", value: "group" },
  { label: "竞价报备单", value: "wholesale" }
];

// 状态映射
const statusMap = {
  pending: { text: "待接单", color: "orange" },
  toShip: { text: "待发货", color: "blue" },
  shipped: { text: "待收货", color: "purple" },
  toPay: { text: "待付款", color: "red" },
  success: { text: "交易成功", color: "green" },
  closed: { text: "已关闭", color: "gray" },
  exception: { text: "异常订单", color: "magenta" }
};

// 获取状态文本和颜色
const getStatusText = status => statusMap[status]?.text || "未知状态";
const getStatusColor = status => statusMap[status]?.color || "default";

// 获取订单类型文本
const getOrderTypeText = type => {
  const typeMap = {
    normal: "自然单",
    presale: "商城报备单",
    group: "竞价自然单",
    wholesale: "竞价报备单"
  };
  return typeMap[type] || type || "未知类型";
};

// 订单详情相关数据
const orderBaseInfo = computed(() => {
  if (!currentOrder.value) return [];

  // 格式化时间
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(parseInt(timestamp));
    return date.toLocaleString('zh-CN');
  };

  // 状态映射
  const statusMap = {
    0: 'pending',    // 待接单
    1: 'toPay',      // 待付款
    2: 'toShip',     // 待发货
    3: 'shipped',    // 待收货
    4: 'success',    // 交易成功
    5: 'closed',     // 已关闭
    6: 'exception'   // 异常订单
  };

  const orderStatus = statusMap[currentOrder.value.order_status] || 'pending';

  return [
    { label: "订单编号", value: currentOrder.value.id || currentOrder.value.order_no },
    { label: "第三方订单号", value: currentOrder.value.third_party_order_sn || '-' },
    { label: "下单时间", value: formatTime(currentOrder.value.created_at) },
    { label: "订单来源", value: currentOrder.value.channel_name || '未知来源' },
    { label: "费率", value: `${(currentOrder.value.rate * 100) || 0}%` },
    {
      label: "订单状态",
      value: getStatusText(orderStatus)
    },
    { label: "创建时间", value: formatTime(currentOrder.value.created_at) },
    { label: "更新时间", value: formatTime(currentOrder.value.updated_at) },
    { label: "业务员", value: currentOrder.value.salesman_name || '-' },
    { label: "订单金额", value: `￥${currentOrder.value.total_amount || 0}` },
    { label: "已付金额", value: `￥${currentOrder.value.paid_amount || 0}` },
    { label: "分配金额", value: `￥${currentOrder.value.assignment_amount || 0}` }
  ];
});

const receiverInfo = computed(() => {
  if (!currentOrder.value) return [];

  return [
    { label: "收货人", value: currentOrder.value.recipient_name || '-' },
    { label: "联系电话", value: currentOrder.value.recipient_phone || '-' },
    { label: "收货地址", value: currentOrder.value.recipient_address || '-' }
  ];
});

const logisticsInfo = computed(() => {
  if (!currentOrder.value || !currentOrder.value.logistics) return [];

  return [
    { label: "物流公司", value: currentOrder.value.logistics.company_name },
    { label: "物流单号", value: currentOrder.value.logistics.tracking_no },
    { label: "发货时间", value: currentOrder.value.logistics.ship_time }
  ];
});

// 商品表格列配置
const productColumns = [
  { title: "商品名称", dataIndex: "product_name" },
  { title: "规格型号", dataIndex: "product_spec" },
  {
    title: "单价",
    dataIndex: "unit_price",
    render: ({ record }) => `￥${record.unit_price || 0}`
  },
  { title: "数量", dataIndex: "quantity" },
  {
    title: "小计",
    dataIndex: "subtotal_amount",
    render: ({ record }) => `￥${record.subtotal_amount || (record.unit_price * record.quantity) || 0}`
  }
];

// 创建订单时的商品表格列配置
const createProductColumns = [
  { title: "商品名称", dataIndex: "product_name", editable: true },
  { title: "商品SKU", dataIndex: "sku", editable: true },
  { title: "数量", dataIndex: "quantity", editable: true },
  { title: "单价", dataIndex: "price", editable: true },
  { title: "分类", dataIndex: "category", editable: false },
  { title: "品牌", dataIndex: "brand", editable: false },
  {
    title: "小计",
    dataIndex: "subtotal",
    render: ({ record }) => `￥${record.price * record.quantity || 0}`
  },
  { title: "操作", dataIndex: "operation", slotName: "operation", width: 80 }
];

// 日志表格列配置
const logColumns = [
  { title: "操作时间", dataIndex: "created_at" },
  { title: "操作人", dataIndex: "operator_name" },
  { title: "操作类型", dataIndex: "action_type" },
  { title: "操作内容", dataIndex: "content" }
];

// 表格列配置
const columns = reactive([
  {
    title: () => {
      return h("div", { class: "product-info-header" }, [
        h(ACheckbox, {
          modelValue: isAllSelectedOnPage.value,
          indeterminate: isIndeterminate.value,
          onChange: handleSelectAllOnPage
        }),
        h("span", { class: "product-info-title" }, "商品信息")
      ]);
    },
    slotName: "productInfo-cell",
    width: 380
  },
  {
    title: "单价/数量",
    slotName: "price-quantity-cell",
    width: 100,
    align: "center"
  },
  {
    title: "订单类型",
    slotName: "order-type-cell",
    width: 100,
    align: "center"
  },
  { title: "费率", slotName: "rate-cell", width: 80, align: "center" },
  { title: "订单金额", slotName: "payment-cell", width: 120, align: "right" },
  { title: "买家/收货人", slotName: "consumer-cell", width: 100 },
  { title: "收货地址", slotName: "address-cell", width: 180 },
  { title: "业务员", slotName: "salesman-cell", width: 100, align: "center" },
  {
    title: "创建时间",
    slotName: "created-time-cell",
    width: 150,
    align: "center"
  },
  { title: "订单状态", slotName: "status-cell", width: 100, align: "center" },
  {
    title: "操作",
    slotName: "operations-cell",
    width: 150,
    align: "center",
    fixed: "right"
  }
]);

// 处理订单数据转换
const processOrderData = orders => {
  const result = [];

  orders.forEach((order, orderIndex) => {
    // 状态映射
    const statusMap = {
      0: 'pending',    // 待接单
      1: 'toPay',      // 待付款
      2: 'toShip',     // 待发货
      3: 'shipped',    // 待收货
      4: 'success',    // 交易成功
      5: 'closed',     // 已关闭
      6: 'exception'   // 异常订单
    };

    const orderStatus = statusMap[order.order_status] || 'pending';

    // 格式化时间
    const formatTime = (timestamp) => {
      if (!timestamp) return '';
      const date = new Date(parseInt(timestamp));
      return date.toLocaleString('zh-CN');
    };

    // 添加订单头部行
    result.push({
      type: "header",
      rowId: `header-${order.id}`,
      orderId: `order-${order.id}`, // 添加orderId字段用于全选功能
      order_no: order.id, // 使用订单ID作为订单编号
      order_time: formatTime(order.created_at),
      order_source_text: order.channel_name || '未知来源',
      originalId: order.id,
      relatedOrderId: order.id
    });

    // 添加商品行
    if (order.products && order.products.length > 0) {
      order.products.forEach((product, productIndex) => {
        result.push({
          type: "details",
          rowId: `detail-${order.id}-${productIndex}`,
          orderId: `order-${order.id}`, // 添加orderId字段用于全选功能
          originalId: order.id,
          productIndex: productIndex,
          productCount: order.products.length,
          order_status: orderStatus,
          product_name: product.product_name || '未知商品',
          spec: product.product_spec || '标准规格',
          price: product.unit_price || 0,
          quantity: product.quantity || 0,
          product_id: product.product_id || '',
          imageUrl: product.product_image || "/assets/images/default-product.png",
          receiver_name: order.recipient_name || '',
          receiver_address: order.recipient_address || '',
          actual_amount: order.total_amount || 0,
          paid_amount: order.paid_amount || 0,
          rate: order.rate || 0,
          salesman_name: order.salesman_name || '',
          created_at: formatTime(order.created_at),
          isRisk: false // 暂时设为false，后续可根据实际业务逻辑判断
        });
      });
    } else {
      // 如果没有商品，添加一个空商品行
      result.push({
        type: "details",
        rowId: `detail-${order.id}-0`,
        originalId: order.id,
        productIndex: 0,
        productCount: 1,
        order_status: orderStatus,
        product_name: "无商品信息",
        spec: "-",
        price: 0,
        quantity: 0,
        imageUrl: "/assets/images/default-product.png",
        receiver_name: order.recipient_name || '',
        receiver_address: order.recipient_address || '',
        actual_amount: order.total_amount || 0,
        paid_amount: order.paid_amount || 0,
        rate: order.rate || 0,
        salesman_name: order.salesman_name || '',
        created_at: formatTime(order.created_at)
      });
    }
  });

  return result;
};

// 刷新列表
const refreshList = async () => {
  try {
    // 构建请求参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      sortField: 'created_at',
      sortOrder: 'desc'
    };

    // 添加搜索条件
    if (search.value.order_no) {
      params.orderNumber = search.value.order_no;
    }
    if (search.value.product_name) {
      params.productName = search.value.product_name;
    }
    if (search.value.receiver_name) {
      params.receiverName = search.value.receiver_name;
    }
    if (search.value.order_source) {
      params.orderSource = search.value.order_source;
    }

    // 添加订单状态筛选
    if (activeTab.value && activeTab.value !== 'all') {
      // 将前端状态映射到后端状态值
      const statusMap = {
        'pending': 0,    // 待接单
        'toPay': 1,      // 待付款
        'toShip': 2,     // 待发货
        'shipped': 3,    // 待收货
        'success': 4,    // 交易成功
        'closed': 5,     // 已关闭
        'exception': 6   // 异常订单
      };
      if (statusMap[activeTab.value] !== undefined) {
        params.orderStatus = statusMap[activeTab.value];
      }
    }

    // 添加时间范围筛选
    if (search.value.order_time && search.value.order_time.length === 2) {
      params.orderTimeStart = search.value.order_time[0];
      params.orderTimeEnd = search.value.order_time[1];
    }

    // 调用API获取数据
    const res = await cooperativeApi.orders.getList(params);

    if (res.code === 200 && res.data) {
      total.value = res.data.pageInfo?.total || 0;
      tableData.value = processOrderData(res.data.items || []);
    } else {
      Message.error(res.message || '获取订单列表失败');
      total.value = 0;
      tableData.value = [];
    }
  } catch (error) {
    console.error("获取订单列表失败:", error);
    Message.error("获取订单列表失败");
    total.value = 0;
    tableData.value = [];
  }
};

// 初始化
onMounted(async () => {
  await fetchSalesmanList();
  refreshList();
});

// 获取业务员列表
const fetchSalesmanList = async () => {
  try {
    // 这里应该调用获取业务员列表的API
    // 示例数据，实际项目中应替换为真实API调用
    salesmanList.value = [
      { id: 1, name: "张三" },
      { id: 2, name: "李四" },
      { id: 3, name: "王五" }
    ];
  } catch (error) {
    console.error("获取业务员列表失败:", error);
    Message.error("获取业务员列表失败");
  }
};

// 处理全选/取消全选
const handleSelectAllOnPage = checked => {
  const pageKeys = selectableRowKeysOnPage.value;
  if (checked) {
    const newKeys = [...new Set([...selectedRowKeys.value, ...pageKeys])];
    selectedRowKeys.value = newKeys;
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter(
      key => !pageKeys.includes(key)
    );
  }
};

// 处理单个订单选择
const handleSingleOrderSelect = (checked, rowId) => {
  if (checked) {
    if (!selectedRowKeys.value.includes(rowId)) {
      selectedRowKeys.value.push(rowId);
    }
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter(key => key !== rowId);
  }
};

// 复制订单ID
const copyOrderId = async id => {
  try {
    await navigator.clipboard.writeText(id);
    Message.success(`订单号 ${id} 已复制`);
  } catch (err) {
    Message.error("复制失败");
    console.error("Failed to copy: ", err);
  }
};

// 统计卡片滚动
const scrollStatsCards = direction => {
  if (!statsCardsRef.value) return;

  const container = statsCardsRef.value;
  const scrollAmount = 300; // 每次滚动的距离

  if (direction === "left") {
    container.scrollLeft -= scrollAmount;
  } else {
    container.scrollLeft += scrollAmount;
  }
};

// 设置活动的统计卡片
const setActiveStatsCard = cardId => {
  activeStatsCard.value = cardId;
  activeTab.value = cardId;
  handleTabChange(cardId);
};

// 获取状态对应的数量
const getStatusCount = status => {
  // TODO: 这里应该调用API获取实际的状态统计数据
  // 暂时返回0，后续可以通过调用 cooperativeApi.orders.getStats() 获取真实数据
  return 0;
};

// 处理表格单元格合并
const spanMethod = ({ record, column }) => {
  if (record.type === "header") {
    // 表头行: 第一列合并所有列
    if (column.slotName === "productInfo-cell") {
      return { rowspan: 1, colspan: columns.length };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
  if (record.type === "details") {
    // 详情行: 对特定列进行垂直合并
    const isFirstProduct = record.productIndex === 0;
    const rowspan = isFirstProduct ? record.productCount : 0;
    // 需要垂直合并的列 (订单级别信息)
    const colsToSpan = [
      "payment-cell",
      "consumer-cell",
      "address-cell",
      "status-cell",
      "operations-cell"
    ];

    if (colsToSpan.includes(column.slotName)) {
      return { rowspan: rowspan, colspan: 1 };
    }
  }
  // 其他单元格不合并
  return { rowspan: 1, colspan: 1 };
};

// 获取状态对应的CSS类
const getStatusClass = status => {
  return `status-${status}`;
};

// 标签页切换
const handleTabChange = key => {
  activeTab.value = key;
  refreshList();
};

// 分页处理
const handlePageChange = page => {
  currentPage.value = page;
  refreshList();
};

const handlePageSizeChange = size => {
  pageSize.value = size;
  currentPage.value = 1;
  refreshList();
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  refreshList();
};

const resetSearch = () => {
  search.value = {};
  currentPage.value = 1;
  refreshList();
};

// 注意：已删除模拟数据，现在使用真实API

// 跳转到订单详情页面
const goToDetail = id => {
  router.push(`/master/customer/ordermanagement/detail/${id}`);
};

// 查看订单详情
const viewOrderDetail = async id => {
  try {
    const res = await cooperativeApi.orders.getDetail(id);
    if (res.code === 200) {
      currentOrder.value = res.data;
      detailDrawerVisible.value = true;
    } else {
      Message.error(res.message || "获取订单详情失败");
    }
  } catch (error) {
    console.error("获取订单详情失败:", error);
    Message.error("获取订单详情失败");
  }
};

// 合同附件上传相关
const uploadModalVisible = ref(false);
const currentUploadOrderId = ref('');
const currentUploadOrderNo = ref('');
const uploadLoading = ref(false);
const fileList = ref([]);

// 打开上传合同附件弹窗
const openUploadModal = (orderId, orderNo) => {
  currentUploadOrderId.value = orderId;
  currentUploadOrderNo.value = orderNo;
  uploadModalVisible.value = true;
};

// 处理文件上传前
const beforeUpload = (file) => {
  // 验证文件类型
  const isValidType = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type);
  if (!isValidType) {
    Message.error('只能上传PDF或Word文档');
    return false;
  }
  // 验证文件大小（限制为20MB）
  const isLt20M = file.size / 1024 / 1024 < 20;
  if (!isLt20M) {
    Message.error('文件大小不能超过20MB');
    return false;
  }
  return true;
};

// 处理文件上传
const handleUpload = async (options) => {
  try {
    uploadLoading.value = true;
    const { file } = options;
    
    // TODO: 调用上传API
    // const formData = new FormData();
    // formData.append('file', file);
    // formData.append('orderId', currentUploadOrderId.value);
    // const response = await uploadContractFile(formData);
    
    // 模拟上传成功
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    Message.success('合同附件上传成功');
    uploadModalVisible.value = false;
    fileList.value = [];
  } catch (error) {
    console.error('上传失败:', error);
    Message.error('上传失败，请重试');
  } finally {
    uploadLoading.value = false;
  }
};

// 查看合同
const viewContract = id => {
  Modal.info({
    title: "合同查看",
    content: `正在查看订单 ${id} 的合同信息，该功能暂未实现。`
  });
};

// 显示订单详情
const showOrderDetail = order => {
  currentOrder.value = order;
  drawerVisible.value = true;
};

// 显示添加订单弹窗
const showAddOrderModal = () => {
  // 重置表单
  Object.keys(orderForm).forEach(key => {
    if (Array.isArray(orderForm[key])) {
      orderForm[key] = [];
    } else if (typeof orderForm[key] === "number") {
      orderForm[key] = 0;
    } else {
      orderForm[key] = "";
    }
  });
  createOrderModalVisible.value = true;
};

// 显示转移订单弹窗
const showTransferOrderModal = () => {
  if (selectedRowKeys.value.length === 0) {
    Message.warning("请选择要转移的订单");
    return;
  }
  transferForm.salesmanId = "";
  transferForm.remark = "";
  transferModalVisible.value = true;
};

// 取消订单
const cancelOrder = id => {
  currentOrderId.value = id;
  cancelModalVisible.value = true;
};

// 转移订单
const transferOrder = id => {
  currentOrderId.value = id;
  showTransferOrderModal();
  transferModalVisible.value = true;
};

// 创建订单
const createOrder = () => {
  createOrderModalVisible.value = true;
};

// 处理转移订单
const handleTransferOrder = () => {
  if (selectedRowKeys.value.length === 0) {
    Message.warning("请选择要转移的订单");
    return;
  }

  transferForm.salesmanId = "";
  transferForm.remark = "";
  transferModalVisible.value = true;
};

// 确认转移订单
const confirmTransferOrder = async () => {
  if (!transferForm.salesmanId) {
    Message.error("请选择接收业务员");
    return;
  }

  try {
    // TODO: 实现转移订单的API
    // 这里应该调用转移订单的API
    // const res = await cooperativeApi.orders.transfer({
    //   orderIds: selectedRowKeys.value,
    //   salesmanId: transferForm.salesmanId,
    //   remark: transferForm.remark
    // });

    // 暂时模拟成功响应
    const res = { code: 200, message: "订单转移成功" };

    if (res.code === 200) {
      Message.success("订单转移成功");
      transferModalVisible.value = false;
      selectedRowKeys.value = [];
      refreshList();
    } else {
      Message.error(res.message || "订单转移失败");
    }
  } catch (error) {
    console.error("订单转移失败:", error);
    Message.error("订单转移失败");
  }
};

// 处理创建订单
const handleCreateOrder = () => {
  // 重置表单
  Object.assign(orderForm, {
    order_source: "",
    order_type: "",
    salesman_id: "",
    rate: 0,
    receiver_name: "",
    receiver_phone: "",
    receiver_address: "",
    total_amount: 0,
    actual_amount: 0,
    products: []
  });

  createOrderModalVisible.value = true;
};

// 商品分类和品牌的mock数据
const productCatalog = [
  {
    name: "EARMCRF行动者 M3型装备",
    sku: "EARMCRF-M3",
    category: "电子设备",
    brand: "EARMCRF"
  },
  {
    name: "军用级便携式太阳能充电板",
    sku: "SOLAR-P100",
    category: "能源设备",
    brand: "星辰科技"
  },
  {
    name: "防强光夜视仪",
    sku: "NV-2000",
    category: "光学仪器",
    brand: "天际光电"
  },
  { name: "防水作战手表", sku: "WW-101", category: "作战装备", brand: "军表行" }
];

// 根据商品名称或SKU查询分类和品牌
const queryProductInfo = (name, sku) => {
  // 默认返回值
  let result = { category: "", brand: "" };

  // 如果商品名称或SKU为空，直接返回默认值
  if (!name && !sku) return result;

  // 查询商品信息
  const product = productCatalog.find(item => {
    return (name && item.name.includes(name)) || (sku && item.sku === sku);
  });

  // 如果找到匹配的商品，返回其分类和品牌
  if (product) {
    result.category = product.category;
    result.brand = product.brand;
  }

  return result;
};

// 更新商品信息，自动带出分类和品牌
const updateProductInfo = rowIndex => {
  const product = orderForm.products[rowIndex];
  if (!product) return;

  // 根据商品名称或SKU查询分类和品牌
  const info = queryProductInfo(product.product_name, product.sku);

  // 更新商品的分类和品牌
  product.category = info.category;
  product.brand = info.brand;

  // 如果找到匹配的商品，可以考虑自动填充其他信息，如单价
  if (info.category && product.price === 0) {
    // 在实际应用中，可以从产品目录中获取默认价格
    const matchedProduct = productCatalog.find(
      item =>
        (product.product_name && item.name.includes(product.product_name)) ||
        (product.sku && item.sku === product.sku)
    );

    if (matchedProduct) {
      // 这里可以设置默认价格，如果有的话
      // product.price = matchedProduct.price || 0;
    }
  }
};

// 添加商品
const addProduct = () => {
  orderForm.products.push({
    product_name: "",
    sku: "",
    quantity: 1,
    price: 0,
    category: "", // 分类，自动带出
    brand: "" // 品牌，自动带出
  });
};

// 移除商品
const removeProduct = index => {
  orderForm.products.splice(index, 1);
};

// 确认创建订单
const confirmCreateOrder = async () => {
  // 表单验证
  if (!orderForm.order_source) {
    Message.error("请选择订单来源");
    return;
  }
  if (!orderForm.order_type) {
    Message.error("请选择订单类型");
    return;
  }
  if (!orderForm.salesman_id) {
    Message.error("请选择业务员");
    return;
  }
  if (!orderForm.receiver_name) {
    Message.error("请输入收货人姓名");
    return;
  }
  if (!orderForm.receiver_phone) {
    Message.error("请输入收货人电话");
    return;
  }
  if (!orderForm.receiver_address) {
    Message.error("请输入收货地址");
    return;
  }
  if (orderForm.products.length === 0) {
    Message.error("请添加至少一个商品");
    return;
  }
  if (!orderForm.actual_amount) {
    Message.error("请输入订单金额");
    return;
  }

  try {
    const res = await orderApi.createOrder(orderForm);
    if (res.code === 200) {
      Message.success("订单创建成功");
      createOrderModalVisible.value = false;
      refreshList();
    } else {
      Message.error(res.message || "订单创建失败");
    }
  } catch (error) {
    console.error("订单创建失败:", error);
    Message.error("订单创建失败");
  }
};

// 处理导入订单
const handleImportOrder = () => {
  // 实际项目中应该打开导入弹窗或跳转到导入页面
  Message.info("导入订单功能待实现");
};

// 处理导出订单
const handleExportOrder = async () => {
  try {
    const res = await orderApi.exportOrders({
      order_status: activeTab.value
    });

    // 处理文件下载
    const blob = new Blob([res], { type: "application/vnd.ms-excel" });
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `订单数据_${new Date().toLocaleDateString()}.xlsx`;
    link.click();
    URL.revokeObjectURL(link.href);

    Message.success("订单导出成功");
  } catch (error) {
    console.error("订单导出失败:", error);
    Message.error("订单导出失败");
  }
};
</script>
  
  <style scoped lang="less">
@import "./ordermanagement.css";

// 表格空状态样式
.empty-wrapper {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-content {
  text-align: center;
}

.empty-text {
  margin-top: 8px;
  color: var(--color-text-3);
}

.product-info-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.product-info-title {
  margin-left: 10px !important;
}

.order-header-cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  line-height: 32px;
}

.order-header-left .order-checkbox {
  margin-top: 2px;
}

.order-id-text {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.order-tabs-container {
  background-color: var(--color-bg-2);
  padding: 8px 16px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.order-detail {
  .arco-card {
    margin-bottom: 16px;
  }

  .arco-card-header {
    border-bottom: 1px solid var(--color-border-2);
    padding-bottom: 8px;
  }

  .arco-descriptions {
    margin-top: 8px;
  }

  .arco-table {
    margin-top: 8px;
  }
}

.order-action-group {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 16px;

  .arco-btn {
    margin-left: 8px;
  }
}
</style>
  