<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud
      :options="crud"
      :columns="columns"
      ref="crudRef"
      :row-class="() => ''"
      :row-selection="{ type: 'none' }"
      :expandable="expandableConfig"
    >
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
      </template>

      <!-- 操作列 -->
      <!-- <template #operationBeforeExtend="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button
            type="text"
            size="small"
            @click="handleBid(record)"
            v-if="record.status === 0"
          >参与竞价</a-button>
        </div>
      </template> -->

      <!-- 展开行 -->
      <template #expand-row="{ record }">
        <div class="bid-detail-container p-0">
          <!-- 项目基本信息区域 -->
          <div class="bid-info-header bg-white p-3 border-b border-gray-200">
            <div class="bid-info-title font-bold mb-2">项目信息（反向竞价共三轮招标，剩余时间：20分钟）</div>
            <div class="bid-info-grid">
              <div class="bid-info-row">
                <div class="bid-info-item">
                  <span class="bid-info-label">招标开始时间：</span>
                  <span
                    class="bid-info-value"
                  >{{ formatDate(record.publishTime) }} {{ formatTime(record.publishTime) }}</span>
                </div>
                <div class="bid-info-item">
                  <span class="bid-info-label">招标截止时间：</span>
                  <span
                    class="bid-info-value"
                  >{{ formatDate(record.endTime) }} {{ formatTime(record.endTime) }}</span>
                </div>
              </div>
              <div class="bid-info-row">
                <div class="bid-info-item">
                  <span class="bid-info-label">招标地点：</span>
                  <span class="bid-info-value">{{ record.initiator || '反向招标' }}</span>
                </div>
                <div class="bid-info-item">
                  <span class="bid-info-label"></span>
                  <span class="bid-info-value"></span>
                </div>
              </div>
              <div class="bid-info-row">
                <div class="bid-info-item">
                  <span class="bid-info-label">项目编号：</span>
                  <span class="bid-info-value">{{ record.id || 'TP20240429008' }}</span>
                </div>
                <div class="bid-info-item">
                  <span class="bid-info-label">招标状态：</span>
                  <span class="bid-info-value">未投标</span>
                </div>
              </div>
              <div class="bid-info-row">
                <div class="bid-info-item">
                  <span class="bid-info-label">采购方式：</span>
                  <span class="bid-info-value">公开招标</span>
                </div>
                <div class="bid-info-item">
                  <span class="bid-info-label">付款方式：</span>
                  <span class="bid-info-value">合同约定</span>
                </div>
              </div>
              <div class="bid-info-row">
                <div class="bid-info-item">
                  <span class="bid-info-label">截标时间：</span>
                  <span class="bid-info-value">正常截标</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bid-detail-container p-0 mt-4">
          <!-- 商品信息 -->
          <div class="bid-info-header bg-white p-3 border-b border-gray-200">
            <div class="bid-info-title font-bold mb-2">商品信息</div>
            <div style="background-color: white; padding: 0;">
              <a-table
                :data="record.products || [{
                productName: 'H30系列模块 HXXT-SHDK-3000 型号机电组合',
                productCode: '050105429',
                unitPrice: '4000.0',
                quantity: '20'
              }]"
                :bordered="{cell:true}"
                :pagination="false"
                class="product-detail-table"
                size="medium"
                :scroll="{ x: true }"
                :stripe="false"
                :hover="false"
              >
                <template #columns>
                  <!-- 商品名称 -->
                  <a-table-column title="商品名称" data-index="productName" width="380" align="center">
                    <template #cell="{ record }">
                      <div class="product-name-cell">{{ record.productName }}</div>
                    </template>
                  </a-table-column>

                  <!-- 商品编码 -->
                  <a-table-column title="商品编码" data-index="productCode" width="120" align="center">
                    <template #cell="{ record }">
                      <div class="product-code-cell">{{ record.productCode }}</div>
                    </template>
                  </a-table-column>

                  <!-- 预算单价 -->
                  <a-table-column title="预算单价" data-index="unitPrice" align="center" width="120">
                    <template #cell="{ record }">
                      <span class="font-medium">{{ record.unitPrice }}</span>
                    </template>
                  </a-table-column>

                  <!-- 数量 -->
                  <a-table-column title="数量" data-index="quantity" align="center" width="100">
                    <template #cell="{ record }">
                      <span class="font-medium">{{ record.quantity }}</span>
                    </template>
                  </a-table-column>

                  <!-- 查看详情 -->
                  <a-table-column title="商品详情" align="center" width="120">
                    <template #cell="{ record }">
                      <a-button type="text" size="small" @click="viewProductDetail(record)">查看详情</a-button>
                    </template>
                  </a-table-column>
                </template>
              </a-table>
            </div>
          </div>
        </div>
      </template>
    </ma-crud>

    <!-- 参与竞价弹窗 -->
    <a-modal v-model="bidVisible" title="参与竞价" @cancel="closeBid" @before-ok="handleBidSubmit">
      <a-form :model="bidForm" layout="vertical">
        <a-form-item label="竞价金额" required>
          <a-input-number
            v-model="bidForm.amount"
            :min="0"
            :precision="2"
            :step="100"
            style="width: 100%"
            placeholder="请输入竞价金额"
          />
        </a-form-item>
        <a-form-item label="交付周期(天)" required>
          <a-input-number
            v-model="bidForm.deliveryDays"
            :min="1"
            :precision="0"
            style="width: 100%"
            placeholder="请输入交付周期"
          />
        </a-form-item>
        <a-form-item label="竞价说明">
          <a-textarea v-model="bidForm.remark" placeholder="请输入竞价说明" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from "vue";
import { Message } from "@arco-design/web-vue";
definePageMeta({
  name: 'master-cooperative-providermanage-bidList',
  path: '/master/cooperative/providermanage/bidList',
  title: '竞价项目列表'
})
// 表格引用
const crudRef = ref(null);

// 竞价详情展开状态
const expandedKeys = ref([]);
const toggleExpand = record => {
  const key = record.id;
  const index = expandedKeys.value.indexOf(key);
  if (index > -1) {
    expandedKeys.value.splice(index, 1);
  } else {
    expandedKeys.value.push(key);
  }
  if (crudRef.value) {
    crudRef.value.toggleRowExpansion(record, index === -1);
  }
};
const expandableConfig = reactive({
  width: 80,
  expandedRowRender: record => {
    if (record.key === "3") {
      return `My Name is ${record.name}`;
    }
  }
});
// 展开行配置
// const expandableConfig = reactive({
//   expandedRowKeys: expandedKeys,
//   // 允许通过点击行展开
//   expandRowByClick: false,
//   // 不使用自定义展开图标，使用表格原生的展开图标
//   // 这样可以确保点击图片中红框圈起的加号按钮能正常工作
// });

// 竞价弹窗相关
const bidVisible = ref(false);
const currentBid = ref(null);
const bidForm = reactive({
  amount: 0,
  deliveryDays: 7,
  remark: ""
});

const handleBid = record => {
  console.log("参与竞价", record);
  // TODO: 实现参与竞价逻辑
  currentBid.value = record;
  bidForm.amount = record.budget * 0.95; // 默认竞价金额为预算的95%
  bidForm.deliveryDays = 7;
  bidForm.remark = "";
  bidVisible.value = true;
};

// 查看商品详情函数
const viewProductDetail = product => {
  console.log("查看商品详情", product);
  // TODO: 实现查看商品详情逻辑，可以打开弹窗或跳转到详情页面
  ElMessage.success("查看商品详情：" + product.productName);
};

const closeBid = () => {
  bidVisible.value = false;
};

const handleBidSubmit = () => {
  if (!bidForm.amount) {
    Message.error("请输入竞价金额");
    return false;
  }
  if (!bidForm.deliveryDays) {
    Message.error("请输入交付周期");
    return false;
  }

  // 模拟提交竞价
  Message.success("竞价提交成功");
  bidVisible.value = false;

  // 刷新表格数据
  if (crudRef.value) {
    crudRef.value.refresh();
  }

  return true;
};

// 状态处理函数
const getStatusColor = status => {
  switch (parseInt(status)) {
    case 0:
      return "blue";
    case 1:
      return "green";
    case 2:
      return "orange";
    case 3:
      return "red";
    default:
      return "gray";
  }
};

const getStatusText = status => {
  switch (parseInt(status)) {
    case 0:
      return "竞价中";
    case 1:
      return "已中标";
    case 2:
      return "已流标";
    case 3:
      return "已关闭";
    default:
      return "未知状态";
  }
};

// 格式化日期
const formatDate = date => {
  if (!date) return "-";
  const dateObj = new Date(date);
  return dateObj.toLocaleDateString("zh-CN");
};

// 格式化时间
const formatTime = date => {
  if (!date) return "";
  const dateObj = new Date(date);
  return dateObj.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit"
  });
};

// 格式化金额
const formatAmount = amount => {
  if (!amount && amount !== 0) return "-";
  return amount.toLocaleString("zh-CN", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 计算商品总价
const calculateTotal = products => {
  if (!products || !products.length) return 0;
  return products.reduce((total, product) => {
    const price = product.unitPrice || 0;
    const quantity = product.quantity || 0;
    return total + price * quantity;
  }, 0);
};

// 表格列定义
const columns = reactive([
  {
    title: "项目标题",
    dataIndex: "title",
    search: true,
    width: 300,
    searchSpan: 8,
    ellipsis: true,
    render: ({ record }) => {
      return h(
        "div",
        {
          class: "bid-title-cell cursor-pointer",
          onClick: () => toggleExpand(record)
        },
        [
          h("div", { class: "font-medium text-blue-600" }, record.title),
          h(
            "div",
            { class: "text-gray-500 text-sm mt-1" },
            `项目编号：${record.id}`
          ),
          h(
            "div",
            { class: "text-gray-400 text-xs mt-1" },
            `发布时间：${formatDate(
              record.publishTime
            )} | 截止时间：${formatDate(record.endTime)}`
          )
        ]
      );
    }
  },
  {
    title: "项目编号",
    dataIndex: "id",
    width: 120,
    search: true,
    searchSpan: 8,
    render: ({ record }) => record.id
  },
  {
    title: "状态",
    dataIndex: "status",
    search: true,
    width: 100,
    formType: "select",
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "竞价中", value: 0 },
        { label: "已中标", value: 1 },
        { label: "已流标", value: 2 },
        { label: "已关闭", value: 3 }
      ]
    }
  },
 
  {
    title: "发布时间",
    dataIndex: "publishTime",
    width: 120,
    search: false,
    render: ({ record }) => formatDate(record.publishTime)
  },
  {
    title: "截止时间",
    dataIndex: "endTime",
    width: 120,
    render: ({ record }) => formatDate(record.endTime)
  },
  {
    title: "项目预算",
    dataIndex: "budget",
    width: 120,
    render: ({ record }) => `¥${formatAmount(record.budget)}`
  },
  {
    title: "发起单位",
    dataIndex: "initiator",
    search: false,
    width: 150,
    searchSpan: 6
  },
//   {
//     title: "操作",
//     dataIndex: "operationBeforeExtend",
//     width: 150,
//     align: "center",
//     fixed: "right"
//   }
]);

// 表格配置
const crud = reactive({
  title: "竞价项目列表",
  api: getBidList,
  searchLabelWidth: "100px",
  searchSpan: 6,
  pageSize: 10,
  menu: false,
  showExpandRow: true,
  expandRowByClick: true
});

// 模拟API函数
function getBidList(params) {
  // 模拟分页和筛选逻辑
  const { page = 1, limit = 10, ...filters } = params || {};

  // 过滤数据
  let filteredData = [...mockData];

  // 应用搜索条件筛选
  if (filters) {
    if (filters.title) {
      filteredData = filteredData.filter(item =>
        item.title.includes(filters.title)
      );
    }
    if (filters.id) {
      filteredData = filteredData.filter(item =>
        item.id.includes(filters.id)
      );
    }
    if (filters.status !== undefined && filters.status !== "") {
      filteredData = filteredData.filter(
        item => item.status === Number(filters.status)
      );
    }

    // 日期范围搜索已移除
  }

  // 计算总数
  const total = filteredData.length;

  // 分页
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  return Promise.resolve({
    success: true,
    message: "获取竞价项目列表成功",
    code: 200,
    data: {
      items: filteredData.slice(startIndex, endIndex),
      pageInfo: {
        total: total,
        currentPage: page,
        totalPage: Math.ceil(total / limit)
      }
    }
  });
}

// 模拟数据
const mockData = [
  {
    id: "BID20250001",
    title: "2025年度办公设备采购项目",
    publishTime: "2025-05-10",
    endTime: "2025-05-20",
    budget: 50000.0,
    status: 0,
    initiator: "某政府采购中心",
    contact: "李经理",
    description:
      "本项目为2025年度办公设备采购项目，包括电脑、打印机等办公设备，要求供应商具有相关资质和经验。",
    products: [
      {
        productName: "高性能商务笔记本电脑",
        specification: "i7处理器 16G内存 512G固态",
        productCode: "NB-2025-001",
        productImage: "/assets/images/products/laptop.jpg",
        quantity: 20,
        unitPrice: 6000.0
      },
      {
        productName: "激光打印机",
        specification: "黑白双面打印 30页/分钟",
        productCode: "PR-2025-002",
        productImage: "/assets/images/products/printer.jpg",
        quantity: 5,
        unitPrice: 2000.0
      }
    ]
  },
  {
    id: "BID20250002",
    title: "某企业智能会议系统采购项目",
    publishTime: "2025-05-08",
    endTime: "2025-05-18",
    budget: 80000.0,
    status: 0,
    initiator: "某科技有限公司",
    contact: "张总监",
    description:
      "采购智能会议系统一套，包括视频会议设备、音响系统、智能控制系统等，要求兼容现有办公系统。",
    products: [
      {
        productName: "4K高清视频会议摄像头",
        specification: "4K分辨率 自动追踪 降噪",
        productCode: "VC-2025-001",
        productImage: "/assets/images/products/camera.jpg",
        quantity: 2,
        unitPrice: 15000.0
      },
      {
        productName: "智能会议控制主机",
        specification: "多平台兼容 云端管理",
        productCode: "MC-2025-002",
        productImage: "/assets/images/products/controller.jpg",
        quantity: 1,
        unitPrice: 30000.0
      },
      {
        productName: "会议室音响系统",
        specification: "360°拾音 智能降噪",
        productCode: "AS-2025-003",
        productImage: "/assets/images/products/audio.jpg",
        quantity: 1,
        unitPrice: 20000.0
      }
    ]
  },
  {
    id: "BID20250003",
    title: "某学校多媒体教室设备更新项目",
    publishTime: "2025-05-05",
    endTime: "2025-05-15",
    budget: 120000.0,
    status: 1,
    initiator: "某实验中学",
    contact: "王主任",
    description:
      "对10间教室进行多媒体设备更新，包括投影仪、电子白板、音响系统等，要求设备稳定可靠，售后服务完善。",
    products: [
      {
        productName: "4K激光投影仪",
        specification: "5000流明 无线投屏",
        productCode: "PJ-2025-001",
        productImage: "/assets/images/products/projector.jpg",
        quantity: 10,
        unitPrice: 8000.0
      },
      {
        productName: "交互式电子白板",
        specification: "86英寸 4K分辨率 多点触控",
        productCode: "IWB-2025-002",
        productImage: "/assets/images/products/whiteboard.jpg",
        quantity: 10,
        unitPrice: 4000.0
      }
    ]
  },
  {
    id: "BID20250004",
    title: "某医院医疗设备采购项目",
    publishTime: "2025-04-28",
    endTime: "2025-05-12",
    budget: 500000.0,
    status: 2,
    initiator: "某三甲医院",
    contact: "刘院长",
    description:
      "采购一批医疗检测设备，包括血液分析仪、心电图机等，要求设备精度高，符合医疗器械标准。",
    products: [
      {
        productName: "全自动血液分析仪",
        specification: "高精度 快速检测 数据联网",
        productCode: "MED-2025-001",
        productImage: "/assets/images/products/medical1.jpg",
        quantity: 2,
        unitPrice: 150000.0
      },
      {
        productName: "12导联心电图机",
        specification: "高精度 便携式 触屏操作",
        productCode: "MED-2025-002",
        productImage: "/assets/images/products/medical2.jpg",
        quantity: 5,
        unitPrice: 40000.0
      }
    ]
  },
  {
    id: "BID20250005",
    title: "某企业网络安全设备升级项目",
    publishTime: "2025-04-25",
    endTime: "2025-05-10",
    budget: 300000.0,
    status: 3,
    initiator: "某金融科技公司",
    contact: "赵经理",
    description:
      "对公司网络安全设备进行全面升级，包括防火墙、入侵检测系统、安全审计系统等，要求具备高级威胁防护能力。",
    products: [
      {
        productName: "新一代防火墙",
        specification: "千兆吞吐 高级威胁防护",
        productCode: "SEC-2025-001",
        productImage: "/assets/images/products/firewall.jpg",
        quantity: 2,
        unitPrice: 80000.0
      },
      {
        productName: "入侵检测与防御系统",
        specification: "实时监控 自动响应",
        productCode: "SEC-2025-002",
        productImage: "/assets/images/products/ids.jpg",
        quantity: 1,
        unitPrice: 120000.0
      }
    ]
  }
];

// 页面加载时初始化
onMounted(() => {
  if (crudRef.value) {
    crudRef.value.refresh();
  }
});
</script>

<style lang="less" scoped>
/* 展开行容器样式 */
.bid-detail-container {
  max-width: 100%;
  overflow-x: auto;
  width: 100%;
  background: #e4e9e9f3 !important
}

:deep(.arco-table-tr-expand .arco-table-td) {
  padding: 0 !important;
  background-color: #ffffff !important;
}

:deep(.arco-table-cell-fixed-expand) {
  background-color: #f3f3f3 !important;
}

/* 项目信息头部样式 */
.bid-info-header {
  border: 1px solid #f0f0f0;
  background-color: #f9f9f9;
}

/* 信息行样式 */
.info-row {
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-label {
  font-size: 14px;
  color: #666;
  margin-right: 8px;
  display: inline-block;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #333;
}

/* 商品表格样式 */
.product-detail-table {
  :deep(.arco-table-th) {
    background-color: #f3f3f3 !important;
  }

  :deep(.arco-table-td) {
    border-color: #f3f3f3 !important;
  }

  :deep(.arco-table-border-cell .arco-table-th) {
    border-color: #f3f3f3 !important;
  }

  :deep(.arco-table-summary) {
    background-color: #f3f3f3 !important;
  }
}

/* 商品名称样式 */
.product-detail-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 展开图标样式 */
.expand-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  transition: all 0.2s;

  &:hover {
    background-color: rgba(22, 93, 255, 0.1);
  }
}

/* 标题单元格样式 */
.bid-title-cell {
  cursor: pointer;
  padding: 8px 0;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(22, 93, 255, 0.05);
  }
}

/* 标题标签样式 */
.bid-title-tag {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 8px;

  &.urgent {
    background-color: #fff2f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
  }
}

/* 金额样式 */
.text-red-500 {
  color: #ff4d4f;
}

/* 网格布局 */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.gap-4 {
  gap: 1rem;
}

.col-span-2 {
  grid-column: span 2;
}

.md\:col-span-1 {
  @media (min-width: 768px) {
    grid-column: span 1;
  }
}

.amount-value {
  color: #ff4d4f;
  font-weight: bold;
}

.product-detail-table {
  background-color: #ffffff !important;

  :deep(.arco-table-th) {
    background-color: #ffffff !important;
    font-weight: 600;
    color: #1d2129;
    font-size: 14px;
    height: 48px;
    border-bottom: 1px solid #e5e6eb;
  }

  :deep(.arco-table-container) {
    background-color: #ffffff !important;
  }

  :deep(.arco-table) {
    background-color: #ffffff !important;
  }

  :deep(.arco-table-td) {
    vertical-align: middle;
    padding: 14px 16px;
    border-color: #f0f0f0 !important;
  }

  :deep(.arco-table-tr) {
    background-color: #ffffff !important;
  }

  :deep(.arco-table-tr:hover) {
    background-color: #ffffff !important;
  }

  :deep(.arco-table-body) {
    background-color: #ffffff !important;
  }

  :deep(.arco-table-header) {
    background-color: #ffffff !important;
  }

  :deep(.arco-table-border-cell .arco-table-container) {
    border-color: #f0f0f0 !important;
  }

  :deep(.arco-table-border-cell .arco-table-container::before) {
    background-color: #f0f0f0 !important;
  }
}

.product-info-cell {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.product-name {
  font-weight: 500;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-code {
  color: #86909c;
}

/* 商品信息标题样式 */
.bid-products {
  .text-lg {
    position: relative;
    padding-left: 12px;
    margin-bottom: 16px;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #165dff;
      border-radius: 2px;
    }
  }
}

/* 招标信息样式 */
.bid-info-header {
  background-color: #fff;
  padding: 16px;
}

.bid-info-title {
  font-size: 16px;
  margin-bottom: 12px;
  //   color: #ff4d4f;
  font-weight: bold;
}

.bid-info-grid {
  width: 100%;
}

.bid-info-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.bid-info-item {
  display: flex;
  width: 50%;
  padding: 8px 0;
}

.bid-info-label {
  width: 120px;
  color: #666;
  text-align: right;
  padding-right: 8px;
}

.bid-info-value {
  flex: 1;
  color: #333;
}
</style>