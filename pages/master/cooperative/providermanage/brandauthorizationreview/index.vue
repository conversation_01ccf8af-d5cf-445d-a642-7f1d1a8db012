<template>
  <div class="ma-content-block p-4">
    <!-- 状态标签页 -->
    <div class="mb-4">
      <a-tabs :active-key="activeTab" @change="handleTabChange">
        <a-tab-pane key="-1" title="全部"></a-tab-pane>
        <a-tab-pane key="0" title="待审核"></a-tab-pane>
        <a-tab-pane key="1" title="已审核"></a-tab-pane>
        <a-tab-pane key="2" title="已驳回"></a-tab-pane>
        <a-tab-pane key="3" title="已过期"></a-tab-pane>
      </a-tabs>
    </div>

    <!-- CRUD 组件 -->
    <ma-crud
      :options="crud"
      :columns="columns"
      ref="crudRef"
      :row-class="() => ''"
      :row-selection="{ type: 'none' }"
    >
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">{{ getStatusText(record.status) }}</a-tag>
      </template>

      <!-- 操作列 -->
      <template #operationBeforeExtend="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button type="text" size="small" @click="openDetails(record)">详情</a-button>
          <!-- <template v-if="record.status === 0">
            <a-button type="text" size="small" @click="handleAudit(record)">审核</a-button>
          </template>-->
          <!-- <template v-if="record.status === 1">
            <a-button
              type="text"
              size="small"
              @click="handleToggleStatus(record)"
            >{{ record.is_active ? '停用' : '启用' }}</a-button>
          </template> -->
        </div>
      </template>
    </ma-crud>

    <!-- 品牌授权详情抽屉 -->
    <a-drawer
      :width="'70%'"
      :visible="detailVisible"
      @update:visible="(val) => detailVisible = val"
      @cancel="closeDetail"
      unmountOnClose
      :footer="true"
    >
      <template #title>品牌授权详情</template>

      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <a-button @click="closeDetail" class="mr-2">取消</a-button>
          <a-button type="primary" @click="closeDetail()" class="mr-2">确定</a-button>
          <!-- 待审核状态下显示审核按钮 -->
          <a-button
            v-if="detailRecord.status === 0"
            type="primary"
            status="success"
            @click="handleAudit(detailRecord)"
          >审核</a-button>

          <a-button
            v-if="detailRecord.status === 1 && detailRecord.is_active"
            type="primary"
            status="danger"
          
            @click="handleToggleStatus(detailRecord)"
          >停用</a-button>
          <a-button
            v-if="detailRecord.status === 1 && !detailRecord.is_active"
            type="primary"
            status="success"
           
            @click="handleToggleStatus(detailRecord)"
          >启用</a-button>
        </div>
      </template>

      <div class="provider-detail-container">
     
        <!-- 选项卡导航 -->
        <a-tabs :active-key="detailActiveTab" @update:active-key="(key) => detailActiveTab = key">
          <a-tab-pane key="1" title="基本信息">
            <div class="info-section">
              <div class="grid grid-cols-2 gap-4">
                <!-- 授权编号 -->
                <div class="info-item">
                  <div class="text-gray-500">授权编号：</div>
                  <div>{{ detailRecord.auth_code || '-' }}</div>
                </div>

                <!-- 授权品牌名称 -->
                <div class="info-item">
                  <div class="text-gray-500">授权品牌名称：</div>
                  <div>{{ detailRecord.brand_name || '-' }}</div>
                </div>

                <!-- 授权公司 -->
                <div class="info-item">
                  <div class="text-gray-500">授权公司：</div>
                  <div>{{ detailRecord.auth_company || '-' }}</div>
                </div>

                <!-- 被授权单位 -->
                <div class="info-item">
                  <div class="text-gray-500">被授权单位：</div>
                  <div>{{ detailRecord.authorized_company || '-' }}</div>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="2" title="授权内容">
            <div class="info-section">
              <div class="grid grid-cols-2 gap-4">
                <!-- 有效期 -->
                <div class="info-item">
                  <div class="text-gray-500">有效期：</div>
                  <div>{{ formatDate(detailRecord.valid_until) || '-' }}</div>
                </div>

                <!-- 授权范围 -->
                <div class="info-item col-span-2">
                  <div class="text-gray-500">授权范围：</div>
                  <div>{{ detailRecord.auth_scope || '-' }}</div>
                </div>

                <!-- 授权书文件 -->
                <div class="info-item col-span-2">
                  <div class="text-gray-500 mb-2">授权书文件：</div>
                  <div class="image-preview mb-4">
                    <a-image
                      v-if="detailRecord.auth_file"
                      :src="detailRecord.auth_file"
                      width="300"
                      height="200"
                      fit="contain"
                    />
                    <div v-else class="no-image">暂无图片</div>
                  </div>
                </div>
              </div>
            </div>
          </a-tab-pane>

          <a-tab-pane key="3" title="文件记录">
            <div class="info-section">
              <!-- 授权记录 -->
              <h4 class="text-base font-medium mb-2 text-blue-500">授权记录</h4>
              <a-table
                :columns="authRecordColumns"
                :data="detailRecord.auth_records || []"
                :pagination="false"
                :bordered="true"
                size="small"
                class="mb-4"
              ></a-table>

              <!-- 审核日志 -->
              <h4 class="text-base font-medium mb-2 text-blue-500">审核日志</h4>
              <a-table
                :columns="auditLogColumns"
                :data="detailRecord.audit_logs || []"
                :pagination="false"
                :bordered="true"
                size="small"
              ></a-table>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-drawer>

    <!-- 审核弹窗 -->
    <a-modal
      v-model:visible="auditVisible"
      title="品牌授权审核"
      @cancel="closeAudit"
      @before-ok="handleAuditSubmit"
    >
      <a-form :model="auditForm" layout="vertical">
        <a-form-item label="审核结果" required>
          <a-radio-group v-model="auditForm.result">
            <a-radio :value="1">通过</a-radio>
            <a-radio :value="2">驳回</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审核意见" required>
          <a-textarea v-model="auditForm.comment" placeholder="请输入审核意见" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";

// 状态标签页
const activeTab = ref("-1");
const handleTabChange = key => {
  activeTab.value = key;
};

// 详情抽屉
const detailVisible = ref(false);
const detailRecord = reactive({});
const detailActiveTab = ref("1");

const openDetails = record => {
  Object.assign(detailRecord, record);
  detailVisible.value = true;
};

const closeDetail = () => {
  detailVisible.value = false;
};

// 审核弹窗
const auditVisible = ref(false);
const auditForm = reactive({
  result: 1,
  comment: ""
});

const handleAudit = record => {
  Object.assign(detailRecord, record);
  auditVisible.value = true;
  auditForm.result = 1;
  auditForm.comment = "";
};

const closeAudit = () => {
  auditVisible.value = false;
};

const handleAuditSubmit = () => {
  // 提交审核结果
  console.log("提交审核结果", auditForm);
  auditVisible.value = false;
};

// 状态处理函数
const getStatusColor = status => {
  switch (parseInt(status)) {
    case 0:
      return "orange";
    case 1:
      return "green";
    case 2:
      return "red";
    case 3:
      return "gray";
    default:
      return "gray";
  }
};

const getStatusText = status => {
  switch (parseInt(status)) {
    case 0:
      return "待审核";
    case 1:
      return "已通过";
    case 2:
      return "已驳回";
    case 3:
      return "已过期";
    default:
      return "未知状态";
  }
};

// 处理启用/停用状态
const handleToggleStatus = record => {
  console.log("切换状态", record);
  // 实际项目中需要调用API
};

// 格式化日期
const formatDate = date => {
  if (!date) return "-";
  return new Date(date).toLocaleDateString("zh-CN");
};

// 表格列定义
const columns = reactive([
  {
    title: "授权编号",
    dataIndex: "auth_code",
    search: true,
    width: 120,
    searchSpan: 6
  },
  {
    title: "授权品牌名称",
    dataIndex: "brand_name",
    search: true,
    width: 180,
    searchSpan: 6
  },
  {
    title: "授权公司",
    dataIndex: "auth_company",
    search: true,
    width: 150,
    searchSpan: 6
  },
  {
    title: "被授权单位",
    dataIndex: "authorized_company",
    width: 150
  },
  {
    title: "有效期",
    dataIndex: "valid_until",
    width: 120,
    render: ({ record }) => formatDate(record.valid_until)
  },
  {
    title: "审核状态",
    dataIndex: "status",
    search: true,
    width: 100,
    formType: "select",
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "待审核", value: 0 },
        { label: "已通过", value: 1 },
        { label: "已驳回", value: 2 },
        { label: "已过期", value: 3 }
      ]
    }
  },
  {
    title: "操作",
    dataIndex: "operationBeforeExtend",
    width: 100,
    align: "center",
    fixed: "right"
  }
]);

// 表格配置
const crudRef = ref(null);
const crud = reactive({
  title: "品牌授权审核",
  api: "http://127.0.0.1:4000/api/v1/master/cooperative/brand-authorization",
  searchLabelWidth: "115px",
  searchSpan: 6,
  pageSize: 10,
  menu: false
});

// 授权记录表格列
const authRecordColumns = [
  {
    title: "记录编号",
    dataIndex: "record_id"
  },
  {
    title: "操作类型",
    dataIndex: "operation_type"
  },
  {
    title: "操作人",
    dataIndex: "operator"
  },
  {
    title: "操作时间",
    dataIndex: "operation_time",
    render: ({ record }) => formatDate(record.operation_time)
  },
  {
    title: "备注",
    dataIndex: "remark"
  }
];

// 审核日志表格列
const auditLogColumns = [
  {
    title: "日志编号",
    dataIndex: "log_id"
  },
  {
    title: "审核结果",
    dataIndex: "audit_result"
  },
  {
    title: "审核人",
    dataIndex: "auditor"
  },
  {
    title: "审核时间",
    dataIndex: "audit_time",
    render: ({ record }) => formatDate(record.audit_time)
  },
  {
    title: "审核意见",
    dataIndex: "audit_comment"
  }
];

// 模拟API函数
const getBrandAuthorizationList = params => {
  // 模拟分页和筛选逻辑
  const { page = 1, limit = 10, ...filters } = params || {};

  // 过滤数据
  let filteredData = [...mockData];

  // 根据标签页状态过滤
  if (activeTab.value !== "-1") {
    filteredData = filteredData.filter(
      item => item.status === Number(activeTab.value)
    );
  }

  // 应用搜索条件筛选
  if (filters) {
    if (filters.auth_code) {
      filteredData = filteredData.filter(item =>
        item.auth_code.includes(filters.auth_code)
      );
    }
    if (filters.brand_name) {
      filteredData = filteredData.filter(item =>
        item.brand_name.includes(filters.brand_name)
      );
    }
    if (filters.auth_company) {
      filteredData = filteredData.filter(item =>
        item.auth_company.includes(filters.auth_company)
      );
    }
    // 如果标签页已经按状态筛选，则忽略搜索条件中的状态筛选
    if (
      activeTab.value === "-1" &&
      filters.status !== undefined &&
      filters.status !== ""
    ) {
      filteredData = filteredData.filter(
        item => item.status === Number(filters.status)
      );
    }
  }

  // 计算总数
  const total = filteredData.length;

  // 分页
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const pageData = filteredData.slice(startIndex, endIndex);

  // 返回结果 - 按照组件需要的格式
  return Promise.resolve({
    success: true,
    message: "获取品牌授权列表成功",
    code: 200,
    data: {
      items: pageData,
      pageInfo: {
        total: total,
        currentPage: page,
        totalPage: Math.ceil(total / limit)
      }
    }
  });
};

// 模拟数据
const mockData = [
  {
    id: "BA001",
    auth_code: "AUTH20240101001",
    brand_name: "优质品牌A",
    auth_company: "北京优质品牌管理有限公司",
    authorized_company: "深圳市电子科技有限公司",
    auth_scope: "电子产品销售与维修服务",
    valid_until: "2025-12-31",
    status: 0,
    is_active: true,
    auth_file: "https://example.com/auth_files/auth001.pdf",
    logo: "/assets/images/brand-logos/brand-a.png",
    created_at: "2024-01-01",
    auth_records: [
      {
        record_id: "REC001",
        operation_type: "创建授权",
        operator: "张三",
        operation_time: "2024-01-01 10:30:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: []
  },
  {
    id: "BA002",
    auth_code: "AUTH20240115002",
    brand_name: "品质生活B",
    auth_company: "上海品质生活用品有限公司",
    authorized_company: "广州市家居用品销售有限公司",
    auth_scope: "家居用品销售与服务",
    valid_until: "2025-06-30",
    status: 1,
    is_active: true,
    auth_file: "https://example.com/auth_files/auth002.pdf",
    logo: "/assets/images/brand-logos/brand-b.png",
    created_at: "2024-01-15",
    auth_records: [
      {
        record_id: "REC002",
        operation_type: "创建授权",
        operator: "李四",
        operation_time: "2024-01-15 14:20:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: [
      {
        log_id: "LOG001",
        audit_result: "通过",
        auditor: "王五",
        audit_time: "2024-01-20 09:30:00",
        audit_comment: "资料齐全，符合要求"
      }
    ]
  },
  {
    id: "BA003",
    auth_code: "AUTH20240201003",
    brand_name: "时尚潮流C",
    auth_company: "杭州时尚潮流品牌管理有限公司",
    authorized_company: "北京市服装销售有限公司",
    auth_scope: "服装、鞋帽、配饰销售",
    valid_until: "2026-01-31",
    status: 2,
    is_active: false,
    auth_file: "https://example.com/auth_files/auth003.pdf",
    logo: "/assets/images/brand-logos/brand-c.png",
    created_at: "2024-02-01",
    auth_records: [
      {
        record_id: "REC003",
        operation_type: "创建授权",
        operator: "赵六",
        operation_time: "2024-02-01 11:15:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: [
      {
        log_id: "LOG002",
        audit_result: "驳回",
        auditor: "王五",
        audit_time: "2024-02-05 16:45:00",
        audit_comment: "授权文件不完整，请补充完整的授权协议"
      }
    ]
  },
  {
    id: "BA004",
    auth_code: "AUTH20240215004",
    brand_name: "科技创新D",
    auth_company: "深圳科技创新有限公司",
    authorized_company: "上海市电子产品销售有限公司",
    auth_scope: "智能电子产品销售与技术支持",
    valid_until: "2024-12-31",
    status: 1,
    is_active: true,
    auth_file: "https://example.com/auth_files/auth004.pdf",
    logo: "/assets/images/brand-logos/brand-d.png",
    created_at: "2024-02-15",
    auth_records: [
      {
        record_id: "REC004",
        operation_type: "创建授权",
        operator: "钱七",
        operation_time: "2024-02-15 09:20:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: [
      {
        log_id: "LOG003",
        audit_result: "通过",
        auditor: "王五",
        audit_time: "2024-02-20 10:30:00",
        audit_comment: "资料齐全，符合要求"
      }
    ]
  },
  {
    id: "BA005",
    auth_code: "AUTH20240301005",
    brand_name: "健康生活E",
    auth_company: "北京健康生活用品有限公司",
    authorized_company: "成都市健康产品销售有限公司",
    auth_scope: "健康食品、保健品销售",
    valid_until: "2023-12-31",
    status: 3,
    is_active: false,
    auth_file: "https://example.com/auth_files/auth005.pdf",
    logo: "/assets/images/brand-logos/brand-e.png",
    created_at: "2024-03-01",
    auth_records: [
      {
        record_id: "REC005",
        operation_type: "创建授权",
        operator: "孙八",
        operation_time: "2024-03-01 14:30:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: [
      {
        log_id: "LOG004",
        audit_result: "通过",
        auditor: "王五",
        audit_time: "2024-03-05 11:20:00",
        audit_comment: "资料齐全，符合要求"
      },
      {
        log_id: "LOG005",
        audit_result: "过期",
        auditor: "系统",
        audit_time: "2024-01-01 00:00:00",
        audit_comment: "授权已过期"
      }
    ]
  },
  {
    id: "BA006",
    auth_code: "AUTH20240315006",
    brand_name: "美食天地F",
    auth_company: "广州美食天地餐饮管理有限公司",
    authorized_company: "北京市餐饮管理有限公司",
    auth_scope: "餐饮服务、食品销售",
    valid_until: "2026-03-14",
    status: 0,
    is_active: true,
    auth_file: "https://example.com/auth_files/auth006.pdf",
    logo: "/assets/images/brand-logos/brand-f.png",
    created_at: "2024-03-15",
    auth_records: [
      {
        record_id: "REC006",
        operation_type: "创建授权",
        operator: "周九",
        operation_time: "2024-03-15 16:40:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: []
  },
  {
    id: "BA007",
    auth_code: "AUTH20240401007",
    brand_name: "运动健身G",
    auth_company: "上海运动健身器材有限公司",
    authorized_company: "深圳市体育用品销售有限公司",
    auth_scope: "运动器材、健身设备销售与维护",
    valid_until: "2025-03-31",
    status: 0,
    is_active: true,
    auth_file: "https://example.com/auth_files/auth007.pdf",
    logo: "/assets/images/brand-logos/brand-g.png",
    created_at: "2024-04-01",
    auth_records: [
      {
        record_id: "REC007",
        operation_type: "创建授权",
        operator: "吴十",
        operation_time: "2024-04-01 09:50:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: []
  },
  {
    id: "BA008",
    auth_code: "AUTH20240415008",
    brand_name: "教育培训H",
    auth_company: "北京教育培训咨询有限公司",
    authorized_company: "上海市教育科技有限公司",
    auth_scope: "教育培训服务、教材销售",
    valid_until: "2025-04-14",
    status: 1,
    is_active: true,
    auth_file: "https://example.com/auth_files/auth008.pdf",
    logo: "/assets/images/brand-logos/brand-h.png",
    created_at: "2024-04-15",
    auth_records: [
      {
        record_id: "REC008",
        operation_type: "创建授权",
        operator: "郑十一",
        operation_time: "2024-04-15 14:10:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: [
      {
        log_id: "LOG006",
        audit_result: "通过",
        auditor: "王五",
        audit_time: "2024-04-20 11:30:00",
        audit_comment: "资料齐全，符合要求"
      }
    ]
  },
  {
    id: "BA009",
    auth_code: "AUTH20240501009",
    brand_name: "旅游服务I",
    auth_company: "杭州旅游服务管理有限公司",
    authorized_company: "成都市旅游咨询有限公司",
    auth_scope: "旅游服务、票务代理",
    valid_until: "2025-04-30",
    status: 2,
    is_active: false,
    auth_file: "https://example.com/auth_files/auth009.pdf",
    logo: "/assets/images/brand-logos/brand-i.png",
    created_at: "2024-05-01",
    auth_records: [
      {
        record_id: "REC009",
        operation_type: "创建授权",
        operator: "王十二",
        operation_time: "2024-05-01 10:20:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: [
      {
        log_id: "LOG007",
        audit_result: "驳回",
        auditor: "王五",
        audit_time: "2024-05-05 15:40:00",
        audit_comment: "授权范围描述不明确，请详细说明授权内容"
      }
    ]
  },
  {
    id: "BA010",
    auth_code: "AUTH20240515010",
    brand_name: "家电品牌J",
    auth_company: "广州家电品牌管理有限公司",
    authorized_company: "北京市电器销售有限公司",
    auth_scope: "家用电器销售与售后服务",
    valid_until: "2026-05-14",
    status: 0,
    is_active: true,
    auth_file: "https://example.com/auth_files/auth010.pdf",
    logo: "/assets/images/brand-logos/brand-j.png",
    created_at: "2024-05-15",
    auth_records: [
      {
        record_id: "REC010",
        operation_type: "创建授权",
        operator: "李十三",
        operation_time: "2024-05-15 16:30:00",
        remark: "初始授权创建"
      }
    ],
    audit_logs: []
  }
];

// 页面初始化
onMounted(() => {
  // 更新CRUD配置，使用模拟API
  crud.api = getBrandAuthorizationList;
});
</script>

<script>
export default { name: "master-cooperative-brand-authorization-review" };
</script>

<style scoped lang="less">
.provider-detail-container {
  padding: 0 16px;
}

.info-section,
.qualification-section,
.agreement-section,
.business-data-section {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item .text-gray-500 {
  width: 120px;
  flex-shrink: 0;
}

.image-preview {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 16px;
  display: inline-block;
}

.no-image {
  width: 300px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
  border-radius: 4px;
}

/* 覆盖表格行高亮样式 */
:deep(.arco-table-tr) {
  cursor: default !important;
}

/* 图片上传相关样式 */
.upload-image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
}

.upload-skin {
  background-color: #f2f3f5;
  border: 1px dashed #c9cdd4;
  width: 130px;
  height: 130px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.upload-skin:hover {
  border-color: #165dff;
  background-color: #f7f8fa;
}

.upload-skin .icon {
  font-size: 24px;
  color: #86909c;
  margin-bottom: 8px;
}

.upload-skin .title {
  font-size: 14px;
  color: #86909c;
}

.upload-tip {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.image-list {
  position: relative;
  width: 130px;
  height: 130px;
  background-color: #f7f8fa;
  border: 1px solid #e5e6eb;
  border-radius: 2px;
  overflow: hidden;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f53f3f;
  border: none;
}

.delete-btn:hover {
  background-color: rgba(255, 255, 255, 1);
}

:deep(.arco-table-tr:hover td) {
  background-color: var(--color-bg-2) !important;
}

:deep(.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr-selected td) {
  background-color: transparent !important;
}

:deep(.arco-table-tr:focus-within) {
  background-color: transparent !important;
  outline: none !important;
  border: none !important;
}

:deep(.arco-table-tr.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr.arco-table-tr-selected td) {
  background-color: transparent !important;
}

:deep(.arco-table-tr.arco-table-tr-selected:hover td) {
  background-color: var(--color-bg-2) !important;
}

:deep(.arco-table-td) {
  border-right: none !important;
}
</style>