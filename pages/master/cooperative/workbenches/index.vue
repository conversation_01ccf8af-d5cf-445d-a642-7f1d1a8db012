<!--
 - 合作伙伴工作台页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <div class="mb-4">
      <div class="text-xl font-bold">工作台</div>
    </div>

    <!-- 数据统计 -->
    <!-- <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <a-card v-for="(item, index) in statistics" :key="index" class="h-full">
        <div class="flex items-center">
          <div
            class="w-12 h-12 rounded-full flex items-center justify-center mr-3"
            :style="{backgroundColor: item.bgColor}"
          >
            <template v-if="item.title === '本月销售额'">
              <icon-wallet class="text-xl" :style="{color: item.iconColor}" />
            </template>
            <template v-else-if="item.title === '本月订单数'">
              <icon-shopping-cart class="text-xl" :style="{color: item.iconColor}" />
            </template>
            <template v-else-if="item.title === '本月新增服务商'">
              <icon-user-add class="text-xl" :style="{color: item.iconColor}" />
            </template>
            <template v-else-if="item.title === '本月退款率'">
              <icon-undo class="text-xl" :style="{color: item.iconColor}" />
            </template>
          </div>
          <div>
            <div class="text-gray-500 text-sm">{{ item.title }}</div>
            <div class="text-xl font-bold">{{ item.value }}</div>
          </div>
        </div>
      </a-card>
    </div> -->

    <!-- 待办事项 -->
    <a-card class="mb-6">
      <div class="mb-4">
        <h2 class="text-lg font-bold">待办事项</h2>
      </div>

      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <!-- 待处理工单 -->
        <!-- <div
          class="bg-purple-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/master/cooperative/tickets')"
        >
          <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-2">
            <icon-file-exception class="text-purple-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待处理工单</div>
          <div class="text-xl font-bold">{{ todoData.pendingTickets }}</div>
        </div> -->

        <!-- 待审核商品数 -->
        <div
          class="bg-blue-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/master/cooperative/products/review')"
        >
          <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-2">
            <icon-gift class="text-blue-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待审核商品数</div>
          <div class="text-xl font-bold">{{ todoData.pendingProducts }}</div>
        </div>

        <!-- 待审核发货 -->
        <div
          class="bg-green-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/master/cooperative/shipments/review')"
        >
          <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
            <icon-send class="text-green-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待审核发货</div>
          <div class="text-xl font-bold">{{ todoData.pendingShipments }}</div>
        </div>

        <!-- 待审核退款 -->
        <div
          class="bg-orange-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/master/cooperative/refunds')"
        >
          <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-2">
            <icon-undo class="text-orange-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待审核退款</div>
          <div class="text-xl font-bold">{{ todoData.pendingRefunds }}</div>
        </div>

        <!-- 服务商入驻审核 -->
        <div
          class="bg-cyan-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/master/cooperative/vendors/review')"
        >
          <div class="w-12 h-12 rounded-full bg-cyan-100 flex items-center justify-center mb-2">
            <icon-user class="text-cyan-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">服务商入驻审核</div>
          <div class="text-xl font-bold">{{ todoData.pendingVendors }}</div>
        </div>

        <!-- 进项发票审核 -->
        <div
          class="bg-indigo-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/master/cooperative/invoices/review')"
        >
          <div class="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-2">
            <icon-file class="text-indigo-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">进项发票审核</div>
          <div class="text-xl font-bold">{{ todoData.pendingInvoices }}</div>
        </div>

        <!-- 待申请发票 -->
        <div
          class="bg-red-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
          @click="navigateTo('/master/cooperative/invoices/apply')"
        >
          <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-2">
            <icon-file-add class="text-red-500 text-xl" />
          </div>
          <div class="text-gray-500 text-sm">待申请发票</div>
          <div class="text-xl font-bold">{{ todoData.pendingInvoiceApplications }}</div>
        </div>
      </div>
    </a-card>

    <!-- 团队概况 -->
    <a-card class="mb-6">
      <div class="mb-4">
        <h2 class="text-lg font-bold">团队概况</h2>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 目标销售总额 -->
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="text-sm text-gray-500">目标销售总额</div>
          <div class="text-xl font-bold">¥{{ teamOverview.salesTarget.target.toLocaleString() }}</div>
        </div>
        
        <!-- 目标完成率 -->
        <div class="bg-green-50 p-4 rounded-lg">
          <div class="text-sm text-gray-500">目标完成率</div>
          <div class="text-xl font-bold">{{ ((teamOverview.salesTarget.current / teamOverview.salesTarget.target) * 100).toFixed(0) }}%</div>
          <a-progress
            :percent="(teamOverview.salesTarget.current / teamOverview.salesTarget.target) * 100"
            :color="getProgressColor(teamOverview.salesTarget.current, teamOverview.salesTarget.target)"
            :show-text="false"
            class="mt-2"
          />
        </div>
        
        <!-- 当日订单数 -->
        <div class="bg-amber-50 p-4 rounded-lg">
          <div class="text-sm text-gray-500">当日订单数</div>
          <div class="text-xl font-bold">{{ teamOverview.dailyOrders }}</div>
        </div>
        
        <!-- 当日下单金额 -->
        <div class="bg-purple-50 p-4 rounded-lg">
          <div class="text-sm text-gray-500">当日下单金额</div>
          <div class="text-xl font-bold">¥{{ teamOverview.dailySales.toLocaleString() }}万</div>
        </div>
      </div>
      
      <!-- 团队成员图表区域 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
        <!-- 团队成员销售占比饼图 -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <h3 class="text-md font-bold">团队成员销售占比</h3>
            <div class="flex items-center gap-2">
              <a-select v-model="salesDisplayCount" size="small" style="width: 120px;">
                <a-option value="all">显示全部</a-option>
                <a-option value="top5">前5名</a-option>
                <a-option value="top10">前10名</a-option>
              </a-select>
              <a-radio-group v-model="salesChartType" type="button" size="small">
                <a-radio value="pie">饼图</a-radio>
                <a-radio value="bar">柱状图</a-radio>
              </a-radio-group>
            </div>
          </div>
          <div ref="teamSalesChartRef" style="width: 100%; height: 300px;"></div>
        </div>
        
        <!-- 团队成员目标完成率对比条形图 -->
        <div>
          <div class="flex items-center justify-between mb-2">
            <h3 class="text-md font-bold">团队成员目标完成率</h3>
            <div class="flex items-center gap-2">
              <a-select v-model="targetDisplayCount" size="small" style="width: 120px;">
                <a-option value="all">显示全部</a-option>
                <a-option value="top5">前5名</a-option>
                <a-option value="top10">前10名</a-option>
                <a-option value="bottom5">后5名</a-option>
              </a-select>
              <a-switch v-model="showTargetTable" size="small">
                <template #checked>表格</template>
                <template #unchecked>图表</template>
              </a-switch>
            </div>
          </div>
          <div v-show="!showTargetTable" ref="teamTargetChartRef" style="width: 100%; height: 300px;"></div>
          <div v-show="showTargetTable" class="overflow-auto" style="max-height: 300px;">
            <a-table :columns="teamMembersColumns" :data="getFilteredTeamMembers()" :pagination="false" size="small">
              <template #completionRate="{ record }">
                <div class="flex items-center">
                  <div class="w-full bg-gray-200 rounded-full h-2 mr-2" style="width: 100px;">
                    <div class="h-2 rounded-full" :style="{
                      width: `${record.completionRate}%`,
                      backgroundColor: getCompletionRateColor(record.completionRate)
                    }"></div>
                  </div>
                  <span>{{ record.completionRate }}%</span>
                </div>
              </template>
            </a-table>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 我的概况 -->
    <a-card class="mb-6">
      <div class="mb-4">
        <h2 class="text-lg font-bold">我的概况</h2>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 目标销售总额 -->
        <div class="bg-blue-50 p-4 rounded-lg">
          <div class="text-sm text-gray-500">目标销售总额</div>
          <div class="text-xl font-bold">¥{{ personalOverview.salesTarget.target.toLocaleString() }}</div>
        </div>
        
        <!-- 目标完成率 -->
        <div class="bg-green-50 p-4 rounded-lg">
          <div class="text-sm text-gray-500">目标完成率</div>
          <div class="text-xl font-bold">{{ ((personalOverview.salesTarget.current / personalOverview.salesTarget.target) * 100).toFixed(0) }}%</div>
          <a-progress
            :percent="(personalOverview.salesTarget.current / personalOverview.salesTarget.target) * 100"
            :color="getProgressColor(personalOverview.salesTarget.current, personalOverview.salesTarget.target)"
            :show-text="false"
            class="mt-2"
          />
        </div>
        
        <!-- 当日订单数 -->
        <div class="bg-amber-50 p-4 rounded-lg">
          <div class="text-sm text-gray-500">当日订单数</div>
          <div class="text-xl font-bold">{{ personalOverview.dailyOrders || 0 }}</div>
        </div>
        
        <!-- 当日下单金额 -->
        <div class="bg-purple-50 p-4 rounded-lg">
          <div class="text-sm text-gray-500">当日下单金额</div>
          <div class="text-xl font-bold">¥{{ (personalOverview.dailySales || 0).toLocaleString() }}万</div>
        </div>
      </div>
    </a-card>

    <!-- 数据看板 -->
    <a-card class="mb-6">
      <div class="mb-4">
        <h2 class="text-lg font-bold">数据看板</h2>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- 服务商销售排行榜 -->
        <a-card class="border border-gray-200 shadow-sm">
          <template #title>
            <h3 class="text-base font-medium">服务商销售排行榜</h3>
          </template>
          <div ref="vendorChartRef" style="width: 100%; height: 300px;"></div>
        </a-card>
        
        <!-- 热销品牌排行榜 -->
        <a-card class="border border-gray-200 shadow-sm">
          <template #title>
            <h3 class="text-base font-medium">热销品牌排行榜</h3>
          </template>
          <div ref="brandChartRef" style="width: 100%; height: 300px;"></div>
        </a-card>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <!-- 热销类目排行榜 -->
        <a-card class="border border-gray-200 shadow-sm">
          <template #title>
            <h3 class="text-base font-medium">热销类目排行榜</h3>
          </template>
          <div ref="categoryChartRef" style="width: 100%; height: 300px;"></div>
        </a-card>
        
        <!-- 销售渠道分析 -->
        <a-card class="border border-gray-200 shadow-sm">
          <template #title>
            <h3 class="text-base font-medium">销售渠道分析</h3>
          </template>
          <div ref="channelAnalysisChartRef" style="width: 100%; height: 300px;"></div>
        </a-card>
      </div>
      
      <!-- 月销售金额环比分析 -->
      <a-card class="border border-gray-200 shadow-sm">
        <template #title>
          <h3 class="text-base font-medium">月销售金额环比分析</h3>
        </template>
        <div ref="monthlySalesChartRef" style="width: 100%; height: 400px;"></div>
      </a-card>
    </a-card>
 
  </div>

  <!-- 悬浮快捷操作按钮 -->
  <div class="fixed right-6 bottom-6 z-10">
    <a-button type="primary" shape="circle" size="large" @click="showQuickActions = true">
      <template #icon>
        <icon-plus />
      </template>
    </a-button>
  </div>

  <!-- 快捷操作抽屉 -->
  <a-drawer
    :visible="showQuickActions"
    @cancel="showQuickActions = false"
    width="300"
    unmount-on-close
  >
    <template #title>快捷操作</template>
    <div class="grid grid-cols-2 gap-4">
      <a-button type="outline" @click="navigateTo('/master/cooperative/tickets/create')">
        <template #icon>
          <icon-plus />
        </template>
        创建工单
      </a-button>
      <a-button type="outline" @click="navigateTo('/master/cooperative/products/create')">
        <template #icon>
          <icon-plus />
        </template>
        添加商品
      </a-button>
      <a-button type="outline" @click="navigateTo('/master/cooperative/vendors/create')">
        <template #icon>
          <icon-plus />
        </template>
        添加服务商
      </a-button>
      <a-button type="outline" @click="navigateTo('/master/cooperative/invoices/create')">
        <template #icon>
          <icon-plus />
        </template>
        申请发票
      </a-button>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import { useRouter } from "vue-router";
import * as echarts from 'echarts/core';
import { BarChart, PieChart, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  BarChart,
  PieChart,
  LineChart,
  CanvasRenderer
]);
// 页面元数据
definePageMeta({
  name: "master-cooperative-workbenches",
  path: "/master/cooperative/workbenches"
});
const router = useRouter();
const showQuickActions = ref(false);

// 图表引用

// 数据统计
const statistics = ref([
  {
    title: "本月销售额",
    value: "¥128,560",
    icon: "icon-wallet",
    iconColor: "#165DFF",
    bgColor: "#E8F3FF"
  },
  {
    title: "本月订单数",
    value: "1,234",
    icon: "icon-shopping-cart",
    iconColor: "#00B42A",
    bgColor: "#E8FFEA"
  },
  {
    title: "本月新增服务商",
    value: "16",
    icon: "icon-user-add",
    iconColor: "#F7BA1E",
    bgColor: "#FFF3E8"
  },
  {
    title: "本月退款率",
    value: "0.8%",
    icon: "icon-undo",
    iconColor: "#F53F3F",
    bgColor: "#FFECE8"
  }
]);

// 待办事项数据
const todoData = ref({
  pendingTickets: 12,
  pendingProducts: 25,
  pendingShipments: 8,
  pendingRefunds: 5,
  pendingVendors: 3,
  pendingInvoices: 7,
  pendingInvoiceApplications: 4
});

// 数据看板
const dataBoard = ref({
  vendorRankings: [
    { name: "星辰科技", sales: "56,780" },
    { name: "天际光电", sales: "45,230" },
    { name: "EARMCRF", sales: "38,450" },
    { name: "军表行", sales: "32,120" },
    { name: "正元佳业", sales: "28,760" }
  ],
  brandRankings: [
    { name: "EARMCRF", sales: 256 },
    { name: "星辰科技", sales: 198 },
    { name: "天际光电", sales: 167 },
    { name: "军表行", sales: 145 },
    { name: "正元佳业", sales: 132 }
  ],
  categoryRankings: [
    { name: "电子设备", sales: 423 },
    { name: "能源设备", sales: 356 },
    { name: "光学仪器", sales: 289 },
    { name: "作战装备", sales: 245 },
    { name: "通信设备", sales: 198 }
  ],
  monthlySalesChart: [
    { month: "1月", value: 45600 },
    { month: "2月", value: 52300 },
    { month: "3月", value: 48900 },
    { month: "4月", value: 65200 },
    { month: "5月", value: 72400 },
    { month: "6月", value: 68500 }
  ],
  channelAnalysis: [
    { name: "官方商城", value: 45 },
    { name: "第三方平台", value: 25 },
    { name: "线下门店", value: 20 },
    { name: "其他渠道", value: 10 }
  ]
});

// 团队概况
const teamOverview = ref({
  salesTarget: {
    target: 1000000,
    current: 750000
  },
  dailyOrders: 12,
  dailySales: 5.60,
  teamRankings: [
    { name: "张三", sales: 42560 },
    { name: "李四", sales: 38720 },
    { name: "王五", sales: 32450 },
    { name: "赵六", sales: 28760 },
    { name: "钱七", sales: 24890 }
  ]
});

// 个人概况
const personalOverview = ref({
  salesTarget: {
    target: 50000,
    current: 32450
  }
});

// 获取进度条颜色
const getProgressColor = (current, target) => {
  const percentage = (current / target) * 100;
  if (percentage < 30) return "#F53F3F";
  if (percentage < 60) return "#F7BA1E";
  if (percentage < 90) return "#00B42A";
  return "#165DFF";
};

// 导航到指定页面
const navigateTo = path => {
  router.push(path);
};

// 图表DOM引用
const vendorChartRef = ref(null);
const brandChartRef = ref(null);
const categoryChartRef = ref(null);
const monthlySalesChartRef = ref(null);
const channelAnalysisChartRef = ref(null);
const teamSalesChartRef = ref(null);
const teamTargetChartRef = ref(null);

// 图表实例
let vendorChart = null;
let brandChart = null;
let categoryChart = null;
let monthlySalesChart = null;
let channelAnalysisChart = null;
let teamSalesChart = null;
let teamTargetChart = null;

// 图表控制变量
const salesChartType = ref('pie'); // 销售占比图表类型：饼图或柱状图
const showTargetTable = ref(false); // 是否以表格形式显示目标完成率
const salesDisplayCount = ref('all'); // 销售占比显示数量
const targetDisplayCount = ref('all'); // 目标完成率显示数量

// 团队成员表格列定义
const teamMembersColumns = [
  { title: '成员名称', dataIndex: 'name' },
  { title: '目标金额', dataIndex: 'targetAmount', render: ({ record }) => `¥${formatAmountWithUnit(record.targetAmount)}` },
  { title: '实际销售', dataIndex: 'salesAmount', render: ({ record }) => `¥${formatAmountWithUnit(record.salesAmount)}` },
  { title: '完成率', slotName: 'completionRate' }
];

// 团队成员数据
const teamMembers = ref([
  { name: '张三', salesAmount: 280000, targetAmount: 400000, completionRate: 70 },
  { name: '李四', salesAmount: 350000, targetAmount: 400000, completionRate: 87.5 },
  { name: '王五', salesAmount: 120000, targetAmount: 300000, completionRate: 40 },
  { name: '赵六', salesAmount: 180000, targetAmount: 300000, completionRate: 60 },
  { name: '孙七', salesAmount: 220000, targetAmount: 350000, completionRate: 63 },
  { name: '周八', salesAmount: 320000, targetAmount: 400000, completionRate: 80 },
]);

// 格式化金额，超过万元时显示为"x.xx万"
const formatAmountWithUnit = (amount) => {
  if (amount >= 10000000) {
    return (amount / 10000000).toFixed(2) + '千万';
  } else if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + '万';
  } else {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
};

// 窗口大小变化时重新调整图表大小
const resizeHandler = () => {
  vendorChart?.resize();
  brandChart?.resize();
  categoryChart?.resize();
  monthlySalesChart?.resize();
  channelAnalysisChart?.resize();
  teamSalesChart?.resize();
  teamTargetChart?.resize();
};

// 初始化服务商销售排行榜图表
const initVendorChart = () => {
  if (!vendorChartRef.value) return;
  
  vendorChart = echarts.init(vendorChartRef.value);
  vendorChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '15%',
      bottom: '3%',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: true, lineStyle: { type: 'dashed' } },
      axisLabel: {
        formatter: (value) => value === 0 ? '0' : value + '万'
      }
    },
    yAxis: {
      type: 'category',
      data: dataBoard.value.vendorRankings.map(item => item.name),
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { 
        interval: 0,
        margin: 15,
        color: '#333'
      }
    },
    series: [{
      name: '销售额',
      type: 'bar',
      barWidth: '60%',
      data: dataBoard.value.vendorRankings.map((item, index) => ({
        value: parseFloat(item.sales.replace(/,/g, '')) / 10000, // 转换为万元
        itemStyle: {
          color: '#2E8CF0'
        }
      })),
      label: {
        show: true,
        position: 'right',
        formatter: '{c}万',
        color: '#333',
        fontSize: 12,
        fontWeight: 'normal',
        distance: 5
      }
    }]
  });
};

// 初始化热销品牌排行榜图表
const initBrandChart = () => {
  if (!brandChartRef.value) return;
  
  brandChart = echarts.init(brandChartRef.value);
  brandChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '15%',
      bottom: '3%',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: true, lineStyle: { type: 'dashed' } },
      axisLabel: {
        formatter: (value) => value === 0 ? '0' : value + '万'
      }
    },
    yAxis: {
      type: 'category',
      data: dataBoard.value.brandRankings.map(item => item.name),
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { 
        interval: 0,
        margin: 15,
        color: '#333'
      }
    },
    series: [{
      name: '销售额',
      type: 'bar',
      barWidth: '60%',
      data: dataBoard.value.brandRankings.map((item, index) => ({
        value: parseInt(item.sales),
        itemStyle: {
          color: '#FFA2A2' // 粉色系条形图
        }
      })),
      label: {
        show: true,
        position: 'right',
        formatter: '{c}万',
        color: '#333',
        fontSize: 12,
        fontWeight: 'normal',
        distance: 5
      }
    }]
  });
};

// 初始化月销售金额环比分析图表
const initMonthlySalesChart = () => {
  if (!monthlySalesChartRef.value) return;
  
  monthlySalesChart = echarts.init(monthlySalesChartRef.value);
  monthlySalesChart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dataBoard.value.monthlySalesChart.map(item => item.month)
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} 元'
      }
    },
    series: [{
      data: dataBoard.value.monthlySalesChart.map(item => item.value),
      type: 'line',
      smooth: true,
      lineStyle: {
        color: '#165DFF',
        width: 3
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(22, 93, 255, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(22, 93, 255, 0.05)'
            }
          ]
        }
      },
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: '#165DFF',
        borderWidth: 2,
        borderColor: '#FFF'
      }
    }]
  });
};

// 初始化销售渠道分析图表
const initChannelChart = () => {
  if (!channelAnalysisChartRef.value) return;
  
  channelAnalysisChart = echarts.init(channelAnalysisChartRef.value);
  channelAnalysisChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: dataBoard.value.channelAnalysis.map(item => item.name)
    },
    series: [{
      name: '销售渠道',
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: dataBoard.value.channelAnalysis.map(item => ({
        value: item.value,
        name: item.name
      })),
      color: ['#165DFF', '#00B42A', '#F7BA1E', '#F53F3F', '#7D5AC8']
    }]
  });
};



// 初始化热销类目排行榜图表
const initCategoryChart = () => {
  if (!categoryChartRef.value) return;
  
  categoryChart = echarts.init(categoryChartRef.value);
  categoryChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' }
    },
    grid: {
      left: '3%',
      right: '15%',
      bottom: '3%',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: true, lineStyle: { type: 'dashed' } },
      axisLabel: {
        formatter: (value) => value === 0 ? '0' : value + '万'
      }
    },
    yAxis: {
      type: 'category',
      data: dataBoard.value.categoryRankings.map(item => item.name),
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: { 
        interval: 0,
        margin: 15,
        color: '#333'
      }
    },
    series: [{
      name: '销售量',
      type: 'bar',
      barWidth: '60%',
      data: dataBoard.value.categoryRankings.map((item, index) => ({
        value: parseInt(item.sales),
        itemStyle: {
          color: '#7BD88B' // 绿色系条形图
        }
      })),
      label: {
        show: true,
        position: 'right',
        formatter: '{c}万',
        color: '#333',
        fontSize: 12,
        fontWeight: 'normal',
        distance: 5
      }
    }]
  });
};

// 初始化所有图表
const initAllCharts = () => {
  // 销毁旧的图表实例
  vendorChart?.dispose();
  brandChart?.dispose();
  categoryChart?.dispose();
  monthlySalesChart?.dispose();
  channelAnalysisChart?.dispose();
  teamSalesChart?.dispose();
  teamTargetChart?.dispose();
  
  // 初始化新的图表实例
  initVendorChart();
  initBrandChart();
  initCategoryChart();
  initMonthlySalesChart();
  initChannelChart();
  initTeamSalesChart();
  initTeamTargetChart();
};

// 获取筛选后的团队成员数据
const getFilteredTeamMembers = () => {
  let members = [...teamMembers.value];
  
  // 按完成率排序
  members.sort((a, b) => b.completionRate - a.completionRate);
  
  // 根据选择进行筛选
  if (targetDisplayCount.value === 'top5') {
    return members.slice(0, 5);
  } else if (targetDisplayCount.value === 'top10') {
    return members.slice(0, 10);
  } else if (targetDisplayCount.value === 'bottom5') {
    return members.slice(-5);
  }
  
  return members;
};

// 获取销售图表数据
const getSalesChartData = () => {
  let members = [...teamMembers.value];
  
  // 按销售金额排序
  members.sort((a, b) => b.salesAmount - a.salesAmount);
  
  // 根据选择进行筛选
  if (salesDisplayCount.value === 'top5') {
    members = members.slice(0, 5);
  } else if (salesDisplayCount.value === 'top10') {
    members = members.slice(0, 10);
  }
  
  return members;
};

// 监听图表类型变化
watch(salesChartType, (newValue) => {
  updateTeamSalesChart();
});

// 监听销售占比显示数量变化
watch(salesDisplayCount, (newValue) => {
  updateTeamSalesChart();
});

// 监听目标完成率显示数量变化
watch(targetDisplayCount, (newValue) => {
  if (!showTargetTable.value) {
    updateTeamTargetChart();
  }
});

// 监听表格显示模式变化
watch(showTargetTable, (newValue) => {
  if (!newValue) {
    // 如果切换回图表模式，需要重新初始化图表
    nextTick(() => {
      updateTeamTargetChart();
    });
  }
});

// 组件挂载后初始化图表
onMounted(() => {
  initVendorChart();
  initBrandChart();
  initCategoryChart();
  initMonthlySalesChart();
  initChannelChart();
  initTeamSalesChart();
  initTeamTargetChart();
  
  window.addEventListener('resize', resizeHandler);
});

// 获取完成率颜色
const getCompletionRateColor = (rate) => {
  if (rate >= 85) return '#4cd137'; // 绿色
  if (rate >= 65) return '#2e86de'; // 蓝色
  if (rate >= 40) return '#ffa502'; // 黄色
  return '#ff4757'; // 红色
};

// 初始化团队成员销售占比图表
const initTeamSalesChart = () => {
  if (!teamSalesChartRef.value) return;
  
  teamSalesChart = echarts.init(teamSalesChartRef.value);
  updateTeamSalesChart();
};

// 更新团队成员销售占比图表
const updateTeamSalesChart = () => {
  if (!teamSalesChartRef.value || !teamSalesChart) return;
  
  // 清除之前的图表配置
  teamSalesChart.clear();
  
  // 获取筛选后的数据
  const chartData = getSalesChartData();
  
  // 饼图配置
  if (salesChartType.value === 'pie') {
    teamSalesChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}元 ({d}%)'
      },
      legend: {
        type: chartData.length > 15 ? 'scroll' : 'plain',
        orient: 'vertical',
        right: '5%',
        top: 'middle',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          fontSize: 12,
          overflow: 'truncate',
          width: 60
        },
        formatter: function(name) {
          // 如果名称过长，截取前4个字符并添加省略号
          if (name.length > 4) {
            return name.substring(0, 4) + '...';
          }
          return name;
        },
        data: chartData.map(item => item.name)
      },
      series: [
        {
          name: '销售金额',
          type: 'pie',
          radius: ['35%', '65%'],
          center: ['40%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 1
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: chartData.map(item => ({
            value: item.salesAmount,
            name: item.name
          }))
        }
      ]
    });
  } 
  // 柱状图配置
  else if (salesChartType.value === 'bar') {
    teamSalesChart.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          return `${params[0].name}<br/>销售金额: ¥${formatAmountWithUnit(params[0].value)}`;
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLine: { show: false },
        axisTick: { show: false },
        splitLine: { show: true, lineStyle: { type: 'dashed' } }
      },
      yAxis: {
        type: 'category',
        data: chartData.map(item => item.name),
        axisLine: { show: false },
        axisTick: { show: false },
        axisLabel: { 
          interval: 0,
          margin: 15,
          color: '#333'
        }
      },
      series: [{
        name: '销售金额',
        type: 'bar',
        barWidth: '60%',
        data: chartData.map(item => item.salesAmount),
        itemStyle: {
          color: '#1890ff'
        },
        label: {
          show: true,
          position: 'right',
          formatter: function(params) {
            return formatAmountWithUnit(params.value);
          }
        }
      }]
    });
  }
};

// 初始化团队成员目标完成率图表
const initTeamTargetChart = () => {
  if (!teamTargetChartRef.value) return;
  
  teamTargetChart = echarts.init(teamTargetChartRef.value);
  updateTeamTargetChart();
};

// 更新团队成员目标完成率图表
const updateTeamTargetChart = () => {
  // 如果当前是表格模式或者DOM引用不存在，则直接返回
  if (!teamTargetChartRef.value || showTargetTable.value || !teamTargetChart) return;
  
  // 获取筛选后的数据
  const chartData = getFilteredTeamMembers();
  
  // 清除之前的图表配置
  teamTargetChart.clear();
  
  // 重新设置图表配置
  teamTargetChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const member = chartData.find(m => m.name === params[0].name);
        return `${params[0].name}<br/>目标金额: ¥${formatAmountWithUnit(member.targetAmount)}<br/>已完成: ¥${formatAmountWithUnit(member.salesAmount)}<br/>完成率: ${member.completionRate}%`;
      }
    },
    grid: {
      left: '15%',
      right: '5%',
      bottom: '5%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    yAxis: {
      type: 'category',
      data: chartData.map(item => item.name),
      axisLabel: {
        interval: 0,
        margin: 8
      }
    },
    series: [
      {
        name: '完成率',
        type: 'bar',
        barWidth: '70%',
        data: chartData.map(item => ({
          value: item.completionRate,
          itemStyle: {
            color: getCompletionRateColor(item.completionRate)
          }
        })),
        label: {
          show: true,
          position: 'right',
          formatter: '{c}%'
        }
      }
    ]
  });
};

// 创建MutationObserver
let observer = null;

// 组件挂载时初始化图表
onMounted(() => {
  // 延迟初始化图表，确保DOM已经渲染
  setTimeout(() => {
    try {
      initAllCharts();
      window.addEventListener('resize', resizeHandler);
      
      // 使用更精确的选择器定位容器
      const container = monthlySalesChartRef.value?.parentElement;
      if (container) {
        // 仅监听属性变化，不监听子元素
        observer = new MutationObserver(() => {
          // 防止频繁触发，使用节流
          if (vendorChart && brandChart && monthlySalesChart && channelAnalysisChart) {
            requestAnimationFrame(resizeHandler);
          }
        });
        observer.observe(container, { attributes: true });
      }
    } catch (error) {
      console.error('初始化图表错误:', error);
    }
  }, 300);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', resizeHandler);
  if (observer) {
    observer.disconnect();
    observer = null;
  }
  vendorChart?.dispose();
  brandChart?.dispose();
  categoryChart?.dispose();
  monthlySalesChart?.dispose();
  channelAnalysisChart?.dispose();
  
  vendorChart = null;
  brandChart = null;
  categoryChart = null;
  monthlySalesChart = null;
  channelAnalysisChart = null;
});
</script>

<style scoped>
.arco-card {
  height: 100%;
}

.ranking-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f2f3f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #86909c;
}

.ranking-badge.top-rank {
  background-color: #165dff;
  color: white;
}

.chart-container {
  width: 100%;
  height: 100%;
  min-height: 200px;
}
</style>