<template>
  <div class="ma-content-block p-4">
    <!-- 顶部标签页 -->
    <a-tabs v-model="activeTab" @change="handleTabChange">
      <a-tab-pane key="member" title="成员目标"></a-tab-pane>
      <a-tab-pane key="department" title="部门目标"></a-tab-pane>
      <!-- <a-tab-pane key="company" title="公司目标"></a-tab-pane> -->
    </a-tabs>

    <!-- 查询条件 -->
    <div class="mb-4 mt-4">
      <a-form :model="searchForm" layout="inline">
        <a-form-item field="fiscalYear" label="目标财年">
          <a-select v-model="searchForm.fiscalYear" placeholder="请选择财年" style="width: 120px" allow-clear>
            <a-option v-for="year in fiscalYears" :key="year" :value="year">{{ year }}年</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="targetType" label="目标类型">
          <a-select v-model="searchForm.targetType" placeholder="请选择类型" style="width: 120px" allow-clear>
            <a-option value="成交金额">成交金额</a-option>
            <a-option value="订单数量">订单数量</a-option>
          </a-select>
        </a-form-item>
      
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              查询
            </a-button>
            <a-button @click="resetSearch">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
            <a-button type="primary" @click="batchEditTargets">
              <template #icon>
                <icon-edit />
              </template>
              批量编辑
            </a-button>
            <a-button type="primary" @click="addNewTarget">
              <template #icon>
                <icon-plus />
              </template>
              添加目标
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </div>

    <!-- 目标粒度选择和目标总计显示 -->
    <div class="flex justify-between items-center mb-4">
      <div class="flex items-center">
        <span class="mr-2">目标粒度：</span>
        <a-select v-model="targetGranularity" style="width: 120px">
          <a-option value="year">年度</a-option>
          <a-option value="quarter">季度</a-option>
          <a-option value="month">月度</a-option>
        </a-select>
      </div>
      <div class="flex items-center">
        <span class="mr-2">{{ getTargetTypeLabel() }}目标合计：</span>
        <span class="text-blue-600 font-bold">{{ formatAmount(totalYearlyTarget) }}</span>
        <a-button type="text" class="ml-2" @click="refreshTargets">
          <template #icon>
            <icon-refresh />
          </template>
          刷新数据
        </a-button>
      </div>
    </div>

    <!-- 目标列表表格 -->
    <a-table
      :key="activeTab"
      :columns="getColumns()"
      :data="filteredTargetData"
      :pagination="{ 
        total: filteredTargetData.length,
        showTotal: true,
        showJumper: true,
        showPageSize: true,
        pageSize: pageSize,
        current: currentPage
      }"
      @page-change="onPageChange"
      @page-size-change="onPageSizeChange"
      :loading="loading"
      :scroll="{ x: '100%' }"
      stripe
      row-key="id"
    >
      <!-- 操作列 -->
      <template #operations="{ record }">
        <a-space>
          <a-button type="text" size="small" @click="editTarget(record)">
            <template #icon>
              <icon-edit />
            </template>
            编辑
          </a-button>
          <a-popconfirm content="确定要删除该目标吗？" @ok="deleteTarget(record)">
            <a-button type="text" size="small" status="danger">
              <template #icon>
                <icon-delete />
              </template>
              删除
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>
    </a-table>

    <!-- 新建/编辑目标弹窗 -->
    <a-modal
      :visible="targetModalVisible"
      :title="isEdit ? '编辑业绩目标' : '设置业绩目标'"
      @cancel="closeTargetModal"
      @ok="submitTargetForm"
      :ok-loading="submitLoading"
      ok-text="确定"
      cancel-text="取消"
      :mask-closable="false"
      :unmount-on-close="true"
      width="750px"
    >
      <div class="target-form-wrapper">
        <a-form :model="targetForm" ref="targetFormRef" :rules="targetRules" layout="vertical">
          <!-- 选择成员 -->
          <div class="target-setting-row">
            <div class="target-setting-item" v-if="activeTab === 'member'">
              <a-form-item field="name" class="custom-form-item">
                <template #label>
                  <span>选择成员：</span>
                </template>
                <a-select v-model="targetForm.selectedMembers" placeholder="请选择成员" style="width: 100%" multiple allow-clear>
                  <a-option v-for="user in userOptions" :key="user.id" :value="user.id">{{ user.name }}</a-option>
                </a-select>

              </a-form-item>
            </div>
            <div class="target-setting-item" v-if="activeTab === 'department'">
              <a-form-item field="name" class="custom-form-item">
                <template #label>
                  <span>选择部门：</span>
                </template>
                <a-select v-model="targetForm.name" placeholder="请选择部门" style="width: 100%">
                  <a-option v-for="dept in departmentOptions" :key="dept.id" :value="dept.name">{{ dept.name }}</a-option>
                </a-select>
              </a-form-item>
            </div>
            <div class="target-setting-item" v-if="activeTab === 'company'">
              <a-form-item field="name" class="custom-form-item">
                <template #label>
                  <span>公司名称：</span>
                </template>
                <a-input v-model="targetForm.name" placeholder="请输入公司名称" style="width: 100%" />
              </a-form-item>
            </div>
          </div>

          <div class="target-setting-row">
            <div class="target-setting-item">
              <a-form-item field="fiscalYear" class="custom-form-item">
                <template #label>
                  <span > 目标财年：</span>
                </template>
                <a-select v-model="targetForm.fiscalYear" placeholder="请选择财年" style="width: 100%">
                  <a-option v-for="year in fiscalYears" :key="year" :value="year">{{ year }}年</a-option>
                </a-select>
              </a-form-item>
            </div>
            <div class="target-setting-item">
              <a-form-item field="targetType" class="custom-form-item">
                <template #label>
                  <span>目标类型：</span>
                </template>
                <a-select v-model="targetForm.targetType" placeholder="请选择类型" style="width: 100%">
                  <a-option value="成交金额">成交金额</a-option>
                  <a-option value="订单数量">订单数量</a-option>
                </a-select>
              </a-form-item>
            </div>
          </div>
          
          <div class="target-setting-row">
            <div class="target-setting-item">
              <a-form-item field="yearlyTarget" class="custom-form-item">
                <template #label>
                  <span>年度目标：</span>
                </template>
                <a-input-number
                  v-model="targetForm.yearlyTarget"
                  placeholder="请输入年度目标数值"
                  :min="0"
                  :step="100"
                  hide-button
                  style="width: 100%"
                />
              </a-form-item>
            </div>
            <div class="target-setting-item">
              <a-form-item field="unit" class="custom-form-item">
                <template #label>
                  <span > 目标单位：</span>
                </template>
                <a-select v-model="targetForm.unit" placeholder="请选择单位" style="width: 100%">
                  <a-option value="万">万</a-option>
                  <a-option value="个">个</a-option>
                </a-select>
              </a-form-item>
            </div>
          </div>

          <div class="target-distribution">
            <div class="target-distribution-title">目标分解</div>

            <div class="target-quarters-wrapper">
              <!-- 第一季度模块 -->
              <div class="quarter-block">
                <div class="quarter-row">
                  <div class="quarter-label">第一季度</div>
                  <a-input-number
                    v-model="targetForm.q1Target"
                    placeholder="0"
                    :min="0"
                    :step="100"
                    hide-button
                    class="quarter-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-01</div>
                  <a-input-number
                    v-model="targetForm.m1Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-02</div>
                  <a-input-number
                    v-model="targetForm.m2Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-03</div>
                  <a-input-number
                    v-model="targetForm.m3Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
              </div>
              
              <!-- 第二季度模块 -->
              <div class="quarter-block">
                <div class="quarter-row">
                  <div class="quarter-label">第二季度</div>
                  <a-input-number
                    v-model="targetForm.q2Target"
                    placeholder="300"
                    :min="0"
                    :step="100"
                    hide-button
                    class="quarter-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-04</div>
                  <a-input-number
                    v-model="targetForm.m4Target"
                    placeholder="100"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-05</div>
                  <a-input-number
                    v-model="targetForm.m5Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-06</div>
                  <a-input-number
                    v-model="targetForm.m6Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
              </div>
              
              <!-- 第三季度模块 -->
              <div class="quarter-block">
                <div class="quarter-row">
                  <div class="quarter-label">第三季度</div>
                  <a-input-number
                    v-model="targetForm.q3Target"
                    placeholder="300"
                    :min="0"
                    :step="100"
                    hide-button
                    class="quarter-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-07</div>
                  <a-input-number
                    v-model="targetForm.m7Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-08</div>
                  <a-input-number
                    v-model="targetForm.m8Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-09</div>
                  <a-input-number
                    v-model="targetForm.m9Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
              </div>
              
              <!-- 第四季度模块 -->
              <div class="quarter-block">
                <div class="quarter-row">
                  <div class="quarter-label">第四季度</div>
                  <a-input-number
                    v-model="targetForm.q4Target"
                    placeholder="300"
                    :min="0"
                    :step="100"
                    hide-button
                    class="quarter-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-10</div>
                  <a-input-number
                    v-model="targetForm.m10Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-11</div>
                  <a-input-number
                    v-model="targetForm.m11Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
                
                <div class="month-row">
                  <div class="month-label">{{ targetForm.fiscalYear }}-12</div>
                  <a-input-number
                    v-model="targetForm.m12Target"
                    placeholder="0"
                    :min="0"
                    :step="10"
                    hide-button
                    class="month-input"
                  />
                </div>
              </div>
            </div>
          </div>
        </a-form>
      </div>
    </a-modal>

    <!-- 批量编辑目标弹窗 -->
    <a-modal
      :visible="batchEditModalVisible"
      title="批量编辑目标"
      @cancel="closeBatchEditModal"
      @ok="submitBatchEdit"
      :ok-loading="submitLoading"
      ok-text="确定"
      cancel-text="取消"
      :mask-closable="false"
      width="80%"
    >
      <a-form :model="batchEditForm" layout="vertical">
        <a-form-item field="targets" label="选择要编辑的目标">
          <a-table
            :columns="getBatchEditColumns()"
            :data="filteredTargetData"
            :row-selection="{
              type: 'checkbox',
              showCheckedAll: true,
              selectedRowKeys: batchEditForm.selectedKeys
            }"
            @selection-change="handleBatchSelect"
            :pagination="false"
            :bordered="true"
            size="small"
            :scroll="{ y: '300px' }"
            row-key="id"
          />
        </a-form-item>

        <a-divider>批量设置</a-divider>

        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item field="yearlyTarget" label="年度目标">
              <a-input-number
                v-model="batchEditForm.yearlyTarget"
                placeholder="请输入年度目标数值"
                :min="0"
                :step="100"
                hide-button
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="targetType" label="目标类型">
              <a-select v-model="batchEditForm.targetType" placeholder="请选择类型" allow-clear>
                <a-option value="成交金额">成交金额</a-option>
                <a-option value="订单数量">订单数量</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item field="unit" label="目标单位">
              <a-select v-model="batchEditForm.unit" placeholder="请选择单位" allow-clear>
                <a-option value="万">万</a-option>
                <a-option value="个">个</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';
import { request } from '@/utils/request';

// 标签页切换
const activeTab = ref('member');

// 目标粒度选择
const targetGranularity = ref('year');

// 分页设置
const currentPage = ref(1);
const pageSize = ref(10);
const loading = ref(false);

// 财年选项
const currentYear = new Date().getFullYear();
const fiscalYears = [currentYear - 1, currentYear, currentYear + 1, currentYear + 2];

// 搜索表单
const searchForm = reactive({
  fiscalYear: currentYear,
  targetType: '成交金额',
  yearlyTarget: 0
});

// 目标表单
const targetFormRef = ref(null);
const targetForm = reactive({
  id: '',
  name: '',
  department: '',
  selectedMembers: [],
  fiscalYear: currentYear,
  targetType: '成交金额',
  unit: '万',
  yearlyTarget: 0,
  q1Target: 0,
  q2Target: 0,
  q3Target: 0,
  q4Target: 0,
  m1Target: 0,
  m2Target: 0,
  m3Target: 0,
  m4Target: 0,
  m5Target: 0,
  m6Target: 0,
  m7Target: 0,
  m8Target: 0,
  m9Target: 0,
  m10Target: 0,
  m11Target: 0,
  m12Target: 0
});

// 批量编辑表单
const batchEditForm = reactive({
  selectedKeys: [],
  yearlyTarget: null,
  targetType: null,
  unit: null
});

// 弹窗控制
const targetModalVisible = ref(false);
const batchEditModalVisible = ref(false);
const isEdit = ref(false);
const submitLoading = ref(false);

// 表单引用已在上面定义

// 表单验证规则
const targetRules = {
  name: [{ required: true, message: '请选择名称' }],
  fiscalYear: [{ required: true, message: '请选择财年' }],
  targetType: [{ required: true, message: '请选择目标类型' }],
  unit: [{ required: true, message: '请选择单位' }],
  yearlyTarget: [{ required: true, message: '请输入年度目标' }]
};

// API数据
const goalTargets = ref([]);
const userOptions = ref([]);
const departmentOptions = ref([]);

// 根据当前标签页获取目标数据
const targetData = computed(() => {
  return goalTargets.value.filter(item => item.target_type === activeTab.value);
});

// 根据搜索条件过滤目标数据
const filteredTargetData = computed(() => {
  return targetData.value.filter(item => {
    const fiscalYearMatch = !searchForm.fiscalYear || item.fiscalYear === searchForm.fiscalYear;
    const targetTypeMatch = !searchForm.targetType || item.targetType === searchForm.targetType;
    const yearlyTargetMatch = !searchForm.yearlyTarget || item.yearlyTarget >= searchForm.yearlyTarget;

    return fiscalYearMatch && targetTypeMatch && yearlyTargetMatch;
  });
});

// 计算目标总和
const totalYearlyTarget = computed(() => {
  return filteredTargetData.value.reduce((sum, item) => sum + item.yearlyTarget, 0);
});

// 获取目标类型标签
const getTargetTypeLabel = () => {
  return searchForm.targetType || '全部';
};

// 格式化金额
const formatAmount = (amount) => {
  return `${amount.toLocaleString()} ${searchForm.targetType === '成交金额' ? '万' : '个'}`;
};

// API调用方法
// 获取目标设置列表
const fetchGoalList = async () => {
  try {
    loading.value = true;
    const params = {
      target_type: activeTab.value,
      page: currentPage.value,
      pageSize: pageSize.value
    };

    if (searchForm.fiscalYear) {
      params.fiscal_year = searchForm.fiscalYear;
    }

    if (searchForm.targetType) {
      params.goal_type = searchForm.targetType;
    }

    const response = await request({
      url: '/api/v1/provider/goal',
      method: 'GET',
      params
    });

    if (response.code === 200) {
      // 将后端数据字段映射为前端期望的字段
      goalTargets.value = (response.data.items || []).map(item => ({
        id: item.id,
        name: item.name,
        target_type: item.target_type,
        target_id: item.target_id,
        fiscalYear: item.fiscalYear,
        targetType: item.targetType,
        unit: item.unit,
        yearlyTarget: item.yearlyTarget,
        q1Target: item.q1Target,
        q2Target: item.q2Target,
        q3Target: item.q3Target,
        q4Target: item.q4Target,
        m1Target: item.m1Target,
        m2Target: item.m2Target,
        m3Target: item.m3Target,
        m4Target: item.m4Target,
        m5Target: item.m5Target,
        m6Target: item.m6Target,
        m7Target: item.m7Target,
        m8Target: item.m8Target,
        m9Target: item.m9Target,
        m10Target: item.m10Target,
        m11Target: item.m11Target,
        m12Target: item.m12Target,
        createTime: item.createTime,
        status: item.status
      }));
    } else {
      Message.error(response.message || '获取目标设置列表失败');
    }
  } catch (error) {
    console.error('获取目标设置列表失败:', error);
    Message.error('获取目标设置列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取用户列表
const fetchUserList = async () => {
  try {
    const response = await request({
      url: '/api/v1/provider/goal/users/list',
      method: 'GET'
    });

    if (response.code === 200) {
      userOptions.value = response.data || [];
    } else {
      Message.error(response.message || '获取用户列表失败');
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    Message.error('获取用户列表失败');
  }
};

// 获取部门列表
const fetchDepartmentList = async () => {
  try {
    const response = await request({
      url: '/api/v1/provider/goal/departments/list',
      method: 'GET'
    });

    if (response.code === 200) {
      departmentOptions.value = response.data || [];
    } else {
      Message.error(response.message || '获取部门列表失败');
    }
  } catch (error) {
    console.error('获取部门列表失败:', error);
    Message.error('获取部门列表失败');
  }
};

// 创建目标设置
const createGoalSetting = async (goalData) => {
  try {
    const response = await request({
      url: '/api/v1/provider/goal',
      method: 'POST',
      data: goalData
    });

    if (response.code === 200 || response.code === 201) {
      Message.success('创建目标设置成功');
      return response.data;
    } else {
      Message.error(response.message || '创建目标设置失败');
      return null;
    }
  } catch (error) {
    console.error('创建目标设置失败:', error);
    Message.error('创建目标设置失败');
    return null;
  }
};

// 更新目标设置
const updateGoalSetting = async (id, goalData) => {
  try {
    const response = await request({
      url: `/api/v1/provider/goal/${id}`,
      method: 'PUT',
      data: goalData
    });

    if (response.code === 200) {
      Message.success('更新目标设置成功');
      return response.data;
    } else {
      Message.error(response.message || '更新目标设置失败');
      return null;
    }
  } catch (error) {
    console.error('更新目标设置失败:', error);
    Message.error('更新目标设置失败');
    return null;
  }
};

// 删除目标设置
const deleteGoalSetting = async (id) => {
  try {
    const response = await request({
      url: `/api/v1/provider/goal/${id}`,
      method: 'DELETE'
    });

    if (response.code === 200) {
      Message.success('删除目标设置成功');
      return true;
    } else {
      Message.error(response.message || '删除目标设置失败');
      return false;
    }
  } catch (error) {
    console.error('删除目标设置失败:', error);
    Message.error('删除目标设置失败');
    return false;
  }
};

// 批量更新目标设置
const batchUpdateGoalSettings = async (goalIds, updateData) => {
  try {
    const response = await request({
      url: '/api/v1/provider/goal/batch/update',
      method: 'PUT',
      data: {
        goal_ids: goalIds,
        update_data: updateData
      }
    });

    if (response.code === 200) {
      Message.success('批量更新目标设置成功');
      return true;
    } else {
      Message.error(response.message || '批量更新目标设置失败');
      return false;
    }
  } catch (error) {
    console.error('批量更新目标设置失败:', error);
    Message.error('批量更新目标设置失败');
    return false;
  }
};

// 获取表格列配置
const getColumns = () => {
  // 基础列
  const baseColumns = [
    {
      title: '序号',
      slotName: 'index',
      render: ({ rowIndex }) => rowIndex + 1 + (currentPage.value - 1) * pageSize.value,
      width: 80,
    }
  ];
  
  // 根据标签页类型添加不同的列
  if (activeTab.value === 'member') {
    // 成员目标表格
    baseColumns.push(
      {
        title: '姓名',
        dataIndex: 'name',
        width: 120,
      },
      {
        title: '所属部门',
        dataIndex: 'department',
        width: 120,
      }
    );
  } else {
    // 部门目标表格或公司目标表格
    baseColumns.push({
      title: activeTab.value === 'department' ? '部门名称' : '公司名称',
      dataIndex: 'name',
      width: 120,
    });
  }
  
  // 添加通用列
  baseColumns.push(
    {
      title: '财年',
      dataIndex: 'fiscalYear',
      width: 100,
      render: ({ record }) => `${record.fiscalYear}年`
    },
    {
      title: '目标类型',
      dataIndex: 'targetType',
      width: 100,
    },
    {
      title: '年度目标',
      dataIndex: 'yearlyTarget',
      width: 120,
      render: ({ record }) => `${record.yearlyTarget} ${record.unit}`
    }
  );
  
  // 根据目标粒度添加对应的列
  if (targetGranularity.value === 'year' || targetGranularity.value === 'quarter') {
    // 年度和季度粒度都显示季度列
    baseColumns.push(
      {
        title: '第一季度',
        dataIndex: 'q1Target',
        width: 100,
        render: ({ record }) => `${record.q1Target} ${record.unit}`
      },
      {
        title: '第二季度',
        dataIndex: 'q2Target',
        width: 100,
        render: ({ record }) => `${record.q2Target} ${record.unit}`
      },
      {
        title: '第三季度',
        dataIndex: 'q3Target',
        width: 100,
        render: ({ record }) => `${record.q3Target} ${record.unit}`
      },
      {
        title: '第四季度',
        dataIndex: 'q4Target',
        width: 100,
        render: ({ record }) => `${record.q4Target} ${record.unit}`
      }
    );
  }
  
  if (targetGranularity.value === 'year' || targetGranularity.value === 'month') {
    // 年度和月度粒度显示月度列
    if (targetGranularity.value === 'month') {
      // 只有月度粒度时显示所有月度列
      baseColumns.push(
        {
          title: '1月',
          dataIndex: 'm1Target',
          width: 80,
          render: ({ record }) => `${record.m1Target} ${record.unit}`
        },
        {
          title: '2月',
          dataIndex: 'm2Target',
          width: 80,
          render: ({ record }) => `${record.m2Target} ${record.unit}`
        },
        {
          title: '3月',
          dataIndex: 'm3Target',
          width: 80,
          render: ({ record }) => `${record.m3Target} ${record.unit}`
        },
        {
          title: '4月',
          dataIndex: 'm4Target',
          width: 80,
          render: ({ record }) => `${record.m4Target} ${record.unit}`
        },
        {
          title: '5月',
          dataIndex: 'm5Target',
          width: 80,
          render: ({ record }) => `${record.m5Target} ${record.unit}`
        },
        {
          title: '6月',
          dataIndex: 'm6Target',
          width: 80,
          render: ({ record }) => `${record.m6Target} ${record.unit}`
        },
        {
          title: '7月',
          dataIndex: 'm7Target',
          width: 80,
          render: ({ record }) => `${record.m7Target} ${record.unit}`
        },
        {
          title: '8月',
          dataIndex: 'm8Target',
          width: 80,
          render: ({ record }) => `${record.m8Target} ${record.unit}`
        },
        {
          title: '9月',
          dataIndex: 'm9Target',
          width: 80,
          render: ({ record }) => `${record.m9Target} ${record.unit}`
        },
        {
          title: '10月',
          dataIndex: 'm10Target',
          width: 80,
          render: ({ record }) => `${record.m10Target} ${record.unit}`
        },
        {
          title: '11月',
          dataIndex: 'm11Target',
          width: 80,
          render: ({ record }) => `${record.m11Target} ${record.unit}`
        },
        {
          title: '12月',
          dataIndex: 'm12Target',
          width: 80,
          render: ({ record }) => `${record.m12Target} ${record.unit}`
        }
      );
    }
  }
  
  // 添加操作列
  baseColumns.push({
    title: '操作',
    slotName: 'operations',
    fixed: 'right',
    align:"center",
    width: 150,
  });

  return baseColumns;
};

// 获取批量编辑表格列配置
const getBatchEditColumns = () => {
  return [
    {
      title: activeTab.value === 'member' ? '姓名' : (activeTab.value === 'department' ? '部门名称' : '公司名称'),
      dataIndex: 'name',
      width: 120,
    },
    ...(activeTab.value === 'member' ? [{
      title: '所属部门',
      dataIndex: 'department',
      width: 120,
    }] : []),
    {
      title: '财年',
      dataIndex: 'fiscalYear',
      width: 100,
      render: ({ record }) => `${record.fiscalYear}年`
    },
    {
      title: '目标类型',
      dataIndex: 'targetType',
      width: 100,
    },
    {
      title: '年度目标',
      dataIndex: 'yearlyTarget',
      width: 120,
      render: ({ record }) => `${record.yearlyTarget} ${record.unit}`
    }
  ];
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  fetchGoalList();
};

// 重置搜索条件
const resetSearch = () => {
  searchForm.fiscalYear = null;
  searchForm.targetType = null;
  searchForm.yearlyTarget = 0;
  handleSearch();
};

// 刷新目标数据
const refreshTargets = () => {
  fetchGoalList();
};

// 添加新目标
const addNewTarget = () => {
  // 先重置表单，再设置编辑状态
  resetTargetForm();
  isEdit.value = false;

  // 根据当前标签页设置默认值
  if (activeTab.value === 'member') {
    targetForm.selectedMembers = [];
  } else if (activeTab.value === 'department') {
    targetForm.name = departmentOptions.value[0]?.name || '';
  } else {
    targetForm.name = '总公司';
  }

  // 直接设置弹窗可见性
  targetModalVisible.value = true;
};

// 编辑目标
const editTarget = (record) => {
  isEdit.value = true;

  // 填充表单数据
  targetForm.id = record.id;
  targetForm.name = record.name;
  targetForm.fiscalYear = record.fiscalYear;
  targetForm.targetType = record.targetType;
  targetForm.unit = record.unit;
  targetForm.yearlyTarget = record.yearlyTarget;
  targetForm.q1Target = record.q1Target;
  targetForm.q2Target = record.q2Target;
  targetForm.q3Target = record.q3Target;
  targetForm.q4Target = record.q4Target;
  targetForm.m1Target = record.m1Target;
  targetForm.m2Target = record.m2Target;
  targetForm.m3Target = record.m3Target;
  targetForm.m4Target = record.m4Target;
  targetForm.m5Target = record.m5Target;
  targetForm.m6Target = record.m6Target;
  targetForm.m7Target = record.m7Target;
  targetForm.m8Target = record.m8Target;
  targetForm.m9Target = record.m9Target;
  targetForm.m10Target = record.m10Target;
  targetForm.m11Target = record.m11Target;
  targetForm.m12Target = record.m12Target;

  // 如果是成员目标，设置选中的成员
  if (activeTab.value === 'member') {
    targetForm.selectedMembers = [record.target_id];
  }

  targetModalVisible.value = true;
};

// 删除目标
const deleteTarget = async (record) => {
  const success = await deleteGoalSetting(record.id);
  if (success) {
    fetchGoalList();
  }
};

// 批量编辑目标
const batchEditTargets = () => {
  batchEditForm.selectedKeys = [];
  batchEditForm.yearlyTarget = null;
  batchEditForm.targetType = null;
  batchEditForm.unit = null;
  batchEditModalVisible.value = true;
};

// 提交批量编辑
const submitBatchEdit = async () => {
  if (batchEditForm.selectedKeys.length === 0) {
    Message.warning('请至少选择一条记录');
    return;
  }

  submitLoading.value = true;

  const updateData = {};
  if (batchEditForm.yearlyTarget !== null) updateData.yearly_target = batchEditForm.yearlyTarget;
  if (batchEditForm.targetType !== null) updateData.goal_type = batchEditForm.targetType;
  if (batchEditForm.unit !== null) updateData.unit = batchEditForm.unit;

  const success = await batchUpdateGoalSettings(batchEditForm.selectedKeys, updateData);

  submitLoading.value = false;

  if (success) {
    batchEditModalVisible.value = false;
    fetchGoalList();
  }
};

// 重置目标表单
const resetTargetForm = () => {
  Object.assign(targetForm, {
    id: '',
    name: '',
    department: '',
    selectedMembers: [],
    fiscalYear: currentYear,
    targetType: '成交金额',
    unit: '万',
    yearlyTarget: 0,
    q1Target: 0,
    q2Target: 0,
    q3Target: 0,
    q4Target: 0,
    m1Target: 0,
    m2Target: 0,
    m3Target: 0,
    m4Target: 0,
    m5Target: 0,
    m6Target: 0,
    m7Target: 0,
    m8Target: 0,
    m9Target: 0,
    m10Target: 0,
    m11Target: 0,
    m12Target: 0
  });
};

// 提交目标表单
const submitTargetForm = async () => {
  try {
    await targetFormRef.value.validate();

    submitLoading.value = true;

    // 准备API数据
    const goalData = {
      target_type: activeTab.value,
      fiscal_year: targetForm.fiscalYear,
      goal_type: targetForm.targetType,
      unit: targetForm.unit,
      yearly_target: targetForm.yearlyTarget,
      q1_target: targetForm.q1Target,
      q2_target: targetForm.q2Target,
      q3_target: targetForm.q3Target,
      q4_target: targetForm.q4Target,
      m1_target: targetForm.m1Target,
      m2_target: targetForm.m2Target,
      m3_target: targetForm.m3Target,
      m4_target: targetForm.m4Target,
      m5_target: targetForm.m5Target,
      m6_target: targetForm.m6Target,
      m7_target: targetForm.m7Target,
      m8_target: targetForm.m8Target,
      m9_target: targetForm.m9Target,
      m10_target: targetForm.m10Target,
      m11_target: targetForm.m11Target,
      m12_target: targetForm.m12Target
    };

    let success = false;

    if (isEdit.value) {
      // 编辑现有目标
      goalData.target_name = targetForm.name;
      success = await updateGoalSetting(targetForm.id, goalData);
    } else {
      // 添加新目标
      if (activeTab.value === 'member') {
        // 成员目标：为每个选中的成员创建目标
        for (const memberId of targetForm.selectedMembers) {
          const member = userOptions.value.find(u => u.id === memberId);
          if (member) {
            const memberGoalData = {
              ...goalData,
              target_id: memberId,
              target_name: member.name
            };
            const result = await createGoalSetting(memberGoalData);
            if (result) success = true;
          }
        }
      } else if (activeTab.value === 'department') {
        // 部门目标
        const dept = departmentOptions.value.find(d => d.name === targetForm.name);
        if (dept) {
          goalData.target_id = dept.id;
          goalData.target_name = dept.name;
          success = await createGoalSetting(goalData);
        }
      } else {
        // 公司目标
        goalData.target_id = '1'; // 公司ID
        goalData.target_name = targetForm.name;
        success = await createGoalSetting(goalData);
      }
    }

    submitLoading.value = false;

    if (success) {
      targetModalVisible.value = false;
      fetchGoalList();
    }
  } catch (error) {
    submitLoading.value = false;
    // 表单验证失败
  }
};

// 关闭目标弹窗
const closeTargetModal = () => {
  targetModalVisible.value = false;
};

// 关闭批量编辑弹窗
const closeBatchEditModal = () => {
  batchEditModalVisible.value = false;
};

// 分页处理
const onPageChange = (page) => {
  currentPage.value = page;
};

const onPageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

// 批量选择处理
const handleBatchSelect = (rowKeys) => {
  batchEditForm.selectedKeys = rowKeys;
};

// 根据ID获取成员名称
const getUserNameById = (userId) => {
  const user = userOptions.value.find(user => user.id === userId);
  return user ? user.name : '';
};

// 移除选中的成员
const removeSelectedMember = (memberId) => {
  const index = targetForm.selectedMembers.indexOf(memberId);
  if (index !== -1) {
    targetForm.selectedMembers.splice(index, 1);
  }
};

// 处理标签页切换
const handleTabChange = (key) => {
  // 更新当前标签页
  activeTab.value = key;
  // 切换标签页时刷新数据
  refreshTargets();
};

// 监听标签页切换
watch(activeTab, (newValue) => {
  console.log('activeTab变化为:', newValue);
});

// 页面加载时初始化数据
onMounted(async () => {
  // 并行加载用户列表、部门列表和目标设置列表
  await Promise.all([
    fetchUserList(),
    fetchDepartmentList(),
    fetchGoalList()
  ]);
});
</script>

<style lang="less" scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
}

// 表格样式优化
:deep(.arco-table-container) {
  border-radius: 4px;
  overflow: hidden;
}

:deep(.arco-table-th) {
  background-color: var(--color-fill-2);
  font-weight: 500;
}

:deep(.arco-table-stripe .arco-table-tr:nth-child(even)) {
  background-color: var(--color-fill-1);
}

// 表单项样式优化
:deep(.arco-form-item-label-col) {
  font-weight: 500;
}

// 弹窗内表单样式
:deep(.arco-modal-body) {
  max-height: 70vh;
  overflow-y: auto;
}

// 目标总计显示样式
.text-blue-600 {
  color: rgb(28, 100, 242);
}

.font-bold {
  font-weight: 600;
}

// 目标设置弹窗样式
.target-form-wrapper {
  padding: 0 10px;
}

.selected-members-tags {
  margin-bottom: 20px;
}

.label-required {
  position: relative;
  font-weight: 500;
  
  &::before {
    content: '*';
    color: #f53f3f;
    margin-right: 2px;
  }
}

.target-setting-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10px;
  
  .target-setting-item {
    flex: 1;
    min-width: 180px;
    padding: 0 10px;
    margin-bottom: 16px;
  }
}

.target-distribution {
  margin-top: 10px;
  
  .target-distribution-title {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 16px;
    color: #1d2129;
  }
  
  .target-quarters-wrapper {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 15px;
    
    .quarter-block {
      width: calc(25% - 12px);
      border: 1px solid #e5e6eb;
      border-radius: 4px;
      padding: 10px;
      background-color: #f7f8fa;
      
      .quarter-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .quarter-label {
          font-size: 13px;
          color: #4e5969;
          font-weight: 500;
          width: 60px;
          white-space: nowrap;
        }
        
        .quarter-input {
          width: 70px;
          border: 1px solid #e5e6eb;
          border-radius: 4px;
          background-color: #fff;
        }
      }
      
      .month-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .month-label {
          font-size: 13px;
          color: #4e5969;
          width: 60px;
          white-space: nowrap;
        }
        
        .month-input {
          width: 70px;
          border: 1px solid #e5e6eb;
          border-radius: 4px;
          background-color: #fff;
        }
      }
    }
  }
}

// 自定义表单项样式
:deep(.custom-form-item) {
  .arco-form-item-label-col {
    padding-bottom: 8px;
  }
}
</style>