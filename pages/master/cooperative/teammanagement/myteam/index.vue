<!--
 - 团队管理页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 成员数量列 -->
      <template #memberCount="{ record }">
        <a-button type="text" @click="showMembersDetail(record)">{{ record.members.length }}</a-button>
      </template>

      <!-- 操作列 -->
      <template #operations="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button type="text" size="small" @click="editTeam(record)">
            <template #icon>
              <icon-edit />
            </template>
            编辑
          </a-button>
          <a-popconfirm content="确定要删除该团队吗？" @ok="deleteTeam(record)">
            <a-button type="text" size="small" status="danger">
              <template #icon>
                <icon-delete />
              </template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>
      
      <!-- 自定义搜索按钮区域 -->
      <template #searchButtons>
        <a-space>
          <a-button type="primary" @click="handleSearch">
            <template #icon>
              <icon-search />
            </template>
            查询
          </a-button>
          <a-button @click="resetSearch">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
          <a-button type="primary" @click="openCreateTeamModal">
          <template #icon>
            <icon-plus />
          </template>
          新建团队
        </a-button>
        <a-button class="ml-2">
          <template #icon>
            <icon-export />
          </template>
          导出数据
        </a-button>
        </a-space>
      </template>
      
      <!-- 表格前的按钮区域 -->
      <!-- <template #tableBeforeButtons>
     
      </template> -->
    </ma-crud>

    <!-- 新建/编辑团队弹窗 -->
    <a-modal
      v-model:visible="teamModalVisible"
      :title="isEdit ? '编辑团队' : '新建团队'"
      @cancel="closeTeamModal"
      @ok="submitTeamForm"
      :ok-loading="submitLoading"
      ok-text="确定"
      cancel-text="取消"
      :mask-closable="false"
      :unmount-on-close="true"
      width="65%"
    >
      <a-form :model="teamForm" ref="teamFormRef" :rules="teamRules" layout="vertical">
        <a-form-item field="name" label="团队名称" required>
          <a-input v-model="teamForm.name" placeholder="请输入团队名称" />
        </a-form-item>

        <a-form-item field="description" label="团队描述">
          <a-textarea v-model="teamForm.description" placeholder="请输入团队描述" :max-length="500" show-word-limit />
        </a-form-item>

        <a-form-item field="leader" label="团队负责人" required>
          <a-select v-model="teamForm.leader" placeholder="请选择团队负责人" allow-search>
            <a-option
              v-for="user in userOptions"
              :key="user.id"
              :value="user.id"
            >{{ user.name }} ({{ user.department }})</a-option>
          </a-select>
        </a-form-item>

        <a-divider>团队成员</a-divider>

        <a-form-item field="members" label="团队成员" required>
          <a-select
            v-model="teamForm.members"
            placeholder="请选择团队成员"
            multiple
            allow-search
            allow-clear
          >
            <a-option
              v-for="user in userOptions"
              :key="user.id"
              :value="user.id"
              :disabled="teamForm.leader === user.id"
            >
              {{ user.name }} ({{ user.department }})
            </a-option>
          </a-select>
        </a-form-item>
   
      </a-form>
    </a-modal>

    <!-- 团队成员详情弹窗 -->
    <a-modal
      v-model:visible="memberDetailVisible"
      title="团队成员详情"
      @cancel="closeMemberDetail"
      :footer="false"
      :mask-closable="true"
      width="60%"
    >
      <div v-if="currentTeam">
        <h3 class="text-lg font-medium mb-4">{{ currentTeam.name }} - 成员列表</h3>
        <a-table
          :columns="memberColumns"
          :data="memberDetailList"
          :pagination="false"
          :bordered="true"
          size="small"
        />
      </div>
    </a-modal>
  </div>
</template>
    
  <script setup>
import { ref, computed, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
// 页面元数据
definePageMeta({
  name: "master-cooperative-teammanagement-myteam",
  path: "/master/cooperative/teammanagement/myteam"
});

// 搜索表单
const searchForm = reactive({
  id: "",
  name: "",
  leader: null,
  timeRange: []
});

// 分页设置
const currentPage = ref(1);
const pageSize = ref(10);
const loading = ref(false);

// 团队表单
const teamModalVisible = ref(false);
const isEdit = ref(false);
const submitLoading = ref(false);
const teamFormRef = ref(null);

// 团队表单数据
const teamForm = reactive({
  id: "",
  name: "",
  description: "",
  leader: null,
  members: [],
  monthlyTarget: 0,
  quarterlyTarget: 0,
  yearlyTarget: 0
});

// 表单验证规则
const teamRules = {
  name: [{ required: true, message: "请输入团队名称" }],
  leader: [{ required: true, message: "请选择团队负责人" }],
  members: [{ required: true, message: "请至少选择一名团队成员" }]
};

// 成员详情弹窗
const memberDetailVisible = ref(false);
const currentTeam = ref(null);
const memberDetailList = ref([]);

// 打开成员详情弹窗
const showMembersDetail = async (team) => {
  currentTeam.value = team;

  try {
    // 获取团队详细信息，包括成员详情
    const response = await teamApi.getById(team.id);
    const teamDetail = response.data;

    // 设置成员详细信息
    memberDetailList.value = teamDetail.members || [];

    memberDetailVisible.value = true;
  } catch (error) {
    console.error('获取团队成员详情失败:', error);
    Message.error('获取团队成员详情失败');
  }
};

// 关闭成员详情弹窗
const closeMemberDetail = () => {
  memberDetailVisible.value = false;
  currentTeam.value = null;
  memberDetailList.value = [];
};

// 格式化金额
const formatAmount = amount => {
  if (!amount) return "0.00";
  return Number(amount).toFixed(2);
};





// 分页处理
const onPageChange = page => {
  currentPage.value = page;
};

const onPageSizeChange = size => {
  pageSize.value = size;
  currentPage.value = 1;
};

// 处理搜索
const handleSearch = () => {
  currentPage.value = 1;
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 团队表格列定义
const columns = reactive([
  {
    title: "团队名称",
    dataIndex: "name",
    width: 150,
    search: true,
    searchSpan: 6
  },
  {
    title: "团队负责人",
    dataIndex: "leaderName",
    width: 120,
    align: "center",
    search: true,
    searchSpan: 6,
    searchKey: "leader",
    searchSlot: true
  },
  {
    title: "成员数量",
    dataIndex: "memberCount",
    slotName: "memberCount",
    width: 100,
    align: "center"
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    width: 150,
    search: true,
    searchSpan: 8,
    searchKey: "timeRange",
    formType: "date-range"
  },
  {
    title: "操作",
    dataIndex: "operations",
    slotName: "operations",
    width: 150,
    align: 'center',
    fixed: "right"
  }
]);

// 团队成员表格列定义
const memberColumns = [
  {
    title: "成员姓名",
    dataIndex: "name"
  },
  {
    title: "角色",
    dataIndex: "role",
    render: ({ record }) => record.role === 'leader' ? '负责人' : '成员'
  },
  {
    title: "部门",
    dataIndex: "department"
  },
  {
    title: "月度目标金额",
    dataIndex: "monthly_target",
    render: ({ record }) => `¥${formatAmount(record.monthly_target || 0)}`
  },
  {
    title: "季度目标金额",
    dataIndex: "quarterly_target",
    render: ({ record }) => `¥${formatAmount(record.quarterly_target || 0)}`
  },
  {
    title: "年度目标金额",
    dataIndex: "yearly_target",
    render: ({ record }) => `¥${formatAmount(record.yearly_target || 0)}`
  },
  {
    title: "加入时间",
    dataIndex: "join_time"
  }
];

// 用户数据
const userOptions = ref([]);

// 团队数据
const teamData = ref([]);
// 引入API
import teamApi from '@/api/provider/team.js'

// 获取团队数据的API函数
const getTeamList = async (params) => {
  try {
    const response = await teamApi.getList(params);
    return {
      success: true,
      message: '获取团队列表成功',
      code: 200,
      data: {
        items: response.data.items || [],
        pageInfo: response.data.pageInfo || {
          total: 0,
          currentPage: 1,
          totalPage: 1
        }
      }
    };
  } catch (error) {
    console.error('获取团队列表失败:', error);
    return {
      success: false,
      message: error.message || '获取团队列表失败',
      code: 500,
      data: {
        items: [],
        pageInfo: {
          total: 0,
          currentPage: 1,
          totalPage: 1
        }
      }
    };
  }
};

// ma-crud组件配置
const crudRef = ref(null);
const crud = reactive({
  // API配置（使用模拟数据）
  api: getTeamList,
  showIndex: true,
  pageLayout: "fixed",
  operationColumn: false, // 禁用自动添加的操作列，因为已在columns中手动定义了
  searchLabelWidth: '100px',
  // 禁用行选择
  rowSelection: false,
  // 显示搜索区域
  showSearch: true,
  // 显示搜索区域后的内容
  showSearchAfter: true,
});

// 重置搜索
const resetSearch = () => {
  searchForm.id = '';
  searchForm.name = '';
  searchForm.leader = null;
  searchForm.timeRange = null;
  
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 打开创建团队弹窗
const openCreateTeamModal = () => {
  isEdit.value = false;
  resetTeamForm();
  teamModalVisible.value = true;
};

// 打开编辑团队弹窗
const editTeam = async (team) => {
  isEdit.value = true;

  try {
    // 获取团队详细信息
    const response = await teamApi.getById(team.id);
    const teamDetail = response.data;

    // 填充表单数据
    teamForm.id = teamDetail.id;
    teamForm.name = teamDetail.name;
    teamForm.leader = teamDetail.leader_id;
    teamForm.members = teamDetail.members.map(m => m.user_id);
    teamForm.description = teamDetail.description || '';
    teamForm.monthlyTarget = teamDetail.monthly_target;
    teamForm.quarterlyTarget = teamDetail.quarterly_target;
    teamForm.yearlyTarget = teamDetail.yearly_target;

    teamModalVisible.value = true;
  } catch (error) {
    console.error('获取团队详情失败:', error);
    Message.error('获取团队详情失败');
  }
};



// 重置团队表单
const resetTeamForm = () => {
  teamForm.id = "";
  teamForm.name = "";
  teamForm.description = "";
  teamForm.leader = null;
  teamForm.members = [];
  teamForm.monthlyTarget = 0;
  teamForm.quarterlyTarget = 0;
  teamForm.yearlyTarget = 0;

  if (teamFormRef.value) {
    teamFormRef.value.resetFields();
  }
};

// 关闭团队弹窗
const closeTeamModal = () => {
  teamModalVisible.value = false;
  resetTeamForm();
};

// 提交团队表单
const submitTeamForm = async () => {
  if (!teamFormRef.value) return;

  try {
    await teamFormRef.value.validate();

    submitLoading.value = true;

    // 准备提交数据
    const submitData = {
      name: teamForm.name,
      leader_id: teamForm.leader,
      members: teamForm.members,
      description: teamForm.description || '',
      monthly_target: teamForm.monthlyTarget || null,
      quarterly_target: teamForm.quarterlyTarget || null,
      yearly_target: teamForm.yearlyTarget || null
    };

    try {
      if (isEdit.value) {
        // 编辑现有团队
        await teamApi.update(teamForm.id, submitData);
        Message.success("团队更新成功");
      } else {
        // 创建新团队
        await teamApi.create(submitData);
        Message.success("团队创建成功");
      }

      submitLoading.value = false;
      teamModalVisible.value = false;
      resetTeamForm();

      // 刷新团队列表
      if (crudRef.value) {
        crudRef.value.refresh();
      }
    } catch (apiError) {
      console.error('API请求失败:', apiError);
      Message.error(apiError.message || (isEdit.value ? '更新团队失败' : '创建团队失败'));
      submitLoading.value = false;
    }
  } catch (error) {
    console.error("表单验证失败:", error);
    submitLoading.value = false;
  }
};

// 删除团队
const deleteTeam = async (team) => {
  loading.value = true;

  try {
    await teamApi.delete(team.id);
    Message.success("团队删除成功");

    // 刷新表格数据
    if (crudRef.value) {
      crudRef.value.refresh();
    }
  } catch (error) {
    console.error('删除团队失败:', error);
    Message.error(error.message || '删除团队失败');
  } finally {
    loading.value = false;
  }
};

// 加载用户列表
const loadUserList = async () => {
  try {
    const response = await teamApi.getUserList();
    userOptions.value = response.data || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
    Message.error('获取用户列表失败');
  }
};

// 初始化
onMounted(() => {
  // 加载用户列表
  loadUserList();
});
</script>
  
  <style scoped>
.empty-placeholder {
  display: flex;
  height: 100px;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
  border-radius: 4px;
}
</style>