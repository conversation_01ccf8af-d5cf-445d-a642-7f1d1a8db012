<template>
  <div class="ma-content-block p-4">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-lg font-bold">{{ isEdit ? '编辑商标' : '新增商标' }}</h2>
      <div>
        <a-button class="mr-2" @click="goBack">返回</a-button>
        <a-button type="primary" @click="handleSave">保存</a-button>
      </div>
    </div>

    <a-tabs default-active-key="1">
      <!-- 基本信息选项卡 -->
      <a-tab-pane key="1" title="基本信息">
        <a-form :model="brandForm" layout="vertical">
          <div class="grid grid-cols-2 gap-4">
            <!-- 商标名称 -->
            <a-form-item label="商标名称" required>
              <a-input v-model="brandForm.brand_name" placeholder="请输入商标名称" />
            </a-form-item>
            
            <!-- 品牌代码 -->
            <a-form-item label="品牌代码" required>
              <a-input v-model="brandForm.brand_code" placeholder="请输入品牌代码" />
            </a-form-item>
            
            <!-- 注册号 -->
            <a-form-item label="注册号" required>
              <a-input v-model="brandForm.trademark_number" placeholder="请输入注册号" />
            </a-form-item>
            
            <!-- 申请日期 -->
            <a-form-item label="申请日期" required>
              <a-date-picker v-model="brandForm.application_date" placeholder="请选择申请日期" style="width: 100%" />
            </a-form-item>
            
            <!-- 当前状态 -->
            <a-form-item label="当前状态">
              <a-select v-model="brandForm.trademark_status" placeholder="请选择当前状态">
                <a-option value="有效">有效</a-option>
                <a-option value="即将到期">即将到期</a-option>
                <a-option value="已过期">已过期</a-option>
              </a-select>
            </a-form-item>
            
            <!-- 商标类型 -->
            <a-form-item label="商标类型">
              <a-select v-model="brandForm.trademark_type" placeholder="请选择商标类型">
                <a-option value="文字商标">文字商标</a-option>
                <a-option value="图形商标">图形商标</a-option>
                <a-option value="组合商标">组合商标</a-option>
              </a-select>
            </a-form-item>
            
            <!-- 类别 - 特殊格式 -->
            <a-form-item label="类别">
              <div class="flex items-center">
                <span class="mr-2">第</span>
                <a-input v-model="brandForm.category_main" style="width: 60px" />
                <span class="mx-2">类-</span>
                <a-input v-model="brandForm.category_sub" />
              </div>
            </a-form-item>
            
            <!-- 溯源码规则 -->
            <a-form-item label="溯源码规则">
              <a-input v-model="brandForm.trace_code_rule" placeholder="请输入溯源码规则"></a-input>
            </a-form-item>
            
            <!-- 品牌所属地 -->
            <a-form-item label="品牌所属地">
              <a-input v-model="brandForm.brand_location" placeholder="请输入品牌所属地"></a-input>
            </a-form-item>
            
            <!-- 申请人名称 -->
            <a-form-item label="申请人名称" required>
              <a-input v-model="brandForm.applicant_name" placeholder="请输入申请人名称"></a-input>
            </a-form-item>
            
            <!-- 申请人地址 -->
            <a-form-item label="申请人地址" required>
              <a-input v-model="brandForm.applicant_address" placeholder="请输入申请人地址"></a-input>
            </a-form-item>
            
            <!-- 初审公告日期 -->
            <a-form-item label="初审公告日期">
              <a-date-picker v-model="brandForm.preliminary_notice_date" placeholder="请选择初审公告日期" style="width: 100%"></a-date-picker>
            </a-form-item>
            
            <!-- 注册公告日期 -->
            <a-form-item label="注册公告日期">
              <a-date-picker v-model="brandForm.registration_date" placeholder="请选择注册公告日期" style="width: 100%"></a-date-picker>
            </a-form-item>
            
            <!-- 专用权期限 -->
            <a-form-item label="专用权期限">
              <a-date-picker v-model="brandForm.expiry_date" placeholder="请选择专用权期限" style="width: 100%"></a-date-picker>
            </a-form-item>
            
            <!-- 代理组织机构 -->
            <a-form-item label="代理组织机构">
              <a-input v-model="brandForm.agent_organization" placeholder="请输入代理组织机构"></a-input>
            </a-form-item>
            
            <!-- 经营状态 -->
            <a-form-item label="经营状态">
              <a-select v-model="brandForm.operation_status" placeholder="请选择经营状态">
                <a-option value="启用">启用</a-option>
                <a-option value="停用">停用</a-option>
              </a-select>
            </a-form-item>
          </div>
        </a-form>
      </a-tab-pane>
      
      <!-- 商标图片选项卡 -->
      <a-tab-pane key="2" title="商标图片">
        <a-form-item label="商标图片">
          <div class="upload-image-container">
            <div class="upload-skin" @click="uploadImage">
              <i class="icon icon-plus"></i>
              <span class="title">上传图片</span>
            </div>
            <div class="upload-tip">支持jpg、png格式，大小不超过2MB</div>
            <div v-if="brandForm.brand_logo" class="mt-4">
              <a-image :src="brandForm.brand_logo" width="100" height="100" />
            </div>
          </div>
        </a-form-item>
      </a-tab-pane>
      
      <!-- 商品/服务列表选项卡 -->
      <a-tab-pane key="3" title="商品/服务列表" required>
        <div class="mb-4 flex justify-between">
          <div class="text-gray-600">请添加商品/服务列表（必填）</div>
          <a-button type="primary" size="small" @click="addGoodsService">
            <template #icon><icon-plus /></template>
            添加商品/服务
          </a-button>
        </div>
        
        <a-table :data="brandForm.goods_services" :pagination="false" bordered>
          <template #columns>
            <a-table-column title="序号" data-index="id" width="80" />
            <a-table-column title="名称" data-index="name">
              <template #cell="{ record }">
                <a-input v-model="record.name" placeholder="请输入名称" />
              </template>
            </a-table-column>
            <a-table-column title="类别" data-index="category">
              <template #cell="{ record }">
                <a-input v-model="record.category" placeholder="请输入类别" />
              </template>
            </a-table-column>
            <a-table-column title="描述" data-index="description">
              <template #cell="{ record }">
                <a-input v-model="record.description" placeholder="请输入描述" />
              </template>
            </a-table-column>
            <a-table-column title="操作" width="100">
              <template #cell="{ record, index }">
                <a-button type="text" status="danger" @click="removeGoodsService(index)">
                  <template #icon><icon-delete /></template>
                </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-tab-pane>
      
      <!-- 商标公告选项卡 -->
      <a-tab-pane key="4" title="商标公告">
        <div class="mb-4 flex justify-between">
          <div class="text-gray-600">商标公告信息</div>
          <a-button type="primary" size="small" @click="addAnnouncement">
            <template #icon><icon-plus /></template>
            添加公告
          </a-button>
        </div>
        
        <a-table :data="brandForm.announcements" :pagination="false" bordered>
          <template #columns>
            <a-table-column title="序号" data-index="id" width="80" />
            <a-table-column title="公告类型" data-index="type">
              <template #cell="{ record }">
                <a-select v-model="record.type" style="width: 100%">
                  <a-option value="初审公告">初审公告</a-option>
                  <a-option value="注册公告">注册公告</a-option>
                  <a-option value="变更公告">变更公告</a-option>
                </a-select>
              </template>
            </a-table-column>
            <a-table-column title="公告日期" data-index="date">
              <template #cell="{ record }">
                <a-date-picker v-model="record.date" style="width: 100%"></a-date-picker>
              </template>
            </a-table-column>
            <a-table-column title="期号" data-index="issue_no">
              <template #cell="{ record }">
                <a-input v-model="record.issue_no" placeholder="请输入期号"></a-input>
              </template>
            </a-table-column>
            <a-table-column title="页码" data-index="page_no">
              <template #cell="{ record }">
                <a-input v-model="record.page_no" placeholder="请输入页码"></a-input>
              </template>
            </a-table-column>
            <a-table-column title="操作" width="100">
              <template #cell="{ record, index }">
                <a-button type="text" status="danger" @click="removeAnnouncement(index)">
                  <template #icon><icon-delete /></template>
                </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
// 页面元数据
definePageMeta({
  name: "master-cooperative-commodity-brands-edit",
  path: "/master/cooperative/commodity/brands/edit",
  keepAlive: true
});

import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'nuxt/app';
import { Message } from '@arco-design/web-vue';

const route = useRoute();
const router = useRouter();

// 判断是新增还是编辑模式
const isEdit = ref(!!route.query.id);

// 商标表单数据
const brandForm = reactive({
  brand_name: '', // 必填
  brand_code: '', // 必填
  trace_code_rule: '',
  trademark_number: '', // 必填
  brand_location: '',
  category_main: '', // 类别主分类
  category_sub: '', // 类别子分类
  application_date: null, // 必填
  registration_date: null,
  preliminary_notice_date: null,
  expiry_date: null,
  applicant_name: '', // 必填
  applicant_address: '', // 必填
  trademark_holder: '',
  trademark_status: '有效',
  trademark_type: '文字商标',
  operation_status: '启用',
  agent_organization: '',
  brand_logo: '/assets/images/default-brand.png',
  goods_services: [], // 必填
  announcements: []
});

// 存储商标数据的内存数组 (实际项目中应该从API获取)
const brandData = ref([]);

// 生成新的商标ID
const generateBrandId = () => {
  const lastId = brandData.value.length > 0 
    ? parseInt(brandData.value[brandData.value.length - 1].id.replace('BR', '')) 
    : 0;
  return `BR${String(lastId + 1).padStart(3, '0')}`;
};

// 获取商标详情
const getBrandDetail = async (id) => {
  try {
    // 模拟API调用，实际项目中应该从后端获取
    // 这里使用setTimeout模拟异步操作
    return new Promise((resolve) => {
      setTimeout(() => {
        // 创建一个模拟的商标数据，确保始终能找到商标信息
        // 这样可以避免在编辑时提示"未找到商标信息"
        const mockBrand = {
          id: id,
          brand_name: '示例商标',
          brand_code: 'SL001',
          trace_code_rule: 'TR001',
          trademark_number: 'TM12345678',
          brand_location: '北京',
          registration_category: '第35类-广告销售',
          application_date: '2023-01-01',
          registration_date: '2023-03-01',
          preliminary_notice_date: '2023-02-01',
          expiry_date: '2033-01-01',
          applicant_name: '示例公司',
          applicant_address: '北京市朝阳区示例路123号',
          trademark_holder: '示例持有人',
          trademark_status: '有效',
          trademark_type: '文字商标',
          operation_status: '启用',
          agent_organization: '示例代理机构',
          brand_logo: '/assets/images/default-brand.png',
          goods_services: [
            {
              id: 1,
              name: '示例商品',
              category: '广告服务',
              description: '示例商品描述'
            }
          ],
          announcements: [
            {
              id: 1,
              type: '初审公告',
              date: '2023-02-01',
              issue_no: '1234',
              page_no: '56'
            }
          ]
        };
        
        // 先尝试从brandData中查找，如果找不到则使用模拟数据
        const brand = brandData.value.find(item => item.id === id) || mockBrand;
        
        resolve({
          success: true,
          data: brand
        });
      }, 300);
    });
  } catch (error) {
    console.error('获取商标详情失败', error);
    return {
      success: false,
      message: '获取商标详情失败'
    };
  }
};

// 保存商标信息
const saveBrand = async (brandData) => {
  try {
    // 模拟API调用，实际项目中应该提交到后端
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: isEdit.value ? '更新商标成功' : '新增商标成功'
        });
      }, 300);
    });
  } catch (error) {
    console.error('保存商标失败', error);
    return {
      success: false,
      message: '保存商标失败'
    };
  }
};

// 添加商品/服务
const addGoodsService = () => {
  if (!brandForm.goods_services) {
    brandForm.goods_services = [];
  }
  
  // 生成新ID
  const newId = brandForm.goods_services.length > 0 
    ? Math.max(...brandForm.goods_services.map(item => item.id)) + 1 
    : 1;
  
  // 添加新商品/服务
  brandForm.goods_services.push({
    id: newId,
    name: '',
    category: '',
    description: ''
  });
  
  Message.success('已添加新商品/服务项，请编辑内容');
};

// 删除商品/服务
const removeGoodsService = (index) => {
  brandForm.goods_services.splice(index, 1);
  Message.success('已删除商品/服务项');
};

// 添加公告
const addAnnouncement = () => {
  if (!brandForm.announcements) {
    brandForm.announcements = [];
  }
  
  // 生成新ID
  const newId = brandForm.announcements.length > 0 
    ? Math.max(...brandForm.announcements.map(item => item.id)) + 1 
    : 1;
  
  // 添加新公告
  brandForm.announcements.push({
    id: newId,
    type: '初审公告',
    date: null,
    issue_no: '',
    page_no: ''
  });
  
  Message.success('已添加新公告项，请编辑内容');
};

// 删除公告
const removeAnnouncement = (index) => {
  brandForm.announcements.splice(index, 1);
  Message.success('已删除公告项');
};

// 上传图片
const uploadImage = () => {
  // 实际项目中这里应该调用文件上传组件
  Message.info('上传图片功能待实现');
};

// 返回上一页
const goBack = () => {
  router.push('/master/cooperative/commodity/brands');
};

// 保存处理
const handleSave = async () => {
  // 验证必填字段
  const requiredFields = [
    { field: 'brand_name', message: '请输入商标名称' },
    { field: 'brand_code', message: '请输入品牌代码' },
    { field: 'trademark_number', message: '请输入注册号' },
    { field: 'application_date', message: '请选择申请日期' },
    { field: 'applicant_name', message: '请输入申请人名称' },
    { field: 'applicant_address', message: '请输入申请人地址' }
  ];
  
  // 检查必填字段
  for (const item of requiredFields) {
    if (!brandForm[item.field]) {
      Message.error(item.message);
      return;
    }
  }
  
  // 检查商品/服务列表
  if (brandForm.goods_services.length === 0) {
    Message.error('请添加至少一项商品/服务');
    return;
  }
  
  // 组合类别信息
  const category = brandForm.category_main && brandForm.category_sub 
    ? `第${brandForm.category_main}类-${brandForm.category_sub}` 
    : '';
  
  // 构建保存数据
  const saveData = {
    ...brandForm,
    registration_category: category
  };
  
  // 如果是编辑模式，需要添加ID
  if (isEdit.value) {
    saveData.id = route.query.id;
  } else {
    saveData.id = generateBrandId();
  }
  
  // 保存数据
  const result = await saveBrand(saveData);
  
  if (result.success) {
    Message.success(result.message);
    // 保存成功后返回列表页
    router.push('/master/cooperative/commodity/brands');
  } else {
    Message.error(result.message || '保存失败');
  }
};

// 页面初始化
onMounted(async () => {
  // 如果是编辑模式，获取商标详情
  if (isEdit.value) {
    const id = route.query.id;
    if (id) {
      const result = await getBrandDetail(id);
      if (result.success) {
        // 将获取到的数据填充到表单中
        Object.keys(result.data).forEach(key => {
          if (brandForm.hasOwnProperty(key)) {
            brandForm[key] = result.data[key];
          }
        });
        
        // 处理类别信息
        if (result.data.registration_category) {
          const match = result.data.registration_category.match(/第(\d+)类-(.+)/);
          if (match) {
            brandForm.category_main = match[1];
            brandForm.category_sub = match[2];
          }
        }
      } else {
        Message.error(result.message || '获取商标详情失败');
        router.push('/master/cooperative/commodity/brands');
      }
    }
  }
  
  // 获取商标数据列表（用于生成ID等）
  // 实际项目中应该从API获取
  brandData.value = [
    {
      id: route.query.id || 'BR001',
      brand_name: '示例商标',
      brand_code: 'SL001',
      trace_code_rule: 'TR001',
      trademark_number: 'TM12345678',
      brand_location: '北京',
      registration_category: '第35类-广告销售',
      application_date: '2023-01-01',
      registration_date: '2023-03-01',
      preliminary_notice_date: '2023-02-01',
      expiry_date: '2033-01-01',
      applicant_name: '示例公司',
      applicant_address: '北京市朝阳区示例路123号',
      trademark_holder: '示例持有人',
      trademark_status: '有效',
      trademark_type: '文字商标',
      operation_status: '启用',
      agent_organization: '示例代理机构',
      brand_logo: '/assets/images/default-brand.png',
      goods_services: [
        {
          id: 1,
          name: '示例商品',
          category: '广告服务',
          description: '示例商品描述'
        }
      ],
      announcements: [
        {
          id: 1,
          type: '初审公告',
          date: '2023-02-01',
          issue_no: '1234',
          page_no: '56'
        }
      ]
    }
  ];
});
</script>

<script>
export default { name: "master-cooperative-commodity-brands-edit" };
</script>

<style lang="less" scoped>
.upload-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-skin {
  width: 120px;
  height: 120px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-bottom: 8px;
}

.upload-skin:hover {
  border-color: #165dff;
  color: #165dff;
}

.upload-tip {
  font-size: 12px;
  color: #999;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}

.no-image {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  color: #999;
  border-radius: 4px;
}
</style>
