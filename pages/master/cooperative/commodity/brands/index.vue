<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" :row-class="() => ''" :row-selection="{ type: 'none' }">
      <!-- 自定义搜索按钮区域 -->
      <template #searchButtons>
        <a-space>
          <a-button type="primary" @click="handleSearch">
            <template #icon>
              <icon-search />
            </template>
            查询
          </a-button>
          <a-button @click="resetSearch">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
          <a-button type="primary" @click="goToAddPage()">
            <template #icon>
              <icon-plus />
            </template>
            新增
          </a-button>
        </a-space>
      </template>
      
      <!-- 商标状态列 -->
      <template #trademark_status="{ record }">
        <a-tag :color="record.trademark_status === '有效' ? 'green' : record.trademark_status === '即将到期' ? 'orange' : 'red'">
          {{ record.trademark_status }}
        </a-tag>
      </template>
      
      <!-- 商标logo列 -->
      <template #brand_logo="{ record }">
        <a-avatar :size="40" :image-url="record.brand_logo" />
      </template>
      
      <!-- 经营状态列 -->
      <template #operation_status="{ record }">
        <a-tag :color="record.operation_status === '启用' ? 'green' : 'red'">
          {{ record.operation_status }}
        </a-tag>
      </template>
      
      <!-- 操作列 -->
      <template #operationBeforeExtend="{ record }">
        <div class="flex justify-center space-x-2">
          <a-button type="text" size="small" @click="openDetails(record)">详情</a-button>
          <a-button type="text" size="small" @click="goToEditPage(record)">编辑</a-button>
          <a-button 
            type="text" 
            size="small" 
            @click="toggleStatus(record)"
            :status="record.operation_status === '启用' ? 'danger' : 'success'"
          >
            {{ record.operation_status === '启用' ? '停用' : '启用' }}
          </a-button>
        </div>
      </template>
    </ma-crud>
    
    <!-- 商标详情抽屉 -->
    <a-drawer 
      :visible="detailVisible" 
      @update:visible="(val) => detailVisible = val"
      @cancel="closeDetail" 
      :footer="true"
      :mask-closable="false"
      :width="1000"
      placement="right"
    >
      <template #title>
        商标详情
      </template>
      
      <!-- 底部按钮区域 -->
      <template #footer>
        <div class="flex justify-end">
          <a-button @click="closeDetail" class="mr-2">取消</a-button>
          <!-- 编辑模式下显示保存按钮，查看模式下显示确定按钮 -->
          <a-button 
            type="primary" 
            @click="isEditing ? saveEdit() : closeDetail()" 
          >{{ isEditing ? '保存' : '确定' }}</a-button>
        </div>
      </template>
      
      <div class="brand-detail-container">
        <!-- 商标基本信息头部 -->
        <div class="brand-header mb-4 pb-4 border-b border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <a-avatar 
                :size="64" 
                :image-url="detailRecord.logo || '/assets/images/default-brand.png'" 
                class="mr-4"
              >
              </a-avatar>
              <div class="flex items-center">
                <h3 class="text-lg font-medium">{{ detailRecord.brand_name || '未知商标' }}</h3>
              </div>
            </div>
            <div>
              <a-tag :color="detailRecord.trademark_status === '有效' ? 'green' : detailRecord.trademark_status === '即将到期' ? 'orange' : 'red'" size="large">
                {{ detailRecord.trademark_status }}
              </a-tag>
            </div>
          </div>
        </div>
        
        <!-- 选项卡导航 -->
        <a-tabs :active-key="detailActiveTab" @update:active-key="(key) => detailActiveTab = key">
          <a-tab-pane key="1" title="基本信息">
            <div class="info-section">
              <div class="grid grid-cols-2 gap-4">
                <!-- 商标名称 -->
                <div class="info-item">
                  <div class="text-gray-500">商标名称：</div>
                  <div v-if="!isEditing">{{ detailRecord.brand_name || '-' }}</div>
                  <a-input v-else v-model="detailRecord.brand_name" placeholder="请输入商标名称" />
                </div>
                
                <!-- 品牌代码 -->
                <div class="info-item">
                  <div class="text-gray-500">品牌代码：</div>
                  <div v-if="!isEditing">{{ detailRecord.brand_code || '-' }}</div>
                  <a-input v-else v-model="detailRecord.brand_code" placeholder="请输入品牌代码" />
                </div>
                
                <!-- 注册号/商标号 -->
                <div class="info-item">
                  <div class="text-gray-500">注册号：</div>
                  <div v-if="!isEditing">{{ detailRecord.trademark_number || '-' }}</div>
                  <a-input v-else v-model="detailRecord.trademark_number" placeholder="请输入注册号" />
                </div>
                
                <!-- 申请日期 -->
                <div class="info-item">
                  <div class="text-gray-500">申请日期：</div>
                  <div v-if="!isEditing">{{ detailRecord.application_date || '-' }}</div>
                  <a-date-picker v-else v-model="detailRecord.application_date" placeholder="请选择申请日期" />
                </div>
                
                <!-- 商标状态 -->
                <div class="info-item">
                  <div class="text-gray-500">商标状态：</div>
                  <div v-if="!isEditing">{{ detailRecord.trademark_status || '-' }}</div>
                  <a-select v-else v-model="detailRecord.trademark_status" placeholder="请选择商标状态">
                    <a-option value="有效">有效</a-option>
                    <a-option value="即将到期">即将到期</a-option>
                    <a-option value="已过期">已过期</a-option>
                  </a-select>
                </div>
                
                <!-- 商标类型 -->
                <div class="info-item">
                  <div class="text-gray-500">商标类型：</div>
                  <div v-if="!isEditing">{{ detailRecord.trademark_type || '-' }}</div>
                  <a-select v-else v-model="detailRecord.trademark_type" placeholder="请选择商标类型">
                    <a-option value="文字商标">文字商标</a-option>
                    <a-option value="图形商标">图形商标</a-option>
                    <a-option value="组合商标">组合商标</a-option>
                    <a-option value="立体商标">立体商标</a-option>
                    <a-option value="声音商标">声音商标</a-option>
                  </a-select>
                </div>
                
                <!-- 类别 -->
                <div class="info-item">
                  <div class="text-gray-500">类别：</div>
                  <div v-if="!isEditing">{{ detailRecord.registration_category || '-' }}</div>
                  <a-input v-else v-model="detailRecord.registration_category" placeholder="请输入类别" />
                </div>
                
                <!-- 溯源码规则 -->
                <div class="info-item">
                  <div class="text-gray-500">溯源码规则：</div>
                  <div v-if="!isEditing">{{ detailRecord.trace_code_rule || '-' }}</div>
                  <a-input v-else v-model="detailRecord.trace_code_rule" placeholder="请输入溯源码规则" />
                </div>
                
                <!-- 品牌所属地 -->
                <div class="info-item">
                  <div class="text-gray-500">品牌所属地：</div>
                  <div v-if="!isEditing">{{ detailRecord.brand_location || '-' }}</div>
                  <a-input v-else v-model="detailRecord.brand_location" placeholder="请输入品牌所属地" />
                </div>
                
                <!-- 申请人名称 -->
                <div class="info-item">
                  <div class="text-gray-500">申请人名称：</div>
                  <div v-if="!isEditing">{{ detailRecord.applicant_name || '-' }}</div>
                  <a-input v-else v-model="detailRecord.applicant_name" placeholder="请输入申请人名称" />
                </div>
                
                <!-- 申请人地址 -->
                <div class="info-item">
                  <div class="text-gray-500">申请人地址：</div>
                  <div v-if="!isEditing">{{ detailRecord.applicant_address || '-' }}</div>
                  <a-input v-else v-model="detailRecord.applicant_address" placeholder="请输入申请人地址" />
                </div>
                
                <!-- 初审公告日期 -->
                <div class="info-item">
                  <div class="text-gray-500">初审公告日期：</div>
                  <div v-if="!isEditing">{{ detailRecord.preliminary_notice_date || '-' }}</div>
                  <a-date-picker v-else v-model="detailRecord.preliminary_notice_date" placeholder="请选择初审公告日期" />
                </div>
                
                <!-- 注册公告日期 -->
                <div class="info-item">
                  <div class="text-gray-500">注册公告日期：</div>
                  <div v-if="!isEditing">{{ detailRecord.registration_date || '-' }}</div>
                  <a-date-picker v-else v-model="detailRecord.registration_date" placeholder="请选择注册公告日期" />
                </div>
                
                <!-- 专用权期限 -->
                <div class="info-item">
                  <div class="text-gray-500">专用权期限：</div>
                  <div v-if="!isEditing">{{ detailRecord.exclusive_rights_duration || '-' }}</div>
                  <a-input v-else v-model="detailRecord.exclusive_rights_duration" placeholder="请输入专用权期限" />
                </div>
                
                <!-- 有效期 -->
                <div class="info-item">
                  <div class="text-gray-500">有效期至：</div>
                  <div v-if="!isEditing">{{ detailRecord.expiry_date || '-' }}</div>
                  <a-date-picker v-else v-model="detailRecord.expiry_date" placeholder="请选择有效期" />
                </div>
                
                <!-- 代理组织机构 -->
                <div class="info-item">
                  <div class="text-gray-500">代理组织机构：</div>
                  <div v-if="!isEditing">{{ detailRecord.agent_organization || '-' }}</div>
                  <a-input v-else v-model="detailRecord.agent_organization" placeholder="请输入代理组织机构" />
                </div>
                
                <!-- 经营状态 -->
                <div class="info-item">
                  <div class="text-gray-500">经营状态：</div>
                  <div v-if="!isEditing">{{ detailRecord.operation_status || '-' }}</div>
                  <a-select v-else v-model="detailRecord.operation_status" placeholder="请选择经营状态">
                    <a-option value="启用">启用</a-option>
                    <a-option value="停用">停用</a-option>
                  </a-select>
                </div>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="2" title="商标图片">
            <div class="info-section">
              <div class="image-preview mb-4">
                <div v-if="detailRecord.trademark_image" class="image-container">
                  <img :src="detailRecord.trademark_image" alt="商标图片" style="max-width: 100%; max-height: 300px;" />
                </div>
                <div v-else class="no-image">暂无商标图片</div>
              </div>
              
              <div v-if="isEditing" class="upload-image-container">
                <div class="upload-skin" @click="uploadImage">
                  <i class="icon icon-plus"></i>
                  <span class="title">上传图片</span>
                </div>
                <div class="upload-tip">支持jpg、png格式，大小不超过2MB</div>
              </div>
            </div>
          </a-tab-pane>
          
          <!-- 商品/服务列表 -->
          <a-tab-pane key="3" title="商品/服务列表">
            <div class="info-section">
              <a-table 
                :columns="goodsServiceColumns" 
                :data="detailRecord.goods_services || []" 
                :pagination="false"
                border="{{border: true}}"
              >
                <template #empty>
                  <div class="text-center py-4 text-gray-400">暂无商品/服务列表数据</div>
                </template>
              </a-table>
              
              <div v-if="isEditing" class="mt-4">
                <a-button type="primary" @click="addGoodsService">
                  <template #icon><icon-plus /></template>
                  添加商品/服务
                </a-button>
              </div>
            </div>
          </a-tab-pane>
          
          <!-- 商标流程 -->
          <a-tab-pane key="4" title="商标流程">
            <div class="info-section">
              <a-timeline>
                <a-timeline-item v-if="detailRecord.application_date">
                  <div class="font-medium">商标申请</div>
                  <div class="text-gray-500">{{ detailRecord.application_date }}</div>
                </a-timeline-item>
                <a-timeline-item v-if="detailRecord.preliminary_notice_date">
                  <div class="font-medium">初审公告</div>
                  <div class="text-gray-500">{{ detailRecord.preliminary_notice_date }}</div>
                </a-timeline-item>
                <a-timeline-item v-if="detailRecord.registration_date">
                  <div class="font-medium">注册公告</div>
                  <div class="text-gray-500">{{ detailRecord.registration_date }}</div>
                </a-timeline-item>
                <a-timeline-item v-if="detailRecord.expiry_date">
                  <div class="font-medium">有效期至</div>
                  <div class="text-gray-500">{{ detailRecord.expiry_date }}</div>
                </a-timeline-item>
              </a-timeline>
              
              <div v-if="!detailRecord.application_date && !detailRecord.preliminary_notice_date && !detailRecord.registration_date && !detailRecord.expiry_date" class="text-center py-4 text-gray-400">
                暂无商标流程数据
              </div>
            </div>
          </a-tab-pane>
          
          <!-- 商标公告 -->
          <a-tab-pane key="5" title="商标公告">
            <div class="info-section">
              <a-table 
                :columns="announcementColumns" 
                :data="detailRecord.announcements || []" 
                :pagination="false"
                border="{{border: true}}"
              >
                <template #empty>
                  <div class="text-center py-4 text-gray-400">暂无商标公告数据</div>
                </template>
              </a-table>
            </div>
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-drawer>
    
  
  </div>
</template>

<style scoped>
.brand-detail-container,
.brand-form-container {
  padding: 16px;
}

.info-item {
  margin-bottom: 16px;
}

.upload-image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-skin {
  width: 120px;
  height: 120px;
  border: 1px dashed #ccc;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-bottom: 8px;
}

.upload-skin:hover {
  border-color: #165dff;
  color: #165dff;
}

.upload-tip {
  font-size: 12px;
  color: #999;
}

.image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}

.no-image {
  width: 100%;
  height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  color: #999;
  border-radius: 4px;
}
</style>

<script setup>
// 页面元数据
definePageMeta({
  name: "master-cooperative-commodity-brands",
  path: "/master/cooperative/commodity/brands"
});

import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'nuxt/app';
import { Message } from '@arco-design/web-vue';

// 表格引用
const crudRef = ref(null);
const router = useRouter();

// 详情抽屉状态
const detailVisible = ref(false);
const detailRecord = reactive({});
const isEditing = ref(false);
const detailActiveTab = ref('1');

// 处理搜索和重置函数

// 处理搜索操作
const handleSearch = () => {
  if (crudRef.value) {
    crudRef.value.search();
    Message.success('搜索成功');
  }
};

// 重置搜索条件
const resetSearch = () => {
  if (crudRef.value) {
    crudRef.value.searchReset();
    Message.success('已重置搜索条件');
  }
};

// 存储商标数据的内存数组
const brandData = ref([
  {
    id: 'BR001',
    brand_name: '耐克',
    brand_logo: '/assets/images/brands/nike.png',
    brand_code: 'NIKE001',
    trace_code_rule: 'NK-###-***',
    trademark_number: '**********',
    brand_location: '美国',
    registration_category: '第25类-服装鞋帽',
    application_date: '2019-05-10',
    preliminary_notice_date: '2019-10-15',
    registration_date: '2020-01-15',
    expiry_date: '2030-01-14',
    trademark_holder: '耐克国际有限公司',
    trademark_status: '有效',
    operation_status: '启用',
    trademark_image: '/assets/images/brands/nike_tm.png',
    trademark_type: '组合商标',
    applicant_name: '耐克国际有限公司',
    applicant_address: '美国俄勒冈州比弗顿市鲍尔曼大道1号',
    exclusive_rights_duration: '10年',
    agent_organization: '中国专利代理(香港)有限公司',
    goods_services: [
      { id: 1, name: '运动鞋', category: '鞋类', description: '各类运动鞋' },
      { id: 2, name: '运动服', category: '服装类', description: '各类运动服装' },
      { id: 3, name: '运动帽', category: '配饰类', description: '各类运动帽' }
    ],
    announcements: [
      { id: 1, type: '初审公告', date: '2019-10-15', issue_no: '第1665期', page_no: '125' },
      { id: 2, type: '注册公告', date: '2020-01-15', issue_no: '第1675期', page_no: '87' }
    ]
  },
  {
    id: 'BR002',
    brand_name: '阿迪达斯',
    brand_logo: '/assets/images/brands/adidas.png',
    brand_code: 'ADIDAS002',
    trace_code_rule: 'AD-###-***',
    trademark_number: 'TM20250002',
    brand_location: '德国',
    registration_category: '第25类-服装鞋帽',
    registration_date: '2021-05-20',
    expiry_date: '2031-05-19',
    trademark_holder: '阿迪达斯股份公司',
    trademark_status: '有效',
    operation_status: '启用',
    trademark_image: '/assets/images/brands/adidas_tm.png'
  },
  {
    id: 'BR003',
    brand_name: '苹果',
    brand_logo: '/assets/images/brands/apple.png',
    brand_code: 'APPLE003',
    trace_code_rule: 'AP-###-***',
    trademark_number: 'TM20250003',
    brand_location: '美国',
    registration_category: '第9类-电子产品',
    registration_date: '2019-11-10',
    expiry_date: '2029-11-09',
    trademark_holder: '苹果公司',
    trademark_status: '有效',
    operation_status: '启用',
    trademark_image: '/assets/images/brands/apple_tm.png'
  },
  {
    id: 'BR004',
    brand_name: '华为',
    brand_logo: '/assets/images/brands/huawei.png',
    brand_code: 'HUAWEI004',
    trace_code_rule: 'HW-###-***',
    trademark_number: 'TM20250004',
    brand_location: '中国',
    registration_category: '第9类-电子产品',
    registration_date: '2018-08-05',
    expiry_date: '2023-08-04',
    trademark_holder: '华为技术有限公司',
    trademark_status: '即将到期',
    operation_status: '启用',
    trademark_image: '/assets/images/brands/huawei_tm.png'
  },
  {
    id: 'BR005',
    brand_name: '小米',
    brand_logo: '/assets/images/brands/xiaomi.png',
    brand_code: 'XIAOMI005',
    trace_code_rule: 'XM-###-***',
    trademark_number: 'TM20250005',
    brand_location: '中国',
    registration_category: '第9类-电子产品',
    registration_date: '2017-03-22',
    expiry_date: '2022-03-21',
    trademark_holder: '小米科技有限公司',
    trademark_status: '已过期',
    operation_status: '停用',
    trademark_image: '/assets/images/brands/xiaomi_tm.png'
  },
  {
    id: 'BR006',
    brand_name: '李宁',
    brand_logo: '/assets/images/brands/lining.png',
    brand_code: 'LINING006',
    trace_code_rule: 'LN-###-***',
    trademark_number: 'TM20250006',
    brand_location: '中国',
    registration_category: '第25类-服装鞋帽',
    registration_date: '2022-06-15',
    expiry_date: '2032-06-14',
    trademark_holder: '李宁体育用品有限公司',
    trademark_status: '有效',
    operation_status: '启用',
    trademark_image: '/assets/images/brands/lining_tm.png'
  },
  {
    id: 'BR007',
    brand_name: '安踏',
    brand_logo: '/assets/images/brands/anta.png',
    brand_code: 'ANTA007',
    trace_code_rule: 'AT-###-***',
    trademark_number: 'TM20250007',
    brand_location: '中国',
    registration_category: '第25类-服装鞋帽',
    registration_date: '2021-09-10',
    expiry_date: '2031-09-09',
    trademark_holder: '安踏体育用品有限公司',
    trademark_status: '有效',
    operation_status: '启用',
    trademark_image: '/assets/images/brands/anta_tm.png'
  },
  {
    id: 'BR008',
    brand_name: '海尔',
    brand_logo: '/assets/images/brands/haier.png',
    brand_code: 'HAIER008',
    trace_code_rule: 'HR-###-***',
    trademark_number: 'TM20250008',
    brand_location: '中国',
    registration_category: '第11类-家用电器',
    registration_date: '2020-04-25',
    expiry_date: '2030-04-24',
    trademark_holder: '海尔集团公司',
    trademark_status: '有效',
    operation_status: '启用',
    trademark_image: '/assets/images/brands/haier_tm.png'
  },
  {
    id: 'BR009',
    brand_name: '美的',
    brand_logo: '/assets/images/brands/midea.png',
    brand_code: 'MIDEA009',
    trace_code_rule: 'MD-###-***',
    trademark_number: 'TM20250009',
    brand_location: '中国',
    registration_category: '第11类-家用电器',
    registration_date: '2019-07-18',
    expiry_date: '2024-07-17',
    trademark_holder: '美的集团股份有限公司',
    trademark_status: '即将到期',
    operation_status: '启用',
    trademark_image: '/assets/images/brands/midea_tm.png'
  },
  {
    id: 'BR010',
    brand_name: '格力',
    brand_logo: '/assets/images/brands/gree.png',
    brand_code: 'GREE010',
    trace_code_rule: 'GR-###-***',
    trademark_number: 'TM20250010',
    brand_location: '中国',
    registration_category: '第11类-家用电器',
    registration_date: '2018-12-05',
    expiry_date: '2023-12-04',
    trademark_holder: '珠海格力电器股份有限公司',
    trademark_status: '即将到期',
    operation_status: '启用',
    trademark_image: '/assets/images/brands/gree_tm.png'
  }
]);

// 生成新的商标ID
const generateBrandId = () => {
  const lastId = brandData.value.length > 0 
    ? parseInt(brandData.value[brandData.value.length - 1].id.replace('BR', '')) 
    : 0;
  return `BR${String(lastId + 1).padStart(3, '0')}`;
};

// 模拟API函数 - 获取商标列表
const getBrandList = (params) => {
  return new Promise((resolve) => {
    // 使用内存数组
    let result = [...brandData.value];
    
    // 筛选处理
    if (params) {
      // 商标名称筛选
      if (params.brand_name) {
        result = result.filter(item => item.brand_name.includes(params.brand_name));
      }
      
      // 商标注册类别筛选
      if (params.registration_category) {
        result = result.filter(item => item.registration_category.includes(params.registration_category));
      }
      
      // 商标号筛选
      if (params.trademark_number) {
        result = result.filter(item => item.trademark_number.includes(params.trademark_number));
      }
      
      // 商标持有人筛选
      if (params.trademark_holder) {
        result = result.filter(item => item.trademark_holder.includes(params.trademark_holder));
      }
      
      // 经营状态筛选
      if (params.operation_status) {
        result = result.filter(item => item.operation_status === params.operation_status);
      }
    }
    
    // 返回结果
    setTimeout(() => {
      resolve({
        success: true,
        message: '获取商标列表成功',
        code: 200,
        data: {
          items: result,
          pageInfo: {
            total: result.length,
            currentPage: params?.currentPage || 1,
            totalPage: Math.ceil(result.length / (params?.pageSize || 10))
          }
        }
      });
    }, 300);
  });
};

// CRUD 配置
const crud = reactive({
  // API 配置
  api: getBrandList,
  searchLabelWidth: '115px',
  // 分页配置
  pagination: {
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
    total: 0,
    pagerCount: 5
  },
  // 表格配置
  table: {
    stripe: true,
    border: true,
    size: 'medium',
    showHeader: true,
    rowKey: 'id'
  },
  // 搜索表单配置
  search: {
    labelWidth: 100,
    defaultCollapsed: false,
    showResetButton: true,
    span: 6
  },
  // 操作栏配置
  options: {
    height: 'auto',
    add: true,
    addText: '新增商标',
    addIcon: 'icon-plus',
    export: false,
    viewBtn: false,
    editBtn: false,
    delBtn: false,
    excelBtn: false,
    menuBtn: false
  }
});

// 表格列配置
const columns = ref([
  {
    title: '商标名称',
    dataIndex: 'brand_name',
    search: true,
    width: 150,
    searchSpan: 6,
  },
  {
    title: '商标logo',
    dataIndex: 'brand_logo',
    width: 100,
    slotName: 'brand_logo'
  },
  {
    title: '品牌代码',
    dataIndex: 'brand_code',
    width: 120
  },
  {
    title: '溯源码规则',
    dataIndex: 'trace_code_rule',
    width: 150
  },
  {
    title: '商标号',
    dataIndex: 'trademark_number',
    search: true,
    width: 150,
    searchSpan: 6
  },
  {
    title: '品牌所属地',
    dataIndex: 'brand_location',
    width: 120
  },
  {
    title: '商标注册类别',
    dataIndex: 'registration_category',
    search: true,
    width: 150,
    searchSpan: 6
  },
  {
    title: '注册日期',
    dataIndex: 'registration_date',
    width: 120
  },
  {
    title: '有效期',
    dataIndex: 'expiry_date',
    width: 120
  },
  {
    title: '商标持有人',
    dataIndex: 'trademark_holder',
    search: true,
    width: 150,
    searchSpan: 6
  },
  {
    title: '商标状态',
    dataIndex: 'trademark_status',
    width: 100,
    slotName: 'trademark_status'
  },
  {
    title: '经营状态',
    dataIndex: 'operation_status',
    search: true,
    width: 100,
    slotName: 'operation_status',
    formType: 'select',
    dict: {
      data: [
        { label: '全部', value: '' },
        { label: '启用', value: '启用' },
        { label: '停用', value: '停用' }
      ]
    }
  },
  {
    title: '操作',
    dataIndex: 'operationBeforeExtend',
    width: 250,
    align: 'center',
    fixed: 'right'
  },
]);

// 打开详情抽屉
const openDetails = (record) => {
  Object.assign(detailRecord, record);
  detailVisible.value = true;
  isEditing.value = false;
  detailActiveTab.value = '1';
};


const goToEditPage = (record) => {
  const router = useRouter();
  router.push(`/master/cooperative/commodity/brands/edit?id=${record.id}`);
};
// 跳转到编辑页面
// const goToEditPage = (record) => {
//   router.push(`/master/cooperative/commodity/brands/edit?id=${record.id}`);
// };

// 关闭详情抽屉
const closeDetail = () => {
  detailVisible.value = false;
  isEditing.value = false;
  // 清空详情记录
  Object.keys(detailRecord).forEach(key => {
    delete detailRecord[key];
  });
};

// 保存编辑函数在下面已经定义

// 切换经营状态函数在下面已经定义

// 上传图片
const uploadImage = () => {
  // 实际项目中这里应该调用文件上传组件
  Message.info('上传图片功能待实现');
};


// 新增/编辑商标弹窗显示状态
const addVisible = ref(false);

// 是否为编辑模式
const isEditMode = ref(false);

// 新增/编辑商标表单数据
const newBrandForm = reactive({
  brand_name: '', // 必填
  brand_code: '', // 必填
  trace_code_rule: '',
  trademark_number: '', // 必填
  brand_location: '',
  category_main: '', // 类别主分类
  category_sub: '', // 类别子分类
  application_date: null, // 必填
  registration_date: null,
  preliminary_notice_date: null,
  expiry_date: null,
  applicant_name: '', // 必填
  applicant_address: '', // 必填
  trademark_holder: '',
  trademark_status: '有效',
  trademark_type: '文字商标',
  operation_status: '启用',
  agent_organization: '',
  brand_logo: '/assets/images/default-brand.png',
  goods_services: [], // 必填
  announcements: []
});


const goToAddPage = () => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(router, `/master/cooperative/commodity/brands/edit`);
  });
  // router.push('/master/cooperative/commodity/brands/edit');
};

// 关闭新增商标弹窗的函数已在下面定义

// 保存新增商标
const saveNewBrand = () => {
  // 验证必填字段
  const requiredFields = [
    { field: 'brand_name', message: '请输入商标名称' },
    { field: 'brand_code', message: '请输入品牌代码' },
    { field: 'trademark_number', message: '请输入注册号' },
    { field: 'application_date', message: '请选择申请日期' },
    { field: 'applicant_name', message: '请输入申请人名称' },
    { field: 'applicant_address', message: '请输入申请人地址' }
  ];
  
  // 检查必填字段
  for (const item of requiredFields) {
    if (!newBrandForm[item.field]) {
      Message.error(item.message);
      return;
    }
  }
  
  // 检查商品/服务列表
  if (newBrandForm.goods_services.length === 0) {
    Message.error('请添加至少一项商品/服务');
    return;
  }
  
  // 生成新ID
  const newId = generateBrandId();
  
  // 组合类别信息
  const category = newBrandForm.category_main && newBrandForm.category_sub 
    ? `第${newBrandForm.category_main}类-${newBrandForm.category_sub}` 
    : '';
  
  // 添加新商标
  const newBrand = {
    id: newId,
    brand_name: newBrandForm.brand_name,
    brand_logo: newBrandForm.brand_logo || '/assets/images/default-brand.png',
    brand_code: newBrandForm.brand_code,
    trace_code_rule: newBrandForm.trace_code_rule,
    trademark_number: newBrandForm.trademark_number,
    brand_location: newBrandForm.brand_location,
    registration_category: category,
    application_date: newBrandForm.application_date,
    registration_date: newBrandForm.registration_date,
    preliminary_notice_date: newBrandForm.preliminary_notice_date,
    expiry_date: newBrandForm.expiry_date,
    applicant_name: newBrandForm.applicant_name,
    applicant_address: newBrandForm.applicant_address,
    trademark_holder: newBrandForm.trademark_holder,
    trademark_status: newBrandForm.trademark_status,
    trademark_type: newBrandForm.trademark_type,
    operation_status: newBrandForm.operation_status,
    agent_organization: newBrandForm.agent_organization,
    goods_services: [...newBrandForm.goods_services],
    announcements: [...newBrandForm.announcements]
  };
  
  // 添加到数据源
  brandData.value.unshift(newBrand);
  
  // 关闭弹窗
  addVisible.value = false;
  
  // 刷新表格
  crudRef.value?.refresh();
  
  // 提示成功
  Message.success('新增商标成功');
};

// 打开新增商标弹窗
const openAddDrawer = () => {
  addVisible.value = true;
};

// 关闭新增/编辑商标弹窗
const closeAddDrawer = () => {
  addVisible.value = false;
  isEditMode.value = false; // 重置编辑模式
  // 重置表单
  Object.keys(newBrandForm).forEach(key => {
    if (key === 'brand_logo') {
      newBrandForm[key] = '/assets/images/default-brand.png';
    } else if (key === 'trademark_status') {
      newBrandForm[key] = '有效';
    } else if (key === 'operation_status') {
      newBrandForm[key] = '启用';
    } else {
      newBrandForm[key] = '';
    }
  });
};

// 保存编辑商标（弹窗模式）
const saveEditModal = () => {
  // 验证必填字段 (与新增相同的验证)
  const requiredFields = [
    { field: 'brand_name', message: '请输入商标名称' },
    { field: 'brand_code', message: '请输入品牌代码' },
    { field: 'trademark_number', message: '请输入注册号' },
    { field: 'application_date', message: '请选择申请日期' },
    { field: 'applicant_name', message: '请输入申请人名称' },
    { field: 'applicant_address', message: '请输入申请人地址' }
  ];

  for (const { field, message } of requiredFields) {
    if (!newBrandForm[field]) {
      Message.error(message);
      return;
    }
  }

  // 查找要编辑的商标在数据源中的索引
  const index = brandData.value.findIndex(item => item.trademark_number === newBrandForm.trademark_number);
  
  if (index !== -1) {
    // 更新数据源中的商标信息
    Object.keys(newBrandForm).forEach(key => {
      brandData.value[index][key] = newBrandForm[key];
    });
    
    // 关闭弹窗
    addVisible.value = false;
    isEditMode.value = false;
    
    // 刷新表格
    crudRef.value?.refresh();
    
    // 提示成功
    Message.success('编辑商标成功');
  } else {
    Message.error('未找到要编辑的商标信息');
  }
};

// 保存新增商标函数已在上面定义

// 保存编辑
const saveEdit = async () => {
  try {
    // 表单验证
    if (!detailRecord.brand_name) {
      Message.error('商标名称不能为空');
      return;
    }
    
    // 检查商标名称是否已存在（排除自身）
    const exists = brandData.value.some(item => 
      item.brand_name === detailRecord.brand_name && item.id !== detailRecord.id
    );
    if (exists) {
      Message.error('商标名称已存在');
      return;
    }
    
    // 在内存数组中更新数据
    const index = brandData.value.findIndex(item => item.id === detailRecord.id);
    if (index !== -1) {
      brandData.value[index] = { ...detailRecord };
      Message.success('保存成功');
      closeDetail();
      // 刷新表格数据
      crudRef.value.refresh();
    } else {
      Message.error('未找到要编辑的商标');
    }
  } catch (error) {
    Message.error('保存失败：' + error.message);
  }
};

// 切换经营状态
const toggleStatus = async (record) => {
  try {
    const newStatus = record.operation_status === '启用' ? '停用' : '启用';
    
    // 在内存数组中更新状态
    const index = brandData.value.findIndex(item => item.id === record.id);
    if (index !== -1) {
      brandData.value[index].operation_status = newStatus;
      Message.success(`${newStatus}成功`);
      // 刷新表格数据
      crudRef.value.refresh();
    } else {
      Message.error('未找到要操作的商标');
    }
  } catch (error) {
    Message.error(`操作失败：${error.message}`);
  }
};

// 删除商标
const deleteBrand = async (id) => {
  try {
    // 在内存数组中删除数据
    const index = brandData.value.findIndex(item => item.id === id);
    if (index !== -1) {
      brandData.value.splice(index, 1);
      Message.success('删除成功');
      // 刷新表格数据
      crudRef.value.refresh();
    } else {
      Message.error('未找到要删除的商标');
    }
  } catch (error) {
    Message.error('删除失败：' + error.message);
  }
};

// 商品/服务列表表格列定义
const goodsServiceColumns = ref([
  {
    title: '商品/服务名称',
    dataIndex: 'name',
    width: 200
  },
  {
    title: '类别',
    dataIndex: 'category',
    width: 150
  },
  {
    title: '描述',
    dataIndex: 'description',
    width: 300
  }
]);

// 商标公告表格列定义
const announcementColumns = ref([
  {
    title: '公告类型',
    dataIndex: 'type',
    width: 150
  },
  {
    title: '公告日期',
    dataIndex: 'date',
    width: 150
  },
  {
    title: '期号',
    dataIndex: 'issue_no',
    width: 150
  },
  {
    title: '页码',
    dataIndex: 'page_no',
    width: 100
  }
]);

// 添加商品/服务方法
const addGoodsService = () => {
  if (!detailRecord.goods_services) {
    detailRecord.goods_services = [];
  }
  
  // 生成新ID
  const newId = detailRecord.goods_services.length > 0 
    ? Math.max(...detailRecord.goods_services.map(item => item.id)) + 1 
    : 1;
  
  // 添加新商品/服务
  detailRecord.goods_services.push({
    id: newId,
    name: '',
    category: '',
    description: ''
  });
  
  Message.success('已添加新商品/服务项，请编辑内容');
};

// 页面初始化
onMounted(() => {
  // 修改CRUD的options配置，添加自定义的新增按钮点击事件
  crud.options.addClick = () => {
    openAddModal();
  };
  
  // 初始化加载数据
  crudRef.value?.refresh();
});
</script>

<script>
export default { name: "master-cooperative-commodity-brands" };
</script>

<style lang="less" scoped>
.brand-detail-container {
  padding: 0 16px;
}

.info-section, .qualification-section, .agreement-section, .business-data-section {
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-item .text-gray-500 {
  width: 120px;
  flex-shrink: 0;
}

.image-preview {
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 16px;
  display: inline-block;
}

.no-image {
  width: 300px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #999;
  border-radius: 4px;
}

/* 覆盖表格行高亮样式 */
:deep(.arco-table-tr) {
  cursor: default !important;
}

/* 图片上传相关样式 */
.upload-image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
}

.upload-skin {
  background-color: #f2f3f5;
  border: 1px dashed #c9cdd4;
  width: 130px;
  height: 130px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.upload-skin:hover {
  border-color: #165dff;
  background-color: #f7f8fa;
}

.upload-skin .icon {
  font-size: 24px;
  color: #86909c;
  margin-bottom: 8px;
}

.upload-skin .title {
  font-size: 14px;
  color: #86909c;
}

.upload-tip {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.image-list {
  position: relative;
  width: 130px;
  height: 130px;
  background-color: #f7f8fa;
  border: 1px solid #e5e6eb;
  border-radius: 2px;
  overflow: hidden;
}

.image-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #f53f3f;
  border: none;
}

.delete-btn:hover {
  background-color: rgba(255, 255, 255, 1);
}

:deep(.arco-table-tr:hover td) {
  background-color: var(--color-bg-2) !important;
}

:deep(.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr-selected td) {
  background-color: transparent !important;
}

:deep(.arco-table-tr:focus-within) {
  background-color: transparent !important;
  outline: none !important;
  border: none !important;
}

:deep(.arco-table-tr.arco-table-tr-selected) {
  background-color: transparent !important;
}

:deep(.arco-table-tr.arco-table-tr-selected td) {
  background-color: transparent !important;
}

:deep(.arco-table-tr.arco-table-tr-selected:hover td) {
  background-color: var(--color-bg-2) !important;
}

:deep(.arco-table-td) {
  border-right: none !important;
}
</style>