<template>
  <div class="ma-content-block p-4">
    <!-- 标签页 -->
    <a-tabs :active-key="activeTab" @change="handleTabChange" class="mb-4">
      <a-tab-pane key="" title="全部" />
      <a-tab-pane key="0" title="待审核" />
      <a-tab-pane key="1" title="已发布" />
    </a-tabs>
    
    <!-- CRUD组件 -->
    <ma-crud
      ref="crudRef"
      :options="crudOptions"
      :columns="columns"
      :expandable="expandableConfig"
    >
      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space>
          <a-link @click="handleEdit(record)">编辑</a-link>
          <!-- <a-link 
            @click="handleChangeStatus(record)" 
            :status="record.status === 1 ? 'warning' : 'success'"
          >
            {{ record.status === 1 ? '下架' : '上架' }}
          </a-link>
          <a-link status="danger" @click="handleDelete(record)">删除</a-link> -->
        </a-space>
      </template>
      
      <!-- 商品信息 -->
      <template #goodsInfo="{ record }">
        <div class="flex items-start">
          <a-avatar
            :size="60"
            shape="square"
            :image-url="record.mainImage"
            fit="contain"
            class="mr-3 flex-shrink-0"
          />
          <div class="flex flex-col overflow-hidden">
            <div class="font-medium text-ellipsis overflow-hidden whitespace-nowrap" style="max-width: 220px;">
              {{ record.name }}
            </div>
            <div class="text-gray-500 text-xs mt-1">{{ record.categoryName }}</div>
            <div class="text-gray-500 text-xs mt-1">{{ record.brandName }}</div>
            <div class="flex flex-wrap gap-1 mt-1">
              <template v-if="record.services">
                <a-tag 
                  v-for="(service, index) in record.services.split(', ')" 
                  :key="index" 
                  size="small" 
                  color="blue"
                >
                  {{ service }}
                </a-tag>
              </template>
            </div>
          </div>
        </div>
      </template>
      
      <!-- 状态 -->
      <template #status="{ record }">
        <div>{{ record.statusText }}</div>
      </template>
      
      <!-- 创建时间 -->
      <template #createdAt="{ record }">
        <div v-time="record.createdAt" v-if="record.createdAt"></div>
        <div v-else></div>
      </template>
      
      <!-- 自定义关联分类搜索 -->
      <template #search-categoryId="{ searchForm }">
        <a-cascader
          v-model="searchForm.categoryId"
          :options="categoryOptions"
          placeholder="请选择分类"
          allow-clear
        />
      </template>
      
      <!-- SKU 展开行 -->
      <template #expand-row="{ record }">
        <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded">
          <div class="max-w-6xl mx-auto">
            <a-table
              :data="record.skus"
              :pagination="false"
              size="small"
              :scroll="{ x: '100%' }"
            >
              <template #columns>
                <a-table-column
                  title="SKU编码"
                  data-index="skuCode"
                  :width="100"
                />
                <a-table-column
                  title="规格"
                  :width="150"
                >
                  <template #cell="{ record }">
                    <div class="flex flex-wrap gap-1">
                      <template v-if="record.specValues && Object.keys(record.specValues).length > 0">
                        <a-tag 
                          v-for="(value, key) in record.specValues" 
                          :key="key" 
                          size="small"
                        >
                          {{ key }}: {{ value }}
                        </a-tag>
                      </template>
                      <span v-else>默认规格</span>
                    </div>
                  </template>
                </a-table-column>
                <a-table-column
                  title="图片"
                  data-index="imageUrl"
                  :width="80"
                  align="center"
                >
                  <template #cell="{ record }">
                    <a-image
                      :src="record.imageUrl || '/placeholder.png'"
                      width="40"
                      height="40"
                      fit="cover"
                    />
                  </template>
                </a-table-column>
                <a-table-column
                  title="销售价"
                  data-index="salesPrice"
                  :width="100"
                  align="right"
                ></a-table-column>
                <a-table-column
                  title="市场价"
                  data-index="marketPrice"
                  :width="100"
                  align="right"
                ></a-table-column>
                <a-table-column
                  title="成本价"
                  data-index="costPrice"
                  :width="100"
                  align="right"
                ></a-table-column>
                <a-table-column
                  title="库存"
                  data-index="stock"
                  :width="80"
                  align="center"
                ></a-table-column>
                <a-table-column
                  title="单位"
                  data-index="unit"
                  :width="80"
                ></a-table-column>
                <a-table-column
                  title="状态"
                  :width="80"
                  align="center"
                >
                  <template #cell="{ record }">
                    <a-tag :color="record.status === 1 ? 'green' : 'red'">
                      {{ record.statusText }}
                    </a-tag>
                  </template>
                </a-table-column>
              </template>
            </a-table>
          </div>
        </div>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import { useRouter } from 'vue-router';

// 页面元数据
definePageMeta({
  name: "master-cooperative-commodity-commoditylist",
  path: "/master/cooperative/commodity/commoditylist"
});

const router = useRouter();
const crudRef = ref(null);

// 状态标签页
const activeTab = ref('');

// 处理标签页切换
const handleTabChange = (key) => {
  activeTab.value = key;
  // 重新加载数据
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 处理添加商品
const handleAdd = () => {
  router.push('/master/cooperative/commodity/add');
};

// 处理编辑商品
const handleEdit = (record) => {
  // 参考goodsList页面的实现方式
  import("@/utils/common").then((module) => {
    module.navigateWithTag(router, `/master/cooperative/commodity/add`);
  }).catch(error => {
    console.error('导入common模块失败:', error);
    // 如果导入失败，使用普通的路由跳转
    router.push('/master/cooperative/commodity/add');
  });
}

// 处理修改商品状态
const handleChangeStatus = (record) => {
  const newStatus = record.status === 1 ? 0 : 1;
  const statusText = newStatus === 1 ? '上架' : '下架';
  
  Modal.confirm({
    title: `确认${statusText}商品？`,
    content: `您确定要${statusText}商品「${record.name}」吗？`,
    onOk: async () => {
      try {
        // 模拟异步请求
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 这里应该是调用接口的代码
        Message.success(`商品已${statusText}`);
        
        // 刷新数据
        if (crudRef.value) {
          crudRef.value.refresh();
        }
      } catch (error) {
        console.error(`${statusText}商品失败`, error);
        Message.error(`${statusText}失败，请重试`);
      }
    }
  });
};

// 处理删除商品
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除商品？',
    content: `您确定要删除商品「${record.name}」吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        // 模拟异步请求
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 这里应该是调用接口的代码
        Message.success('商品已删除');
        
        // 刷新数据
        if (crudRef.value) {
          crudRef.value.refresh();
        }
      } catch (error) {
        console.error('删除商品失败', error);
        Message.error('删除失败，请重试');
      }
    }
  });
};

// 表格展开配置
const expandableConfig = reactive({
  // title: '展开',
  // width: 60,
});

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '已上架', value: '1' },
  { label: '已下架', value: '0' },
];

// 分类选项
const categoryOptions = ref([]);

// 品牌选项
const brandOptions = ref([]);

// 获取分类数据
const getCategoryList = async () => {
  try {
    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 这里应该是调用接口的代码
    categoryOptions.value = [
      {
        value: '1',
        label: '菜肉类',
        children: [
          {
            value: '1-1',
            label: '菜肉炒鸡',
          }
        ]
      },
      {
        value: '2',
        label: '水果类',
        children: [
          {
            value: '2-1',
            label: '苹果',
          },
          {
            value: '2-2',
            label: '香蕉',
          }
        ]
      }
    ];
  } catch (error) {
    console.error('获取分类数据失败', error);
  }
};

// 获取品牌数据
const getBrandList = async () => {
  try {
    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 这里应该是调用接口的代码
    brandOptions.value = [
      { value: '1', label: '苹果手机' },
      { value: '2', label: '菜肉手机' }
    ];
  } catch (error) {
    console.error('获取品牌数据失败', error);
  }
};

// 表格列定义
const columns = reactive([
  {
    title: 'ID',
    dataIndex: 'id',
    width: 70,
    align: 'center',
    fixed: 'left',
    show: false,
  },
  {
    title: '商品名称/编码',
    dataIndex: 'name',
    search: true,
    width: 320,
    slotName: 'name',
  },
  // 商品信息列，合并图片、名称、标签
  {
    title: '商品信息',
    dataIndex: 'goodsInfo',
    width: 320,
    slotName: 'goodsInfo',
  },
  {
    title: '关联分类',
    dataIndex: 'categoryId',
    search: true,
    width: 120,
    formType: 'cascader',
  },
  {
    title: '商品品牌',
    dataIndex: 'brandId',
    search: true,
    width: 120,
    formType: 'select',
    dict: {
      data: async () => {
        await getBrandList();
        return brandOptions.value;
      },
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 90,
    align: 'center',
    search: true,
    formType: 'select',
    dict: { data: statusOptions },
  },
  {
    title: '销量',
    dataIndex: 'sales',
    width: 90,
    align: 'center',
  },
  {
    title: '商品服务',
    dataIndex: 'services',
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 170,
    align: 'center',
    search: true,
    formType: 'range',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 150,
    align: 'center',
    fixed: 'right',
    // 添加插槽名称
    slotName: 'column-operation'
  },
]);

// 获取数据函数
const fetchData = async (params) => {
  try {
    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 这里应该是调用接口的代码
    // 使用您提供的数据结构
    const mockResponse = {
      "items": [
        {
          "id": "179527685519839232",
          "mainImage": "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/images/20250430/anonymous/1745976588735_5rmymiu3.png",
          "imageList": [
            "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/images/20250430/anonymous/1745976588735_5rmymiu3.png"
          ],
          "name": "试试",
          "categoryName": "菜肉炒鸡",
          "brandName": "苹果手机",
          "price": "¥1",
          "status": 1,
          "statusText": "已上架",
          "sales": 222,
          "services": "菜肉按摩服务",
          "createdAt": "1746869935691",
          "skus": [
            {
              "id": "179527685721165824",
              "skuCode": "555code",
              "skuName": "试试",
              "specValues": {},
              "specs": [],
              "imageUrl": "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/images/20250430/anonymous/1745976588735_5rmymiu3.png",
              "salesPrice": "1",
              "marketPrice": "2",
              "costPrice": "3",
              "stock": 4,
              "salesVolume": 0,
              "unit": "",
              "weight": null,
              "volume": null,
              "barcode": null,
              "status": 1,
              "statusText": "启用"
            }
          ]
        },
        {
          "id": "179419705617027072",
          "mainImage": "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/images/20250430/anonymous/1745976588735_5rmymiu3.png",
          "imageList": [
            "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/images/20250430/anonymous/1745976588735_5rmymiu3.png"
          ],
          "name": "奇奇怪怪",
          "categoryName": "菜肉炒鸡",
          "brandName": "苹果手机",
          "price": "¥1 ~ ¥11",
          "status": 1,
          "statusText": "已上架",
          "sales": 0,
          "services": "1, 七天无理由服务",
          "createdAt": "1746844191276",
          "skus": [
            {
              "id": "179517784152215552",
              "skuCode": "7hh",
              "skuName": "奇奇怪怪 红色",
              "specValues": {
                "颜色": "红色"
              },
              "specs": [
                {
                  "specNameId": "3",
                  "specName": "颜色",
                  "specValueId": "8",
                  "specValue": "红色",
                  "specificationValueId": "8"
                }
              ],
              "imageUrl": "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/images/20250430/anonymous/1745976588735_5rmymiu3.png",
              "salesPrice": "1",
              "marketPrice": "2",
              "costPrice": "5",
              "stock": 6,
              "salesVolume": 0,
              "unit": "个",
              "weight": "3",
              "volume": "4",
              "barcode": null,
              "status": 1,
              "statusText": "启用"
            },
            {
              "id": "179517784206741504",
              "skuCode": "17oo",
              "skuName": "奇奇怪怪 黑色",
              "specValues": {
                "颜色": "黑色"
              },
              "specs": [
                {
                  "specNameId": "3",
                  "specName": "颜色",
                  "specValueId": "7",
                  "specValue": "黑色",
                  "specificationValueId": "7"
                }
              ],
              "imageUrl": "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/images/20250430/anonymous/1745976588735_5rmymiu3.png",
              "salesPrice": "11",
              "marketPrice": "12",
              "costPrice": "15",
              "stock": 16,
              "salesVolume": 0,
              "unit": "台",
              "weight": "13",
              "volume": "14",
              "barcode": null,
              "status": 1,
              "statusText": "启用"
            }
          ]
        }
      ],
      "pageInfo": {
        "total": 16,
        "currentPage": params.page || 1,
        "totalPage": 2
      }
    };
    
    // 返回数据，这对于 ma-crud 组件是必要的
    return {
      code: 0,
      msg: '成功',
      data: mockResponse
    };
  } catch (error) {
    console.error('获取商品列表失败', error);
    return {
      code: 500,
      msg: '获取数据失败',
      data: {
        items: [],
        pageInfo: {
          total: 0,
          currentPage: 1,
          totalPage: 1
        }
      }
    };
  }
};

// CRUD选项配置
const crudOptions = reactive({
  // API配置 - 直接使用函数
  api: fetchData,
  searchLabelWidth:"130px",
  // 添加按钮配置
  add: {
    show: true,
    text: '添加商品',
    action: handleAdd,
  },
  // 搜索配置
  searchColNumber: 3,
  expandRowByClick: true,
  showExpandRow: true,
  showIndex: false,
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有创建时间参数，转换为毫秒级时间戳
    if (params.createdAt && params.createdAt.length > 0) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.createdAt[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startTime = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.createdAt[1]);
      endDate.setHours(23, 59, 59, 0);
      params.endTime = endDate.getTime();

      delete params.createdAt;
    } else {
      delete params.startTime;
      delete params.endTime;
    }
    
    // 如果有标签页状态，添加到参数中
    if (activeTab.value) {
      params.status = activeTab.value;
    }
    
    return params;
  },
});

// 初始化数据
onMounted(async () => {
  // 获取分类数据
  await getCategoryList();
  
  // 获取品牌数据
  await getBrandList();
});
</script>

<style lang="less" scoped>
/* 可以添加自定义样式 */
</style>
