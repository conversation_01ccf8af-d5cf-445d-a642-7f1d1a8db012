<template>
  <div class="ma-content-block p-4">
    <a-card class="mb-4">
     
      <template #title>
      
        <div class="text-lg font-semibold">{{ pageTitle }}</div>
      </template>

      <!-- 步骤导航 -->
      <a-steps :current="currentStep + 1" class="mb-6">
        <a-step title="基本信息" />
        <a-step title="属性信息" />
        <a-step title="规格/SKU" />
        <a-step title="商品详情" />
      </a-steps>

      <!-- 步骤内容区域 -->
      <div class="step-content">
        <!-- 步骤1：基本信息 -->
        <div v-show="currentStep === 0">
          <a-card class="mb-4">
            <a-form :model="basicForm" ref="basicFormRef" layout="vertical">
              <div class="grid grid-cols-1 gap-4">
                <a-form-item field="name" label="商品名称" :rules="[{ required: true, message: '请输入商品名称' }]">
                  <a-input v-model="basicForm.name" placeholder="请输入商品名称" />
                </a-form-item>
                <a-form-item field="categoryId" label="商品分类" :rules="[{ required: true, message: '请选择商品分类' }]">
                  <a-cascader
                    v-model="basicForm.categoryId"
                    :options="categoryOptions"
                    placeholder="请选择分类"
                    allow-clear
                  />
                </a-form-item>
                <a-form-item field="brandId" label="商品品牌" :rules="[{ required: true, message: '请选择商品品牌' }]">
                  <a-select
                    v-model="basicForm.brandId"
                    :options="brandOptions"
                    placeholder="请选择品牌"
                    allow-clear
                  />
                </a-form-item>
                <a-form-item field="unit" label="计量单位" :rules="[{ required: true, message: '请输入计量单位' }]">
                  <a-input v-model="basicForm.unit" placeholder="如：个、件、套、把" />
                </a-form-item>
              </div>
            </a-form>
          </a-card>
          
          <a-card class="mb-4" title="商品图片">
            <a-form-item field="spuImages" :rules="[{ required: false, message: '请上传至少一张商品图片' }]">
              <ma-upload
                v-model="basicForm.spuImages"
                upload-type="image"
                :multiple="true"
                :limit="5"
                :accept="'.jpg,.jpeg,.png,.gif'"
                tip="最多上传5张图片，建议尺寸800x800px，大小不超过2MB"
                return-type="url"
              />
            </a-form-item>
          </a-card>
          
          <a-card class="mb-4" title="商品标签">
            <a-checkbox-group v-model="basicForm.tags" class="w-full flex flex-wrap gap-4">
              <a-checkbox v-for="tag in tagOptions" :key="tag.value" :value="tag.value">{{ tag.label }}</a-checkbox>
            </a-checkbox-group>
            <a-button type="text" class="mt-4">
              <template #icon><icon-plus /></template>
              添加标签
            </a-button>
          </a-card>
          
          <a-card class="mb-4" title="商品服务">
            <a-checkbox-group v-model="basicForm.services" class="w-full flex flex-wrap gap-4">
              <a-checkbox value="免费安装">免费安装</a-checkbox>
              <a-checkbox value="7天无理由退换">7天无理由退换</a-checkbox>
              <a-checkbox value="上门服务">上门服务</a-checkbox>
              <a-checkbox value="技术支持">技术支持</a-checkbox>
            </a-checkbox-group>
            <a-button type="text" class="mt-4">
              <template #icon><icon-plus /></template>
              添加服务
            </a-button>
          </a-card>
          
          <a-card class="mb-4" title="物流设置">
            <a-radio-group v-model="basicForm.isFreeShipping" class="mb-4">
              <a-radio :value="1">包邮</a-radio>
              <a-radio :value="0">不包邮</a-radio>
            </a-radio-group>
          </a-card>
          
          <a-card class="mb-4" title="配送信息">
            <a-form :model="basicForm" layout="vertical">
              <a-form-item field="deliveryArea" label="配送区域">
                <a-select placeholder="请选择配送区域" allow-clear>
                  <a-option value="全国">全国</a-option>
                  <a-option value="北京">北京</a-option>
                  <a-option value="上海">上海</a-option>
                </a-select>
              </a-form-item>
              <a-form-item field="deliveryTime" label="发货时间">
                <a-select placeholder="请选择发货时间" allow-clear>
                  <a-option value="1">付款后24小时内发货</a-option>
                  <a-option value="2">付款后48小时内发货</a-option>
                  <a-option value="3">付款后72小时内发货</a-option>
                </a-select>
              </a-form-item>
            </a-form>
          </a-card>
        </div>

        <!-- 步骤2：属性信息 -->
        <div v-show="currentStep === 1">
          <a-form :model="attributeForm" ref="attributeFormRef" layout="vertical">
            <div class="grid grid-cols-2 gap-4">
              <a-form-item v-for="(attr, index) in attributeList" :key="index" :field="`attr_${index}`" :label="attr.name">
                <a-input v-model="attributeForm[attr.code]" :placeholder="`请输入${attr.name}`" />
              </a-form-item>
            </div>
          </a-form>
        </div>

        <!-- 步骤3：规格/SKU -->
        <div v-show="currentStep === 2">
          <div class="mb-4">
            <div class="font-medium mb-2">添加规格项</div>
            <a-space>
              <a-input v-model="newSpecName" placeholder="规格名称，如颜色、尺寸" />
              <a-input v-model="newSpecValue" placeholder="规格值，多个值用逗号分隔" />
              <a-button type="primary" @click="addSpec">添加规格</a-button>
            </a-space>
          </div>

          <div v-if="specList.length > 0" class="mb-4">
            <div class="font-medium mb-2">已添加规格</div>
            <a-space wrap>
              <a-tag 
                v-for="(spec, index) in specList" 
                :key="index"
                closable
                @close="removeSpec(index)"
              >
                {{ spec.name }}：{{ spec.values.join('、') }}
              </a-tag>
            </a-space>
          </div>

          <div v-if="skuList.length > 0" class="mb-4">
            <div class="font-medium mb-2">SKU列表</div>
            <a-table :data="skuList" :bordered="true">
              <template #columns>
                <a-table-column title="规格" data-index="specText" :width="200" />
                <a-table-column title="图片" :width="100">
                  <template #cell="{ record }">
                    <a-upload
                      list-type="picture-card"
                      :file-list="record.imageList"
                      :custom-request="(options) => handleUploadSkuImage(options, record)"
                      :limit="1"
                    >
                      <template #upload-button>
                        <div>
                          <i-icon-plus />
                        </div>
                      </template>
                    </a-upload>
                  </template>
                </a-table-column>
                <a-table-column title="销售价" :width="120">
                  <template #cell="{ record }">
                    <a-input-number v-model="record.salesPrice" :min="0" :precision="2" />
                  </template>
                </a-table-column>
                <a-table-column title="市场价" :width="120">
                  <template #cell="{ record }">
                    <a-input-number v-model="record.marketPrice" :min="0" :precision="2" />
                  </template>
                </a-table-column>
                <a-table-column title="成本价" :width="120">
                  <template #cell="{ record }">
                    <a-input-number v-model="record.costPrice" :min="0" :precision="2" />
                  </template>
                </a-table-column>
                <a-table-column title="库存" :width="100">
                  <template #cell="{ record }">
                    <a-input-number v-model="record.stock" :min="0" :precision="0" />
                  </template>
                </a-table-column>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 步骤4：商品详情 -->
        <div v-show="currentStep === 3">
          <a-form :model="detailForm" ref="detailFormRef" layout="vertical">
            <a-form-item field="content" label="商品详情" :rules="[{ required: true, message: '请输入商品详情' }]">
              <a-textarea
                v-model="detailForm.content"
                :auto-size="{ minRows: 10, maxRows: 20 }"
                placeholder="请输入商品详情，支持富文本编辑"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 步骤按钮 -->
      <div class="flex justify-between mt-4">
        <a-button @click="prevStep" :disabled="currentStep === 0">上一步</a-button>
        <div>
          <a-button type="outline" @click="saveAsDraft" class="mr-2">保存草稿</a-button>
          <a-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</a-button>
          <a-button v-else type="primary" @click="submitForm">提交</a-button>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import MaUpload from '~/components/base/ma-upload/index.vue';
import { useRouter, useRoute } from 'vue-router';

// 页面元数据
definePageMeta({
  name: "master-cooperative-commodity-add",
  path: "/master/cooperative/commodity/add"
});

const router = useRouter();
const route = useRoute();

// 判断是新增还是编辑模式
const isEditMode = computed(() => !!route.query.id);
const productId = computed(() => route.query.id);

// 页面标题
const pageTitle = computed(() => isEditMode.value ? '编辑商品' : '发布商品');

// 当前步骤
const currentStep = ref(0);

// 基本信息表单
const basicFormRef = ref(null);
const basicForm = reactive({
  name: '',
  categoryId: null,
  brandId: null,
  unit: '',
  spuImages: [],
  tags: [],
  services: []
});

// 上传主图
const mainImageList = ref([]);
const handleUploadMainImage = (options) => {
  // 模拟上传
  setTimeout(() => {
    const url = 'https://example.com/upload/image.jpg';
    const newImage = {
      uid: Date.now().toString(),
      name: (options.file && options.file.name) ? options.file.name : `image_${mainImageList.value.length + 1}.jpg`,
      url: url
    };
    
    // 添加新图片到列表
    mainImageList.value.push(newImage);
    
    if (options.onSuccess) {
      options.onSuccess(url);
    }
  }, 1000);
};

// 删除图片
const removeImage = (index) => {
  mainImageList.value.splice(index, 1);
};

// 上传前验证
const beforeUpload = (file) => {
  // 验证文件类型
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    Message.error('只能上传图片文件！');
    return false;
  }
  
  // 验证文件大小
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    Message.error('图片大小不能超过 2MB！');
    return false;
  }
  
  return true;
};

// 属性信息表单
const attributeFormRef = ref(null);
const attributeForm = reactive({});
const attributeList = ref([]);

// 规格/SKU表单
const newSpecName = ref('');
const newSpecValue = ref('');
const specList = ref([]);
const skuList = ref([]);

// 添加规格
const addSpec = () => {
  if (!newSpecName.value) {
    Message.error('请输入规格名称');
    return;
  }
  if (!newSpecValue.value) {
    Message.error('请输入规格值');
    return;
  }
  
  // 添加规格
  const values = newSpecValue.value.split(',').map(v => v.trim()).filter(v => v);
  specList.value.push({
    name: newSpecName.value,
    values: values
  });
  
  // 生成SKU列表
  generateSkuList();
  
  // 清空输入
  newSpecName.value = '';
  newSpecValue.value = '';
};

// 移除规格
const removeSpec = (index) => {
  specList.value.splice(index, 1);
  generateSkuList();
};

// 生成SKU列表
const generateSkuList = () => {
  if (specList.value.length === 0) {
    skuList.value = [];
    return;
  }
  
  // 生成笛卡尔积
  const cartesian = (arr) => {
    return arr.reduce((a, b) => {
      return a.flatMap(x => b.map(y => [...x, y]));
    }, [[]]);
  };
  
  const specs = specList.value.map(spec => spec.values);
  const combinations = cartesian(specs);
  
  // 生成SKU列表
  skuList.value = combinations.map((combination, index) => {
    // 生成规格文本
    const specText = combination.map((value, i) => {
      return `${specList.value[i].name}:${value}`;
    }).join(', ');
    
    // 生成SKU名称
    const skuName = `${basicForm.name} ${combination.join(' ')}`;
    
    return {
      id: `sku_${index}`,
      specValues: combination,
      specText: specText,
      skuName: skuName,
      imageUrl: '',
      imageList: [],
      salesPrice: 0,
      marketPrice: 0,
      costPrice: 0,
      stock: 0
    };
  });
};

// 上传SKU图片
const handleUploadSkuImage = (options, record) => {
  // 模拟上传
  setTimeout(() => {
    const url = 'https://example.com/upload/sku.jpg';
    record.imageUrl = url;
    record.imageList = [
      {
        uid: '1',
        name: 'sku.jpg',
        url: url
      }
    ];
    options.onSuccess(url);
  }, 1000);
};

// 商品详情表单
const detailFormRef = ref(null);
const detailForm = reactive({
  content: ''
});

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 下一步
const nextStep = async () => {
  if (currentStep.value === 0) {
    // 验证基本信息表单
    if (basicFormRef.value) {
      try {
        const result = await basicFormRef.value.validate();
        if (result === false || (result && !result.valid)) {
          return;
        }
      } catch (error) {
        console.error('表单验证错误:', error);
        Message.error('表单验证失败，请检查必填项');
        return;
      }
    }
  } else if (currentStep.value === 1) {
    // 验证属性信息表单
    if (attributeFormRef.value) {
      try {
        const result = await attributeFormRef.value.validate();
        if (result === false || (result && !result.valid)) {
          return;
        }
      } catch (error) {
        console.error('表单验证错误:', error);
        Message.error('表单验证失败，请检查必填项');
        return;
      }
    }
  } else if (currentStep.value === 2) {
    // 验证SKU信息
    if (skuList.value.length === 0) {
      Message.error('请添加至少一个规格');
      return;
    }
    
    // 验证SKU价格和库存
    const invalidSku = skuList.value.find(sku => !sku.salesPrice || sku.salesPrice <= 0);
    if (invalidSku) {
      Message.error('请为所有SKU设置有效的销售价');
      return;
    }
  }
  
  if (currentStep.value < 3) {
    currentStep.value++;
  }
};

// 保存草稿
const saveAsDraft = async () => {
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Message.success('已保存为草稿');
  } catch (error) {
    console.error('保存草稿失败:', error);
    Message.error('保存失败，请重试');
  }
};

// 提交表单
const submitForm = async () => {
  try {
    // 验证商品详情表单
    if (detailFormRef.value) {
      try {
        const result = await detailFormRef.value.validate();
        if (result === false || (result && !result.valid)) {
          Message.error('表单验证失败，请检查必填项');
          return;
        }
      } catch (error) {
        console.error('表单验证错误:', error);
        Message.error('表单验证失败，请检查必填项');
        return;
      }
    }
    
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Message.success('商品发布成功，等待审核');
    
    // 跳转到列表页
    router.push('/master/cooperative/commodity');
  } catch (error) {
    console.error('提交表单失败:', error);
    Message.error('提交失败，请重试');
  }
};

// 分类选项
const categoryOptions = ref([]);

// 品牌选项
const brandOptions = ref([]);

// 标签选项
const tagOptions = ref([]);

// 获取商品详情
const fetchProductDetail = async (id) => {
  try {
    console.log('开始获取商品详情，ID:', id);
    
    // 模拟异步请求
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 使用硬编码的模拟数据，不再调用getMockProductById函数
    const mockProductData = {
      id: 1,
      name: '有机山地苹果',
      categoryId: '1-1',
      brandId: '1',
      unit: '件',
      images: [
        { url: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp' },
        { url: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/e278888093bef8910e829486fb45dd69.png~tplv-uwbnlip3yd-webp.webp' }
      ],
      tags: [
        { id: 1, name: '有机' },
        { id: 2, name: '新鲜' },
        { id: 3, name: '热销' }
      ],
      services: [
        { id: 1, name: '包邮' },
        { id: 2, name: '7天无理由退换' }
      ],
      isFreeShipping: 1,
      attributes: {
        material: '天然有机',
        color: '红色',
        size: '中等',
        origin: '陕西'
      },
      skus: [
        {
          id: 101,
          skuName: '小号 500g',
          imageUrl: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
          salesPrice: 15.8,
          marketPrice: 19.8,
          costPrice: 10.5,
          stock: 200,
          specs: {
            '规格': '小号',
            '重量': '500g'
          }
        },
        {
          id: 102,
          skuName: '中号 1kg',
          imageUrl: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/e278888093bef8910e829486fb45dd69.png~tplv-uwbnlip3yd-webp.webp',
          salesPrice: 25.8,
          marketPrice: 29.8,
          costPrice: 18.5,
          stock: 300,
          specs: {
            '规格': '中号',
            '重量': '1kg'
          }
        }
      ],
      description: '这是一款有机山地苹果，采用纯天然种植方式，无任何化学添加剂，口感美味。',
      seoTitle: '有机山地苹果 - 纯天然无添加',
      seoKeywords: '有机苹果,山地苹果,无添加苹果',
      seoDescription: '采用纯天然种植方式，无任何化学添加剂，口感美味。'
    };
    
    console.log('使用硬编码的模拟商品数据:', mockProductData);
    
    // 始终返回有效数据
    if (true) {
      // 填充基本信息表单
      basicForm.name = mockProductData.name;
      basicForm.categoryId = mockProductData.categoryId;
      basicForm.brandId = mockProductData.brandId;
      basicForm.unit = mockProductData.unit || '件';
      basicForm.spuImages = mockProductData.images ? mockProductData.images.map(img => img.url) : [];
      basicForm.tags = mockProductData.tags ? mockProductData.tags.map(tag => tag.id.toString()) : [];
      basicForm.services = mockProductData.services ? mockProductData.services.map(service => service.name) : [];
      basicForm.isFreeShipping = mockProductData.isFreeShipping || 0;
      
      // 填充属性信息
      if (mockProductData.attributes) {
        Object.keys(mockProductData.attributes).forEach(key => {
          attributeForm[key] = mockProductData.attributes[key];
        });
      }
      
      // 填充规格/SKU信息
      if (mockProductData.skus && mockProductData.skus.length > 0) {
        // 提取规格信息
        const specs = {};
        mockProductData.skus.forEach(sku => {
          if (sku.specs) {
            Object.keys(sku.specs).forEach(key => {
              if (!specs[key]) {
                specs[key] = new Set();
              }
              specs[key].add(sku.specs[key]);
            });
          }
        });
        
        // 转换为规格列表
        specList.value = Object.keys(specs).map(key => ({
          name: key,
          values: Array.from(specs[key])
        }));
        
        // 生成SKU列表
        generateSkuList();
        
        // 填充SKU数据
        skuList.value.forEach(sku => {
          const matchedSku = mockProductData.skus.find(s => {
            // 比较规格是否匹配
            if (!s.specs) return false;
            return Object.keys(s.specs).every(key => {
              const specIndex = sku.specs.findIndex(spec => spec.name === key);
              return specIndex !== -1 && sku.specs[specIndex].value === s.specs[key];
            });
          });
          
          if (matchedSku) {
            sku.image = matchedSku.imageUrl || '';
            sku.salesPrice = matchedSku.salesPrice || 0;
            sku.marketPrice = matchedSku.marketPrice || 0;
            sku.costPrice = matchedSku.costPrice || 0;
            sku.stock = matchedSku.stock || 0;
          }
        });
      }
      
      // 填充商品详情
      detailForm.description = mockProductData.description || '';
      detailForm.seoTitle = mockProductData.seoTitle || '';
      detailForm.seoKeywords = mockProductData.seoKeywords || '';
      detailForm.seoDescription = mockProductData.seoDescription || '';
      
      Message.success('商品信息加载成功');
    } else {
      Message.error('未找到商品信息');
      router.push('/master/cooperative/commodity');
    }
  } catch (error) {
    console.error('获取商品详情失败', error);
    // Message.error('获取商品详情失败');
  }
};

// 根据ID获取模拟商品数据
const getMockProductById = (id) => {
  console.log('开始获取模拟商品数据，ID:', id);
  
  // 模拟商品数据
  const mockProducts = [
    {
      id: 1,
      name: '有机山地苹果',
      categoryId: '1-1',
      brandId: '1',
      unit: '件',
      images: [
        { url: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp' },
        { url: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/e278888093bef8910e829486fb45dd69.png~tplv-uwbnlip3yd-webp.webp' }
      ],
      tags: [
        { id: 1, name: '有机' },
        { id: 2, name: '新鲜' },
        { id: 3, name: '热销' }
      ],
      services: [
        { id: 1, name: '包邮' },
        { id: 2, name: '7天无理由退换' }
      ],
      isFreeShipping: 1,
      attributes: {
        material: '天然有机',
        color: '红色',
        size: '中等',
        origin: '陕西'
      },
      skus: [
        {
          id: 101,
          skuName: '小号 500g',
          imageUrl: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/a8c8cdb109cb051163646151a4a5083b.png~tplv-uwbnlip3yd-webp.webp',
          salesPrice: 15.8,
          marketPrice: 19.8,
          costPrice: 10.5,
          stock: 200,
          specs: {
            '规格': '小号',
            '重量': '500g'
          }
        },
        {
          id: 102,
          skuName: '中号 1kg',
          imageUrl: 'https://p1-arco.byteimg.com/tos-cn-i-uwbnlip3yd/e278888093bef8910e829486fb45dd69.png~tplv-uwbnlip3yd-webp.webp',
          salesPrice: 25.8,
          marketPrice: 29.8,
          costPrice: 18.5,
          stock: 300,
          specs: {
            '规格': '中号',
            '重量': '1kg'
          }
        }
      ],
      description: '这是一款有机山地苹果，采用纯天然种植方式，无任何化学添加剂，口感美味。',
      seoTitle: '有机山地苹果 - 纯天然无添加',
      seoKeywords: '有机苹果,山地苹果,无添加苹果',
      seoDescription: '采用纯天然种植方式，无任何化学添加剂，口感美味。'
    }
  ];
  
  // 无论传入什么ID，始终返回第一个商品
  console.log('返回的模拟商品数据:', mockProducts[0]);
  return mockProducts[0];
};

// 初始化数据
onMounted(() => {
  // 始终加载模拟商品数据，不依赖URL参数
  fetchProductDetail(1);
  
  // 模拟获取分类数据
  categoryOptions.value = [
    {
      value: '1',
      label: '办公家具',
      children: [
        {
          value: '1-1',
          label: '办公桌椅',
        },
        {
          value: '1-2',
          label: '文件柜',
        },
        {
          value: '1-3',
          label: '会议桌',
        }
      ]
    },
    {
      value: '2',
      label: '办公设备',
      children: [
        {
          value: '2-1',
          label: '电脑配件',
        },
        {
          value: '2-2',
          label: '投影设备',
        },
        {
          value: '2-3',
          label: '打印设备',
        }
      ]
    }
  ];
  
  // 模拟获取品牌数据
  brandOptions.value = [
    { value: '1', label: '优派家具' },
    { value: '2', label: '智慧办公' },
    { value: '3', label: '科技办公' }
  ];
  
  // 模拟获取标签数据
  tagOptions.value = [
    { value: '1', label: '办公家具' },
    { value: '2', label: '人体工学' },
    { value: '3', label: '会议家具' },
    { value: '4', label: '智能家具' },
    { value: '5', label: '办公设备' },
    { value: '6', label: '会议设备' }
  ];
  
  // 模拟获取属性列表
  attributeList.value = [
    { name: '材质', code: 'material' },
    { name: '颜色', code: 'color' },
    { name: '尺寸', code: 'size' },
    { name: '承重', code: 'weight_capacity' },
    { name: '产地', code: 'origin' },
    { name: '保修期', code: 'warranty' }
  ];
});
</script>

<style lang="less" scoped>
.step-content {
  min-height: 400px;
}

.upload-button-container {
  width: 100px;
  height: 100px;
}

.upload-button {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #f5f7fa;
  
  &:hover {
    border-color: #165dff;
    color: #165dff;
  }
}

.image-preview-container {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 4px;
  overflow: hidden;
  
  &:hover {
    .delete-icon {
      display: flex;
    }
  }
  
  .delete-icon {
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    color: #fff;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
</style>
