<template>
  <div class="ma-content-block p-4">
    <a-card class="mb-4">
      <template #title>
        <div class="flex justify-between items-center">
          <div class="text-lg font-semibold">商品详情</div>
          <a-button type="primary" @click="goBack">返回列表</a-button>
        </div>
      </template>

      <a-spin :loading="loading">
        <!-- 商品基本信息 -->
        <div class="mb-6">
          <div class="flex items-start">
            <!-- 商品主图 -->
            <div class="mr-6">
              <a-image
                :src="productDetail.mainImage || '/placeholder.png'"
                width="200"
                height="200"
                fit="cover"
                class="rounded border border-gray-200"
              />
            </div>
            
            <!-- 基本信息 -->
            <div class="flex-1">
              <h2 class="text-xl font-semibold mb-3">{{ productDetail.name }}</h2>
              
              <div class="grid grid-cols-2 gap-4">
                <div class="flex items-center">
                  <span class="text-gray-500 w-20">商品分类：</span>
                  <span>{{ productDetail.categoryName }}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-gray-500 w-20">商品品牌：</span>
                  <span>{{ productDetail.brandName }}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-gray-500 w-20">计量单位：</span>
                  <span>{{ productDetail.unit }}</span>
                </div>
                <div class="flex items-center">
                  <span class="text-gray-500 w-20">商品状态：</span>
                  <a-tag :color="productDetail.status === 1 ? 'green' : productDetail.status === 0 ? 'orange' : 'red'">
                    {{ productDetail.status === 1 ? '已发布' : productDetail.status === 0 ? '待审核' : '已下架' }}
                  </a-tag>
                </div>
                <div class="flex items-center">
                  <span class="text-gray-500 w-20">创建时间：</span>
                  <span v-time="productDetail.createdAt"></span>
                </div>
                <div class="flex items-center">
                  <span class="text-gray-500 w-20">更新时间：</span>
                  <span v-time="productDetail.updatedAt"></span>
                </div>
              </div>
              
              <!-- 标签 -->
              <div class="mt-4 flex items-center">
                <span class="text-gray-500 w-20">商品标签：</span>
                <div class="flex-1 flex flex-wrap">
                  <a-tag
                    v-for="tag in productDetail.tags"
                    :key="tag.id"
                    class="mr-2 mb-2"
                  >{{ tag.name }}</a-tag>
                </div>
              </div>
              
              <!-- 服务 -->
              <div class="mt-4 flex items-center">
                <span class="text-gray-500 w-20">商品服务：</span>
                <div class="flex-1 flex flex-wrap">
                  <a-tag
                    v-for="service in productDetail.services"
                    :key="service"
                    color="arcoblue"
                    class="mr-2 mb-2"
                  >{{ service }}</a-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 商品属性 -->
        <a-divider>商品属性</a-divider>
        <div class="mb-6">
          <a-descriptions :column="3" size="large" bordered>
            <a-descriptions-item 
              v-for="(value, key) in productDetail.attributes" 
              :key="key" 
              :label="getAttributeLabel(key)"
            >
              {{ value }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
        
        <!-- 商品规格 -->
        <a-divider>商品规格</a-divider>
        <div class="mb-6">
          <a-table
            :data="productDetail.skus"
            :pagination="false"
            :bordered="true"
            :scroll="{ x: '100%' }"
          >
            <template #columns>
              <a-table-column title="规格" data-index="specText" :width="200" />
              <a-table-column title="图片" :width="100">
                <template #cell="{ record }">
                  <a-image
                    :src="record.imageUrl || '/placeholder.png'"
                    width="50"
                    height="50"
                    fit="cover"
                  />
                </template>
              </a-table-column>
              <a-table-column title="销售价" data-index="salesPrice" :width="120" align="right">
                <template #cell="{ record }">
                  <span class="text-red-500">¥{{ record.salesPrice?.toFixed(2) }}</span>
                </template>
              </a-table-column>
              <a-table-column title="市场价" data-index="marketPrice" :width="120" align="right">
                <template #cell="{ record }">
                  <span class="text-gray-500">¥{{ record.marketPrice?.toFixed(2) }}</span>
                </template>
              </a-table-column>
              <a-table-column title="成本价" data-index="costPrice" :width="120" align="right">
                <template #cell="{ record }">
                  <span>¥{{ record.costPrice?.toFixed(2) }}</span>
                </template>
              </a-table-column>
              <a-table-column title="库存" data-index="stock" :width="100" align="center" />
              <a-table-column title="销量" data-index="sales" :width="100" align="center" />
              <a-table-column title="单位" data-index="unit" :width="80" />
            </template>
          </a-table>
        </div>
        
        <!-- 商品详情 -->
        <a-divider>商品详情</a-divider>
        <div class="product-detail-content">
          <div v-html="productDetail.detail?.content || '暂无详情'"></div>
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRoute, useRouter } from 'vue-router';

// 页面元数据
definePageMeta({
  name: "master-cooperative-commodity-view",
  path: "/master/cooperative/commodity/view/:id"
});

const route = useRoute();
const router = useRouter();
const productId = route.params.id;

// 加载状态
const loading = ref(true);

// 商品详情数据
const productDetail = reactive({
  id: '',
  name: '',
  categoryId: '',
  categoryName: '',
  brandId: '',
  brandName: '',
  unit: '',
  mainImage: '',
  tags: [],
  services: [],
  attributes: {},
  specs: [],
  skus: [],
  detail: {
    content: ''
  },
  status: 0,
  createdAt: '',
  updatedAt: ''
});

// 属性标签映射
const attributeLabels = {
  material: '材质',
  color: '颜色',
  size: '尺寸',
  weight_capacity: '承重',
  origin: '产地',
  warranty: '保修期'
};

// 获取属性标签
const getAttributeLabel = (code) => {
  return attributeLabels[code] || code;
};

// 返回列表
const goBack = () => {
  router.push('/master/cooperative/commodity');
};

// 获取商品详情
const getProductDetail = async () => {
  loading.value = true;
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟数据
    const mockData = {
      id: productId,
      name: '高品质办公椅',
      categoryId: '1-1',
      categoryName: '办公家具 > 办公桌椅',
      brandId: '1',
      brandName: '优派家具',
      unit: '把',
      mainImage: 'https://example.com/chair.jpg',
      tags: [
        { id: '1', name: '办公家具' },
        { id: '2', name: '人体工学' }
      ],
      services: ['免费安装', '3年保修', '7天无理由退换'],
      attributes: {
        material: '网布+金属',
        color: '黑色、灰色',
        size: '60cm×60cm×120cm',
        weight_capacity: '150kg',
        origin: '中国',
        warranty: '3年'
      },
      specs: [
        {
          name: '颜色',
          values: ['黑色', '灰色']
        }
      ],
      skus: [
        {
          id: 'SKU001',
          specValues: ['黑色'],
          specText: '颜色:黑色',
          skuName: '高品质办公椅 黑色',
          imageUrl: 'https://example.com/chair-black.jpg',
          salesPrice: 599.00,
          marketPrice: 699.00,
          costPrice: 399.00,
          stock: 200,
          sales: 98,
          unit: '把'
        },
        {
          id: 'SKU002',
          specValues: ['灰色'],
          specText: '颜色:灰色',
          skuName: '高品质办公椅 灰色',
          imageUrl: 'https://example.com/chair-gray.jpg',
          salesPrice: 599.00,
          marketPrice: 699.00,
          costPrice: 399.00,
          stock: 150,
          sales: 58,
          unit: '把'
        }
      ],
      detail: {
        content: `
          <div class="product-detail">
            <h3>产品介绍</h3>
            <p>这是一款高品质的办公椅，采用人体工学设计，舒适耐用。</p>
            <p>特点：</p>
            <ul>
              <li>人体工学设计，贴合背部曲线</li>
              <li>高弹性网布，透气舒适</li>
              <li>可调节高度和靠背角度</li>
              <li>静音万向轮，自由移动</li>
            </ul>
            <p>适用场景：办公室、会议室、家庭办公等</p>
          </div>
        `
      },
      status: 1,
      createdAt: '2023-08-15 10:30:00',
      updatedAt: '2023-08-16 15:20:00'
    };
    
    // 填充数据
    Object.assign(productDetail, mockData);
    
  } catch (error) {
    console.error('获取商品详情失败:', error);
    Message.error('获取商品详情失败，请重试');
  } finally {
    loading.value = false;
  }
};

// 页面加载时获取数据
onMounted(() => {
  getProductDetail();
});
</script>

<style lang="scss" scoped>
.product-detail-content {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
  
  :deep(h3) {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #333;
  }
  
  :deep(p) {
    margin-bottom: 12px;
    line-height: 1.6;
  }
  
  :deep(ul) {
    padding-left: 20px;
    margin-bottom: 16px;
    
    li {
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }
}
</style>
