<template>
  <div class="ma-content-block p-4">
    <a-card class="mb-4">
      <template #title>
        <div class="text-lg font-semibold">商品详情</div>
      </template>

      <!-- 步骤导航 -->
      <a-steps :current="currentStep" class="mb-6">
        <a-step title="基本信息" />
        <a-step title="属性信息" />
        <a-step title="规格/SKU" />
        <a-step title="商品详情" />
      </a-steps>

      <!-- 步骤内容区域 -->
      <div class="step-content">
        <!-- 步骤1：基本信息 -->
        <div v-show="currentStep === 0">
          <div class="grid grid-cols-2 gap-4">
            <a-form-item field="name" label="商品名称">
              <a-input v-model="basicForm.name" placeholder="请输入商品名称" disabled />
            </a-form-item>
            <a-form-item field="categoryId" label="商品分类">
              <a-input v-model="basicForm.categoryName" disabled />
            </a-form-item>
            <a-form-item field="brandId" label="商品品牌">
              <a-input v-model="basicForm.brandName" disabled />
            </a-form-item>
            <a-form-item field="status" label="商品状态">
              <a-tag :color="basicForm.status === 1 ? 'green' : basicForm.status === 0 ? 'orange' : 'red'">
                {{ basicForm.status === 1 ? '已发布' : basicForm.status === 0 ? '待审核' : '已下架' }}
              </a-tag>
            </a-form-item>
            <a-form-item field="mainImage" label="商品主图" :span="2">
              <a-image :src="basicForm.mainImage || '/placeholder.png'" width="120" height="120" />
            </a-form-item>
            <a-form-item field="tags" label="商品标签" :span="2">
              <div class="flex flex-wrap">
                <a-tag v-for="tag in basicForm.tags" :key="tag.id" class="mr-2 mb-2">
                  {{ tag.name }}
                </a-tag>
              </div>
            </a-form-item>
            <a-form-item field="services" label="商品服务" :span="2">
              <div class="flex flex-wrap">
                <a-tag v-for="service in basicForm.services" :key="service" color="arcoblue" class="mr-2 mb-2">
                  {{ service }}
                </a-tag>
              </div>
            </a-form-item>
          </div>
        </div>

        <!-- 步骤2：属性信息 -->
        <div v-show="currentStep === 1">
          <div class="grid grid-cols-2 gap-4">
            <a-descriptions :data="attributeData" layout="vertical" bordered />
          </div>
        </div>

        <!-- 步骤3：规格/SKU -->
        <div v-show="currentStep === 2">
          <a-table :data="basicForm.skus" :pagination="false" :bordered="true">
            <template #columns>
              <a-table-column title="SKU图片" data-index="imageUrl" :width="100">
                <template #cell="{ record }">
                  <a-image :src="record.imageUrl || '/placeholder.png'" width="60" height="60" />
                </template>
              </a-table-column>
              <a-table-column title="SKU名称" data-index="skuName" :width="200" />
              <a-table-column title="销售价" data-index="salesPrice" :width="100" />
              <a-table-column title="市场价" data-index="marketPrice" :width="100" />
              <a-table-column title="成本价" data-index="costPrice" :width="100" />
              <a-table-column title="库存" data-index="stock" :width="80" />
              <a-table-column title="销量" data-index="sales" :width="80" />
              <a-table-column title="单位" data-index="unit" :width="80" />
            </template>
          </a-table>
        </div>

        <!-- 步骤4：商品详情 -->
        <div v-show="currentStep === 3">
          <div class="rich-text-content" v-html="detailForm.content"></div>
        </div>
      </div>

      <!-- 步骤按钮 -->
      <div class="flex justify-between mt-4">
        <a-button @click="prevStep" :disabled="currentStep === 0">上一步</a-button>
        <div>
          <a-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</a-button>
          <a-button v-else type="primary" @click="goBack">返回列表</a-button>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// 页面元数据
definePageMeta({
  name: "master-cooperative-commodity-detail",
  path: "/master/cooperative/commodity/:id"
});

const route = useRoute();
const router = useRouter();
const productId = route.params.id;

// 当前步骤
const currentStep = ref(0);

// 基本信息表单
const basicForm = reactive({
  id: '',
  name: '',
  categoryId: '',
  categoryName: '',
  brandId: '',
  brandName: '',
  mainImage: '',
  status: 0,
  tags: [],
  services: [],
  skus: []
});

// 属性信息
const attributeData = ref([]);

// 详情信息
const detailForm = reactive({
  content: ''
});

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 下一步
const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++;
  }
};

// 返回列表
const goBack = () => {
  router.push('/master/cooperative/commodity');
};

// 获取商品详情
const getProductDetail = async () => {
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟数据
    const mockData = {
      id: productId,
      name: '高品质办公椅',
      categoryId: '1',
      categoryName: '办公家具',
      brandId: '1',
      brandName: '优派家具',
      mainImage: 'https://example.com/chair.jpg',
      status: 1,
      tags: [
        { id: 1, name: '办公家具' },
        { id: 2, name: '人体工学' }
      ],
      services: ['免费安装', '3年保修'],
      skus: [
        {
          id: 'SKU001',
          skuName: '高品质办公椅-黑色',
          imageUrl: 'https://example.com/chair-black.jpg',
          salesPrice: 599.00,
          marketPrice: 699.00,
          costPrice: 399.00,
          sales: 98,
          stock: 200,
          unit: '把'
        },
        {
          id: 'SKU002',
          skuName: '高品质办公椅-灰色',
          imageUrl: 'https://example.com/chair-gray.jpg',
          salesPrice: 599.00,
          marketPrice: 699.00,
          costPrice: 399.00,
          sales: 58,
          stock: 150,
          unit: '把'
        }
      ],
      attributes: [
        { label: '材质', value: '网布+金属' },
        { label: '颜色', value: '黑色、灰色' },
        { label: '尺寸', value: '60cm×60cm×120cm' },
        { label: '承重', value: '150kg' },
        { label: '产地', value: '中国' },
        { label: '保修期', value: '3年' }
      ],
      detail: {
        content: `
          <div class="product-detail">
            <h3>产品介绍</h3>
            <p>这是一款高品质的办公椅，采用人体工学设计，舒适耐用。</p>
            <p>特点：</p>
            <ul>
              <li>人体工学设计，贴合背部曲线</li>
              <li>高弹性网布，透气舒适</li>
              <li>可调节高度和靠背角度</li>
              <li>静音万向轮，自由移动</li>
            </ul>
            <p>适用场景：办公室、会议室、家庭办公等</p>
          </div>
        `
      }
    };
    
    // 填充表单数据
    Object.assign(basicForm, mockData);
    
    // 填充属性数据
    attributeData.value = mockData.attributes.map(attr => ({
      label: attr.label,
      value: attr.value
    }));
    
    // 填充详情数据
    detailForm.content = mockData.detail.content;
    
  } catch (error) {
    console.error('获取商品详情失败:', error);
  }
};

// 页面加载时获取商品详情
onMounted(() => {
  getProductDetail();
});
</script>

<style lang="scss" scoped>
.rich-text-content {
  padding: 16px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  
  :deep(h3) {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  
  :deep(p) {
    margin-bottom: 12px;
    line-height: 1.6;
  }
  
  :deep(ul) {
    padding-left: 20px;
    margin-bottom: 16px;
    
    li {
      margin-bottom: 8px;
    }
  }
}
</style>
