<template>
  <div class="ma-content-block p-4">
    <a-card class="mb-4">
      <template #title>
        <div class="text-lg font-semibold">编辑商品</div>
      </template>

      <!-- 步骤导航 -->
      <a-steps :current="currentStep" class="mb-6">
        <a-step title="基本信息" />
        <a-step title="属性信息" />
        <a-step title="规格/SKU" />
        <a-step title="商品详情" />
      </a-steps>

      <!-- 步骤内容区域 -->
      <div class="step-content">
        <!-- 步骤1：基本信息 -->
        <div v-show="currentStep === 0">
          <a-form :model="basicForm" ref="basicFormRef" layout="vertical">
            <div class="grid grid-cols-2 gap-4">
              <a-form-item field="name" label="商品名称" :rules="[{ required: true, message: '请输入商品名称' }]">
                <a-input v-model="basicForm.name" placeholder="请输入商品名称" />
              </a-form-item>
              <a-form-item field="categoryId" label="商品分类" :rules="[{ required: true, message: '请选择商品分类' }]">
                <a-cascader
                  v-model="basicForm.categoryId"
                  :options="categoryOptions"
                  placeholder="请选择分类"
                  allow-clear
                />
              </a-form-item>
              <a-form-item field="brandId" label="商品品牌" :rules="[{ required: true, message: '请选择商品品牌' }]">
                <a-select
                  v-model="basicForm.brandId"
                  :options="brandOptions"
                  placeholder="请选择品牌"
                  allow-clear
                />
              </a-form-item>
              <a-form-item field="unit" label="计量单位" :rules="[{ required: true, message: '请输入计量单位' }]">
                <a-input v-model="basicForm.unit" placeholder="如：个、件、套、把" />
              </a-form-item>
              <a-form-item field="mainImage" label="商品主图" :span="2" :rules="[{ required: true, message: '请上传商品主图' }]">
                <a-upload
                  list-type="picture-card"
                  :file-list="mainImageList"
                  :custom-request="handleUploadMainImage"
                  :limit="1"
                >
                  <template #upload-button>
                    <div>
                      <i-icon-plus />
                      <div>上传图片</div>
                    </div>
                  </template>
                </a-upload>
              </a-form-item>
              <a-form-item field="tags" label="商品标签" :span="2">
                <a-select
                  v-model="basicForm.tags"
                  :options="tagOptions"
                  placeholder="请选择标签"
                  allow-clear
                  multiple
                />
              </a-form-item>
              <a-form-item field="services" label="商品服务" :span="2">
                <a-checkbox-group v-model="basicForm.services" direction="horizontal">
                  <a-checkbox value="免费安装">免费安装</a-checkbox>
                  <a-checkbox value="2年保修">2年保修</a-checkbox>
                  <a-checkbox value="3年保修">3年保修</a-checkbox>
                  <a-checkbox value="5年保修">5年保修</a-checkbox>
                  <a-checkbox value="7天无理由退换">7天无理由退换</a-checkbox>
                  <a-checkbox value="上门服务">上门服务</a-checkbox>
                  <a-checkbox value="技术支持">技术支持</a-checkbox>
                </a-checkbox-group>
              </a-form-item>
            </div>
          </a-form>
        </div>

        <!-- 步骤2：属性信息 -->
        <div v-show="currentStep === 1">
          <a-form :model="attributeForm" ref="attributeFormRef" layout="vertical">
            <div class="grid grid-cols-2 gap-4">
              <a-form-item v-for="(attr, index) in attributeList" :key="index" :field="`attr_${index}`" :label="attr.name">
                <a-input v-model="attributeForm[attr.code]" :placeholder="`请输入${attr.name}`" />
              </a-form-item>
            </div>
          </a-form>
        </div>

        <!-- 步骤3：规格/SKU -->
        <div v-show="currentStep === 2">
          <div class="mb-4">
            <div class="font-medium mb-2">添加规格项</div>
            <a-space>
              <a-input v-model="newSpecName" placeholder="规格名称，如颜色、尺寸" />
              <a-input v-model="newSpecValue" placeholder="规格值，多个值用逗号分隔" />
              <a-button type="primary" @click="addSpec">添加规格</a-button>
            </a-space>
          </div>

          <div v-if="specList.length > 0" class="mb-4">
            <div class="font-medium mb-2">已添加规格</div>
            <a-space wrap>
              <a-tag 
                v-for="(spec, index) in specList" 
                :key="index"
                closable
                @close="removeSpec(index)"
              >
                {{ spec.name }}：{{ spec.values.join('、') }}
              </a-tag>
            </a-space>
          </div>

          <div v-if="skuList.length > 0" class="mb-4">
            <div class="font-medium mb-2">SKU列表</div>
            <a-table :data="skuList" :bordered="true">
              <template #columns>
                <a-table-column title="规格" data-index="specText" :width="200" />
                <a-table-column title="图片" :width="100">
                  <template #cell="{ record }">
                    <a-upload
                      list-type="picture-card"
                      :file-list="record.imageList"
                      :custom-request="(options) => handleUploadSkuImage(options, record)"
                      :limit="1"
                    >
                      <template #upload-button>
                        <div>
                          <i-icon-plus />
                        </div>
                      </template>
                    </a-upload>
                  </template>
                </a-table-column>
                <a-table-column title="销售价" :width="120">
                  <template #cell="{ record }">
                    <a-input-number v-model="record.salesPrice" :min="0" :precision="2" />
                  </template>
                </a-table-column>
                <a-table-column title="市场价" :width="120">
                  <template #cell="{ record }">
                    <a-input-number v-model="record.marketPrice" :min="0" :precision="2" />
                  </template>
                </a-table-column>
                <a-table-column title="成本价" :width="120">
                  <template #cell="{ record }">
                    <a-input-number v-model="record.costPrice" :min="0" :precision="2" />
                  </template>
                </a-table-column>
                <a-table-column title="库存" :width="100">
                  <template #cell="{ record }">
                    <a-input-number v-model="record.stock" :min="0" :precision="0" />
                  </template>
                </a-table-column>
                <a-table-column title="销量" :width="100">
                  <template #cell="{ record }">
                    <span>{{ record.sales || 0 }}</span>
                  </template>
                </a-table-column>
              </template>
            </a-table>
          </div>
        </div>

        <!-- 步骤4：商品详情 -->
        <div v-show="currentStep === 3">
          <a-form :model="detailForm" ref="detailFormRef" layout="vertical">
            <a-form-item field="content" label="商品详情" :rules="[{ required: true, message: '请输入商品详情' }]">
              <a-textarea
                v-model="detailForm.content"
                :auto-size="{ minRows: 10, maxRows: 20 }"
                placeholder="请输入商品详情，支持富文本编辑"
              />
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 步骤按钮 -->
      <div class="flex justify-between mt-4">
        <a-button @click="prevStep" :disabled="currentStep === 0">上一步</a-button>
        <div>
          <a-button type="outline" @click="saveAsDraft" class="mr-2">保存草稿</a-button>
          <a-button v-if="currentStep < 3" type="primary" @click="nextStep">下一步</a-button>
          <a-button v-else type="primary" @click="submitForm">提交</a-button>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRoute, useRouter } from 'vue-router';

// 页面元数据
definePageMeta({
  name: "master-cooperative-commodity-edit",
  path: "/master/cooperative/commodity/edit/:id"
});

const route = useRoute();
const router = useRouter();
const productId = route.params.id;

// 当前步骤
const currentStep = ref(0);

// 基本信息表单
const basicFormRef = ref(null);
const basicForm = reactive({
  name: '',
  categoryId: null,
  brandId: null,
  unit: '',
  mainImage: '',
  tags: [],
  services: []
});

// 主图上传
const mainImageList = ref([]);
const handleUploadMainImage = (options) => {
  // 模拟上传
  setTimeout(() => {
    const url = 'https://example.com/upload/product.jpg';
    basicForm.mainImage = url;
    mainImageList.value = [
      {
        uid: '1',
        name: 'product.jpg',
        url: url
      }
    ];
    options.onSuccess(url);
  }, 1000);
};

// 属性信息表单
const attributeFormRef = ref(null);
const attributeForm = reactive({});
const attributeList = ref([]);

// 规格/SKU表单
const newSpecName = ref('');
const newSpecValue = ref('');
const specList = ref([]);
const skuList = ref([]);

// 添加规格
const addSpec = () => {
  if (!newSpecName.value) {
    Message.error('请输入规格名称');
    return;
  }
  if (!newSpecValue.value) {
    Message.error('请输入规格值');
    return;
  }
  
  // 添加规格
  const values = newSpecValue.value.split(',').map(v => v.trim()).filter(v => v);
  specList.value.push({
    name: newSpecName.value,
    values: values
  });
  
  // 生成SKU列表
  generateSkuList();
  
  // 清空输入
  newSpecName.value = '';
  newSpecValue.value = '';
};

// 移除规格
const removeSpec = (index) => {
  specList.value.splice(index, 1);
  generateSkuList();
};

// 生成SKU列表
const generateSkuList = () => {
  if (specList.value.length === 0) {
    skuList.value = [];
    return;
  }
  
  // 生成笛卡尔积
  const cartesian = (arr) => {
    return arr.reduce((a, b) => {
      return a.flatMap(x => b.map(y => [...x, y]));
    }, [[]]);
  };
  
  const specs = specList.value.map(spec => spec.values);
  const combinations = cartesian(specs);
  
  // 生成SKU列表
  skuList.value = combinations.map((combination, index) => {
    // 生成规格文本
    const specText = combination.map((value, i) => {
      return `${specList.value[i].name}:${value}`;
    }).join(', ');
    
    // 生成SKU名称
    const skuName = `${basicForm.name} ${combination.join(' ')}`;
    
    // 查找已有的SKU
    const existingSku = skuList.value.find(sku => {
      return sku.specValues && sku.specValues.length === combination.length && 
        sku.specValues.every((val, i) => val === combination[i]);
    });
    
    if (existingSku) {
      return existingSku;
    }
    
    return {
      id: `sku_${index}`,
      specValues: combination,
      specText: specText,
      skuName: skuName,
      imageUrl: '',
      imageList: [],
      salesPrice: 0,
      marketPrice: 0,
      costPrice: 0,
      stock: 0,
      sales: 0
    };
  });
};

// 上传SKU图片
const handleUploadSkuImage = (options, record) => {
  // 模拟上传
  setTimeout(() => {
    const url = 'https://example.com/upload/sku.jpg';
    record.imageUrl = url;
    record.imageList = [
      {
        uid: '1',
        name: 'sku.jpg',
        url: url
      }
    ];
    options.onSuccess(url);
  }, 1000);
};

// 商品详情表单
const detailFormRef = ref(null);
const detailForm = reactive({
  content: ''
});

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 下一步
const nextStep = async () => {
  if (currentStep.value === 0) {
    // 验证基本信息表单
    const { valid } = await basicFormRef.value.validate();
    if (!valid) {
      return;
    }
  } else if (currentStep.value === 1) {
    // 验证属性信息表单
    const { valid } = await attributeFormRef.value.validate();
    if (!valid) {
      return;
    }
  } else if (currentStep.value === 2) {
    // 验证SKU信息
    if (skuList.value.length === 0) {
      Message.error('请添加至少一个规格');
      return;
    }
    
    // 验证SKU价格和库存
    const invalidSku = skuList.value.find(sku => !sku.salesPrice || sku.salesPrice <= 0);
    if (invalidSku) {
      Message.error('请为所有SKU设置有效的销售价');
      return;
    }
  }
  
  if (currentStep.value < 3) {
    currentStep.value++;
  }
};

// 保存草稿
const saveAsDraft = async () => {
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Message.success('已保存为草稿');
  } catch (error) {
    console.error('保存草稿失败:', error);
    Message.error('保存失败，请重试');
  }
};

// 提交表单
const submitForm = async () => {
  try {
    // 验证商品详情表单
    const { valid } = await detailFormRef.value.validate();
    if (!valid) {
      return;
    }
    
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Message.success('商品编辑成功，等待审核');
    
    // 跳转到列表页
    router.push('/master/cooperative/commodity');
  } catch (error) {
    console.error('提交表单失败:', error);
    Message.error('提交失败，请重试');
  }
};

// 分类选项
const categoryOptions = ref([]);

// 品牌选项
const brandOptions = ref([]);

// 标签选项
const tagOptions = ref([]);

// 获取商品详情
const getProductDetail = async () => {
  try {
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 模拟数据
    const mockData = {
      id: productId,
      name: '高品质办公椅',
      categoryId: '1',
      brandId: '1',
      unit: '把',
      mainImage: 'https://example.com/chair.jpg',
      tags: ['1', '2'],
      services: ['免费安装', '3年保修'],
      attributes: {
        material: '网布+金属',
        color: '黑色、灰色',
        size: '60cm×60cm×120cm',
        weight_capacity: '150kg',
        origin: '中国',
        warranty: '3年'
      },
      specs: [
        {
          name: '颜色',
          values: ['黑色', '灰色']
        }
      ],
      skus: [
        {
          id: 'SKU001',
          specValues: ['黑色'],
          specText: '颜色:黑色',
          skuName: '高品质办公椅 黑色',
          imageUrl: 'https://example.com/chair-black.jpg',
          imageList: [
            {
              uid: '1',
              name: 'chair-black.jpg',
              url: 'https://example.com/chair-black.jpg'
            }
          ],
          salesPrice: 599.00,
          marketPrice: 699.00,
          costPrice: 399.00,
          stock: 200,
          sales: 98
        },
        {
          id: 'SKU002',
          specValues: ['灰色'],
          specText: '颜色:灰色',
          skuName: '高品质办公椅 灰色',
          imageUrl: 'https://example.com/chair-gray.jpg',
          imageList: [
            {
              uid: '1',
              name: 'chair-gray.jpg',
              url: 'https://example.com/chair-gray.jpg'
            }
          ],
          salesPrice: 599.00,
          marketPrice: 699.00,
          costPrice: 399.00,
          stock: 150,
          sales: 58
        }
      ],
      detail: {
        content: `
          <div class="product-detail">
            <h3>产品介绍</h3>
            <p>这是一款高品质的办公椅，采用人体工学设计，舒适耐用。</p>
            <p>特点：</p>
            <ul>
              <li>人体工学设计，贴合背部曲线</li>
              <li>高弹性网布，透气舒适</li>
              <li>可调节高度和靠背角度</li>
              <li>静音万向轮，自由移动</li>
            </ul>
            <p>适用场景：办公室、会议室、家庭办公等</p>
          </div>
        `
      }
    };
    
    // 填充基本信息表单
    Object.assign(basicForm, {
      name: mockData.name,
      categoryId: mockData.categoryId,
      brandId: mockData.brandId,
      unit: mockData.unit,
      mainImage: mockData.mainImage,
      tags: mockData.tags,
      services: mockData.services
    });
    
    // 设置主图
    mainImageList.value = [
      {
        uid: '1',
        name: 'product.jpg',
        url: mockData.mainImage
      }
    ];
    
    // 填充属性信息表单
    Object.assign(attributeForm, mockData.attributes);
    
    // 填充规格信息
    specList.value = mockData.specs;
    
    // 填充SKU列表
    skuList.value = mockData.skus;
    
    // 填充商品详情
    detailForm.content = mockData.detail.content;
    
  } catch (error) {
    console.error('获取商品详情失败:', error);
    Message.error('获取商品详情失败，请重试');
  }
};

// 初始化数据
onMounted(() => {
  // 模拟获取分类数据
  categoryOptions.value = [
    {
      value: '1',
      label: '办公家具',
      children: [
        {
          value: '1-1',
          label: '办公桌椅',
        },
        {
          value: '1-2',
          label: '文件柜',
        },
        {
          value: '1-3',
          label: '会议桌',
        }
      ]
    },
    {
      value: '2',
      label: '办公设备',
      children: [
        {
          value: '2-1',
          label: '电脑配件',
        },
        {
          value: '2-2',
          label: '投影设备',
        },
        {
          value: '2-3',
          label: '打印设备',
        }
      ]
    }
  ];
  
  // 模拟获取品牌数据
  brandOptions.value = [
    { value: '1', label: '优派家具' },
    { value: '2', label: '智慧办公' },
    { value: '3', label: '科技办公' }
  ];
  
  // 模拟获取标签数据
  tagOptions.value = [
    { value: '1', label: '办公家具' },
    { value: '2', label: '人体工学' },
    { value: '3', label: '会议家具' },
    { value: '4', label: '智能家具' },
    { value: '5', label: '办公设备' },
    { value: '6', label: '会议设备' }
  ];
  
  // 模拟获取属性列表
  attributeList.value = [
    { name: '材质', code: 'material' },
    { name: '颜色', code: 'color' },
    { name: '尺寸', code: 'size' },
    { name: '承重', code: 'weight_capacity' },
    { name: '产地', code: 'origin' },
    { name: '保修期', code: 'warranty' }
  ];
  
  // 获取商品详情
  getProductDetail();
});
</script>

<style lang="scss" scoped>
.step-content {
  min-height: 400px;
}
</style>
