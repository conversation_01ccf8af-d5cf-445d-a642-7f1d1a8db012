<template>
  <a-drawer
    :visible="visible"
    :width="500"
    :title="'留言详细信息'"
    @cancel="closeDrawer"
    unmountOnClose
  >
    <template #footer>
      <a-space>
        <a-button @click="closeDrawer">关闭</a-button>
        <a-button 
          v-if="!currentMessage.status" 
          type="primary" 
          @click="handleProcess"
        >标记为已处理</a-button>
      </a-space>
    </template>

    <div class="message-detail">
      <a-descriptions :data="descriptionData" :column="1" size="large" bordered />
      
      <!-- 附件区域 -->
      <div v-if="currentMessage.attachment && currentMessage.attachment.length > 0" class="attachment-area mt-4">
        <a-divider>附件</a-divider>
        <a-space direction="vertical" style="width: 100%;">
          <a-card
            v-for="(item, index) in currentMessage.attachment"
            :key="index"
            :style="{ width: '100%' }"
            class="attachment-card"
          >
            <template #cover>
              <div class="file-preview">
                <icon-file-image size="50" v-if="isImageFile(item)" />
                <icon-file-pdf size="50" v-else-if="isPdfFile(item)" />
                <icon-file size="50" v-else />
              </div>
            </template>
            <a-card-meta>
              <template #title>{{ item }}</template>
              <template #description>
                <a-button type="text" size="small">
                  <template #icon><icon-download /></template>
                  下载
                </a-button>
                <a-button type="text" size="small">
                  <template #icon><icon-eye /></template>
                  预览
                </a-button>
              </template>
            </a-card-meta>
          </a-card>
        </a-space>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, computed, defineEmits } from 'vue';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  message: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'process']);

// 当前留言信息
const currentMessage = computed(() => props.message || {});

// 关闭抽屉
const closeDrawer = () => {
  emit('update:visible', false);
};

// 处理留言
const handleProcess = () => {
  emit('process', currentMessage.value);
  closeDrawer();
};

// 描述列表数据
const descriptionData = computed(() => {
  return [
    {
      label: '留言时间',
      value: currentMessage.value.messageTime 
        ? formatTime(currentMessage.value.messageTime)
        : '-'
    },
    {
      label: '留言位置',
      value: currentMessage.value.location || '-'
    },
    {
      label: '留言人',
      value: currentMessage.value.name || '-'
    },
    {
      label: '邮箱',
      value: currentMessage.value.email || '-'
    },
    {
      label: '电话',
      value: currentMessage.value.phone || '-'
    },
    {
      label: '状态',
      value: currentMessage.value.status ? '已处理' : '未处理'
    },
    {
      label: '详细内容',
      value: currentMessage.value.details || '-'
    }
  ];
});

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 判断是否是图片文件
const isImageFile = (filename) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
  return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
};

// 判断是否是PDF文件
const isPdfFile = (filename) => {
  return filename.toLowerCase().endsWith('.pdf');
};
</script>

<style scoped>
.message-detail {
  padding: 8px;
}

.attachment-area {
  margin-top: 16px;
}

.attachment-card {
  margin-bottom: 12px;
}

.file-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  background-color: var(--color-fill-2);
}
</style>
