<template>
  <div class="ma-content-block p-4">
    <!-- 留言详情抽屉组件 -->
    <MessageDetailDrawer
      v-model:visible="detailDrawerVisible"
      :message="currentMessage"
      @process="toggleStatus"
    />
    <ma-crud
      :options="crudOptions"
      :columns="columns"
      ref="crudRef"
    >
      <!-- 留言时间 -->
      <template #created_at="{ record }">
        <div v-if="record.created_at" v-time="record.created_at"></div>
        <div v-else></div>
      </template>

      <!-- 附件 -->
      <!-- <template #attachment="{ record }">
        <a-space v-if="record.attachment && record.attachment.length > 0">
          <a-button type="primary" size="small" @click="viewAttachment(record)">
            <template #icon>
              <icon-file />
            </template>
            查看附件
          </a-button>
        </a-space>
        <span v-else>-</span>
      </template> -->

      <!-- 详细信息 -->
      <template #details="{ record }">
        <a-popover position="right">
          <template #content>
            <div
              class="content-preview"
              style="max-width: 400px; max-height: 300px; overflow: auto"
            >
              <div >{{ record.message_details }}</div>
            </div>
          </template>
          <a-button type="text" size="small"> <icon-eye /> 查看 </a-button>
        </a-popover>

      </template>

      <!-- 状态 -->
      <template #status="{ record }">
        <a-tag 
          :color="record.status ? 'green' : 'orange'" 
          style="cursor: pointer"
        >
          {{ record.status ? '已处理' : '未处理' }}
        </a-tag>
      </template>

      <!-- 操作 -->
      <template #operation="{ record }">
        <!-- <a-button 
          v-if="!record.status" 
          type="primary" 
          size="small" 
          @click="toggleStatus(record)"
        >
          处理完成
        </a-button> -->
        <a-link 
        v-if="!record.status" 
          type="primary" 
          size="small" 
          @click="toggleStatus(record)"
        >
        <icon-edit />
        处理完成
        </a-link>
      </template>

    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import officialWebsiteModuleApi from "@/api/master/officialWebsiteModule";
const messageManagementApi = officialWebsiteModuleApi.messageManagement;
// 导入留言详情抽屉组件
import MessageDetailDrawer from "./components/MessageDetailDrawer.vue";

// 本地模拟数据
// 留言位置数据
const mockLocations = [
  { id: 1, name: '联系我们页面' },
  { id: 2, name: '产品详情页' },
  { id: 3, name: '关于我们页面' },
  { id: 4, name: '首页' },
  { id: 5, name: '售后服务页' }
];

// 留言列表数据
const mockMessages = [
  { 
    id: 1, 
    messageTime: Date.now() - 1000 * 60 * 60 * 24 * 2, 
    location: '联系我们页面',
    name: '张先生',
    email: '<EMAIL>',
    phone: '13800138001',
    attachment: ['附件1.pdf'],
    details: '我对贵公司的产品非常感兴趣，希望能够了解更多详细信息。',
    status: 0 // 0表示未处理
  },
  { 
    id: 2, 
    messageTime: Date.now() - 1000 * 60 * 60 * 24 * 3, 
    location: '产品详情页',
    name: '李女士',
    email: '<EMAIL>',
    phone: '13900139002',
    attachment: [],
    details: '请问这款产品有什么特殊功能？能否提供更多技术参数？',
    status: 1 // 1表示已处理
  },
  { 
    id: 3, 
    messageTime: Date.now() - 1000 * 60 * 60 * 24 * 4, 
    location: '关于我们页面',
    name: '王经理',
    email: '<EMAIL>',
    phone: '13700137003',
    attachment: ['商务合作.doc', '公司介绍.ppt'],
    details: '我们公司希望与贵司建立长期合作关系，附件是我们的合作提案，请查阅。',
    status: 1 // 1表示已处理
  },
  { 
    id: 4, 
    messageTime: Date.now() - 1000 * 60 * 60 * 24 * 1, 
    location: '首页',
    name: '赵工程师',
    email: '<EMAIL>',
    phone: '13600136004',
    attachment: [],
    details: '网站UI设计非常好，但是在移动端有一些兼容性问题，建议优化。',
    status: 0 // 0表示未处理
  },
  { 
    id: 5, 
    messageTime: Date.now() - 1000 * 60 * 60 * 12, 
    location: '售后服务页',
    name: '刘女士',
    email: '<EMAIL>',
    phone: '13500135005',
    attachment: ['问题截图.jpg'],
    details: '我购买的产品出现了一些问题，附件是问题截图，希望能尽快得到解决。',
    status: 0 // 0表示未处理
  },
];

// 模拟接口
const messageApi = {
  getList: (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 简单的过滤逻辑
        let filteredData = [...mockMessages];
        
        if (params) {
          // ID过滤
          if (params.id) {
            filteredData = filteredData.filter(item => 
              item.id.toString().includes(params.id.toString())
            );
          }
          
          // 留言人过滤
          if (params.name) {
            filteredData = filteredData.filter(item => 
              item.name.includes(params.name)
            );
          }
          
          // 位置过滤
          if (params.location) {
            filteredData = filteredData.filter(item => 
              item.location === params.location
            );
          }
          
          // 电话过滤
          if (params.phone) {
            filteredData = filteredData.filter(item => 
              item.phone.includes(params.phone)
            );
          }
          
          // 邮箱过滤
          if (params.email) {
            filteredData = filteredData.filter(item => 
              item.email.includes(params.email)
            );
          }
          
          // 状态过滤
          if (params.status !== undefined && params.status !== '') {
            filteredData = filteredData.filter(item => 
              item.status === Number(params.status)
            );
          }
        }
        
        // 模拟分页
        const pageSize = params?.pageSize || 10;
        const current = params?.current || 1;
        const total = filteredData.length;
        const start = (current - 1) * pageSize;
        const end = start + pageSize;
        const items = filteredData.slice(start, end);
        
        resolve({
          data: {
            items,
            total,
            current,
            pageSize
          },
          success: true
        });
      }, 300); // 模拟网络延迟
    });
  },
  delete: (id) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 删除操作，实际应用中应调用后端 API
        const index = mockMessages.findIndex(item => item.id === id);
        if (index !== -1) {
          mockMessages.splice(index, 1);
        }
        resolve({ success: true });
      }, 300);
    });
  }
};

const locationApi = {
  getList: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            value: mockLocations
          },
          success: true
        });
      }, 300);
    });
  }
};

// 定义页面路由元信息
definePageMeta({
  name: "messageManagementList",
  title: "留言管理",
  icon: "icon-message",
  sort: 1,
  layout: "default",
});

const crudRef = ref();
const locationOptions = ref([]);

// 获取留言位置列表
const getLocationList = async () => {
  try {
    // 实际应用中应调用后端 API
    // const res = await locationApi.getList();
    // locationOptions.value = res.data;
    
    // 这里使用模拟数据
    locationOptions.value = mockLocations;
  } catch (error) {
    console.error("获取位置列表失败", error);
  }
};

onMounted(() => {
  getLocationList();
});

// 查看附件
const viewAttachment = (record) => {
  if (record.attachment && record.attachment.length > 0) {
    // 遍历附件并打开
    record.attachment.forEach(file => {
      // 模拟附件路径，实际项目中应使用真实的文件URL
      const fileUrl = `/uploads/attachments/${file}`;
      // 使用window.open打开链接
      window.open(fileUrl, '_blank');
      
      // 打印日志，实际项目中可以移除
      console.log(`打开附件：${fileUrl}`);
    });
  } else {
    Message.info("没有附件可查看");
  }
};

// 留言详情抽屉相关状态
const detailDrawerVisible = ref(false);
const currentMessage = ref(null);

// 查看详情
const viewDetails = (record) => {
  // 设置当前选中的留言
  currentMessage.value = record;
  // 打开留言详情抽屉
  detailDrawerVisible.value = true;
};

// 处理编辑/处理留言
const handleEdit = (record) => {
  navigateTo(`/master/officialWebsiteModule/messageManagement/process/${record.id}`);
};

// 处理删除留言
const handleDelete = async (record) => {
  try {
    // 删除操作，实际应用中应调用后端 API
    const index = mockMessages.findIndex(item => item.id === record.id);
    if (index !== -1) {
      mockMessages.splice(index, 1);
    }
    Message.success("删除成功");
    // 刷新列表
    if (crudRef.value) {
      crudRef.value.refresh();
    }
  } catch (error) {
    Message.error("删除失败");
  }
};

// 切换留言处理状态
const toggleStatus = async (record) => {
  try {
    // 获取当前状态的反值（0->1, 1->0）
    const newStatus = record.status ? 0 : 1;
    
    // 调用API更新状态，传入ID和状态取反值
    await messageManagementApi.update(record.id, { status: newStatus });
    
    // 显示状态切换成功提示
    const statusText = newStatus ? '已处理' : '未处理';
    Message.success(`留言状态已更改为：${statusText}`);
    
    // 刷新列表
    if (crudRef.value) {
      crudRef.value.refresh();
    }
  } catch (error) {
    console.error('更新留言状态失败:', error);
    Message.error("状态切换失败");
  }
};

// 表格列配置
const columns = [
  {
    title: "留言时间",
    dataIndex: "created_at",
    width: 150,
    slot: true
  },
  {
    title: "留言位置",
    dataIndex: "message_location",
    width: 150,
    search: true,
  },
  {
    title: "留言人",
    dataIndex: "submitter_name",
    width: 120,
    search: true,
  },
  {
    title: "邮箱",
    dataIndex: "email",
    width: 180,
    search: true,
  },
  {
    title: "电话",
    dataIndex: "phone",
    width: 150,
    search: true,
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 100,
    slot: true,
    search: {
      show: true,
      component: 'select',
      componentProps: {
        options: [
          { label: '未处理', value: 0 },
          { label: '已处理', value: 1 }
        ],
        placeholder: '请选择状态'
      }
    },
  },
  // {
  //   title: "附件",
  //   dataIndex: "attachment_url",
  //   width: 120,
  //   slot: true,
  // },
  {
    title: "详细信息",
    dataIndex: "details",
    width: 120,
    slot: true,
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 100,
    align: "center",
    fixed: "right",
    slot: true
  }
];

// 搜索前处理参数
const beforeSearch = (params) => {
  // 可以对搜索参数进行处理，如日期格式化等
  return params;
};

// ma-crud 配置项
const crudOptions = reactive({
  // 使用API调用方式获取数据
  api: messageManagementApi.getList,
  title: "留言管理",
  add: {
    show: false,
  },
  search: {
    cols: 3,
    beforeSearch,
  },
  searchForm: {
    id: "",
    name: "",
    location: "",
    phone: "",
    email: "",
  },
  table: {
    rowKey: "id",
  },
});
</script>

<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: var(--border-radius-medium);
}
</style>