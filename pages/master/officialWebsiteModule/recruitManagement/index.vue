<template>
  <div class="ma-content-block p-4">
    <ma-crud :options="crudOptions" :columns="columns" ref="crudRef">
      <!-- 招聘类型 -->
      <template #recruitment_type="{ record }">
        <a-tag :color="record.recruitment_type === 1 ? 'blue' : 'purple'">
          {{ record.recruitment_type === 1 ? "社会招聘" : "校园招聘" }}
        </a-tag>
      </template>

      <!-- 状态 -->
      <template #status="{ record }">
        <a-tag :color="record.status ? 'green' : 'gray'">
          {{ record.status ? "显示" : "隐藏" }}
        </a-tag>
      </template>
      <template #created_at="{ record }">
        <div v-time="record.created_at"></div>
      </template>
      <template #publish_time="{ record }">
        <div v-if="record.publish_time" v-time="record.publish_time"></div>
        <div v-else></div>
      </template>

      <template #updated_at="{ record }">
        <div v-if="record.updated_at" v-time="record.updated_at"></div>
        <div v-else></div>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import dayjs from "dayjs";
import { Message } from "@arco-design/web-vue";
import officialWebsiteModuleApi from "@/api/master/officialWebsiteModule";
const recruitManagementApi = officialWebsiteModuleApi.recruitManagement;

// 定义页面路由元信息
definePageMeta({
  name: "officialWebsiteModule_recruitManagementList",
  path: "/master/officialWebsiteModule/recruitManagement",
});

const crudRef = ref();
const router = useRouter();

// 这些处理函数已通过ma-crud组件内部实现，不需要单独定义

// ma-crud 配置项
const crudOptions = reactive({
  api: recruitManagementApi.getList,
  title: "招聘列表",
  searchLabelWidth: "80px",
  operationColumn: true,
  rowSelection: { showCheckedAll: true },
  add: {
    show: true,
    api: recruitManagementApi.create,
  },
  edit: {
    show: true,
    api: recruitManagementApi.update,
  },
  delete: {
    show: true,
    api: recruitManagementApi.delete,
  },
  export: { show: false },
  actionColumn: {
    width: 180,
  },
  formOption: { width: 1000 },
  table: {
    rowKey: "id",
    border: true,
    size: "medium",
  },
  search: {
    collapsed: false,
    showCollapse: true,
    collapsePosition: "left",
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 这里可以对搜索参数进行处理
    return params;
  },

  beforeAdd: (params) => {
    params.recruitment_type = Number(params.recruitment_type);
    params.status = Number(params.status);
    // 将发布时间字符串转换为毫秒级时间戳
    if (params.publish_time) {
      params.publish_time = new Date(params.publish_time).getTime();
    }
    // 添加校园招聘详情内容验证
    if (params.recruitment_type === 2 && params.position_details =='<p><br></p>') {
    
        Message.error('校园招聘的详情内容不能为空');
        return false; // 阻止提交
      }
    return params;
  },
  beforeEdit: (params) => {
    params.recruitment_type = Number(params.recruitment_type);
    params.status = Number(params.status);
    // 将发布时间字符串转换为毫秒级时间戳
    if (params.publish_time) {
      params.publish_time = new Date(params.publish_time).getTime();
    }
     // 编辑校园招聘详情内容验证
     if (params.recruitment_type === 2 && params.position_details =='<p><br></p>') {
    
    Message.error('校园招聘的详情内容不能为空');
    return false; // 阻止提交
  }

    return params;
  },

  beforeOpenEdit: (record) => {
    function formatTimestampToDate(timestamp) {
      // 确保输入是数字类型，处理字符串时间戳
      const ts =
        typeof timestamp === "string" ? parseInt(timestamp) : timestamp;

      if (!ts || isNaN(ts)) {
        console.log("警告：无效的时间戳", timestamp);
        return "";
      }

      // 创建日期对象
      const date = new Date(ts);

      // 判断日期是否有效
      if (!(date instanceof Date) || isNaN(date.getTime())) {
        console.log("警告：无效的日期对象", date);
        return "";
      }

      // 获取年、月、日（月份从 0 开始，所以要 +1）
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 补零
      const day = String(date.getDate()).padStart(2, "0"); // 补零

      // 组合成 YYYY-MM-DD 格式
      return `${year}-${month}-${day}`;
    }
    console.log("编辑前原始参数:", record);
    record.publish_time = formatTimestampToDate(record.publish_time);
    return true; // 必须返回true才会打开编辑弹窗
  },
});

// 状态选项
const statusOptions = [
  { label: "显示", value: 1 },
  // { label: "隐藏", value: 0 },
];

// 表格列配置
const columns = reactive([
  {
    title: "标题",
    dataIndex: "title",
    width: 180,
    search: true,
    formType: "input",
    commonRules:[{required:true,message:"请输入标题"}]
  },
  {
    title: "招聘类型",
    dataIndex: "recruitment_type",
    width: 120,
    search: true,
    formType: "select",
    addDefaultValue: 1,
    dict: {
      data: [
        { label: "社会招聘", value: 1 },
        { label: "校园招聘", value: 2 },
      ],
    },
    commonRules:[{required:true,message:"请输入招聘类型"}],
    // 监听招聘类型变化，动态控制相关字段的显示和隐藏
    onControl: (value, maFormObject) => {
      if (!maFormObject) return;

      const service = maFormObject.getColumnService();
      if (!service) return;

      // 社会招聘字段
      const socialFields = [
        "position_name", // 岗位名称
        "position_address", // 岗位地址
        "position_duty", // 岗位职责
        "position_requirement", // 岗位要求
        "publish_time", // 发布时间
      ];

      // 校园招聘字段
      const campusFields = [
        "position_details", // 详情内容
      ];

      // 确保value是数字类型
      const recruitmentType =
        typeof value === "string" ? parseInt(value) : value;
      // 判断是社会招聘还是校园招聘
      const isSocialRecruitment = recruitmentType === 1;

      // 控制社会招聘相关字段
      socialFields.forEach((field) => {
        const fieldObj = service.get(field);
        if (fieldObj && typeof fieldObj.setAttr === "function") {
          // 社会招聘时显示这些字段，校园招聘时隐藏
          fieldObj.setAttr("display", isSocialRecruitment);

          // 设置必填规则
          if (isSocialRecruitment) {
            if (field === "position_name") {
              fieldObj.setAttr("rules", [
                { required: true, message: "岗位名称必填" },
              ]);
            } else if (field === "position_address") {
              fieldObj.setAttr("rules", [
                { required: true, message: "岗位地址必填" },
              ]);
            } else if (field === "position_duty") {
              fieldObj.setAttr("rules", [
                { required: true, message: "岗位职责必填" },
              ]);
            } else if (field === "position_requirement") {
              fieldObj.setAttr("rules", [
                { required: true, message: "岗位要求必填" },
              ]);
            } else if (field === "publish_time") {
              fieldObj.setAttr("rules", [
                { required: true, message: "发布时间必填" },
              ]);
            }
          } else {
            // 校园招聘时移除必填规则
            fieldObj.setAttr("rules", []);
          }
        }
      });

      // 控制校园招聘相关字段
      campusFields.forEach((field) => {
        const fieldObj = service.get(field);
        if (fieldObj && typeof fieldObj.setAttr === "function") {
          // 校园招聘时显示这些字段，社会招聘时隐藏
          fieldObj.setAttr("display", !isSocialRecruitment);

          // 如果是校园招聘，设置详情内容为必填
          if (field === "position_details" && !isSocialRecruitment) {
            fieldObj.setAttr("rules", [
              { required: true, message: "详情内容必填" },
            ]);
          } else {
            // 社会招聘时移除必填规则
            fieldObj.setAttr("rules", []);
          }
        }
      });
    },
  },
  {
    title: "岗位名称",
    dataIndex: "position_name",
    width: 150,
    search: true,
    formType: "input",
    // 字段显示控制由onControl函数统一管理，不需要单独控制
    editDisplay: true,
    addDisplay: true,
  },
  {
    title: "岗位地址",
    dataIndex: "position_address",
    width: 150,
    search: true,
    formType: "input",
    hide: true,
    // 字段显示控制由onControl函数统一管理，不需要单独控制
    editDisplay: true,
    addDisplay: true,
  },
  {
    title: "岗位职责",
    dataIndex: "position_duty",
    width: 120,
    search: false,
    hide: true,
    align: "center",
    formType: "textarea",
    // 字段显示控制由onControl函数统一管理，不需要单独控制
    editDisplay: true,
    addDisplay: true,
  },
  {
    title: "岗位要求",
    dataIndex: "position_requirement",
    width: 120,
    search: false,
    hide: true,
    align: "center",
    formType: "textarea",
    // 字段显示控制由onControl函数统一管理，不需要单独控制
    editDisplay: true,
    addDisplay: true,
  },
  {
    title: "发布时间",
    dataIndex: "publish_time",
    width: 160,
    search: false,
    hide: true,
    formType: "date",
  },

  {
    title: "详情内容",
    dataIndex: "position_details",
    width: 100,
    search: false,
    hide: true,
    formType: "wang-editor",
    // 字段显示控制由onControl函数统一管理，不需要单独控制
    commonRules:[{required:true,message:"请输入详情内容"}],
    editDisplay: true,
    addDisplay: true,
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 80,
    search: true,
    formType: "select",
    addDefaultValue: 1,
    commonRules:[{required:true,message:"请选择状态"}],
    dict: { data: statusOptions },
  },
  {
    title: "创建时间",
    dataIndex: "created_at",
    width: 160,
    search: false,
    formatter: ({ cellValue }) => {
      return dayjs(cellValue).format("YYYY-MM-DD HH:mm:ss");
    },
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: "更新时间",
    dataIndex: "updated_at",
    width: 160,
    search: false,
    formatter: ({ cellValue }) => {
      return dayjs(cellValue).format("YYYY-MM-DD HH:mm:ss");
    },
    addDisplay: false,
    editDisplay: false,
  },
]);

// 生命周期钩子
onMounted(() => {
  // 初始化
});
</script>

<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: var(--border-radius-medium);
}
</style>
