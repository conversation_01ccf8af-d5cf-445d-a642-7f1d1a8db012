<template>
  <div>
    <div class="head bg-white p-4 rounded">
      <div class="grid grid-cols-[200px_100px_1fr_1fr_1fr_1fr_1fr_1fr] gap-4">
        <div class="rounded">
          <div class="text-center">
            <div class="text-sm text-gray-500 mb-2">最近15分钟活跃访客数</div>
            <div class="text-2xl font-bold text-primary">24</div>
          </div>
        </div>
        <div class="rounded">
          <div class="text-left">
            <div class="text-sm text-gray-500 mb-2">&nbsp;</div>
            <div class="flex flex-col">
              <div class="text-base font-bold text-primary mb-1">今日</div>
              <div class="text-base font-bold">昨日</div>
            </div>
          </div>
        </div>
        <div class="rounded">
          <div class="text-right">
            <div class="text-sm text-gray-500 mb-2">IP数</div>
            <div class="flex flex-col items-end">
              <div class="text-base font-bold text-primary mb-1">187</div>
              <div class="text-base font-bold text-gray-400">165</div>
            </div>
          </div>
        </div>
        <div class="rounded">
          <div class="text-right">
            <div class="text-sm text-gray-500 mb-2">浏览量(PV)</div>
            <div class="flex flex-col items-end">
              <div class="text-base font-bold text-primary mb-1">1024</div>
              <div class="text-base font-bold text-gray-400">896</div>
            </div>
          </div>
        </div>
        <div class="rounded">
          <div class="text-right">
            <div class="text-sm text-gray-500 mb-2">访客数(UV)</div>
            <div class="flex flex-col items-end">
              <div class="text-base font-bold text-primary mb-1">562</div>
              <div class="text-base font-bold text-gray-400">489</div>
            </div>
          </div>
        </div>
        <div class="rounded">
          <div class="text-right">
            <div class="text-sm text-gray-500 mb-2">会话数</div>
            <div class="flex flex-col items-end">
              <div class="text-base font-bold text-primary mb-1">32.5%</div>
              <div class="text-base font-bold text-gray-400">28.3%</div>
            </div>
          </div>
        </div>
        <div class="rounded">
          <div class="text-right">
            <div class="text-sm text-gray-500 mb-2">跳出率</div>
            <div class="flex flex-col items-end">
              <div class="text-base font-bold text-primary mb-1">03:45</div>
              <div class="text-base font-bold text-gray-400">03:12</div>
            </div>
          </div>
        </div>
        <div class="rounded">
          <div class="text-right">
            <div class="text-sm text-gray-500 mb-2">平均访问时长</div>
            <div class="flex flex-col items-end">
              <div class="text-base font-bold text-primary mb-1">43.8%</div>
              <div class="text-base font-bold text-gray-400">39.5%</div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
    <div class="mb-4 mt-4">
      <a-radio-group type="button" v-model="dateType">
        <a-radio value="today">今日</a-radio>
        <a-radio value="yesterday">昨日</a-radio>
        <a-radio value="7days">最近7日</a-radio>
        <a-radio value="30days">最近30日</a-radio>
      </a-radio-group>
    </div>

    <!-- 趋势分析 -->
    <a-card class="mb-4">
      <template #title>
        <div class="flex justify-between items-center">
          <span>趋势分析</span>
          <a-tooltip content="刷新">
            <a-button type="text" shape="circle">
              <template #icon><icon-refresh /></template>
            </a-button>
          </a-tooltip>
        </div>
      </template>
      <a-radio-group type="button" v-model="chartType" class="mb-4">
        <a-radio value="ip">IP数</a-radio>
        <a-radio value="pv">浏览量(PV)</a-radio>
        <a-radio value="uv">访客数(UV)</a-radio>
        <a-radio value="newVisitor">新访客数</a-radio>
        <a-radio value="session">会话数</a-radio>
      </a-radio-group>
      <div ref="chartRef" style="width: 100%; height: 300px;"></div>
    </a-card>

    <!-- 上方三个数据区域 -->
    <div class="grid grid-cols-3 gap-4 mb-4">
      <!-- 来路 -->
      <a-card>
        <template #title>
          <div class="flex justify-between items-center">
            <span>来路</span>
            
          </div>
        </template>
        <a-table :data="sourceData" :pagination="false" :bordered="false">
          <template #columns>
            <a-table-column title="来源网站" data-index="site" />
            <a-table-column title="访问次数" data-index="visits" align="right" />
          </template>
        </a-table>
      </a-card>

      <!-- 受访页 -->
      <a-card>
        <template #title>
          <div class="flex justify-between items-center">
            <span>受访页</span>
            
          </div>
        </template>
        <a-table :data="visitedPageData" :pagination="false" :bordered="false">
          <template #columns>
            <a-table-column title="页面地址" data-index="url" />
            <a-table-column title="访问次数" data-index="visits" align="right" />
          </template>
        </a-table>
      </a-card>

      <!-- 入口页 -->
      <a-card>
        <template #title>
          <div class="flex justify-between items-center">
            <span>入口页</span>
            
         
          </div>
        </template>
        <a-table :data="entryPageData" :pagination="false" :bordered="false">
          <template #columns>
            <a-table-column title="入口页面" data-index="url" />
            <a-table-column title="入口次数" data-index="visits" align="right" />
          </template>
        </a-table>
      </a-card>
    </div>
    
    <!-- 下方两个数据区域 -->
    <div class="grid grid-cols-3 gap-4 mb-4">
      <!-- 地域 -->
      <a-card>
        <template #title>
          <div class="flex justify-between items-center">
            <span>地域</span>
            
          </div>
        </template>
        <div>
          <a-table :data="regionData" :pagination="false" :bordered="false">
            <template #columns>
              <a-table-column title="地区" data-index="region">
                <template #cell="{ record }">
                  <div class="flex items-center">
                    <span class="w-4 h-4 mr-2" :style="{ backgroundColor: record.color }"></span>
                    <span>{{ record.region }}</span>
                  </div>
                </template>
              </a-table-column>
              <a-table-column title="访问量" data-index="visits" align="right" />
            </template>
          </a-table>
        </div>
      </a-card>
      
      <!-- 访客IP -->
      <a-card>
        <template #title>
          <div class="flex justify-between items-center">
            <span>访客IP</span>
          </div>
        </template>
        <div>
          <a-table :data="visitorIpData" :pagination="false" :bordered="false">
            <template #columns>
              <a-table-column title="IP" data-index="ip" />
              <a-table-column title="访问次数" data-index="count" align="right" />
            </template>
          </a-table>
        </div>
      </a-card>
      <!-- 终端设备 -->
      <a-card>
        <template #title>
          <div class="flex justify-between items-center">
            <span>终端设备 (UV)</span>
            
          </div>
        </template>
        <div class="flex justify-center items-center">
          <div ref="deviceChartRef" style="width: 400px; height: 320px;"></div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, onBeforeUnmount, watch, nextTick } from 'vue';
import * as echarts from 'echarts';

definePageMeta({
  name: "controlPanel",
  path: "/master/officialWebsiteModule/controlPanel",
});

// 日期类型选择
const dateType = ref('today');

// 图表类型选择
const chartType = ref('ip');



// 模拟数据 - 可替换为实际API调用
const sourceData = ref([
  { site: '直接输入网址/收藏夹', visits: 10 },
  { site: 'www.baidu.com', visits: 4 },
]);

const visitedPageData = ref([
  { url: 'http://web-51.ik/service/about_us', visits: 4 },
  { url: 'https://web-51.ik/service/about_us', visits: 5 },
  { url: 'http://web-51.ik/service/friend_link', visits: 4 },
  { url: 'http://web-51.ik/service/center_log', visits: 1 },
  { url: 'https://web-51.ik/service/friend_link', visits: 1 },
]);

const entryPageData = ref([
  { url: 'http://web-51.ik/service/about_us', visits: 4 },
  { url: 'https://web-51.ik/service/about_us', visits: 5 },
  { url: 'http://web-51.ik/service/friend_link', visits: 4 },
  { url: 'http://web-51.ik/service/center_log', visits: 1 },
  { url: 'https://web-51.ik/service/friend_link', visits: 1 },
]);

// 地域数据
const regionData = ref([
  { region: '北京市', visits: 4, color: '#1D7BFF' },
  { region: '江苏省', visits: 4, color: '#1D7BFF' },
  { region: '广东省', visits: 1, color: '#36CFC9' },
  { region: '浙江省', visits: 1, color: '#36CFC9' },
  { region: '四川省', visits: 1, color: '#36CFC9' },
]);

// 访客IP数据
const visitorIpData = ref([
  { ip: '*************', count: 27 },
  { ip: '*************', count: 18 },
  { ip: '*************', count: 15 },
  { ip: '*************', count: 12 },
  { ip: '*************', count: 10 },
  { ip: '*************', count: 8 },
  { ip: '*************', count: 5 },
]);

// 终端设备数据
const deviceData = ref([
  { name: '安卓', value: 35, itemStyle: { color: '#1D7BFF' } },
  { name: 'iOS', value: 25, itemStyle: { color: '#36CFC9' } },
  { name: 'Windows', value: 22, itemStyle: { color: '#FF7D9E' } },
  { name: 'macOS', value: 10, itemStyle: { color: '#D3E5FF' } },
  { name: 'Linux', value: 5, itemStyle: { color: '#FFC53D' } },
  { name: '其他', value: 3, itemStyle: { color: '#8A8A8A' } },
]);

// 图表相关
const chartRef = ref(null);
const mapChartRef = ref(null);
const deviceChartRef = ref(null);
let chart = null;
let mapChart = null;
let deviceChart = null;

// 初始化所有图表
const initAllCharts = () => {
  initTrendChart();
//   initMapChart();
  initDeviceChart();
};

// 初始化趋势图表
const initTrendChart = () => {
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
    
    // 获取当前日期标签
    const dateLabels = getDateLabels();
    
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: dateLabels
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', 
               '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: dateLabels[0],
          type: 'line',
          stack: 'Total',
          data: [0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          itemStyle: {
            color: '#1D7BFF'
          }
        },
        {
          name: dateLabels[1],
          type: 'line',
          stack: 'Total',
          data: [0, 0, 2, 1, 0, 0, 0, 0, 2, 0, 0, 1, 3, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0],
          itemStyle: {
            color: '#FF7D9E'
          }
        }
      ]
    };

    chart.setOption(option);
    
    // 窗口大小变化时自动调整图表大小
    window.addEventListener('resize', () => {
      chart && chart.resize();
    });
  }
};

// 监听日期类型变化
watch(dateType, (newVal) => {
  // 这里可以根据日期类型调用不同的API获取数据
  console.log('日期类型变化:', newVal);
  // 更新数据后重新渲染图表
  updateChartData();
});

// 监听图表类型变化
watch(chartType, (newVal) => {
  console.log('图表类型变化:', newVal);
  updateChartData();
});

// 更新图表数据
const updateChartData = () => {
  if (!chart) return;
  
  // 模拟不同类型的数据
  let data1 = [];
  let data2 = [];
  
  switch(chartType.value) {
    case 'ip':
      data1 = [0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 4, 0, 0, 0, 0, 0, 0, 0, 0, 0];
      data2 = [0, 0, 2, 1, 0, 0, 0, 0, 2, 0, 0, 1, 3, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0];
      break;
    case 'pv':
      data1 = [1, 2, 1, 0, 0, 0, 0, 2, 1, 3, 2, 0, 1, 2, 5, 1, 0, 0, 0, 0, 0, 0, 0, 0];
      data2 = [0, 1, 3, 2, 0, 0, 1, 1, 3, 1, 0, 2, 4, 1, 0, 2, 0, 1, 0, 0, 2, 0, 0, 0];
      break;
    case 'uv':
      data1 = [0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0];
      data2 = [0, 0, 1, 1, 0, 0, 0, 0, 2, 0, 0, 1, 2, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0];
      break;
    case 'newVisitor':
      data1 = [0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0];
      data2 = [0, 0, 1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
      break;
    case 'session':
      data1 = [0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 0];
      data2 = [0, 0, 1, 1, 0, 0, 0, 0, 1, 0, 0, 1, 2, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0];
      break;
  }
  
  // 根据当前日期类型获取正确的日期标签
  const dateLabels = getDateLabels();
  
  // 更新图表配置
  chart.setOption({
    legend: {
      data: dateLabels
    },
    series: [
      {
        name: dateLabels[0],
        data: data1,
        itemStyle: {
          color: '#1D7BFF'
        }
      },
      {
        name: dateLabels[1],
        data: data2,
        itemStyle: {
          color: '#FF7D9E'
        }
      }
    ]
  });
};

// 获取日期标签
const getDateLabels = () => {
  const today = new Date();
  const todayStr = formatDate(today);
  
  let yesterdayStr = '';
  switch(dateType.value) {
    case 'today':
    case 'yesterday':
      const yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      yesterdayStr = formatDate(yesterday);
      break;
    case '7days':
      const lastWeek = new Date(today);
      lastWeek.setDate(today.getDate() - 7);
      yesterdayStr = formatDate(lastWeek) + ' 至 ' + todayStr;
      break;
    case '30days':
      const lastMonth = new Date(today);
      lastMonth.setDate(today.getDate() - 30);
      yesterdayStr = formatDate(lastMonth) + ' 至 ' + todayStr;
      break;
  }
  
  return [todayStr, yesterdayStr];
};

// 格式化日期
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 初始化地域地图
const initMapChart = () => {
  if (mapChartRef.value) {
    mapChart = echarts.init(mapChartRef.value);
    
    // 使用中国地图数据
    // 注意: 实际开发中可能需要单独引入 ECharts 的中国地图数据
    // 或者使用公司内部地图组件
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}'
      },
      visualMap: {
        min: 0,
        max: 10,
        left: 'left',
        top: 'bottom',
        text: ['高', '低'],
        inRange: {
          color: ['#e0f7fa', '#4fc3f7', '#0288d1']
        },
        show: false
      },
      series: [
        {
          type: 'map',
          map: 'china',
          roam: false,
          label: {
            show: false
          },
          data: [
            { name: '北京', value: 4 },
            { name: '江苏', value: 4 },
            { name: '广东', value: 1 },
            { name: '浙江', value: 1 },
            { name: '四川', value: 1 }
          ],
          emphasis: {
            label: {
              show: true
            },
            itemStyle: {
              areaColor: '#0288d1'
            }
          }
        }
      ]
    };
    
    mapChart.setOption(option);
  }
};

// 初始化设备图表
const initDeviceChart = () => {
  if (deviceChartRef.value) {
    deviceChart = echarts.init(deviceChartRef.value);
    
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      legend: {
        top: '5%',
        left: 'center',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          color: '#666',
          fontSize: 12
        }
      },
      series: [
        {
          name: '终端设备',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['40%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 20,
              fontWeight: 'bold',
              formatter: '{b}\n{c} ({d}%)'
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          data: deviceData.value
        }
      ]
    };
    
    // 添加中心文本（不强调时显示）
    option.graphic = {
      type: 'text',
      left: '40%',
      top: '50%',
      style: {
        text: '设备占比',
        fontSize: 14,
        fontWeight: 'bold',
        textAlign: 'center',
        fill: '#333',
      },
      z: 100
    };
    
    deviceChart.setOption(option);
  }
};

// 挂载后初始化所有图表
onMounted(() => {
  // 使用 nextTick 确保 DOM 已经渲染完毕
  nextTick(() => {
    initAllCharts();
    
    // 添加窗口调整事件
    window.addEventListener('resize', handleResize);
  });
});

// 处理窗口调整
const handleResize = () => {
  chart && chart.resize();
  mapChart && mapChart.resize();
  deviceChart && deviceChart.resize();
};

// 组件销毁前清理所有图表实例
onBeforeUnmount(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  if (mapChart) {
    mapChart.dispose();
    mapChart = null;
  }
  if (deviceChart) {
    deviceChart.dispose();
    deviceChart = null;
  }
  window.removeEventListener('resize', handleResize);
});


</script>

<style scoped>
.container {
  padding: 0;
}
</style>
