<template>
    <div class="ma-content-block p-4">
      <ma-crud :options="crudOptions" :columns="columns" ref="crudRef">
        <!-- 首图 -->
        <template #cover_image="{ record }">
          <a-image
            :src="record.cover_image"
            :width="50"
            :height="50"
            fit="cover"
            show-loader
          />
        </template>
  
        <!-- 状态 -->
        <template #status="{ record }">
          <a-tag :color="record.status ? 'green' : 'gray'">
            {{ record.status ? "显示" : "隐藏" }}
          </a-tag>
        </template>
  
  
        <template #created_at="{ record }">
          <div v-if="record.created_at" v-time="record.created_at"></div>
          <div v-else></div>
        </template>
  
        <template #updated_at="{ record }">
          <div v-if="record.updated_at" v-time="record.updated_at"></div>
          <div v-else></div>
        </template>
  
        <!-- 详细内容 -->
        <template #content="{ record }">
          <a-popover position="right">
            <template #content>
              <div
                class="content-preview"
                style="max-width: 400px; max-height: 300px; overflow: auto"
              >
                <div v-html="record.content"></div>
              </div>
            </template>
            <a-button type="text" size="small"> <icon-eye /> 查看 </a-button>
          </a-popover>
        </template>
  
        <!-- 操作列 -->
        <template #operation="{ record }">
          <a-space>
            <a-link @click="handleEdit(record)">编辑</a-link>
            <a-link @click="handlePreview(record)">预览</a-link>
            <a-popconfirm content="确定删除该产品吗？" @ok="handleDelete(record)">
              <a-link status="danger">删除</a-link>
            </a-popconfirm>
          </a-space>
        </template>
      </ma-crud>
    </div>
  </template>
  
  <script setup>
  import { ref, reactive, onMounted } from "vue";
  import dayjs from "dayjs";
  import { Message } from "@arco-design/web-vue";
  import officialWebsiteModuleApi from "@/api/master/officialWebsiteModule";
  const enterpriseInfoApi = officialWebsiteModuleApi.enterpriseInfo;
  // 定义页面路由元信息
  definePageMeta({
    name: "enterpriseInfoList",
    path: "/master/officialWebsiteModule/enterpriseInfo",
  });
  
  const crudRef = ref();
  const router = useRouter();
  
  // 处理添加产品
  const handleAdd = () => {
    router.push("/master/officialWebsiteModule/productManagement/edit");
  };
  
  // 处理编辑产品
  const handleEdit = (record) => {
    router.push(
      `/master/officialWebsiteModule/productManagement/edit?id=${record.id}`
    );
  };
  
  // 处理预览产品
  const handlePreview = (record) => {
    // 在新窗口打开预览页面
    window.open(`/topic/${record.id}`, "_blank");
  };
  
  // 处理删除产品
  const handleDelete = async (record) => {
    try {
      await topicApi.delete(record.id);
      Message.success("删除成功");
      crudRef.value.refresh();
    } catch (error) {
      Message.error("删除失败：" + (error.message || "未知错误"));
    }
  };
  
  // ma-crud 配置项
  const crudOptions = reactive({
    api: enterpriseInfoApi.getList,
    title: "案例列表",
    searchLabelWidth: "80px",
    rowSelection: { showCheckedAll: true },
    operationColumn: true,
    actionColumn: {
      width: 180,
    },
    formOption: { width: 1000 },
    // 搜索前处理参数
    beforeSearch: (params) => {
      // 这里可以对搜索参数进行处理
      delete params.created_at
      delete params.updated_at
      return params;
    },
  
    // 添加前处理参数
    beforeAdd(params) {
      delete params.created_at
      delete params.updated_at
      return params;
    },
    
    // 编辑前处理参数
    beforeEdit(params) {
      params.status = Number(params.status)
      return params;
    },
  
    add: { 
      show: true,
      api: enterpriseInfoApi.create,
    },
    edit: {
      show: true,
      api: enterpriseInfoApi.update,
    },
    delete: {
      show: true,
      api: enterpriseInfoApi.delete,
    },
  });
  
  // 状态选项
  const statusOptions = [
    { label: "全部", value: "" },
    { label: "显示", value: 1 },
    // { label: "隐藏", value: 0 },
  ];
  
  // 表格列配置
  const columns = reactive([
    {
      title: "标题",
      dataIndex: "title",
      width: 180,
      search: true,
      formType: "input",
      commonRules: [{ required: true, message: '标题必填' }],
    },
    {
      title: "图片",
      dataIndex: "cover_image",
      width: 100,
      align: "center",
      search: false,
      formType: "upload",
      accept: ".jpg,.jpeg,.png",
      limit: 1,
      size:2,
      tip: "请上传图片，建议尺寸676*380px，大小不超过2MB",
      commonRules: [{ required: true, message: '图片必填' }],
    },
    {
      title: "简介",
      dataIndex: "description",
      width: 220,
      search: true,
      formType: "input",
      commonRules: [{ required: true, message: '简介必填' }],
    },
    {
      title: "分类",
      dataIndex: "cate_name",
      width: 80,
      align: "center",
      search: false,
      formType: "select",
      dict: {
        data: [
          { label: "企业文化", value: '企业文化'},
          { label: "公司简介", value: '公司简介' },
          { label: "服务支持", value: '服务支持' },
        ]
      },
      commonRules: [{ required: true, message: '分类必填' }],
    },
    {
      title: "状态",
      dataIndex: "status",
      width: 80,
      search: true,
      formType: "select",
      dict: { data: [...statusOptions].splice(1) },
      commonRules: [{ required: true, message: '状态必填' }],
    },
    {
      title: "排序",
      dataIndex: "sort",
      width: 80,
      search: false,
      formType: "inputNumber",
      addDefaultValue: 0,
    },
    {
      title: "创建时间",
      dataIndex: "created_at",
      width: 160,
      search: false,
      formatter: ({ cellValue }) => {
        return dayjs(cellValue).format("YYYY-MM-DD HH:mm:ss");
      },
      addDisplay: false,
      editDisplay: false,
    },
    {
      title: "更新时间",
      dataIndex: "updated_at",
      width: 160,
      search: false,
      formatter: ({ cellValue }) => {
        return dayjs(cellValue).format("YYYY-MM-DD HH:mm:ss");
      },
      addDisplay: false,
      editDisplay: false,
    },
    {
      title: "详细内容",
      dataIndex: "content",
      width: 100,
      align: "center",
      search: false,
      hide: true,
      formType: "wang-editor",
    },
  ]);
  
  // 生命周期钩子
  onMounted(() => {
    // 初始化
  });
  </script>
  
  <style scoped>
  .ma-content-block {
    background-color: var(--color-bg-2);
    border-radius: var(--border-radius-medium);
  }
  </style>
  