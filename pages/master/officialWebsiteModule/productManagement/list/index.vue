<template>
  <div class="ma-content-block p-4">
    <ma-crud :options="crudOptions" :columns="columns" ref="crudRef">
      <!-- 图片 -->
      <template #image="{ record }">
        <a-image
          :src="record.image"
          :width="50"
          :height="50"
          fit="cover"
          show-loader
        />
      </template>

      <!-- 商品分类 -->
      <template #category_id="{ record }">
        <div>{{ record.category_name }}</div>
      </template>

      <!-- 状态 -->
      <template #is_enabled="{ record }">
        <a-tag :color="record.is_enabled ? 'green' : 'gray'">
          {{ record.is_enabled ? "显示" : "隐藏" }}
        </a-tag>
      </template>

      <!-- 详情图 -->
      <template #detail_images="{ record }">
        <a-link type="primary" size="small" @click="showDetailModal(record)">
          <template #icon>
          <icon-eye />
        </template>
          查看详情
        </a-link>
      </template>

   
    </ma-crud>

    <a-modal v-model:visible="visible" width="800px">
      <template #title>
        商品详情
      </template>
      <div v-html="detailContent"></div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";

// 控制模态框显示
const visible = ref(false);
// 详情内容
const detailContent = ref('');
import officialWebsiteModuleApi from "@/api/master/officialWebsiteModule";
const productManagementApi = officialWebsiteModuleApi.productManagement;
// 本地模拟数据
// 专题分类数据
const mockCategories = [
  { id: 1, name: "电子产品" },
  { id: 2, name: "家居用品" },
  { id: 3, name: "办公用品" },
  { id: 4, name: "运动装备" },
  { id: 5, name: "食品饮料" },
];

// 商品列表数据
const mockTopics = [
  {
    id: 1,
    title: "智能手机 Pro X",
    description: "全新一代智能手机，配备高清摄像头和强大处理器",
    specDescription: "6.5英寸屏幕，8GB内存，256GB存储",
    image: "/assets/placeholder.png",
    detailImages: ["/assets/placeholder.png", "/assets/placeholder.png"],
    categoryId: 1,
    categoryName: "电子产品",
    status: true,
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 7,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 2,
  },
  {
    id: 2,
    title: "人体工学办公椅",
    description: "舒适的办公椅，专为长时间工作设计",
    specDescription: "可调节高度，网面靠背，360度旋转",
    image: "/assets/placeholder.png",
    detailImages: ["/assets/placeholder.png", "/assets/placeholder.png"],
    categoryId: 2,
    categoryName: "家居用品",
    status: true,
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 5,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 1,
  },
  {
    id: 3,
    title: "多功能笔记本电脑支架",
    description: "可调节角度的笔记本支架，减轻颈部疲劳",
    specDescription: "铝合金材质，适配13-17英寸笔记本",
    image: "/assets/placeholder.png",
    detailImages: ["/assets/placeholder.png", "/assets/placeholder.png"],
    categoryId: 3,
    categoryName: "办公用品",
    status: true,
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 3,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 1,
  },
  {
    id: 4,
    title: "专业跑步鞋",
    description: "轻量化设计，提供卓越的缓震和支撑",
    specDescription: "多种尺码可选，透气网面，橡胶大底",
    image: "/assets/placeholder.png",
    detailImages: ["/assets/placeholder.png", "/assets/placeholder.png"],
    categoryId: 4,
    categoryName: "运动装备",
    status: true,
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 2,
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 1,
  },
  {
    id: 5,
    title: "有机坚果礼盒",
    description: "精选多种坚果，营养丰富，送礼佳品",
    specDescription: "500g装，含腰果、杏仁、核桃等",
    image: "/assets/placeholder.png",
    detailImages: ["/assets/placeholder.png", "/assets/placeholder.png"],
    categoryId: 5,
    categoryName: "食品饮料",
    status: true,
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 1,
    updatedAt: Date.now(),
  },
];

// 模拟接口
const topicApi = {
  getList: (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 简单的过滤逻辑
        let filteredData = [...mockTopics];

        if (params) {
          // ID过滤
          if (params.id) {
            filteredData = filteredData.filter((item) =>
              item.id.toString().includes(params.id.toString())
            );
          }

          // 标题过滤
          if (params.title) {
            filteredData = filteredData.filter((item) =>
              item.title.includes(params.title)
            );
          }

          // 分类过滤
          if (params.categoryId) {
            filteredData = filteredData.filter(
              (item) => item.categoryId === params.categoryId
            );
          }

          // 状态过滤
          if (params.status !== undefined && params.status !== "") {
            filteredData = filteredData.filter(
              (item) => item.status === params.status
            );
          }
        }

        // 模拟分页
        const pageSize = params?.pageSize || 10;
        const current = params?.current || 1;
        const total = filteredData.length;
        const start = (current - 1) * pageSize;
        const end = start + pageSize;
        const items = filteredData.slice(start, end);

        resolve({
          data: {
            items,
            total,
            current,
            pageSize,
          },
          success: true,
        });
      }, 300); // 模拟网络延迟
    });
  },
  delete: (id) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 删除操作，实际应用中应调用后端 API
        const index = mockTopics.findIndex((item) => item.id === id);
        if (index !== -1) {
          mockTopics.splice(index, 1);
        }
        resolve({ success: true });
      }, 300);
    });
  },
};

const categoryApi = {
  getList: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            value: mockCategories,
          },
          success: true,
        });
      }, 300);
    });
  },
};

// 定义页面路由元信息
definePageMeta({
  name: "officialWebsiteModule_productManagementList",
  path: "/master/officialWebsiteModule/productManagement/list",
});

const crudRef = ref();
const categoryOptions = ref([]);
const router = useRouter();

// 处理添加产品
const handleAdd = () => {
  router.push("/master/officialWebsiteModule/productManagement/edit");
};

// 处理编辑产品
const handleEdit = (record) => {
  router.push(
    `/master/officialWebsiteModule/productManagement/edit?id=${record.id}`
  );
};

// 处理预览产品
const handlePreview = (record) => {
  // 在新窗口打开预览页面
  window.open(`/topic/${record.id}`, "_blank");
};

// 处理删除产品
const handleDelete = async (record) => {
  try {
    await topicApi.delete(record.id);
    Message.success("删除成功");
    crudRef.value.refresh();
  } catch (error) {
    Message.error("删除失败：" + (error.message || "未知错误"));
  }
};

// 获取商品分类列表
const getCategoryList = async () => {
  try {
    const { data } = await categoryApi.getList();
    categoryOptions.value = data.value || [];
  } catch (error) {
    Message.error("获取商品分类失败：" + (error.message || "未知错误"));
  }
};

// ma-crud 配置项
const crudOptions = reactive({
  api: productManagementApi.list.getList,
  title: "商品列表",
  searchLabelWidth: "80px",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 200,
  add: { show: true, api: productManagementApi.list.create },
  delete: { show: true, api: productManagementApi.list.delete },
  edit: { show: true, api: productManagementApi.list.update },
  export: { show: false },
  formOption: { width: 1000 },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 这里可以对搜索参数进行处理
    return params;
  },
  beforeAdd: (params) => {
    params.is_enabled = Number(params.is_enabled);
    params.category_id = Number(params.category_id);
    return params;
  },
  beforeEdit:(params)=>{
    params.is_enabled = Number(params.is_enabled);
    params.category_id = Number(params.category_id);
    return params;
  }
});

// 状态选项
const statusOptions = [
  { label: "全部", value: "" },
  { label: "显示", value: 1 },
  // { label: "隐藏", value: 0 },
];

// 表格列配置
const columns = reactive([
  {
    title: "图片",
    dataIndex: "image",
    width: 100,
    align: "center",
    search: false,
    formType: "upload",
    commonRules: [{ required: true, message: '图片必填' }],
    size:2,
    accept: ".jpg,.jpeg,.png",
    limit: 1,
    tip: "请上传图片，建议宽度尺寸330*330px，大小不超过2MB",
  },
  {
    title: "名称",
    dataIndex: "name",
    width: 200,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '名称必填' }],
   
  },
  {
    title: "描述",
    dataIndex: "description",
    width: 250,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '描述必填' }],
  },
  {
    title: "规格描述",
    dataIndex: "specification",
    width: 200,
    search: false,
    commonRules: [{ required: true, message: '规格描述必填' }],
  },
  // {
  //   title: "商品分类",
  //   dataIndex: "category_id",
  //   width: 150,
  // },
  {
    title: "商品分类",
    dataIndex: "category_id",
    formType: "tree-select",
    placeholder: "请选择商品分类",
    rules: [{ required: true, message: "请选择商品分类" }],
    commonRules: [{ required: true, message: '商品分类必填' }],
    allowSearch: false,
    allowClear: false,
    dict: {
      data: async () => {
        const res = await productManagementApi.getList();
        const transform = (item) => {
          return {
            value: item.id,
            label: item.name,
            children: item.children ? item.children.map(transform) : undefined,
          };
        };
        return res.data.items.map(transform);
      },
    },
    search: true,
  },
  {
    title: "状态",
    dataIndex: "is_enabled",
    width: 100,
    search: true,
    formType: "select",
    dict: { data: statusOptions.slice(1) },
    commonRules: [{ required: true, message: '状态必填' }],
  },
  {
    title: "详情",
    dataIndex: "detail_images",
    width: 120,
    align: "center",
    search: false,
    formType: "wang-editor",
  },
]);

// 显示详情模态框
const showDetailModal = (record) => {
  detailContent.value = record.detail_images;
  visible.value = true;
};

// 生命周期钩子
onMounted(() => {
  getCategoryList();
});
</script>

<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: var(--border-radius-medium);
}
</style>
