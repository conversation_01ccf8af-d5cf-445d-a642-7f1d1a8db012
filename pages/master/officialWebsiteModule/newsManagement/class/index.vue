<template>
  <div class="ma-content-block lg:flex justify-between p-4 gap-4">
    <div
      class="lg:w-4/12 w-full mb-4 lg:mb-0 border rounded-md p-3 bg-white dark:bg-blackdark-5 dark:border-blackdark-3 shadow"
    >
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-lg font-semibold">新闻分类</h3>
        <a-button type="primary" size="small" @click="handleAddRoot">
          <template #icon><icon-plus /></template> 添加分类
        </a-button>
      </div>
      <a-input
        v-model="filterText"
        placeholder="搜索分类名称"
        allow-clear
        class="mb-2"
      >
        <template #suffix>
          <icon-search />
        </template>
      </a-input>
      <div class="tree-container customer-scrollbar">
        <a-tree
          ref="treeRef"
          :data="filteredTreeData"
          :field-names="{ title: 'name', key: 'id', children: 'children' }"
          :show-line="true"
          :block-node="true"
          @select="handleTreeSelect"
          :filter-node-method="filterNode"
        >
          <template #title="nodeData">
            <span class="truncate">{{
              (nodeData && nodeData.name) || "未命名分类"
            }}</span>
          </template>
          <template #extra="nodeData">
            <a-space size="mini">
              <a-button
                type="text"
                size="mini"
                @click.stop="handleEdit(nodeData)"
              >
                <template #icon><icon-edit /></template>
              </a-button>
              <a-popconfirm
                content="确定要删除此分类及其子分类吗?"
                @ok="handleDelete(nodeData)"
              >
                <a-button type="text" status="danger" size="mini" @click.stop>
                  <template #icon><icon-delete /></template>
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-tree>
      </div>
    </div>

    <div
      class="lg:w-8/12 w-full border rounded-md p-4 bg-white dark:bg-blackdark-5 dark:border-blackdark-3 shadow"
    >
      <div class="text-lg font-semibold mb-4">
        {{
          formMode === "add"
            ? "新增分类详情"
            : formMode === "edit"
            ? "编辑分类详情"
            : "分类详情"
        }}
      </div>
      <ma-form
        ref="maFormRef"
        v-model="formModel"
        :columns="columns"
        :options="formOptions"
        auto-label-width
        @submit="handleSubmit"
      >
        <!-- 关联属性模板 -->
        <template #form-template_id>
          <a-select
            v-model="formModel.template_id"
            placeholder="请选择关联属性模板"
            allow-clear
            allow-search
          >
            <a-option value="0">无</a-option>
            <a-option value="1">手机属性</a-option>
            <a-option value="2">服装属性</a-option>
            <a-option value="3">家具属性</a-option>
            <a-option value="4">电脑属性</a-option>
          </a-select>
        </template>
      </ma-form>
      <div v-if="formMode === 'view'" class="p-4 text-center text-gray-500">
        请在左侧选择新闻分类进行编辑，或点击按钮添加新分类。
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick, watch } from "vue";
import MaForm from "~/components/base/ma-form/index.vue";
import { Message } from "@arco-design/web-vue";
import {
  IconPlus,
  IconEdit,
  IconDelete,
  IconSearch,
} from "@arco-design/web-vue/es/icon";
import tool from "@/utils/tool";
import officialWebsiteModuleApi from "@/api/master/officialWebsiteModule";
const newsManagementtApi = officialWebsiteModuleApi.newsManagement;
definePageMeta({
  name: "newsManagementClass",
  path: "/master/officialWebsiteModule/newsManagement/class",
});
const treeRef = ref();
const maFormRef = ref();
const filterText = ref("");
const formMode = ref("view");
const classData = ref([]);
const filteredTreeData = ref([]);

const initialForm = {
  id: undefined,
  name: "",
  is_enabled: "1",
  description: "",
};
const formModel = ref({ ...initialForm });
formMode.value = "add";

const formOptions = reactive({
  labelWidth: "130px",
  showButtons: true,
  submitText: "保存",
  resetText: "重置",
});

const columns = reactive([
  {
    title: "分类名称",
    dataIndex: "name",
    formType: "input",
    rules: [{ required: true, message: "分类名称必填" }],
    labelWidth: "135px",
  },

  // {
  //     title: '分类图片',
  //     dataIndex: 'files',
  //     formType: 'upload',
  //     labelWidth: '135px'
  // },
  {
    title: "分类描述",
    dataIndex: "description",
    formType: "textarea",
    labelWidth: "135px",
  },

  {
    title: "状态",
    dataIndex: "is_enabled",
    formType: "radio",
    addDefaultValue: 1,
    dict: {
      data: [
        { label: "启用", value: "1" },
        // { label: "禁用", value: "0" },
      ],
    },
    labelWidth: "135px",
  },
  // 添加关联属性模板
  //    {
  //         title: '关联属性模板',
  //         dataIndex: 'template_id',
  //         formType: 'select',
  //         addDefaultValue: '0',
  //         labelWidth: '135px',
  //         dict: { data: [{ label: '启用', value: 1 }, { label: '禁用', value: 0 }] },
  //         labelWidth: '135px',
  //     },
]);

const getList = () => {
  newsManagementtApi.getList().then((res) => {
    console.log("🚀 ~ file: index.vue:166 ~ res:", res);
    classData.value = res.data.items;
    filteredTreeData.value = res.data.items;
  });
};
onMounted(() => {
  getList();
});

watch(filterText, (val) => {
  if (val) {
    // 搜索模式：过滤树数据
    filteredTreeData.value = filterTreeData(classData.value, val);
  } else {
    // 重置为原始数据
    filteredTreeData.value = classData.value;
  }
});

// 递归过滤树数据，保留匹配节点及其祖先节点
const filterTreeData = (data, keyword) => {
  if (!data || !data.length || !keyword) return data;

  const result = [];

  for (const node of data) {
    // 创建节点的副本以避免修改原始数据
    const newNode = { ...node };

    // 检查节点自身是否匹配
    const isMatch =
      node.name && node.name.toLowerCase().includes(keyword.toLowerCase());

    // 递归检查子节点
    let filteredChildren = [];
    if (node.children && node.children.length) {
      filteredChildren = filterTreeData(node.children, keyword);
    }

    // 如果节点本身匹配或者它有匹配的子节点，则保留该节点
    if (isMatch || filteredChildren.length > 0) {
      // 如果有匹配的子节点，添加到节点的children属性
      if (filteredChildren.length > 0) {
        newNode.children = filteredChildren;
      }
      result.push(newNode);
    }
  }

  return result;
};

// 过滤单个节点的方法（用于tree组件的filter-node-method属性）
const filterNode = (value, data) => {
  if (!value) return true;
  return data && data.name
    ? data.name.toLowerCase().includes(value.toLowerCase())
    : false;
};

const handleTreeSelect = (selectedKeys, { node, e }) => {
  if (selectedKeys.length > 0 && node) {
    nextTick(() => {
      // 复制节点数据并确保is_enabled是数字类型
      const nodeData = { ...node.dataRef };
      if (nodeData.is_enabled !== undefined) {
        nodeData.is_enabled = String(nodeData.is_enabled);
      }
      formModel.value = nodeData;
      formMode.value = "edit";
      maFormRef.value?.clearValidate();
    });
  } else {
    resetFormView();
  }
};

const handleAddRoot = () => {
  resetFormView();
  formModel.value.goodsParentCategoryId = 0;
  formMode.value = "add";
};

const handleAddChild = (parentData) => {
  resetFormView();
  formModel.value.goodsParentCategoryId = parentData.id;
  formMode.value = "add";
};

const resetFormView = () => {
  formModel.value = { ...initialForm };
  formMode.value = "view";
  // 注意：ma-form组件可能没有提供resetFields和clearValidate方法
  // 直接重置表单数据即可
};

const handleSubmit = (formData) => {
  // 确保is_enabled是数字类型
  if (formData.is_enabled !== undefined) {
    formData.is_enabled = Number(formData.is_enabled);
  }

  if (formMode.value === "add") {
    newsManagementtApi
      .create(formData)
      .then((res) => {
        Message.success("分类添加成功");
        handleAddRoot();
        getList();
      })
      .catch((err) => {
        Message.error("分类添加失败");
      });
  } else if (formMode.value === "edit") {
    newsManagementtApi
      .update(formData.id, formData)
      .then((res) => {
        Message.success("分类更新成功");
        handleAddRoot();
        getList();
      })
      .catch((err) => {
        Message.error("分类更新失败");
      });
  }
};
const handleDelete = (data) => {
  // 调用API删除分类
  newsManagementtApi
    .delete(data.id)
    .then((res) => {
      if (res.code === 200) {
        Message.success(`分类 "${data.name}" 及其子分类已删除`);
        getList(); // 刷新分类列表
        resetFormView();
      } else {
        Message.error(res.message || "删除失败");
      }
    })
    .catch((error) => {
      Message.error("删除失败：" + (error.message || "未知错误"));
    });
};
const findNodeById = (tree, id) => {
  for (const node of tree) {
    if (node.id === id) {
      return node;
    }
    if (node.children) {
      const found = findNodeById(node.children, id);
      if (found) {
        return found;
      }
    }
  }
  return null;
};

const handleEdit = (nodeData) => {
  if (nodeData) {
    nodeData.is_enabled = String(nodeData.is_enabled);
    formModel.value = { ...nodeData };
    formMode.value = "edit";

    // 注意：ma-form组件可能没有提供clearValidate方法
  }
};
</script>

<style scoped>
.tree-container {
  height: 100%;
  max-height: calc(100vh - 240px);
  overflow: auto;
}

:deep(.arco-tree-node-selected) .arco-tree-node-title,
:deep(.arco-tree-node-selected) .arco-tree-node-title:hover {
  background-color: rgb(var(--primary-1));
  color: rgb(var(--primary-6));
}

:deep(.arco-tree-node-title-hover) {
  background-color: var(--color-fill-1);
}

:deep(.arco-tree-node-extra) {
  display: flex;
}
</style>
