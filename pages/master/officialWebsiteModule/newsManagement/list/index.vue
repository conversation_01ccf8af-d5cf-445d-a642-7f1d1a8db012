<template>
  <div class="ma-content-block p-4">
    <ma-crud
      :options="crudOptions"
      :columns="columns"
      ref="crudRef"
    >
      <!-- 专题分类 -->
      <template #news_category_id="{ record }">
        <div>{{ record.category_name }}</div>
      </template>

      <template #image="{ record }">
        <a-image
          width="100"
          height="100"
          fit="cover"
          :src="record.image"
        />
      </template>

      <!-- 状态 -->
      <template #is_enabled="{ record }">
        <a-tag :color="record.is_enabled ? 'green' : 'gray'">
          {{ record.is_enabled ? "显示" : "隐藏" }}
        </a-tag>
      </template>

      <!-- 创建时间 -->
      <template #created_at="{ record }">
        <div v-time="record.created_at" v-if="record.created_at"></div>
        <div v-else></div>
      </template>

      <!-- 更新时间 -->
      <template #updated_at="{ record }">
        <div v-time="record.updated_at" v-if="record.updated_at"></div>
        <div v-else></div>
      </template>

      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space>
          <a-link @click="handleEdit(record)">编辑</a-link>
          <a-link @click="handlePreview(record)">预览</a-link>
          <a-popconfirm
            content="确定删除该专题吗？"
            @ok="handleDelete(record)"
          >
            <a-link status="danger">删除</a-link>
          </a-popconfirm>
        </a-space>
      </template>

      <!-- 自定义分类搜索 -->
      <template #search-categoryId="{ searchForm }">
        <a-select
          v-model="searchForm.categoryId"
          placeholder="请选择专题分类"
          allow-clear
        >
          <a-option
            v-for="item in categoryOptions"
            :key="item.id"
            :value="item.id"
          >
            {{ item.name }}
          </a-option>
        </a-select>
      </template>

      <template #operationBeforeExtend="{ record }">
        <a-link type="primary" @click="handleEdit(record)"> <icon-edit />编辑</a-link>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import officialWebsiteModuleApi from '@/api/master/officialWebsiteModule';
const newsManagementtApi = officialWebsiteModuleApi.newsManagement;


// 定义页面路由元信息
definePageMeta({
  name: "newsManagementList",
  path: "/master/officialWebsiteModule/newsManagement/list",
});

const crudRef = ref();
const categoryOptions = ref([]);
const router = useRouter();

// 处理添加专题
const handleAdd = () => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(router, "/master/officialWebsiteModule/newsManagement/list/details/add");
  });
};

// 处理编辑专题
const handleEdit = (record) => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(router, `/master/officialWebsiteModule/newsManagement/list/details/${record.id}`);
  });
};

// 处理预览专题
const handlePreview = (record) => {
  // 在新窗口打开预览页面
  window.open(`/topic/${record.id}`, '_blank');
};

// 处理删除专题
const handleDelete = async (record) => {
  try {
    await topicApi.delete(record.id);
    Message.success('删除成功');
    crudRef.value.refresh();
  } catch (error) {
    Message.error('删除失败：' + (error.message || '未知错误'));
  }
};



// ma-crud 配置项
const crudOptions = reactive({
  api: newsManagementtApi.list.getList,
  title: "专题列表",
  searchLabelWidth: "80px",
  operationColumn: true,
  operationColumnWidth: 200,
  rowSelection: { showCheckedAll: true },
  add: { show: true, text: "新增", action: handleAdd },
  delete: { show: true, api: newsManagementtApi.list.delete },
  export: { show: false },
  actionColumn: {
    width: 180,
  },
  table: {
    rowKey: "id",
    border: true,
    size: "medium",
  },
  search: {
    collapsed: false,
    showCollapse: true,
    collapsePosition: "left",
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 这里可以对搜索参数进行处理
    return params;
  },
});

// 状态选项
const statusOptions = [
  { label: "全部", value: "" },
  { label: "显示", value: 1 },
  // { label: "隐藏", value: 0 },
];

// 表格列配置
const columns = reactive([
  {
    title: "标题",
    dataIndex: "title",
    width: 200,
    search: true,
    formType: "input",
  },
  {
    title: "首图",
    dataIndex: "image",
    width: 120,
    search: false,
  },
  {
    title: "简介",
    dataIndex: "summary",
    width: 180,
    search: false,
    ellipsis: true,
  },
  {
    title: "新闻分类",
    dataIndex: "news_category_id",
    width: 150,
    search: true,
    formType: "select",
    dict: {
      data: async () => {
        const res = await newsManagementtApi.getList();
        const transform = (item) => {
          
          return {
            value: item.id,
            label: item.name,
          };
        };
        return res.data.items.map(transform);
      },
    },
  },
  {
    title: "状态",
    dataIndex: "is_enabled",
    width: 100,
    search: true,
    formType: "select",
    dict: { data: statusOptions },
  },
  {
    title: "排序",
    dataIndex: "sort_order",
    width: 80,
    search: false,
  },
  {
    title: "创建时间",
    dataIndex: "created_at",
    width: 160,
    search: false,
  },
  {
    title: "更新时间",
    dataIndex: "updated_at",
    width: 160,
    search: false,
  },
  {
    title: "详情",
    dataIndex: "detail",
    width: 120,
    search: false,
    hide: true,
  },
]);

// 生命周期钩子
onMounted(() => {
 
});
</script>

<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: var(--border-radius-medium);
}
</style>