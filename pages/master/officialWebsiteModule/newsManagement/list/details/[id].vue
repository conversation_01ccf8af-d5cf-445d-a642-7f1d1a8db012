<template>
  <div class="ma-content-block p-4">
    <a-card :title="formTitle" :bordered="false">
      <ma-form
        ref="formRef"
        :columns="columns"
        :options="formOptions"
        v-model="formData"
        @submit="handleSubmit"
      >
      </ma-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRoute, useRouter } from "vue-router";
import { closeTag, modifyTag } from "~/utils/common.js";
import officialWebsiteModuleApi from "@/api/master/officialWebsiteModule";
const newsManagementtApi = officialWebsiteModuleApi.newsManagement;
// 路由相关
const route = useRoute();
const router = useRouter();
const id = route.params.id;
// 判断是否为编辑模式
const isEdit = id != "add";

if (isEdit) {
  const currentTag = {
    path: route.fullPath,
    name: route.name,
  };
  modifyTag(currentTag, "编辑新闻");
}

// 页面状态
const loading = ref(false);
const formRef = ref(null);
const formTitle = computed(() => (isEdit ? "编辑新闻" : "新增新闻"));

// 图片上传相关
const fileList = ref([]);
const uploadUrl = "/api/upload"; // 替换为实际的上传API地址

// 新闻类型数据
const newsTypeOptions = [
  { label: "企业新闻", value: 1 },
  { label: "行业新闻", value: 2 },
];

// 状态选项
const statusOptions = [
  { label: "显示", value: 1 },
  // { label: "隐藏", value: 0 },
];

// 表单数据
const formData = reactive({

});

// 表单配置项
const formOptions = reactive({
  submitText: "保存",
  resetText: "重置",
  labelWidth: 100,
  layout: "horizontal",
  submitShowBtn: true,
  resetShowBtn: true,
});

// 表单字段配置
const columns = [
  {
    title: "标题",
    dataIndex: "title",
    formType: "input",
    rules: [{ required: true, message: "请输入新闻标题" }],
    colProps: { span: 24 },
  },
  {
    title: "首图",
    dataIndex: "image",
    formType: "upload",
    type: "image",
    multiple: false,
    limit: 1,
    size:2,
    accept: ".jpg,.jpeg,.png",
    tip: "最多上传1张图片，建议尺寸894*320px，大小不超过2MB",
    returnType: "url",
    rules: [{ required: true, message: "请上传至少一张图片" }],

  },
  {
    title: "简介",
    dataIndex: "summary",
    formType: "textarea",
    formProps: {
      placeholder: "请输入新闻简介",
      rows: 4,
      maxLength: 500,
      showWordLimit: true,
    },
    colProps: { span: 24 },
    rules: [{ required: true, message: "请输入简介" }],
  },
  {
    title: "新闻类型",
    dataIndex: "news_category_id",
    formType: "select",
    dict: {
      data: async () => {
        const res = await newsManagementtApi.getList();
        const transform = (item) => {
          return {
            value: item.id,
            label: item.name,
          };
        };
        return res.data.items.map(transform);
      },
    },
    rules: [{ required: true, message: "请选择新闻类型" }],
    colProps: { span: 24 },
  },
  {
    title: "状态",
    dataIndex: "is_enabled",
    formType: "select",
    dict: {
      data: [
        { label: "显示", value: 1 },
        // { label: "隐藏", value: 0 },
      ],
    },
    colProps: { span: 24 },
    rules: [{ required: true, message: "请选择状态" }],
  },
  {
    title: "排序",
    dataIndex: "sort_order",
    formType: "input-number",
    formProps: {
      min: 0,
      max: 9999,
      step: 1,
      placeholder: "数值越小越靠前",
    },
    colProps: { span: 24 },
    rules: [{ required: true, message: "请输入排序" }],
  },
  {
    title: "详情内容",
    dataIndex: "content",
    formType: "wang-editor",
    placeholder: "请输入详情内容",
    height: 300,
  },
];

// 图片上传处理
const handleImageChange = (fileList) => {
  if (fileList.fileList && fileList.fileList.length > 0) {
    const file = fileList.fileList[fileList.fileList.length - 1];
    if (file.response && file.response.success) {
      // 假设上传接口返回 {success: true, data: {url: '图片地址'}}
      formData.coverImage = file.response.data.url;
    }
  } else {
    formData.coverImage = "";
  }
};

// 获取新闻详情
const fetchNewsDetail = async () => {
  if (!isEdit) return;

  try {
    loading.value = true;
    
    // 判断当前地址，如果ID不是add则调用获取详情接口
    if (id !== 'add') {
      const res = await newsManagementtApi.list.getList({}, id);
      if (res && res.data) {
        const newsData = res.data;
        newsData.is_enabled = String(newsData.is_enabled)
        // 填充表单数据
        Object.keys(formData).forEach((key) => {
          if (newsData[key] !== undefined) {
            formData[key] = newsData[key];
          }
        });
      }
    } 
    loading.value = false;
  } catch (error) {
    Message.error("获取新闻详情失败");
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async (values) => {
  try {
    loading.value = true;
    let data = {...values}
    data.is_enabled = Number(data.is_enabled)
    data.news_category_id = Number(data.news_category_id)
    let res;

    if (!isEdit) {
      // 创建新闻
      res = await newsManagementtApi.list.create(data);
      Message.success("新增新闻成功");
    } else {
      // 更新新闻
      res = await newsManagementtApi.list.update(id, data);
      Message.success("编辑新闻成功");
    }

    loading.value = false;
    // 返回列表页
    router.push("/master/officialWebsiteModule/newsManagement/list");
  } catch (error) {
    Message.error(error?.message || "保存新闻失败");
    loading.value = false;
  }
};

// 页面加载时获取详情
onMounted(() => {
  fetchNewsDetail();
});

// 页面元数据
definePageMeta({
  name: "newsManagementListDetail",
  path: "/master/officialWebsiteModule/newsManagement/list/details/:id",
});
</script>
