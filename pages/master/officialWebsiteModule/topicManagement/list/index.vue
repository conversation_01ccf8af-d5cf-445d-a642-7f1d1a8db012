<template>
  <div class="ma-content-block p-4">
    <ma-crud
      :options="crudOptions"
      :columns="columns"
      ref="crudRef"
    >
      <!-- 专题分类 -->
      <template #categoryId="{ record }">
        <div>{{ record.categoryName }}</div>
      </template>

      <!-- 状态 -->
      <template #status="{ record }">
        <a-tag :color="record.status ? 'green' : 'gray'">
          {{ record.status ? "显示" : "隐藏" }}
        </a-tag>
      </template>

      <!-- 创建时间 -->
      <template #createdAt="{ record }">
        <div v-time="record.createdAt" v-if="record.createdAt"></div>
        <div v-else></div>
      </template>

      <!-- 更新时间 -->
      <template #updatedAt="{ record }">
        <div v-time="record.updatedAt" v-if="record.updatedAt"></div>
        <div v-else></div>
      </template>

      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space>
          <a-link @click="handleEdit(record)">编辑</a-link>
          <a-link @click="handlePreview(record)">预览</a-link>
          <a-popconfirm
            content="确定删除该专题吗？"
            @ok="handleDelete(record)"
          >
            <a-link status="danger">删除</a-link>
          </a-popconfirm>
        </a-space>
      </template>

      <!-- 自定义分类搜索 -->
      <template #search-categoryId="{ searchForm }">
        <a-select
          v-model="searchForm.categoryId"
          placeholder="请选择专题分类"
          allow-clear
        >
          <a-option
            v-for="item in categoryOptions"
            :key="item.id"
            :value="item.id"
          >
            {{ item.name }}
          </a-option>
        </a-select>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";

// 本地模拟数据
// 专题分类数据
const mockCategories = [
  { id: 1, name: '产品动态' },
  { id: 2, name: '公司新闻' },
  { id: 3, name: '行业资讯' },
  { id: 4, name: '促销活动' },
  { id: 5, name: '技术分享' }
];

// 专题列表数据
const mockTopics = [
  { 
    id: 1, 
    title: '公司上线新产品发布会', 
    categoryId: 1, 
    categoryName: '产品动态',
    status: true, 
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 7, 
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 2
  },
  { 
    id: 2, 
    title: '关于我们公司荣获技术创新奖的公告', 
    categoryId: 2, 
    categoryName: '公司新闻',
    status: true, 
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 5, 
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 1
  },
  { 
    id: 3, 
    title: '2025年行业发展趋势分析', 
    categoryId: 3, 
    categoryName: '行业资讯',
    status: true, 
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 3, 
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 1
  },
  { 
    id: 4, 
    title: '夏季促销活动大礼包', 
    categoryId: 4, 
    categoryName: '促销活动',
    status: false, 
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 2, 
    updatedAt: Date.now() - 1000 * 60 * 60 * 24 * 1
  },
  { 
    id: 5, 
    title: '技术分享会：人工智能在电商领域的应用', 
    categoryId: 5, 
    categoryName: '技术分享',
    status: true, 
    createdAt: Date.now() - 1000 * 60 * 60 * 24 * 1, 
    updatedAt: Date.now()
  },
];

// 模拟接口
const topicApi = {
  getList: (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 简单的过滤逻辑
        let filteredData = [...mockTopics];
        
        if (params) {
          // ID过滤
          if (params.id) {
            filteredData = filteredData.filter(item => 
              item.id.toString().includes(params.id.toString())
            );
          }
          
          // 标题过滤
          if (params.title) {
            filteredData = filteredData.filter(item => 
              item.title.includes(params.title)
            );
          }
          
          // 分类过滤
          if (params.categoryId) {
            filteredData = filteredData.filter(item => 
              item.categoryId === params.categoryId
            );
          }
          
          // 状态过滤
          if (params.status !== undefined && params.status !== '') {
            filteredData = filteredData.filter(item => 
              item.status === params.status
            );
          }
        }
        
        // 模拟分页
        const pageSize = params?.pageSize || 10;
        const current = params?.current || 1;
        const total = filteredData.length;
        const start = (current - 1) * pageSize;
        const end = start + pageSize;
        const items = filteredData.slice(start, end);
        
        resolve({
          data: {
            items,
            total,
            current,
            pageSize
          },
          success: true
        });
      }, 300); // 模拟网络延迟
    });
  },
  delete: (id) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 删除操作，实际应用中应调用后端 API
        const index = mockTopics.findIndex(item => item.id === id);
        if (index !== -1) {
          mockTopics.splice(index, 1);
        }
        resolve({ success: true });
      }, 300);
    });
  }
};

const categoryApi = {
  getList: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          data: {
            value: mockCategories
          },
          success: true
        });
      }, 300);
    });
  }
};

// 定义页面路由元信息
definePageMeta({
  name: "topicManagementList",
  path: "/master/officialWebsiteModule/topicManagement/list",
});

const crudRef = ref();
const categoryOptions = ref([]);
const router = useRouter();

// 处理添加专题
const handleAdd = () => {
  router.push('/master/officialWebsiteModule/topicManagement/edit');
};

// 处理编辑专题
const handleEdit = (record) => {
  router.push(`/master/officialWebsiteModule/topicManagement/edit?id=${record.id}`);
};

// 处理预览专题
const handlePreview = (record) => {
  // 在新窗口打开预览页面
  window.open(`/topic/${record.id}`, '_blank');
};

// 处理删除专题
const handleDelete = async (record) => {
  try {
    await topicApi.delete(record.id);
    Message.success('删除成功');
    crudRef.value.refresh();
  } catch (error) {
    Message.error('删除失败：' + (error.message || '未知错误'));
  }
};

// 获取专题分类列表
const getCategoryList = async () => {
  try {
    const { data } = await categoryApi.getList();
    categoryOptions.value = data.value || [];
  } catch (error) {
    Message.error('获取专题分类失败：' + (error.message || '未知错误'));
  }
};

// ma-crud 配置项
const crudOptions = reactive({
  api: topicApi.getList,
  title: "专题列表",
  searchLabelWidth: "80px",
  rowSelection: { showCheckedAll: true },
  add: { show: true, text: "新增专题", handler: handleAdd },
  export: { show: false },
  actionColumn: {
    width: 180,
  },
  table: {
    rowKey: "id",
    border: true,
    size: "medium",
  },
  search: {
    collapsed: false,
    showCollapse: true,
    collapsePosition: "left",
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 这里可以对搜索参数进行处理
    return params;
  },
});

// 状态选项
const statusOptions = [
  { label: "全部", value: "" },
  { label: "显示", value: true },
  { label: "隐藏", value: false },
];

// 表格列配置
const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    width: 80,
    align: "center",
    search: true,
    formType: "input",
  },
  {
    title: "标题",
    dataIndex: "title",
    width: 250,
    search: true,
    formType: "input",
  },
  {
    title: "专题分类",
    dataIndex: "categoryId",
    width: 150,
    search: true,
    formType: "select",
    dict: {
      data: () => categoryOptions.value.map(item => ({
        label: item.name,
        value: item.id
      }))
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 100,
    search: true,
    formType: "select",
    dict: { data: statusOptions },
  },
  {
    title: "创建时间",
    dataIndex: "createdAt",
    width: 180,
    search: {
      show: true,
      component: "a-range-picker",
    },
  },
  {
    title: "更新时间",
    dataIndex: "updatedAt",
    width: 180,
    search: {
      show: false,
    },
  },
]);

// 生命周期钩子
onMounted(() => {
  getCategoryList();
});
</script>

<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: var(--border-radius-medium);
}
</style>