<template>
  <div class="ma-content-block p-4">
    <a-card title="基础信息管理" :bordered="false">
      <!-- 基础信息表单 -->
      <ma-form
        v-model="form"
        :columns="columns"
        :options="options"
        @submit="handleSubmit"
        :loading="loading"
      >
        <!-- 自定义插槽 -->
        <template #form-now_type="component">
          <div class="flex items-center" style="width: 100%">
            <a-select
              v-model="form.now_type"
              placeholder="请选择官网模板"
              style="width: 100%"
              :options="component.dict.data"
              :allow-clear="true"
            >
            </a-select>
            <a-button type="primary" class="ml-2" @click="previewTemplate"
              >预览模板</a-button
            >
          </div>
        </template>
      </ma-form>
    </a-card>

    <a-modal
      v-model:visible="visible"
      @ok="handleOk"
      @cancel="handleCancel"
      :width="1000"
    >
      <template #title> 模板预览 </template>
      <div>
        <a-image
          :src="TemplatePicture[form.now_type]"
          :preview="false"
          :width="'100%'"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import officialWebsiteModuleApi from "~/api/master/officialWebsiteModule.js";

definePageMeta({
  name: "officialWebsiteModule-basisManagement",
  path: "/master/officialWebsiteModule/basisManagement",
});
const TemplatePicture = ref({
  1: "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/ead6ea4e209b26fb71c54281c4c83592.png",
  2: "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/973bdbc77b63216fb0f5d18927b32f72.png",
  3: "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/8fd6732432a3e581eaf4b5cd283228b4.png",
  4: "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/8ecf3b4de4a783173c99a4f62fc84d89.png",
});
// 模板预览弹窗显示状态
const visible = ref(false);

// 预览模板
const previewTemplate = () => {
  if (!form.now_type) {
    Message.error("请选择官网模板");
    return;
  }
  visible.value = true;
};

// 处理弹窗确认
const handleOk = () => {
  visible.value = false;
};

// 处理弹窗取消
const handleCancel = () => {
  visible.value = false;
};

// 表单数据
const form = reactive({
  company_name: "",
  company_address: "",
  logo_url: "",
  banner_url: [],
  vision: "",
  mission: "",
  core_values: "",
  icp_number: "",
  copyright: "",
  service_phone: "",
  wechat_qrcode: "",
});

// 表单加载状态
const loading = ref(false);

// 获取基础信息数据
const fetchBasisData = async () => {
  loading.value = true;
  try {
    const res = await officialWebsiteModuleApi.basisManagement.getData();
    console.log("🚀 ~ file: index.vue:53 ~ res:", res);
    if (res && res.data) {
      // 假设API成功时返回 { code: 200, data: {...}, message: '...'}
      Object.keys(form).forEach((key) => {
        if (res.data[key] !== undefined) {
          if (key == "banner_url") {
            form[key] = res.data[key].split(",");
          } else {
            form[key] = res.data[key];
          }
          // form[key] = res.data[key];
        }
      });
      Message.success("基础信息加载成功");
    } else {
      Message.error(res.message || "获取基础信息失败");
    }
  } catch (error) {
    console.error("获取基础信息失败:", error);
    Message.error("获取基础信息失败，请稍后再试");
  } finally {
    loading.value = false;
  }
};

// 在组件挂载时加载数据
onMounted(() => {
  fetchBasisData();
});

// ma-form 组件的配置选项
const options = reactive({
  // 表单布局
  labelWidth: 100,
  labelAlign: "right",
  layout: "horizontal",
  // 每行显示两列
  column: 2,
  // 按钮配置
  showButtons: true,
  submitText: "保存设置",
  submitType: "primary",
  // 自定义类名
  customClass: "website-base-form",
});

// 表单列定义
// 使用formType: 'grid'来实现布局控制
const columns = reactive([
  // 使用grid容器进行布局控制
  {
    formType: "grid",
    gutter: 16, // 列间距
    cols: [
      {
        span: 12, // 占据一半宽度
        formList: [
          {
            title: "公司名称",
            dataIndex: "company_name",
            formType: "input",
            commonRules: [{ required: true, message: "请输入公司名称" }],
            placeholder: "请输入公司名称",
          },
        ],
      },
      {
        span: 12, // 占据一半宽度
        formList: [
          {
            title: "公司地址",
            dataIndex: "company_address",
            formType: "textarea",
            placeholder: "请输入公司地址",
            componentProps: {
              autoSize: { minRows: 2, maxRows: 4 },
            },
          },
        ],
      },
    ],
  },

  // 第二行：公司地址和Banner
  {
    formType: "grid",
    gutter: 16,
    cols: [
      {
        span: 12,
        formList: [
          {
            title: "公司LOGO",
            dataIndex: "logo_url",
            formType: "upload",
            type: "image",
            multiple: false,
            limit: 1,
            accept: ".jpg,.jpeg,.png",
            tip: "最多上传1张图片，建议尺寸124*50px，大小不超过2MB",
            returnType: "url",
            rules: [{ required: true, message: "请上传公司LOGO" }],
            size: 2,
          },
        ],
      },
      {
        span: 12,
        formList: [
          {
            title: "Banner",
            dataIndex: "banner_url",
            formType: "upload",
            type: "image",
            multiple: true,
            limit: 5,
            accept: ".jpg,.jpeg,.png,.mp4",
            tip: "最多上传5张图片/视频，建议尺寸1920*960px，大小不超过200MB",
            returnType: "url",
            rules: [{ required: true, message: "请上传首页Banner" }],
            size: 200,
          },
        ],
      },
    ],
  },

  // 第三行：企业文化
  {
    formType: "grid",
    gutter: 16,
    cols: [
      {
        span: 12,
        formList: [
          {
            title: "企业愿景",
            dataIndex: "vision",
            formType: "textarea",
            placeholder: "请输入企业愿景",
            componentProps: {
              autoSize: { minRows: 2, maxRows: 4 },
            },
          },
        ],
      },
      {
        span: 12,
        formList: [
          {
            title: "企业使命",
            dataIndex: "mission",
            formType: "textarea",
            placeholder: "请输入企业使命",
            componentProps: {
              autoSize: { minRows: 2, maxRows: 4 },
            },
          },
        ],
      },
    ],
  },

  // 第四行：核心价值观和备案号
  {
    formType: "grid",
    gutter: 16,
    cols: [
      {
        span: 12,
        formList: [
          {
            title: "核心价值观",
            dataIndex: "core_values",
            formType: "textarea",
            placeholder: "请输入企业核心价值观",
            componentProps: {
              autoSize: { minRows: 2, maxRows: 4 },
            },
          },
        ],
      },
      {
        span: 12,
        formList: [
          {
            title: "企业备案号",
            dataIndex: "icp_number",
            formType: "input",
            placeholder: "请输入企业备案号",
          },
        ],
      },
    ],
  },

  // 第五行：版权信息
  {
    formType: "grid",
    gutter: 16,
    cols: [
      {
        span: 12,
        formList: [
          {
            title: "版权信息",
            dataIndex: "copyright",
            formType: "input",
            placeholder: "请输入版权信息，如：© 2025 XX公司",
          },
        ],
      },
      {
        span: 12,
        formList: [
          {
            title: "客服电话",
            dataIndex: "service_phone",
            formType: "input",
            placeholder: "请输入客服电话，如：************",
          },
        ],
      },
    ],
  },

  // 第六行：微信二维码
  {
    formType: "grid",
    gutter: 16,
    cols: [
      {
        span: 12,
        formList: [
          {
            title: "微信二维码",
            dataIndex: "wechat_qrcode",
            formType: "upload",
            type: "image",
            multiple: false,
            limit: 1,
            accept: ".jpg,.jpeg,.png,.gif",
            tip: "请上传微信二维码，建议尺寸300x300px",
            returnType: "url",
          },
        ],
      },
      {
        span: 12,
        formList: [
          {
            title: "官网模板",
            dataIndex: "now_type",
            formType: "select",
            dict: {
              data: [
                { label: "模板1", value: 1 },
                { label: "模板2", value: 2 },
                { label: "模板3", value: 3 },
                { label: "模板4", value: 4 },
              ],
            },
            slot: "now_type", // 添加插槽标识
            rules: [{ required: true, message: "请选择官网模板" }],
          },
        ],
      },
    ],
  },
]);

// 提交表单处理函数
const handleSubmit = async (formData) => {
  loading.value = true;
  try {
    formData.banner_url = formData.banner_url.join(",");
    const res = await officialWebsiteModuleApi.basisManagement.update(formData);
    if (res && res.code === 200) {
      // 假设API成功时返回 { code: 200, message: '...'}
      Message.success(res.message || "保存成功");
      // 可选择在成功后重新拉取数据，或者直接更新本地form对象（如果API返回了更新后的数据）
      fetchBasisData();
    } else {
      Message.error(res.message || "保存失败");
    }
  } catch (error) {
    console.error("保存基础信息失败:", error);
    Message.error("保存失败，请稍后再试");
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.container {
  padding: 16px;
}

.grid-section {
  margin-top: 24px;
}

.grid-demo :deep(.arco-col) {
  margin-bottom: 16px;
}

.grid-demo :deep(.arco-card) {
  height: 100%;
}
</style>
