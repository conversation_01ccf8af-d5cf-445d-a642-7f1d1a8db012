<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #isBuiltIn="{ record }">
        {{ record.isBuiltIn == 1 ? '是' : '否' }}
      </template>
      <!-- 渠道图标列 -->
      <template #iconUrl="{ record }">
        <div class="flex items-center">
          <!-- 如果是URL则使用a-image组件，否则使用iconfont类 -->
          <a-image v-if="isUrl(record.iconUrl)" :src="record.iconUrl" :width="24" :height="24" />
          <i v-else :class="[record.iconUrl, 'iconfont', 'text-2xl']"></i>
        </div>
      </template>
      
      <!-- 自定义列 - 创建时间 -->
      <template #createdAt="{ record }">
        <div v-time="record.createdAt"></div>
      </template>
    </MaCrud>
  </div>
</template>

<script setup>

import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import orderApi from '@/api/master/order';

// 判断字符串是否为URL
const isUrl = (str) => {
  try {
    return str && (str.startsWith('http://') || str.startsWith('https://') || str.startsWith('/'));
  } catch (e) {
    return false;
  }
};

definePageMeta({
  name: 'master-channelManage',
  path: '/master/upstream/channelManage'
})

const crudRef = ref();

// 渠道数据
const tableData = ref([]);

// 表格列定义
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    align: 'center',
    editDisplay: false,
  },
  {
    title: '渠道名称',
    dataIndex: 'name',
    align: 'center',
    search: true,
    commonRules: [{ required: true, message: '渠道名称必填' }],
  },
  { 
    title: '渠道图标', 
    dataIndex: 'iconUrl', 
    width: 80, 
    formType: 'icon-picker', 
    style: { width: '100%' },
    formProps: {
      showTabs: ['iconfont'] // 只显示Iconfont图标
    },
    commonRules: [{ required: true, message: '渠道图标必选' }],
  },
  {
    title: '是否内置',
    dataIndex: 'isBuiltIn',
    align: 'center',
    formType: 'radio',
    addDefaultValue: 0,
    editDisabled: true, // 编辑时禁用
    addDisabled: true, // 编辑时禁用
    dict: {
      data: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ]
    }
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    formType: "range",
    search: true,
    addDisplay: false,
    editDisplay: false,
    form: false
  },
];

// CRUD 配置
const crud = reactive({
  // 使用真实API获取数据
  api: orderApi.getChannelList,
  showIndex: true,
  pageLayout: 'fixed',
  operationColumn: true,
  operationColumnWidth: 200,
  searchLabelWidth: '100px',
  add: {
    show: true,api: orderApi.createChannel // 使用自定义新增按钮
  },
  edit: {
    show: true,api: orderApi.updateChannel // 使用自定义编辑按钮
  },
  delete: {
    show: true,api: orderApi.deleteChannel // 使用自定义删除按钮
  },
  beforeSearch: (params) => {
    // 如果有创建时间参数，转换为时间戳
    if(params.createdAt){
      params.startTime =new Date(params.createdAt[0]).getTime();
      params.endTime =new Date(params.createdAt[1]).getTime();
      delete params.createdAt
    }else{
      delete params.startTime
      delete params.endTime
    }
    return params;
  }
});
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* 图标选择器样式 */
.icon-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-item:hover {
  background-color: #f2f3f5;
}

.icon-item.active {
  border-color: #1278FF;
  background-color: #e8f3ff;
}

.icon-item .iconfont {
  font-size: 24px;
  margin-bottom: 5px;
}

.icon-name {
  font-size: 12px;
  color: #4e5969;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
</style>