<template>
  <div class="p-5 bg-white rounded">
    <!-- 右侧：邮箱配置 -->
    <div>
      <a-card :body-style="{ padding: '16px' }">
        <div style="font-weight: 600;font-size: 23px; color: black;margin-bottom: 5px;">SMTP邮箱配置</div>
        <div class="mb-3 text-sm text-gray-500">请填写SMTP邮箱的相关配置信息</div>
        <a-form :model="formModel" layout="vertical">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 处理基本字段 -->
            <a-form-item 
              v-for="(entry, index) in Object.entries(formModel).filter(([k, v]) => typeof v !== 'object')" 
              :key="index" 
              :field="entry[0]" 
              :label="entry[0]"
            >
              <template v-if="entry[0] === 'port'">
                <a-input-number 
                  v-model="formModel[entry[0]]" 
                  :placeholder="'请输入' + entry[0]" 
                  :min="1" 
                  :max="65535" 
                  style="width: 100%" 
                />
              </template>
              <template v-else-if="entry[0] === 'secure' || typeof entry[1] === 'boolean'">
                <a-switch v-model="formModel[entry[0]]" />
              </template>
              <template v-else>
                <a-input 
                  v-model="formModel[entry[0]]" 
                  :placeholder="'请输入' + entry[0]" 
                />
              </template>
            </a-form-item>
            
            <!-- 处理auth对象 -->
            <template v-if="formModel.auth && formModel.auth.user">
              <a-form-item field="auth.user" label="邮箱账号">
                <a-input v-model="formModel.auth.user" placeholder="请输入邮箱账号" />
              </a-form-item>
            </template>
            
            <template v-if="formModel.auth && formModel.auth.pass">
              <a-form-item field="auth.pass" label="邮箱密码/授权码">
                <a-input-password v-model="formModel.auth.pass" placeholder="请输入邮箱密码或授权码" />
              </a-form-item>
            </template>

            <!-- 处理from对象 -->
            <template v-if="formModel.from && formModel.from.name">
              <a-form-item field="from.name" label="发件人名称">
                <a-input v-model="formModel.from.name" placeholder="请输入发件人名称" />
              </a-form-item>
            </template>
            
            <template v-if="formModel.from && formModel.from.email">
              <a-form-item field="from.email" label="发件人邮箱">
                <a-input v-model="formModel.from.email" placeholder="请输入发件人邮箱" />
              </a-form-item>
            </template>
          </div>
          <div class="mt-6">
            <div class="font-medium mb-3" style="font-size: 16px; color: black;">测试发送</div>
            <!-- 测试发送 -->
            <a-card style="margin-top: 10px;">
              <div class="grid grid-cols-1 gap-4">
                <a-form-item field="testEmail" label="收件人邮箱">
                  <a-input v-model="testEmail" placeholder="请输入收件人邮箱地址，多个收件人用逗号分隔" />
                </a-form-item>
                <a-form-item field="emailSubject" label="邮件主题">
                  <a-input v-model="emailSubject" placeholder="请输入邮件主题" />
                </a-form-item>
                <a-form-item field="emailContent" label="邮件内容">
                  <a-textarea v-model="emailContent" placeholder="请输入邮件内容" :auto-size="{ minRows: 3, maxRows: 5 }" />
                </a-form-item>
                <div class="flex justify-end">
                  <a-button type="primary" @click="sendTestEmail">
                    发送测试邮件
                  </a-button>
                </div>
              </div>
            </a-card>
          </div>
          </a-form>
          <div class="flex justify-end mt-4">
            <a-button type="primary" @click="submitForm">
              <template #icon><icon-save /></template>
              保存设置
            </a-button>
          </div>
        </a-card>
      </div>
  </div>
</template>
  
<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { Message } from '@arco-design/web-vue';
import systemApi from '@/api/master/system';
import commonApi from '@/api/common';
definePageMeta({
  name: 'system-config-email',
  path: '/master/system/config/email'
});

const configApi = systemApi.configuration;


const testEmail = ref('');
const emailSubject = ref('测试邮件');
const emailContent = ref('这是一封测试邮件，用于测试邮件发送功能是否正常。');

// 表单数据
const formModel = ref({  });

// 发送测试邮件
const sendTestEmail = async () => {
  if (!testEmail.value) {
    Message.warning('请输入收件人邮箱地址');
    return;
  }
  
  if (!emailSubject.value) {
    Message.warning('请输入邮件主题');
    return;
  }
  
  if (!emailContent.value) {
    Message.warning('请输入邮件内容');
    return;
  }
  
  // 验证邮箱格式（如果是多个邮箱，则验证第一个）
  const firstEmail = testEmail.value.split(',')[0].trim();
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (!emailRegex.test(firstEmail)) {
    Message.warning('邮箱格式不正确，请重新输入');
    return;
  }
  
  try {
    Message.loading('正在发送测试邮件，请稍候...');
    
    // 发送测试邮件
    const res = await commonApi.sendEmail({
      to: testEmail.value,
      subject: emailSubject.value,
      content: emailContent.value,
      platform: 'smtp',
      configValue: formModel.value,
      isDefault: true,
    });
    
    if (res.code === 200) {
      Message.success(res.message || '测试邮件发送成功，请查收');
    } else {
      Message.error(res.message || '测试邮件发送失败');
    }
  } catch (error) {
    console.error('测试邮件发送失败:', error);
    Message.error('测试邮件发送失败');
  }
};

// 全局配置数据缓存
const configData = ref(null);

// 获取设置
const fetchSettings = async () => {
  const res = await configApi.objectTypes('Email')
  if (res.code === 200 && res.data) {
    // 缓存数据供切换提供商时使用
    formModel.value = res.data.smtp;
    console.log(formModel.value,'xxxx');
  }
};

// 提交表单
const submitForm = async () => {
  try {
    Message.loading('正在保存设置，请稍候...');
    
    // 组装最终要提交的数据
    const data = {
      configValue: formModel.value,
      isDefault: true,
    };
    
    // 调用API接口保存配置
    const res = await configApi.update('Email', 'smtp', data);
    
    if(res.code === 200){
      Message.success(res.message || '保存成功');
      fetchSettings();
    } else {
      Message.error(res.message || '保存失败');
    }
  } catch (error) {
    console.error('保存邮箱设置失败:', error);
    Message.error('保存失败，请稍后重试');
  }
};

onMounted(() => {
  fetchSettings();
});
</script>
  
<style lang="less" scoped>
// 存储选项卡片样式
.storage-options {
  width: 100%;
}

.storage-option-card {
  width: 100%;
  height: 54px;
  display: flex;
  align-items: center;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  padding: 0 16px;
  margin-bottom: 15px;
  cursor: pointer;
  background: #fff;
  transition: border-color 0.2s;
  position: relative;
}

.storage-option-card.active {
  border-color: rgb(var(--primary-6));
  background: #f7f8fa;
}

.storage-option-card:hover {
  border-color: rgb(var(--primary-6));
}

.storage-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.storage-icon {
  font-size: 18px;
  margin: 0 8px;
  color: #86909c;
}

.storage-text {
  font-size: 14px;
  line-height: 1.2;
}

// 表单和卡片样式调整
:deep(.arco-form-item-extra) {
  margin: 8px 0px !important;
  color: #86909c;
}

:deep(.arco-form-item-label-col) {
  margin-bottom: 10px !important;
}

:deep(.arco-card) {
  border-radius: 4px;
}

:deep(.arco-input-wrapper) {
  width: 100%;
}

:deep(.arco-form-item) {
  margin-bottom: 10px !important;
}
</style>