<!--
 - Mine<PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div>
    <a-card title="商品详情" class="mb-4">
      <div class="mb-4">
        <ma-form
          ref="detailFormRef"
          v-model="detailForm"
          :columns="detailFormColumns"
          layout="vertical"
          :options="{ showButtons: false }"
        />
      </div>
    </a-card>

    <a-card title="其他设置" class="mb-4">
      <ma-form
        ref="otherFormRef"
        v-model="otherForm"
        :columns="otherFormColumns"
        layout="vertical"
        :options="{ showButtons: false }"
      />
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from "vue";

const props = defineProps({
  detailValue: {
    type: Object,
    default: () => ({}),
  },
  otherValue: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:detailValue", "update:otherValue"]);

// 商品详情表单
const detailForm = reactive({
  content: props.detailValue.content || "",
});

// 其他设置表单
const otherForm = reactive({ ...props.otherValue });

// 监听父组件传入的值变化
watch(
  () => props.detailValue,
  (newVal) => {
    Object.assign(detailForm, newVal);
  },
  { deep: true }
);

watch(
  () => props.otherValue,
  (newVal) => {
    Object.assign(otherForm, newVal);
  },
  { deep: true }
);

// 监听本地表单值变化，同步到父组件
watch(
  detailForm,
  (newVal) => {
    emit("update:detailValue", newVal);
  },
  { deep: true }
);

watch(
  otherForm,
  (newVal) => {
    emit("update:otherValue", newVal);
  },
  { deep: true }
);

// 商品详情表单配置
const detailFormColumns = [
  {
    title: "",
    dataIndex: "content",
    formType: "wang-editor",
    placeholder: "请输入商品详情",
    height: 300,
    labelWidth: 5,
  },
];

// 其他设置表单配置
const otherFormColumns = [
  {
    title: "上架状态",
    dataIndex: "status",
    formType: "radio",
    defaultValue: 1,
    dict: {
      data: [
        { label: "上架", value: 1 },
        { label: "下架", value: 0 },
      ],
    },
  },
  {
    title: "商品排序",
    dataIndex: "sort",
    formType: "inputNumber",
    placeholder: "请输入排序值",
    defaultValue: 0,
    attrs: {
      min: 0,
      precision: 0,
    },
  },
  {
    title: "销量",
    dataIndex: "totalSales",
    formType: "inputNumber",
    placeholder: "请输入销量",
    defaultValue: 0,
    attrs: {
      min: 0,
      precision: 0,
    },
  },
  {
    title: "关键词",
    dataIndex: "keywords",
    formType: "input",
    placeholder: "请输入关键词，多个关键词用逗号分隔",
  },
  {
    title: "SEO标题",
    dataIndex: "seoTitle",
    formType: "input",
    placeholder: "请输入SEO标题",
  },
  {
    title: "SEO描述",
    dataIndex: "seoDescription",
    formType: "textarea",
    placeholder: "请输入SEO描述",
    attrs: {
      autoSize: { minRows: 3, maxRows: 5 },
    },
  },
];

// 表单引用
const detailFormRef = ref(null);
const otherFormRef = ref(null);

// 表单验证
const validate = () => {
  if (!detailForm.content.trim()) {
    return Promise.reject("请输入商品详情");
  }

  return new Promise((resolve, reject) => {
    // 检查 otherFormRef 是否存在且有 validate 方法
    if (
      otherFormRef.value &&
      typeof otherFormRef.value.validate === "function"
    ) {
      otherFormRef.value
        .validate()
        .then(() => {
          resolve(true);
        })
        .catch((error) => {
          reject(error);
        });
    } else {
      console.warn("其他设置表单引用不存在或没有验证方法，跳过验证");
      // 如果 otherFormRef 不可用，仍然视为验证通过
      resolve(true);
    }
  });
};

// 暴露方法给父组件
defineExpose({
  validate,
  detailFormRef,
  otherFormRef,
  detailForm,
  otherForm,
});
</script>
