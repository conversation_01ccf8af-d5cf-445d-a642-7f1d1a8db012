<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div>
    <a-card class="mb-4">
      <template #title>
        <div class="flex items-center justify-between">
          <span>商品属性信息</span>
          <a-button type="outline" size="small" @click="resetAttributes">
            <template #icon><icon-refresh /></template>
            重置属性
          </a-button>
        </div>
      </template>

      <ma-form
        ref="maFormRef"
        :columns="formColumns"
        v-model="formData"
        :label-width="300"
        :options="{ showButtons: false }"
        :key="key"
      />
      <div
        v-if="attributes.length === 0"
        class="text-center py-4 text-gray-500"
      >
        暂无属性数据
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import {
  IconPlus,
  IconEdit,
  IconDelete,
  IconRefresh,
} from "@arco-design/web-vue/es/icon";

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  categoryId: {
    type: [Number, String],
    default: "",
  },
});

const emit = defineEmits(["update:modelValue"]);

// 属性列表
const attributes = ref([
  // {
  //   name: "颜色",
  //   type: "select",
  //   value: "黑色\n白色\n红色",
  //   isRequired: false,
  //   isFilterable: true,
  //   sort: 0,
  //   status: true,
  // },
  // {
  //   name: "规格",
  //   type: "radio",
  //   value: "大\n小",
  //   isRequired: true,
  //   isFilterable: true,
  //   sort: 0,
  //   status: true,
  // },
  // {
  //   name: "上传资质",
  //   type: "upload",
  //   isRequired: true,
  //   isFilterable: true,
  //   sort: 0,
  //   status: true,
  // },
  // {
  //   name: "产品说明",
  //   type: "input",
  //   isRequired: true,
  //   isFilterable: true,
  //   sort: 0,
  //   status: true,
  // },
  // {
  //   name: "到期时间",
  //   type: "date",
  //   isRequired: true,
  //   isFilterable: true,
  //   sort: 0,
  //   status: false,
  // },
]);

// 表单数据
const formData = ref({});
const key = ref(1);
// 表单引用
const maFormRef = ref(null);

// 表单配置
const formOptions = reactive({
  submitShowBtn: false,
  layout: "vertical",
  labelAlign: "left",
  disabled: false,
});

// 生成表单列定义
const formColumns = ref([]);

// 映射属性类型到表单类型
function mapTypeToFormType(type) {
  switch (type) {
    case "input":
      return "input";
    case "select":
      return "select";
    case "radio":
      return "radio";
    case "upload":
      return "upload";
    case "date":
      return "date";
    default:
      return "input";
  }
}

// 生成表单列定义，自动适配各类型属性
function generateFormColumns(reset) {
  formData.value = {}
  formColumns.value = attributes.value.map((attr) => {
    if(attr.defaultValue && !reset)  formData.value[attr.name] = attr.defaultValue
    const formType = mapTypeToFormType(attr.type);
    const column = {
      title: attr.name,
      dataIndex: attr.name, // 保证唯一且与formData匹配
      formType,
      placeholder:
        formType === "date" ? "请选择" + attr.name : "请输入" + attr.name,
      rules: attr.isRequired
        ? [
            {
              required: true,
              message: `请${formType === "date" ? "选择" : "输入"}${attr.name}`,
            },
          ]
        : [],
      // 其它通用配置
      required: attr.isRequired,
      filterable: attr.isFilterable,
      // sort: attr.sort,
      // status: attr.status,
      sort: 0,
      status: true,
      
    };

    // 针对不同类型补充配置
    if (formType === "select") {
      // 参考 BasicInfo.vue 中的 select 实现
      column.data = attr.value
        ? attr.value.split("\n").map((v) => ({ label: v, value: v }))
        : [];
      column.allowSearch = true;
      column.allowClear = true;
      column.placeholder = `请选择${attr.name}`;
    }
    if (formType === "radio") {
      // 参考 ProductDetail.vue 中的单选框实现
      column.dict = {
        data: attr.value
          ? attr.value.split("\n").map((v) => ({ label: v, value: v }))
          : [],
      };
      column.defaultValue = attr.value ? attr.value.split("\n")[0] : "";
      column.placeholder = `请选择${attr.name}`;
    }
    if (formType === "upload") {
      column.type = "image";
      column.multiple = true;
      column.limit = 5;
      column.accept = ".jpg,.jpeg,.png,.gif";
      column.tip = "最多上传5张图片，建议尺寸800x800px，大小不超过2MB";
      column.returnType = "url";
      column.placeholder = `请上传${attr.name}`;
    }
    if (formType === "date") {
      column.format = "YYYY-MM-DD";
      column.allowClear = true;
      column.type = "date"; // 确保是日期类型
      column.valueFormat = "YYYY-MM-DD"; // 返回值格式
    }

    return column;
  });
  key.value++;
}

// 监听属性变化，生成表单列
watch(
  () => attributes.value,
  (newVal) => {
    if (!newVal || newVal.length === 0) {
      formColumns.value = [];
      return;
    }
    generateFormColumns(false);
  },
  { deep: true }
);

// 监听表单数据变化，及时同步到父组件
watch(
  () => formData.value,
  () => {
    syncToParent();
  },
  { deep: true }
);

// 监听商品分类变化，获取对应的属性选项
watch(
  () => props.categoryId,
  (newVal) => {
    if (newVal) {
      fetchCategoryAttributes(newVal);
    }
  },
  { immediate: true }
);

// 获取分类对应的属性
const fetchCategoryAttributes = async (categoryId) => {
  try {
    // 实际开发中，这里应该调用后端API获取分类对应的属性
    // 使用预设的属性数据
    // 同步到父组件
    syncToParent();
  } catch (error) {
    Message.error("获取分类属性失败");
  }
};

// 重置属性
const resetAttributes = () => {
  // 实际项目中可能需要重新从API获取默认属性
  Message.success("属性已重置");
  formData.value = {}

  generateFormColumns(true);
};

// 同步数据到父组件
const syncToParent = () => {
  // 将 attributeValues 改为数组格式
  const attributeValuesArray = [];

  // 遍历 attributes 获取属性 ID 和对应的值
  attributes.value.forEach((attr, index) => {
    // 构建每个属性项对象，包含 attributeItemId 和 value
    if (formData.value[attr.name] != undefined)
      attributeValuesArray.push({
        attributeItemId: attr.id, // 使用属性的 id 作为 attributeItemId
        value: formData.value[attr.name] || "", // 从 formData 获取对应的值
      });
  });

  // 发送更新事件给父组件
  emit("update:modelValue", {
    attributes: attributes.value,
    attributeValues: attributeValuesArray, // 使用数组格式的 attributeValues
  });
};

// 表单验证
const validate = async () => {
  if (maFormRef.value) {
    try {
      // await maFormRef.value.validateForm();
      const maFormPromise = await maFormRef.value.validateForm();
      if (maFormPromise != undefined) {
        throw errors;
      }

      syncToParent();
      return true;
    } catch (error) {
      throw errors;
    }
  }
  return true;
};

const updata_attributes = (data) => {
  attributes.value = data;
};

// 暴露方法给父组件
defineExpose({
  validate,
  attributes,
  updata_attributes,
});
</script>

<style scoped>
.attribute-card {
  margin-bottom: 16px;
}
</style>
