<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div>
    <a-card title="物流设置" class="mb-4">
      <div class="mb-4">
        <div class="mb-2">
          <span class="text-sm text-gray-600">运费模板</span>
          <span class="text-red-500 ml-1">*</span>
        </div>
        <div class="flex items-center">
          <a-select v-model="logisticsForm.freightTemplateId" placeholder="请选择运费模板" style="width: 200px">
            <a-option :value="1">标准快递</a-option>
            <a-option :value="2">经济快递</a-option>
            <a-option :value="3">顺丰速运</a-option>
          </a-select>
          <a-button type="text" @click="openFreightManage" class="ml-4">
            <template #icon><icon-plus /></template>
            运费模板管理
          </a-button>
        </div>
      </div>

    </a-card>
    
    <a-card title="配送信息" class="mb-4">
      <ma-form ref="deliveryFormRef" v-model="deliveryForm" :columns="deliveryFormColumns" layout="vertical" :options="{ showButtons: false }"/>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { IconPlus } from '@arco-design/web-vue/es/icon';

const props = defineProps({
  logisticsValue: {
    type: Object,
    default: () => ({})
  },
  deliveryValue: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:logisticsValue', 'update:deliveryValue', 'open-freight-manage']);

// 物流表单
const logisticsForm = reactive({
  freightTemplateId: props.logisticsValue.freightTemplateId || '',
  valuationType: props.logisticsValue.valuationType || 1
});

// 配送表单
const deliveryForm = reactive({...props.deliveryValue});

// 监听父组件传入的值变化
watch(() => props.logisticsValue, (newVal) => {
  Object.assign(logisticsForm, newVal);
}, { deep: true });

watch(() => props.deliveryValue, (newVal) => {
  Object.assign(deliveryForm, newVal);
}, { deep: true });

// 监听本地表单值变化，同步到父组件
watch(logisticsForm, (newVal) => {
  emit('update:logisticsValue', newVal);
}, { deep: true });

watch(deliveryForm, (newVal) => {
  emit('update:deliveryValue', newVal);
}, { deep: true });

// 配送信息表单配置
const deliveryFormColumns = [
  {
    title: '配送区域',
    dataIndex: 'deliveryArea',
    formType: 'city-linkage',
    placeholder: '请选择配送区域',
    rules: [{ required: true, message: '请选择配送区域' }],
    type: 'cascader',
    mode: 'code'
  },
  {
    title: '发货时间',
    dataIndex: 'deliveryTime',
    formType: 'date',
    placeholder: '请选择发货时间',
    rules: [{ required: true, message: '请选择发货时间' }],
    allowClear: true,
    format: 'YYYY-MM-DD'
  },
  {
    title: '商品重量',
    dataIndex: 'weight',
    formType: 'inputNumber',
    placeholder: '请输入商品重量',
    attrs: {
      min: 0,
      precision: 2,
      suffix: 'kg'
    }
  },
  {
    title: '商品体积',
    dataIndex: 'volume',
    formType: 'inputNumber',
    placeholder: '请输入商品体积',
    attrs: {
      min: 0,
      precision: 2,
      suffix: 'm³'
    }
  }
];

// 表单引用
const deliveryFormRef = ref(null);

// 打开运费模板管理
const openFreightManage = () => {
  emit('open-freight-manage');
};

// 表单验证
const validate = () => {
  if (!logisticsForm.freightTemplateId) {
    return Promise.reject('请选择运费模板');
  }
  
  return new Promise((resolve, reject) => {
    deliveryFormRef.value.validateForm().then((valid) => {
      if (!valid) {
        console.log('配送信息验证通过', deliveryForm);
        resolve(true);
      } else {
        console.error('配送信息验证失败', valid);
        reject(valid);
      }
    }).catch(error => {
      console.error('配送信息验证失败', error);
      reject(error);
    });
  });
};

// 暴露方法给父组件
defineExpose({
  validate,
  deliveryFormRef,
  logisticsForm,
  deliveryForm
});
</script>
