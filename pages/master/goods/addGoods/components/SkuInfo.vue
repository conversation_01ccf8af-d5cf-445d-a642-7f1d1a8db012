<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <a-card class="mb-4">
    <div class="mb-4 flex justify-between items-center">
      <div>
        <a-radio-group v-model="skuType" type="button">
          <a-radio :value="1">单规格</a-radio>
          <a-radio :value="2">多规格</a-radio>
        </a-radio-group>
      </div>
      <div v-if="skuType === 2">
        <a-button type="primary" @click="addSpecItem">
          <template #icon><icon-plus /></template>
          添加规格项
        </a-button>
      </div>
    </div>

    <!-- 单规格表单 -->
    <div v-if="skuType === 1">
      <ma-form
        ref="singleSkuFormRef"
        v-model="singleSkuForm"
        :columns="singleSkuFormColumns"
        layout="vertical"
        :options="{ showButtons: false }"
      />
    </div>

    <!-- 多规格表单 -->
    <div v-if="skuType === 2">
      <!-- 规格项列表 -->
      <div
        v-for="(spec, specIndex) in skuForm.specs"
        :key="specIndex"
        class="mb-4 p-4 border border-gray-200 rounded-lg"
      >
        <div class="flex justify-between items-center mb-2">
          <a-input
            v-model="spec.name"
            placeholder="规格名称，如颜色、尺寸"
            style="width: 200px"
          />
          <a-button status="danger" circle @click="removeSpecItem(specIndex)">
            <template #icon><icon-delete /></template>
          </a-button>
        </div>

        <div class="flex flex-wrap gap-2 items-center">
          <a-tag
            v-for="(value, valueIndex) in spec.values"
            :key="valueIndex"
            closable
            @close="removeSpecValue(specIndex, valueIndex)"
          >
            {{ value }}
          </a-tag>

          <div class="inline-flex items-center">
            <a-input
              v-model="spec.inputValue"
              placeholder="规格值"
              style="width: 120px"
              @keyup.enter="addSpecValue(specIndex)"
              @blur="handleSpecValueBlur(specIndex)"
            />
            <a-button type="text" @click="addSpecValue(specIndex)">
              <template #icon><icon-plus /></template>
            </a-button>
          </div>
        </div>
      </div>

      <!-- SKU列表 -->
      <div v-if="skuForm.specs.length > 0 && hasValidSpecs" class="mt-4">
        <a-table
          :columns="skuColumns"
          :data="skuList"
          :pagination="false"
          border
          class="sku-table"
        >
          <!-- 动态生成每个规格的插槽 -->
          <template
            v-for="spec in dynamicSpecSlots"
            #[spec.slotName]="{ record }"
          >
            {{ record.specs[spec.name] }}
          </template>
          <template #image="{ record }">
            <ma-upload
              v-model="record.image"
              :storage-mode="1"
              :config="{
                uploadType: 'image',
                multiple: false,
                limit: 1,
              }"
              tip="建议尺寸：800x800px"
              accept=".jpg,.jpeg,.png"
            />
          </template>
          <template #price="{ record }">
            <a-input-number
              v-model="record.price"
              :min="0"
              :precision="2"
              placeholder="销售价"
              style="width: 100%"
            />
          </template>
          <template #marketPrice="{ record }">
            <a-input-number
              v-model="record.marketPrice"
              :min="0"
              :precision="2"
              placeholder="市场价"
              style="width: 100%"
            />
          </template>
          <template #costPrice="{ record }">
            <a-input-number
              v-model="record.costPrice"
              :min="0"
              :precision="2"
              placeholder="成本价"
              style="width: 100%"
            />
          </template>
          <template #unit="{ record }">
            <a-input
              v-model="record.unit"
              placeholder="单位"
              style="width: 100%"
            />
          </template>
          <template #weight="{ record }">
            <a-input-number
              v-model="record.weight"
              :min="0"
              :precision="2"
              placeholder="重量"
              style="width: 100%"
            />
          </template>
          <template #volume="{ record }">
            <a-input-number
              v-model="record.volume"
              :min="0"
              :precision="2"
              placeholder="体积"
              style="width: 100%"
            />
          </template>
          <template #stock="{ record }">
            <a-input-number
              v-model="record.stock"
              :min="0"
              :precision="0"
              :step="1"
              placeholder="库存"
              style="width: 100%"
              :parser="
                (value) => {
                  const numStr = String(value).replace(/[^\d]/g, '');
                  return numStr === '' ? undefined : parseInt(numStr, 10);
                }
              "
              :formatter="
                (value) => {
                  return value !== undefined && value !== null
                    ? String(Math.floor(Number(value)))
                    : '';
                }
              "
            />
          </template>
          <template #salesVolume="{ record }">
            <a-input-number
              v-model="record.salesVolume"
              :min="0"
              :precision="0"
              :step="1"
              placeholder="销量"
              style="width: 100%"
              :parser="
                (value) => {
                  const numStr = String(value).replace(/[^\d]/g, '');
                  return numStr === '' ? undefined : parseInt(numStr, 10);
                }
              "
              :formatter="
                (value) => {
                  return value !== undefined && value !== null
                    ? String(Math.floor(Number(value)))
                    : '';
                }
              "
            />
          </template>
          <template #code="{ record }">
            <a-input v-model="record.code" placeholder="编码" />
          </template>
        </a-table>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconPlus, IconDelete } from "@arco-design/web-vue/es/icon";

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      skuType: 1,
      specs: [],
    }),
  },
});

const emit = defineEmits(["update:modelValue"]);

// 规格类型
const skuType = ref(props.modelValue.skuType || 1);

// 规格表单
const skuForm = reactive({
  specs: props.modelValue.specs || [],
});

// 监听规格类型变化，同步到父组件
watch(
  skuType,
  (newVal, oldVal) => {
    // 只有当值真正改变时才更新
    if (newVal !== oldVal) {
      // 当切换到多规格且当前没有规格项时，默认添加一个规格项
      if (newVal === 2 && skuForm.specs.length === 0) {
        addSpecItem();
      }
      emit("update:modelValue", {
        skuType: newVal,
        specs: skuForm.specs,
      });
    }
  },
  { immediate: true }
);

// 监听父组件传入的值变化
watch(
  () => props.modelValue,
  (newVal, oldVal) => {
    // 只在首次加载或 skuType 真正改变时更新本地 skuType
    if (!oldVal || (newVal && newVal.skuType !== skuType.value)) {
      skuType.value = newVal.skuType;
    }

    // 只在 specs 真正改变时更新
    if (
      newVal &&
      newVal.specs &&
      JSON.stringify(newVal.specs) !== JSON.stringify(skuForm.specs)
    ) {
      skuForm.specs = JSON.parse(JSON.stringify(newVal.specs));
    }
  },
  { deep: true }
);

// 引入防抖函数
import { useThrottleFn } from "@vueuse/core";

// 使用节流函数包装emit，减少更新频率
const throttledEmit = useThrottleFn((data) => {
  emit("update:modelValue", data);
}, 300);

// 监听本地表单值变化，同步到父组件
watch(
  skuForm,
  (newVal, oldVal) => {
    // 避免在 props.modelValue 的 watch 处理中又触发更新
    if (JSON.stringify(newVal) !== JSON.stringify(props.modelValue)) {
      throttledEmit({
        skuType: skuType.value,
        ...skuForm,
      });
    }
  },
  { deep: true }
);

// 单规格表单
const singleSkuForm = reactive({
  price: "",
  marketPrice: "",
  costPrice: "",
  stock: "",
  code: "",
  image: "",
});

// 单规格表单配置
const singleSkuFormColumns = [
  {
    title: "商品图片",
    dataIndex: "image",
    formType: "upload",
    type: "image",
    multiple: false,
    limit: 1,
    accept: ".jpg,.jpeg,.png,.gif",
    tip: "最多上传1张图片，建议尺寸800x800px，大小不超过2MB",
    returnType: "url",
    rules: [{ required: true, message: "请上传商品图片" }],
    size: 2,
  },
  {
    title: "销售价",
    dataIndex: "price",
    formType: "inputNumber",
    placeholder: "请输入销售价",
    rules: [{ required: true, message: "请输入销售价" }],
    attrs: {
      min: 0,
      precision: 2,
      prefix: "¥",
    },
  },
  {
    title: "市场价",
    dataIndex: "marketPrice",
    formType: "inputNumber",
    placeholder: "请输入市场价",
    attrs: {
      min: 0,
      precision: 2,
      prefix: "¥",
    },
  },
  {
    title: "成本价",
    dataIndex: "costPrice",
    formType: "inputNumber",
    placeholder: "请输入成本价",
    attrs: {
      min: 0,
      precision: 2,
      prefix: "¥",
    },
  },
  {
    title: "库存",
    dataIndex: "stock",
    formType: "inputNumber",
    placeholder: "请输入库存",
    rules: [{ required: true, message: "请输入库存" }],
    attrs: {
      min: 0,
      precision: 0,
      step: 1,
    },
  },
  {
    title: "销量",
    dataIndex: "salesVolume",
    formType: "inputNumber",
    placeholder: "请输入销量",
    rules: [{ required: true, message: "请输入销量" }],
    attrs: {
      min: 0,
      precision: 0,
      step: 1,
    },
  },
  {
    title: "商品编码",
    dataIndex: "code",
    formType: "input",
    placeholder: "请输入商品编码",
  },
];

// 表单引用
const singleSkuFormRef = ref(null);

// 判断是否有有效的规格项（至少有一个规格项有规格值）
const hasValidSpecs = computed(() => {
  // 过滤出有名称且有规格值的规格项
  const validSpecs = skuForm.specs.filter(
    (spec) => spec.name && spec.values.length > 0
  );
  return validSpecs.length > 0;
});

// SKU列表
const skuList = ref([]);

// SKU表格列
const skuColumns = computed(() => {
  // 基础列
  const baseColumns = [];

  // 根据规格项动态生成规格列
  skuForm.specs.forEach((spec) => {
    if (spec.name && spec.values.length > 0) {
      baseColumns.push({
        title: spec.name,
        dataIndex: ["specs", spec.name],
        slotName: `spec-${spec.name}`,
        cellStyle: { minWidth: "80px" },
        headerCellStyle: { whiteSpace: "nowrap", minWidth: "80px" },
      });
    }
  });

  // 其他列
  const otherColumns = [
    {
      title: "图片",
      dataIndex: "image",
      slotName: "image",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "100px" },
    },
    {
      title: "销售价",
      dataIndex: "price",
      slotName: "price",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "100px" },
    },
    {
      title: "市场价",
      dataIndex: "marketPrice",
      slotName: "marketPrice",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "100px" },
    },
    {
      title: "单位",
      dataIndex: "unit",
      slotName: "unit",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "80px" },
    },
    {
      title: "重量",
      dataIndex: "weight",
      slotName: "weight",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "80px" },
    },
    {
      title: "体积",
      dataIndex: "volume",
      slotName: "volume",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "80px" },
    },
    {
      title: "成本价",
      dataIndex: "costPrice",
      slotName: "costPrice",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "100px" },
    },
    {
      title: "库存",
      dataIndex: "stock",
      slotName: "stock",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "80px" },
    },
    {
      title: "销量",
      dataIndex: "salesVolume",
      slotName: "salesVolume",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "80px" },
    },
    {
      title: "商品编码",
      dataIndex: "code",
      slotName: "code",
      headerCellStyle: { whiteSpace: "nowrap", minWidth: "100px" },
    },
  ];

  return [...baseColumns, ...otherColumns];
});

// 动态生成规格插槽
const dynamicSpecSlots = computed(() => {
  return skuForm.specs
    .filter((spec) => spec.name && spec.values.length > 0)
    .map((spec) => ({
      name: spec.name,
      slotName: `spec-${spec.name}`,
    }));
});

// 格式化规格文本
const formatSpecsText = (specs) => {
  return Object.entries(specs)
    .map(([key, value]) => `${key}: ${value}`)
    .join("，");
};

// 添加规格项
const addSpecItem = () => {
  skuForm.specs.push({
    name: "",
    values: [],
    inputValue: "",
  });
};

// 移除规格项
const removeSpecItem = (index) => {
  skuForm.specs.splice(index, 1);
  generateSkuList();
};

// 添加规格值
const addSpecValue = (specIndex) => {
  const spec = skuForm.specs[specIndex];
  if (!spec.inputValue.trim()) {
    Message.warning("规格值不能为空");
    return;
  }

  if (spec.values.includes(spec.inputValue.trim())) {
    Message.warning("规格值不能重复");
    return;
  }

  spec.values.push(spec.inputValue.trim());
  spec.inputValue = "";

  generateSkuList();
};

// 规格值输入框失去焦点时添加规格值
const handleSpecValueBlur = (specIndex) => {
  if (skuForm.specs[specIndex].inputValue.trim()) {
    addSpecValue(specIndex);
  }
};

// 移除规格值
const removeSpecValue = (specIndex, valueIndex) => {
  skuForm.specs[specIndex].values.splice(valueIndex, 1);
  generateSkuList();
};

// 生成SKU列表
const generateSkuList = () => {
  // 获取有效的规格项（有名称且有规格值）
  const validSpecs = skuForm.specs.filter(
    (spec) => spec.name && spec.values.length > 0
  );

  // 如果没有有效的规格项，则返回空数组
  if (validSpecs.length === 0) {
    skuList.value = [];
    return;
  }

  // 获取所有规格的笛卡尔积
  const cartesian = (arr) => {
    return arr.reduce(
      (a, b) => {
        return a.flatMap((x) => b.map((y) => [...x, y]));
      },
      [[]]
    );
  };

  const specs = validSpecs.map((spec) => spec.values);
  const combinations = cartesian(specs);

  // 生成SKU列表
  skuList.value = combinations.map((combination) => {
    const specs = {};
    validSpecs.forEach((spec, index) => {
      specs[spec.name] = combination[index];
    });

    return {
      specs,
      price: "",
      marketPrice: "",
      costPrice: "",
      stock: "",
      code: "",
      image: "",
      weight: "",
      volume: "",
      unit: "",
    };
  });
};

// 表单验证
const validate = () => {
  if (skuType.value === 1) {
    return new Promise((resolve, reject) => {
      singleSkuFormRef.value
        .validateForm()
        .then((valid) => {
          if (!valid) {
            // 单规格表单验证通过时，发送完整数据给父组件

            emit("update:modelValue", {
              skuType: skuType.value,
              ...singleSkuForm,
            });
            resolve(true);
          } else {
            reject(valid);
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  } else {
    // 多规格验证
    if (skuForm.specs.length === 0) {
      Message.error("请添加至少一个规格项");
      return Promise.reject("请添加至少一个规格项");
    }

    if (!skuForm.specs.every((spec) => spec.name.trim())) {
      Message.error("请填写所有规格项的名称");
      return Promise.reject("请填写所有规格项的名称");
    }

    if (!skuForm.specs.every((spec) => spec.values.length > 0)) {
      Message.error("请为每个规格项添加至少一个规格值");
      return Promise.reject("请为每个规格项添加至少一个规格值");
    }

    // 验证SKU列表中的必填项
    const invalidSku = skuList.value.find((sku) => !sku.price || !sku.stock);
    if (invalidSku) {
      Message.error("请填写所有SKU的销售价和库存");
      return Promise.reject("请填写所有SKU的销售价和库存");
    }

    // 多规格验证通过时，更新参数并发送给父组件
    // 更新函数会将最新的 skuList 数据发送给父组件
    emit("update:modelValue", {
      skuType: skuType.value,
      specs: skuForm.specs,
      skuList: JSON.parse(JSON.stringify(skuList.value)),
    });

    return Promise.resolve(true);
  }
};

// 监听规格变化，生成SKU列表
watch(
  () => skuForm.specs,
  () => {
    generateSkuList();

    // 如果是多规格，当规格项变化时同步数据到父组件
    if (skuType.value === 2) {
      emit("update:modelValue", {
        skuType: skuType.value,
        specs: skuForm.specs,
        skuList: JSON.parse(JSON.stringify(skuList.value)),
      });
    }
  },
  { deep: true }
);

// 监听单规格表单数据变化
watch(
  singleSkuForm,
  () => {
    // 如果是单规格，自动同步数据到父组件
    if (skuType.value === 1) {
      emit("update:modelValue", {
        skuType: skuType.value,
        ...singleSkuForm,
      });
    }
  },
  { deep: true }
);

// 监听多规格表格数据变化
watch(
  skuList,
  () => {
    // 如果是多规格，自动同步表格数据到父组件
    if (skuType.value === 2) {
      emit("update:modelValue", {
        skuType: skuType.value,
        specs: skuForm.specs,
        skuList: JSON.parse(JSON.stringify(skuList.value)),
      });
    }
  },
  { deep: true }
);

// 修改单规格表单数据的方法
const updateSingleSkuForm = (data) => {
  if (!data || typeof data !== "object") {
    return false;
  }

  // 遍历数据对象的属性，更新到 singleSkuForm 中
  Object.keys(data).forEach((key) => {
    if (key in singleSkuForm) {
      singleSkuForm[key] = data[key];
    }
  });

  // 更新后同步数据到父组件
  emit("update:modelValue", {
    skuType: skuType.value,
    ...singleSkuForm,
  });

  return true;
};

// 设置 SKU 列表数据的方法
const setSkuList = (list) => {
  if (!Array.isArray(list)) {
    return false;
  }

  // 确保 skuList 被更新
  skuList.value = list;

  // 更新后同步数据到父组件
  if (skuType.value === 2) {
    emit("update:modelValue", {
      skuType: skuType.value,
      specs: skuForm.specs,
      skuList: JSON.parse(JSON.stringify(skuList.value)),
    });
  }

  return true;
};

// 暴露方法给父组件
defineExpose({
  validate,
  singleSkuFormRef,
  skuForm,
  singleSkuForm,
  skuList,
  updateSingleSkuForm,
  setSkuList,
});
</script>
