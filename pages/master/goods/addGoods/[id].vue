<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <a-card class="mb-4">
      <template #title>
        <div class="text-lg font-semibold">添加商品</div>
      </template>

      <!-- 步骤导航 -->
      <a-steps :current="currentStep + 1" class="mb-6">
        <a-step title="基本信息" />
        <a-step title="属性信息" />
        <a-step title="规格/SKU" />
        <a-step title="商品详情" />
      </a-steps>

      <!-- 步骤内容区域 -->
      <div class="step-content">
        <!-- 步骤1：基本信息 -->
        <div v-show="currentStep === 0">
          <BasicInfo
            ref="basicInfoRef"
            :modelValue="basicForm"
            :associations="associations"
            :logisticsForm="logisticsForm"
            :deliveryForm="deliveryForm"
            @update:modelValue="(val) => Object.assign(basicForm, val)"
            @update:associations="(val) => Object.assign(associations, val)"
            @update:logisticsForm="(val) => Object.assign(logisticsForm, val)"
            @update:deliveryForm="(val) => Object.assign(deliveryForm, val)"
            @open-label-manage="openLabelManage"
            @open-service-manage="openServiceManage"
            @change-category-id="getCategoryTemplate"
          />
        </div>

        <!-- 步骤2：属性信息 -->
        <div v-show="currentStep === 1">
          <AttributeInfo
            ref="attributeInfoRef"
            :modelValue="attributeForm"
            @update:modelValue="(val) => Object.assign(attributeForm, val)"
          />
        </div>

        <!-- 步骤3：规格/SKU -->
        <div v-show="currentStep === 2">
          <SkuInfo
            ref="skuInfoRef"
            :modelValue="skuForm"
            @update:modelValue="handleSkuUpdate"
          />
        </div>

        <!-- 步骤4：商品详情 -->
        <div v-show="currentStep === 3">
          <ProductDetail
            ref="productDetailRef"
            :detailValue="detailForm"
            @update:detailValue="(val) => Object.assign(detailForm, val)"
            :otherValue="otherForm"
            @update:otherValue="(val) => Object.assign(otherForm, val)"
          />
        </div>
      </div>

      <!-- 步骤按钮 -->
      <div class="flex justify-between mt-4">
        <a-button @click="prevStep" :disabled="currentStep === 0"
          >上一步</a-button
        >
        <div>
          <a-button
            type="outline"
            v-if="!isEdit"
            @click="saveAsDraft"
            class="mr-2"
            >保存草稿</a-button
          >
          <a-button v-if="currentStep < 3" type="primary" @click="nextStep"
            >下一步</a-button
          >
          <a-button v-else type="primary" @click="nextStep">提交</a-button>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRoute, useRouter } from "vue-router";
import tool from "~/utils/tool.js";
import goodsApi from "@/api/master/goods";
import { modifyTag, closeCurrentTagAndNavigate } from "~/utils/common.js";
const goodsSpuApi = goodsApi.spu;
// 获取路由参数
const route = useRoute();
const router = useRouter();
const id = route.params.id;

// 判断是否为编辑模式
const isEdit = id != "add";

if (isEdit) {
  const currentTag = {
    path: route.fullPath,
    name: route.name,
  };
  modifyTag(currentTag, "编辑商品");
}
let draft_id = reactive("");
// 定义页面路由元信息
definePageMeta({
  name: "master-addGoods",
  path: "/master/goods/addGoods/:id",
});

// 导入组件
import BasicInfo from "./components/BasicInfo.vue";
import AttributeInfo from "./components/AttributeInfo.vue";
import SkuInfo from "./components/SkuInfo.vue";
import ProductDetail from "./components/ProductDetail.vue";

// 当前步骤
const currentStep = ref(0);

// 组件引用
const basicInfoRef = ref(null);
const attributeInfoRef = ref(null);
const skuInfoRef = ref(null);
const productDetailRef = ref(null);

// 基本信息表单
let basicForm = reactive({
  name: "",
  categoryId: "",
  brandId: "",
  subtitle: "",
  spuImages: [],
  video: "",
});

// 商品标签-商品服务表单
let associations = reactive({
  tagIds: [],
  serviceIds: [],
});

// 物流设置表单
let logisticsForm = reactive({
  isFreeShipping: 1, // 默认包邮：1-包邮，0-不包邮
  freightTemplateId: "",
});

// 配送信息表单
let deliveryForm = reactive({
  deliveryArea: "",
  deliveryTime: "",
});

// 属性信息表单
let attributeForm = reactive({
  attributes: [],
});

// 规格表单
let skuForm = reactive({
  skuType: 1,
  specs: [],
});

// 商品详情表单
let detailForm = reactive({
  content: "",
});

// 其他设置表单
let otherForm = reactive({
  status: 1,
  sort: 0,
  sales: 0,
  keywords: "",
  seoTitle: "",
  seoDescription: "",
});

let goodsData = reactive({});

// 下一步
const nextStep = async () => {
  try {
    // 根据当前步骤进行表单验证
    if (currentStep.value === 0) {
      // 基本信息步骤验证
      await basicInfoRef.value.validate();
      // 验证通过后打印数据，检查是否成功获取到子组件数据
    } else if (currentStep.value === 1) {
      // 属性信息步骤验证
      await attributeInfoRef.value.validate();
    } else if (currentStep.value === 2) {
      // 规格/SKU步骤验证
      await skuInfoRef.value.validate();
    } else if (currentStep.value === 3) {
      // 商品详情步骤验证
      await productDetailRef.value.validate();
      submitForm();
    }

    // 验证通过，进入下一步
    if (currentStep.value < 3) {
      currentStep.value++;
    }
  } catch (error) {}
};

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 打开标签管理
const openLabelManage = () => {
  Message.info("打开标签管理");
  // 这里应该打开标签管理弹窗
};

// 打开服务管理
const openServiceManage = () => {
  Message.info("打开服务管理");
  // 这里应该打开服务管理弹窗
};

// 保存草稿
const saveAsDraft = async () => {
  try {
    // 构建商品数据，与 submitForm 方法一致
    // 这里应该进行完整的表单验证和数据提交
    let data = {
      attributeValues: attributeForm.attributeValues,
      detailForm: detailForm,
      otherForm: otherForm,
      deliveryForm: deliveryForm,
      logisticsForm: logisticsForm,
      associations: associations,
      basicInfo: basicForm,
    };

    data.specifications = change_specifications(skuForm);

    // 调用保存草稿的API
    const res = await goodsSpuApi.goodsDraft({ content: JSON.stringify(data) });

    if (res.code == 200) {
      Message.success(res.message || "已保存为草稿");
    } else {
      Message.error(res.message || "草稿保存失败");
    }
  } catch (error) {
    Message.error("草稿保存失败");
  }
};
const change_specifications = (data) => {
  console.log("data: ", data);
  if (data.skuType == 1) {
    return {
      skuType: 1,
      individual: {
        price: data.price,
        costPrice: data.costPrice,
        stock: data.stock,
        code: data.code,
        image: data.image,
        marketPrice: data.marketPrice,
        salesVolume: data.salesVolume,
      },
    };
  }
  if (data.skuType == 2) {
    return {
      skuType: 2,
      multiple: {
        specs: data.specs,
        skuList: data.skuList,
      },
    };
  }
};
// 提交表单
const submitForm = async () => {
  try {
    // 这里应该进行完整的表单验证和数据提交
    let data = {
      attributeValues: attributeForm.attributeValues,
      detailForm: detailForm,
      otherForm: otherForm,
      deliveryForm: deliveryForm,
      logisticsForm: logisticsForm,
      associations: associations,
      basicInfo: basicForm,
    };

    data.specifications = change_specifications(skuForm);

    // 这里应该调用提交商品的API
    let res = null;
    if (!isEdit) {
      res = await goodsSpuApi.create(data);
    } else {
      res = await goodsSpuApi.update(id, data);
    }

    if (res.code == 200) {
      Message.success(res.message);
      if (draft_id) delDraft();
      // 提交成功后关闭当前标签页并跳转到商品列表页
      closeCurrentTagAndNavigate(router, "/master/goods/goodsList");
    } else {
      Message.error(res.message || "商品添加失败");
    }
  } catch (error) {
    // Message.error("请完善表单信息");
  }
};
const getCategoryTemplate = async (id) => {
  let res = await goodsSpuApi.getCategoryTemplate(id);
  if (res.code == 200) {
    const attributes = res.data.attributes;
    if (isEdit || draft_id) {
      if (goodsData) {
        attributes.forEach((item) => {
          goodsData.attributeValues.forEach((attr) => {
            if (item.id == attr.attributeItemId) {
              item.defaultValue = attr.value;
            }
          });
        });
      }
    }
    attributeInfoRef.value.updata_attributes(attributes);
    // currentStep.value = 1
  }
};

// 处理SKU更新，避免循环更新
const handleSkuUpdate = (val) => {
  // 通过比较判断是否有真正的变化
  if (JSON.stringify(val) !== JSON.stringify(skuForm)) {
    // 使用深拷贝完全替换当前 skuForm
    const newData = JSON.parse(JSON.stringify(val));

    // 清空当前 skuForm 所有数据
    Object.keys(skuForm).forEach((key) => {
      delete skuForm[key];
    });

    // 将新数据全部添加到 skuForm 中
    Object.keys(newData).forEach((key) => {
      skuForm[key] = newData[key];
    });
  }
};

const get_details = async (id) => {
  try {
    // 显示加载中提示
    const loading = Message.loading("正在加载商品信息...");

    // 调用获取商品详情的API
    const res = await goodsSpuApi.getById(id);

    // 关闭加载提示
    loading.close();

    if (res.code === 200) {
      // 使用Object.assign更新goodsData，而不是直接赋值
      Object.assign(goodsData, res.data);

      // 确保deliveryForm存在
      if (goodsData.deliveryForm && goodsData.deliveryForm.deliveryTime) {
        goodsData.deliveryForm.deliveryTime = tool.formatDate(
          goodsData.deliveryForm.deliveryTime
        );
      }

      // 设置表单数据
      setData(goodsData);
      Message.success("商品信息加载成功");
      return true;
    } else {
      Message.error(res.message || "获取商品信息失败");
      return false;
    }
  } catch (error) {
    console.error("获取商品信息失败:", error);
    Message.error("获取商品信息失败");
    return false;
  }
};
const goodsDraftLatest = async () => {
  // 只在新增商品（非编辑模式）时获取最新草稿箱内容
  if (!isEdit) {
    try {
      const { data } = await goodsSpuApi.goodsDraftLatest();
      if (data && data.content) {
        draft_id = data.id;
        // 从草稿箱中获取数据并填充表单
        const parsedData = JSON.parse(data.content);
        // 使用Object.assign更新goodsData，而不是直接赋值
        Object.assign(goodsData, parsedData);

        setDraftData(goodsData);

        // 通知用户已加载草稿箱数据
        Message.success("已加载最新草稿箱内容");
      }
    } catch (error) {}
  }
};
const setData = (goodsData) => {
  // 只保留需要的字段
  const keepFields = ["name", "categoryId", "brandId", "subtitle", "video"];

  // 填充基本信息表单（只保留指定字段）
  if (goodsData.basicInfo) {
    // 重置basicForm
    Object.keys(basicForm).forEach((key) => {
      delete basicForm[key];
    });

    // 只复制需要保留的字段
    keepFields.forEach((field) => {
      if (goodsData.basicInfo[field] !== undefined) {
        basicForm[field] = goodsData.basicInfo[field];
      }
    });

    // 特殊处理 spuImages 字段，从 mediaInfo.images 获取
    if (
      goodsData.mediaInfo &&
      goodsData.mediaInfo.images &&
      Array.isArray(goodsData.mediaInfo.images)
    ) {
      basicForm.spuImages = goodsData.mediaInfo.images.map((item) => item.url);
    } else {
      basicForm.spuImages = [];
    }
  }

  // 填充商品标签-商品服务表单（从 relatedInfo 获取）
  if (goodsData.relatedInfo) {
    // 重置 associations
    associations.tagIds = [];
    associations.serviceIds = [];

    // 从 relatedInfo 获取 tagIds 和 serviceIds
    if (Array.isArray(goodsData.relatedInfo.tags)) {
      associations.tagIds = [
        ...goodsData.relatedInfo.tags.map((item) => item.id),
      ];
    }

    if (Array.isArray(goodsData.relatedInfo.services)) {
      associations.serviceIds = [
        ...goodsData.relatedInfo.services.map((item) => item.id),
      ];
    }
  }

  // 填充物流设置表单
  Object.assign(logisticsForm, goodsData.logisticsForm || {});

  // 填充配送信息表单
  Object.assign(deliveryForm, goodsData.deliveryForm || {});

  // 填充属性信息表单
  if (goodsData.attributeValues) {
    Object.assign(attributeForm, {
      attributeValues: goodsData.attributeValues,
    });
  }

  // 填充规格表单
  if (goodsData.skuInfo) {
    // 根据skuType处理数据
    if (goodsData.skuInfo.skuType === 1) {
      Object.assign(skuForm, {
        skuType: 1,
        price: goodsData.skuInfo.skuList[0].salesPrice,
        costPrice: goodsData.skuInfo.skuList[0].costPrice,
        stock: goodsData.skuInfo.skuList[0].stock,
        code: goodsData.skuInfo.skuList[0].code,
        image: goodsData.skuInfo.skuList[0].image.url,
        marketPrice: goodsData.skuInfo.skuList[0].marketPrice,
        salesVolume: Number(goodsData.skuInfo.skuList[0].salesVolume),
      });
      skuInfoRef.value.updateSingleSkuForm({
        price: goodsData.skuInfo.skuList[0].salesPrice,
        costPrice: goodsData.skuInfo.skuList[0].costPrice,
        stock: goodsData.skuInfo.skuList[0].stock,
        code: goodsData.skuInfo.skuList[0].skuCode,
        image: goodsData.skuInfo.skuList[0].image.url,
        marketPrice: goodsData.skuInfo.skuList[0].marketPrice,
        salesVolume: Number(goodsData.skuInfo.skuList[0].salesVolume),
      });
    } else if (goodsData.skuInfo.skuType === 2) {
      // 从 skuList 中提取规格项及规格值
      const specNames = new Set();
      const specValuesMap = {};

      // 遍历 skuList，收集所有规格项和规格值
      if (goodsData.skuInfo.skuList && goodsData.skuInfo.skuList.length > 0) {
        goodsData.skuInfo.skuList.forEach((sku) => {
          if (sku.specs) {
            // 将字符串形式的规格转换为对象
            let specsObj = {};
            try {
              specsObj =
                typeof sku.specs === "string"
                  ? JSON.parse(sku.specs)
                  : sku.specs;
            } catch (e) {
              specsObj = sku.specs || {};
            }

            // 收集规格项和规格值
            Object.entries(specsObj).forEach(([name, value]) => {
              specNames.add(name);
              if (!specValuesMap[name]) {
                specValuesMap[name] = new Set();
              }
              specValuesMap[name].add(value);
            });
          }
        });
      }

      // 构建规格数组
      const specs = Array.from(specNames).map((name) => ({
        name,
        values: Array.from(specValuesMap[name] || []),
        inputValue: "",
      }));

      // 更新 skuForm
      Object.assign(skuForm, {
        skuType: 2,
        specs: specs,
        skuList: goodsData.skuInfo?.skuList || [],
      });

      // 处理 skuList 中的数据格式
      const formattedSkuList = (goodsData.skuInfo?.skuList || []).map((sku) => {
        // 将字符串形式的规格转换为对象
        let specsObj = {};
        try {
          specsObj =
            typeof sku.specs === "string" ? JSON.parse(sku.specs) : sku.specs;
        } catch (e) {
          specsObj = sku.specs || {};
        }

        return {
          specs: specsObj,
          price: Number(sku.salesPrice) || "",
          costPrice: Number(sku.costPrice) || "",
          stock: Number(sku.stock) || "",
          code: sku.skuCode || "",
          image:
            typeof sku.image === "object" && sku.image !== null
              ? sku.image.url || ""
              : sku.image || "",
          weight: Number(sku.weight) || "",
          volume: Number(sku.volume) || "",
          unit: sku.unit || "",
          marketPrice: Number(sku.marketPrice) || "",
          salesVolume: Number(sku.salesVolume),
        };
      });

      // 通过 nextTick 确保组件已更新后再设置 skuList
      nextTick(() => {
        if (
          skuInfoRef.value &&
          typeof skuInfoRef.value.setSkuList === "function"
        ) {
          skuInfoRef.value.setSkuList(formattedSkuList);
        } else {
          // 备用方案：直接更新 skuForm
          skuForm.skuList = formattedSkuList;
        }
      });
    }
  }

  // 填充商品详情表单
  // 确保 detailForm 中的 content 字段始终有值，避免 WangEditor 的 setHtml 方法出错
  if (goodsData.detailForm && goodsData.detailForm.content !== undefined) {
    // 确保 content 不为 null
    const safeContent = goodsData.detailForm.content || "";
    Object.assign(detailForm, {
      ...goodsData.detailForm,
      content: safeContent,
    });
  } else {
    // 如果没有 detailForm 或 content 字段，确保 detailForm.content 为空字符串
    detailForm.content = detailForm.content || "";
  }

  // 填充其他设置表单（只保留指定字段）
  if (goodsData.otherInfo) {
    const { status, sortOrder, metaKeywords, metaTitle, metaDescription } =
      goodsData.otherInfo;
    Object.assign(otherForm, {
      status: String(status) || 1,
      sort: sortOrder || 0,
      totalSales: goodsData.basicInfo.totalSales || 0,
      keywords: metaKeywords || "",
      seoTitle: metaTitle || "",
      seoDescription: metaDescription || "",
    });
  }

  // 如果有分类ID，获取分类属性模板
  if (basicForm.categoryId) {
    getCategoryTemplate(basicForm.categoryId);
  }
};

const setDraftData = (goodsData) => {
  // 填充基本信息表单（只保留指定字段）
  if (goodsData.basicInfo) {
    Object.assign(basicForm, goodsData.basicInfo || {});
  }

  // 填充商品标签-商品服务表单（从 relatedInfo 获取）
  if (goodsData.associations) {
    // 重置 associations
    associations.tagIds = goodsData.associations.tagIds;
    associations.serviceIds = goodsData.associations.serviceIds;
  }

  // 填充物流设置表单
  Object.assign(logisticsForm, goodsData.logisticsForm || {});

  // 填充配送信息表单
  Object.assign(deliveryForm, goodsData.deliveryForm || {});

  // 填充属性信息表单
  if (goodsData.attributeValues) {
    Object.assign(attributeForm, {
      attributeValues: goodsData.attributeValues,
    });
  }

  // 填充规格表单
  if (goodsData.specifications) {
    // 根据skuType处理数据
    if (goodsData.specifications.skuType === 1) {
      Object.assign(skuForm, {
        skuType: 1,
        ...goodsData.specifications.individual,
      });
      skuInfoRef.value.updateSingleSkuForm({
        ...goodsData.specifications.individual,
      });
    } else if (goodsData.specifications.skuType === 2) {
      // 从 skuList 中提取规格项及规格值
      const specNames = new Set();
      const specValuesMap = {};

      // 遍历 skuList，收集所有规格项和规格值
      if (
        goodsData.specifications.multiple.skuList &&
        goodsData.specifications.multiple.skuList.length > 0
      ) {
        goodsData.specifications.multiple.skuList.forEach((sku) => {
          if (sku.specs) {
            // 将字符串形式的规格转换为对象
            let specsObj = {};
            try {
              specsObj =
                typeof sku.specs === "string"
                  ? JSON.parse(sku.specs)
                  : sku.specs;
            } catch (e) {
              specsObj = sku.specs || {};
            }

            // 收集规格项和规格值
            Object.entries(specsObj).forEach(([name, value]) => {
              specNames.add(name);
              if (!specValuesMap[name]) {
                specValuesMap[name] = new Set();
              }
              specValuesMap[name].add(value);
            });
          }
        });
      }

      // 构建规格数组
      const specs = Array.from(specNames).map((name) => ({
        name,
        values: Array.from(specValuesMap[name] || []),
        inputValue: "",
      }));

      // 更新 skuForm
      Object.assign(skuForm, {
        skuType: 2,
        specs: specs,
        skuList: goodsData.specifications.multiple.skuList || [],
      });
      // 通过 nextTick 确保组件已更新后再设置 skuList
      nextTick(() => {
        if (
          skuInfoRef.value &&
          typeof skuInfoRef.value.setSkuList === "function"
        ) {
          skuInfoRef.value.setSkuList(
            goodsData.specifications.multiple.skuList
          );
        } else {
          // 备用方案：直接更新 skuForm
          skuForm.skuList = formattedSkuList;
        }
      });
    }
  }

  // 填充商品详情表单
  // 确保 detailForm 中的 content 字段始终有值，避免 WangEditor 的 setHtml 方法出错
  if (goodsData.detailForm && goodsData.detailForm.content !== undefined) {
    // 确保 content 不为 null
    const safeContent = goodsData.detailForm.content || "";
    Object.assign(detailForm, {
      ...goodsData.detailForm,
      content: safeContent,
    });
  } else {
    // 如果没有 detailForm 或 content 字段，确保 detailForm.content 为空字符串
    detailForm.content = detailForm.content || "";
  }

  // 填充其他设置表单（只保留指定字段）
  if (goodsData.otherForm) {
    Object.assign(otherForm, {
      ...goodsData.otherForm,
    });
  }

  // 如果有分类ID，获取分类属性模板
  if (basicForm.categoryId) {
    getCategoryTemplate(basicForm.categoryId);
  }
};
const delDraft = async () => {
  let res = await goodsSpuApi.deleteDraft(draft_id);
  if (res.code == 200) {
    Message.success(res.message);
  } else {
    Message.error(res.message);
  }
};
// 初始化
onMounted(() => {
  // 确保当前步骤为基本信息（第一步）
  currentStep.value = 0;

  if (isEdit) {
    // 编辑模式：获取商品详情
    get_details(id);
  } else {
    // 新增模式：获取最新草稿箱内容
    goodsDraftLatest();
  }
});
</script>
