<template>
  <div class="product-line-container">
    <ma-crud
      ref="crudRef"
      :options="crudOptions"
      :columns="columns"
      :data="mockData"
    >
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>

      <!-- 是否自主品牌列 -->
      <template #isOwnBrand="{ record }">
        <a-tag :color="record.isOwnBrand ? 'green' : 'gray'">
          {{ record.isOwnBrand ? '是' : '否' }}
        </a-tag>
      </template>

      <!-- 资质到期时间列 -->
      <template #qualificationExpiry="{ record }">
        <span :class="{ 'text-red-500': isExpiringSoon(record.qualificationExpiry) }">
          {{ record.qualificationExpiry }}
        </span>
      </template>

    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'

// 定义页面路由元信息
definePageMeta({
  name: "master-productLine",
  path: "/master/goods/productLine",
});

// 组件引用
const crudRef = ref()

// CRUD 配置
const crudOptions = reactive({
  title: "产品线管理",
  searchLabelWidth: '100px',
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: false,
  operationColumnWidth: 200,
  add: {
    show: false,
    text: "新增产品线"
  },
  edit: {
    show: false // 使用自定义编辑按钮
  },
  delete: {
    show: false // 使用自定义删除按钮
  },
  table: {
    stripe: true,
    border: true,
    size: 'medium'
  },
  pagination: {
    pageSize: 10,
    showTotal: true,
    showPageSize: true,
    showJumper: true
  }
})

// 表格列配置
const columns = reactive([
  // {
  //   title: "ID",
  //   dataIndex: "id",
  //   addDisplay: false,
  //   editDisplay: false,
  //   width: 70,
  //   align: "center",
  //   fixed: "left",
  //   show: false,
  // },
  {
    title: "一级分类",
    dataIndex: "firstCategory",
    width: 120,
    align: "center",
    search: false,
    formType: "select",
    dict: {
      data: [
        { label: '电子产品', value: '电子产品' },
        { label: '服装鞋帽', value: '服装鞋帽' },
        { label: '家居用品', value: '家居用品' },
        { label: '食品饮料', value: '食品饮料' }
      ]
    }
  },
  {
    title: "品牌分类",
    dataIndex: "brandCategoryLib",
    width: 120,
    align: "center",
    search: true,
    hide: true,
    formType: "select",
    dict: {
      data: [
        { label: '电子产品', value: '电子产品' },
        { label: '服装鞋帽', value: '服装鞋帽' },
        { label: '家居用品', value: '家居用品' },
        { label: '食品饮料', value: '食品饮料' }
      ]
    }
  },
  {
    title: "二级分类",
    dataIndex: "secondCategory",
    width: 120,
    align: "center",
    search: false
  },
  {
    title: "三级分类",
    dataIndex: "thirdCategory",
    width: 120,
    align: "center",
    search: false
  },
  {
    title: "品牌",
    dataIndex: "brand",
    width: 100,
    align: "center",
    search: true
  },
  {
    title: "主体",
    dataIndex: "entity",
    width: 100,
    align: "center",
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: '八灵', value: '八灵' },
        { label: '好帮手', value: '好帮手' },
        { label: '骐麟', value: '骐麟' },
        { label: '文成君泰', value: '文成君泰' }
      ]
    }
  },
  {
    title: "是否自主品牌",
    dataIndex: "isOwnBrand",
    width: 120,
    align: "center",
    slotName: "isOwnBrand",
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: '是', value: true },
        { label: '否', value: false }
      ]
    }
  },
  {
    title: "产品线类型",
    dataIndex: "productLineType",
    width: 120,
    align: "center",
    search: false,
    formType: "select",
    dict: {
      data: [
        { label: '标准产品线', value: '标准产品线' },
        { label: '定制产品线', value: '定制产品线' },
        { label: '特殊产品线', value: '特殊产品线' }
      ]
    }
  },
  {
    title: "一级部门",
    dataIndex: "firstDepartment",
    width: 120,
    align: "center",
    search: false
  },
  {
    title: "采购员",
    dataIndex: "purchaser",
    width: 100,
    align: "center",
    search: false
  },
  {
    title: "产品线分级",
    dataIndex: "productLineLevel",
    width: 100,
    align: "center",
    search: false,
    formType: "select",
    dict: {
      data: [
        { label: 'A级', value: 'A级' },
        { label: 'B级', value: 'B级' },
        { label: 'C级', value: 'C级' }
      ]
    }
  },
  {
    title: "资质到期时间",
    dataIndex: "qualificationExpiry",
    width: 140,
    align: "center",
    slotName: "qualificationExpiry",
    search: true,
    formType: "range"
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 100,
    align: "center",
    slotName: "status",
    search: true,
    formType: "select",
    dict: {
      data: [
        { label: '无效', value: 'invalid' },
        { label: '已生效', value: 'active' },
        { label: '资质过期', value: 'expired' }
      ]
    }
  }
])

// 模拟数据
const mockData = ref([
  {
    id: 1,
    firstCategory: '电子产品',
    secondCategory: '手机通讯',
    thirdCategory: '智能手机',
    brand: '华为',
    entity: '八灵',
    isOwnBrand: true,
    productLineType: '标准产品线',
    firstDepartment: '采购部',
    purchaser: '张三',
    productLineLevel: 'A级',
    qualificationExpiry: '2024-12-31',
    status: 'active'
  },
  {
    id: 2,
    firstCategory: '服装鞋帽',
    secondCategory: '男装',
    thirdCategory: '休闲装',
    brand: '优衣库',
    entity: '好帮手',
    isOwnBrand: false,
    productLineType: '定制产品线',
    firstDepartment: '商品部',
    purchaser: '李四',
    productLineLevel: 'B级',
    qualificationExpiry: '2024-06-30',
    status: 'expired'
  },
  {
    id: 3,
    firstCategory: '家居用品',
    secondCategory: '厨房用品',
    thirdCategory: '锅具',
    brand: '苏泊尔',
    entity: '骐麟',
    isOwnBrand: true,
    productLineType: '标准产品线',
    firstDepartment: '采购部',
    purchaser: '王五',
    productLineLevel: 'A级',
    qualificationExpiry: '2025-03-15',
    status: 'active'
  },
  {
    id: 4,
    firstCategory: '食品饮料',
    secondCategory: '休闲食品',
    thirdCategory: '坚果',
    brand: '三只松鼠',
    entity: '文成君泰',
    isOwnBrand: false,
    productLineType: '特殊产品线',
    firstDepartment: '食品部',
    purchaser: '赵六',
    productLineLevel: 'C级',
    qualificationExpiry: '2024-01-20',
    status: 'invalid'
  },
  {
    id: 5,
    firstCategory: '电子产品',
    secondCategory: '电脑办公',
    thirdCategory: '笔记本电脑',
    brand: '联想',
    entity: '八灵',
    isOwnBrand: true,
    productLineType: '标准产品线',
    firstDepartment: '采购部',
    purchaser: '孙七',
    productLineLevel: 'A级',
    qualificationExpiry: '2024-09-10',
    status: 'active'
  },
  {
    id: 6,
    firstCategory: '服装鞋帽',
    secondCategory: '女装',
    thirdCategory: '连衣裙',
    brand: 'ZARA',
    entity: '好帮手',
    isOwnBrand: false,
    productLineType: '定制产品线',
    firstDepartment: '时尚部',
    purchaser: '周八',
    productLineLevel: 'B级',
    qualificationExpiry: '2024-07-25',
    status: 'expired'
  },
  {
    id: 7,
    firstCategory: '家居用品',
    secondCategory: '家纺',
    thirdCategory: '床上用品',
    brand: '水星家纺',
    entity: '骐麟',
    isOwnBrand: true,
    productLineType: '标准产品线',
    firstDepartment: '家居部',
    purchaser: '吴九',
    productLineLevel: 'A级',
    qualificationExpiry: '2025-01-30',
    status: 'active'
  },
  {
    id: 8,
    firstCategory: '食品饮料',
    secondCategory: '饮料',
    thirdCategory: '果汁',
    brand: '农夫山泉',
    entity: '文成君泰',
    isOwnBrand: false,
    productLineType: '标准产品线',
    firstDepartment: '饮品部',
    purchaser: '郑十',
    productLineLevel: 'B级',
    qualificationExpiry: '2024-11-15',
    status: 'active'
  }
])

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    'active': 'green',
    'expired': 'orange',
    'invalid': 'red'
  }
  return colorMap[status] || 'gray'
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    'active': '已生效',
    'expired': '资质过期',
    'invalid': '无效'
  }
  return textMap[status] || '未知'
}

// 判断资质是否即将到期（30天内）
const isExpiringSoon = (expiryDate) => {
  if (!expiryDate) return false
  const today = new Date()
  const expiry = new Date(expiryDate)
  const diffTime = expiry - today
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays <= 30 && diffDays > 0
}

// 操作方法
const handleView = (record) => {
  Message.info(`查看产品线：${record.brand} - ${record.thirdCategory}`)
}

const handleEdit = (record) => {
  Message.info(`编辑产品线：${record.brand} - ${record.thirdCategory}`)
}

const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除产品线"${record.brand} - ${record.thirdCategory}"吗？`,
    onOk: () => {
      // 这里应该调用删除API
      const index = mockData.value.findIndex(item => item.id === record.id)
      if (index > -1) {
        mockData.value.splice(index, 1)
        Message.success('删除成功')
      }
    }
  })
}
</script>

<style scoped lang="less">
.product-line-container {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
}

.text-red-500 {
  color: #f56565;
  font-weight: 500;
}
</style>