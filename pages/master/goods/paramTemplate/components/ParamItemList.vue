<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <a-modal
    :visible="visible"
    :title="`${record.name || ''} - 参数项目`"
    @cancel="handleCancel"
    :footer="false"
    :width="900"
    :mask-closable="false"
  >
    <div class="params-container">
      <div class="mb-4 flex justify-between">
        <a-button type="primary" @click="addParamItem">
          <template #icon><icon-plus /></template>添加参数
        </a-button>
      </div>
      <!-- 使用a-table组件展示参数项 -->
      <a-table :data="paramItems" :pagination="false" row-key="id">
        <template #columns>
          <a-table-column title="参数名称" data-index="name" :width="120" />
          <a-table-column title="参数类型" data-index="type" :width="100">
            <template #cell="{ record }">
              <a-tag v-if="record.type === 'text'">文本</a-tag>
              <a-tag v-else-if="record.type === 'number'">数字</a-tag>
              <a-tag v-else-if="record.type === 'radio'">单选</a-tag>
              <a-tag v-else-if="record.type === 'checkbox'">多选</a-tag>
              <a-tag v-else-if="record.type === 'select'">下拉选择</a-tag>
            </template>
          </a-table-column>
          <a-table-column title="可选值" data-index="value" :width="150" />
          <a-table-column title="必填" data-index="isRequired" :width="80">
            <template #cell="{ record }">
              <a-tag color="green" v-if="record.isRequired">是</a-tag>
              <a-tag v-else>否</a-tag>
            </template>
          </a-table-column>
          <a-table-column title="可筛选" data-index="isFilterable" :width="80">
            <template #cell="{ record }">
              <a-tag color="blue" v-if="record.isFilterable">是</a-tag>
              <a-tag v-else>否</a-tag>
            </template>
          </a-table-column>
          <a-table-column title="状态" data-index="isEnabled" :width="80">
            <template #cell="{ record }">
              <a-tag color="green" v-if="record.isEnabled">启用</a-tag>
              <a-tag color="red" v-else>禁用</a-tag>
            </template>
          </a-table-column>
          <a-table-column title="操作" :width="200" align="center">
            <template #cell="{ record }">
              <div>
                <a-button type="text" size="small" @click="handleEditParam(record)">
                  <template #icon><icon-edit /></template>
                  编辑
                </a-button>
                <a-popconfirm content="确定要删除该参数项吗?" position="bottom" @ok="removeParamItem(record.id)">
                  <a-button type="text" status="danger" size="small">
                    <template #icon><icon-delete /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </div>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>

    <!-- 参数项编辑组件 -->
    <param-item-edit
      v-model:visible="paramEditVisible"
      :title="paramEditTitle"
      :data="currentParamItem"
      @submit="handleParamSubmit"
    />
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import ParamItemEdit from './ParamItemEdit.vue';
import goodsApi from "@/api/master/goods";
const paramTemplateApi = goodsApi.attrTemplate;
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'save', 'cancel']);

// 参数项编辑相关
const paramEditVisible = ref(false);
const paramEditTitle = ref('编辑参数项');
const currentParamItem = ref({});
const paramItems = ref([]);

// 监听记录变化，初始化参数项数据
watch(() => props.record, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    loadParamItems(newVal);
  }
}, { immediate: true, deep: true });

// 加载参数项数据
const loadParamItems = async (record,type) => {
  if(type)emit('save')
  const res = await paramTemplateApi.getParamList(record.id);
  if(res.code == 200){
    paramItems.value = res.data;
  }
};

// 添加参数项目
const addParamItem = () => {
  paramEditTitle.value = '新增参数项';
  currentParamItem.value = {
    id: 0,
    name: '',
    type: 'text',
    options: '',
    required: 0,
    filterable: 0,
    isEnabled: 1
  };
  paramEditVisible.value = true;
};

// 编辑参数项
const handleEditParam = (record) => {
  paramEditTitle.value = '编辑参数项';
  currentParamItem.value = { ...record };
  paramEditVisible.value = true;
};

// 处理参数项提交
const handleParamSubmit = async (data) => {
  if(!data.options) delete data.options;
  if (currentParamItem.value.id === 0) {
    const res = await paramTemplateApi.addParam(props.record.id,data);
    if (res.code == 200) {
      Message.success('参数项添加成功');
      // 删除后重新加载数据
      await loadParamItems(props.record,'save');
    }else{
      Message.error(res.message || '参数项添加失败');
    }
  } else {
    // 更新参数项
    const res = await paramTemplateApi.updateParam(currentParamItem.value.id, data);
    if (res.code == 200) {
      Message.success('参数项更新成功');
      // 删除后重新加载数据
      await loadParamItems(props.record,'save');
    }else{
      Message.error(res.message || '参数项更新失败');
    }
  }
  paramEditVisible.value = false;
};

// 删除参数项目
const removeParamItem = async (id) => {
  try {
    // 调用API真实删除
    const res = await paramTemplateApi.deleteParam(id);
    if (res.code === 200) {
      // 删除成功后更新前端列表
      // paramItems.value = paramItems.value.filter(item => item.id !== id);
      Message.success('参数项删除成功');
      // 删除后重新加载数据
      await loadParamItems(props.record,'save');
    } else {
      Message.error(res.message || '参数项删除失败');
    }
  } catch (error) {
    console.error('删除参数项出错:', error);
    Message.error('删除参数项失败，请稍后重试');
  }
};

// 保存参数项目
const saveParams = () => {
  // 验证参数项目
  const emptyParams = paramItems.value.filter(item => !item.name || !item.options);
  if (emptyParams.length > 0) {
    Message.error('参数名称和可选值/单位不能为空');
    return false;
  }

  emit('save', {
    id: props.record.id,
    params: paramItems.value,
    params_count: paramItems.value.length
  });
  
  Message.success('参数项目保存成功');
  emit('update:visible', false);
};

// 取消处理
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};
</script>

<style scoped>
.params-container {
  padding: 0 16px;
}
</style>
