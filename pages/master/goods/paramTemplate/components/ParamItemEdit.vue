<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <a-modal
    :visible="visible"
    :title="title"
    @cancel="handleCancel"
    @before-ok="handleSubmit"
    :width="600"
  >
    <ma-form 
      ref="formRef" 
      v-model="formData" 
      :columns="formColumns" 
      class="param-edit-form" 
      :options="{ showButtons: false }"
    >
      <!-- 添加一个空的默认插槽以确保插槽在渲染函数内部被调用 -->
      <template #default></template>
    </ma-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import MaForm from '~/components/base/ma-form/index.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '编辑参数项'
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'submit', 'cancel']);

const formRef = ref();

// 表单数据
const formData = reactive({
  name: '',
  type: '',
  value: '',
  isRequired: 0,
  isFilterable: 0,
  isEnabled: 1
});

// 参数类型选项
const paramTypes = ref([
  { label: '文本', value: 'text' },
  { label: '数字', value: 'number' },
  { label: '单选', value: 'radio' },
  { label: '多选', value: 'checkbox' },
  { label: '下拉选择', value: 'select' }
]);

// 表单列定义
const formColumns = reactive([
  {
    dataIndex: 'name',
    title: '参数名称',
    labelWidth: '80px',
    rules: [{ required: true, message: '参数名称必填' }],
    formType: 'input',
    placeholder: '请输入参数名称'
  },
  {
    dataIndex: 'type',
    title: '参数类型',
    labelWidth: '80px',
    rules: [{ required: true, message: '参数类型必选' }],
    formType: 'select',
    placeholder: '请选择参数类型',
    dict: { data: paramTypes },
    onControl: (value, { formModel }) => {
      return {
        value: {
          display: ['radio', 'checkbox', 'select'].includes(value)
        }
      }
    }
  },
  {
    dataIndex: 'value',
    title: '可选值',
    labelWidth: '80px',
    formType: 'textarea',
    placeholder: '例如：\n蓝色\n黑色\n白色',
    autoSize: { minRows: 5, maxRows: 10 },
    help: '提示：可选值用换行分隔，每行一个选项'
  },
  {
    dataIndex: 'sort',
    title: '排序',
    labelWidth: '80px',
    formType: 'input',
    defaultValue: 0
  },
  {
    dataIndex: 'isRequired',
    title: '是否必填',
    labelWidth: '80px',
    formType: 'switch',
    defaultValue: 0,
    checkedValue: 1,
    uncheckedValue: 0
  },
  {
    dataIndex: 'isFilterable',
    title: '是否可筛选',
    labelWidth: '80px',
    formType: 'switch',
    defaultValue: 0,
    checkedValue: 1,
    uncheckedValue: 0
  },
  {
    dataIndex: 'isEnabled',
    title: '状态',
    labelWidth: '80px',
    formType: 'switch',
    defaultValue: 1,
    checkedValue: 1,
    uncheckedValue: 0
  }
]);

// 监听数据变化，初始化表单数据
watch(() => props.data, (newVal) => {
  if (newVal && Object.keys(newVal).length > 0) {
    Object.assign(formData, {
      name: newVal.name || '',
      type: newVal.type || 'text',
      value: newVal.value || '',
      isRequired: newVal.isRequired || 0,
      isFilterable: newVal.isFilterable || 0,
      isEnabled: newVal.isEnabled || 0,
      sort: newVal.sort || 0
    });
  }
}, { immediate: true, deep: true });

// 取消处理
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};

// 提交处理
const handleSubmit = async (done) => {
 
  try {
    // 获取表单验证结果，如果有错误信息则验证失败
    const validationErrors = await formRef.value.getFormRef().validate();
    // 如果有错误信息，则阻止关闭弹窗
    if (validationErrors) {
      return false;
    }  
    // 验证可选值格式（对于单选/多选/下拉选择类型）
    if (['radio', 'checkbox', 'select'].includes(formData.type) && !formData.value) {
      Message.error('单选/多选/下拉选择类型必须填写可选值');
      return false;
    }
    emit('submit', { ...formData });
    done();
  } catch (error) {
    console.error('表单验证出错:', error);
    return false;
  }
};
</script>

<style scoped>
.param-edit-form :deep(.arco-form-item-label) {
  text-align: right;
  white-space: nowrap;
}
</style>