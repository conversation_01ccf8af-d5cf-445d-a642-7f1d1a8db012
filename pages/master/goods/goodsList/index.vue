<template>
  <div class="ma-content-block p-4">
    <!-- <a-tabs v-model:active-key="activeTab" @change="handleTabChange">
      <a-tab-pane key="all" title="全部商品"></a-tab-pane>
      <a-tab-pane key="on_sale" title="上架"></a-tab-pane>
      <a-tab-pane key="discontinued" title="下架"></a-tab-pane>
    </a-tabs>-->

    <ma-crud
      :options="crudOptions"
      :columns="columns"
      :expandable="expandableConfig"
      ref="crudRef"
    >
      <!-- 商品信息列 -->
      <template #goodsInfo="{ record }">
        <div class="flex items-center py-2">
          <!-- 商品图片 -->
          <a-image
            :src="record.mainImage || '/placeholder.png'"
            width="105"
            height="105"
            fit="cover"
            class="rounded mr-3 flex-shrink-0"
          />
          <div class="flex flex-col min-w-0">
            <!-- 商品名称 -->
            <div :class="['goods-name font-medium text-gray-800 two-lines']">
              <span v-if="record.brandName"> 【{{ record.brandName }}】 </span
              >{{ formatGoodsName(record.name) }}
            </div>
            <div
              class="font-medium text-gray-800 text-xs leading-tight truncate w-full"
            >
              ID: {{ record.id }}
            </div>

            <div
              class="font-medium text-gray-800 text-xs leading-tight truncate w-full"
            >
              分类: {{ record.categoryName }}
            </div>
            <div class="flex text-xs items-center space-x-2">
              在售平台：
              <a-tooltip
                v-for="item in record.channels"
                :key="item.id"
                :content="item.channelName"
              >
                <i :class="[item.channelIcon, 'iconfont', 'text-2xl']"></i>
              </a-tooltip>
            </div>
            <!-- 商品标签 -->
            <div class="mt-1 w-full flex flex-wrap">
              <a-tag
                v-for="tag in record.tags"
                :key="tag"
                size="small"
                class="mr-1 mb-1"
                >{{ tag.name }}</a-tag
              >
            </div>
          </div>
        </div>
      </template>

      <!-- 价格 -->
      <template #price="{ record }">
        <div>{{ record.price }}</div>
      </template>
      <!-- 库存 -->
      <template #stock="{ record }">
        <div>{{ calculateTotalStock(record) }}</div>
      </template>
      <!-- 信息 -->
      <template #info="{ record }">
        <div>
          <div class="flex items-center">
            <span>创建时间：</span
            ><span v-time="record.createdAt" v-if="record.createdAt"></span>
          </div>
          <div class="flex items-center">
            <span>修改时间：</span
            ><span v-time="record.createdAt" v-if="record.createdAt"></span>
          </div>
          <div class="flex items-center">
            <span>当前状态：</span>
            <a-tag :color="record.status ? 'blue' : 'red'">
              {{ record.status ? "已上架" : "已下架" }}
            </a-tag>
          </div>
        </div>
      </template>

      <!-- SPU 服务 -->
      <template #service="{ record }">
        <template v-if="record.service && Array.isArray(record.service)">
          <a-tag
            v-for="srv in record.service"
            :key="srv"
            color="arcoblue"
            class="mr-1 mb-1"
            >{{ srv }}</a-tag
          >
        </template>
        <span v-else>{{ record.service }}</span>
      </template>

      <!-- SKU 展开行 -->
      <template #expand-row="{ record }">
        <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded">
          <div class="max-w-6xl mx-auto">
            <a-table
              :data="record.skus"
              :pagination="false"
              size="small"
              :scroll="{ x: '100%' }"
            >
              <template #columns>
                <a-table-column title="id" data-index="id" :width="170" />
                <a-table-column
                  title="SKU图片"
                  data-index="imageUrl"
                  :width="80"
                  align="center"
                >
                  <template #cell="{ record: skuRecord }">
                    <a-image
                      :src="skuRecord.imageUrl"
                      width="40"
                      height="40"
                      fit="cover"
                    />
                  </template>
                </a-table-column>
                <a-table-column
                  title="SKU名称"
                  data-index="skuName"
                  :width="150"
                ></a-table-column>

                <a-table-column
                  title="在售平台"
                  data-index="channelIcon"
                  :width="120"
                >
                  <template #cell="{ record }">
                    <div class="flex items-center space-x-2">
                      <a-tooltip
                        v-for="item in record.channels"
                        :key="item.id"
                        :content="item.channelName"
                      >
                        <i
                          :class="[item.channelIcon, 'iconfont', 'text-2xl']"
                        ></i>
                      </a-tooltip>
                    </div>
                  </template>
                </a-table-column>
                <a-table-column
                  title="销售价"
                  data-index="salesPrice"
                  :width="100"
                  align="right"
                ></a-table-column>
                <a-table-column
                  title="市场价"
                  data-index="marketPrice"
                  :width="100"
                  align="right"
                ></a-table-column>
                <a-table-column
                  title="成本价"
                  data-index="costPrice"
                  :width="100"
                  align="right"
                ></a-table-column>
                <a-table-column
                  title="销量"
                  data-index="salesVolume"
                  :width="80"
                  align="center"
                ></a-table-column>
                <a-table-column
                  title="库存"
                  data-index="stock"
                  :width="80"
                  align="center"
                ></a-table-column>
                <a-table-column
                  title="单位"
                  data-index="unit"
                  :width="80"
                ></a-table-column>
              </template>
            </a-table>
          </div>
        </div>
      </template>

      <!-- 在售平台 -->
      <template #platformForSale="{ record }">
        <div class="flex items-center space-x-2">
          <a-tooltip
            v-for="channel in getUniqueChannels(record)"
            :key="channel.id"
            :content="channel.name"
          >
            <i :class="[channel.icon, 'iconfont', 'text-2xl']"></i>
          </a-tooltip>
        </div>
      </template>
      <!-- 商品品牌 -->
      <template #brandId="{ record }">
        <div>{{ record.brandName }}</div>
      </template>
      <!-- 状态 -->
      <template #status="{ record }">
        <div>{{ record.status ? "已上架" : "已下架" }}</div>
      </template>
      <!-- 创建时间 -->
      <!-- <template #createdAt="{ record }">
        <div v-time="record.createdAt" v-if="record.createdAt"></div>
        <div v-else></div>
      </template> -->
      <!-- 商品分类 -->
      <template #categoryId="{ record }">
        <div>{{ record.categoryName }}</div>
      </template>
      <!-- 自定义关联分类搜索 -->
      <template #search-categoryId="{ searchForm }">
        <a-cascader
          v-model="searchForm.categoryId"
          :options="options"
          placeholder="请选择分类"
          allow-clear
        />
      </template>

      <template #operationBeforeExtend="{ record }">
        <a-link @click="handleEdit(record)" v-if="!record.deletedAt"
          >编辑</a-link
        >
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import goodsApi from "@/api/master/goods";
const goodsSpuApi = goodsApi.spu;
const brandApi = goodsApi.brand;
const goodsCategoryApi = goodsApi.category;
// 定义页面路由元信息
definePageMeta({
  name: "master-goodsList",
  path: "/master/goods/goodsList",
});
const crudRef = ref();
const activeTab = ref("all");
const router = useRouter();
const displayedData = ref([]);
const options = ref([]);

const selectedCategoryId = ref(null); // 选中的分类ID
// 处理添加商品
const handleAdd = () => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(router, "/master/goods/addGoods/add");
  });
};

// --- ma-crud Basic Options (No API or Data here) ---
const crudOptions = reactive({
  api: goodsSpuApi.getList,
  operationColumn: true,
  operationColumnWidth: 150,
  // --- CRUD 功能开关 ---
  add: {
    show: true,
    text: "添加商品",
    action: handleAdd /* api: '/api/master/goods/save' */,
  },
  // edit and delete handled via operation column
  searchColNumber: 3,
  expandRowByClick: true,
  showExpandRow: true,
  showIndex: false,
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有创建时间参数，转换为毫秒级时间戳
    if (params.createdAt && params.createdAt.length > 0) {
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.createdAt[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startTime = startDate.getTime();

      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.createdAt[1]);
      endDate.setHours(23, 59, 59, 0);
      params.endTime = endDate.getTime();

      delete params.createdAt;
    } else {
      delete params.startTime;
      delete params.endTime;
    }
    return params;
  },
  recycleApi: goodsSpuApi.getRetrieveList,
  delete: {
    show: true,
    api: goodsSpuApi.delete,
    realApi: goodsSpuApi.deleteReal,
  },
  recovery: {
    show: true,
    api: goodsSpuApi.recover,
  },
  pk: "id",
});

const expandableConfig = reactive({
  // title: '展开',
  // width: 60,
});

const statusOptions = [
  { label: "全部", value: "" },
  { label: "已上架", value: "1" },
  { label: "已下架", value: "0" },
];
// SPU 表格列定义
const columns = reactive([
  // {
  //   title: "ID",
  //   dataIndex: "id",
  //   addDisplay: false,
  //   editDisplay: false,
  //   width: 70,
  //   align: "center",
  //   fixed: "left",
  //   show: false,
  // },
  // 商品信息列，合并图片、名称、标签
  {
    title: "商品信息",
    dataIndex: "goodsInfo",
    width: 320,
    slotName: "goodsInfo",
  },
  {
    title: "价格",
    dataIndex: "price",
    width: 90,
    align: "center",
    // sortable: { sortDirections: ["ascend", "descend"], sorter: true },
  },
  {
    title: "库存",
    dataIndex: "stock",
    width: 90,
    align: "center",
    // sortable: { sortDirections: ["ascend", "descend"], sorter: true },
  },
  // {
  //   title: "在售平台",
  //   dataIndex: "platformForSale",
  //   width: 120,
  //   slotName: "platformForSale"
  // },
  {
    title: "关联分类",
    dataIndex: "categoryId",
    search: true,
    hide: true,
    width: 120,
    formType: "cascader",
  },
  {
    title: "商品品牌",
    dataIndex: "brandId",
    search: true,
    hide: true,
    width: 120,
    formType: "select",
    dict: {
      data: async () => {
        const res = await brandApi.getList();
        const transform = (item) => {
          return {
            value: item.id,
            label: item.name,
          };
        };
        return res.data.items.map(transform);
      },
    },
  },

  {
    title: "销量",
    dataIndex: "sales",
    width: 90,
    align: "center",
    // sortable: { sortDirections: ["ascend", "descend"], sorter: true },
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 90,
    align: "center",
    search: true,
    hide: true,
    formType: "select",
    dict: { data: statusOptions },
  },
  {
    title: "商品服务",
    dataIndex: "services",
    width: 150,
    formType: "checkbox",
    hide: true,
    // dict: { data: mockServices, props: { label: "name", value: "id" } },
  },
  {
    title: "相关信息",
    dataIndex: "info",
    width: 170,
    align: "left",

    // sortable: { sortDirections: ["ascend", "descend"], sorter: true },
  },
  {
    title: "创建时间",
    dataIndex: "createdAt",
    width: 170,
    align: "center",
    search: true,
    hide: true,
    formType: "range",
    // sortable: { sortDirections: ["ascend", "descend"], sorter: true },
  },
]);

// 标签页切换处理
const handleTabChange = (key) => {
  activeTab.value = key;
};

const handleEdit = (record) => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(router, `/master/goods/addGoods/${record.id}`);
  });
};
const getCategoryList = async () => {
  const transform = (item) => {
    const result = {
      value: item.id,
      label: item.name,
    };

    if (item.children && item.children.length > 0) {
      result.children = item.children.map(transform);
    }

    return result;
  };
  const res = await goodsCategoryApi.getList();
  options.value = res.data.map(transform);
  console.log(options.value, "options");
};
// 获取唯一的渠道（根据channelId去重）
// 计算商品总库存
const calculateTotalStock = (record) => {
  if (!record.skus || !Array.isArray(record.skus)) return 0;
  return record.skus.reduce((total, sku) => total + (sku.stock || 0), 0);
};

const formatGoodsName = (name) => {
  // 移除所有空格后计算字符数
  const textWithoutSpaces = name.replace(/\s+/g, "");
  if (textWithoutSpaces.length > 15) {
    // 找到第15个非空格字符的位置
    let charCount = 0;
    let splitIndex = 0;
    for (let i = 0; i < name.length; i++) {
      if (name[i] !== " ") {
        charCount++;
        if (charCount === 15) {
          splitIndex = i + 1;
          break;
        }
      }
    }
    return name.slice(0, splitIndex) + "\n" + name.slice(splitIndex);
  }
  return name;
};

const getUniqueChannels = (record) => {
  // 如果没有skus数据，返回空数组
  if (!record.skus || !Array.isArray(record.skus)) return [];

  // 用于存储已处理的channelId
  const processedChannelIds = new Set();
  // 用于存储唯一的渠道信息
  const uniqueChannels = [];

  // 遍历所有sku
  record.skus.forEach((sku) => {
    // 确保channels存在且是数组
    if (sku.channels && Array.isArray(sku.channels)) {
      // 遍历当前sku的所有channel
      sku.channels.forEach((channel) => {
        // 如果该channelId还未处理过且有channelIcon
        if (
          !processedChannelIds.has(channel.channelId) &&
          channel.channelIcon
        ) {
          // 添加到已处理集合
          processedChannelIds.add(channel.channelId);
          // 添加到唯一渠道数组
          uniqueChannels.push({
            id: channel.channelId,
            icon: channel.channelIcon,
            name: channel.channelName || "未知渠道",
          });
        }
      });
    }
  });
  return uniqueChannels;
};

// --- 生命周期钩子 ---
onMounted(() => {
  getCategoryList();
});
</script>
<script>
export default { name: "master-goodsList" };
</script>
<style scoped>
/* 可以添加一些自定义样式 */
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}

:deep(.arco-tabs-nav) {
  padding-left: 16px;
  /* 让 tabs 和下面的内容对齐 */
}

:deep(.arco-table-cell .arco-image-img) {
  object-fit: cover;
  /* 确保图片按比例填充 */
}

.bg-gray-100 {
  background-color: #f7f8fa;
}

.dark .bg-gray-700 {
  background-color: #2a2a2b;
  /* 适配暗黑模式的背景色 */
}

.text-xs {
  font-size: 12px;
  color: #6c6c6c;
  margin-top: 5px;
}
.goods-name {
  font-size: 14px;
  width: 225px;
  line-height: 20px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
  white-space: pre-wrap;
}

.goods-name:not(.two-lines) {
  -webkit-line-clamp: 1;
  height: 20px;
}

.goods-name.two-lines {
  -webkit-line-clamp: 2;
  height: 40px;
  white-space: normal;
}
</style>
