<template>
    <a-modal v-model:visible="visible" @ok="handleOk" @cancel="handleCancel" :width="800">
    <template #title>
        选择地区
    </template>
    <div class="area-container">
      <div v-for="(province, index) in areaOptions" :key="index" class="province-item">
        <div class="province-header">
          <a-checkbox 
            :model-value="isProvinceSelected(province)" 
            @change="(checked) => handleProvinceSelect(province, checked)"
            :indeterminate="isProvinceIndeterminate(province)"
            :disabled="isProvinceDisabled(province)"
          >
            {{ province.name }}
          </a-checkbox>
        </div>
        <div class="city-container flex flex-wrap">
          <div v-for="(city, cityIndex) in province.children" :key="cityIndex" class="city-item">
            <a-checkbox 
              :model-value="isCitySelected(city)" 
              @change="(checked) => handleCitySelect(province, city, checked)"
              :disabled="isCityDisabled(city)"
            >
              {{ city.name }}
            </a-checkbox>
          </div>
        </div>
      </div>
      <div class="selected-area" v-if="selectedAreas.length > 0">
        <div class="selected-title">已选择：</div>
        <div class="selected-items">
          <a-tag 
            v-for="(area, index) in selectedAreas" 
            :key="index" 
            closable 
            @close="removeSelectedArea(area)"
          >
            {{ area.name }}
          </a-tag>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup>
import { ref, computed } from "vue";

const visible = ref(false);
const title = ref('运费模板');
const areaOptions = ref([]);
const selectedAreas = ref([]);
const disabledAreas = ref([]); // 存储其他行已选择的地区，这些地区将被禁用
const emit = defineEmits(['update:visible', 'confirm']);

// 打开弹窗的方法，供父组件调用
const open = (record, otherSelectedAreas = []) => {
  visible.value = true;
  areaOptions.value = JSON.parse(localStorage.getItem('areaOptions') || '[]');
  
  // 设置其他行已选择的地区（需要被禁用的地区）
  disabledAreas.value = otherSelectedAreas || [];
  
  // 如果传入了记录且有已选择的区域，进行回显
  if (record && record.areas && Array.isArray(record.areas)) {
    // 如果有完整的区域信息，直接使用
    selectedAreas.value = [...record.areas];
  } else {
    // 如果没有传入记录或记录中没有区域数据，则清空已选择的区域
    selectedAreas.value = [];
  }
};

// 判断省份是否被选中
const isProvinceSelected = (province) => {
  // 如果省份下的所有城市都被选中，则省份被选中
  if (!province.children || province.children.length === 0) return false;
  return province.children.every(city => isCitySelected(city));
};

// 判断省份是否处于半选状态
const isProvinceIndeterminate = (province) => {
  if (!province.children || province.children.length === 0) return false;
  // 如果有部分城市被选中（不是全部也不是零个），则省份处于半选状态
  const selectedCities = province.children.filter(city => isCitySelected(city));
  return selectedCities.length > 0 && selectedCities.length < province.children.length;
};

// 判断城市是否被选中
const isCitySelected = (city) => {
  return selectedAreas.value.some(area => area.code === city.code);
};

// 判断城市是否应该被禁用
const isCityDisabled = (city) => {
  // 如果城市在其他行已选择的地区中，则禁用
  return disabledAreas.value.some(area => area.code === city.code);
};

// 判断省份是否应该被禁用
const isProvinceDisabled = (province) => {
  // 如果省份下所有城市都已经被禁用，则禁用整个省份
  if (!province.children || province.children.length === 0) return false;
  return province.children.every(city => isCityDisabled(city));
};

// 处理省份选择
const handleProvinceSelect = (province, checked) => {
  // 如果省份全部城市被禁用，则不允许选择
  if (isProvinceDisabled(province)) return;
  // 获取省份下可选择的城市（未被禁用的城市）
  const selectableCities = province.children.filter(city => !isCityDisabled(city));
  
  // // 如果没有可选择的城市，直接返回
  // if (selectableCities.length === 0) return;
  // 检查是否处于半选状态
const isIndeterminate = isProvinceIndeterminate(province);

  if (checked && !isIndeterminate) {
 
    // 全选操作：选中所有未被禁用且未被选中的城市
    const citiesToAdd = [];
    
    selectableCities.forEach(city => {
      // 只有未被选中的城市才添加到选中列表
      if (!isCitySelected(city)) {
        citiesToAdd.push({
          code: city.code,
          name: city.name,
          parentName: province.name
        });
      }
    });
    
    // 如果有需要添加的城市，一次性添加
    if (citiesToAdd.length > 0) {
      selectedAreas.value = [...selectedAreas.value, ...citiesToAdd];
    }
  } else {
    // 反选操作：取消选中所有可选择的城市
    // 获取当前省份下已选中的城市代码列表
    const selectedCityCodes = selectableCities
      .filter(city => isCitySelected(city))
      .map(city => city.code);
    // 如果没有已选中的城市，不需要再处理
    if (selectedCityCodes.length === 0) return;
    
    // 从已选择列表中移除这些城市
    selectedAreas.value = selectedAreas.value.filter(area => 
      !selectedCityCodes.includes(area.code)
    );
  }
};

// 处理城市选择
const handleCitySelect = (province, city, checked) => {
  // 如果城市被禁用，则不允许选择
  if (isCityDisabled(city)) return;
  
  if (checked) {
    // 添加城市到已选择列表
    if (!isCitySelected(city)) {
      selectedAreas.value.push({
        code: city.code,
        name: city.name,
        parentName: province.name
      });
    }
  } else {
    // 从已选择列表中移除城市
    selectedAreas.value = selectedAreas.value.filter(area => area.code !== city.code);
  }
};

// 移除已选择的区域
const removeSelectedArea = (area) => {
  selectedAreas.value = selectedAreas.value.filter(item => item.code !== area.code);
};

// 定义暴露给父组件的属性和方法
defineExpose({
  open
});

const handleOk = () => {
  // 将选择的区域信息传递给父组件，只使用areas格式
  const selectedAreaInfo = {
    name: selectedAreas.value.map(area => `${area.parentName}-${area.name}`).join('、'),
    areas: [...selectedAreas.value]
  };
  emit('confirm', selectedAreaInfo);
  visible.value = false;
  emit('update:visible', false);
};

const handleCancel = () => {
  visible.value = false;
  emit('update:visible', false);
};
</script>

<style scoped>
.area-container {
  max-height: 500px;
  overflow-y: auto;
}

.province-item {
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.province-header {
  font-weight: bold;
  margin-bottom: 8px;
}

.city-container {
  display: flex;
  flex-wrap: wrap;
}

.city-item {
  margin-right: 16px;
  margin-bottom: 8px;
  min-width: 100px;
}

.selected-area {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.selected-title {
  font-weight: bold;
  margin-bottom: 8px;
}

.selected-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>