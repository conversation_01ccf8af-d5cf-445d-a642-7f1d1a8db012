<template>
  <a-drawer
    :width="1000"
    :visible="visible"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title>
      {{ title }}
    </template>
    <div>
      <a-form :model="formData" auto-label-width ref="formRef">
        <a-form-item
          label="模板名称"
          field="name"
          :rules="[{ required: true, message: '请输入模板名称' }]"
        >
          <a-input v-model="formData.name" />
        </a-form-item>
        <a-form-item
          label="计费类型"
          field="chargeType"
          :rules="[{ required: true, message: '请输入模板名称' }]"
        >
          <a-radio-group v-model="formData.chargeType">
            <a-radio :value="1">按件计费</a-radio>
            <a-radio :value="2">按重量计费</a-radio>
            <a-radio :value="3">按体积计费</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="费用设置" field="items">
          <div style="width: 100%">
            <a-table
              :columns="columns"
              :data="formData.items"
              :pagination="false"
            >
              <template #name="{ record, rowIndex }">
                <div @click="handleSelectArea(record, rowIndex)">
                  {{ record.name ? record.name : "请选择地区" }}
                </div>
              </template>
              <template #firstItem="{ record }">
                <a-input-number v-model="record.firstItem" />
              </template>
              <template #firstFee="{ record }">
                <a-input-number v-model="record.firstFee" />
              </template>
              <template #additionalItem="{ record }">
                <a-input-number v-model="record.additionalItem" />
              </template>
              <template #additionalFee="{ record }">
                <a-input-number v-model="record.additionalFee" />
              </template>
              <template #operation="{ record, rowIndex }">
                <a-button
                  v-if="record.name !== '全国'"
                  type="text"
                  size="small"
                  @click="handleDelete(record, rowIndex)"
                  >删除</a-button
                >
              </template>
            </a-table>
            <a-button style="margin-top: 10px" type="primary" @click="handleAdd"
              >添加</a-button
            >
          </div>
        </a-form-item>
      </a-form>
    </div>
  </a-drawer>

  <!-- 选择区域弹窗组件 -->
  <SelectArea ref="selectAreaRef" @confirm="handleAreaSelected" />
</template>
<script setup>
import { ref, shallowRef, nextTick, computed } from "vue";
import SelectArea from "./SelectArea.vue";
import { Message } from "@arco-design/web-vue";
import common from "@/api/common";
import goodsApi from "@/api/master/goods";
const emit = defineEmits(["success"]);
const freightTemplateApi = goodsApi.freightTemplate;
const visible = ref(false);
const title = ref("运费模板");
const formRef = ref();
const formData = ref({
  name: "",
  chargeType: 1,
  items: [
    {
      name: "全国",
      firstItem: 1,
      firstFee: 10,
      additionalItem: 1,
      additionalFee: 5,
      isDefault: 1,
    },
  ],
});
const columns = computed(() => {
  const baseColumns = [
    {
      title: "配送区域",
      dataIndex: "name",
      slotName: "name",
      width: 200,
    },
    {
      title: formData.value.chargeType === 1 ? "首件" : 
            formData.value.chargeType === 2 ? "首重" : "首体积数量",
      dataIndex: "firstItem",
      slotName: "firstItem",
      width: 100,
      align: "center",
    },
    {
      title: formData.value.chargeType === 1 ? "首费" : 
            formData.value.chargeType === 2 ? "首重费用" : "首体积费用",
      dataIndex: "firstFee",
      slotName: "firstFee",
      width: 100,
      align: "center",
    },
    {
      title: formData.value.chargeType === 1 ? "续件" : 
            formData.value.chargeType === 2 ? "续重" : "续体积数量",
      dataIndex: "additionalItem",
      slotName: "additionalItem",
      width: 100,
      align: "center",
    },
    {
      title: formData.value.chargeType === 1 ? "续费" : 
            formData.value.chargeType === 2 ? "续重费用" : "续体积费用",
      dataIndex: "additionalFee",
      slotName: "additionalFee",
      width: 100,
      align: "center",
    },
    {
      title: "操作",
      dataIndex: "operation",
      slotName: "operation",
      width: 50,
      align: "center",
    },
  ];
  return baseColumns;
});
// 打开抽屉方法
const openDrawer = (drawerTitle = "运费模板", data = {}) => {
  console.log("🚀 ~ file: FreightEditDrawer.vue:149 ~ data:", data);
  getAreaOptions();
  title.value = drawerTitle;
  if (data.id) {
    setFormData(data);
  } else {
    formData.value = {
      name: "",
      chargeType: 1,
      items: [
        {
          name: "全国",
          firstItem: 1,
          firstFee: 10,
          additionalItem: 1,
          additionalFee: 5,
          isDefault: 1,
        },
      ],
    };
  }
  visible.value = true;
};

const handleOk = async () => {
  const valid = await formRef.value.validate();
  if (valid) {
    let message = "";
    for (let name in valid) message += valid[name].message + "、";
    Message.error(message.substring(0, message.length - 1));
    return true;
  }

  let pass = true;
  formData.value.items.forEach((item) => {
    if (!item.isDefault) {
      if (item.areas.length === 0) {
        pass = false;
        Message.error("请设置配送区域");
      }
    }
  });
  if (!pass) {
    return true;
  }

  let data = { ...formData.value };
  data.items.forEach(item => {
    delete item.regionRelations;
  });
  data.freightConfigs = data.items;
  delete data.items;
  let res = null;
  if(data.id){
    const id = data.id;
    delete data.id;
    res = await freightTemplateApi.update(id, data);
  }else{
    res = await freightTemplateApi.create(data);
  }
  if (res.code === 200) {
    Message.success(title.value + "成功");
    visible.value = false;
    emit("success");
  } else {
    Message.error(res.message || title.value + "失败");
  }
};

const handleCancel = () => {
  visible.value = false;
};

const handleAdd = () => {
  formData.value.items.push({
    name: "",
    firstItem: 1,
    firstFee: 10,
    additionalItem: 1,
    additionalFee: 5,
    areas: [],
    isDefault: 0,
  });
};
const handleDelete = (record, rowIndex) => {
  formData.value.items.splice(rowIndex, 1);
};

// 选择区域相关
const selectAreaRef = ref(null);
const currentRowIndex = ref(null);

// 处理选择区域
const handleSelectArea = (record, rowIndex) => {
  if (record.name === "全国") {
    return;
  }
  currentRowIndex.value = rowIndex;
  // 获取除当前行外的所有已选择地区
  const otherSelectedAreas = formData.value.items
    .filter((_, index) => index !== rowIndex && _.areas && _.areas.length > 0)
    .flatMap((item) => item.areas || []);

  // 下一个DOM更新周期执行，确保组件已加载
  nextTick(() => {
    if (selectAreaRef.value) {
      selectAreaRef.value.open(record, otherSelectedAreas);
    }
  });
};
// 处理区域选择确认
const handleAreaSelected = (selectedArea) => {
  if (currentRowIndex.value !== null && selectedArea) {
    // 更新表格中对应行的区域名称和完整区域信息
    formData.value.items[currentRowIndex.value].name =
      selectedArea.name || "请选择地区";
    // 保存完整的区域信息，用于编辑回显，只使用areas格式
    formData.value.items[currentRowIndex.value].areas =
      selectedArea.areas || [];
  }
};

const getAreaOptions = async () => {
  const res = await common.getTreeRegion({ onlyProvinceCity: true });
  let data = res.data;

  // 处理数据，子ID前面拼接父ID
  if (data && Array.isArray(data)) {
    data.forEach((province) => {
      if (province.children && Array.isArray(province.children)) {
        province.children.forEach((city) => {
          // 将父ID(省份ID)和子ID(城市ID)用逗号拼接
          city.code = `${province.code},${city.code}`;
        });
      }
    });
  }
  if (res.code === 200) {
    localStorage.setItem("areaOptions", JSON.stringify(data));
  }
  return data;
};

const setFormData = (FormData) => {
    let data = {...FormData}
    data.items = data.freightConfigs.map(item => {
        console.log("🚀 ~ file: FreightEditDrawer.vue:292 ~ item:", item)
        if(item.isDefault){
            item.name = "全国";
            // delete item.regionRelations;
        }else{
            item.name = item.regionRelations.map(area =>area.parentName +  '-' + area.regionName).join("、");
            item.areas = item.regionRelations.map(area =>{
                return {
                    name: area.regionName,
                    parentName: area.parentName,
                    code: area.regionCode
                }
            });
            // delete item.regionRelations;
        }
        return item;
    });
    formData.value = {id: data.id,name: data.name, chargeType: data.chargeType, items: data.items };
};
// 将方法暴露给父组件调用
defineExpose({
  openDrawer,
});
</script>
