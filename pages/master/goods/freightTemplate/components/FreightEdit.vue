<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div>
    <a-modal
      :visible="visible"
      :title="title"
      @cancel="handleCancel"
      @before-ok="handleSubmit"
      :width="800"
    >
      <!-- 基本信息表单 -->
      <div class="basic-info-section mb-4">
        <a-form
          :model="formData"
          layout="horizontal"
          :label-col-props="{ span: 4 }"
          :wrapper-col-props="{ span: 20 }"
        >
          <a-form-item field="name" label="模板名称" required>
            <a-input
              :model-value="formData.name"
              @update:model-value="(value) => (formData.name = value)"
              placeholder="请输入运费模板名称"
            />
          </a-form-item>
          <a-form-item field="chargeType" label="计费类型" required>
            <a-radio-group
              :model-value="formData.chargeType"
              @update:model-value="(value) => (formData.chargeType = value)"
            >
              <a-radio :value="1">按件计费</a-radio>
              <a-radio :value="2">按重量计费</a-radio>
              <a-radio :value="3">按体积计费</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-form>
      </div>

      <!-- 配送区域表格 -->
      <div class="delivery-area-section mt-4">
        <div class="section-header flex justify-between items-center mb-2">
          <div class="text-base font-medium">配送区域</div>
          <a-button type="primary" size="small" @click="addDeliveryArea">
            <template #icon><icon-plus /></template>
            添加配送区域
          </a-button>
        </div>

        <a-table :data="deliveryAreas" :bordered="true" :pagination="false">
          <template #columns>
            <a-table-column title="配送区域" data-index="regionNames">
              <template #cell="{ record }">
                {{ record.regionNames || "全国" }}
              </template>
            </a-table-column>
            <a-table-column :title="firstItemLabel" align="center">
              <template #cell="{ record }">
                <a-input-number
                  :model-value="record.firstItem"
                  @update:model-value="(value) => (record.firstItem = value)"
                  :min="1"
                  :precision="0"
                  :step="1"
                  style="width: 100px"
                />
              </template>
            </a-table-column>
            <a-table-column title="首件运费(元)" align="center">
              <template #cell="{ record }">
                <a-input-number
                  :model-value="record.firstFee"
                  @update:model-value="(value) => (record.firstFee = value)"
                  :min="0"
                  :precision="2"
                  :step="0.1"
                  style="width: 100px"
                />
              </template>
            </a-table-column>
            <a-table-column :title="additionalItemLabel" align="center">
              <template #cell="{ record }">
                <a-input-number
                  :model-value="record.additionalItem"
                  @update:model-value="
                    (value) => (record.additionalItem = value)
                  "
                  :min="1"
                  :precision="0"
                  :step="1"
                  style="width: 100px"
                />
              </template>
            </a-table-column>
            <a-table-column title="续建运费(元)" align="center">
              <template #cell="{ record }">
                <a-input-number
                  :model-value="record.additionalFee"
                  @update:model-value="
                    (value) => (record.additionalFee = value)
                  "
                  :min="0"
                  :precision="2"
                  :step="0.1"
                  style="width: 100px"
                />
              </template>
            </a-table-column>
            <a-table-column title="操作" align="center" :width="80">
              <template #cell="{ record, index }">
                <a-button
                  type="text"
                  status="danger"
                  size="small"
                  @click="removeDeliveryArea(index)"
                >
                  <template #icon><icon-delete /></template>
                  删除
                </a-button>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>
    </a-modal>

    <!-- 添加配送区域弹窗 -->
    <a-modal
      :visible="areaModalVisible"
      @update:visible="(value) => (areaModalVisible = value)"
      title="添加配送区域"
      @ok="confirmAddArea"
      @cancel="areaModalVisible = false"
      :width="500"
    >
      <a-form layout="vertical" :model="newAreaForm">
        <a-form-item label="选择区域" required>
          <a-cascader
            allow-search
            :model-value="newAreaForm.selectedAreas"
            @update:model-value="(value) => (newAreaForm.selectedAreas = value)"
            :options="areaOptions"
            placeholder="请选择配送区域"
            expand-trigger="hover"
            multiple
            path-mode
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconPlus, IconDelete } from "@arco-design/web-vue/es/icon";
import common from "@/api/common";
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "新增运费模板",
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "submit"]);

const formData = reactive({
  name: "",
  chargeType: 1, // 默认按件计费
  // useSpecificAreas: true, // 是否指定区域配送
  // hasNoDeliveryAreas: false // 是否有不配送区域
});

// 配送区域数据
const deliveryAreas = ref([
  {
    regionCodes: "全国",
    regionNames: "全国",
    firstItem: 1,
    firstFee: 10,
    additionalItem: 1,
    additionalFee: 5,
  },
]);

// 根据计费类型动态显示文本
const firstItemLabel = computed(() => {
  return formData.chargeType === 1
    ? "首件数量(件)"
    : formData.chargeType === 2
    ? "首重(kg)"
    : "首体积(m³)";
});

const additionalItemLabel = computed(() => {
  return formData.chargeType === 1
    ? "续件数量(件)"
    : formData.chargeType === 2
    ? "续重(kg)"
    : "续体积(m³)";
});

// 添加配送区域相关
const areaModalVisible = ref(false);
const newAreaForm = reactive({
  selectedAreas: [],
});

const areaOptions = ref([]);

// 监听数据变化，初始化表单
watch(
  () => props.data,
  (newVal) => {
    console.log("🚀 ~ file: FreightEdit.vue:309 ~ newVal:", newVal);
    if (newVal && Object.keys(newVal).length > 0) {
      // 重置表单数据
      Object.assign(formData, {
        name: newVal.name || "",
        chargeType: newVal.chargeType || 1,
      });

      // 处理配送区域数据
      if (newVal.regionConfigs && Array.isArray(newVal.regionConfigs)) {
        let temp = [];
        // 如果已经是数组格式，直接使用
        deliveryAreas.value = newVal.regionConfigs.map((area) => {
          console.log("🚀 ~ file: FreightEdit.vue:245 ~ area:", area);
          temp.push(area.regionCodes.split(","));
          return {
            regionCodes: area.regionCodes,
            regionNames: area.regionNames,
            firstItem: area.firstItem,
            firstFee: Number(area.firstFee),
            additionalItem: area.additionalItem,
            additionalFee: Number(area.additionalFee),
          };
        });
        newAreaForm.selectedAreas = temp;
      } else {
        // 默认值
        deliveryAreas.value = [
          {
            regionCodes: "0",
            regionNames: "全国",
            firstItem: 1,
            firstFee: 10,
            additionalItem: 1,
            additionalFee: 5,
          },
        ];
      }
    } else {
      // 如果没有数据，重置为默认值
      Object.assign(formData, {
        name: "",
        chargeType: 1,
      });
      deliveryAreas.value = [
        {
          regionCodes: "0",
          regionNames: "全国",
          firstItem: 1,
          firstFee: 10,
          additionalItem: 1,
          additionalFee: 5,
        },
      ];
    }
  },
  { immediate: true, deep: true }
);

// 打开添加配送区域弹窗
const addDeliveryArea = () => {
  // if (props.title === "新增运费模板") {
  //   newAreaForm.selectedAreas = [];
  // }
  areaModalVisible.value = true;
};

// 确认添加配送区域
const confirmAddArea = () => {
  if (!newAreaForm.selectedAreas || newAreaForm.selectedAreas.length === 0) {
    Message.warning("请选择配送区域");
    return;
  }

  // 获取选中的最后一级区域
  const selectedAreas = Array.isArray(newAreaForm.selectedAreas[0])
    ? newAreaForm.selectedAreas
    : [newAreaForm.selectedAreas];

  selectedAreas.forEach((areaPath) => {
    // 处理区域编码和名称
    const regionCodes = [];
    const regionNames = [];

    // 检查areaPath的格式并提取区域信息
    if (Array.isArray(areaPath)) {
      // 多级选择的情况
      areaPath.forEach((area) => {
        regionCodes.push(area);
        regionNames.push(area);
      });
    } else if (typeof areaPath === "object") {
      // 单个对象的情况
      if (areaPath.code && areaPath.name) {
        regionCodes.push(areaPath.code);
        regionNames.push(areaPath.name);
      }
    } else if (typeof areaPath === "string") {
      // 单个字符串的情况
      regionCodes.push(areaPath);
      regionNames.push(areaPath);
    }

    // 检查是否已存在相同的区域
    const exists = deliveryAreas.value.some((item) => {
      return item.regionCodes === regionCodes.join(",");
    });

    // 如果不存在则添加
    if (!exists && regionCodes.length > 0) {
      deliveryAreas.value.push({
        regionCodes: regionCodes.join(","), // 区域编码路径，多个用逗号分隔
        regionNames: findRegionNameById(regionCodes) || regionNames.join(","), // 只保留最后一级名称
        firstItem: 1,
        firstFee: 10,
        additionalItem: 1,
        additionalFee: 5,
      });
    } else if (exists) {
      Message.warning(
        `区域"${findRegionNameById(regionCodes)}"已存在，不可重复添加`
      );
    }
  });

  // 关闭弹窗
  areaModalVisible.value = false;
  // 清空选择
  // newAreaForm.selectedAreas = [];
};

// 删除配送区域
const removeDeliveryArea = (index) => {
  deliveryAreas.value.splice(index, 1);
};

// 取消处理
const handleCancel = () => {
  newAreaForm.selectedAreas = [];
  emit("update:visible", false);
};

// 提交处理
const handleSubmit = async (done) => {
  // 表单验证
  if (!formData.name) {
    Message.warning("请输入运费模板名称");
    return false;
  }

  if (deliveryAreas.value.length === 0) {
    Message.warning("请至少添加一个配送区域");
    return false;
  }

  const dataToSubmit = {
    ...formData,
    deliveryAreas: deliveryAreas.value,
  };

  try {
    emit("submit", dataToSubmit);
    // 清空选择
    newAreaForm.selectedAreas = [];
    done(true);
  } catch (error) {
    console.error("提交失败:", error);
    Message.error("操作失败，请重试");
    done(false);
  }
};

const getTreeList = async () => {
  try {
    const res = await common.getTreeRegion();
    if (res && res.code === 200 && res.data) {
      // 清空现有数据
      areaOptions.value = [];

      // 将API返回的数据转换为组件需要的格式并加载到province中
      res.data.forEach((item) => {
        const provinceItem = {
          label: item.name,
          value: item.code.toString(),
        };

        // 处理市级数据
        if (item.children && item.children.length > 0) {
          provinceItem.children = [];
          item.children.forEach((cityItem) => {
            const city = {
              label: cityItem.name,
              value: cityItem.code.toString(),
            };

            // 处理区县级数据
            if (cityItem.children && cityItem.children.length > 0) {
              city.children = [];
              cityItem.children.forEach((areaItem) => {
                city.children.push({
                  label: areaItem.name,
                  value: areaItem.code.toString(),
                });
              });
            }

            provinceItem.children.push(city);
          });
        }

        areaOptions.value.push(provinceItem);
      });

      console.log(
        "区域数据加载成功，共加载省份数量：",
        areaOptions.value.length
      );
      areaOptions.value.unshift({
        label: "全国",
        value: 0,
        // children: []
      });
    } else {
      console.error("获取区域数据失败：", res);
      // 加载失败时使用备用的空数据结构
      areaOptions.value = [];
    }
  } catch (error) {
    console.error("获取区域数据出错：", error);
    // 出错时使用备用的空数据结构
    areaOptions.value = [];
  }
};

/**
 * 根据区域ID集合查找区域名称
 * @param {Array<number|string>} areaIds - 需要查找的区域ID集合
 * @returns {string} - 找到的区域名称，多个地区用空格分隔，如果未找到则返回空字符串
 */
const findRegionNameById = (areaIds) => {
  console.log("🚀 ~ file: FreightEdit.vue:468 ~ areaIds:", areaIds);
  if (!Array.isArray(areaIds)) {
    return "";
  }

  // 如果是空数组，返回空字符串
  if (areaIds.length === 0) {
    return "";
  }

  // 如果包含"全国"，直接返回"全国"
  if (areaIds.includes("全国")) {
    return "全国";
  }

  const result = [];

  // 递归搜索函数，用于遍历区域树
  const search = (tree) => {
    // 如果当前节点的value在目标ID集合中，只记录当前节点的label
    if (areaIds.includes(tree.value)) {
      result.push(tree.label);
    }

    // 如果当前节点有子节点，递归搜索每个子节点
    if (tree.children && tree.children.length > 0) {
      for (const child of tree.children) {
        search(child);
      }
    }
  };

  // 遍历areaOptions数组中的每个顶级节点
  for (const province of areaOptions.value) {
    search(province);
  }

  // 返回所有找到的地区名称，用空格分隔
  return result.join(",");
};

onMounted(() => {
  getTreeList();
});
</script>

<style scoped>
.basic-info-section {
  margin-bottom: 16px;
}

.delivery-area-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.section-header {
  margin-bottom: 12px;
}

.delivery-settings {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}
</style>
