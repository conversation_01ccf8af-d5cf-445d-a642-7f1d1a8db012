<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-popconfirm
            content="确定要删除该运费模板吗?"
            position="bottom"
            @ok="handleDelete(record.id)"
          >
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>

      <!-- 自定义列 - 计费类型 -->
      <template #chargeType="{ record }">
        <a-tag
          :color="
            record.chargeType === 1
              ? 'blue'
              : record.chargeType === 2
              ? 'orangered'
              : 'green'
          "
        >
          {{ chargeTypeMap[record.chargeType] }}
        </a-tag>
      </template>
      <!-- 自定义列 - 创建时间 -->
      <template #created_at="{ record }">
        <div v-time="record.createdAt" v-if="record.createdAt" />
      </template>
    </ma-crud>    
    <!-- 运费模板抽屉组件 -->
    <freight-edit-drawer ref="freightDrawerRef" @success="handleSuccess" />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconEdit, IconDelete, IconPlus } from "@arco-design/web-vue/es/icon";
import FreightEdit from "./components/FreightEdit.vue";
import FreightEditDrawer from "./components/FreightEditDrawer.vue";
import goodsApi from "@/api/master/goods";
const freightTemplateApi = goodsApi.freightTemplate;
definePageMeta({
  name: "master-freightTemplate",
  path: "/master/goods/freightTemplate",
});

const crudRef = ref();
const freightDrawerRef = ref(null);
const modalVisible = ref(false);
const modalTitle = ref("新增运费模板");
const currentTemplate = ref({});
const currentId = ref(null);

const chargeTypeMap = {
  1: "按件数",
  2: "按重量",
  3: "按体积",
};

// 打开新增弹窗
const handleAdd = () => {
  // 使用抽屉组件
  freightDrawerRef.value.openDrawer("新增运费模板", {
    name: "",
    chargeType: 1
  });
};

// 打开编辑弹窗
const handleEdit = (record) => {
  console.log("🚀 ~ file: index.vue:108 ~ record:", record)
  // const data = {...record}
  freightDrawerRef.value.openDrawer("编辑运费模板", record);
};

// 处理删除
const handleDelete = async (id) => {
  const result = await freightTemplateApi.delete(id);
  if (result.code == 200) {
    Message.success("删除成功");
    crudRef.value.refresh(); // 刷新表格
  } else {
    Message.error(result.message || "删除失败");
  }
};

// CRUD 配置
const crud = reactive({
  api: freightTemplateApi.getList, // 实际项目中应该填写API路径
  pageLayout: "fixed",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 200,
  add: {
    show: false, // 使用自定义新增按钮
  },
  edit: {
    show: false, // 使用自定义编辑按钮
  },
  delete: {
    show: false, // 使用自定义删除按钮
  },

});

// 表格列配置
const columns = reactive([
  {
    title: "模板名称",
    dataIndex: "name",
    width: 150,
    search:true
  },
  {
    title: "计费类型",
    dataIndex: "chargeType",
    width: 100,
    slotName: "chargeType",
    search:false
  },
  {
    title: "配送区域",
    dataIndex: "deliveryAreas",
    ellipsis: true,
  },
  {
    title: "创建时间",
    dataIndex: "created_at",
    width: 200,
    
  },
]);
const handleSuccess = () => {
  crudRef.value.refresh();
};
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>
