
<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义列 - 图片 -->
      <template #imageUrl="{ record }">
        <a-image
          :src="record.imageUrl"
          width="60"
          height="60"
          fit="contain"
        />
      </template>
       <!-- 自定义列 - 创建时间 -->
       <template #createdAt="{ record }">
        <div v-time="record.createdAt"></div>
      </template>
    </MaCrud>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconEdit, IconDelete, IconPlus } from '@arco-design/web-vue/es/icon';
import goodsApi from '@/api/master/goods';
const tagApi = goodsApi.tag;

definePageMeta({
  name: "master-labelManage",
  path: "/master/goods/labelManage",
})

const crudRef = ref();
// CRUD 配置
const crud = reactive({
  api: tagApi.getList, // 使用API接口
  pageLayout: 'fixed',
  operationColumn: true,
  operationColumnWidth: 200,
  searchLabelWidth: '100px',
  add: {
    show: true,
    api: tagApi.create
  },
  edit: {
    show: true,
    api: tagApi.update
  },
  delete: {
    show: true,
    api: tagApi.delete
  },
   // 搜索前处理参数
   beforeSearch: (params) => {
    // 如果有创建时间参数，转换为时间戳
    if(params.createdAt && params.createdAt.length > 0){
     // 设置开始时间为当天的00:00:00
     const startDate = new Date(params.createdAt[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startTime = startDate.getTime() ;
      
      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.createdAt[1]);
      endDate.setHours(23, 59, 59, 999);
      params.endTime = endDate.getTime() ;
      
      delete params.createdAt
    }else{
      delete params.startTime
      delete params.endTime
    }
    return params;
  },
});

// 表格列配置
const columns = reactive([
  {
    title: '标签名称',
    dataIndex: 'name',

    search: true,
    commonRules: [{ required: true, message: '标签名称必填' }],
  },
  {
    title: '图片',
    dataIndex: 'imageUrl',
    formType: 'upload',
    // 限制文件类型
    accept: '.jpg,.jpeg,.png,.gif',
    // 限制文件大小为2MB
    size: 2 * 1024 * 1024,
    // 限制上传数量为1张
    limit: 1,
    // 返回类型为URL
    returnType: 'url',
    // 显示已上传图片列表
    showList: true,
    // 不允许多选
    multiple: false,
    // 提示信息
    tip: '建议上传正方形图片，大小不超过2MB',
    // 指定为图片上传
    type: 'image',
    // 是否显示为圆形
    rounded: false
  },
  {
    title: '标签说明',
    dataIndex: 'description',

    search: true,
    ellipsis: true,
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    formType: 'inputNumber',
    sortable: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    formType: "range",
    search: true, 
    addDisplay: false, 
    editDisplay: false,
  }
]);
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>