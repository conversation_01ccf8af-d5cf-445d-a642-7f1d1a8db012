
<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef" >
      <!-- 自定义列 - logo图片 -->
     <template #logoUrl="{ record }">
        <a-image
          :src="record.logoUrl"
          width="60"
          height="60"
          fit="contain"
        />
      </template>
      <!-- 自定义列 - 创建时间 -->
      <template #createdAt="{ record }">
        <div v-time="record.createdAt"></div>
      </template>
    </MaCrud>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconEdit, IconDelete, IconPlus } from '@arco-design/web-vue/es/icon';
import goodsApi from '@/api/master/goods';
const brandApi = goodsApi.brand;
definePageMeta({
  name: "master-brandManage",
  path: "/master/goods/brandManage",
})

const crudRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增品牌');
const currentBrand = ref({});
const currentId = ref(null);


// CRUD 配置
const crud = reactive({
  api: brandApi.getList, // 实际项目中应该填写API路径
  pageLayout: 'fixed',
  //rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 200,
  searchLabelWidth: '100px',
  add: {
    show: true,api: brandApi.create // 使用自定义新增按钮
  },
  edit: {
    show: true,api: brandApi.update // 使用自定义编辑按钮
  },
  delete: {
    show: true,api: brandApi.delete // 使用自定义删除按钮
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有创建时间参数，转换为毫秒级时间戳
    if(params.createdAt && params.createdAt.length > 0){
      // 设置开始时间为当天的00:00:00
      const startDate = new Date(params.createdAt[0]);
      startDate.setHours(0, 0, 0, 0);
      params.startTime = startDate.getTime() ;
      
      // 设置结束时间为当天的23:59:59
      const endDate = new Date(params.createdAt[1]);
      endDate.setHours(23, 59, 59, 999);
      params.endTime = endDate.getTime() ;;
      
      delete params.createdAt
    }else{
      delete params.startTime
      delete params.endTime
    }
    return params;
  },
});

// 表格列配置
const columns = reactive([
{
    title: 'ID',
    dataIndex: 'id',
    search: false, 
    addDisplay: false, 
    editDisplay: false,
  },
  {
    title: '品牌名称',
    dataIndex: 'name',
    search: true, 
    commonRules: [{ required: true, message: '品牌名称必填' }],
  },
  {
    title: '品牌Logo',
    dataIndex: 'logoUrl',
    formType: 'upload',
    // 限制文件类型
    accept: '.jpg,.jpeg,.png,.gif',
    // 限制文件大小为2MB
    size: 2 * 1024 * 1024,
    // 限制上传数量为1张
    limit: 1,
    // 返回类型为URL
    returnType: 'url',
    // 显示已上传图片列表
    showList: true,
    // 不允许多选
    multiple: false,
    // 提示信息
    tip: '建议上传正方形图片，大小不超过2MB',
    // 指定为图片上传
    type: 'image',
    // 是否显示为圆形
    rounded: false
  },
  {
    title: '品牌描述',
    dataIndex: 'description',
    search: true, 
    ellipsis: true,
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    formType: "range",
    search: true, 
    addDisplay: false, 
    editDisplay: false,
  }
]);
</script>

<style scoped>
.ma-content-block {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
</style>