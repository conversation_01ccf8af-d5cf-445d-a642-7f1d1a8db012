<template>
  <div class="basic-info">
    <div class="info-header">
      <h3>基础信息</h3>
    </div>
    <div class="info-content">
      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">供应商名称：</span>
            <span class="value">{{ supplierData.supplier_name || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">公司性质：</span>
            <span class="value">{{ supplierData.company_nature || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">主营产品：</span>
            <span class="value">
              <a-tag 
                v-for="product in supplierData.main_products" 
                :key="product" 
                color="blue"
                style="margin-right: 4px;"
              >
                {{ product }}
              </a-tag>
              <span v-if="!supplierData.main_products?.length">-</span>
            </span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">合作方式：</span>
            <span class="value">
              <a-tag :color="getCooperationTypeColor(supplierData.cooperation_type)">
                {{ supplierData.cooperation_type || '-' }}
              </a-tag>
            </span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">供应商代码：</span>
            <span class="value">{{ supplierData.supplier_code || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">下单次数：</span>
            <span class="value">{{ supplierData.order_count || 0 }}次</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">合作状态：</span>
            <span class="value">
              <a-tag :color="getCooperationStatusColor(supplierData.cooperation_status)">
                {{ supplierData.cooperation_status || '-' }}
              </a-tag>
            </span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">合作协议：</span>
            <span class="value">{{ supplierData.cooperation_agreement || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">供应商分组：</span>
            <span class="value">{{ supplierData.supplier_group || '-' }}</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">通讯地址：</span>
            <span class="value">{{ supplierData.address || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">注册时间：</span>
            <span class="value">{{ supplierData.register_time || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">统一信用代码：</span>
            <span class="value">{{ supplierData.credit_code || '-' }}</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="8">
          <div class="info-item">
            <span class="label">供应商关联：</span>
            <span class="value">{{ supplierData.supplier_relation || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">详细地址：</span>
            <span class="value">{{ supplierData.detailed_address || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="8">
          <div class="info-item">
            <span class="label">生产地址：</span>
            <span class="value">{{ supplierData.production_address || '-' }}</span>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  supplierData: {
    type: Object,
    default: () => ({})
  }
});

// 获取合作方式颜色
const getCooperationTypeColor = (type) => {
  const colorMap = {
    "长期合作": "blue",
    "项目合作": "green",
    "临时采购": "orange"
  };
  return colorMap[type] || "gray";
};

// 获取合作状态颜色
const getCooperationStatusColor = (status) => {
  const colorMap = {
    "正常合作": "green",
    "暂停合作": "orange",
    "待审核": "blue",
    "已终止": "red"
  };
  return colorMap[status] || "gray";
};
</script>

<style scoped>
.basic-info {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
}

.info-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.info-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-1);
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  min-height: 32px;
}

.label {
  font-weight: 500;
  color: var(--color-text-2);
  min-width: 120px;
  flex-shrink: 0;
}

.value {
  color: var(--color-text-1);
  flex: 1;
  word-break: break-all;
}
</style>
