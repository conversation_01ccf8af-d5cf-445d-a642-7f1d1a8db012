<template>
  <div class="contact-info">
    <div class="info-header">
      <h4>联系人信息</h4>
      <span class="contact-count">共 {{ contacts.length }} 个联系人</span>
    </div>
    <div class="contact-content">
      <div v-if="contacts.length === 0" class="empty-state">
        <a-empty description="暂无联系人信息" />
      </div>
      <div v-else class="contact-grid">
        <div 
          v-for="contact in contacts" 
          :key="contact.id || contact.name" 
          class="contact-card"
        >
          <div class="contact-card-header">
            <div class="contact-avatar">
              <a-avatar :size="48">
                <template #trigger-icon>
                  <icon-user />
                </template>
                {{ contact.name ? contact.name.charAt(0) : 'U' }}
              </a-avatar>
            </div>
            <div class="contact-basic">
              <div class="contact-name">{{ contact.name || '-' }}</div>
              <div class="contact-position">{{ contact.position || '-' }}</div>
            </div>
          </div>
          <div class="contact-card-body">
            <div class="contact-detail">
              <div class="detail-item">
                <icon-phone class="detail-icon" />
                <span class="detail-label">电话：</span>
                <span class="detail-value">{{ contact.phone || '-' }}</span>
              </div>
              <div class="detail-item" v-if="contact.email">
                <icon-email class="detail-icon" />
                <span class="detail-label">邮箱：</span>
                <span class="detail-value">{{ contact.email }}</span>
              </div>
              <div class="detail-item" v-if="contact.department">
                <icon-user-group class="detail-icon" />
                <span class="detail-label">部门：</span>
                <span class="detail-value">{{ contact.department }}</span>
              </div>
              <div class="detail-item" v-if="contact.is_primary">
                <icon-star-fill class="detail-icon primary-icon" />
                <span class="detail-label">主要联系人</span>
              </div>
              <div class="detail-item" v-if="contact.remark">
                <icon-message class="detail-icon" />
                <span class="detail-label">备注：</span>
                <span class="detail-value">{{ contact.remark }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  supplierData: {
    type: Object,
    default: () => ({})
  }
});

// 计算联系人列表
const contacts = computed(() => {
  return props.supplierData.contacts || [];
});
</script>

<style scoped>
.contact-info {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.info-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.contact-count {
  font-size: 14px;
  color: var(--color-text-3);
}

.contact-content {
  min-height: 200px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.contact-card {
  background-color: var(--color-bg-1);
  border: 1px solid var(--color-border-2);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.contact-card:hover {
  border-color: var(--color-primary-light-4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.contact-avatar {
  margin-right: 12px;
}

.contact-basic {
  flex: 1;
}

.contact-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
  margin-bottom: 4px;
}

.contact-position {
  font-size: 14px;
  color: var(--color-text-3);
}

.contact-card-body {
  margin-top: 12px;
}

.contact-detail {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-icon {
  font-size: 14px;
  color: var(--color-text-3);
  flex-shrink: 0;
}

.primary-icon {
  color: var(--color-warning-6);
}

.detail-label {
  font-size: 14px;
  color: var(--color-text-3);
  flex-shrink: 0;
}

.detail-value {
  font-size: 14px;
  color: var(--color-text-2);
  flex: 1;
  word-break: break-all;
}
</style>
