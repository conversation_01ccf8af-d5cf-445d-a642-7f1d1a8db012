<template>
  <div class="agreement-info">
    <div class="info-header">
      <h4>协议信息</h4>
      <span class="agreement-count">共 {{ agreements.length }} 个协议</span>
    </div>
    <div class="agreement-content">
      <div v-if="agreements.length === 0" class="empty-state">
        <a-empty description="暂无协议信息" />
      </div>
      <div v-else class="agreement-list">
        <a-table 
          :columns="columns" 
          :data="agreements" 
          :pagination="false"
          :bordered="false"
          size="medium"
        >
          <template #agreement_type="{ record }">
            <a-tag :color="getAgreementTypeColor(record.agreement_type)">
              {{ record.agreement_type }}
            </a-tag>
          </template>
          
          <template #agreement_period="{ record }">
            <div class="period-info">
              <div class="period-dates">
                {{ record.start_date }} ~ {{ record.end_date }}
              </div>
              <div class="period-duration" v-if="record.duration">
                <a-tag size="small" color="blue">{{ record.duration }}</a-tag>
              </div>
            </div>
          </template>

          <template #status="{ record }">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>

          <template #actions="{ record }">
            <a-space>
              <a-button type="text" size="small" @click="viewAgreement(record)">
                <template #icon><icon-eye /></template>
                查看
              </a-button>
              <a-button type="text" size="small" @click="downloadAgreement(record)" v-if="record.file_url">
                <template #icon><icon-download /></template>
                下载
              </a-button>
            </a-space>
          </template>
        </a-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  supplierData: {
    type: Object,
    default: () => ({})
  }
});

// 计算协议列表
const agreements = computed(() => {
  return props.supplierData.agreements || [];
});

// 表格列配置
const columns = [
  {
    title: '协议类型',
    dataIndex: 'agreement_type',
    slotName: 'agreement_type',
    width: 120
  },
  {
    title: '协议时间',
    dataIndex: 'agreement_period',
    slotName: 'agreement_period',
    width: 200
  },
  {
    title: '协议名称',
    dataIndex: 'agreement_name',
    width: 200
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 100
  },
  {
    title: '签署日期',
    dataIndex: 'sign_date',
    width: 120
  },
  {
    title: '操作',
    dataIndex: 'actions',
    slotName: 'actions',
    width: 120,
    fixed: 'right'
  }
];

// 获取协议类型颜色
const getAgreementTypeColor = (type) => {
  const colorMap = {
    '供货协议': 'blue',
    '服务协议': 'green',
    '技术协议': 'orange',
    '保密协议': 'purple',
    '框架协议': 'cyan'
  };
  return colorMap[type] || 'gray';
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'active': 'green',
    'expired': 'red',
    'pending': 'orange',
    'draft': 'gray'
  };
  return colorMap[status] || 'gray';
};

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    'active': '生效中',
    'expired': '已过期',
    'pending': '待生效',
    'draft': '草稿'
  };
  return textMap[status] || '未知';
};

// 查看协议
const viewAgreement = (record) => {
  Message.info(`查看协议：${record.agreement_name}`);
};

// 下载协议
const downloadAgreement = (record) => {
  Message.info(`下载协议：${record.agreement_name}`);
};
</script>

<style scoped>
.agreement-info {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.info-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.agreement-count {
  font-size: 14px;
  color: var(--color-text-3);
}

.agreement-content {
  min-height: 200px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.agreement-list {
  background-color: var(--color-bg-1);
  border-radius: 6px;
  overflow: hidden;
}

.period-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.period-dates {
  font-size: 14px;
  color: var(--color-text-1);
}

.period-duration {
  display: flex;
  justify-content: flex-start;
}

:deep(.arco-table-th) {
  background-color: var(--color-fill-2);
  font-weight: 600;
}

:deep(.arco-table-td) {
  padding: 12px 16px;
}
</style>
