<template>
  <div class="account-info">
    <div class="info-header">
      <h4>账户信息</h4>
    </div>
    <div class="info-content">
      <a-row :gutter="24">
        <a-col :span="12">
          <div class="info-item">
            <span class="label">付款条件：</span>
            <span class="value">{{ supplierData.payment_terms || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="info-item">
            <span class="label">发票类型：</span>
            <span class="value">{{ supplierData.invoice_type || '-' }}</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <div class="info-item">
            <span class="label">税率：</span>
            <span class="value">{{ supplierData.tax_rate ? `${supplierData.tax_rate}%` : '-' }}</span>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="info-item">
            <span class="label">开户名：</span>
            <span class="value">{{ supplierData.account_name || '-' }}</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <div class="info-item">
            <span class="label">结算方式：</span>
            <span class="value">{{ supplierData.settlement_method || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="info-item">
            <span class="label">开户账号：</span>
            <span class="value">{{ supplierData.account_number || '-' }}</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <div class="info-item">
            <span class="label">开户行：</span>
            <span class="value">{{ supplierData.bank_name || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="info-item">
            <span class="label">注册资金(万元)：</span>
            <span class="value">{{ supplierData.registered_capital ? `${supplierData.registered_capital}万元` : '-' }}</span>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  supplierData: {
    type: Object,
    default: () => ({})
  }
});
</script>

<style scoped>
.account-info {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
}

.info-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.info-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  min-height: 32px;
}

.label {
  font-weight: 500;
  color: var(--color-text-2);
  min-width: 120px;
  flex-shrink: 0;
}

.value {
  color: var(--color-text-1);
  flex: 1;
}
</style>
