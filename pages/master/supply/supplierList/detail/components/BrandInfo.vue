<template>
  <div class="brand-info">
    <div class="info-header">
      <h4>品牌信息</h4>
      <span class="brand-count">共 {{ brands.length }} 个品牌</span>
    </div>
    <div class="brand-content">
      <div v-if="brands.length === 0" class="empty-state">
        <a-empty description="暂无品牌信息" />
      </div>
      <div v-else class="brand-grid">
        <div 
          v-for="brand in brands" 
          :key="brand.id || brand.name" 
          class="brand-card"
        >
          <div class="brand-card-header">
            <div class="brand-name">{{ brand.name }}</div>
            <div class="brand-status">
              <a-tag :color="brand.authorized ? 'green' : 'orange'">
                {{ brand.authorized ? '已授权' : '未授权' }}
              </a-tag>
            </div>
          </div>
          <div class="brand-card-body">
            <div class="brand-detail">
              <div class="detail-item" v-if="brand.authorization_date">
                <span class="detail-label">授权时间：</span>
                <span class="detail-value">{{ brand.authorization_date }}</span>
              </div>
              <div class="detail-item" v-if="brand.expiry_date">
                <span class="detail-label">到期时间：</span>
                <span class="detail-value">{{ brand.expiry_date }}</span>
              </div>
              <div class="detail-item" v-if="brand.authorization_scope">
                <span class="detail-label">授权范围：</span>
                <span class="detail-value">{{ brand.authorization_scope }}</span>
              </div>
              <div class="detail-item" v-if="brand.remark">
                <span class="detail-label">备注：</span>
                <span class="detail-value">{{ brand.remark }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  supplierData: {
    type: Object,
    default: () => ({})
  }
});

// 计算品牌列表
const brands = computed(() => {
  return props.supplierData.brands || [];
});
</script>

<style scoped>
.brand-info {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.info-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.brand-count {
  font-size: 14px;
  color: var(--color-text-3);
}

.brand-content {
  min-height: 200px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.brand-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.brand-card {
  background-color: var(--color-bg-1);
  border: 1px solid var(--color-border-2);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
}

.brand-card:hover {
  border-color: var(--color-primary-light-4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.brand-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.brand-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.brand-status {
  flex-shrink: 0;
}

.brand-card-body {
  margin-top: 12px;
}

.brand-detail {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-label {
  font-size: 14px;
  color: var(--color-text-3);
  min-width: 80px;
  flex-shrink: 0;
}

.detail-value {
  font-size: 14px;
  color: var(--color-text-2);
  flex: 1;
  word-break: break-all;
}
</style>
