<template>
  <div class="submitter-info">
    <div class="info-header">
      <h4>提交人信息</h4>
    </div>
    <div class="info-content">
      <a-row :gutter="24">
        <a-col :span="12">
          <div class="info-item">
            <span class="label">部门：</span>
            <span class="value">{{ supplierData.department || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="info-item">
            <span class="label">提交人：</span>
            <span class="value">{{ supplierData.submitter || '-' }}</span>
          </div>
        </a-col>
      </a-row>

      <a-row :gutter="24">
        <a-col :span="12">
          <div class="info-item">
            <span class="label">复核人：</span>
            <span class="value">{{ supplierData.reviewer || '-' }}</span>
          </div>
        </a-col>
        <a-col :span="12">
          <div class="info-item">
            <span class="label">提交时间：</span>
            <span class="value">{{ supplierData.submit_date || '-' }}</span>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  supplierData: {
    type: Object,
    default: () => ({})
  }
});
</script>

<style scoped>
.submitter-info {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
}

.info-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.info-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  min-height: 32px;
}

.label {
  font-weight: 500;
  color: var(--color-text-2);
  min-width: 100px;
  flex-shrink: 0;
}

.value {
  color: var(--color-text-1);
  flex: 1;
}
</style>
