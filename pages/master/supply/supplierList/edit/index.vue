<template>
  <a-drawer
    v-model:visible="visible"
    title="编辑供应商信息"
    :width="1200"
    :footer="false"
    unmount-on-close
    :mask-closable="false"
    :esc-to-close="true"
    @close="handleClose"
    placement="right"
  >
    <div class="supplier-edit">
      <!-- Tab切换编辑区域 -->
      <div class="tab-edit-section">
        <a-tabs v-model:active-key="activeTab" type="line" size="large">
          <a-tab-pane key="basic" title="基础信息">
            <BasicInfoEdit
              ref="basicInfoRef"
              v-model:form-data="formData.basicInfo"
              :errors="errors.basicInfo"
              @save="handleSaveBasicInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="account" title="账户信息">
            <AccountInfoEdit
              ref="accountInfoRef"
              v-model:form-data="formData.accountInfo"
              :errors="errors.accountInfo"
              @save="handleSaveAccountInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="brand" title="品牌信息">
            <BrandInfoEdit
              ref="brandInfoRef"
              v-model:form-data="formData.brandInfo"
              :errors="errors.brandInfo"
              @save="handleSaveBrandInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="contact" title="联系人信息">
            <ContactInfoEdit
              ref="contactInfoRef"
              v-model:form-data="formData.contactInfo"
              :errors="errors.contactInfo"
              @save="handleSaveContactInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="agreement" title="协议信息">
            <AgreementInfoEdit
              ref="agreementInfoRef"
              v-model:form-data="formData.agreementInfo"
              :errors="errors.agreementInfo"
              @save="handleSaveAgreementInfo"
            />
          </a-tab-pane>
          <a-tab-pane key="attachment" title="附件信息">
            <AttachmentInfoEdit
              ref="attachmentInfoRef"
              v-model:form-data="formData.attachmentInfo"
              :errors="errors.attachmentInfo"
              @save="handleSaveAttachmentInfo"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue';
import { Message } from '@arco-design/web-vue';
import BasicInfoEdit from './components/BasicInfoEdit.vue';
import AccountInfoEdit from './components/AccountInfoEdit.vue';
import BrandInfoEdit from './components/BrandInfoEdit.vue';
import ContactInfoEdit from './components/ContactInfoEdit.vue';
import AgreementInfoEdit from './components/AgreementInfoEdit.vue';
import AttachmentInfoEdit from './components/AttachmentInfoEdit.vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  supplierData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'save-success']);

const visible = ref(props.visible);
const activeTab = ref('basic'); // 默认显示基础信息
const saving = ref(false);

// 表单引用
const basicInfoRef = ref();
const accountInfoRef = ref();
const brandInfoRef = ref();
const contactInfoRef = ref();
const agreementInfoRef = ref();
const attachmentInfoRef = ref();

// 表单数据
const formData = reactive({
  basicInfo: {},
  accountInfo: {},
  brandInfo: [],
  contactInfo: [],
  agreementInfo: [],
  attachmentInfo: {}
});

// 错误信息
const errors = reactive({
  basicInfo: {},
  accountInfo: {},
  brandInfo: {},
  contactInfo: {},
  agreementInfo: {},
  attachmentInfo: {}
});

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    initFormData();
  }
});

watch(visible, (newVal) => {
  emit('update:visible', newVal);
  if (newVal) {
    activeTab.value = 'basic';
  }
});

// 初始化表单数据
const initFormData = () => {
  const data = props.supplierData;
  
  // 基础信息
  formData.basicInfo = {
    supplier_name: data.supplier_name || '',
    supplier_code: data.supplier_code || '',
    company_nature: data.company_nature || '',
    main_products: data.main_products || [],
    cooperation_type: data.cooperation_type || '',
    cooperation_status: data.cooperation_status || '',
    cooperation_agreement: data.cooperation_agreement || '',
    supplier_group: data.supplier_group || '',
    address: data.address || '',
    detailed_address: data.detailed_address || '',
    production_address: data.production_address || '',
    register_time: data.register_time || '',
    credit_code: data.credit_code || '',
    supplier_relation: data.supplier_relation || ''
  };

  // 账户信息
  formData.accountInfo = {
    payment_terms: data.payment_terms || '',
    invoice_type: data.invoice_type || '',
    tax_rate: data.tax_rate || '',
    account_name: data.account_name || '',
    settlement_method: data.settlement_method || '',
    account_number: data.account_number || '',
    bank_name: data.bank_name || '',
    registered_capital: data.registered_capital || ''
  };

  // 品牌信息
  formData.brandInfo = data.brands ? [...data.brands] : [];

  // 联系人信息
  formData.contactInfo = data.contacts ? [...data.contacts] : [];

  // 协议信息
  formData.agreementInfo = data.agreements ? [...data.agreements] : [];

  // 附件信息
  formData.attachmentInfo = data.attachments ? { ...data.attachments } : {};
};

// 基础信息保存
const handleSaveBasicInfo = async () => {
  try {
    if (basicInfoRef.value?.validate) {
      const isValid = await basicInfoRef.value.validate();
      if (!isValid) {
        Message.error('请检查基础信息填写是否正确');
        return;
      }
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    Message.success('基础信息保存成功');

    // 触发保存成功事件
    const saveData = {
      id: props.supplierData.id,
      ...formData.basicInfo
    };
    emit('save-success', saveData);
  } catch (error) {
    console.error('基础信息保存失败:', error);
    Message.error('基础信息保存失败，请重试');
  }
};

// 账户信息保存
const handleSaveAccountInfo = async () => {
  try {
    if (accountInfoRef.value?.validate) {
      const isValid = await accountInfoRef.value.validate();
      if (!isValid) {
        Message.error('请检查账户信息填写是否正确');
        return;
      }
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    Message.success('账户信息保存成功');

    const saveData = {
      id: props.supplierData.id,
      ...formData.accountInfo
    };
    emit('save-success', saveData);
  } catch (error) {
    console.error('账户信息保存失败:', error);
    Message.error('账户信息保存失败，请重试');
  }
};

// 品牌信息保存
const handleSaveBrandInfo = async () => {
  try {
    if (brandInfoRef.value?.validate) {
      const isValid = await brandInfoRef.value.validate();
      if (!isValid) {
        Message.error('请检查品牌信息填写是否正确');
        return;
      }
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    Message.success('品牌信息保存成功');

    const saveData = {
      id: props.supplierData.id,
      brands: formData.brandInfo
    };
    emit('save-success', saveData);
  } catch (error) {
    console.error('品牌信息保存失败:', error);
    Message.error('品牌信息保存失败，请重试');
  }
};

// 联系人信息保存
const handleSaveContactInfo = async () => {
  try {
    if (contactInfoRef.value?.validate) {
      const isValid = await contactInfoRef.value.validate();
      if (!isValid) {
        Message.error('请检查联系人信息填写是否正确');
        return;
      }
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    Message.success('联系人信息保存成功');

    const saveData = {
      id: props.supplierData.id,
      contacts: formData.contactInfo
    };
    emit('save-success', saveData);
  } catch (error) {
    console.error('联系人信息保存失败:', error);
    Message.error('联系人信息保存失败，请重试');
  }
};

// 协议信息保存
const handleSaveAgreementInfo = async () => {
  try {
    if (agreementInfoRef.value?.validate) {
      const isValid = await agreementInfoRef.value.validate();
      if (!isValid) {
        Message.error('请检查协议信息填写是否正确');
        return;
      }
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    Message.success('协议信息保存成功');

    const saveData = {
      id: props.supplierData.id,
      agreements: formData.agreementInfo
    };
    emit('save-success', saveData);
  } catch (error) {
    console.error('协议信息保存失败:', error);
    Message.error('协议信息保存失败，请重试');
  }
};

// 附件信息保存
const handleSaveAttachmentInfo = async () => {
  try {
    if (attachmentInfoRef.value?.validate) {
      const isValid = await attachmentInfoRef.value.validate();
      if (!isValid) {
        Message.error('请检查附件信息填写是否正确');
        return;
      }
    }

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    Message.success('附件信息保存成功');

    const saveData = {
      id: props.supplierData.id,
      attachments: formData.attachmentInfo
    };
    emit('save-success', saveData);
  } catch (error) {
    console.error('附件信息保存失败:', error);
    Message.error('附件信息保存失败，请重试');
  }
};

// 关闭处理
const handleClose = () => {
  visible.value = false;
};
</script>

<style scoped>
.supplier-edit {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.tab-edit-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

:deep(.arco-drawer-body) {
  padding: 0;
  height: 100%;
  overflow: hidden;
}

:deep(.arco-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.arco-tabs-content) {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  height: calc(100vh - 120px);
}

:deep(.arco-tabs-tab-list) {
  flex-shrink: 0;
  padding: 0 20px;
  background-color: var(--color-bg-1);
  border-bottom: 1px solid var(--color-border-2);
}

:deep(.arco-tabs-tab) {
  font-size: 16px;
  font-weight: 500;
  padding: 16px 20px;
}

:deep(.arco-tabs-content-item) {
  height: 100%;
  overflow-y: auto;
}
</style>
