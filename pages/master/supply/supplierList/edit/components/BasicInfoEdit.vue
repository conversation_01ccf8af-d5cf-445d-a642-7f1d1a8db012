<template>
  <div class="basic-info-edit">
    <div class="edit-header">
      <h3>基础信息</h3>
    </div>
    <div class="edit-content">
      <a-form 
        ref="formRef"
        :model="formData" 
        :rules="rules"
        layout="vertical"
        :label-col-props="{ span: 24 }"
        :wrapper-col-props="{ span: 24 }"
      >
        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="供应商名称" field="supplier_name">
              <a-input 
                v-model="formData.supplier_name" 
                placeholder="请输入供应商名称"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="供应商代码" field="supplier_code">
              <a-input 
                v-model="formData.supplier_code" 
                placeholder="请输入供应商代码"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="公司性质" field="company_nature">
              <a-select 
                v-model="formData.company_nature" 
                placeholder="请选择公司性质"
                allow-clear
              >
                <a-option value="有限责任公司">有限责任公司</a-option>
                <a-option value="股份有限公司">股份有限公司</a-option>
                <a-option value="外商独资企业">外商独资企业</a-option>
                <a-option value="国有企业">国有企业</a-option>
                <a-option value="合伙企业">合伙企业</a-option>
                <a-option value="个体工商户">个体工商户</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="主营产品" field="main_products">
              <a-select 
                v-model="formData.main_products" 
                placeholder="请选择主营产品"
                multiple
                allow-clear
                allow-create
              >
                <a-option value="电子产品">电子产品</a-option>
                <a-option value="机械设备">机械设备</a-option>
                <a-option value="化工原料">化工原料</a-option>
                <a-option value="建筑材料">建筑材料</a-option>
                <a-option value="纺织服装">纺织服装</a-option>
                <a-option value="食品饮料">食品饮料</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="合作方式" field="cooperation_type">
              <a-select 
                v-model="formData.cooperation_type" 
                placeholder="请选择合作方式"
                allow-clear
              >
                <a-option value="长期合作">长期合作</a-option>
                <a-option value="项目合作">项目合作</a-option>
                <a-option value="临时采购">临时采购</a-option>
                <a-option value="战略合作">战略合作</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="合作状态" field="cooperation_status">
              <a-select 
                v-model="formData.cooperation_status" 
                placeholder="请选择合作状态"
                allow-clear
              >
                <a-option value="正常合作">正常合作</a-option>
                <a-option value="暂停合作">暂停合作</a-option>
                <a-option value="待审核">待审核</a-option>
                <a-option value="已终止">已终止</a-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="合作协议" field="cooperation_agreement">
              <a-input 
                v-model="formData.cooperation_agreement" 
                placeholder="请输入合作协议名称"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="供应商分组" field="supplier_group">
              <a-select 
                v-model="formData.supplier_group" 
                placeholder="请选择供应商分组"
                allow-clear
              >
                <a-option value="A类供应商">A类供应商</a-option>
                <a-option value="B类供应商">B类供应商</a-option>
                <a-option value="C类供应商">C类供应商</a-option>
                <a-option value="战略供应商">战略供应商</a-option>
                <a-option value="临时供应商">临时供应商</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="注册时间" field="register_time">
              <a-date-picker 
                v-model="formData.register_time" 
                placeholder="请选择注册时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="8">
            <a-form-item label="统一信用代码" field="credit_code">
              <a-input 
                v-model="formData.credit_code" 
                placeholder="请输入统一信用代码"
                allow-clear
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="供应商关联" field="supplier_relation">
              <a-select 
                v-model="formData.supplier_relation" 
                placeholder="请选择供应商关联"
                allow-clear
              >
                <a-option value="独立供应商">独立供应商</a-option>
                <a-option value="关联供应商">关联供应商</a-option>
                <a-option value="子公司">子公司</a-option>
                <a-option value="分公司">分公司</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="通讯地址" field="address">
              <a-input 
                v-model="formData.address" 
                placeholder="请输入通讯地址"
                allow-clear
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="详细地址" field="detailed_address">
              <a-textarea 
                v-model="formData.detailed_address" 
                placeholder="请输入详细地址"
                :auto-size="{ minRows: 2, maxRows: 4 }"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="生产地址" field="production_address">
              <a-textarea 
                v-model="formData.production_address" 
                placeholder="请输入生产地址"
                :auto-size="{ minRows: 2, maxRows: 4 }"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 保存按钮 -->
      <div class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存基础信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  },
  errors: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:formData', 'save']);

const formRef = ref();
const saving = ref(false);

// 表单数据
const formData = reactive({ ...props.formData });

// 表单验证规则
const rules = {
  supplier_name: [
    { required: true, message: '请输入供应商名称' },
    { minLength: 2, message: '供应商名称至少2个字符' }
  ],
  supplier_code: [
    { required: true, message: '请输入供应商代码' },
    { pattern: /^[A-Z0-9]{3,20}$/, message: '供应商代码格式不正确' }
  ],
  company_nature: [
    { required: true, message: '请选择公司性质' }
  ],
  main_products: [
    { required: true, message: '请选择主营产品' }
  ],
  cooperation_type: [
    { required: true, message: '请选择合作方式' }
  ],
  cooperation_status: [
    { required: true, message: '请选择合作状态' }
  ],
  credit_code: [
    { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '统一信用代码格式不正确' }
  ]
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  Object.assign(formData, newVal);
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    const result = await formRef.value.validate();
    return result;
  } catch (error) {
    console.error('基础信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.basic-info-edit {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.edit-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.edit-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-1);
}

.edit-content {
  margin-top: 16px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
