<template>
  <div class="contact-info-edit">
    <div class="edit-header">
      <h4>联系人信息</h4>
      <a-button type="primary" @click="addContact">
        <template #icon><icon-plus /></template>
        添加联系人
      </a-button>
    </div>
    <div class="edit-content">
      <div v-if="formData.length === 0" class="empty-state">
        <a-empty description="暂无联系人信息，点击上方按钮添加" />
      </div>
      <div v-else class="contact-list">
        <div 
          v-for="(contact, index) in formData" 
          :key="contact.id || index"
          class="contact-item"
        >
          <a-card class="contact-card" :bordered="true">
            <template #title>
              <div class="contact-title">
                <span>联系人 {{ contact.name || index + 1 }}</span>
                <div class="title-actions">
                  <a-tag v-if="contact.is_primary" color="orange" size="small">
                    主要联系人
                  </a-tag>
                  <a-button 
                    type="text" 
                    status="danger" 
                    size="small"
                    @click="removeContact(index)"
                  >
                    <template #icon><icon-delete /></template>
                    删除
                  </a-button>
                </div>
              </div>
            </template>
            
            <a-form 
              :model="contact" 
              layout="vertical"
              :label-col-props="{ span: 24 }"
              :wrapper-col-props="{ span: 24 }"
            >
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="联系人姓名" :field="`${index}.name`" :rules="contactRules.name">
                    <a-input 
                      v-model="contact.name" 
                      placeholder="请输入联系人姓名"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="联系电话" :field="`${index}.phone`" :rules="contactRules.phone">
                    <a-input 
                      v-model="contact.phone" 
                      placeholder="请输入联系电话"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="职位" :field="`${index}.position`">
                    <a-input 
                      v-model="contact.position" 
                      placeholder="请输入职位"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="邮箱" :field="`${index}.email`" :rules="contactRules.email">
                    <a-input 
                      v-model="contact.email" 
                      placeholder="请输入邮箱"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="部门" :field="`${index}.department`">
                    <a-input 
                      v-model="contact.department" 
                      placeholder="请输入部门"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="8">
                  <a-form-item label="是否主要联系人" :field="`${index}.is_primary`">
                    <a-switch 
                      v-model="contact.is_primary"
                      checked-text="是"
                      unchecked-text="否"
                      @change="handlePrimaryChange(index)"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="备注" :field="`${index}.remark`">
                    <a-textarea 
                      v-model="contact.remark" 
                      placeholder="请输入备注信息"
                      :auto-size="{ minRows: 2, maxRows: 4 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-card>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存联系人信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  formData: {
    type: Array,
    default: () => []
  },
  errors: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:formData', 'save']);

const saving = ref(false);

// 表单数据
const formData = reactive([...props.formData]);

// 联系人验证规则
const contactRules = {
  name: [
    { required: true, message: '请输入联系人姓名' },
    { minLength: 2, message: '联系人姓名至少2个字符' }
  ],
  phone: [
    { required: true, message: '请输入联系电话' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址' }
  ]
};

// 添加联系人
const addContact = () => {
  const newContact = {
    id: Date.now(), // 临时ID
    name: '',
    phone: '',
    position: '',
    email: '',
    department: '',
    is_primary: false,
    remark: ''
  };
  formData.push(newContact);
};

// 删除联系人
const removeContact = (index) => {
  if (formData.length <= 1) {
    Message.warning('至少保留一个联系人信息');
    return;
  }
  formData.splice(index, 1);
};

// 处理主要联系人变更
const handlePrimaryChange = (index) => {
  if (formData[index].is_primary) {
    // 如果设置为主要联系人，取消其他联系人的主要状态
    formData.forEach((contact, i) => {
      if (i !== index) {
        contact.is_primary = false;
      }
    });
  }
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  formData.splice(0, formData.length, ...newVal);
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    // 验证每个联系人的必填字段
    for (let i = 0; i < formData.length; i++) {
      const contact = formData[i];
      if (!contact.name) {
        Message.error(`第${i + 1}个联系人的姓名不能为空`);
        return false;
      }
      if (!contact.phone) {
        Message.error(`第${i + 1}个联系人的电话不能为空`);
        return false;
      }
      if (contact.phone && !/^1[3-9]\d{9}$/.test(contact.phone)) {
        Message.error(`第${i + 1}个联系人的电话格式不正确`);
        return false;
      }
      if (contact.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
        Message.error(`第${i + 1}个联系人的邮箱格式不正确`);
        return false;
      }
    }

    // 检查是否有主要联系人
    const hasPrimary = formData.some(contact => contact.is_primary);
    if (!hasPrimary && formData.length > 0) {
      Message.warning('建议设置一个主要联系人');
    }

    return true;
  } catch (error) {
    console.error('联系人信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.contact-info-edit {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.edit-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.edit-content {
  flex: 1;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-item {
  width: 100%;
}

.contact-card {
  background-color: var(--color-bg-1);
  border: 1px solid var(--color-border-2);
}

.contact-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.title-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 12px;
}

:deep(.arco-card-header) {
  padding: 12px 16px;
  background-color: var(--color-fill-1);
}

:deep(.arco-card-body) {
  padding: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
