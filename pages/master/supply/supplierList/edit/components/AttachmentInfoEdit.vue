<template>
  <div class="attachment-info-edit">
    <div class="edit-header">
      <h4>附件信息</h4>
    </div>
    <div class="edit-content">
      <!-- 营业执照 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>营业执照</span>
          <span class="file-count">({{ businessLicenseCount }}/1)</span>
        </div>
        <a-upload
          v-model:file-list="formData.business_license"
          :limit="1"
          :auto-upload="false"
          accept="image/*"
          list-type="picture-card"
          @change="handleBusinessLicenseChange"
        >
          <template #upload-button>
            <div class="upload-placeholder">
              <icon-plus />
              <div>上传营业执照</div>
            </div>
          </template>
        </a-upload>
      </div>

      <!-- 开户证明 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>开户证明</span>
          <span class="file-count">({{ bankProofCount }}/1)</span>
        </div>
        <a-upload
          v-model:file-list="formData.bank_proof"
          :limit="1"
          :auto-upload="false"
          accept="image/*"
          list-type="picture-card"
          @change="handleBankProofChange"
        >
          <template #upload-button>
            <div class="upload-placeholder">
              <icon-plus />
              <div>上传开户证明</div>
            </div>
          </template>
        </a-upload>
      </div>

      <!-- 法人身份证 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>法人身份证</span>
          <span class="file-count">({{ idCardCount }}/2)</span>
        </div>
        <div class="id-card-upload">
          <div class="id-card-item">
            <div class="id-card-label">正面</div>
            <a-upload
              v-model:file-list="formData.id_card_front"
              :limit="1"
              :auto-upload="false"
              accept="image/*"
              list-type="picture-card"
              @change="handleIdCardFrontChange"
            >
              <template #upload-button>
                <div class="upload-placeholder">
                  <icon-plus />
                  <div>上传正面</div>
                </div>
              </template>
            </a-upload>
          </div>
          <div class="id-card-item">
            <div class="id-card-label">反面</div>
            <a-upload
              v-model:file-list="formData.id_card_back"
              :limit="1"
              :auto-upload="false"
              accept="image/*"
              list-type="picture-card"
              @change="handleIdCardBackChange"
            >
              <template #upload-button>
                <div class="upload-placeholder">
                  <icon-plus />
                  <div>上传反面</div>
                </div>
              </template>
            </a-upload>
          </div>
        </div>
      </div>

      <!-- 其他附件 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>其他附件</span>
          <span class="file-count">({{ otherAttachmentsCount }}/5)</span>
        </div>
        <a-upload
          v-model:file-list="formData.others"
          :limit="5"
          :auto-upload="false"
          accept="image/*"
          list-type="picture-card"
          @change="handleOthersChange"
        >
          <template #upload-button>
            <div class="upload-placeholder">
              <icon-plus />
              <div>上传其他附件</div>
            </div>
          </template>
        </a-upload>
      </div>

      <!-- 备注信息 -->
      <div class="attachment-section">
        <div class="section-title">
          <span>备注信息</span>
        </div>
        <a-form-item>
          <a-textarea
            v-model="formData.remark"
            placeholder="请输入备注信息"
            :auto-size="{ minRows: 4, maxRows: 8 }"
          />
        </a-form-item>
      </div>

      <!-- 保存按钮 -->
      <div class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存附件信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({})
  },
  errors: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:formData', 'save']);

const saving = ref(false);

// 表单数据
const formData = reactive({
  business_license: [],
  bank_proof: [],
  id_card_front: [],
  id_card_back: [],
  others: [],
  remark: '',
  ...props.formData
});

// 计算各类附件数量
const businessLicenseCount = computed(() => {
  return formData.business_license?.length || 0;
});

const bankProofCount = computed(() => {
  return formData.bank_proof?.length || 0;
});

const idCardCount = computed(() => {
  return (formData.id_card_front?.length || 0) + (formData.id_card_back?.length || 0);
});

const otherAttachmentsCount = computed(() => {
  return formData.others?.length || 0;
});

// 文件上传处理函数
const handleBusinessLicenseChange = (fileList) => {
  formData.business_license = fileList;
};

const handleBankProofChange = (fileList) => {
  formData.bank_proof = fileList;
};

const handleIdCardFrontChange = (fileList) => {
  formData.id_card_front = fileList;
};

const handleIdCardBackChange = (fileList) => {
  formData.id_card_back = fileList;
};

const handleOthersChange = (fileList) => {
  formData.others = fileList;
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  Object.assign(formData, newVal);
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    // 这里可以添加附件相关的验证逻辑
    // 比如检查必要的附件是否已上传等
    return true;
  } catch (error) {
    console.error('附件信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.attachment-info-edit {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.edit-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.edit-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.edit-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.attachment-section {
  background-color: var(--color-bg-1);
  border-radius: 6px;
  padding: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-1);
}

.file-count {
  font-size: 12px;
  color: var(--color-text-3);
  font-weight: normal;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--color-text-3);
  font-size: 14px;
}

.id-card-upload {
  display: flex;
  gap: 16px;
}

.id-card-item {
  flex: 1;
}

.id-card-label {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-2);
}

:deep(.arco-upload-list-picture-card .arco-upload-list-item) {
  width: 120px;
  height: 120px;
}

:deep(.arco-upload-picture-card) {
  width: 120px;
  height: 120px;
}

:deep(.arco-form-item) {
  margin-bottom: 0;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
