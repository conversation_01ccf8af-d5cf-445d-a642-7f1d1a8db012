<template>
  <div class="brand-info-edit">
    <div class="edit-header">
      <h4>品牌信息</h4>
      <a-button type="primary" @click="addBrand">
        <template #icon><icon-plus /></template>
        添加品牌
      </a-button>
    </div>
    <div class="edit-content">
      <div v-if="formData.length === 0" class="empty-state">
        <a-empty description="暂无品牌信息，点击上方按钮添加" />
      </div>
      <div v-else class="brand-list">
        <div 
          v-for="(brand, index) in formData" 
          :key="brand.id || index"
          class="brand-item"
        >
          <a-card class="brand-card" :bordered="true">
            <template #title>
              <div class="brand-title">
                <span>品牌： {{ brand.name || index + 1 }}</span>
                <a-button 
                  type="text" 
                  status="danger" 
                  size="small"
                  @click="removeBrand(index)"
                >
                  <template #icon><icon-delete /></template>
                  删除
                </a-button>
              </div>
            </template>
            
            <a-form 
              :model="brand" 
              layout="vertical"
              :label-col-props="{ span: 24 }"
              :wrapper-col-props="{ span: 24 }"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="品牌名称" :field="`${index}.name`" :rules="brandRules.name">
                    <a-input 
                      v-model="brand.name" 
                      placeholder="请输入品牌名称"
                      allow-clear
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="是否已授权" :field="`${index}.authorized`">
                    <a-switch 
                      v-model="brand.authorized"
                      checked-text="已授权"
                      unchecked-text="未授权"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16" v-if="brand.authorized">
                <a-col :span="12">
                  <a-form-item label="授权时间" :field="`${index}.authorization_date`">
                    <a-date-picker 
                      v-model="brand.authorization_date" 
                      placeholder="请选择授权时间"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item label="到期时间" :field="`${index}.expiry_date`">
                    <a-date-picker 
                      v-model="brand.expiry_date" 
                      placeholder="请选择到期时间"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16" v-if="brand.authorized">
                <a-col :span="24">
                  <a-form-item label="授权范围" :field="`${index}.authorization_scope`">
                    <a-textarea 
                      v-model="brand.authorization_scope" 
                      placeholder="请输入授权范围"
                      :auto-size="{ minRows: 2, maxRows: 4 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="24">
                  <a-form-item label="备注" :field="`${index}.remark`">
                    <a-textarea 
                      v-model="brand.remark" 
                      placeholder="请输入备注信息"
                      :auto-size="{ minRows: 2, maxRows: 4 }"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>
          </a-card>
        </div>
      </div>

      <!-- 保存按钮 -->
      <div class="save-section">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><icon-save /></template>
          保存品牌信息
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  formData: {
    type: Array,
    default: () => []
  },
  errors: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:formData', 'save']);

const saving = ref(false);

// 表单数据
const formData = reactive([...props.formData]);

// 品牌验证规则
const brandRules = {
  name: [
    { required: true, message: '请输入品牌名称' },
    { minLength: 1, message: '品牌名称不能为空' }
  ]
};

// 添加品牌
const addBrand = () => {
  const newBrand = {
    id: Date.now(), // 临时ID
    name: '',
    authorized: false,
    authorization_date: '',
    expiry_date: '',
    authorization_scope: '',
    remark: ''
  };
  formData.push(newBrand);
};

// 删除品牌
const removeBrand = (index) => {
  if (formData.length <= 1) {
    Message.warning('至少保留一个品牌信息');
    return;
  }
  formData.splice(index, 1);
};

// 监听表单数据变化
watch(formData, (newVal) => {
  emit('update:formData', newVal);
}, { deep: true });

// 监听props变化
watch(() => props.formData, (newVal) => {
  formData.splice(0, formData.length, ...newVal);
}, { deep: true });

// 验证表单
const validate = async () => {
  try {
    // 验证每个品牌的必填字段
    for (let i = 0; i < formData.length; i++) {
      const brand = formData[i];
      if (!brand.name) {
        Message.error(`第${i + 1}个品牌的名称不能为空`);
        return false;
      }
      if (brand.authorized && !brand.authorization_date) {
        Message.error(`第${i + 1}个品牌已授权但未填写授权时间`);
        return false;
      }
    }
    return true;
  } catch (error) {
    console.error('品牌信息验证失败:', error);
    return false;
  }
};

// 保存处理
const handleSave = async () => {
  saving.value = true;
  try {
    emit('save');
  } finally {
    saving.value = false;
  }
};

// 暴露验证方法
defineExpose({
  validate
});
</script>

<style scoped>
.brand-info-edit {
  background-color: var(--color-bg-2);
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-border-2);
}

.edit-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.edit-content {
  flex: 1;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.brand-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.brand-item {
  width: 100%;
}

.brand-card {
  background-color: var(--color-bg-1);
  border: 1px solid var(--color-border-2);
}

.brand-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: var(--color-text-1);
}

:deep(.arco-form-item) {
  margin-bottom: 12px;
}

:deep(.arco-card-header) {
  padding: 12px 16px;
  background-color: var(--color-fill-1);
}

:deep(.arco-card-body) {
  padding: 16px;
}

.save-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--color-border-2);
  display: flex;
  justify-content: flex-end;
}
</style>
