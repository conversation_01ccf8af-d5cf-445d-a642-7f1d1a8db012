<template>
  <div class="order-detail-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-breadcrumb>
        <a-breadcrumb-item>
          <router-link to="/master/supply/purchaseOrder">采购订单管理</router-link>
        </a-breadcrumb-item>
        <a-breadcrumb-item>订单详情</a-breadcrumb-item>
      </a-breadcrumb>
      
      <div class="header-actions">
        <a-space>
          <a-button @click="goBack">
            <template #icon><icon-arrow-left /></template>
            返回列表
          </a-button>
          <a-button type="primary" @click="handleEdit">
            <template #icon><icon-edit /></template>
            编辑订单
          </a-button>
          <a-dropdown @select="handleAction">
            <a-button>
              更多操作
              <template #icon><icon-down /></template>
            </a-button>
            <template #content>
              <a-doption value="template">
                <template #icon><icon-file-text /></template>
                下单模版
              </a-doption>
              <a-doption value="purchaser">
                <template #icon><icon-user /></template>
                更换采购员
              </a-doption>
              <a-doption value="remark">
                <template #icon><icon-edit /></template>
                采购备注
              </a-doption>
              <a-doption value="split">
                <template #icon><icon-branch /></template>
                拆分商品
              </a-doption>
              <a-doption value="expense">
                <template #icon><icon-file /></template>
                费用单
              </a-doption>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="page-content" v-if="orderData">
      <a-row :gutter="16">
        <!-- 左侧主要信息 -->
        <a-col :span="16">
          <!-- 基本信息 -->
          <BasicInfo :order-data="orderData" />
          
          <!-- 商品信息 -->
          <ProductInfo :order-data="orderData" />

          <!-- 比价信息 -->
          <PriceComparisonInfo :order-data="orderData" />

          <!-- 成本信息 -->
          <CostInfo :order-data="orderData" />

          <!-- 物流信息 -->
          <LogisticsInfo :order-data="orderData" />
        </a-col>
        
        <!-- 右侧状态和日志 -->
        <a-col :span="8">
          <!-- 状态信息 -->
          <StatusInfo :order-data="orderData" />
          
          <!-- 操作日志 -->
          <OperationLog :order-data="orderData" />
        </a-col>
      </a-row>
    </div>

    <!-- 加载状态 -->
    <div v-else class="loading-container">
      <a-spin size="large" tip="加载订单详情中...">
        <div class="loading-placeholder"></div>
      </a-spin>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'

// 导入组件
import BasicInfo from './components/BasicInfo/index.vue'
import ProductInfo from './components/ProductInfo/index.vue'
import CostInfo from './components/CostInfo/index.vue'
import PriceComparisonInfo from './components/PriceComparisonInfo/index.vue'
import StatusInfo from './components/StatusInfo/index.vue'
import LogisticsInfo from './components/LogisticsInfo/index.vue'
import OperationLog from './components/OperationLog/index.vue'
const route = useRoute()
const router = useRouter()

const orderData = ref(null)
const loading = ref(true)

// 获取订单ID
const orderId = route.params.id

// 模拟订单数据
const mockOrderData = {
  id: 1,
  orderNumber: 'PO202312010001',
  purchaseTime: '2023-12-01 10:30:00',
  orderTime: '2023-12-01 10:00:00',
  orderSource: '商城',
  storeName: '电子产品专营店',
  deliveryAddress: '北京市朝阳区xxx大厦xxx号',
  follower: '李德斌',
  purchaser: '张三',
  orderType: '商城自然单',
  totalAmount: '127600.00',
  freight: '0.00',
  discountAmount: '0.00',
  totalQuantity: 3,
  grossProfitRate: '15.5%',
  costTotal: '108000.00',
  buyerAccount: '李德斌***********-9614',
  businessRemark: '客户要求加急处理，收货地址：北京市朝阳区xxx大厦xxx号，联系人：李德斌，电话：***********',
  supplierRemark: '货期确认：预计3-5个工作日发货，请确保收货地址准确',
  logisticsInfo: '顺丰快递',
  purchaseProgress: '已下单',
  purchaseRemark: '优先发货',
  erpStatus: '已同步',
  logisticsNumber: 'SF1234567890',
  splitOrderStatus: '未拆分',
  auditStatus: '审核通过',
  orderStatus: '待发货',
  cancelStatus: null,
  pendingCancelStatus: null,
  linkStatus: 'linked',
  recipientName: '李德斌',
  contactPhone: '***********',
  orderAddress: '北京市朝阳区xxx大厦xxx号',
  isLoss: 'no',
  // 附件信息
  attachments: [
    {
      name: '采购申请表.pdf',
      size: 1024000,
      url: '/files/purchase-application.pdf',
      type: 'pdf'
    },
    {
      name: '产品规格说明书.docx',
      size: 512000,
      url: '/files/product-specification.docx',
      type: 'docx'
    },
    {
      name: '报价单.xlsx',
      size: 256000,
      url: '/files/quotation.xlsx',
      type: 'xlsx'
    }
  ],
  // 商品列表
  products: [
    {
      id: 1,
      productImage: 'https://via.placeholder.com/60x60',
      productName: '联想/LENOVO P340 工作站 Intel Core i7-10700 2.9GHz 8核 DDR4 16GB 1TB SSD',
      sku: '0000000-500-4100-916',
      productCode: 'G-6478310580',
      specification: '联想/LENOVO P340',
      taxCategory: '电子产品',
      unitPrice: '7800.00',
      quantity: 1,
      unit: '台',
      totalAmount: '7800.00',
      expectedCost: '7800.00',
      purchasePrice: '7500.00',
      contractPrice: '7600.00',
      purchaseCode: 'PC-2023-001',
      actualCost: '6500.00',
      inspectionCost: '6600.00',
      profitRate: '15.5%',
      actualSupplier: '联想官方渠道',
      suggestedSupplier: '建议供应商A',
      remark: '工作站配置'
    },
    {
      id: 2,
      productImage: 'https://via.placeholder.com/60x60',
      productName: '交换设备 华为/Huawei CE6851-48S6Q-EI 48端口千兆以太网交换机',
      sku: 'ce6851-48s6q-ei',
      productCode: 'c-e4c5990e153a',
      specification: 'CE6851-48S6Q',
      taxCategory: '网络设备',
      unitPrice: '32000.00',
      quantity: 2,
      unit: '台',
      totalAmount: '64000.00',
      expectedCost: '32000.00',
      purchasePrice: '31800.00',
      contractPrice: '32000.00',
      purchaseCode: 'PC-2023-002',
      actualCost: '26000.00',
      inspectionCost: '26500.00',
      profitRate: '18.2%',
      actualSupplier: '华为官方渠道',
      suggestedSupplier: '建议供应商B',
      remark: '交换设备'
    },
    {
      id: 3,
      productImage: 'https://via.placeholder.com/60x60',
      productName: '服务器 戴尔/DELL PowerEdge R740 2U机架式服务器',
      sku: 'dell-r740-2u-001',
      productCode: 'DELL-R740-001',
      specification: 'PowerEdge R740 2U',
      taxCategory: '服务器设备',
      unitPrice: '55800.00',
      quantity: 1,
      unit: '台',
      totalAmount: '55800.00',
      expectedCost: '55800.00',
      purchasePrice: '55000.00',
      contractPrice: '55800.00',
      purchaseCode: 'PC-2023-003',
      actualCost: '48000.00',
      inspectionCost: '48500.00',
      profitRate: '13.8%',
      actualSupplier: '戴尔官方渠道',
      suggestedSupplier: '建议供应商C',
      remark: '服务器设备'
    }
  ],
  // 比价信息
  priceComparisons: [
    {
      productName: '联想/LENOVO P340 工作站',
      selectionReason: '价格优势明显，交期较短',
      suppliers: [
        {
          supplierName: '联想官方渠道',
          price: '7800.00',
          deliveryTime: '3-5工作日',
          warranty: '3年',
          remark: '官方渠道，质量保证',
          isRecommended: true,
          isSelected: true,
          isLowestPrice: true
        },
        {
          supplierName: '供应商A',
          price: '8200.00',
          deliveryTime: '5-7工作日',
          warranty: '2年',
          remark: '价格较高',
          isRecommended: false,
          isSelected: false,
          isLowestPrice: false
        },
        {
          supplierName: '供应商B',
          price: '8500.00',
          deliveryTime: '7-10工作日',
          warranty: '1年',
          remark: '交期较长',
          isRecommended: false,
          isSelected: false,
          isLowestPrice: false
        }
      ]
    },
    {
      productName: '华为/Huawei CE6851-48S6Q-EI 交换机',
      selectionReason: '综合性价比最优',
      suppliers: [
        {
          supplierName: '华为官方渠道',
          price: '32000.00',
          deliveryTime: '5-7工作日',
          warranty: '5年',
          remark: '官方渠道，售后完善',
          isRecommended: true,
          isSelected: true,
          isLowestPrice: false
        },
        {
          supplierName: '代理商C',
          price: '31500.00',
          deliveryTime: '3-5工作日',
          warranty: '3年',
          remark: '价格最低但质保期短',
          isRecommended: false,
          isSelected: false,
          isLowestPrice: true
        },
        {
          supplierName: '代理商D',
          price: '33000.00',
          deliveryTime: '7-10工作日',
          warranty: '5年',
          remark: '价格偏高',
          isRecommended: false,
          isSelected: false,
          isLowestPrice: false
        }
      ]
    }
  ]
}

// 加载订单详情
const loadOrderDetail = async () => {
  try {
    loading.value = true
    
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 这里应该调用真实的API
    // const response = await fetch(`/api/v1/master/supply/purchaseOrder/${orderId}`)
    // orderData.value = await response.json()
    
    orderData.value = mockOrderData
  } catch (error) {
    console.error('加载订单详情失败:', error)
    Message.error('加载订单详情失败')
  } finally {
    loading.value = false
  }
}

// 返回列表
const goBack = () => {
  router.push('/master/supply/purchaseOrder')
}

// 编辑订单
const handleEdit = () => {
  Message.info('编辑功能开发中...')
}

// 处理更多操作
const handleAction = (action) => {
  const actionMap = {
    template: '下单模版',
    purchaser: '更换采购员',
    remark: '采购备注',
    split: '拆分商品',
    expense: '费用单'
  }
  
  Message.info(`${actionMap[action]}功能开发中...`)
}

// 页面挂载时加载数据
onMounted(() => {
  if (!orderId) {
    Message.error('订单ID不能为空')
    goBack()
    return
  }
  
  loadOrderDetail()
})
</script>

<style scoped>
.order-detail-page {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-content {
  margin-top: 16px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
}

.loading-placeholder {
  width: 100%;
  height: 200px;
}

:deep(.arco-breadcrumb-item-link) {
  color: #165dff;
  text-decoration: none;
}

:deep(.arco-breadcrumb-item-link:hover) {
  color: #0e42d2;
}

:deep(.arco-card) {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

:deep(.arco-card-header) {
  border-bottom: 1px solid #e5e6eb;
  font-weight: 600;
}
</style>
