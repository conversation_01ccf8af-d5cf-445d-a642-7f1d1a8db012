<template>
  <a-card title="状态信息" class="detail-card">
    <a-row :gutter="16">
      <a-col :span="12">
        <div class="status-section">
          <h4 class="section-title">
            <icon-check-circle class="mr-1" />
            订单状态
          </h4>
          <div class="status-grid">
            <div class="status-item">
              <span class="status-label">订单状态：</span>
              <a-tag :color="getOrderStatusColor(orderData.orderStatus)" size="large">
                {{ orderData.orderStatus }}
              </a-tag>
            </div>
            <div class="status-item">
              <span class="status-label">审核状态：</span>
              <a-tag :color="getAuditStatusColor(orderData.auditStatus)" size="large">
                {{ orderData.auditStatus }}
              </a-tag>
            </div>
            <div class="status-item">
              <span class="status-label">ERP状态：</span>
              <a-tag :color="getErpStatusColor(orderData.erpStatus)" size="large">
                {{ orderData.erpStatus }}
              </a-tag>
            </div>
            <div class="status-item">
              <span class="status-label">拆分状态：</span>
              <a-tag :color="getSplitStatusColor(orderData.splitOrderStatus)" size="large">
                {{ orderData.splitOrderStatus }}
              </a-tag>
            </div>
          </div>
        </div>
      </a-col>
      
      <a-col :span="12">
        <div class="status-section">
          <h4 class="section-title">
            <icon-exclamation-circle class="mr-1" />
            特殊状态
          </h4>
          <div class="status-grid">
            <div class="status-item" v-if="orderData.cancelStatus">
              <span class="status-label">废止状态：</span>
              <a-tag :color="getCancelStatusColor(orderData.cancelStatus)" size="large">
                {{ orderData.cancelStatus }}
              </a-tag>
            </div>
            <div class="status-item" v-if="orderData.pendingCancelStatus">
              <span class="status-label">待废止状态：</span>
              <a-tag :color="getPendingCancelStatusColor(orderData.pendingCancelStatus)" size="large">
                {{ orderData.pendingCancelStatus }}
              </a-tag>
            </div>
            <div class="status-item" v-if="!orderData.cancelStatus && !orderData.pendingCancelStatus">
              <span class="status-label">特殊状态：</span>
              <a-tag color="gray">无</a-tag>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>
    
    <!-- 状态时间线 -->
    <a-divider>状态流转</a-divider>
    <a-timeline>
      <a-timeline-item>
        <template #dot>
          <icon-check-circle style="color: #00b42a" />
        </template>
        <div class="timeline-content">
          <div class="timeline-title">订单创建</div>
          <div class="timeline-time">{{ formatDateTime(orderData.orderTime) }}</div>
          <div class="timeline-desc">订单已创建，等待处理</div>
        </div>
      </a-timeline-item>
      
      <a-timeline-item v-if="orderData.auditStatus === '审核通过'">
        <template #dot>
          <icon-check-circle style="color: #00b42a" />
        </template>
        <div class="timeline-content">
          <div class="timeline-title">审核通过</div>
          <div class="timeline-time">{{ formatDateTime(orderData.purchaseTime) }}</div>
          <div class="timeline-desc">订单审核通过，进入采购流程</div>
        </div>
      </a-timeline-item>
      
      <a-timeline-item v-if="orderData.orderStatus === '已发货' || orderData.orderStatus === '已收货'">
        <template #dot>
          <icon-truck style="color: #165dff" />
        </template>
        <div class="timeline-content">
          <div class="timeline-title">商品发货</div>
          <div class="timeline-time">预计发货时间</div>
          <div class="timeline-desc">商品已发货，等待收货确认</div>
        </div>
      </a-timeline-item>
      
      <a-timeline-item v-if="orderData.orderStatus === '已收货'">
        <template #dot>
          <icon-check-circle style="color: #00b42a" />
        </template>
        <div class="timeline-content">
          <div class="timeline-title">订单完成</div>
          <div class="timeline-time">完成时间</div>
          <div class="timeline-desc">订单已完成，交易结束</div>
        </div>
      </a-timeline-item>
      
      <a-timeline-item v-if="orderData.cancelStatus || orderData.pendingCancelStatus">
        <template #dot>
          <icon-close-circle style="color: #f53f3f" />
        </template>
        <div class="timeline-content">
          <div class="timeline-title">订单异常</div>
          <div class="timeline-time">异常时间</div>
          <div class="timeline-desc">订单出现异常状态，需要处理</div>
        </div>
      </a-timeline-item>
    </a-timeline>
  </a-card>
</template>

<script setup>
const props = defineProps({
  orderData: {
    type: Object,
    required: true
  }
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 状态颜色函数
const getOrderStatusColor = (status) => {
  const colorMap = {
    '待发货': 'orange',
    '发货未完成': 'blue',
    '待收货': 'cyan',
    '已废止': 'gray',
    '已关闭': 'red',
    '废止待确认': 'magenta'
  }
  return colorMap[status] || 'gray'
}

const getAuditStatusColor = (status) => {
  const colorMap = {
    '审核通过': 'green',
    '待审核': 'orange',
    '审核驳回': 'red',
    '废止待确认': 'magenta'
  }
  return colorMap[status] || 'gray'
}

const getErpStatusColor = (status) => {
  const colorMap = {
    '已同步': 'green',
    '待同步': 'orange',
    '同步失败': 'red'
  }
  return colorMap[status] || 'gray'
}

const getSplitStatusColor = (status) => {
  const colorMap = {
    '未拆分': 'blue',
    '已拆分': 'green',
    '拆分中': 'orange'
  }
  return colorMap[status] || 'gray'
}

const getCancelStatusColor = (status) => {
  const colorMap = {
    '待废止': 'orange',
    '废止待确认': 'red',
    '已废止': 'gray'
  }
  return colorMap[status] || 'gray'
}

const getPendingCancelStatusColor = (status) => {
  const colorMap = {
    '待废止': 'orange',
    '废止待确认': 'red',
    '废止审核中': 'blue',
    '废止已驳回': 'magenta'
  }
  return colorMap[status] || 'gray'
}
</script>

<style scoped>
.detail-card {
  margin-bottom: 16px;
  background: #ffffff;
}

.section-title {
  margin-bottom: 16px;
  color: #333;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.status-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.status-label {
  color: #666;
  font-weight: 500;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.timeline-time {
  color: #86909c;
  font-size: 12px;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #666;
  font-size: 13px;
}

.mr-1 {
  margin-right: 4px;
}
</style>
