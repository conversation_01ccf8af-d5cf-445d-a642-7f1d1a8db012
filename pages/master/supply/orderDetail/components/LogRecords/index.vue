<template>
  <a-card class="detail-card">
    <template #title>
      <div class="card-title">
        <icon-file-text class="mr-1" />
        日志记录
      </div>
    </template>

    <div class="log-records-container">
      <!-- 物流单记录 -->
      <div class="record-section">
        <div class="section-header">
          <h4 class="section-title">
            <icon-truck class="mr-1" />
            物流单记录
          </h4>
        </div>
        <div class="record-content">
          <div v-if="orderData.logisticsRecords && orderData.logisticsRecords.length > 0" class="records-list">
            <div v-for="(record, index) in orderData.logisticsRecords" :key="index" class="record-item">
              <div class="record-header">
                <span class="record-title">{{ record.title }}</span>
                <span class="record-time">{{ record.createTime }}</span>
              </div>
              <div class="record-details">
                <div class="detail-row">
                  <span class="detail-label">物流公司：</span>
                  <span class="detail-value">{{ record.logisticsCompany }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">运单号：</span>
                  <span class="detail-value">{{ record.trackingNumber }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">状态：</span>
                  <a-tag :color="getStatusColor(record.status)" size="small">{{ record.status }}</a-tag>
                </div>
                <div v-if="record.remark" class="detail-row">
                  <span class="detail-label">备注：</span>
                  <span class="detail-value">{{ record.remark }}</span>
                </div>
              </div>
              <!-- 附件 -->
              <div v-if="record.attachments && record.attachments.length > 0" class="record-attachments">
                <div class="attachments-label">附件：</div>
                <div class="attachments-list">
                  <div 
                    v-for="(attachment, idx) in record.attachments" 
                    :key="idx" 
                    class="attachment-item"
                    @click="viewAttachment(attachment)"
                  >
                    <icon-file class="attachment-icon" />
                    <span class="attachment-name">{{ attachment.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-records">
            <icon-empty class="empty-icon" />
            <span class="empty-text">暂无物流单记录</span>
          </div>
        </div>
      </div>

      <!-- 签收单记录 -->
      <div class="record-section">
        <div class="section-header">
          <h4 class="section-title">
            <icon-check-circle class="mr-1" />
            签收单记录
          </h4>
        </div>
        <div class="record-content">
          <div v-if="orderData.receiptRecords && orderData.receiptRecords.length > 0" class="records-list">
            <div v-for="(record, index) in orderData.receiptRecords" :key="index" class="record-item">
              <div class="record-header">
                <span class="record-title">{{ record.title }}</span>
                <span class="record-time">{{ record.signTime }}</span>
              </div>
              <div class="record-details">
                <div class="detail-row">
                  <span class="detail-label">签收人：</span>
                  <span class="detail-value">{{ record.signedBy }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">签收地址：</span>
                  <span class="detail-value">{{ record.signAddress }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">状态：</span>
                  <a-tag :color="getStatusColor(record.status)" size="small">{{ record.status }}</a-tag>
                </div>
                <div v-if="record.remark" class="detail-row">
                  <span class="detail-label">备注：</span>
                  <span class="detail-value">{{ record.remark }}</span>
                </div>
              </div>
              <!-- 附件 -->
              <div v-if="record.attachments && record.attachments.length > 0" class="record-attachments">
                <div class="attachments-label">附件：</div>
                <div class="attachments-list">
                  <div 
                    v-for="(attachment, idx) in record.attachments" 
                    :key="idx" 
                    class="attachment-item"
                    @click="viewAttachment(attachment)"
                  >
                    <icon-file class="attachment-icon" />
                    <span class="attachment-name">{{ attachment.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-records">
            <icon-empty class="empty-icon" />
            <span class="empty-text">暂无签收单记录</span>
          </div>
        </div>
      </div>

      <!-- 延迟发货记录 -->
      <div class="record-section">
        <div class="section-header">
          <h4 class="section-title">
            <icon-clock-circle class="mr-1" />
            延迟发货记录
          </h4>
        </div>
        <div class="record-content">
          <div v-if="orderData.delayRecords && orderData.delayRecords.length > 0" class="records-list">
            <div v-for="(record, index) in orderData.delayRecords" :key="index" class="record-item">
              <div class="record-header">
                <span class="record-title">{{ record.title }}</span>
                <span class="record-time">{{ record.delayTime }}</span>
              </div>
              <div class="record-details">
                <div class="detail-row">
                  <span class="detail-label">延迟原因：</span>
                  <span class="detail-value">{{ record.delayReason }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">预计发货时间：</span>
                  <span class="detail-value">{{ record.expectedShipTime }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">状态：</span>
                  <a-tag :color="getStatusColor(record.status)" size="small">{{ record.status }}</a-tag>
                </div>
                <div v-if="record.remark" class="detail-row">
                  <span class="detail-label">备注：</span>
                  <span class="detail-value">{{ record.remark }}</span>
                </div>
              </div>
              <!-- 附件 -->
              <div v-if="record.attachments && record.attachments.length > 0" class="record-attachments">
                <div class="attachments-label">附件：</div>
                <div class="attachments-list">
                  <div 
                    v-for="(attachment, idx) in record.attachments" 
                    :key="idx" 
                    class="attachment-item"
                    @click="viewAttachment(attachment)"
                  >
                    <icon-file class="attachment-icon" />
                    <span class="attachment-name">{{ attachment.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-records">
            <icon-empty class="empty-icon" />
            <span class="empty-text">暂无延迟发货记录</span>
          </div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
const props = defineProps({
  orderData: {
    type: Object,
    required: true
  }
})

// 获取状态颜色
const getStatusColor = (status) => {
  const statusColorMap = {
    '已发货': 'blue',
    '运输中': 'orange',
    '已签收': 'green',
    '已完成': 'green',
    '延迟': 'red',
    '待处理': 'gray',
    '处理中': 'orange'
  }
  return statusColorMap[status] || 'gray'
}

// 查看附件
const viewAttachment = (attachment) => {
  if (attachment.url) {
    window.open(attachment.url, '_blank')
  } else {
    console.log('查看附件:', attachment.name)
    // 这里可以添加具体的附件查看逻辑
  }
}
</script>

<style scoped>
.detail-card {
  margin-bottom: 16px;
  background: #ffffff;
  height: 100%;
  overflow: visible;
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.mr-1 {
  margin-right: 4px;
}

/* 日志记录容器 */
.log-records-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 记录区域样式 */
.record-section {
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  padding: 16px 20px;
  background: #f7f8fa;
  border-bottom: 1px solid #e5e6eb;
}

.section-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.record-content {
  padding: 16px 20px;
  background: #ffffff;
}

/* 记录列表样式 */
.records-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.record-item {
  padding: 16px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #e5e6eb;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.record-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.record-time {
  color: #86909c;
  font-size: 12px;
}

/* 记录详情样式 */
.record-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
}

.detail-value {
  font-size: 13px;
  color: #333;
  word-break: break-word;
}

/* 附件样式 */
.record-attachments {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e6eb;
}

.attachments-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
  margin-bottom: 8px;
}

.attachments-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.attachment-item:hover {
  background: #f2f3f5;
  border-color: #165dff;
}

.attachment-icon {
  color: #165dff;
  font-size: 12px;
  flex-shrink: 0;
}

.attachment-name {
  color: #333;
  font-size: 12px;
  word-break: break-word;
}

/* 空状态样式 */
.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #86909c;
}

.empty-icon {
  font-size: 32px;
  margin-bottom: 8px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
  color: #86909c;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-label {
    min-width: auto;
  }
  
  .attachments-list {
    flex-direction: column;
  }
}
</style>
