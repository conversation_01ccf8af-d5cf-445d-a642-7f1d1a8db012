<template>
  <a-card title="成本信息" class="detail-card">
    <!-- 多商品成本信息 -->
    <div v-if="orderData.products && orderData.products.length > 0" class="products-cost-container">
      <div v-for="(product, index) in orderData.products" :key="product.id" class="product-cost-section">
        <div class="product-cost-header">
          <h4 class="product-title">
            <icon-package class="mr-1" />
            {{ product.productName }}
            <a-tag size="small" color="blue" class="ml-2">商品 {{ index + 1 }}</a-tag>
          </h4>
        </div>

        <a-row :gutter="16">
          <!-- 商品成本明细 -->
          <a-col :span="12">
            <div class="cost-section">
              <h5 class="section-title">
                <icon-calculator class="mr-1" />
                成本明细
              </h5>
              <a-descriptions :column="1" bordered size="small">
                <a-descriptions-item label="实际成本">
                  <span class="cost-value">¥{{ product.actualCost }}</span>
                </a-descriptions-item>
                <a-descriptions-item label="巡检成本">
                  <span class="cost-value">¥{{ product.inspectionCost }}</span>
                </a-descriptions-item>
                <a-descriptions-item label="毛利润">
                  <span class="profit-amount">¥{{ calculateProductProfit(product) }}</span>
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-col>

          <!-- 商品利润分析 -->
          <a-col :span="12">
            <div class="profit-section">
              <h5 class="section-title">
                <icon-fund class="mr-1" />
                利润分析
              </h5>
              <a-descriptions :column="1" bordered size="small">
                <a-descriptions-item label="单价">
                  <span class="price-value">¥{{ product.unitPrice }}</span>
                </a-descriptions-item>
                <a-descriptions-item label="数量">
                  <span class="quantity-value">{{ product.quantity }}件</span>
                </a-descriptions-item>
                <a-descriptions-item label="毛利率">
                  <span class="profit-value" :class="getProfitClass(product.profitRate)">
                    {{ product.profitRate }}
                  </span>
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-col>
        </a-row>

        <!-- 分隔线 -->
        <a-divider v-if="index < orderData.products.length - 1" />
      </div>
    </div>

    <!-- 兼容旧的单商品数据结构 -->
    <div v-else class="single-product-cost">
      <a-row :gutter="16">
        <!-- 成本明细 -->
        <a-col :span="12">
          <div class="cost-section">
            <h4 class="section-title">
              <icon-calculator class="mr-1" />
              成本明细
            </h4>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="实际成本">
                <span class="cost-value">¥{{ orderData.actualCost }}</span>
              </a-descriptions-item>
              <a-descriptions-item label="巡检成本">
                <span class="cost-value">¥{{ orderData.inspectionCost }}</span>
              </a-descriptions-item>
              <a-descriptions-item label="毛利润">
                <span class="profit-amount">¥{{ getProfitAmount() }}</span>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-col>

        <!-- 利润分析 -->
        <a-col :span="12">
          <div class="profit-section">
            <h4 class="section-title">
              <icon-fund class="mr-1" />
              利润分析
            </h4>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="毛利率">
                <span class="profit-value" :class="getProfitClass(orderData.profitRate)">
                  {{ orderData.profitRate }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="总毛利率">
                <span class="profit-value" :class="getProfitClass(orderData.grossProfitRate)">
                  {{ orderData.grossProfitRate }}
                </span>
              </a-descriptions-item>
              <a-descriptions-item label="是否亏损">
                <a-tag :color="isLossOrder() ? 'red' : 'green'">
                  {{ isLossOrder() ? '是' : '否' }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-col>
      </a-row>
    </div>

    <!-- 订单总成本分析 -->
    <a-divider>订单总成本构成</a-divider>
    <div class="cost-chart">
      <a-progress
        type="circle"
        :percent="getCostRatio()"
        :width="120"
        :stroke-width="8"
        class="cost-progress"
      >
        <template #text>
          <div class="progress-text">
            <div class="progress-label">成本率</div>
            <div class="progress-value">{{ getCostRatio() }}%</div>
          </div>
        </template>
      </a-progress>

      <div class="cost-breakdown">
        <div class="breakdown-item">
          <span class="breakdown-label">销售金额：</span>
          <span class="breakdown-value">¥{{ orderData.totalAmount }}</span>
        </div>
        <div class="breakdown-item">
          <span class="breakdown-label">成本总计：</span>
          <span class="breakdown-value">¥{{ orderData.costTotal }}</span>
        </div>
        <div class="breakdown-item">
          <span class="breakdown-label">总实际成本：</span>
          <span class="breakdown-value">¥{{ getTotalActualCost() }}</span>
        </div>
        <div class="breakdown-item">
          <span class="breakdown-label">总巡检成本：</span>
          <span class="breakdown-value">¥{{ getTotalInspectionCost() }}</span>
        </div>
        <div class="breakdown-item">
          <span class="breakdown-label">总毛利润：</span>
          <span class="breakdown-value profit">
            ¥{{ getTotalProfitAmount() }}
          </span>
        </div>
        <div class="breakdown-item">
          <span class="breakdown-label">总毛利率：</span>
          <span class="breakdown-value profit">
            {{ orderData.grossProfitRate }}
          </span>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  orderData: {
    type: Object,
    required: true
  }
})

// 获取利润样式类
const getProfitClass = (profitRate) => {
  if (!profitRate) return ''
  const rate = parseFloat(profitRate.replace('%', ''))
  if (rate > 15) return 'profit-high'
  if (rate > 5) return 'profit-medium'
  return 'profit-low'
}

// 计算成本率
const getCostRatio = () => {
  const totalAmount = parseFloat(props.orderData.totalAmount || 0)
  const costTotal = parseFloat(props.orderData.costTotal || 0)
  if (totalAmount === 0) return 0
  return Math.round((costTotal / totalAmount) * 100)
}

// 计算单个商品的毛利润
const calculateProductProfit = (product) => {
  const totalAmount = parseFloat(product.totalAmount || 0)
  const actualCost = parseFloat(product.actualCost || 0)
  const inspectionCost = parseFloat(product.inspectionCost || 0)
  const totalCost = actualCost + inspectionCost
  return (totalAmount - totalCost).toFixed(2)
}

// 计算总利润金额（兼容旧数据结构）
const getProfitAmount = () => {
  const totalAmount = parseFloat(props.orderData.totalAmount || 0)
  const costTotal = parseFloat(props.orderData.costTotal || 0)
  return (totalAmount - costTotal).toFixed(2)
}

// 计算总实际成本
const getTotalActualCost = () => {
  if (props.orderData.products && props.orderData.products.length > 0) {
    const total = props.orderData.products.reduce((sum, product) => {
      return sum + parseFloat(product.actualCost || 0)
    }, 0)
    return total.toFixed(2)
  }
  return props.orderData.actualCost || '0.00'
}

// 计算总巡检成本
const getTotalInspectionCost = () => {
  if (props.orderData.products && props.orderData.products.length > 0) {
    const total = props.orderData.products.reduce((sum, product) => {
      return sum + parseFloat(product.inspectionCost || 0)
    }, 0)
    return total.toFixed(2)
  }
  return props.orderData.inspectionCost || '0.00'
}

// 计算总毛利润
const getTotalProfitAmount = () => {
  if (props.orderData.products && props.orderData.products.length > 0) {
    const total = props.orderData.products.reduce((sum, product) => {
      return sum + parseFloat(calculateProductProfit(product))
    }, 0)
    return total.toFixed(2)
  }
  return getProfitAmount()
}

// 判断是否为亏损订单（成本总价 > 巡检成本）
const isLossOrder = () => {
  const costTotal = parseFloat(props.orderData.costTotal || 0)
  const inspectionCost = parseFloat(getTotalInspectionCost())
  return costTotal > inspectionCost
}
</script>

<style scoped>
.detail-card {
  margin-bottom: 16px;
  background: #ffffff;
}

.products-cost-container {
  margin-bottom: 20px;
}

.product-cost-section {
  margin-bottom: 24px;
}

.product-cost-section:last-child {
  margin-bottom: 0;
}

.product-cost-header {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #165dff;
}

.product-title {
  margin: 0;
  color: #333;
  font-size: 15px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.section-title {
  margin-bottom: 12px;
  color: #333;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.cost-section,
.profit-section {
  height: 100%;
}

.cost-value {
  color: #f53f3f;
  font-weight: 600;
}

.profit-amount {
  color: #00b42a;
  font-weight: 600;
  font-size: 15px;
}

.price-value {
  color: #165dff;
  font-weight: 600;
}

.quantity-value {
  color: #333;
  font-weight: 500;
}

.total-cost {
  color: #f53f3f;
  font-weight: 700;
  font-size: 16px;
}

.profit-value {
  font-weight: 600;
}

.profit-high {
  color: #00b42a;
}

.profit-medium {
  color: #ff7d00;
}

.profit-low {
  color: #f53f3f;
}

.cost-chart {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.cost-progress {
  flex-shrink: 0;
}

.progress-text {
  text-align: center;
}

.progress-label {
  font-size: 12px;
  color: #86909c;
}

.progress-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.cost-breakdown {
  flex: 1;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #e5e6eb;
}

.breakdown-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.breakdown-label {
  color: #86909c;
}

.breakdown-value {
  font-weight: 600;
  color: #333;
}

.breakdown-value.profit {
  color: #00b42a;
}

.ml-2 {
  margin-left: 8px;
}

.mr-1 {
  margin-right: 4px;
}

:deep(.arco-descriptions-item-label) {
  font-weight: 500;
  color: #666;
}

:deep(.arco-descriptions-item-value) {
  color: #333;
}

:deep(.arco-divider) {
  margin: 20px 0;
}
</style>
