<template>
  <a-card class="detail-card">
    <template #title>
      <div class="card-title">
        <icon-history class="mr-1" />
        订单流转记录
      </div>
    </template>

    <!-- 切换标签 -->
    <div class="flow-tabs">
      <div 
        class="tab-item" 
        :class="{ active: activeTab === 'flow' }"
        @click="activeTab = 'flow'"
      >
        订单全流程
      </div>
      <div 
        class="tab-item" 
        :class="{ active: activeTab === 'log' }"
        @click="activeTab = 'log'"
      >
        订单日志
      </div>
    </div>

    <div class="flow-records-container">
      <!-- 订单全流程 -->
      <div v-if="activeTab === 'flow'" class="flow-content">
        <div v-if="orderData.flowRecords && orderData.flowRecords.length > 0" class="timeline-container">
          <div v-for="(record, index) in orderData.flowRecords" :key="index" class="timeline-item">
            <div class="timeline-dot" :class="getStatusClass(record.status)">
              <component :is="getStatusIcon(record.status)" class="status-icon" />
            </div>
            <div class="timeline-content">
              <div class="record-header">
                <span class="record-title">{{ record.title }}</span>
                <span class="record-time">{{ record.createTime }}</span>
              </div>
              <div class="record-details">
                <div v-if="record.operator" class="detail-item">
                  <span class="detail-label">操作人：</span>
                  <span class="detail-value">{{ record.operator }}</span>
                </div>
                <div v-if="record.reviewer" class="detail-item">
                  <span class="detail-label">审核人：</span>
                  <span class="detail-value">{{ record.reviewer }}</span>
                </div>
                <div v-if="record.result" class="detail-item">
                  <span class="detail-label">审核结果：</span>
                  <span class="detail-value">{{ record.result }}</span>
                </div>
                <div v-if="record.receiver" class="detail-item">
                  <span class="detail-label">接收账号：</span>
                  <span class="detail-value">{{ record.receiver }}</span>
                </div>
                <div v-if="record.remark" class="detail-item">
                  <span class="detail-label">审核意见：</span>
                  <span class="detail-value">{{ record.remark || '无' }}</span>
                </div>
                <div v-if="record.description" class="detail-item description">
                  <span class="detail-value">{{ record.description }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-records">
          <icon-empty class="empty-icon" />
          <span class="empty-text">暂无流转记录</span>
        </div>
      </div>

      <!-- 订单日志 -->
      <div v-if="activeTab === 'log'" class="log-content">
        <div v-if="orderData.operationLogs && orderData.operationLogs.length > 0" class="timeline-container">
          <div v-for="(log, index) in orderData.operationLogs" :key="index" class="timeline-item">
            <div class="timeline-dot log-dot">
              <icon-file-text class="status-icon" />
            </div>
            <div class="timeline-content">
              <div class="record-header">
                <span class="record-title">{{ log.action }}</span>
                <span class="record-time">{{ log.createTime }}</span>
              </div>
              <div class="record-details">
                <div v-if="log.operator" class="detail-item">
                  <span class="detail-label">操作人：</span>
                  <span class="detail-value">{{ log.operator }}</span>
                </div>
                <div v-if="log.module" class="detail-item">
                  <span class="detail-label">模块：</span>
                  <span class="detail-value">{{ log.module }}</span>
                </div>
                <div v-if="log.description" class="detail-item description">
                  <span class="detail-value">{{ log.description }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-records">
          <icon-empty class="empty-icon" />
          <span class="empty-text">暂无操作日志</span>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  orderData: {
    type: Object,
    required: true
  }
})

const activeTab = ref('flow')

// 获取状态样式类
const getStatusClass = (status) => {
  const statusClassMap = {
    'success': 'success',
    'processing': 'processing', 
    'warning': 'warning',
    'error': 'error',
    'pending': 'pending',
    'completed': 'success',
    'approved': 'success',
    'rejected': 'error',
    'shipped': 'processing',
    'delivered': 'success'
  }
  return statusClassMap[status] || 'default'
}

// 获取状态图标
const getStatusIcon = (status) => {
  const statusIconMap = {
    'success': 'icon-check-circle',
    'processing': 'icon-loading',
    'warning': 'icon-exclamation-circle',
    'error': 'icon-close-circle',
    'pending': 'icon-clock-circle',
    'completed': 'icon-check-circle',
    'approved': 'icon-check-circle',
    'rejected': 'icon-close-circle',
    'shipped': 'icon-truck',
    'delivered': 'icon-check-circle'
  }
  return statusIconMap[status] || 'icon-record'
}
</script>

<style scoped>
.detail-card {
  margin-bottom: 16px;
  background: #ffffff;
  height: 100%;
  overflow: visible;
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.mr-1 {
  margin-right: 4px;
}

/* 切换标签样式 */
.flow-tabs {
  display: flex;
  border-bottom: 1px solid #e5e6eb;
  margin-bottom: 20px;
}

.tab-item {
  padding: 12px 24px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
}

.tab-item:hover {
  color: #165dff;
}

.tab-item.active {
  color: #165dff;
  border-bottom-color: #165dff;
  font-weight: 600;
}

/* 流转记录容器 */
.flow-records-container {
  min-height: 300px;
}

.flow-content,
.log-content {
  position: relative;
}

/* 时间线样式 */
.timeline-container {
  position: relative;
  padding-left: 20px;
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e5e6eb;
}

.timeline-item {
  position: relative;
  margin-bottom: 24px;
  padding-left: 40px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

/* 时间线节点 */
.timeline-dot {
  position: absolute;
  left: -25px;
  top: 4px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  border: 2px solid #e5e6eb;
  z-index: 1;
}

.timeline-dot.success {
  border-color: #00b42a;
  background: #f0f9ff;
}

.timeline-dot.processing {
  border-color: #165dff;
  background: #f0f9ff;
}

.timeline-dot.warning {
  border-color: #ff7d00;
  background: #fff7e6;
}

.timeline-dot.error {
  border-color: #f53f3f;
  background: #ffece8;
}

.timeline-dot.pending {
  border-color: #86909c;
  background: #f7f8fa;
}

.timeline-dot.log-dot {
  border-color: #165dff;
  background: #f0f9ff;
}

.status-icon {
  font-size: 12px;
  color: inherit;
}

.timeline-dot.success .status-icon {
  color: #00b42a;
}

.timeline-dot.processing .status-icon {
  color: #165dff;
}

.timeline-dot.warning .status-icon {
  color: #ff7d00;
}

.timeline-dot.error .status-icon {
  color: #f53f3f;
}

.timeline-dot.pending .status-icon {
  color: #86909c;
}

.timeline-dot.log-dot .status-icon {
  color: #165dff;
}

/* 时间线内容 */
.timeline-content {
  background: #fafbfc;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.record-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.record-time {
  color: #86909c;
  font-size: 12px;
}

/* 记录详情 */
.record-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
}

.detail-item.description {
  margin-top: 4px;
  padding-top: 8px;
  border-top: 1px solid #e5e6eb;
}

.detail-label {
  color: #666;
  font-weight: 500;
  min-width: 70px;
  flex-shrink: 0;
}

.detail-value {
  color: #333;
  word-break: break-word;
  line-height: 1.4;
}

/* 空状态样式 */
.empty-records {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #86909c;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
  color: #86909c;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .detail-item {
    flex-direction: column;
    gap: 4px;
  }
  
  .detail-label {
    min-width: auto;
  }
  
  .timeline-item {
    padding-left: 30px;
  }
  
  .timeline-dot {
    left: -20px;
    width: 20px;
    height: 20px;
  }
  
  .status-icon {
    font-size: 10px;
  }
}
</style>
