<template>
  <a-card title="操作日志" class="detail-card">
    <div class="log-header">
      <a-space>
        <a-select v-model="filterType" placeholder="筛选类型" style="width: 120px" allow-clear>
          <a-option value="all">全部</a-option>
          <a-option value="create">创建</a-option>
          <a-option value="update">更新</a-option>
          <a-option value="status">状态变更</a-option>
          <a-option value="remark">备注</a-option>
        </a-select>
        <a-button @click="refreshLogs">
          <template #icon><icon-refresh /></template>
          刷新
        </a-button>
      </a-space>
    </div>
    
    <div class="log-content">
      <a-timeline>
        <a-timeline-item
          v-for="log in filteredLogs"
          :key="log.id"
        >
          <template #dot>
            <component :is="getLogIcon(log.type)" :style="{ color: getLogColor(log.type) }" />
          </template>
          <div class="log-item">
            <div class="log-header-info">
              <span class="log-action">{{ log.action }}</span>
              <span class="log-time">{{ formatDateTime(log.createdAt) }}</span>
            </div>
            <div class="log-content-info">
              <div class="log-description">{{ log.description }}</div>
              <div class="log-operator">
                <a-avatar size="small" class="mr-1">
                  {{ log.operator?.charAt(0) }}
                </a-avatar>
                {{ log.operator }}
              </div>
            </div>
            <div v-if="log.details" class="log-details">
              <a-collapse>
                <a-collapse-item header="查看详情" key="details">
                  <pre class="log-details-content">{{ JSON.stringify(log.details, null, 2) }}</pre>
                </a-collapse-item>
              </a-collapse>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
      
      <a-empty v-if="filteredLogs.length === 0" description="暂无操作日志">
        <template #image>
          <icon-history style="font-size: 48px; color: #c9cdd4;" />
        </template>
      </a-empty>
    </div>
  </a-card>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const props = defineProps({
  orderData: {
    type: Object,
    required: true
  }
})

const filterType = ref('all')

// 模拟操作日志数据
const logs = ref([
  {
    id: 1,
    type: 'create',
    action: '订单创建',
    description: '订单已创建，等待审核',
    operator: '系统',
    createdAt: props.orderData.orderTime,
    details: {
      orderNumber: props.orderData.orderNumber,
      totalAmount: props.orderData.totalAmount,
      purchaser: props.orderData.purchaser
    }
  },
  {
    id: 2,
    type: 'status',
    action: '状态变更',
    description: '订单状态从"待审核"变更为"审核通过"',
    operator: '张三',
    createdAt: props.orderData.purchaseTime,
    details: {
      oldStatus: '待审核',
      newStatus: '审核通过',
      reason: '订单信息完整，审核通过'
    }
  },
  {
    id: 3,
    type: 'update',
    action: '信息更新',
    description: '更新了采购员信息',
    operator: '李四',
    createdAt: '2023-12-01 15:30:00',
    details: {
      field: 'purchaser',
      oldValue: '王五',
      newValue: props.orderData.purchaser
    }
  },
  {
    id: 4,
    type: 'remark',
    action: '添加备注',
    description: '添加了采购备注',
    operator: props.orderData.purchaser,
    createdAt: '2023-12-01 16:00:00',
    details: {
      remarkType: '采购备注',
      content: props.orderData.purchaseRemark
    }
  }
])

// 筛选后的日志
const filteredLogs = computed(() => {
  if (filterType.value === 'all') {
    return logs.value
  }
  return logs.value.filter(log => log.type === filterType.value)
})

// 获取日志图标
const getLogIcon = (type) => {
  const iconMap = {
    'create': 'icon-plus-circle',
    'update': 'icon-edit',
    'status': 'icon-swap',
    'remark': 'icon-message'
  }
  return iconMap[type] || 'icon-record'
}

// 获取日志颜色
const getLogColor = (type) => {
  const colorMap = {
    'create': '#00b42a',
    'update': '#165dff',
    'status': '#ff7d00',
    'remark': '#722ed1'
  }
  return colorMap[type] || '#86909c'
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 刷新日志
const refreshLogs = () => {
  // 这里可以调用API刷新日志数据
  console.log('刷新操作日志')
}

onMounted(() => {
  // 组件挂载时可以加载日志数据
})
</script>

<style scoped>
.detail-card {
  margin-bottom: 16px;
  background: #ffffff;
}

.log-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e6eb;
}

.log-content {
  padding-left: 10px;
  max-height: 500px;
  overflow-y: auto;
}

.log-item {
  padding-left: 8px;
  margin-bottom: 8px;
}

.log-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.log-action {
  font-weight: 600;
  color: #333;
}

.log-time {
  color: #86909c;
  font-size: 12px;
}

.log-content-info {
  margin-bottom: 8px;
}

.log-description {
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
}

.log-operator {
  display: flex;
  align-items: center;
  color: #86909c;
  font-size: 12px;
}

.log-details {
  margin-top: 8px;
}

.log-details-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  white-space: pre-wrap;
  word-break: break-all;
}

.mr-1 {
  margin-right: 4px;
}

:deep(.arco-timeline-item-content) {
  padding-bottom: 16px;
}

:deep(.arco-collapse-item-header) {
  font-size: 12px;
  padding: 8px 0;
}

:deep(.arco-collapse-item-content) {
  padding: 0;
}
</style>
