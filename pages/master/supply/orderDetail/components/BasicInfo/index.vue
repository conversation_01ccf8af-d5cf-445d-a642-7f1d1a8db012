<template>
  <div class="basic-info-container">
    <!-- 订单标题区域 -->
    <div class="order-header">
      <div class="order-title">
        <h3>订单详情</h3>
        <div class="order-meta">
          <span class="order-number">订单编号：{{ orderData.orderNumber }}</span>
          <span class="order-time">下单时间：{{ formatDateTime(orderData.orderTime) }}</span>
          <span class="order-source">来源：{{ orderData.orderSource }}</span>
          <span class="order-status">状态：{{ orderData.orderStatus }}</span>
          <span class="order-type">类型：{{ orderData.orderType }}</span>
        </div>
      </div>
    </div>

    <!-- 订单状态步骤条 -->
    <div class="order-steps">
      <a-steps :current="getCurrentStep()" size="small" class="custom-steps">
        <a-step
          title="等待商家发货"
          :status="getStepStatus('waiting')"
          description=""
        />
        <a-step
          title="接单"
          :status="getStepStatus('accepted')"
          description=""
        />
        <a-step
          title="发货"
          :status="getStepStatus('shipped')"
          description=""
        />
        <a-step
          title="送达"
          :status="getStepStatus('delivered')"
          description=""
        />
        <a-step
          title="上传物流单"
          :status="getStepStatus('uploaded')"
          description=""
        />
      </a-steps>
    </div>

    <!-- 信息展示区域 -->
    <div class="info-sections">
      <!-- 收货人信息 -->
      <div class="info-section">
        <div class="section-title">收货人信息</div>
        <div class="section-content">
          <div class="info-item">
            <span class="label">收货人：</span>
            <span class="value">{{ orderData.receiverName || '213' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系电话：</span>
            <span class="value">{{ orderData.receiverPhone || '23' }}</span>
          </div>
          <div class="info-item">
            <span class="label">收货地址：</span>
            <span class="value">{{ orderData.receiverAddress || '内蒙古通辽市科尔沁左翼中旗33' }}</span>
          </div>
        </div>
      </div>

      <!-- 商户信息 -->
      <div class="info-section">
        <div class="section-title">商户信息</div>
        <div class="section-content">
          <div class="info-item">
            <span class="label">商户名称：</span>
            <span class="value">{{ orderData.merchantName || '广东八灵科技发展有限公司' }}</span>
          </div>
          <div class="info-item">
            <span class="label">商户联系人：</span>
            <span class="value">{{ orderData.merchantContact || '黄建翔' }}</span>
          </div>
          <div class="info-item">
            <span class="label">商户联系电话：</span>
            <span class="value">{{ orderData.merchantPhone || '13826112439' }}</span>
          </div>
        </div>
      </div>

      <!-- 付款信息 -->
      <div class="info-section">
        <div class="section-title">付款信息</div>
        <div class="section-content">
          <div class="info-item">
            <span class="label">支付金额：</span>
            <span class="value payment-amount">{{ orderData.paymentAmount || '23.00' }}</span>
          </div>
          <div class="info-item">
            <span class="label">支付方式：</span>
            <span class="value">{{ orderData.paymentMethod || '货到付款' }}</span>
          </div>
          <div class="info-item">
            <span class="label">付款时间：</span>
            <span class="value">{{ formatDateTime(orderData.paymentTime) || '2025-03-18 00:00:00' }}</span>
          </div>
        </div>
      </div>

      <!-- 订单备注 -->
      <div class="info-section">
        <div class="section-title">订单备注</div>
        <div class="section-content">
          <div class="info-item">
            <span class="label">订单备注：</span>
            <span class="value">{{ orderData.orderRemark || '-' }}</span>
          </div>
          <div class="info-item">
            <span class="label">客户备注：</span>
            <span class="value">{{ orderData.customerRemark || '-' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  orderData: {
    type: Object,
    required: true
  }
})

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 获取订单来源颜色
const getSourceColor = (source) => {
  const colorMap = {
    '商城': 'blue',
    '竞价': 'orange',
    '手动': 'green'
  }
  return colorMap[source] || 'gray'
}

// 获取当前步骤
const getCurrentStep = () => {
  const status = props.orderData.orderStatus
  const stepMap = {
    '等待商家发货': 0,
    '接单': 1,
    '发货': 2,
    '送达': 3,
    '上传物流单': 4,
    '已完成': 4,
    '已废止': 0
  }
  return stepMap[status] || 0
}

// 获取步骤状态
const getStepStatus = (step) => {
  const currentStep = getCurrentStep()
  const status = props.orderData.orderStatus

  const stepIndex = {
    'waiting': 0,
    'accepted': 1,
    'shipped': 2,
    'delivered': 3,
    'uploaded': 4
  }

  const index = stepIndex[step]

  // 如果订单已废止，显示错误状态
  if (status === '已废止' && index === 0) {
    return 'error'
  }

  if (index < currentStep) {
    return 'finish'
  } else if (index === currentStep) {
    return 'process'
  } else {
    return 'wait'
  }
}
</script>

<style scoped>
.basic-info-container {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

/* 订单标题区域 */
.order-header {
  background: #fafbfc;
  border-bottom: 1px solid #e5e6eb;
  padding: 16px 20px;
}

.order-title h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
}

.order-meta {
  display: flex;
  gap: 24px;
  font-size: 13px;
  color: #86909c;
}

.order-meta span {
  display: flex;
  align-items: center;
}

.order-number {
  color: #165dff;
  font-weight: 500;
}

/* 步骤条区域 */
.order-steps {
  padding: 20px;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
}

.custom-steps {
  max-width: 800px;
}

:deep(.arco-steps-item-title) {
  font-size: 13px;
  color: #4e5969;
}

:deep(.arco-steps-item-description) {
  font-size: 12px;
  color: #86909c;
}

:deep(.arco-steps-item-icon) {
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
}

/* 信息展示区域 */
.info-sections {
  display: flex;
  background: #ffffff;
  border-top: 1px solid #f0f0f0;
}

.info-section {
  flex: 1;
  border-right: 1px solid #f0f0f0;
  min-height: 120px;
}

.info-section:last-child {
  border-right: none;
}

.section-title {
  background: #f7f8fa;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  color: #1d2129;
  border-bottom: 1px solid #f0f0f0;
}

.section-content {
  padding: 16px;
}

.info-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.5;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  color: #86909c;
  white-space: nowrap;
  margin-right: 4px;
}

.value {
  color: #1d2129;
  word-break: break-all;
}

.order-number-detail {
  font-weight: 600;
  color: #165dff;
  font-size: 14px;
}

.payment-amount {
  font-weight: 600;
  color: #f53f3f;
}

.ml-2 {
  margin-left: 8px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

:deep(.arco-descriptions-item-label) {
  font-weight: 500;
  color: #4e5969;
}

:deep(.arco-descriptions-item-value) {
  color: #1d2129;
}

:deep(.arco-descriptions-body) {
  background: #ffffff;
}

:deep(.arco-descriptions-item) {
  border-color: #e5e6eb;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .order-meta {
    flex-direction: column;
    gap: 8px;
  }

  .order-steps {
    padding: 16px;
  }

  .info-sections {
    flex-direction: column;
  }

  .info-section {
    border-right: none;
    border-bottom: 1px solid #f0f0f0;
  }

  .info-section:last-child {
    border-bottom: none;
  }

  .section-content {
    padding: 12px;
  }

  .info-item {
    flex-direction: column;
    margin-bottom: 12px;
  }

  .label {
    margin-bottom: 4px;
    margin-right: 0;
  }
}
</style>
