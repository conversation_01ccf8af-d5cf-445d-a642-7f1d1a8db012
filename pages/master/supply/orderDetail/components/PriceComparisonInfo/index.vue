<template>
  <a-card class="detail-card">
    <template #title>
      <div class="card-title">
        <icon-bar-chart class="mr-1" />
        比价信息
      </div>
    </template>

    <!-- 比价信息内容 -->
    <div class="price-comparison-container">
      <!-- 如果有比价数据 -->
      <div v-if="orderData.priceComparisons && orderData.priceComparisons.length > 0" class="comparison-content">
        <div v-for="(comparison, index) in orderData.priceComparisons" :key="index" class="comparison-item">
          <div class="comparison-header">
            <h4 class="product-title">
              <icon-package class="mr-1" />
              {{ comparison.productName }}
              <a-tag size="small" color="blue" class="ml-2">比价 {{ index + 1 }}</a-tag>
            </h4>
          </div>

          <!-- 比价表格 -->
          <a-table 
            :columns="comparisonColumns" 
            :data="comparison.suppliers" 
            :pagination="false" 
            :bordered="{ cell: true }"
            class="comparison-table"
            size="small"
          >
            <!-- 供应商列 -->
            <template #supplier="{ record }">
              <div class="supplier-info">
                <span class="supplier-name">{{ record.supplierName }}</span>
                <a-tag v-if="record.isRecommended" color="green" size="small" class="ml-1">推荐</a-tag>
                <a-tag v-if="record.isSelected" color="blue" size="small" class="ml-1">已选择</a-tag>
              </div>
            </template>

            <!-- 报价列 -->
            <template #price="{ record }">
              <span class="price-value" :class="{ 'lowest-price': record.isLowestPrice }">
                ¥{{ record.price }}
              </span>
            </template>

            <!-- 交期列 -->
            <template #deliveryTime="{ record }">
              <span class="delivery-time">{{ record.deliveryTime }}</span>
            </template>

            <!-- 质保期列 -->
            <template #warranty="{ record }">
              <span class="warranty-period">{{ record.warranty }}</span>
            </template>

            <!-- 备注列 -->
            <template #remark="{ record }">
              <span class="remark-text">{{ record.remark || '-' }}</span>
            </template>
          </a-table>

          <!-- 比价总结 -->
          <div class="comparison-summary">
            <div class="summary-item">
              <span class="summary-label">最低价：</span>
              <span class="summary-value lowest">¥{{ getLowestPrice(comparison.suppliers) }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">最高价：</span>
              <span class="summary-value highest">¥{{ getHighestPrice(comparison.suppliers) }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">价差：</span>
              <span class="summary-value difference">¥{{ getPriceDifference(comparison.suppliers) }}</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">选择原因：</span>
              <span class="summary-value reason">{{ comparison.selectionReason || '价格优势' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 如果没有比价数据 -->
      <div v-else class="no-comparison">
        <div class="empty-state">
          <div class="empty-icon">
            <icon-file-text />
          </div>
          <div class="empty-text">暂无比价信息</div>
          <div class="empty-description">该订单暂未进行供应商比价</div>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
const props = defineProps({
  orderData: {
    type: Object,
    required: true
  }
})

// 比价表格列定义
const comparisonColumns = [
  {
    title: '供应商',
    slotName: 'supplier',
    width: 200
  },
  {
    title: '报价',
    slotName: 'price',
    width: 120,
    align: 'center'
  },
  {
    title: '交期',
    slotName: 'deliveryTime',
    width: 120,
    align: 'center'
  },
  {
    title: '质保期',
    slotName: 'warranty',
    width: 120,
    align: 'center'
  },
  {
    title: '备注',
    slotName: 'remark',
    width: 200
  }
]

// 获取最低价
const getLowestPrice = (suppliers) => {
  if (!suppliers || suppliers.length === 0) return '0.00'
  const prices = suppliers.map(s => parseFloat(s.price || 0))
  return Math.min(...prices).toFixed(2)
}

// 获取最高价
const getHighestPrice = (suppliers) => {
  if (!suppliers || suppliers.length === 0) return '0.00'
  const prices = suppliers.map(s => parseFloat(s.price || 0))
  return Math.max(...prices).toFixed(2)
}

// 获取价差
const getPriceDifference = (suppliers) => {
  if (!suppliers || suppliers.length === 0) return '0.00'
  const prices = suppliers.map(s => parseFloat(s.price || 0))
  const difference = Math.max(...prices) - Math.min(...prices)
  return difference.toFixed(2)
}
</script>

<style scoped>
.detail-card {
  margin-bottom: 16px;
  background: #ffffff;
  height: 100%;
  overflow: visible;
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.mr-1 {
  margin-right: 4px;
}

.ml-1 {
  margin-left: 4px;
}

.ml-2 {
  margin-left: 8px;
}

/* 比价容器样式 */
.price-comparison-container {
  min-height: 200px;
}

.comparison-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.comparison-item {
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  overflow: hidden;
}

.comparison-header {
  padding: 16px 20px;
  background: #f7f8fa;
  border-bottom: 1px solid #e5e6eb;
}

.product-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

/* 比价表格样式 */
.comparison-table {
  margin: 0;
}

.supplier-info {
  display: flex;
  align-items: center;
}

.supplier-name {
  font-weight: 500;
  color: #333;
}

.price-value {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.price-value.lowest-price {
  color: #00b42a;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.delivery-time {
  color: #666;
  font-size: 13px;
}

.warranty-period {
  color: #666;
  font-size: 13px;
}

.remark-text {
  color: #86909c;
  font-size: 12px;
}

/* 比价总结样式 */
.comparison-summary {
  padding: 16px 20px;
  background: #fafbfc;
  border-top: 1px solid #e5e6eb;
  display: flex;
  gap: 32px;
  flex-wrap: wrap;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.summary-value {
  font-size: 14px;
  font-weight: 600;
}

.summary-value.lowest {
  color: #00b42a;
}

.summary-value.highest {
  color: #f53f3f;
}

.summary-value.difference {
  color: #ff7d00;
}

.summary-value.reason {
  color: #333;
  font-weight: 500;
}

/* 空状态样式 */
.no-comparison {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.empty-state {
  text-align: center;
  color: #86909c;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: #666;
}

.empty-description {
  font-size: 14px;
  color: #86909c;
}

/* 表格样式优化 */
:deep(.arco-table-th) {
  background-color: #f7f8fa;
  color: #333;
  font-weight: 600;
  font-size: 13px;
  border-bottom: 1px solid #e5e6eb;
}

:deep(.arco-table-td) {
  padding: 12px 16px;
  border-bottom: 1px solid #f2f3f5;
  vertical-align: middle;
}

:deep(.arco-table-tbody .arco-table-tr:hover .arco-table-td) {
  background-color: #f8f9fa;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .comparison-summary {
    flex-direction: column;
    gap: 12px;
  }
  
  .summary-item {
    justify-content: space-between;
  }
}
</style>
