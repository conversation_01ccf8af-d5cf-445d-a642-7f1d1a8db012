<template>
  <a-card class="detail-card">
    <template #title>
      <div class="card-title">
        <icon-truck class="mr-1" />
        物流信息
        <a-tag color="blue" size="small" class="ml-2">
          {{ getTotalLogisticsCount() }}条物流记录
        </a-tag>
      </div>
    </template>

    <!-- 收货信息 -->
    <div class="address-section mb-4">
      <h4 class="section-title">
        <icon-location class="mr-1" />
        收货信息
      </h4>
      <a-descriptions :column="3" bordered size="small">
        <a-descriptions-item label="收货人">
          <icon-user class="mr-1" />
          {{ orderData.recipientName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="联系电话">
          <icon-phone class="mr-1" />
          {{ orderData.contactPhone || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="收货地址">
          <icon-location class="mr-1" />
          {{ orderData.orderAddress || orderData.deliveryAddress || '-' }}
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 物流信息标签页 -->
    <a-tabs v-model:active-key="activeLogisticsTab" type="card">
      <!-- 平台物流 -->
      <a-tab-pane key="platform" title="平台物流">
        <LogisticsCard
          v-if="orderData.logisticsData?.platform"
          :logistics-info="orderData.logisticsData.platform"
          :recipient-info="getRecipientInfo()"
        />
        <a-empty v-else description="暂无平台物流信息">
          <template #image>
            <icon-truck style="font-size: 48px; color: #c9cdd4;" />
          </template>
        </a-empty>
      </a-tab-pane>

      <!-- 拆分单物流 -->
      <a-tab-pane key="split">
        <template #title>
          拆分单物流
          <a-badge
            v-if="orderData.logisticsData?.split?.length"
            :count="orderData.logisticsData.split.length"
            :offset="[10, -5]"
          />
        </template>
        <div v-if="orderData.logisticsData?.split?.length" class="split-logistics-list">
          <LogisticsCard
            v-for="(splitInfo, index) in orderData.logisticsData.split"
            :key="`split-${index}`"
            :logistics-info="splitInfo"
            :recipient-info="getRecipientInfo()"
            class="mb-4"
          />
        </div>
        <a-empty v-else description="暂无拆分单物流信息">
          <template #image>
            <icon-branch style="font-size: 48px; color: #c9cdd4;" />
          </template>
        </a-empty>
      </a-tab-pane>

      <!-- 补发物流 -->
      <a-tab-pane key="reissue">
        <template #title>
          补发物流
          <a-badge
            v-if="orderData.logisticsData?.reissue?.length"
            :count="orderData.logisticsData.reissue.length"
            :offset="[10, -5]"
          />
        </template>
        <div v-if="orderData.logisticsData?.reissue?.length" class="reissue-logistics-list">
          <LogisticsCard
            v-for="(reissueInfo, index) in orderData.logisticsData.reissue"
            :key="`reissue-${index}`"
            :logistics-info="reissueInfo"
            :recipient-info="getRecipientInfo()"
            class="mb-4"
          />
        </div>
        <a-empty v-else description="暂无补发物流信息">
          <template #image>
            <icon-refresh style="font-size: 48px; color: #c9cdd4;" />
          </template>
        </a-empty>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script setup>
import { ref } from 'vue'
import LogisticsCard from './components/LogisticsCard.vue'

const props = defineProps({
  orderData: {
    type: Object,
    required: true
  }
})

const activeLogisticsTab = ref('platform')

// 获取物流记录总数
const getTotalLogisticsCount = () => {
  let count = 0
  if (props.orderData.logisticsData?.platform) count += 1
  if (props.orderData.logisticsData?.split?.length) count += props.orderData.logisticsData.split.length
  if (props.orderData.logisticsData?.reissue?.length) count += props.orderData.logisticsData.reissue.length
  return count
}

// 获取收货人信息
const getRecipientInfo = () => {
  return {
    recipientName: props.orderData.recipientName,
    contactPhone: props.orderData.contactPhone,
    deliveryAddress: props.orderData.orderAddress || props.orderData.deliveryAddress
  }
}
</script>

<style scoped>
.detail-card {
  margin-bottom: 16px;
  background: #ffffff;
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.section-title {
  margin-bottom: 16px;
  color: #333;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.address-section {
  padding: 16px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
}

.split-logistics-list,
.reissue-logistics-list {
  max-height: 600px;
  overflow-y: auto;
}

.mb-4 {
  margin-bottom: 16px;
}

.mr-1 {
  margin-right: 4px;
}

.ml-2 {
  margin-left: 8px;
}

:deep(.arco-descriptions-item-label) {
  font-weight: 500;
  color: #666;
}

:deep(.arco-descriptions-item-value) {
  color: #333;
}

:deep(.arco-tabs-nav) {
  margin-bottom: 16px;
}

:deep(.arco-badge-number) {
  background: #165dff;
}

/* 自定义滚动条 */
.split-logistics-list::-webkit-scrollbar,
.reissue-logistics-list::-webkit-scrollbar {
  width: 6px;
}

.split-logistics-list::-webkit-scrollbar-track,
.reissue-logistics-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.split-logistics-list::-webkit-scrollbar-thumb,
.reissue-logistics-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.split-logistics-list::-webkit-scrollbar-thumb:hover,
.reissue-logistics-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
