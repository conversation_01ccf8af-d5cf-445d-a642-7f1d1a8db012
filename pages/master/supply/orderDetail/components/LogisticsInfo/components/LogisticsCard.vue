<template>
  <a-card class="logistics-card">
    <template #title>
      <div class="logistics-header">
        <div class="logistics-type">
          <icon-truck v-if="logisticsInfo.type === 'platform'" class="mr-1" />
          <icon-branch v-else-if="logisticsInfo.type === 'split'" class="mr-1" />
          <icon-refresh v-else-if="logisticsInfo.type === 'reissue'" class="mr-1" />
          <span class="type-name">{{ logisticsInfo.typeName }}</span>
          <a-tag 
            :color="getStatusColor(logisticsInfo.shippingStatus)" 
            size="small" 
            class="ml-2"
          >
            {{ logisticsInfo.shippingStatus }}
          </a-tag>
        </div>
        <div v-if="logisticsInfo.splitOrderId || logisticsInfo.reissueOrderId" class="order-info">
          <span class="order-id">
            {{ logisticsInfo.splitOrderId || logisticsInfo.reissueOrderId }}
          </span>
        </div>
      </div>
    </template>

    <!-- 物流基本信息 -->
    <a-row :gutter="16" class="mb-4">
      <a-col :span="12">
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="物流公司">
            <icon-truck class="mr-1" />
            {{ logisticsInfo.shippingCompany }}
          </a-descriptions-item>
          <a-descriptions-item label="物流单号">
            <span class="logistics-number">
              {{ logisticsInfo.trackingNumber }}
              <a-button type="text" size="small" @click="copyTrackingNumber">
                <icon-copy />
              </a-button>
            </span>
          </a-descriptions-item>
          <a-descriptions-item v-if="logisticsInfo.shippedAt" label="发货时间">
            <icon-clock-circle class="mr-1" />
            {{ logisticsInfo.shippedAt }}
          </a-descriptions-item>
          <a-descriptions-item v-if="logisticsInfo.estimatedDelivery" label="预计送达">
            <icon-calendar class="mr-1" />
            {{ logisticsInfo.estimatedDelivery }}
          </a-descriptions-item>
        </a-descriptions>
      </a-col>
      
      <a-col :span="12">
        <!-- 特殊信息显示 -->
        <div v-if="logisticsInfo.type === 'split'" class="special-info">
          <h5 class="info-title">
            <icon-package class="mr-1" />
            拆分商品
          </h5>
          <div class="product-list">
            <a-tag 
              v-for="product in logisticsInfo.productNames" 
              :key="product" 
              color="blue" 
              size="small"
              class="mb-1 mr-1"
            >
              {{ product }}
            </a-tag>
          </div>
        </div>
        
        <div v-else-if="logisticsInfo.type === 'reissue'" class="special-info">
          <h5 class="info-title">
            <icon-exclamation-circle class="mr-1" />
            补发信息
          </h5>
          <a-descriptions :column="1" bordered size="small">
            <a-descriptions-item label="补发原因">
              {{ logisticsInfo.reason }}
            </a-descriptions-item>
            <a-descriptions-item label="补发商品">
              <div class="product-list">
                <a-tag 
                  v-for="product in logisticsInfo.productNames" 
                  :key="product" 
                  color="orange" 
                  size="small"
                  class="mb-1 mr-1"
                >
                  {{ product }}
                </a-tag>
              </div>
            </a-descriptions-item>
          </a-descriptions>
        </div>
        
        <div v-else class="special-info">
          <h5 class="info-title">
            <icon-location class="mr-1" />
            收货信息
          </h5>
          <a-descriptions :column="1" bordered size="small">
            <a-descriptions-item label="收货人">
              {{ recipientInfo.recipientName || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="联系电话">
              {{ recipientInfo.contactPhone || '-' }}
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </a-col>
    </a-row>

    <!-- 物流轨迹 -->
    <a-divider>物流轨迹</a-divider>
    <div class="logistics-tracking">
      <a-timeline v-if="logisticsInfo.trackingData?.length">
        <a-timeline-item 
          v-for="(track, index) in logisticsInfo.trackingData" 
          :key="index"
        >
          <template #dot>
            <icon-check-circle 
              v-if="track.status === '已发货' || track.status === '已签收'" 
              style="color: #00b42a" 
            />
            <icon-truck 
              v-else-if="track.status === '运输中' || track.status === '派送中'" 
              style="color: #165dff" 
            />
            <icon-clock-circle 
              v-else 
              style="color: #86909c" 
            />
          </template>
          <div class="tracking-item">
            <div class="tracking-status">{{ track.status }}</div>
            <div class="tracking-time">{{ track.time }}</div>
            <div class="tracking-desc">{{ track.description }}</div>
            <div v-if="track.location" class="tracking-location">
              位置：{{ track.location }}
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
      
      <a-empty v-else description="暂无物流轨迹信息">
        <template #image>
          <icon-info-circle style="font-size: 48px; color: #c9cdd4;" />
        </template>
      </a-empty>
    </div>
  </a-card>
</template>

<script setup>
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  logisticsInfo: {
    type: Object,
    required: true
  },
  recipientInfo: {
    type: Object,
    default: () => ({})
  }
})

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '已下单': 'blue',
    '待发货': 'orange',
    '已发货': 'cyan',
    '运输中': 'blue',
    '派送中': 'purple',
    '已收货': 'green',
    '已签收': 'green',
    '申请废止': 'red'
  }
  return colorMap[status] || 'gray'
}

// 复制物流单号
const copyTrackingNumber = async () => {
  try {
    await navigator.clipboard.writeText(props.logisticsInfo.trackingNumber)
    Message.success('物流单号已复制到剪贴板')
  } catch (err) {
    Message.error('复制失败')
  }
}
</script>

<style scoped>
.logistics-card {
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.logistics-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logistics-type {
  display: flex;
  align-items: center;
}

.type-name {
  font-weight: 600;
  color: #333;
}

.order-info {
  font-size: 12px;
  color: #86909c;
}

.order-id {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.logistics-number {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #165dff;
}

.special-info {
  height: 100%;
}

.info-title {
  margin-bottom: 12px;
  color: #333;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.product-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.logistics-tracking {
  margin-top: 16px;
}

.tracking-item {
  padding-left: 8px;
}

.tracking-status {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.tracking-time {
  color: #86909c;
  font-size: 12px;
  margin-bottom: 4px;
}

.tracking-desc {
  color: #666;
  font-size: 13px;
  margin-bottom: 2px;
}

.tracking-location {
  color: #86909c;
  font-size: 12px;
}

.mb-1 {
  margin-bottom: 4px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mr-1 {
  margin-right: 4px;
}

.ml-2 {
  margin-left: 8px;
}

:deep(.arco-descriptions-item-label) {
  font-weight: 500;
  color: #666;
}

:deep(.arco-descriptions-item-value) {
  color: #333;
}

:deep(.arco-card-header) {
  border-bottom: 1px solid #e5e6eb;
}

:deep(.arco-divider) {
  margin: 16px 0;
}
</style>
