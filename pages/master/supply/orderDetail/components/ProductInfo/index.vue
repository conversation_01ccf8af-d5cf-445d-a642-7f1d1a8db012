<template>
  <a-card class="detail-card">
    <template #title>
      <div class="card-title">
        <icon-package class="mr-1" />
        商品信息
        <a-tag v-if="orderData.products && orderData.products.length > 0" color="blue" size="small" class="ml-2">
          共{{ orderData.products.length }}件商品
        </a-tag>
      </div>
    </template>

    <!-- 商品表格 -->
    <div class="products-table-container">
      <a-table
        :columns="productColumns"
        :data="getProductTableData()"
        :pagination="false"
        :bordered="{ cell: true }"
        class="products-table"
        :scroll="{ x: 1200 }"
      >
        <!-- 商品信息列 -->
        <template #productInfo="{ record }">
          <div class="product-info-cell">
            <div class="product-image-wrapper">
              <a-image
                :src="record.productImage || '/placeholder-image.jpg'"
                width="60"
                height="60"
                fit="cover"
                class="product-thumbnail"
              />
            </div>
            <div class="product-details">
              <div class="product-name-primary">{{ record.productCategory }}</div>
              <div class="product-name-secondary">{{ record.productName }}</div>
              <div class="product-specs">
                <div class="spec-item">SKU: {{ record.sku }}</div>
                <div class="spec-item">规格型号: {{ record.specification }}</div>
                <div class="spec-item">商品编码: {{ record.productCode }}</div>
              </div>
            </div>
          </div>
        </template>

        <!-- 预期成本价列 -->
        <template #expectedCost="{ record }">
          <span class="cost-value">¥{{ record.expectedCost }}</span>
        </template>

        <!-- 实际供应商列 -->
        <template #actualSupplier="{ record }">
          <span class="supplier-name">{{ record.actualSupplier }}</span>
        </template>

        <!-- 建议供应商列 -->
        <template #suggestedSupplier="{ record }">
          <span class="supplier-name">{{ record.suggestedSupplier }}</span>
        </template>

        <!-- 采购价列 -->
        <template #purchasePrice="{ record }">
          <span class="price-value">¥{{ record.purchasePrice }}</span>
        </template>

        <!-- 合同单价列 -->
        <template #contractPrice="{ record }">
          <span class="price-value">¥{{ record.contractPrice }}</span>
        </template>

        <!-- 采购编号列 -->
        <template #purchaseCode="{ record }">
          <span class="code-value">{{ record.purchaseCode }}</span>
        </template>

        <!-- 单价列 -->
        <template #unitPrice="{ record }">
          <span class="price-value">¥{{ record.unitPrice }}</span>
        </template>

        <!-- 单位列 -->
        <template #unit="{ record }">
          <span class="unit-value">{{ record.unit }}</span>
        </template>

        <!-- 数量列 -->
        <template #quantity="{ record }">
          <span class="quantity-value">{{ record.quantity }}</span>
        </template>

        <!-- 小计列 -->
        <template #subtotal="{ record }">
          <span class="subtotal-value">¥{{ record.totalAmount }}</span>
        </template>
      </a-table>
    </div>

    <!-- 业务关联信息区 -->
    <div class="business-info-section">
      <a-row :gutter="24">
        <!-- 左侧：备注与人员信息 -->
        <a-col :span="16">
          <div class="remarks-section">
            <!-- 业务员备注 -->
            <div class="remark-block">
              <div class="remark-title">业务员备注：</div>
              <div class="remark-content">
                {{ orderData.businessRemark || '无备注信息' }}
              </div>
            </div>

            <!-- 供应商备注 -->
            <div class="remark-block">
              <div class="remark-title">供应商备注：</div>
              <div class="remark-content">
                {{ orderData.supplierRemark || '无备注信息' }}
              </div>
            </div>

            <!-- 收货人信息 -->
            <div class="contact-info">
              <div class="contact-item">
                <span class="contact-label">实际收货人：</span>
                <span class="contact-value">{{ orderData.recipientName || '-' }}</span>
              </div>
              <div class="contact-item">
                <span class="contact-label">电话：</span>
                <span class="contact-value">{{ orderData.contactPhone || '-' }}</span>
              </div>
            </div>

            <!-- 收货地址信息 -->
            <div class="address-info">
              <div class="address-item">
                <span class="address-label">实际收货地址：</span>
                <span class="address-value">{{ orderData.deliveryAddress || '-' }}</span>
              </div>
            </div>

            <!-- 业务员申请附件 -->
            <div class="attachment-info">
              <div class="attachment-title">业务员申请附件：</div>
              <div class="attachment-list">
                <div v-if="orderData.attachments && orderData.attachments.length > 0" class="attachment-items">
                  <div
                    v-for="(attachment, index) in orderData.attachments"
                    :key="index"
                    class="attachment-item"
                    @click="viewAttachment(attachment)"
                  >
                    <icon-file class="attachment-icon" />
                    <span class="attachment-name">{{ attachment.name }}</span>
                    <span class="attachment-size">({{ formatFileSize(attachment.size) }})</span>
                  </div>
                </div>
                <div v-else class="no-attachment">
                  暂无附件
                </div>
              </div>
            </div>
          </div>
        </a-col>

        <!-- 右侧：金额与支付模块 -->
        <a-col :span="8">
          <div class="payment-section">
            <div class="amount-item">
              <span class="amount-label">订单金额：</span>
              <span class="amount-value">¥{{ orderData.totalAmount || '0.00' }}</span>
            </div>
            <div class="amount-item">
              <span class="amount-label">运费：</span>
              <span class="amount-value">¥{{ orderData.freight || '0.00' }}</span>
            </div>
            <div class="amount-item">
              <span class="amount-label">优惠金额：</span>
              <span class="amount-value">¥{{ orderData.discountAmount || '0.00' }}</span>
            </div>
            <div class="amount-item payment-total">
              <span class="amount-label">支付金额：</span>
              <span class="amount-value total">¥{{ calculatePaymentAmount() }}</span>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </a-card>
</template>

<script setup>
const props = defineProps({
  orderData: {
    type: Object,
    required: true
  }
})

// 表格列定义
const productColumns = [
  {
    title: '商品信息',
    slotName: 'productInfo',
    width: 350,
    fixed: 'left'
  },
  {
    title: '预期成本价',
    slotName: 'expectedCost',
    width: 110,
    align: 'center'
  },
  {
    title: '实际供应商',
    slotName: 'actualSupplier',
    width: 130,
    align: 'center'
  },
  {
    title: '建议供应商',
    slotName: 'suggestedSupplier',
    width: 130,
    align: 'center'
  },
  {
    title: '采购价',
    slotName: 'purchasePrice',
    width: 100,
    align: 'center'
  },
  {
    title: '合同单价',
    slotName: 'contractPrice',
    width: 100,
    align: 'center'
  },
  {
    title: '采购编号',
    slotName: 'purchaseCode',
    width: 120,
    align: 'center'
  },
  {
    title: '单价',
    slotName: 'unitPrice',
    width: 100,
    align: 'center'
  },
  {
    title: '单位',
    slotName: 'unit',
    width: 80,
    align: 'center'
  },
  {
    title: '数量',
    slotName: 'quantity',
    width: 80,
    align: 'center'
  },
  {
    title: '小计',
    slotName: 'subtotal',
    width: 120,
    align: 'center'
  }
]

// 获取表格数据
const getProductTableData = () => {
  if (!props.orderData.products || props.orderData.products.length === 0) {
    return []
  }

  return props.orderData.products.map(product => ({
    id: product.id,
    productImage: product.productImage,
    productCategory: getProductCategory(product.productName),
    productName: product.productName,
    sku: product.sku,
    specification: product.specification,
    productCode: product.productCode,
    expectedCost: product.expectedCost || product.unitPrice,
    actualSupplier: product.actualSupplier,
    suggestedSupplier: product.suggestedSupplier,
    purchasePrice: product.purchasePrice || product.unitPrice,
    contractPrice: product.contractPrice || product.unitPrice,
    purchaseCode: product.purchaseCode || product.productCode,
    unitPrice: product.unitPrice,
    unit: product.unit || '台',
    quantity: product.quantity,
    remark: product.remark,
    actualCost: product.actualCost,
    totalAmount: product.totalAmount
  }))
}

// 提取商品大类
const getProductCategory = (productName) => {
  if (!productName) return ''

  // 根据商品名称提取大类，这里可以根据实际业务逻辑调整
  if (productName.includes('服务器') || productName.includes('工作站')) {
    return '服务器/工作站'
  } else if (productName.includes('交换') || productName.includes('路由')) {
    return '交换设备'
  } else if (productName.includes('iPhone') || productName.includes('手机')) {
    return '移动设备'
  }

  return '其他设备'
}

// 计算支付金额
const calculatePaymentAmount = () => {
  const totalAmount = parseFloat(props.orderData.totalAmount || 0)
  const freight = parseFloat(props.orderData.freight || 0)
  const discountAmount = parseFloat(props.orderData.discountAmount || 0)

  const paymentAmount = totalAmount + freight - discountAmount
  return paymentAmount.toFixed(2)
}

// 计算商品总额
const calculateProductsTotal = () => {
  if (!props.orderData.products || props.orderData.products.length === 0) {
    return '0.00'
  }

  const total = props.orderData.products.reduce((sum, product) => {
    return sum + parseFloat(product.totalAmount || 0)
  }, 0)

  return total.toFixed(2)
}

// 查看附件
const viewAttachment = (attachment) => {
  if (attachment.url) {
    window.open(attachment.url, '_blank')
  } else {
    console.log('查看附件:', attachment.name)
    // 这里可以添加具体的附件查看逻辑
  }
}

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.detail-card {
  margin-bottom: 16px;
  background: #ffffff;
  height: 100%;
  overflow: visible;
}

/* 表格容器样式 */
.products-table-container {
  margin-bottom: 24px;
}

.products-table {
  background: #ffffff;
}

/* 商品信息单元格样式 */
.product-info-cell {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
}

.product-image-wrapper {
  flex-shrink: 0;
}

.product-thumbnail {
  border-radius: 6px;
  border: 1px solid #e5e6eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-name-primary {
  font-weight: 600;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.product-name-secondary {
  font-size: 13px;
  color: #666;
  margin-bottom: 6px;
  line-height: 1.4;
  word-break: break-word;
}

.product-specs {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.spec-item {
  font-size: 12px;
  color: #86909c;
  line-height: 1.3;
}

/* 表格数值样式 */
.cost-value {
  color: #86909c;
  font-weight: 500;
  font-size: 13px;
}

.supplier-name {
  color: #333;
  font-size: 13px;
  word-break: break-word;
}

.price-value {
  color: #f53f3f;
  font-weight: 600;
  font-size: 14px;
}

.quantity-value {
  color: #165dff;
  font-weight: 500;
  font-size: 13px;
}

.code-value {
  color: #333;
  font-size: 12px;
  font-family: monospace;
}

.remark-text {
  color: #86909c;
  font-size: 12px;
}

.subtotal-value {
  color: #f53f3f;
  font-weight: 600;
  font-size: 14px;
}

/* 业务信息区域样式 */
.business-info-section {
  margin-top: 24px;
  padding: 20px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
}

/* 备注区域样式 */
.remarks-section {
  padding-right: 16px;
}

.remark-block {
  margin-bottom: 16px;
}

.remark-block:last-child {
  margin-bottom: 12px;
}

.remark-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  margin-bottom: 6px;
}

.remark-content {
  color: #666;
  font-size: 13px;
  line-height: 1.5;
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
  word-break: break-word;
}

/* 联系人信息样式 */
.contact-info {
  display: flex;
  gap: 24px;
  margin-top: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
}

.contact-label {
  font-weight: 500;
  color: #333;
  font-size: 13px;
  margin-right: 8px;
}

.contact-value {
  color: #666;
  font-size: 13px;
}

/* 收货地址信息样式 */
.address-info {
  margin-top: 12px;
}

.address-item {
  display: flex;
  align-items: flex-start;
}

.address-label {
  font-weight: 500;
  color: #333;
  font-size: 13px;
  margin-right: 8px;
  flex-shrink: 0;
}

.address-value {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  word-break: break-word;
}

/* 附件信息样式 */
.attachment-info {
  margin-top: 16px;
}

.attachment-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  margin-bottom: 8px;
}

.attachment-list {
  padding: 8px 12px;
  background: #ffffff;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
}

.attachment-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.attachment-item:hover {
  background-color: #f2f3f5;
}

.attachment-icon {
  color: #165dff;
  font-size: 14px;
  flex-shrink: 0;
}

.attachment-name {
  color: #333;
  font-size: 13px;
  flex: 1;
  word-break: break-word;
}

.attachment-size {
  color: #86909c;
  font-size: 12px;
  flex-shrink: 0;
}

.no-attachment {
  color: #86909c;
  font-size: 13px;
  text-align: center;
  padding: 8px 0;
}

/* 单位列样式 */
.unit-value {
  color: #333;
  font-size: 13px;
  font-weight: 500;
}

/* 支付区域样式 */
.payment-section {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.amount-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f2f3f5;
}

.amount-item:last-child {
  border-bottom: none;
}

.amount-item.payment-total {
  margin-top: 8px;
  padding-top: 12px;
  border-top: 2px solid #e5e6eb;
  border-bottom: none;
}

.amount-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.amount-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.amount-value.total {
  font-size: 16px;
  color: #f53f3f;
  font-weight: 700;
}

/* 通用样式 */
.mr-1 {
  margin-right: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
}

/* 表格样式优化 */
:deep(.arco-table-th) {
  background-color: #f7f8fa;
  color: #333;
  font-weight: 600;
  font-size: 13px;
  border-bottom: 2px solid #e5e6eb;
}

:deep(.arco-table-td) {
  padding: 12px 8px;
  border-bottom: 1px solid #f2f3f5;
  vertical-align: top;
}

:deep(.arco-table-tbody .arco-table-tr:hover .arco-table-td) {
  background-color: #f8f9fa;
}

:deep(.arco-table) {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .business-info-section .arco-row {
    flex-direction: column;
  }

  .business-info-section .arco-col {
    width: 100% !important;
    margin-bottom: 16px;
  }

  .contact-info {
    flex-direction: column;
    gap: 8px;
  }

  .products-table-container {
    overflow-x: auto;
  }
}

/* 优化卡片内边距 */
:deep(.arco-card-body) {
  padding: 20px;
}
</style>
