<template>
  <div class="supply-basic-info">
    <a-layout class="layout-container">
      <!-- 左侧菜单栏 -->
      <a-layout-sider
        :width="240"
        class="sidebar"
        :style="{
          background: 'var(--color-bg-2)',
          borderRight: '1px solid var(--color-border-2)'
        }"
      >
        <div class="sidebar-header">
          <h3>供应商基本信息</h3>
        </div>
        <a-menu
          :selected-keys="[activeMenu]"
          @menu-item-click="handleMenuClick"
          :style="{ background: 'transparent', border: 'none' }"
        >
          <a-menu-item key="mainProducts">
            主营产品
          </a-menu-item>
          <a-menu-item key="enterpriseNature">
            企业性质
          </a-menu-item>
          <a-menu-item key="paymentTerms">
            付款条件
          </a-menu-item>
          <a-menu-item key="cooperationAgreement">
            合作协议
          </a-menu-item>
        </a-menu>
      </a-layout-sider>

      <!-- 右侧内容区域 -->
      <a-layout-content class="content-area">
        <div class="content-wrapper">
          <!-- 动态加载对应的组件 -->
          <component :is="currentComponent" />
        </div>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { defineAsyncComponent } from "vue";

// 定义页面路由元信息
definePageMeta({
  name: "basicInfo",
  path: "/master/supply/basicInfo",
});

// 当前激活的菜单项
const activeMenu = ref("mainProducts");

// 异步加载组件
const MainProducts = defineAsyncComponent(() => import('./components/mainProducts/index.vue'));
const EnterpriseNature = defineAsyncComponent(() => import('./components/enterpriseNature/index.vue'));
const PaymentTerms = defineAsyncComponent(() => import('./components/paymentTerms/index.vue'));
const CooperationAgreement = defineAsyncComponent(() => import('./components/cooperationAgreement/index.vue'));

// 组件映射
const componentMap = {
  mainProducts: MainProducts,
  enterpriseNature: EnterpriseNature,
  paymentTerms: PaymentTerms,
  cooperationAgreement: CooperationAgreement,
};

// 当前显示的组件
const currentComponent = computed(() => {
  return componentMap[activeMenu.value] || MainProducts;
});

// 处理菜单点击
const handleMenuClick = (key) => {
  activeMenu.value = key;
  console.log('切换到菜单:', key);
};

// 生命周期钩子
onMounted(() => {
  console.log('供应商基本信息管理页面已加载');
});
</script>

<style scoped>
.supply-basic-info {
  height: 100vh;
  background-color: var(--color-bg-1);
}

.layout-container {
  height: 100%;
}

.sidebar {
  height: 100vh;
  overflow-y: auto;
}

.sidebar-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border-2);
  background-color: var(--color-bg-1);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-1);
}

.content-area {
  background-color: var(--color-bg-1);
  overflow: hidden;
}

.content-wrapper {
  height: 100%;
  padding: 20px;
  overflow-y: auto;
  background-color: var(--color-bg-2);
  border-radius: 8px;
  margin: 16px;
}

/* 菜单样式调整 */
:deep(.arco-menu-item) {
  margin: 4px 8px;
  border-radius: 6px;
  color: var(--color-text-2);
}

:deep(.arco-menu-item:hover) {
  background-color: var(--color-fill-2);
  color: var(--color-text-1);
}

:deep(.arco-menu-item.arco-menu-selected) {
  background-color: var(--color-primary-light-1);
  color: var(--color-primary-6);
  font-weight: 500;
}

:deep(.arco-menu-item.arco-menu-selected::before) {
  display: none;
}

:deep(.arco-menu-icon) {
  margin-right: 8px;
}
</style>