<template>
  <div class="payment-terms">
    <ma-crud :options="crudOptions" :columns="columns" ref="crudRef">
      <!-- 更新时间 -->
      <template #updated_at="{ record }">
        <div v-if="record.updated_at" v-time="record.updated_at"></div>
        <div v-else>-</div>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";

const crudRef = ref();

// 模拟付款条件数据
const mockPaymentTerms = [
  {
    id: 1,
    term_name: "预付款30%，发货前70%",
    submitter: "张三",
    submit_date: "2024-01-15",
    remark: "标准预付款条件",
    updated_at: "2024-01-20 14:20:00",
    updater: "李四"
  },
  {
    id: 2,
    term_name: "货到付款",
    submitter: "李四",
    submit_date: "2024-01-10",
    remark: "货到付款条件",
    updated_at: "2024-01-18 16:45:00",
    updater: "王五"
  },
  {
    id: 3,
    term_name: "月结30天",
    submitter: "王五",
    submit_date: "2024-01-05",
    remark: "月结付款条件",
    updated_at: "2024-01-15 13:30:00",
    updater: "张三"
  },
  {
    id: 4,
    term_name: "信用证付款",
    submitter: "赵六",
    submit_date: "2023-12-20",
    remark: "国际贸易信用证付款",
    updated_at: "2024-01-12 10:15:00",
    updater: "李四"
  },
  {
    id: 5,
    term_name: "承兑汇票90天",
    submitter: "孙七",
    submit_date: "2024-01-08",
    remark: "承兑汇票付款条件",
    updated_at: "2024-01-22 17:20:00",
    updater: "张三"
  }
];

// 模拟API函数
const mockApi = {
  // 获取列表
  getList: async (params = {}) => {
    console.log('付款条件搜索参数:', params);
    
    // 模拟延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    let filteredData = [...mockPaymentTerms];
    
    // 模拟搜索过滤
    if (params.term_name) {
      filteredData = filteredData.filter(item =>
        item.term_name.includes(params.term_name)
      );
    }

    if (params.submitter) {
      filteredData = filteredData.filter(item =>
        item.submitter.includes(params.submitter)
      );
    }
    
    // 模拟分页
    const page = params.page || 1;
    const pageSize = params.pageSize || 10;
    const start = (page - 1) * pageSize;
    const end = start + pageSize;
    const paginatedData = filteredData.slice(start, end);
    
    return {
      code: 200,
      message: '获取成功',
      data: {
        items: paginatedData,
        total: filteredData.length
      }
    };
  },
  
  // 创建
  create: async (data) => {
    console.log('创建付款条件:', data);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const newTerm = {
      ...data,
      id: Date.now(),
      created_at: new Date().toISOString().slice(0, 19).replace('T', ' '),
      updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
    };
    
    mockPaymentTerms.unshift(newTerm);
    
    return {
      code: 200,
      message: '创建成功',
      data: newTerm
    };
  },
  
  // 更新
  update: async (id, data) => {
    console.log('更新付款条件:', id, data);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const index = mockPaymentTerms.findIndex(item => item.id === id);
    if (index !== -1) {
      mockPaymentTerms[index] = {
        ...mockPaymentTerms[index],
        ...data,
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
      };
      
      return {
        code: 200,
        message: '更新成功',
        data: mockPaymentTerms[index]
      };
    }
    
    return {
      code: 404,
      message: '付款条件不存在'
    };
  },
  
  // 删除
  delete: async (id) => {
    console.log('删除付款条件:', id);
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const index = mockPaymentTerms.findIndex(item => item.id === id);
    if (index !== -1) {
      mockPaymentTerms.splice(index, 1);
      return {
        code: 200,
        message: '删除成功'
      };
    }
    
    return {
      code: 404,
      message: '付款条件不存在'
    };
  }
};



// ma-crud 配置项
const crudOptions = reactive({
  api: mockApi.getList,
  title: "付款条件管理",
  searchLabelWidth: "100px",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  actionColumn: {
    width: 180,
  },
  formOption: {
    width: 900,
    viewType: 'modal'
  },
  table: {
    rowKey: "id",
    border: true,
    size: "medium",
    pagination: {
      showTotal: true,
      showJumper: true,
      showPageSize: true,
    },
  },
  search: {
    collapsed: false,
    showCollapse: true,
    collapsePosition: "left",
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    return params;
  },
  // 添加前处理参数
  beforeAdd: (params) => {
    return params;
  },
  // 编辑前处理参数
  beforeEdit: (params) => {
    return params;
  },
  add: {
    show: true,
    api: mockApi.create,
    text: "新增付款条件"
  },
  edit: {
    show: true,
    api: mockApi.update,
  },
  delete: {
    show: true,
    api: mockApi.delete,
  },
});

// 表格列配置
const columns = reactive([
  {
    title: "序号",
    dataIndex: "id",
    width: 80,
    align: "center",
    search: false,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: "付款条件名称",
    dataIndex: "term_name",
    width: 200,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '付款条件名称必填' }],
  },
  {
    title: "提交人",
    dataIndex: "submitter",
    width: 120,
    search: true,
    formType: "input",
    commonRules: [{ required: true, message: '提交人必填' }],
  },
  {
    title: "提交日期",
    dataIndex: "submit_date",
    width: 120,
    search: false,
    formType: "date",
    commonRules: [{ required: true, message: '提交日期必填' }],
  },
  {
    title: "备注",
    dataIndex: "remark",
    width: 250,
    search: false,
    formType: "textarea",
    rows: 3,
  },
  {
    title: "更新时间",
    dataIndex: "updated_at",
    width: 160,
    search: false,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: "更新人",
    dataIndex: "updater",
    width: 120,
    search: false,
    formType: "input",
    commonRules: [{ required: true, message: '更新人必填' }],
  },
]);

// 生命周期钩子
onMounted(() => {
  console.log('付款条件管理页面已加载');
});
</script>

<style scoped>
.payment-terms {
  height: 100%;
}
</style>
