<template>
  <a-modal
    v-model:visible="visible"
    title="下载模版"
    width="600px"
    :footer="false"
    @cancel="handleCancel"
  >
    <div class="template-content">
      <!-- 商品模版列表 -->
      <div class="products-templates">
        <div
          v-for="(product, index) in productList"
          :key="product.id || index"
          class="product-template-item"
          :class="{ 'has-divider': index < productList.length - 1 }"
        >
          <div class="product-template-content">
            <div class="template-field">
              <span class="field-label">品牌：</span>
            </div>
            <div class="template-field">
              <span class="field-label">名称：</span>
              <span class="field-value">{{ product.productName || '' }}</span>
            </div>
            <div class="template-field">
              <span class="field-label">型号：</span>
              <span class="field-value">{{ product.model || product.specification || '' }}</span>
            </div>
            <div class="template-field">
              <span class="field-label">数量：</span>
              <span class="field-value">{{ product.quantity || 1 }}</span>
            </div>
            <div class="template-field">
              <span class="field-label">单位：</span>
              <span class="field-value">{{ product.unit || '台' }}</span>
            </div>
            <div class="template-field">
              <span class="field-label">地址：</span>
              <span class="field-value">{{ getDeliveryAddress() }}</span>
            </div>
            <div class="template-field">
              <span class="field-label">收货人及电话：</span>
              <span class="field-value">{{ getRecipientInfo() }}</span>
            </div>
            <div class="template-field special-note">
              <span class="field-label">注意：</span>
              <span class="field-value warning-text">必须按照要求贴标货物，品牌标签***温免发生退货</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部说明 -->
      <div class="template-footer">
        <div class="footer-note">
          订单中每一条商品信息对应一条模版
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(props.modelValue)

// 商品列表
const productList = computed(() => {
  if (!props.orderData) return []

  // 如果订单有多个商品
  if (props.orderData.products && Array.isArray(props.orderData.products)) {
    return props.orderData.products.map(product => ({
      ...product,
      // 确保必要字段存在
      productName: product.productName || product.name || '',
      model: product.model || product.specification || product.sku || '',
      quantity: product.quantity || 1,
      unit: product.unit || '台'
    }))
  }

  // 兼容单商品数据结构
  if (props.orderData.productName) {
    return [{
      id: props.orderData.id,
      productName: props.orderData.productName,
      model: props.orderData.specification || props.orderData.sku || '',
      quantity: props.orderData.quantity || 1,
      unit: props.orderData.unit || '台'
    }]
  }

  return []
})

// 获取收货地址
const getDeliveryAddress = () => {
  if (!props.orderData) return ''

  // 尝试多种可能的地址字段
  return props.orderData.deliveryAddress ||
         props.orderData.address ||
         props.orderData.shippingAddress ||
         '广东广州市海珠区素社街道桥东街桥东街/小区8栋14号--9171'
}

// 获取收货人信息
const getRecipientInfo = () => {
  if (!props.orderData) return ''

  const recipient = props.orderData.recipientName || props.orderData.receiver || '林小姐'
  const phone = props.orderData.contactPhone || props.orderData.phone || '18466125201-9171'

  return `${recipient}${phone}`
}

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.template-content {
  padding: 16px 0;
}

.products-templates {
  display: flex;
  flex-direction: column;
}

.product-template-item {
  background: white;
  padding: 16px 0;
}

.product-template-item.has-divider {
  border-bottom: 1px solid #e5e6eb;
  margin-bottom: 16px;
  padding-bottom: 16px;
}

.product-template-content {
  /* 移除内边距，由父元素控制 */
}

.template-field {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  line-height: 1.5;
  font-size: 14px;
}

.template-field:last-child {
  margin-bottom: 0;
}

.field-label {
  color: #333;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
}

.field-value {
  color: #333;
  flex: 1;
  word-break: break-word;
}

.special-note {
  margin-top: 12px;
  padding-top: 8px;
}

.warning-text {
  color: #f53f3f;
  font-weight: 500;
}

.template-footer {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e6eb;
}

.footer-note {
  text-align: center;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}
</style>
