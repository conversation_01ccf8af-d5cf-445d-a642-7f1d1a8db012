<template>
  <a-modal
    v-model:visible="visible"
    title="新增费用单"
    width="800px"
    :footer="false"
    @cancel="handleCancel"
  >
    <div class="expense-order-content">
      <a-form :model="formData" layout="vertical" ref="formRef">
        <!-- 第一行：费用类型和费用金额 -->
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item
              field="expenseType"
              :rules="[{ required: true, message: '请选择费用类型' }]"
            >
              <template #label>
                <span class="required-mark">*</span>
                <span>费用类型</span>
              </template>
              <a-select v-model="formData.expenseType" placeholder="请选择费用类型">
                <a-option value="freight">运费</a-option>
                <a-option value="packaging">包装费</a-option>
                <a-option value="installation">安装费</a-option>
                <a-option value="service">服务费</a-option>
                <a-option value="tax">税费</a-option>
                <a-option value="other">其他</a-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              field="expenseAmount"
              :rules="[
                { required: true, message: '请输入费用金额' },
                {
                  validator: (value, callback) => {
                    if (value && (isNaN(Number(value)) || Number(value) <= 0)) {
                      callback('请输入有效的费用金额')
                    } else {
                      callback()
                    }
                  }
                }
              ]"
            >
              <template #label>
                <span class="required-mark">*</span>
                <span>费用金额</span>
              </template>
              <a-input
                v-model="formData.expenseAmount"
                placeholder="请输入费用金额"
                allow-clear
              >
                <template #prefix>¥</template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 第二行：采购总价 -->
        <a-row>
          <a-col :span="24">
            <div class="purchase-total-section">
              <div class="purchase-total-display">
                采购总价
              </div>
            </div>
          </a-col>
        </a-row>

        <!-- 第三行：备注 -->
        <a-row>
          <a-col :span="24">
            <a-form-item field="remark">
              <template #label>
                <span>备注</span>
              </template>
              <a-textarea
                v-model="formData.remark"
                placeholder="请输入备注"
                :rows="4"
                :max-length="500"
                show-word-limit
              />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 商品列表 -->
        <div class="products-section">
          <div class="section-title">
            <span class="required-mark">*</span>
            <span>请选择商品</span>
          </div>

          <div class="products-table">
            <div class="table-header">
              <div class="header-cell checkbox-cell"></div>
              <div class="header-cell name-cell">商品名称</div>
              <div class="header-cell quantity-cell">数量</div>
              <div class="header-cell purchase-price-cell">采购合价</div>
              <div class="header-cell sale-price-cell">销售合价</div>
            </div>

            <div class="table-body">
              <div
                v-for="(product, index) in productList"
                :key="product.id || index"
                class="table-row"
              >
                <div class="body-cell checkbox-cell">
                  <a-checkbox
                    v-model="product.selected"
                    @change="handleProductSelect(product, index)"
                  />
                </div>
                <div class="body-cell name-cell">
                  <div class="product-name" :title="product.productName">
                    {{ product.productName }}
                  </div>
                </div>
                <div class="body-cell quantity-cell">
                  {{ product.quantity }}
                </div>
                <div class="body-cell purchase-price-cell">
                  {{ product.purchasePrice }}
                </div>
                <div class="body-cell sale-price-cell">
                  {{ product.salePrice }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </a-form>

      <!-- 底部按钮 -->
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit" :loading="loading">
          确定
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const formRef = ref()
const loading = ref(false)

// 表单数据
const formData = reactive({
  expenseType: '',
  expenseAmount: '',
  remark: ''
})

// 商品列表
const productList = ref([])

// 监听visible变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetForm()
    initProductList()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 初始化商品列表
const initProductList = () => {
  if (props.orderData && props.orderData.products) {
    productList.value = props.orderData.products.map(product => ({
      ...product,
      selected: false,
      purchasePrice: product.unitPrice || '120',
      salePrice: product.totalAmount || '119.24'
    }))
  } else if (props.orderData && props.orderData.productName) {
    // 兼容单商品数据结构
    productList.value = [{
      id: props.orderData.id,
      productName: props.orderData.productName,
      quantity: props.orderData.quantity || 2,
      purchasePrice: props.orderData.unitPrice || '120',
      salePrice: props.orderData.totalAmount || '119.24',
      selected: false
    }]
  } else {
    // 默认示例数据
    productList.value = [{
      id: 1,
      productName: 'Astree无水洗车液免水冲洗车液白车专用力内饰清...',
      quantity: 2,
      purchasePrice: '120',
      salePrice: '119.24',
      selected: false
    }]
  }
}

// 处理商品选择
const handleProductSelect = (product, index) => {
  // 商品选择逻辑
}

// 计算采购总价
const calculatePurchaseTotal = () => {
  const selectedProducts = productList.value.filter(p => p.selected)
  const total = selectedProducts.reduce((sum, product) => {
    return sum + (parseFloat(product.purchasePrice) || 0)
  }, 0)
  return total.toFixed(2)
}

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      return
    }

    const selectedProducts = productList.value.filter(p => p.selected)
    if (selectedProducts.length === 0) {
      Message.warning('请至少选择一个商品')
      return
    }

    loading.value = true

    const expenseData = {
      orderId: props.orderData?.id,
      orderNumber: props.orderData?.orderNumber,
      expenseType: formData.expenseType,
      expenseAmount: formData.expenseAmount,
      remark: formData.remark,
      selectedProducts,
      purchaseTotal: calculatePurchaseTotal(),
      createdAt: new Date().toISOString(),
      createdBy: '当前用户'
    }

    emit('submit', expenseData)
    Message.success('费用单创建成功')
    visible.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  formData.expenseType = ''
  formData.expenseAmount = ''
  formData.remark = ''
  productList.value.forEach(product => {
    product.selected = false
  })
  formRef.value?.resetFields()
}
</script>

<style scoped>
.expense-order-content {
  padding: 16px 0;
}

.required-mark {
  color: #f53f3f;
  margin-right: 4px;
}

.purchase-total-section {
  margin: 16px 0;
}

.purchase-total-display {
  font-size: 14px;
  color: #333;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
}

.products-section {
  margin-top: 24px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.products-table {
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e5e6eb;
}

.table-body {
  background: white;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.header-cell,
.body-cell {
  padding: 12px 8px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e5e6eb;
}

.header-cell:last-child,
.body-cell:last-child {
  border-right: none;
}

.checkbox-cell {
  width: 60px;
  justify-content: center;
}

.name-cell {
  flex: 1;
  min-width: 200px;
}

.quantity-cell {
  width: 80px;
  justify-content: center;
}

.purchase-price-cell {
  width: 100px;
  justify-content: center;
}

.sale-price-cell {
  width: 100px;
  justify-content: center;
}

.header-cell {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.body-cell {
  font-size: 13px;
  color: #666;
}

.product-name {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #e5e6eb;
  margin-top: 20px;
}
</style>
