<template>
  <a-modal 
    v-model:visible="visible" 
    title="更换采购员" 
    width="500px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form :model="formData" layout="vertical" ref="formRef">
      <a-form-item label="当前采购员">
        <a-input :value="orderData?.purchaser" disabled />
      </a-form-item>
      
      <a-form-item 
        label="新采购员" 
        field="newPurchaser"
        :rules="[{ required: true, message: '请选择新采购员' }]"
      >
        <a-select 
          v-model="formData.newPurchaser" 
          placeholder="请选择新采购员"
          allow-search
        >
          <a-option value="张三">张三</a-option>
          <a-option value="李四">李四</a-option>
          <a-option value="王五">王五</a-option>
          <a-option value="赵六">赵六</a-option>
          <a-option value="钱七">钱七</a-option>
          <a-option value="孙八">孙八</a-option>
        </a-select>
      </a-form-item>
      
      <a-form-item 
        label="更换原因" 
        field="changeReason"
        :rules="[{ required: true, message: '请输入更换原因' }]"
      >
        <a-textarea 
          v-model="formData.changeReason" 
          placeholder="请输入更换原因" 
          :rows="4"
        />
      </a-form-item>

      <a-alert 
        type="warning"
        message="更换提醒"
        title="注：一旦更换，原来的采购员无法在看到这条订单，新的采购员才能看到"
        show-icon
        class="mb-4"
      />
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const formRef = ref()

const formData = reactive({
  newPurchaser: '',
  changeReason: ''
})

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 重置表单
    resetForm()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      return
    }
    
    if (formData.newPurchaser === props.orderData?.purchaser) {
      Message.error('新采购员不能与当前采购员相同')
      return
    }
    
    const changeData = {
      orderId: props.orderData?.id,
      orderNumber: props.orderData?.orderNumber,
      oldPurchaser: props.orderData?.purchaser,
      newPurchaser: formData.newPurchaser,
      changeReason: formData.changeReason,
      changedAt: new Date().toISOString()
    }
    
    emit('submit', changeData)
    Message.success('采购员更换成功')
    visible.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  formData.newPurchaser = ''
  formData.changeReason = ''
  formRef.value?.resetFields()
}
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}
</style>
