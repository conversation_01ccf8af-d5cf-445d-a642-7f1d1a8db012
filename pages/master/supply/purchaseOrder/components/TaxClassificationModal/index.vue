<template>
  <a-modal
    v-model:visible="visible"
    title="税收分类选择"
    :width="800"
    :footer="false"
    unmount-on-close
  >
    <div class="tax-classification-selector">
      <!-- 搜索区域 -->
      <div class="search-section">
        <a-input-search
          v-model="searchKeyword"
          placeholder="请输入税收分类名称或编码"
          search-button
          @search="handleSearch"
          @press-enter="handleSearch"
          @clear="handleClearSearch"
          allow-clear
          class="search-input"
        />
      </div>

      <div class="flex">
        <!-- 左侧分类树 -->
        <div class="w-1/2 pr-4 border-r border-gray-200">
          <div class="tree-container max-h-96 overflow-y-auto">
            <a-tree
              :data="filteredTreeData"
              :expanded-keys="expandedKeys"
              :field-names="{
                key: 'codeid',
                title: 'label',
                children: 'children'
              }"
              @select="handleSelect"
              @expand="handleExpand"
              :show-line="true"
              :auto-expand-parent="true"
            />
          </div>
        </div>
        
        <!-- 右侧选择详情 -->
        <div class="w-1/2 pl-4">
          <div v-if="selectedNode" class="selected-info">
            <h3 class="text-lg font-medium mb-4">已选择分类</h3>
            
            <div class="info-item mb-2">
              <span class="text-gray-500">分类名称：</span>
              <span class="font-medium">{{ selectedNode.label }}</span>
            </div>
            
            <div class="info-item mb-2">
              <span class="text-gray-500">分类编码：</span>
              <span class="font-medium">{{ selectedNode.codeid }}</span>
            </div>
            
            <div class="info-item mb-2">
              <span class="text-gray-500">完整编码：</span>
              <span class="font-medium">{{ selectedNode.num }}</span>
            </div>
            
            <div class="info-item mb-4">
              <span class="text-gray-500">上级分类：</span>
              <span class="font-medium">{{ parentNodeName }}</span>
            </div>
            
            <div class="path-info p-3 bg-gray-50 rounded mb-4">
              <div class="text-gray-500 mb-1">分类路径：</div>
              <div class="text-sm">
                <template v-for="(item, index) in nodePath" :key="index">
                  <span v-if="index > 0" class="mx-1 text-gray-400">/</span>
                  <span>{{ item.label }}</span>
                </template>
              </div>
            </div>
          </div>
          
          <div v-else class="flex items-center justify-center h-64 text-gray-400">
            请从左侧选择一个税收分类
          </div>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="flex justify-end mt-6 space-x-4">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :disabled="!selectedNode" @click="handleConfirm">
          确认选择
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import taxData from '@/mock/tax.js'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  productData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 响应式数据
const visible = ref(props.visible)
const selectedNode = ref(null)
const nodePath = ref([])
const searchKeyword = ref('')
const expandedKeys = ref(['1']) // 展开的节点

// 默认展开的节点
const defaultExpandedKeys = ref(['1']) // 默认展开"货物"节点

// 构建树形数据
const treeData = computed(() => {
  return taxData?.slide_data || []
})

// 过滤后的树形数据
const filteredTreeData = computed(() => {
  if (!searchKeyword.value) {
    return treeData.value
  }

  const keyword = searchKeyword.value.toLowerCase()

  // 递归过滤树形数据
  const filterTree = (nodes) => {
    const filtered = []

    for (const node of nodes) {
      const matchesKeyword =
        node.label.toLowerCase().includes(keyword) ||
        node.codeid.toLowerCase().includes(keyword) ||
        node.num.toLowerCase().includes(keyword)

      let filteredChildren = []
      if (node.children && node.children.length > 0) {
        filteredChildren = filterTree(node.children)
      }

      // 如果节点本身匹配或有匹配的子节点，则包含此节点
      if (matchesKeyword || filteredChildren.length > 0) {
        filtered.push({
          ...node,
          children: filteredChildren
        })
      }
    }

    return filtered
  }

  return filterTree(treeData.value)
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
  if (!newVal) {
    resetModal()
  }
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置弹窗状态
const resetModal = () => {
  selectedNode.value = null
  nodePath.value = []
  searchKeyword.value = ''
  expandedKeys.value = ['1']
}

// 获取父节点名称
const parentNodeName = computed(() => {
  if (!selectedNode.value || !selectedNode.value.codepid || selectedNode.value.codepid === '0') {
    return '无'
  }
  
  // 查找父节点
  const findParent = (nodes, pid) => {
    for (const node of nodes) {
      if (node.codeid === pid) {
        return node
      }
      
      if (node.children && node.children.length) {
        const found = findParent(node.children, pid)
        if (found) return found
      }
    }
    
    return null
  }
  
  const parent = findParent(treeData.value, selectedNode.value.codepid)
  return parent ? parent.label : '无'
})

// 处理选择节点
const handleSelect = (selectedKeys, { selectedNodes }) => {
  if (selectedKeys.length === 0 || !selectedNodes || selectedNodes.length === 0) {
    selectedNode.value = null
    nodePath.value = []
    return
  }
  
  selectedNode.value = selectedNodes[0]
  
  // 计算节点路径
  const path = []
  const findPath = (nodes, targetId, currentPath = []) => {
    for (const node of nodes) {
      const newPath = [...currentPath, node]
      
      if (node.codeid === targetId) {
        return newPath
      }
      
      if (node.children && node.children.length) {
        const found = findPath(node.children, targetId, newPath)
        if (found) return found
      }
    }
    
    return null
  }
  
  nodePath.value = findPath(treeData.value, selectedNode.value.codeid) || []
}

// 确认选择
const handleConfirm = () => {
  if (selectedNode.value) {
    // 剔除 children 属性
    const { children, ...nodeWithoutChildren } = selectedNode.value
    
    emit('confirm', {
      taxClassification: {
        ...nodeWithoutChildren,
        path: nodePath.value.map(item => item.label).join(' / ')
      },
      productData: props.productData
    })
  }
  
  visible.value = false
}

// 搜索处理
const handleSearch = () => {
  if (searchKeyword.value) {
    // 展开所有匹配的节点
    const expandAll = (nodes, expanded = []) => {
      for (const node of nodes) {
        expanded.push(node.codeid)
        if (node.children && node.children.length > 0) {
          expandAll(node.children, expanded)
        }
      }
      return expanded
    }

    expandedKeys.value = expandAll(filteredTreeData.value)
  } else {
    expandedKeys.value = ['1']
  }
}

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = ''
  expandedKeys.value = ['1']
}

// 处理节点展开
const handleExpand = (keys) => {
  expandedKeys.value = keys
}

// 取消选择
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.tax-classification-selector {
  max-height: 70vh;
  overflow-y: auto;
}

.search-section {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
}

.tree-container :deep(.arco-tree-node-title) {
  white-space: normal;
  word-break: break-all;
}

.info-item {
  display: flex;
}

.info-item .text-gray-500 {
  width: 80px;
  flex-shrink: 0;
}

.w-1\/2 {
  width: 50%;
}

.pr-4 {
  padding-right: 1rem;
}

.pl-4 {
  padding-left: 1rem;
}

.border-r {
  border-right-width: 1px;
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.max-h-96 {
  max-height: 24rem;
}

.overflow-y-auto {
  overflow-y: auto;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.font-medium {
  font-weight: 500;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.p-3 {
  padding: 0.75rem;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.rounded {
  border-radius: 0.25rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.h-64 {
  height: 16rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  margin-left: 1rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}
</style>
