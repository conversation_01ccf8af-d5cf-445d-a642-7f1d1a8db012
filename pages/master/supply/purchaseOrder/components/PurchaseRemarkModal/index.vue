<template>
  <a-modal 
    v-model:visible="visible" 
    title="采购备注" 
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form :model="formData" layout="vertical" ref="formRef">
      <a-form-item label="当前备注">
        <a-textarea 
          :value="orderData?.purchaseRemark || '暂无备注'" 
          disabled 
          :rows="3"
          class="disabled-textarea"
        />
      </a-form-item>
      
      <a-form-item 
        label="新增备注" 
        field="newRemark"
        :rules="[{ required: true, message: '请输入新的采购备注' }]"
      >
        <a-textarea 
          v-model="formData.newRemark" 
          placeholder="请输入新的采购备注" 
          :rows="4"
        />
      </a-form-item>

      <a-form-item label="备注类型" field="remarkType">
        <a-select v-model="formData.remarkType" placeholder="请选择备注类型">
          <a-option value="normal">普通备注</a-option>
          <a-option value="urgent">紧急备注</a-option>
          <a-option value="warning">警告备注</a-option>
          <a-option value="quality">质量备注</a-option>
          <a-option value="logistics">物流备注</a-option>
        </a-select>
      </a-form-item>

      <a-alert 
        type="warning" 
        message="备注提醒"
        title="新增的备注将追加到现有备注后面，不会覆盖原有内容。重要信息请选择相应的备注类型。"
        show-icon
        class="mb-4"
      />
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)
const formRef = ref()

const formData = reactive({
  newRemark: '',
  remarkType: 'normal'
})

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 重置表单
    resetForm()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      return
    }
    
    const remarkData = {
      orderId: props.orderData?.id,
      orderNumber: props.orderData?.orderNumber,
      oldRemark: props.orderData?.purchaseRemark || '',
      newRemark: formData.newRemark,
      remarkType: formData.remarkType,
      addedAt: new Date().toISOString(),
      addedBy: '当前用户' // 这里应该从用户状态中获取
    }
    
    emit('submit', remarkData)
    Message.success('采购备注更新成功')
    visible.value = false
    resetForm()
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleCancel = () => {
  visible.value = false
  resetForm()
}

const resetForm = () => {
  formData.newRemark = ''
  formData.remarkType = 'normal'
  formRef.value?.resetFields()
}
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.disabled-textarea {
  background-color: #f5f5f5;
}
</style>
