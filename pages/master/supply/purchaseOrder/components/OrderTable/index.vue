<template>
  <div class="order-table">
    <ma-crud
      ref="crudRef"
      v-model="crudBinding"
      :options="crudOptions"
      :columns="columns"
      :data="mockData"
      @request="handleRequest"
    >
      <!-- 订单信息插槽 -->
      <template #orderInfo="{ record }">
        <div class="order-info-card">
          <div class="info-row">
            <span class="label">订单编号：</span>
            <span class="order-number">{{ record.orderNumber }}</span>
          </div>
          <div class="info-row">
            <span class="label">下单账号：</span>
            <span class="order-account">{{ record.buyerAccount }}</span>
          </div>
          <div class="info-row">
            <span class="label">采购时间：</span>
            <span>{{ record.purchaseTime }}</span>
          </div>
          <div class="info-row">
            <span class="label">下单时间：</span>
            <span>{{ record.orderTime }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单来源：</span>
            <span>{{ record.orderSource }}</span>
          </div>
          <div class="info-row">
            <span class="label">店铺：</span>
            <span>{{ record.storeName }}</span>
          </div>
          <div class="info-row">
            <span class="label">跟单员：</span>
            <span>{{ record.follower }}</span>
          </div>
          <div class="info-row">
            <span class="label">采购员：</span>
            <span>{{ record.purchaser }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单类型：</span>
            <span>{{ record.orderType }}</span>
          </div>

          <!-- 产品线过期时间信息 -->
          <div class="info-row">
            <span class="label">产品线过期时间：</span>
            <span
              class="expire-time-display"
              :class="getProductLineExpireDisplayClass(record)"
            >
              {{ getFirstProductLineExpireTime(record) }}
            </span>
          </div>
        </div>
      </template>

      <!-- 商品信息插槽 -->
      <template #productInfo="{ record }">
        <div class="product-info-card">
          <!-- 多商品展示 -->
          <div v-if="record.products && record.products.length > 0" class="products-container">
            <div class="products-header">
              <span class="products-count">共{{ record.products.length }}种商品</span>
              <span class="total-quantity">{{ record.totalQuantity }}件</span>
            </div>

            <div class="products-list">
              <div
                v-for="(product, index) in record.products"
                :key="product.id"
                class="product-item"
                :class="{ 'has-border': index < record.products.length - 1 }"
              >
                <div class="product-image">
                  <a-image
                    :src="product.productImage"
                    width="50"
                    height="50"
                    fit="cover"
                    :preview="false"
                  />
                </div>
                <div class="product-details">
                  <div>
                    <div class="product-name">{{ product.productName }}</div>
                    <div class="product-sku">SKU: {{ product.sku }}</div>
                    <div class="product-code">编码: {{ product.productCode }}</div>
                    <div class="product-spec">{{ product.specification }}</div>
                    <div class="product-tax-category">
                      <span class="tax-label">税收分类：</span>
                      <a-button
                          type="text"
                          size="small"
                          class="tax-link"
                          @click="handleSelectTaxClassification(product, record)"
                      >
                        {{ product.taxClassification?.label || '点击选择' }}
                        <template #icon><icon-edit /></template>
                      </a-button>
                    </div>
                    <div class="product-price-qty">
                      <span class="price">单价:{{ product.unitPrice }}</span>
                      <span class="quantity">数量:{{ product.quantity }}</span>
                    </div>
                    <div class="product-supplier-info">
                      <div class="supplier-row">
                        <span class="supplier-label">实际供应商：</span>
                        <a-button
                            type="text"
                            size="mini"
                            class="supplier-link"
                            @click="handleSelectProductSupplier(product, record)"
                        >
                          {{ product.actualSupplier || '点击选择' }}
                          <template #icon><icon-edit /></template>
                        </a-button>
                      </div>
                      <div class="supplier-row">
                        <span class="supplier-label">建议供应商：</span>
                        <span class="supplier-text">{{ product.suggestedSupplier }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="product-cost-info">
                    <span class="cost-info-text" style="display: flex;flex-direction: column;">
                      <span>实际成本：{{ product.actualCost }}</span>
                      <span>巡检成本：{{ product.inspectionCost }}</span>
                      <span>毛利率：{{ product.profitRate }}</span>
                    </span>
                  </div>
                  <div style="display: flex;flex-direction: column;">
                    <div class="cost-item">
                      <span class="cost-label">成本总价：</span>
                      <div class="cost-input-wrapper">
                        <span class="currency-symbol">¥</span>
                        <a-input
                            v-model="record.costTotal"
                            size="small"
                            class="cost-input"
                            placeholder="请输入成本总价"
                            @blur="(e) => handleCostTotalChange(record, e.target.value)"
                            @press-enter="(e) => handleCostTotalChange(record, e.target.value)"
                            @input="(value) => handleCostTotalInput(record, value)"
                        />
                      </div>
                    </div>

                    <!-- 亏损信息 - 当成本总价大于巡检成本时显示 -->
                    <div v-if="isLossOrder(record)" class="loss-info">
                      <div class="cost-item loss-amount">
                        <span class="cost-label">当前亏损：</span>
                        <span class="cost-content loss-text">¥{{ calculateLossAmount(record) }}</span>
                      </div>
                      <div class="loss-reason-section">
                        <a-select
                            :model-value="record.lossReason"
                            placeholder="请选择亏损原因"
                            size="small"
                            class="loss-reason-select"
                            @change="(value) => handleLossReasonChange(record, value)"
                        >
                          <a-option value="freight_loss">运费亏损</a-option>
                          <a-option value="packaging_loss">特殊包装亏损</a-option>
                          <a-option value="installation_loss">安装费亏损</a-option>
                          <a-option value="tax_loss">税金亏损</a-option>
                          <a-option value="no_quote">未参与报价</a-option>
                          <a-option value="low_quantity">采购数量少</a-option>
                          <a-option value="quote_error">开发报价错误</a-option>
                          <a-option value="quick_removal">快上快下未及时下架</a-option>
                          <a-option value="discontinued">产品型号停产</a-option>
                          <a-option value="relationship_order">客情自然单</a-option>
                          <a-option value="other">其他</a-option>
                        </a-select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 兼容旧的单商品数据结构 -->
          <div v-else-if="record.productName" class="single-product">
            <div class="product-image">
              <a-image
                :src="record.productImage"
                width="60"
                height="60"
                fit="cover"
                :preview="false"
              />
            </div>
            <div class="product-details">
              <div class="product-name">{{ record.productName }}</div>
              <div class="product-sku">SKU: {{ record.sku }}</div>
              <div class="product-code">商品编码: {{ record.productCode }}</div>
              <div class="product-spec">规格型号: {{ record.specification }}</div>
              <div class="product-tax-category">
                <span class="tax-label">税收分类：</span>
                <a-button
                  type="text"
                  size="small"
                  class="tax-link"
                  @click="handleSelectTaxClassification(record, record)"
                >
                  {{ record.taxClassification?.label || '点击选择' }}
                  <template #icon><icon-edit /></template>
                </a-button>
              </div>
              <div class="product-cost-info">
                <span class="cost-info-text">
                  实：{{ record.actualCost }}/巡：{{ record.inspectionCost }} 毛利率：{{ record.profitRate }}
                </span>
              </div>
              <div class="product-cost-total">
                <span class="cost-total-label">成本总价：</span>
                <span class="cost-total-value">¥{{ record.costTotal }}</span>
              </div>
              <div class="product-supplier-info">
                <div class="supplier-row">
                  <span class="supplier-label">实际：</span>
                  <a-button
                    type="text"
                    size="mini"
                    class="supplier-link"
                    @click="handleSelectProductSupplier(record, record)"
                  >
                    {{ record.actualSupplier || '点击选择' }}
                    <template #icon><icon-edit /></template>
                  </a-button>
                </div>
                <div class="supplier-row">
                  <span class="supplier-label">建议：</span>
                  <span class="supplier-text">{{ record.suggestedSupplier }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>

      <!-- 采购成本信息插槽 -->
      <template #costInfo="{ record }">
        <div class="cost-info-card">
          <div class="cost-item">
            <span class="cost-label">商品总额：</span>
            <span class="cost-content">¥{{ calculateProductsTotal(record) }}</span>
          </div>
          <div class="cost-item">
            <span class="cost-label">运费：</span>
            <span class="cost-content">¥{{ record.freight }}</span>
          </div>
          <div class="cost-item">
            <span class="cost-label">实付金额：</span>
            <span class="cost-content total-amount">¥{{ record.totalAmount }}</span>
          </div>
          <div class="cost-item">
            <span class="cost-sub-label">共{{ record.totalQuantity }}件商品</span>
          </div>
          <div class="cost-item">
            <span class="cost-label">总毛利率：</span>
            <span class="cost-content profit" style="color: red;">{{ record.grossProfitRate }}</span>
          </div>

        </div>
      </template>

      <!-- 采购信息插槽 -->
      <template #purchaseInfo="{ record }">
        <div class="purchase-info-card">
          <div class="purchase-item">
            <span class="purchase-label">买家/收货人：</span>
            <span class="purchase-content">{{ record.buyerAccount }}</span>
          </div>
          <div class="purchase-item">
            <span class="purchase-label">实际收货地址：</span>
            <span>{{ record.deliveryAddress }}</span>
          </div>
          <div class="purchase-item">
            <span class="purchase-label">物流情况：</span>
            <span class="purchase-content">{{ record.logisticsInfo || '-' }}</span>
          </div>
          <div class="purchase-item">
            <span class="purchase-label">采购进度：</span>
            <div class="purchase-progress-wrapper">
              <a-select
                :model-value="record.purchaseProgress"
                size="small"
                class="purchase-progress-select"
                placeholder="请选择采购进度"
                @change="(value) => handlePurchaseProgressChange(record, value)"
              >
                <a-option value="待合同回传">待合同回传</a-option>
                <a-option value="待付款">待付款</a-option>
                <a-option value="待下单">待下单</a-option>
                <a-option value="无货/停产">无货/停产</a-option>
                <a-option value="待参数确定">待参数确定</a-option>
                <a-option value="货期">货期</a-option>
                <a-option value="待废止">待废止</a-option>
                <a-option value="待确认价格">待确认价格</a-option>
              </a-select>
              <!-- 货期时间输入框 - 当选择"货期"时显示 -->
              <div v-if="record.purchaseProgress === '货期'" class="delivery-time-wrapper">
                <a-input
                  :model-value="record.deliveryTime"
                  size="small"
                  class="delivery-time-input"
                  placeholder="请填写货期时间"
                  @blur="(e) => handleDeliveryTimeChange(record, e.target.value)"
                  @press-enter="(e) => handleDeliveryTimeChange(record, e.target.value)"
                />
              </div>
            </div>
          </div>
          <div class="purchase-item">
            <span class="purchase-label">物流单号：</span>
            <span class="purchase-content">{{ record.logisticsNumber || '-' }}</span>
          </div>
        </div>
      </template>

      <!-- 备注信息插槽 -->
      <template #remarkInfo="{ record }">
        <div class="remark-info-card">
          <div class="remark-item" v-if="record.businessRemark">
            <span class="remark-label">业务员备注：</span>
            <span class="remark-content">{{ record.businessRemark }}</span>
          </div>
          <div class="remark-item" v-if="record.purchaseRemark">
            <span class="remark-label">采购备注：</span>
            <span class="remark-content">{{ record.purchaseRemark }}</span>
          </div>
          <div class="remark-item" v-if="!record.businessRemark && !record.purchaseRemark">
            <span class="remark-content text-gray">暂无备注</span>
          </div>
        </div>
      </template>


      <!-- 状态信息插槽 -->
      <template #statusInfo="{ record }">
        <div class="status-info-card">
          <div class="status-item">
            <span class="status-label">订单状态：</span>
            <a-tag :color="getOrderStatusColor(record.orderStatus)">
              {{ record.orderStatus }}
            </a-tag>
          </div>
          <div class="status-item">
            <span class="status-label">审核状态：</span>
            <a-tag :color="getAuditStatusColor(record.auditStatus)">
              {{ record.auditStatus }}
            </a-tag>
          </div>
          <div class="status-item">
            <span class="status-label">ERP状态：</span>
            <a-tag :color="getErpStatusColor(record.erpStatus)">
              {{ record.erpStatus }}
            </a-tag>
          </div>
          <div class="status-item">
            <span class="status-label">拆分状态：</span>
            <a-tag :color="getSplitOrderStatusColor(record.splitOrderStatus)">
              {{ record.splitOrderStatus }}
            </a-tag>
          </div>
          <div class="status-item" v-if="record.cancelStatus">
            <span class="status-label">废止状态：</span>
            <a-tag :color="getCancelStatusColor(record.cancelStatus)">
              {{ record.cancelStatus }}
            </a-tag>
          </div>
          <div class="status-item" v-if="record.pendingCancelStatus">
            <span class="status-label">待废止状态：</span>
            <a-tag :color="getPendingCancelStatusColor(record.pendingCancelStatus)">
              {{ record.pendingCancelStatus }}
            </a-tag>
          </div>
        </div>
      </template>

      <!-- 操作列插槽 -->
      <template #operation="{ record }">
        <a-space direction="vertical" fill >
          <!-- 查看详情 - 始终显示 -->
          <a-button type="text" size="small" @click="handleViewDetail(record)">
            <template #icon><icon-eye /></template>
            查看详情
          </a-button>

          <!-- 当待废止状态为"采购员待审核"时，只显示废止操作和废止记录 -->
          <template v-if="record.pendingCancelStatus === '采购员待审核'">
            <a-button type="text" size="small" @click="handleAbolishOperation(record)">
              <template #icon><icon-close /></template>
              废止操作
            </a-button>
            <a-button type="text" size="small" @click="handleAbolishRecord(record)">
              <template #icon><icon-history /></template>
              废止记录
            </a-button>
          </template>

          <!-- 其他状态下显示所有操作按钮 -->
          <template v-else>
            <a-button type="text" size="small" @click="handleOrderTemplate(record)">
              <template #icon><icon-file-text /></template>
              下单模版
            </a-button>
            <a-button type="text" size="small" @click="handleChangePurchaser(record)">
              <template #icon><icon-user /></template>
              更换采购员
            </a-button>
            <a-button type="text" size="small" @click="handlePurchaseRemark(record)">
              <template #icon><icon-edit /></template>
              采购备注
            </a-button>
            <a-button type="text" size="small" @click="handleSplitProduct(record)">
              <template #icon><icon-branch /></template>
              拆分商品
            </a-button>
            <a-button type="text" size="small" @click="handleExpenseOrder(record)">
              <template #icon><icon-file /></template>
              费用单
            </a-button>
            <!-- 根据税收分类状态和链接生成状态显示不同的链接操作 -->
            <a-button
              v-if="!record.linkGenerated || !hasAllTaxClassifications(record)"
              type="text"
              size="small"
              @click="handleGenerateLink(record)"
              class="generate-link-btn"
            >
              <template #icon><icon-plus /></template>
              生成链接
            </a-button>
            <a-button
              v-else
              type="text"
              size="small"
              @click="handleCopyLink(record)"
              class="copy-link-btn"
            >
              <template #icon><icon-link /></template>
              复制链接
            </a-button>
          </template>
        </a-space>
      </template>
    </ma-crud>

    <!-- 供应商选择弹窗 -->
    <SupplierSelectModal
      v-model:visible="supplierSelectVisible"
      :order-data="currentOrderData"
      @confirm="handleSupplierConfirm"
    />

    <!-- 税收分类选择弹窗 -->
    <TaxClassificationModal
      v-model:visible="taxClassificationVisible"
      :product-data="currentProductData"
      @confirm="handleTaxClassificationConfirm"
    />

    <!-- 生成链接确认弹窗 -->
    <a-modal
      v-model:visible="generateLinkVisible"
      title="生成链接"
      :width="450"
      :footer="false"
      @cancel="handleGenerateLinkCancel"
      class="generate-link-modal"
    >
      <div class="generate-link-content">
        <div class="confirm-question">是否确认该订单生成链接？</div>

        <div class="form-container">
          <div class="form-row">
            <span class="form-label">
              <span class="required-star">*</span>
              实际收货人：
            </span>
            <a-input
              v-model="generateLinkForm.recipientName"
              placeholder="213"
              class="form-control"
            />
          </div>

          <div class="form-row">
            <span class="form-label">
              <span class="required-star">*</span>
              实际收货电话：
            </span>
            <a-input
              v-model="generateLinkForm.contactPhone"
              placeholder="23"
              class="form-control"
            />
          </div>

          <div class="form-row">
            <span class="form-label">
              <span class="required-star">*</span>
              实际收货地址：
            </span>
            <a-textarea
              v-model="generateLinkForm.deliveryAddress"
              placeholder="内蒙古通辽市科尔沁左翼中旗33-23"
              class="form-control form-textarea"
              :rows="3"
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </div>
        </div>

        <div class="button-group">
          <a-button @click="handleGenerateLinkCancel" class="cancel-btn">取消</a-button>
          <a-button
            type="primary"
            @click="handleConfirmGenerateLink"
            class="confirm-btn"
            :disabled="!isGenerateLinkFormValid"
          >
            确认
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 复制链接弹窗 -->
    <a-modal
      v-model:visible="copyLinkVisible"
      title="复制链接"
      :width="500"
      :footer="false"
      @cancel="handleCopyLinkCancel"
    >
      <div class="copy-link-content">
        <!-- 订单信息 -->
        <div class="order-info-section">
          <div class="info-item">
            <span class="info-label">订单号：</span>
            <span class="info-value">{{ currentCopyOrder?.orderNumber || '' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">订单二维码：</span>
          </div>
        </div>

        <!-- 二维码区域 -->
        <div class="qrcode-section">
          <div class="qrcode-container">
            <div class="qrcode-placeholder">
              <!-- 这里可以集成二维码生成库 -->
              <div class="qrcode-mock">
                <div class="qrcode-grid">
                  <div v-for="i in 225" :key="i" class="qrcode-dot" :class="{ active: Math.random() > 0.5 }"></div>
                </div>
              </div>
            </div>
            <div class="qrcode-info">
              <div class="qrcode-order-number">
                订单号：{{ currentCopyOrder?.orderNumber || '' }}
              </div>
              <a-button type="primary" size="small" class="copy-order-btn" @click="copyOrderNumber">
                复制
              </a-button>
            </div>
          </div>

          <!-- 订单详细信息 -->
          <div class="order-details">
            <div class="detail-item">
              <span class="detail-label">收货地址：</span>
              <span class="detail-value">{{ currentCopyOrder?.deliveryAddress || '' }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">商品品牌：</span>
              <span class="detail-value">{{ getProductBrand(currentCopyOrder) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">品类：</span>
              <span class="detail-value">{{ getProductCategory(currentCopyOrder) }}</span>
            </div>
          </div>
        </div>

        <!-- 链接区域 -->
        <div class="link-section">
          <div class="link-label">文本链接：</div>
          <div class="link-input-wrapper">
            <a-input
              :model-value="generateOrderLink(currentCopyOrder)"
              readonly
              class="link-input"
            />
            <a-button type="primary" @click="copyOrderLink">
              复制链接
            </a-button>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="modal-footer">
          <a-button @click="handleCopyLinkCancel">取消</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 费用单弹窗 -->
    <ExpenseOrderModal
      :model-value="expenseOrderVisible"
      :order-data="currentExpenseOrder"
      @update:model-value="expenseOrderVisible = $event"
      @submit="handleExpenseOrderSubmit"
    />

    <!-- 下单模版弹窗 -->
    <OrderTemplateModal
      :model-value="orderTemplateVisible"
      :order-data="currentTemplateOrder"
      @update:model-value="orderTemplateVisible = $event"
    />

    <!-- 废止操作弹窗 -->
    <a-modal
      v-model:visible="abolishOperationVisible"
      title="订单废止"
      :width="500"
      :footer="false"
      @cancel="handleAbolishOperationCancel"
    >
      <div class="abolish-operation-content">
        <div class="order-info">
          <div class="info-item">
            <span class="info-label">订单号：</span>
            <span class="info-value">{{ currentAbolishOrder?.orderNumber || '' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">订单来源：</span>
            <span class="info-value">{{ currentAbolishOrder?.orderSource || '' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">废止原因：</span>
            <span class="info-value">{{ currentAbolishOrder?.abolishReason || '订单拍错/重复下单' }}</span>
          </div>
        </div>

        <div class="form-section">
          <div class="form-item">
            <span class="form-label required">同意废止：</span>
            <a-radio-group v-model="abolishForm.agree" class="radio-group">
              <a-radio :value="true" class="agree-radio">是</a-radio>
              <a-radio :value="false" class="disagree-radio">否</a-radio>
            </a-radio-group>
          </div>

          <div class="form-item">
            <span class="form-label">审核备注：</span>
            <a-textarea
              v-model="abolishForm.remark"
              placeholder="请输入审核备注"
              :rows="3"
              :max-length="200"
              show-word-limit
              class="remark-textarea"
            />
          </div>
        </div>

        <div class="warning-tip">
          <icon-exclamation-circle-fill class="warning-icon" />
          <span class="warning-text">温馨提示：同意废止后，注意订单的跟进</span>
        </div>

        <div class="button-group">
          <a-button @click="handleAbolishOperationCancel" class="cancel-btn">取消</a-button>
          <a-button
            type="primary"
            @click="handleConfirmAbolishOperation"
            class="confirm-btn"
          >
            确定
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 废止记录弹窗 -->
    <a-modal
      v-model:visible="abolishRecordVisible"
      title="废止记录"
      :width="800"
      :footer="false"
      @cancel="handleAbolishRecordCancel"
    >
      <div class="abolish-record-content">
        <a-table
          :data="abolishRecordData"
          :pagination="false"
          :bordered="true"
          size="small"
        >
          <template #columns>
            <a-table-column title="序号" data-index="id" :width="60" align="center" />
            <a-table-column title="操作" data-index="operation" :width="120" />
            <a-table-column title="操作账号" data-index="operator" :width="100" />
            <a-table-column title="废止原因" data-index="reason" :width="150" />
            <a-table-column title="审核备注" data-index="remark" :width="150" />
            <a-table-column title="操作时间" data-index="operationTime" :width="160" />
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import SupplierSelectModal from '../SupplierSelectModal/index.vue'
import TaxClassificationModal from '../TaxClassificationModal/index.vue'
import ExpenseOrderModal from '../ExpenseOrderModal/index.vue'
import OrderTemplateModal from '../OrderTemplateModal/index.vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'view-detail',
  'order-template',
  'change-purchaser',
  'purchase-remark',
  'split-product',
  'expense-order',
  'expense-order-created',
  'request',
  'supplier-updated',
  'tax-classification-updated',
  'loss-reason-updated',
  'cost-total-updated',
  'purchase-progress-updated',
  'delivery-time-updated',
  'copy-link',
  'generate-link',
  'export',
  'abolish-operation',
  'abolish-record'
])

const crudRef = ref()
const crudBinding = ref({})

// 供应商选择相关
const supplierSelectVisible = ref(false)
const currentOrderData = ref({})

// 税收分类选择相关
const taxClassificationVisible = ref(false)
const currentProductData = ref({})
const currentOrderForTax = ref({})

// 生成链接相关
const generateLinkVisible = ref(false)
const currentGenerateOrder = ref({})
const generateLinkForm = reactive({
  recipientName: '213',
  contactPhone: '23',
  deliveryAddress: '内蒙古通辽市科尔沁左翼中旗33-23'
})

// 复制链接相关
const copyLinkVisible = ref(false)
const currentCopyOrder = ref({})

// 费用单相关
const expenseOrderVisible = ref(false)
const currentExpenseOrder = ref({})

// 下单模版相关
const orderTemplateVisible = ref(false)
const currentTemplateOrder = ref({})

// 废止操作相关
const abolishOperationVisible = ref(false)
const currentAbolishOrder = ref({})

// 废止记录相关
const abolishRecordVisible = ref(false)
const currentRecordOrder = ref({})

// 废止操作表单
const abolishForm = reactive({
  agree: true,
  remark: ''
})

// 废止记录模拟数据
const abolishRecordData = ref([
  {
    id: 1,
    operation: '跟单员-同意废止',
    operator: '黄博佳',
    reason: '订单拍错/重复下单',
    remark: '111',
    operationTime: '2024-05-14 17:56:05'
  },
  {
    id: 2,
    operation: '中控台-申请废止',
    operator: 'testAdmin',
    reason: '订单拍错/重复下单',
    remark: '',
    operationTime: '2024-05-14 17:55:51'
  }
])

// 生成链接表单验证
const isGenerateLinkFormValid = computed(() => {
  return generateLinkForm.recipientName.trim() !== '' &&
         generateLinkForm.contactPhone.trim() !== '' &&
         generateLinkForm.deliveryAddress.trim() !== ''
})
// 表格配置
const crudOptions = reactive({
  // 启用tabs功能
  tabs: {
    type: 'line',
    trigger: 'click',
    dataIndex: 'status',
    data: [
      { label: '全部', value: '' },
      { label: '待发货', value: 'pending' },
      { label: '发货未完成', value: 'processing' },
      { label: '待收货', value: 'shipped' },
      { label: '已废止', value: 'completed' },
      { label: '已关闭', value: 'closed' },
      { label: '待废止', value: '待废止' },
      { label: '废止带确认', value: '废止带确认' },
      { label: '历史全部', value: '历史全部' }
    ],
    defaultKey: '',
    searchKey: 'status'
  },
  api: () => Promise.resolve({ data: tableData.value, total: tableData.value.length }),
  showIndex: false,
  pageSize: 10,
  pageSizeOption: [10, 20, 50, 100],
  tablePagination: true,
  operationColumn: false,
  searchLabelWidth: "100px",
  searchColNumber: 4,
  rowSelection: false,
  add: false,
  edit: false,
  delete: false,
  viewLayoutSetting: true,
  showTools: true,
  searchSpan: 6,
  // 启用搜索功能
  search: true,
  searchShow: true,
  searchCollapse: true,
  searchReset: true,
  searchSubmit: true,
  // 导出功能配置
  export: {
    show: true
  },
  // 行样式配置
  rowClass: (record) => {
    return hasExpiredProductLine(record) ? 'expired-row' : ''
  }
})

// 列配置
const columns = [
  // 搜索字段配置
  {
    title: "采购进度",
    dataIndex: "purchaseProgress",
    formType: "select",
    search: true,
    hide: true,
    searchPlaceholder: "请选择采购进度",
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "无货/停产", value: "无货/停产" },
        { label: "待合同回传", value: "待合同回传" },
        { label: "待付款", value: "待付款" },
        { label: "待下单", value: "待下单" },
        { label: "待参数确定", value: "待参数确定" },
        { label: "货期", value: "货期" },
        { label: "待确认价格", value: "待确认价格" },
        { label: "待确认数量", value: "待确认数量" },
        { label: "待确认其他", value: "待确认其他" },
        { label: "待确认其他", value: "待确认其他" },
        { label: "待确认其他", value: "待确认其他" }
      ]
    }
  },
  {
    title: "链接状态",
    dataIndex: "linkStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "已链接", value: "linked" },
        { label: "未链接", value: "unlinked" }
      ]
    },
    searchPlaceholder: "请选择链接状态"
  },
  {
    title: "订单编号",
    dataIndex: "orderNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入订单编号"
  },
  {
    title: "下单账号",
    dataIndex: "buyerAccount",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入下单账号"
  },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "商城", value: "商城" },
        { label: "竞价", value: "竞价" },
        { label: "手动", value: "手动" }
      ]
    },
    searchPlaceholder: "请选择订单来源"
  },
  {
    title: "跟单员",
    dataIndex: "follower",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入跟单员"
  },
  {
    title: "采购员",
    dataIndex: "purchaser",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "张三", value: "张三" },
        { label: "李四", value: "李四" },
        { label: "王五", value: "王五" },
        { label: "赵六", value: "赵六" }
      ]
    },
    searchPlaceholder: "请选择采购员"
  },
  {
    title: "订单状态",
    dataIndex: "orderStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "待发货", value: "待发货" },
        { label: "待收货", value: "待收货" },
        { label: "已废止", value: "已废止" },
        { label: "已关闭", value: "已关闭" },
        { label: "废止待确认", value: "废止待确认" },
        { label: "历史全部", value: "历史全部" }
      ]
    },
    searchPlaceholder: "请选择订单状态"
  },
  {
    title: "审核状态",
    dataIndex: "auditStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "发货待审核", value: "发货待审核" },
        { label: "发货审核驳回", value: "发货审核驳回" },
        { label: "延迟发货待审核", value: "延迟发货待审核" },
        { label: "延迟发货审核驳回", value: "延迟发货审核驳回" },
        { label: "延迟发货审核通过", value: "延迟发货审核通过" },
        { label: "签收单待审核", value: "签收单待审核" },
        { label: "签收单审核驳回", value: "签收单审核驳回" }
      ]
    },
    searchPlaceholder: "请选择审核状态"
  },
  {
    title: "收件人姓名",
    dataIndex: "recipientName",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入收件人姓名"
  },
  {
    title: "联系电话",
    dataIndex: "contactPhone",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入联系电话"
  },
  {
    title: "商品编码",
    dataIndex: "productCode",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入商品编码"
  },
  {
    title: "商品名称",
    dataIndex: "productName",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入商品名称"
  },
  {
    title: "物流单号",
    dataIndex: "logisticsNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入物流单号"
  },
  {
    title: "订单地址",
    dataIndex: "orderAddress",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入订单地址"
  },
  {
    title: "采购备注",
    dataIndex: "purchaseRemark",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入采购备注"
  },
  {
    title: "采购时间",
    dataIndex: "purchaseTimeRange",
    formType: "range",
    search: true,
    hide: true,
    searchPlaceholder: "请选择采购时间范围"
  },
  {
    title: "订单类型",
    dataIndex: "orderType",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "商城自然单", value: "商城自然单" },
        { label: "商城报备单", value: "商城报备单" },
        { label: "竞价自然单", value: "竞价自然单" },
        { label: "竞价报备单", value: "竞价报备单" }
      ]
    },
    searchPlaceholder: "请选择订单类型"
  },
  {
    title: "是否亏损",
    dataIndex: "isLoss",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "是", value: "yes" },
        { label: "否", value: "no" }
      ]
    },
    searchPlaceholder: "请选择是否亏损"
  },
  {
    title: "下单时间",
    dataIndex: "orderTimeRange",
    formType: "range",
    search: true,
    hide: true,
    searchPlaceholder: "请选择下单时间范围"
  },
  {
    title: "开发员",
    dataIndex: "developer",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入开发员"
  },
  {
    title: "成本价范围",
    dataIndex: "costPrice",
    formType: "input-number",
    rangeSearch: true,
    search: true,
    hide: true,
    searchPlaceholder: "请输入成本价范围"
  },
  {
    title: "产品线过期状态",
    dataIndex: "productLineExpireStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "已过期", value: "expired" },
        { label: "未过期", value: "valid" },
        { label: "即将过期", value: "expiring" }
      ]
    },
    searchPlaceholder: "请选择产品线过期状态"
  },
  // 显示列配置
  {
    title: "订单信息",
    dataIndex: "orderInfo",
    width: 280,
    slot: true,
    search: false
  },
  {
    title: "商品信息",
    dataIndex: "productInfo",
    width: 600,
    slot: true,
    search: false
  },
  {
    title: "采购成本信息",
    dataIndex: "costInfo",
    width: 320,
    slot: true,
    search: false
  },
  {
    title: "采购信息",
    dataIndex: "purchaseInfo",
    width: 280,
    slot: true,
    search: false
  },
  {
    title: "状态信息",
    dataIndex: "statusInfo",
    width: 200,
    slot: true,
    search: false
  },
  {
    title: "实际/建议供应商",
    dataIndex: "supplierInfo",
    width: 180,
    hide: true,
    search: false
  },
  {
    title: "备注",
    dataIndex: "remarkInfo",
    width: 250,
    slot: true,
    search: false
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 180,
    slot: true,
    search: false,
    fixed: "right"
  }
]

// 使用传入的数据
const tableData = computed(() => props.data || [])

// 计算商品总额
const calculateProductsTotal = (record) => {
  if (!record.products || record.products.length === 0) {
    return '0.00'
  }

  const total = record.products.reduce((sum, product) => {
    return sum + parseFloat(product.totalAmount || 0)
  }, 0)

  return total.toFixed(2)
}

// 判断是否为亏损订单
const isLossOrder = (record) => {
  const costTotal = parseFloat(record.costTotal || 0)
  const inspectionCost = getTotalInspectionCost(record)
  return costTotal > inspectionCost
}

// 计算亏损金额
const calculateLossAmount = (record) => {
  const costTotal = parseFloat(record.costTotal || 0)
  const inspectionCost = getTotalInspectionCost(record)
  const lossAmount = costTotal - inspectionCost
  return lossAmount > 0 ? lossAmount.toFixed(2) : '0.00'
}

// 计算总巡检成本
const getTotalInspectionCost = (record) => {
  if (record.products && record.products.length > 0) {
    const total = record.products.reduce((sum, product) => {
      return sum + parseFloat(product.inspectionCost || 0)
    }, 0)
    return total
  }
  return parseFloat(record.inspectionCost || 0)
}

// 判断产品线是否已过期
const isProductLineExpired = (expireTime) => {
  if (!expireTime) return false
  const now = new Date()
  const expire = new Date(expireTime)
  return expire < now
}

// 判断产品线是否即将过期（30天内）
const isProductLineExpiring = (expireTime) => {
  if (!expireTime) return false
  const now = new Date()
  const expire = new Date(expireTime)
  const diffTime = expire - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays > 0 && diffDays <= 30
}


// 格式化过期时间显示
const formatExpireTime = (expireTime) => {
  if (!expireTime) return '-'
  const date = new Date(expireTime)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 获取第一个产品线过期时间
const getFirstProductLineExpireTime = (record) => {
  // 处理多商品订单
  if (record.products && record.products.length > 0) {
    for (const product of record.products) {
      if (product.productLineExpireTime) {
        return formatExpireTime(product.productLineExpireTime)
      }
    }
  }
  // 处理单商品订单
  else if (record.productLineExpireTime) {
    return formatExpireTime(record.productLineExpireTime)
  }

  return '-'
}

// 判断订单是否包含过期的产品线
const hasExpiredProductLine = (record) => {
  // 处理多商品订单
  if (record.products && record.products.length > 0) {
    return record.products.some(product =>
      product.productLineExpireTime && isProductLineExpired(product.productLineExpireTime)
    )
  }
  // 处理单商品订单
  else if (record.productLineExpireTime) {
    return isProductLineExpired(record.productLineExpireTime)
  }

  return false
}

// 获取产品线过期时间的显示样式类
const getProductLineExpireDisplayClass = (record) => {
  // 处理多商品订单
  if (record.products && record.products.length > 0) {
    for (const product of record.products) {
      if (product.productLineExpireTime) {
        if (isProductLineExpired(product.productLineExpireTime)) {
          return 'expired'
        } else if (isProductLineExpiring(product.productLineExpireTime)) {
          return 'expiring'
        }
        return 'valid'
      }
    }
  }
  // 处理单商品订单
  else if (record.productLineExpireTime) {
    if (isProductLineExpired(record.productLineExpireTime)) {
      return 'expired'
    } else if (isProductLineExpiring(record.productLineExpireTime)) {
      return 'expiring'
    }
    return 'valid'
  }

  return ''
}

// 处理亏损原因变更
const handleLossReasonChange = (record, reason) => {
  emit('loss-reason-updated', {
    orderData: record,
    lossReason: reason
  })
}

// 处理成本总价输入
const handleCostTotalInput = (record, value) => {
  // 实时更新数据，不进行验证
  record.costTotal = value
}

// 处理成本总价变更
const handleCostTotalChange = (record, value) => {
  // 验证输入值
  const numValue = parseFloat(value)
  if (isNaN(numValue) || numValue < 0) {
    Message.warning('请输入有效的成本总价')
    return
  }

  const formattedValue = numValue.toFixed(2)
  record.costTotal = formattedValue
  emit('cost-total-updated', {
    orderData: record,
    costTotal: formattedValue
  })
}

// 处理采购进度变更
const handlePurchaseProgressChange = (record, progress) => {
  record.purchaseProgress = progress

  // 如果不是选择"货期"，清空货期时间
  if (progress !== '货期') {
    record.deliveryTime = ''
  }

  emit('purchase-progress-updated', {
    orderData: record,
    purchaseProgress: progress
  })

  Message.success('采购进度更新成功')
}

// 处理货期时间变更
const handleDeliveryTimeChange = (record, time) => {
  record.deliveryTime = time
  emit('delivery-time-updated', {
    orderData: record,
    deliveryTime: time
  })

  if (time) {
    Message.success('货期时间更新成功')
  }
}

// 请求处理
const handleRequest = (params) => {
  emit('request', params)
}

// 操作事件处理
const handleViewDetail = (record) => {
  emit('view-detail', record)
}

const handleOrderTemplate = (record) => {
  currentTemplateOrder.value = record
  orderTemplateVisible.value = true
}

const handleChangePurchaser = (record) => {
  emit('change-purchaser', record)
}

const handlePurchaseRemark = (record) => {
  emit('purchase-remark', record)
}

const handleSplitProduct = (record) => {
  emit('split-product', record)
}

const handleExpenseOrder = (record) => {
  currentExpenseOrder.value = record
  expenseOrderVisible.value = true
}

const handleAbolishOperation = (record) => {
  currentAbolishOrder.value = record
  abolishOperationVisible.value = true
  emit('abolish-operation', record)
}

const handleAbolishRecord = (record) => {
  currentRecordOrder.value = record
  abolishRecordVisible.value = true
  emit('abolish-record', record)
}

const handleCopyLink = (record) => {
  currentCopyOrder.value = record
  copyLinkVisible.value = true
  emit('copy-link', record)
}

// 检查订单中所有商品是否都已选择税收分类
const hasAllTaxClassifications = (record) => {
  if (!record) return false

  console.log('检查税收分类:', record)

  // 如果是多商品订单
  if (record.products && Array.isArray(record.products) && record.products.length > 0) {
    return record.products.every(product =>
       product.taxClassification?.label
    )
  }

  // 如果是单商品订单
  return !!( record.taxClassification?.label)
}

// 生成链接处理
const handleGenerateLink = (record) => {
  // 检查是否所有商品都已选择税收分类
  if (!hasAllTaxClassifications(record)) {
    Message.error('请先选择商品税收分类')
    return
  }

  // 所有商品都已选择税收分类，打开生成链接确认弹窗
  openGenerateLinkModal(record)
}

// 打开生成链接确认弹窗
const openGenerateLinkModal = (record) => {
  currentGenerateOrder.value = record

  // 预填充订单信息，如果没有则使用示例数据
  generateLinkForm.recipientName = record.recipientName || '213'
  generateLinkForm.contactPhone = record.contactPhone || '23'
  generateLinkForm.deliveryAddress = record.deliveryAddress || record.orderAddress || '内蒙古通辽市科尔沁左翼中旗33-23'

  generateLinkVisible.value = true
}

// 取消生成链接
const handleGenerateLinkCancel = () => {
  generateLinkVisible.value = false
  currentGenerateOrder.value = {}

  // 重置表单为默认值
  generateLinkForm.recipientName = '213'
  generateLinkForm.contactPhone = '23'
  generateLinkForm.deliveryAddress = '内蒙古通辽市科尔沁左翼中旗33-23'
}

// 确认生成链接
const handleConfirmGenerateLink = () => {
  if (!isGenerateLinkFormValid.value) {
    Message.error('请填写完整的收货信息')
    return
  }

  // 再次检查税收分类状态
  if (!hasAllTaxClassifications(currentGenerateOrder.value)) {
    Message.error('请先选择商品税收分类')
    generateLinkVisible.value = false
    return
  }

  // 更新订单信息，包括链接生成状态
  const updatedOrder = {
    ...currentGenerateOrder.value,
    recipientName: generateLinkForm.recipientName,
    contactPhone: generateLinkForm.contactPhone,
    deliveryAddress: generateLinkForm.deliveryAddress,
    linkGenerated: true, // 标记链接已生成
    linkGeneratedAt: new Date().toISOString() // 记录生成时间
  }

  // 通知父组件生成链接
  emit('generate-link', updatedOrder)

  // 关闭弹窗
  generateLinkVisible.value = false

  Message.success('链接生成成功！')
}

// 处理费用单提交
const handleExpenseOrderSubmit = (expenseData) => {
  console.log('费用单数据:', expenseData)
  // 这里可以调用API保存费用单数据
  // await expenseApi.createExpenseOrder(expenseData)

  // 通知父组件
  emit('expense-order-created', expenseData)
}

// 供应商选择处理
const handleSelectSupplier = (record) => {
  currentOrderData.value = record
  supplierSelectVisible.value = true
}

// 商品级别供应商选择处理
const handleSelectProductSupplier = (productData, orderData) => {
  currentOrderData.value = {
    ...orderData,
    currentProduct: productData
  }
  supplierSelectVisible.value = true
}

// 供应商确认选择
const handleSupplierConfirm = async ({ supplier, orderData }) => {
  try {
    // 这里可以调用API更新供应商信息
    // await supplyApi.purchaseOrder.updateSupplier(orderData.id, {
    //   actualSupplierId: supplier.id,
    //   actualSupplierName: supplier.name
    // })

    Message.success('供应商更新成功')
    emit('supplier-updated', { orderData, supplier })
  } catch (error) {
    console.error('更新供应商失败:', error)
    Message.error('更新供应商失败，请重试')
  }
}

// 税收分类选择处理
const handleSelectTaxClassification = (productData, orderData) => {
  currentProductData.value = productData
  currentOrderForTax.value = orderData
  taxClassificationVisible.value = true
}

// 税收分类确认选择
const handleTaxClassificationConfirm = async ({ taxClassification, productData }) => {
  try {
    // 这里可以调用API更新税收分类信息
    // await productApi.updateTaxClassification(productData.id, {
    //   taxClassificationId: taxClassification.codeid,
    //   taxClassificationName: taxClassification.label,
    //   taxClassificationCode: taxClassification.num
    // })

    Message.success('税收分类更新成功')
    emit('tax-classification-updated', {
      productData,
      taxClassification,
      orderData: currentOrderForTax.value
    })

  } catch (error) {
    console.error('更新税收分类失败:', error)
    Message.error('更新税收分类失败，请重试')
  }
}

// 状态颜色函数
const getOrderStatusColor = (status) => {
  const colorMap = {
    '待发货': 'orange',
    '发货未完成': 'blue', 
    '待收货': 'cyan',
    '已废止': 'gray',
    '已关闭': 'red',
    '废止待确认': 'magenta'
  }
  return colorMap[status] || 'gray'
}

const getAuditStatusColor = (status) => {
  const colorMap = {
    '审核通过': 'green',
    '待审核': 'orange',
    '审核驳回': 'red',
    '废止待确认': 'magenta'
  }
  return colorMap[status] || 'gray'
}

const getErpStatusColor = (status) => {
  const colorMap = {
    '已同步': 'green',
    '待同步': 'orange',
    '同步失败': 'red'
  }
  return colorMap[status] || 'gray'
}

const getSplitOrderStatusColor = (status) => {
  const colorMap = {
    '未拆分': 'blue',
    '已拆分': 'green',
    '拆分中': 'orange'
  }
  return colorMap[status] || 'gray'
}

const getCancelStatusColor = (status) => {
  if (!status) return 'gray'
  const colorMap = {
    '待废止': 'orange',
    '废止待确认': 'red',
    '已废止': 'gray'
  }
  return colorMap[status] || 'gray'
}

const getPendingCancelStatusColor = (status) => {
  if (!status) return 'gray'
  const colorMap = {
    '待废止': 'orange',
    '废止待确认': 'red',
    '废止审核中': 'blue',
    '废止已驳回': 'magenta'
  }
  return colorMap[status] || 'gray'
}

// 复制链接相关方法
const handleCopyLinkCancel = () => {
  copyLinkVisible.value = false
  currentCopyOrder.value = {}
}

// 生成订单链接
const generateOrderLink = (order) => {
  if (!order?.orderNumber) return ''
  return `https://uat.v3.supply.8080bl.com/#/havedship?orderNumber=${order.orderNumber}`
}

// 获取商品品牌
const getProductBrand = (order) => {
  if (!order) return ''
  if (order.products && Array.isArray(order.products) && order.products.length > 0) {
    return order.products[0]?.brand || order.products[0]?.productBrand || ''
  }
  return order.brand || order.productBrand || ''
}

// 获取商品品类
const getProductCategory = (order) => {
  if (!order) return ''
  if (order.products && Array.isArray(order.products) && order.products.length > 0) {
    return order.products[0]?.category || order.products[0]?.productCategory || ''
  }
  return order.category || order.productCategory || ''
}

// 复制订单号
const copyOrderNumber = async () => {
  try {
    const orderNumber = currentCopyOrder.value?.orderNumber || ''
    await navigator.clipboard.writeText(orderNumber)
    Message.success('订单号复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    Message.error('复制失败，请手动复制')
  }
}

// 复制订单链接
const copyOrderLink = async () => {
  try {
    const link = generateOrderLink(currentCopyOrder.value)
    await navigator.clipboard.writeText(link)
    Message.success('链接复制成功')
  } catch (error) {
    console.error('复制失败:', error)
    Message.error('复制失败，请手动复制')
  }
}

// 废止操作相关方法
const handleAbolishOperationCancel = () => {
  abolishOperationVisible.value = false
  currentAbolishOrder.value = {}
  // 重置表单
  abolishForm.agree = true
  abolishForm.remark = ''
}

const handleConfirmAbolishOperation = () => {
  // 这里可以调用API提交废止操作
  console.log('废止操作数据:', {
    orderId: currentAbolishOrder.value.id,
    orderNumber: currentAbolishOrder.value.orderNumber,
    agree: abolishForm.agree,
    remark: abolishForm.remark
  })

  Message.success(abolishForm.agree ? '废止操作已同意' : '废止操作已拒绝')
  abolishOperationVisible.value = false

  // 重置表单
  abolishForm.agree = true
  abolishForm.remark = ''
}

// 废止记录相关方法
const handleAbolishRecordCancel = () => {
  abolishRecordVisible.value = false
  currentRecordOrder.value = {}
}
</script>

<style scoped>
@import './css/index.css';
</style>
