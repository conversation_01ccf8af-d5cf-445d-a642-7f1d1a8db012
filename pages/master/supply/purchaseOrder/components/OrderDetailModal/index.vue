<template>
  <a-modal 
    v-model:visible="visible" 
    title="订单详情" 
    width="1200px"
    :footer="false"
    @cancel="handleCancel"
  >
    <div v-if="orderData" class="detail-content">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="订单编号">{{ orderData.orderNumber }}</a-descriptions-item>
        <a-descriptions-item label="采购时间">{{ orderData.purchaseTime }}</a-descriptions-item>
        <a-descriptions-item label="下单时间">{{ orderData.orderTime }}</a-descriptions-item>
        <a-descriptions-item label="订单来源">{{ orderData.orderSource }}</a-descriptions-item>
        <a-descriptions-item label="店铺">{{ orderData.storeName }}</a-descriptions-item>
        <a-descriptions-item label="订单类型">{{ orderData.orderType }}</a-descriptions-item>
        <a-descriptions-item label="跟单员">{{ orderData.follower }}</a-descriptions-item>
        <a-descriptions-item label="采购员">{{ orderData.purchaser }}</a-descriptions-item>
        <a-descriptions-item label="收货地址" :span="2">{{ orderData.deliveryAddress }}</a-descriptions-item>
      </a-descriptions>
      
      <a-divider>商品信息</a-divider>
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="商品名称">{{ orderData.productName }}</a-descriptions-item>
        <a-descriptions-item label="SKU">{{ orderData.sku }}</a-descriptions-item>
        <a-descriptions-item label="商品编码">{{ orderData.productCode }}</a-descriptions-item>
        <a-descriptions-item label="规格型号">{{ orderData.specification }}</a-descriptions-item>
        <a-descriptions-item label="税收分类">{{ orderData.taxCategory }}</a-descriptions-item>
        <a-descriptions-item label="单价">{{ orderData.unitPrice }}</a-descriptions-item>
        <a-descriptions-item label="数量">{{ orderData.quantity }}件</a-descriptions-item>
        <a-descriptions-item label="总金额">{{ orderData.totalAmount }}</a-descriptions-item>
      </a-descriptions>

      <a-divider>成本信息</a-divider>
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="实际成本">{{ orderData.actualCost }}</a-descriptions-item>
        <a-descriptions-item label="巡检成本">{{ orderData.inspectionCost }}</a-descriptions-item>
        <a-descriptions-item label="成本总价">{{ orderData.costTotal }}</a-descriptions-item>
        <a-descriptions-item label="毛利润">{{ orderData.profitRate }}</a-descriptions-item>
        <a-descriptions-item label="运费">{{ orderData.freight }}</a-descriptions-item>
        <a-descriptions-item label="总毛利">{{ orderData.grossProfitRate }}</a-descriptions-item>
      </a-descriptions>

      <a-divider>状态信息</a-divider>
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="订单状态">
          <a-tag :color="getOrderStatusColor(orderData.orderStatus)">
            {{ orderData.orderStatus }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="审核状态">
          <a-tag :color="getAuditStatusColor(orderData.auditStatus)">
            {{ orderData.auditStatus }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="ERP状态">
          <a-tag :color="getErpStatusColor(orderData.erpStatus)">
            {{ orderData.erpStatus }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="拆分状态">
          <a-tag :color="getSplitOrderStatusColor(orderData.splitOrderStatus)">
            {{ orderData.splitOrderStatus }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="废止状态" v-if="orderData.cancelStatus">
          <a-tag :color="getCancelStatusColor(orderData.cancelStatus)">
            {{ orderData.cancelStatus }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="待废止状态" v-if="orderData.pendingCancelStatus">
          <a-tag :color="getPendingCancelStatusColor(orderData.pendingCancelStatus)">
            {{ orderData.pendingCancelStatus }}
          </a-tag>
        </a-descriptions-item>
      </a-descriptions>

      <a-divider>其他信息</a-divider>
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="买家账号">{{ orderData.buyerAccount }}</a-descriptions-item>
        <a-descriptions-item label="物流情况">{{ orderData.logisticsInfo || '-' }}</a-descriptions-item>
        <a-descriptions-item label="采购进度">{{ orderData.purchaseProgress || '-' }}</a-descriptions-item>
        <a-descriptions-item label="物流单号">{{ orderData.logisticsNumber || '-' }}</a-descriptions-item>
        <a-descriptions-item label="实际供应商">{{ orderData.actualSupplier }}</a-descriptions-item>
        <a-descriptions-item label="建议供应商">{{ orderData.suggestedSupplier }}</a-descriptions-item>
        <a-descriptions-item label="业务员备注" v-if="orderData.businessRemark">{{ orderData.businessRemark }}</a-descriptions-item>
        <a-descriptions-item label="采购备注" v-if="orderData.purchaseRemark">{{ orderData.purchaseRemark }}</a-descriptions-item>
      </a-descriptions>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(props.modelValue)

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleCancel = () => {
  visible.value = false
}

// 状态颜色函数
const getOrderStatusColor = (status) => {
  const colorMap = {
    '待发货': 'orange',
    '发货未完成': 'blue', 
    '待收货': 'cyan',
    '已废止': 'gray',
    '已关闭': 'red',
    '废止待确认': 'magenta'
  }
  return colorMap[status] || 'gray'
}

const getAuditStatusColor = (status) => {
  const colorMap = {
    '审核通过': 'green',
    '待审核': 'orange',
    '审核驳回': 'red',
    '废止待确认': 'magenta'
  }
  return colorMap[status] || 'gray'
}

const getErpStatusColor = (status) => {
  const colorMap = {
    '已同步': 'green',
    '待同步': 'orange',
    '同步失败': 'red'
  }
  return colorMap[status] || 'gray'
}

const getSplitOrderStatusColor = (status) => {
  const colorMap = {
    '未拆分': 'blue',
    '已拆分': 'green',
    '拆分中': 'orange'
  }
  return colorMap[status] || 'gray'
}

const getCancelStatusColor = (status) => {
  if (!status) return 'gray'
  const colorMap = {
    '待废止': 'orange',
    '废止待确认': 'red',
    '已废止': 'gray'
  }
  return colorMap[status] || 'gray'
}

const getPendingCancelStatusColor = (status) => {
  if (!status) return 'gray'
  const colorMap = {
    '待废止': 'orange',
    '废止待确认': 'red',
    '废止审核中': 'blue',
    '废止已驳回': 'magenta'
  }
  return colorMap[status] || 'gray'
}
</script>

<style scoped>
.detail-content .arco-descriptions {
  margin-bottom: 16px;
}
</style>
