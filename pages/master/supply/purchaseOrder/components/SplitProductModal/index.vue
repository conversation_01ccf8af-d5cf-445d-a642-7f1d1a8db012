<template>
  <a-drawer
    v-model:visible="visible"
    title=""
    width="70%"
    :footer="false"
    :header="false"
    @cancel="handleCancel"
    class="split-product-drawer"
  >
    <div class="split-drawer-content">
      <!-- 顶部操作按钮 -->
      <div class="drawer-header">
        <div class="header-buttons">
          <a-button @click="handleBack" size="large" class="return-btn">
            返回
          </a-button>
          <a-button
            v-if="!showSplitConfig"
            type="primary"
            @click="showSplitConfiguration"
            size="large"
            class="split-btn"
          >
            拆分商品
          </a-button>
          <a-button
            v-if="showSplitConfig"
            type="primary"
            @click="addSplitItem"
            size="large"
            class="add-btn"
          >
            新增
          </a-button>
        </div>
      </div>

      <!-- 商品信息表格 -->
      <div class="split-table-container" v-if="!showSplitConfig">
        <a-table
          :data="tableData"
          :pagination="false"
          :bordered="true"
          class="split-product-table"
          :scroll="{ x: 1400 }"
        >
          <template #columns>
            <a-table-column title="序号" align="center" width="80">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>

            <a-table-column title="商品信息" width="350">
              <template #cell="{ record }">
                <div class="product-info-cell">
                  <div class="product-image">
                    <img :src="record.productImage || '/placeholder-product.png'" alt="商品图片" />
                  </div>
                  <div class="product-details">
                    <div class="product-name">{{ record.productName }}</div>
                    <div class="product-code">商品编号: {{ record.productCode }}</div>
                    <div class="product-sku">配货编号: {{ record.sku }}</div>
                    <div class="product-spec">规格型号: {{ record.specification }}</div>
                  </div>
                </div>
              </template>
            </a-table-column>

            <a-table-column title="数量" align="center" width="100">
              <template #cell="{ record }">
                <div class="quantity-cell">
                  <span v-if="!record.isEditable">{{ record.quantity }}</span>
                  <a-input-number
                    v-else
                    v-model="record.quantity"
                    :min="1"
                    :max="originalProduct?.quantity"
                    size="small"
                    style="width: 80px"
                    @change="handleQuantityChange"
                  />
                  <span class="unit">件</span>
                </div>
              </template>
            </a-table-column>

            <a-table-column title="供货价" align="center" width="120">
              <template #cell="{ record }">
                <span class="price-text">¥{{ record.unitPrice || '0.00' }}</span>
              </template>
            </a-table-column>

            <a-table-column title="更新时间" align="center" width="150">
              <template #cell="{ record }">
                {{ record.updateTime || '2025-06-24 15:31:34' }}
              </template>
            </a-table-column>

            <a-table-column title="发货信息" align="center" width="150">
              <template #cell="{ record }">
                <div class="shipping-info">
                  <div class="shipping-status">{{ record.shippingStatus || '待发货' }}</div>
                  <div class="shipping-number" v-if="record.shippingNumber">
                    {{ record.shippingNumber }}
                  </div>
                </div>
              </template>
            </a-table-column>

            <a-table-column title="备注" align="center" width="120">
              <template #cell="{ record }">
                <div class="remark-cell">
                  <a-input
                    v-if="record.isEditable"
                    v-model="record.remark"
                    placeholder="请输入备注"
                    size="small"
                    style="width: 100%"
                  />
                  <span v-else>{{ record.remark || '-' }}</span>
                </div>
              </template>
            </a-table-column>

            <a-table-column title="操作" align="center" width="120">
              <template #cell="{ record, rowIndex }">
                <div class="action-buttons">
                  <a-button
                    type="primary"
                    size="small"
                    class="action-btn view-btn"
                  >
                    查看详情
                  </a-button>
                  <a-button
                    type="primary"
                    size="small"
                    class="action-btn edit-btn"
                  >
                    更新信息
                  </a-button>
                  <a-button
                    type="primary"
                    size="small"
                    class="action-btn qr-btn"
                  >
                    复制二维码
                  </a-button>
                </div>
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>

      <!-- 拆分配置界面 -->
      <div class="split-config-container" v-if="showSplitConfig">
        <!-- 拆分配置表格 -->
        <div class="split-config-table">
          <a-table
            :data="splitConfigData"
            :pagination="false"
            :bordered="true"
            class="config-table"
          >
            <template #columns>
              <a-table-column title="商品信息" width="400">
                <template #cell="{ record }">
                  <div class="product-info-cell">
                    <div class="product-image">
                      <img :src="originalProduct?.productImage || '/placeholder-product.png'" alt="商品图片" />
                    </div>
                    <div class="product-details">
                      <div class="product-name">{{ originalProduct?.productName }}</div>
                      <div class="product-code">商品编号: {{ originalProduct?.productCode }}</div>
                      <div class="product-sku">配货编号: {{ originalProduct?.sku }}</div>
                      <div class="product-spec">规格型号: {{ originalProduct?.specification }}</div>
                    </div>
                  </div>
                </template>
              </a-table-column>

              <a-table-column title="总数量" align="center" width="120">
                <template #cell>
                  <span class="quantity-text">{{ originalProduct?.quantity || 0 }}</span>
                </template>
              </a-table-column>

              <a-table-column title="待分配数量" align="center" width="120">
                <template #cell>
                  <span class="quantity-text pending">{{ remainingQuantity }}</span>
                </template>
              </a-table-column>

              <a-table-column title="分配数量" align="center" width="150">
                <template #cell="{ record, rowIndex }">
                  <a-input-number
                    v-model="record.quantity"
                    :min="1"
                    :max="originalProduct?.quantity"
                    placeholder="数量"
                    size="small"
                    style="width: 100%"
                    @change="handleQuantityChange"
                  />
                </template>
              </a-table-column>

            </template>
          </a-table>
        </div>

        <!-- 提示信息 -->
        <div class="split-notice">
          <span class="notice-text">注：商品一旦确认拆分无法还原，请谨慎拆分；可先拆部分，剩余确定后再拆</span>
        </div>

        <!-- 底部操作按钮 -->
        <div class="config-footer">
          <a-button @click="cancelSplitConfig" size="large">取消</a-button>
          <a-button
            type="primary"
            @click="handleSubmit"
            size="large"
            :disabled="remainingQuantity !== 0"
          >
            提交
          </a-button>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  productData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const visible = ref(props.modelValue)

// 原始商品数据
const originalProduct = ref(null)
const selectedProduct = ref(null)

// 表格数据
const tableData = ref([])

// 视图状态控制
const showSplitConfig = ref(false)

// 拆分配置数据
const splitConfigData = ref([
  { quantity: 1, id: 1 }
])

// 计算总拆分数量
const totalSplitQuantity = computed(() => {
  if (showSplitConfig.value) {
    return splitConfigData.value.reduce((sum, item) => sum + (item.quantity || 0), 0)
  }
  return tableData.value
    .filter(item => !item.isOriginal)
    .reduce((sum, item) => sum + (item.quantity || 0), 0)
})

// 计算剩余数量
const remainingQuantity = computed(() => {
  if (!originalProduct.value) return 0
  return originalProduct.value.quantity - totalSplitQuantity.value
})

watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.productData) {
    initializeData()
  }
})

watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 初始化数据
const initializeData = () => {
  if (!props.productData) return

  originalProduct.value = { ...props.productData }
  selectedProduct.value = { ...props.productData }

  // 重置视图状态
  showSplitConfig.value = false

  // 初始化表格数据，显示原始商品
  tableData.value = [
    {
      ...props.productData,
      isOriginal: true,
      isEditable: false,
      updateTime: '2025-06-24 15:31:34',
      shippingStatus: '待发货',
      shippingNumber: '',
      remark: ''
    }
  ]

  // 初始化拆分配置数据
  const defaultQuantity = Math.floor((props.productData.quantity || 2) / 2)
  splitConfigData.value = [
    { quantity: defaultQuantity, id: 1 },
    { quantity: (props.productData.quantity || 2) - defaultQuantity, id: 2 }
  ]
}

// 数量变化处理
const handleQuantityChange = () => {
  // 重新计算剩余数量
}

// 显示拆分配置界面
const showSplitConfiguration = () => {
  showSplitConfig.value = true
}

// 添加拆分项
const addSplitItem = () => {
  if (showSplitConfig.value) {
    // 在拆分配置模式下添加新的拆分项
    splitConfigData.value.push({
      quantity: 1,
      id: Date.now()
    })
  } else {
    // 在列表模式下添加新的商品项
    if (!originalProduct.value) return

    const newItem = {
      ...originalProduct.value,
      id: `split_${Date.now()}`,
      quantity: 1,
      isOriginal: false,
      isEditable: true,
      updateTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      shippingStatus: '待发货',
      shippingNumber: '',
      remark: `拆分${tableData.value.length}`
    }

    tableData.value.push(newItem)
  }
}

// 删除拆分项
const removeSplitItem = (index) => {
  if (tableData.value[index] && !tableData.value[index].isOriginal) {
    tableData.value.splice(index, 1)
  }
}

// 删除拆分配置项
const removeSplitConfigItem = (index) => {
  if (splitConfigData.value.length > 1) {
    splitConfigData.value.splice(index, 1)
  }
}

// 取消拆分配置
const cancelSplitConfig = () => {
  showSplitConfig.value = false
}

// 返回处理
const handleBack = () => {
  if (showSplitConfig.value) {
    showSplitConfig.value = false
  } else {
    visible.value = false
  }
}

// 提交拆分
const handleSubmit = async () => {
  try {
    if (!originalProduct.value) {
      Message.error('没有可拆分的商品数据')
      return
    }

    if (remainingQuantity.value !== 0) {
      Message.error(`拆分数量总和必须等于原数量${originalProduct.value.quantity}`)
      return
    }

    let splitItems = []

    if (showSplitConfig.value) {
      // 拆分配置模式
      if (splitConfigData.value.length === 0) {
        Message.error('请至少添加一个拆分项')
        return
      }
      splitItems = splitConfigData.value.map((item, index) => ({
        ...originalProduct.value,
        id: `split_${Date.now()}_${index}`,
        quantity: item.quantity,
        splitIndex: index + 1,
        remark: `拆分${index + 1}`
      }))
    } else {
      // 列表模式
      splitItems = tableData.value.filter(item => !item.isOriginal)
      if (splitItems.length === 0) {
        Message.error('请至少添加一个拆分项')
        return
      }
    }

    const splitData = {
      originalProduct: originalProduct.value,
      splitItems: splitItems,
      splitAt: new Date().toISOString()
    }

    emit('submit', splitData)
    Message.success('商品拆分成功')
    visible.value = false
  } catch (error) {
    console.error('拆分提交失败:', error)
    Message.error('拆分提交失败')
  }
}

// 取消操作
const handleCancel = () => {
  showSplitConfig.value = false
  visible.value = false
}
</script>

<style scoped>
.split-product-drawer {
  background: #f5f5f5;
}

.split-product-drawer :deep(.arco-drawer-body) {
  padding: 0;
  background: #f5f5f5;
}

.split-drawer-content {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.drawer-header {
  background: #ffffff;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e6eb;
  flex-shrink: 0;
}

.header-buttons {
  display: flex;
  gap: 12px;
}

.return-btn {
  background: #f2f3f5;
  border: 1px solid #d9d9d9;
  color: #1d2129;
}

.return-btn:hover {
  background: #e5e6eb;
  border-color: #c9cdd4;
}

.split-btn {
  background: #ff4757;
  border-color: #ff4757;
}

.split-btn:hover {
  background: #ff3742;
  border-color: #ff3742;
}

.split-btn:disabled {
  background: #f7f8fa;
  border-color: #e5e6eb;
  color: #c9cdd4;
}

.split-table-container {
  flex: 1;
  padding: 16px 24px;
  overflow: auto;
}

.split-product-table {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.split-product-table :deep(.arco-table-th) {
  background: #f7f8fa;
  font-weight: 500;
  color: #1d2129;
  border-bottom: 1px solid #e5e6eb;
}

.split-product-table :deep(.arco-table-td) {
  border-bottom: 1px solid #f2f3f5;
}

.split-product-table :deep(.arco-table-tbody .arco-table-tr:hover .arco-table-td) {
  background: #f8f9fa;
}

.product-info-cell {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.product-image {
  width: 50px;
  height: 50px;
  margin-right: 12px;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
}

.product-details {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-code {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 2px;
}

.product-sku {
  font-size: 12px;
  color: #165dff;
  margin-bottom: 2px;
}

.product-spec {
  font-size: 12px;
  color: #86909c;
}

.quantity-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.unit {
  font-size: 12px;
  color: #86909c;
}

.price-text {
  font-size: 14px;
  font-weight: 500;
  color: #f53f3f;
}

.shipping-info {
  text-align: center;
}

.shipping-status {
  font-size: 12px;
  color: #1d2129;
  margin-bottom: 2px;
}

.shipping-number {
  font-size: 11px;
  color: #86909c;
}

.remark-cell {
  width: 100%;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.action-btn {
  font-size: 12px;
  padding: 2px 8px;
  height: 24px;
  border-radius: 4px;
}

.edit-btn {
  background: #ff4757;
  border-color: #ff4757;
  color: #ffffff;
}

.edit-btn:hover {
  background: #ff3742;
  border-color: #ff3742;
}

.view-btn {
  background: #52c41a;
  border-color: #52c41a;
  color: #ffffff;
}

.view-btn:hover {
  background: #389e0d;
  border-color: #389e0d;
}

.split-btn {
  background: #1890ff;
  border-color: #1890ff;
  color: #ffffff;
}

.split-btn:hover {
  background: #096dd9;
  border-color: #096dd9;
}

.qr-btn {
  background: #722ed1;
  border-color: #722ed1;
  color: #ffffff;
}

.qr-btn:hover {
  background: #531dab;
  border-color: #531dab;
}

.add-btn {
  background: #52c41a;
  border-color: #52c41a;
}

.add-btn:hover {
  background: #389e0d;
  border-color: #389e0d;
}

/* 拆分配置界面样式 */
.split-config-container {
  flex: 1;
  padding: 16px 24px;
  overflow: auto;
}

.product-info-header {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.quantity-summary {
  display: flex;
  justify-content: space-around;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 8px;
  margin-bottom: 24px;
}

.summary-item {
  text-align: center;
}

.summary-item .label {
  display: block;
  font-size: 14px;
  color: #86909c;
  margin-bottom: 8px;
}

.summary-item .value {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #1d2129;
}

.summary-item .value.pending {
  color: #ff7d00;
}

.summary-item .value.allocated {
  color: #00b42a;
}

.split-config-table {
  margin-bottom: 16px;
}

.config-table {
  background: #ffffff;
  border-radius: 8px;
  overflow: hidden;
}

.config-table :deep(.arco-table-th) {
  background: #f7f8fa;
  font-weight: 500;
  color: #1d2129;
  border-bottom: 1px solid #e5e6eb;
}

.config-table :deep(.arco-table-td) {
  border-bottom: 1px solid #f2f3f5;
}

.split-notice {
  margin: 16px 0;
  text-align: center;
}

.notice-text {
  color: #f53f3f;
  font-size: 14px;
}

.config-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-top: 24px;
  border-top: 1px solid #e5e6eb;
  margin-top: 24px;
}
</style>
