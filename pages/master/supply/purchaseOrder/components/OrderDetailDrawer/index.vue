<template>
  <a-drawer
    v-model:visible="visible"
    title="订单详情"
    width="80%"
    :footer="false"
    placement="right"
    @cancel="handleCancel"
  >
    <div v-if="orderData" class="order-detail-drawer">
      <!-- 基本信息区域 -->
      <div class="basic-info-section">
        <BasicInfo :order-data="orderData" />
      </div>

      <!-- Tabs 区域 -->
      <div class="tabs-section">
        <a-tabs v-model:active-key="activeTab" type="card" :justify="true">
          <a-tab-pane key="product" title="商品信息">
            <ProductInfo :order-data="orderData" />
          </a-tab-pane>
          <a-tab-pane key="cost" title="成本信息">
            <CostInfo :order-data="orderData" />
          </a-tab-pane>
          <a-tab-pane key="logistics" title="物流信息">
            <LogisticsInfo :order-data="orderData" />
          </a-tab-pane>
          <a-tab-pane key="status" title="状态信息">
            <StatusInfo :order-data="orderData" />
          </a-tab-pane>
          <a-tab-pane key="log" title="信息记录">
            <LogRecords :order-data="orderData" />
          </a-tab-pane>
          <a-tab-pane key="orderFlow" title="订单流转记录">
            <OrderFlowRecords :order-data="orderData" />
          </a-tab-pane>
          <a-tab-pane key="priceComparison" title="比较信息">
            <PriceComparisonInfo :order-data="orderData" />
          </a-tab-pane>
        </a-tabs>
      </div>

    </div>

    <!-- 加载状态 -->
    <div v-else class="loading-section">
      <a-spin size="large" />
      <div class="loading-text">加载订单详情中...</div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'

// 导入组件
import BasicInfo from '../../../orderDetail/components/BasicInfo/index.vue'
import ProductInfo from '../../../orderDetail/components/ProductInfo/index.vue'
import CostInfo from '../../../orderDetail/components/CostInfo/index.vue'
import StatusInfo from '../../../orderDetail/components/StatusInfo/index.vue'
import LogisticsInfo from '../../../orderDetail/components/LogisticsInfo/index.vue'
import PriceComparisonInfo from '../../../orderDetail/components/PriceComparisonInfo/index.vue'
import LogRecords from '../../../orderDetail/components/LogRecords/index.vue'
import OrderFlowRecords from '../../../orderDetail/components/OrderFlowRecords/index.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'edit', 'action'])

const visible = ref(props.modelValue)
const activeTab = ref('product')
const orderData = ref(null)
const loading = ref(false)

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal && props.orderId) {
    loadOrderDetail()
  }
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
  if (!newVal) {
    // 关闭时重置数据
    orderData.value = null
    activeTab.value = 'product'
  }
})

// 模拟订单数据
const mockOrderData = {
  id: 1,
  orderNumber: 'PO202312010001',
  purchaseTime: '2023-12-01 10:30:00',
  orderTime: '2023-12-01 10:00:00',
  orderSource: '商城',
  storeName: '电子产品专营店',
  deliveryAddress: '北京市朝阳区xxx大厦xxx号',
  follower: '李德斌',
  purchaser: '张三',
  orderType: '商城自然单',
  // 多商品数据结构
  products: [
    {
      id: 1,
      productImage: 'https://via.placeholder.com/120x120',
      productName: 'iPhone 15 Pro Max',
      sku: 'IPHONE15PM-256G-NT',
      productCode: 'APPLE001',
      specification: '256GB 原色钛金属',
      taxCategory: '电子产品',
      unitPrice: '8999.00',
      quantity: 2,
      totalAmount: '17998.00',
      actualCost: '7500.00',
      inspectionCost: '7600.00',
      profitRate: '15.5%'
    },
    {
      id: 2,
      productImage: 'https://via.placeholder.com/120x120',
      productName: 'MacBook Pro 14英寸',
      sku: 'MBP14-M3-512G',
      productCode: 'APPLE002',
      specification: 'M3芯片 512GB SSD',
      taxCategory: '电子产品',
      unitPrice: '15999.00',
      quantity: 1,
      totalAmount: '15999.00',
      actualCost: '13500.00',
      inspectionCost: '13800.00',
      profitRate: '12.8%'
    },
    {
      id: 3,
      productImage: 'https://via.placeholder.com/120x120',
      productName: 'AirPods Pro 2代',
      sku: 'AIRPODS-PRO2-USB-C',
      productCode: 'APPLE003',
      specification: 'USB-C充电盒',
      taxCategory: '电子产品',
      unitPrice: '1899.00',
      quantity: 3,
      totalAmount: '5697.00',
      actualCost: '1200.00',
      inspectionCost: '1250.00',
      profitRate: '25.2%'
    }
  ],
  freight: '0.00',
  totalQuantity: 6,
  totalAmount: '39694.00',
  grossProfitRate: '17.8%',
  costTotal: '32550.00',
  buyerAccount: '李德斌***********-9614',
  businessRemark: '客户要求加急处理',
  // 平台物流信息
  logisticsInfo: '顺丰快递',
  logisticsNumber: 'SF1234567890',
  purchaseProgress: '已发货',
  // 多种物流信息
  logisticsData: {
    // 平台物流
    platform: {
      type: 'platform',
      typeName: '平台物流',
      shippingCompany: '顺丰快递',
      trackingNumber: 'SF1234567890',
      shippingStatus: '已发货',
      shippedAt: '2023-12-01 14:30:00',
      estimatedDelivery: '2023-12-03 18:00:00',
      trackingData: [
        {
          status: '已发货',
          time: '2023-12-01 14:30:00',
          description: '商品已从仓库发出',
          location: '深圳仓库'
        },
        {
          status: '运输中',
          time: '2023-12-01 18:45:00',
          description: '商品正在运输途中',
          location: '运输途中'
        },
        {
          status: '派送中',
          time: '2023-12-02 09:20:00',
          description: '商品已到达目的地，正在派送',
          location: '北京朝阳区'
        }
      ]
    },
    // 拆分单物流
    split: [
      {
        type: 'split',
        typeName: '拆分单物流',
        splitOrderId: 'SP202312010001',
        productNames: ['iPhone 15 Pro Max'],
        shippingCompany: '中通快递',
        trackingNumber: 'ZTO9876543210',
        shippingStatus: '已发货',
        shippedAt: '2023-12-01 16:00:00',
        estimatedDelivery: '2023-12-04 12:00:00',
        trackingData: [
          {
            status: '已发货',
            time: '2023-12-01 16:00:00',
            description: '拆分商品已发货',
            location: '广州仓库'
          },
          {
            status: '运输中',
            time: '2023-12-02 08:30:00',
            description: '商品运输中',
            location: '运输途中'
          }
        ]
      },
      {
        type: 'split',
        typeName: '拆分单物流',
        splitOrderId: 'SP202312010002',
        productNames: ['MacBook Pro 14英寸'],
        shippingCompany: '申通快递',
        trackingNumber: 'STO5555666677',
        shippingStatus: '待发货',
        shippedAt: null,
        estimatedDelivery: '2023-12-05 15:00:00',
        trackingData: [
          {
            status: '待发货',
            time: '2023-12-01 17:00:00',
            description: '拆分订单已生成，等待发货',
            location: '上海仓库'
          }
        ]
      }
    ],
    // 补发物流
    reissue: [
      {
        type: 'reissue',
        typeName: '补发物流',
        reissueOrderId: 'RE202312010001',
        reason: '商品损坏',
        productNames: ['AirPods Pro 2代'],
        shippingCompany: '韵达快递',
        trackingNumber: 'YD8888999900',
        shippingStatus: '已发货',
        shippedAt: '2023-12-02 10:00:00',
        estimatedDelivery: '2023-12-04 16:00:00',
        trackingData: [
          {
            status: '已发货',
            time: '2023-12-02 10:00:00',
            description: '补发商品已发货',
            location: '深圳仓库'
          },
          {
            status: '运输中',
            time: '2023-12-02 15:20:00',
            description: '补发商品运输中',
            location: '运输途中'
          }
        ]
      }
    ]
  },
  actualSupplier: 'Apple官方授权商',
  suggestedSupplier: 'Apple官方授权商',
  orderStatus: '待收货',
  auditStatus: '审核通过',
  erpStatus: '已同步',
  splitOrderStatus: '未拆分',
  linkStatus: 'linked',
  // 日志记录数据
  logisticsRecords: [
    {
      title: '主订单物流单',
      createTime: '2023-12-01 14:30:00',
      logisticsCompany: '顺丰快递',
      trackingNumber: 'SF1234567890',
      status: '运输中',
      remark: '商品已发货，预计2-3天到达',
      attachments: [
        {
          name: '物流单.pdf',
          url: '/files/logistics-001.pdf'
        }
      ]
    },
    {
      title: '拆分订单物流单',
      createTime: '2023-12-01 16:00:00',
      logisticsCompany: '中通快递',
      trackingNumber: 'ZTO9876543210',
      status: '已发货',
      remark: 'iPhone 15 Pro Max 单独发货',
      attachments: [
        {
          name: '拆分物流单.pdf',
          url: '/files/logistics-002.pdf'
        }
      ]
    }
  ],
  receiptRecords: [
    {
      title: '主订单签收单',
      signTime: '2023-12-03 15:30:00',
      signedBy: '李德斌',
      signAddress: '北京市朝阳区xxx大厦xxx号',
      status: '已签收',
      remark: '商品完好无损，客户满意',
      attachments: [
        {
          name: '签收单.jpg',
          url: '/files/receipt-001.jpg'
        },
        {
          name: '商品验收单.pdf',
          url: '/files/inspection-001.pdf'
        }
      ]
    }
  ],
  delayRecords: [
    {
      title: 'MacBook Pro 延迟发货',
      delayTime: '2023-12-01 18:00:00',
      delayReason: '供应商库存不足',
      expectedShipTime: '2023-12-05 10:00:00',
      status: '处理中',
      remark: '已联系供应商，预计3天内补货发出',
      attachments: [
        {
          name: '延迟说明.docx',
          url: '/files/delay-explanation.docx'
        }
      ]
    }
  ],
  // 订单流转记录
  flowRecords: [
    {
      title: '发货',
      createTime: '2024年11月19日 17:11:57',
      status: 'success',
      reviewer: 'testAdmin',
      result: '一审通过',
      remark: ''
    },
    {
      title: '商家发货',
      createTime: '2024年11月19日 17:09:16',
      status: 'success',
      operator: '测试认款申请业务账号1'
    },
    {
      title: '商家将订单分配给子账号',
      createTime: '2024年11月19日 16:57:42',
      status: 'success',
      operator: '广东八灵科技发展有限公司',
      receiver: '测试认款申请业务账号1'
    },
    {
      title: '商家erp状态已更新为推送成功',
      createTime: '2024年10月23日 15:48:06',
      status: 'success',
      operator: '广东八灵科技发展有限公司',
      description: 'erp创建日期2024-10-21 11:48:04'
    },
    {
      title: '申请采购',
      createTime: '2024年10月16日 11:47:51',
      status: 'success',
      operator: '广东八灵科技发展有限公司'
    },
    {
      title: '商家接单',
      createTime: '2024年10月16日 11:47:38',
      status: 'success',
      operator: '广东八灵科技发展有限公司'
    },
    {
      title: '订单指派',
      createTime: '2024年10月16日 11:47:24',
      status: 'success',
      operator: 'testAdmin',
      receiver: '广东八灵科技发展有限公司'
    },
    {
      title: '商家端退回订单',
      createTime: '2024年10月16日 11:47:15',
      status: 'warning',
      operator: '广东八灵科技发展有限公司'
    },
    {
      title: '申请采购',
      createTime: '2024年10月16日 11:44:01',
      status: 'success',
      operator: '广东八灵科技发展有限公司'
    },
    {
      title: '商家接单',
      createTime: '2024年10月16日 11:43:47',
      status: 'success',
      operator: '广东八灵科技发展有限公司'
    },
    {
      title: '订单指派',
      createTime: '2024年10月16日 11:43:41',
      status: 'success',
      operator: 'testAdmin',
      receiver: '广东八灵科技发展有限公司'
    },
    {
      title: '商家端退回订单',
      createTime: '2024年10月16日 11:43:29',
      status: 'warning',
      operator: '广东八灵科技发展有限公司'
    },
    {
      title: '申请采购',
      createTime: '2024年10月16日 11:42:56',
      status: 'success',
      operator: '广东八灵科技发展有限公司'
    },
    {
      title: '商家接单',
      createTime: '2024年10月10日 19:27:01',
      status: 'success',
      operator: '金珠子账号'
    },
    {
      title: '订单指派',
      createTime: '2024年10月10日 19:20:49',
      status: 'success',
      operator: 'testAdmin',
      receiver: '广东八灵科技发展有限公司'
    },
    {
      title: '商家端退回订单',
      createTime: '2024年10月10日 19:20:39',
      status: 'warning',
      operator: '广东八灵科技发展有限公司'
    },
    {
      title: '商家接单',
      createTime: '2024年10月10日 18:20:38',
      status: 'success',
      operator: '广东八灵科技发展有限公司'
    },
    {
      title: '订单指派',
      createTime: '2024年06月29日 11:57:25',
      status: 'success',
      operator: '八灵测试中控-1A',
      receiver: '广东八灵科技发展有限公司'
    },
    {
      title: '订单录入',
      createTime: '2024年06月29日 11:57:09',
      status: 'success',
      operator: 'testAdmin',
      receiver: ''
    }
  ],
  // 操作日志
  operationLogs: [
    {
      action: '订单状态更新',
      createTime: '2024年11月19日 17:11:57',
      operator: 'testAdmin',
      module: '订单管理',
      description: '订单状态从"待发货"更新为"已发货"'
    },
    {
      action: '商品信息修改',
      createTime: '2024年11月19日 16:30:00',
      operator: '广东八灵科技发展有限公司',
      module: '商品管理',
      description: '修改商品规格信息'
    },
    {
      action: '价格调整',
      createTime: '2024年11月19日 15:45:00',
      operator: '测试认款申请业务账号1',
      module: '价格管理',
      description: '调整商品单价，原价格：8999.00，新价格：8899.00'
    },
    {
      action: '库存同步',
      createTime: '2024年11月19日 14:20:00',
      operator: '系统',
      module: '库存管理',
      description: 'ERP系统自动同步库存信息'
    }
  ]
}

// 加载订单详情
const loadOrderDetail = async () => {
  try {
    loading.value = true
    
    // 模拟API请求
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 这里应该调用真实的API
    // const response = await fetch(`/api/v1/master/supply/purchaseOrder/${props.orderId}`)
    // orderData.value = await response.json()
    
    orderData.value = mockOrderData
  } catch (error) {
    console.error('加载订单详情失败:', error)
    Message.error('加载订单详情失败')
  } finally {
    loading.value = false
  }
}

// 关闭抽屉
const handleCancel = () => {
  visible.value = false
}

// 编辑订单
const handleEdit = () => {
  emit('edit', orderData.value)
}

// 处理更多操作
const handleAction = (action) => {
  emit('action', action, orderData.value)
}
</script>

<style scoped>
.order-detail-drawer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.basic-info-section {
  flex-shrink: 0;
  margin-bottom: 20px;
}

.tabs-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tabs-section :deep(.arco-tabs-content) {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  min-height: 0; /* 重要：允许flex子项收缩 */
}

.tabs-section :deep(.arco-tabs-nav) {
  flex-shrink: 0;
}

.action-section {
  flex-shrink: 0;
  padding: 16px 0;
  border-top: 1px solid #e5e6eb;
  margin-top: 20px;
}

.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.loading-text {
  margin-top: 16px;
  color: #86909c;
}

/* 抽屉内容样式优化 */
:deep(.arco-drawer-body) {
  padding: 20px;
  height: 100%;
  overflow: hidden;
}

:deep(.arco-tabs-nav) {
  margin-bottom: 0;
}

:deep(.arco-tabs-content-item) {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 4px; /* 为滚动条留出空间 */
}

/* 自定义滚动条样式 */
:deep(.arco-tabs-content-item)::-webkit-scrollbar {
  width: 6px;
}

:deep(.arco-tabs-content-item)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(.arco-tabs-content-item)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

:deep(.arco-tabs-content-item)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保组件内容不会超出抽屉高度 */
:deep(.detail-card) {
  margin-bottom: 16px;
}

:deep(.detail-card:last-child) {
  margin-bottom: 0;
}
</style>
