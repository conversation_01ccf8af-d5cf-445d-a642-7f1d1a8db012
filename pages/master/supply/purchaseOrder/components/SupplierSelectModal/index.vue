<template>
  <a-modal
    v-model:visible="visible"
    title="选择实际供应商"
    width="800px"
    :footer="true"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="supplier-select-modal">
      <!-- 搜索区域 -->
      <div class="search-section">
        <a-input-search
          v-model="searchKeyword"
          placeholder="请输入供应商名称或编码"
          search-button
          @search="handleSearch"
          @press-enter="handleSearch"
          class="search-input"
        />
      </div>

      <!-- 供应商列表 -->
      <div class="supplier-list-section">
        <a-spin :loading="loading" style="width: 100%;">
          <div v-if="supplierList.length === 0 && !loading" class="empty-state">
            <a-empty description="暂无数据" />
          </div>
          
          <div v-else class="supplier-table">
            <div class="table-header">
              <div class="header-cell checkbox-cell">选择</div>
              <div class="header-cell code-cell">编码</div>
              <div class="header-cell name-cell">供应商名称</div>
            </div>
            
            <div class="table-body">
              <div
                v-for="supplier in supplierList"
                :key="supplier.id"
                class="table-row"
                :class="{ 'selected': selectedSupplier?.id === supplier.id }"
                @click="selectSupplier(supplier)"
              >
                <div class="body-cell checkbox-cell">
                  <a-radio
                    :model-value="selectedSupplier?.id === supplier.id"
                    @change="() => selectSupplier(supplier)"
                  />
                </div>
                <div class="body-cell code-cell">{{ supplier.code }}</div>
                <div class="body-cell name-cell">{{ supplier.name }}</div>
              </div>
            </div>
          </div>
        </a-spin>
      </div>

      <!-- 分页 -->
      <div class="pagination-section" v-if="total > 0">
        <a-pagination
          v-model:current="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :show-total="true"
          :show-jumper="true"
          :show-page-size="true"
          :page-size-options="['10', '20', '50', '100']"
          @change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm" :disabled="!selectedSupplier">
          确认
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:visible', 'confirm'])

// 响应式数据
const visible = ref(props.visible)
const loading = ref(false)
const searchKeyword = ref('')
const selectedSupplier = ref(null)
const supplierList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
  if (newVal) {
    resetModal()
    loadSupplierList()
  }
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 重置弹窗状态
const resetModal = () => {
  searchKeyword.value = ''
  selectedSupplier.value = null
  currentPage.value = 1
  pageSize.value = 10
  supplierList.value = []
  total.value = 0
}

// 加载供应商列表
const loadSupplierList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: searchKeyword.value
    }
    
    // 使用模拟数据，实际项目中替换为真实API调用
    const response = await mockGetSupplierList(params)
    
    supplierList.value = response.data.list
    total.value = response.data.total
  } catch (error) {
    console.error('加载供应商列表失败:', error)
    Message.error('加载供应商列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 模拟API调用（实际项目中替换为真实API）
const mockGetSupplierList = async (params) => {
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 500))
  
  const mockData = [
    { id: 1, code: 'SUP001', name: '深圳市科技有限公司' },
    { id: 2, code: 'SUP002', name: '北京智能科技股份有限公司' },
    { id: 3, code: 'SUP003', name: '上海电子商务有限公司' },
    { id: 4, code: 'SUP004', name: '广州制造业集团' },
    { id: 5, code: 'SUP005', name: '杭州建材集团' },
    { id: 6, code: 'SUP006', name: '成都软件开发公司' },
    { id: 7, code: 'SUP007', name: '武汉物流配送中心' },
    { id: 8, code: 'SUP008', name: '西安新能源科技' },
    { id: 9, code: 'SUP009', name: '南京医疗器械公司' },
    { id: 10, code: 'SUP010', name: '重庆汽车零部件厂' }
  ]
  
  let filteredData = mockData
  
  // 根据关键词过滤
  if (params.keyword) {
    filteredData = mockData.filter(item => 
      item.name.includes(params.keyword) || 
      item.code.includes(params.keyword)
    )
  }
  
  // 分页处理
  const start = (params.page - 1) * params.pageSize
  const end = start + params.pageSize
  const list = filteredData.slice(start, end)
  
  return {
    data: {
      list,
      total: filteredData.length
    }
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  loadSupplierList()
}

// 选择供应商
const selectSupplier = (supplier) => {
  selectedSupplier.value = supplier
}

// 分页变化处理
const handlePageChange = (page) => {
  currentPage.value = page
  loadSupplierList()
}

const handlePageSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadSupplierList()
}

// 确认选择
const handleConfirm = () => {
  if (!selectedSupplier.value) {
    Message.warning('请选择一个供应商')
    return
  }
  
  emit('confirm', {
    supplier: selectedSupplier.value,
    orderData: props.orderData
  })
  
  visible.value = false
}

// 取消
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.supplier-select-modal {
  padding: 16px 0;
}

.search-section {
  margin-bottom: 16px;
}

.search-input {
  width: 100%;
}

.supplier-list-section {
  min-height: 300px;
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.supplier-table {
  width: 100%;
}

.table-header {
  display: flex;
  background: #f7f8fa;
  border-bottom: 1px solid #e5e6eb;
  font-weight: 500;
}

.table-body {
  background: white;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.table-row:hover {
  background: #f7f8fa;
}

.table-row.selected {
  background: #e8f4ff;
}

.header-cell,
.body-cell {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  border-right: 1px solid #e5e6eb;
}

.header-cell:last-child,
.body-cell:last-child {
  border-right: none;
}

.checkbox-cell {
  width: 80px;
  justify-content: center;
}

.code-cell {
  width: 120px;
}

.name-cell {
  flex: 1;
  min-width: 200px;
}

.pagination-section {
  margin-top: 16px;
  display: flex;
  justify-content: center;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}
</style>
