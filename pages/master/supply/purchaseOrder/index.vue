<template>
  <div class="ma-content-block p-4">
    <!-- 订单表格组件 -->
    <OrderTable
      :data="mockData"
      :loading="loading"
      @view-detail="handleViewDetail"
      @order-template="handleOrderTemplate"
      @change-purchaser="handleChangePurchaser"
      @purchase-remark="handlePurchaseRemark"
      @split-product="handleSplitProduct"
      @expense-order="handleExpenseOrder"
      @request="handleRequest"
      @supplier-updated="handleSupplierUpdated"
      @tax-classification-updated="handleTaxClassificationUpdated"
      @loss-reason-updated="handleLossReasonUpdated"
      @cost-total-updated="handleCostTotalUpdated"
      @generate-link="handleGenerateLink"
      @abolish-operation="handleAbolishOperation"
      @abolish-record="handleAbolishRecord"
    />

    <!-- 订单详情抽屉 -->
    <OrderDetailDrawer
      v-model="detailVisible"
      :order-id="currentOrderId"
      @edit="handleEditOrder"
      @action="handleOrderAction"
    />

    <!-- 下单模版弹窗 -->
    <OrderTemplateModal
      v-model="templateVisible"
      :order-data="currentRecord"
      @submit="handleTemplateSubmit"
    />

    <!-- 更换采购员弹窗 -->
    <ChangePurchaserModal
      v-model="purchaserVisible"
      :order-data="currentRecord"
      @submit="handlePurchaserSubmit"
    />

    <!-- 采购备注弹窗 -->
    <PurchaseRemarkModal
      v-model="remarkVisible"
      :order-data="currentRecord"
      @submit="handleRemarkSubmit"
    />

    <!-- 拆分商品抽屉 -->
    <SplitProductModal
      v-model="splitVisible"
      :product-data="currentSplitProduct"
      @submit="handleSplitSubmit"
    />

    <!-- 费用单弹窗 -->
    <ExpenseOrderModal
      v-model="expenseVisible"
      :order-data="currentRecord"
      @submit="handleExpenseSubmit"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'

// 导入组件
import OrderTable from './components/OrderTable/index.vue'
import OrderDetailDrawer from './components/OrderDetailDrawer/index.vue'
import OrderTemplateModal from './components/OrderTemplateModal/index.vue'
import ChangePurchaserModal from './components/ChangePurchaserModal/index.vue'
import PurchaseRemarkModal from './components/PurchaseRemarkModal/index.vue'
import SplitProductModal from './components/SplitProductModal/index.vue'
import ExpenseOrderModal from './components/ExpenseOrderModal/index.vue'

// 路由实例
const router = useRouter()

// 页面状态
const loading = ref(false)
// 定义页面路由元信息
definePageMeta({
  name: "purchaseOrder",
  path: "/master/supply/purchaseOrder",
});
// 弹窗控制
const detailVisible = ref(false)
const templateVisible = ref(false)
const purchaserVisible = ref(false)
const remarkVisible = ref(false)
const splitVisible = ref(false)
const expenseVisible = ref(false)

// 当前操作的记录
const currentRecord = ref(null)
const currentOrderId = ref(null)
const currentSplitProduct = ref(null)

// 模拟数据
const mockData = ref([
  {
    id: 1,
    orderNumber: 'PO202312010001',
    purchaseTime: '2023-12-01 10:30:00',
    orderTime: '2023-12-01 10:00:00',
    orderSource: '商城',
    storeName: '电子产品专营店',
    deliveryAddress: '北京市朝阳区xxx大厦xxx号',
    follower: '李德斌',
    purchaser: '张三',
    orderType: '商城自然单',
    // 多商品数据结构
    products: [
      {
        id: 1,
        productImage: 'https://via.placeholder.com/60x60',
        productName: 'iPhone 15 Pro Max',
        sku: 'IPHONE15PM-256G-NT',
        productCode: 'APPLE001',
        specification: '256GB 原色钛金属',
        taxCategory: '电子产品',
        unitPrice: '8999.00',
        quantity: 2,
        totalAmount: '17998.00',
        actualCost: '7500.00',
        inspectionCost: '7600.00',
        profitRate: '15.5%',
        productLineExpireTime: '2024-12-31 23:59:59', // 产品线过期时间
        actualSupplier: '苹果官方供应商',
        suggestedSupplier: '建议供应商A',
        costTotal: '15200.00' // 商品成本总价
      }
    ],
    freight: '0.00',
    totalQuantity: 2,
    totalAmount: '17998.00',
    grossProfitRate: '15.5%',
    costTotal: '15200.00',
    buyerAccount: '李德斌***********-9614',
    businessRemark: '客户要求加急处理',
    logisticsInfo: '顺丰快递',
    purchaseProgress: '已下单',
    purchaseRemark: '优先发货',
    actualSupplier: 'Apple官方授权经销商',
    suggestedSupplier: 'Apple官方授权经销商',
    erpStatus: '已同步',
    logisticsNumber: 'SF1234567890',
    splitOrderStatus: '未拆分',
    auditStatus: '审核通过',
    orderStatus: '待发货',
    cancelStatus: null,
    pendingCancelStatus: null,
    linkStatus: 'linked',
    recipientName: '李德斌',
    contactPhone: '***********',
    orderAddress: '北京市朝阳区xxx大厦xxx号',
    isLoss: 'no',
    linkGenerated: false // 未生成链接
  },
  {
    id: 2,
    orderNumber: 'PO202312010002',
    purchaseTime: '2023-12-01 14:20:00',
    orderTime: '2023-12-01 14:00:00',
    orderSource: '竞价',
    storeName: '数码配件店',
    deliveryAddress: '上海市浦东新区xxx路xxx号',
    follower: '王小明',
    purchaser: '李四',
    orderType: '竞价自然单',
    // 多商品数据结构
    products: [
      {
        id: 2,
        productImage: 'https://via.placeholder.com/60x60',
        productName: 'Samsung Galaxy S24 Ultra',
        sku: 'SAMSUNG-S24U-512G-BK',
        productCode: 'SAMSUNG002',
        specification: '512GB 钛金黑',
        // taxCategory: '电子产品', // 注释掉，模拟未选择税收分类
        unitPrice: '6999.00',
        quantity: 1,
        totalAmount: '6999.00',
        actualCost: '6100.00',
        inspectionCost: '6200.00',
        profitRate: '12.8%',
        productLineExpireTime: '2024-06-20 23:59:59', // 产品线已过期
        actualSupplier: '三星授权经销商',
        suggestedSupplier: '建议供应商B',
        costTotal: '6200.00' // 商品成本总价
      }
    ],
    freight: '15.00',
    totalQuantity: 1,
    totalAmount: '6999.00',
    grossProfitRate: '-5.2%',
    costTotal: '7500.00',
    lossReason: 'freight_loss',
    buyerAccount: '王小明***********-1234',
    businessRemark: '客户要求包装加固',
    logisticsInfo: '中通快递',
    purchaseProgress: '待发货',
    purchaseRemark: '注意防潮包装',
    actualSupplier: '三星官方授权商',
    suggestedSupplier: '三星官方授权商',
    erpStatus: '待同步',
    logisticsNumber: '-',
    splitOrderStatus: '未拆分',
    auditStatus: '待审核',
    orderStatus: '待发货',
    cancelStatus: null,
    pendingCancelStatus: '采购员待审核',
    linkStatus: 'unlinked',
    recipientName: '王小明',
    contactPhone: '***********',
    orderAddress: '上海市浦东新区xxx路xxx号',
    isLoss: 'no',
    linkGenerated: false // 未生成链接（因为有商品未选择税收分类）
  },
  {
    id: 3,
    orderNumber: 'PO202312010003',
    purchaseTime: '2023-12-01 16:45:00',
    orderTime: '2023-12-01 16:30:00',
    orderSource: '手动',
    storeName: '智能家居店',
    deliveryAddress: '广州市天河区xxx广场xxx号',
    follower: '陈小红',
    purchaser: '王五',
    orderType: '手动创建单',
    // 多商品数据结构 - 包含多个商品的示例
    products: [
      {
        id: 3,
        productImage: 'https://via.placeholder.com/60x60',
        productName: 'Xiaomi 14 Pro',
        sku: 'XIAOMI-14P-256G-WH',
        productCode: 'XIAOMI003',
        specification: '256GB 雪山粉',
        taxCategory: '电子产品',
        unitPrice: '4999.00',
        quantity: 2,
        totalAmount: '9998.00',
        actualCost: '4100.00',
        inspectionCost: '4200.00',
        profitRate: '18.2%',
        productLineExpireTime: '2025-03-15 23:59:59', // 产品线未过期
        actualSupplier: '小米官方渠道',
        suggestedSupplier: '建议供应商C',
        costTotal: '8200.00' // 商品成本总价
      },
      {
        id: 4,
        productImage: 'https://via.placeholder.com/60x60',
        productName: 'Xiaomi Pad 6',
        sku: 'XIAOMI-PAD6-128G-GY',
        productCode: 'XIAOMI004',
        specification: '128GB 薄荷绿',
        // taxCategory: '电子产品', // 注释掉，模拟未选择税收分类
        unitPrice: '2499.00',
        quantity: 1,
        totalAmount: '2499.00',
        actualCost: '2000.00',
        inspectionCost: '2100.00',
        profitRate: '16.0%',
        productLineExpireTime: '2024-05-10 23:59:59', // 产品线已过期
        actualSupplier: '小米平板供应商',
        suggestedSupplier: '建议供应商D',
        costTotal: '2100.00' // 商品成本总价
      }
    ],
    freight: '25.00',
    totalQuantity: 3,
    totalAmount: '12497.00',
    grossProfitRate: '17.5%',
    costTotal: '12300.00',
    buyerAccount: '陈小红***********-5678',
    businessRemark: '批量采购优惠，VIP客户',
    logisticsInfo: '申通快递',
    purchaseProgress: '已收货',
    purchaseRemark: '质量检验合格，客户满意',
    actualSupplier: '小米官方旗舰店',
    suggestedSupplier: '小米官方旗舰店',
    erpStatus: '已同步',
    logisticsNumber: 'YTO9876543210',
    splitOrderStatus: '已拆分',
    auditStatus: '审核通过',
    orderStatus: '已关闭',
    cancelStatus: '已废止',
    pendingCancelStatus: null,
    linkStatus: 'unlinked',
    recipientName: '陈小红',
    contactPhone: '***********',
    orderAddress: '广州市天河区xxx广场xxx号',
    isLoss: 'no',
    linkGenerated: false // 未生成链接（因为有商品未选择税收分类）
  },
  {
    id: 4,
    orderNumber: 'PO202312010004',
    purchaseTime: '2023-12-02 09:30:00',
    orderTime: '2023-12-02 09:00:00',
    orderSource: '竞价',
    storeName: '数码专营店',
    deliveryAddress: '深圳市南山区xxx科技园xxx号',
    follower: '李七',
    purchaser: '赵六',
    orderType: '竞价自然单',
    // 多商品数据结构
    products: [
      {
        id: 5,
        productImage: 'https://via.placeholder.com/60x60',
        productName: 'OPPO Find X7 Pro',
        sku: 'OPPO-FX7P-256G-BL',
        productCode: 'OPPO004',
        specification: '256GB 星夜黑',
        taxCategory: '电子产品',
        unitPrice: '4999.00',
        quantity: 1,
        totalAmount: '4999.00',
        actualCost: '4500.00',
        inspectionCost: '4550.00',
        profitRate: '10.5%',
        productLineExpireTime: '2024-06-01 23:59:59', // 产品线已过期
        actualSupplier: 'OPPO官方供应商',
        suggestedSupplier: '建议供应商E',
        costTotal: '4550.00' // 商品成本总价
      }
    ],
    freight: '20.00',
    totalQuantity: 1,
    totalAmount: '4999.00',
    grossProfitRate: '-8.1%',
    costTotal: '5400.00',
    lossReason: 'quote_error',
    buyerAccount: '刘小华***********-7890',
    businessRemark: '客户要求退货，产品不符合预期',
    logisticsInfo: '韵达快递',
    purchaseProgress: '申请废止',
    purchaseRemark: '客户不满意商品质量，申请全额退款',
    actualSupplier: 'OPPO官方授权商',
    suggestedSupplier: 'OPPO官方授权商',
    erpStatus: '已同步',
    logisticsNumber: 'YD2345678901',
    splitOrderStatus: '未拆分',
    auditStatus: '废止待确认',
    orderStatus: '废止待确认',
    cancelStatus: null,
    pendingCancelStatus: '废止待确认',
    linkStatus: 'linked',
    recipientName: '刘小华',
    contactPhone: '***********',
    orderAddress: '深圳市南山区xxx科技园xxx号',
    isLoss: 'yes',
    linkGenerated: false // 未生成链接
  }
])

// 初始化时检查并修正链接生成状态
const initializeLinkStatus = () => {
  mockData.value.forEach(order => {
    const hasAllTax = checkAllTaxClassifications(order)
    if (!hasAllTax) {
      order.linkGenerated = false
      order.linkStatus = 'unlinked'
    }
  })
}

// 在组件挂载时执行初始化检查
onMounted(() => {
  initializeLinkStatus()
})

// 请求处理
const handleRequest = (params) => {
  console.log('请求参数:', params)
  loading.value = true

  // 模拟API请求
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

// 操作事件处理函数
const handleViewDetail = (record) => {
  // 打开详情抽屉
  currentRecord.value = record
  currentOrderId.value = record.id
  detailVisible.value = true
}

const handleOrderTemplate = (record) => {
  currentRecord.value = record
  templateVisible.value = true
}

const handleChangePurchaser = (record) => {
  currentRecord.value = record
  purchaserVisible.value = true
}

const handlePurchaseRemark = (record) => {
  currentRecord.value = record
  remarkVisible.value = true
}

const handleSplitProduct = (record) => {
  currentRecord.value = record

  // 如果订单有多个商品，选择第一个商品进行拆分
  // 在实际应用中，可能需要弹出商品选择对话框
  if (record.products && record.products.length > 0) {
    currentSplitProduct.value = record.products[0]
  } else {
    // 如果是单商品订单，直接使用订单数据
    currentSplitProduct.value = {
      id: record.id,
      productName: record.productName || '未知商品',
      productCode: record.productCode || record.orderNumber,
      sku: record.sku || record.orderNumber,
      specification: record.specification || '标准规格',
      productImage: record.productImage || '',
      quantity: record.totalQuantity || 1,
      unitPrice: record.unitPrice || '0.00'
    }
  }

  splitVisible.value = true
}

const handleExpenseOrder = (record) => {
  currentRecord.value = record
  expenseVisible.value = true
}

// 表单提交处理
const handleTemplateSubmit = (data) => {
  console.log('保存下单模版:', data)
  Message.success('下单模版保存成功')
}

const handlePurchaserSubmit = (data) => {
  console.log('更换采购员:', data)
  Message.success('采购员更换成功')
}

const handleRemarkSubmit = (data) => {
  console.log('更新采购备注:', data)
  Message.success('采购备注更新成功')
}

const handleSplitSubmit = (data) => {
  console.log('拆分商品:', data)
  Message.success('商品拆分成功')
}

const handleExpenseSubmit = (data) => {
  console.log('创建费用单:', data)
  Message.success('费用单创建成功')
}

// 处理订单编辑
const handleEditOrder = (orderData) => {
  console.log('编辑订单:', orderData)
  Message.info('编辑功能开发中...')
}

// 处理订单操作
const handleOrderAction = (action, orderData) => {
  const actionMap = {
    template: '下单模版',
    purchaser: '更换采购员',
    remark: '采购备注',
    split: '拆分商品',
    expense: '费用单'
  }

  console.log(`执行操作: ${actionMap[action]}`, orderData)

  // 根据操作类型打开对应的弹窗
  switch (action) {
    case 'template':
      handleOrderTemplate(orderData)
      break
    case 'purchaser':
      handleChangePurchaser(orderData)
      break
    case 'remark':
      handlePurchaseRemark(orderData)
      break
    case 'split':
      handleSplitProduct(orderData)
      break
    case 'expense':
      handleExpenseOrder(orderData)
      break
    default:
      Message.info(`${actionMap[action]}功能开发中...`)
  }
}

// 处理供应商更新
const handleSupplierUpdated = ({ orderData, supplier }) => {
  console.log('供应商更新成功:', { orderData, supplier })

  // 更新本地数据
  const index = mockData.value.findIndex(item => item.id === orderData.id)
  if (index !== -1) {
    mockData.value[index].actualSupplier = supplier.name
    mockData.value[index].actualSupplierId = supplier.id
  }
}

// 处理税收分类更新
const handleTaxClassificationUpdated = ({ productData, taxClassification, orderData }) => {
  console.log('税收分类更新成功:', { productData, taxClassification, orderData })

  // 更新本地数据
  const orderIndex = mockData.value.findIndex(item => item.id === orderData.id)

  if (orderIndex !== -1) {
    const order = mockData.value[orderIndex]

    // 如果是多商品订单
    if (order.products && order.products.length > 0) {
      const productIndex = order.products.findIndex(p => p.id === productData.id)
      if (productIndex !== -1) {
        order.products[productIndex].taxClassification = taxClassification
      }
    }
    // 如果是单商品订单
    else if (order.id === productData.id) {
      order.taxClassification = taxClassification
    }

    // 检查是否所有商品都已选择税收分类，如果不是则重置链接生成状态
    const hasAllTax = checkAllTaxClassifications(order)
    if (!hasAllTax) {
      order.linkGenerated = false
      order.linkStatus = 'unlinked'
    }
  }
}

// 检查订单中所有商品是否都已选择税收分类的辅助函数
const checkAllTaxClassifications = (order) => {
  if (!order) return false

  // 如果是多商品订单
  if (order.products && Array.isArray(order.products) && order.products.length > 0) {
    return order.products.every(product =>
      product.taxCategory || product.taxClassification?.label
    )
  }

  // 如果是单商品订单
  return !!(order.taxCategory || order.taxClassification?.label)
}

// 处理亏损原因更新
const handleLossReasonUpdated = ({ orderData, lossReason }) => {
  console.log('亏损原因更新成功:', { orderData, lossReason })

  // 更新本地数据
  const index = mockData.value.findIndex(item => item.id === orderData.id)
  if (index !== -1) {
    mockData.value[index].lossReason = lossReason
  }
}

// 处理成本总价更新
const handleCostTotalUpdated = ({ orderData, costTotal }) => {
  console.log('成本总价更新成功:', { orderData, costTotal })

  // 更新本地数据
  const index = mockData.value.findIndex(item => item.id === orderData.id)
  if (index !== -1) {
    mockData.value[index].costTotal = costTotal

    // 重新计算毛利率
    const totalAmount = parseFloat(mockData.value[index].totalAmount || 0)
    const cost = parseFloat(costTotal || 0)
    if (totalAmount > 0) {
      const profitRate = ((totalAmount - cost) / totalAmount * 100).toFixed(1)
      mockData.value[index].grossProfitRate = `${profitRate}%`
    }
  }
}

// 处理生成链接
const handleGenerateLink = (updatedOrder) => {
  console.log('生成链接:', updatedOrder)

  // 更新订单数据
  const index = mockData.value.findIndex(item => item.id === updatedOrder.id)
  if (index !== -1) {
    // 更新订单信息
    mockData.value[index] = {
      ...mockData.value[index],
      ...updatedOrder,
      linkStatus: 'linked' // 更新链接状态用于搜索筛选
    }
  }

  console.log('订单链接状态已更新:', mockData.value[index])
}

// 处理废止操作
const handleAbolishOperation = (record) => {
  console.log('废止操作:', record)
  Message.info('废止操作弹窗已打开')
}

// 处理废止记录
const handleAbolishRecord = (record) => {
  console.log('废止记录:', record)
  Message.info('废止记录弹窗已打开')
}
</script>

<style scoped>
.ma-content-block {
  background-color: #ffffff;
  border-radius: var(--border-radius-medium);
}
</style>
