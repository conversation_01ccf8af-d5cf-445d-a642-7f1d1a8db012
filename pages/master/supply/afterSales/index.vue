<template>
  <div class="ma-content-block p-4">
    <!-- 使用ma-crud表格组件 -->
    <SimpleTable
      :data="mockData"
      @detail="handleDetail"
      @reply="handleReply"
      @remark="handleAfterSalesRemark"
      @history="handleReplyHistory"
      @change-staff="handleChangeStaff"
      @copy-issue="handleCopyIssue"
      @request="handleRequest"
      @status-change="handleStatusChange"
    />

    <!-- 详情抽屉 -->
    <OrderDetailDrawer
      v-model="detailVisible"
      :order-id="currentOrderId"
      @edit="handleEditOrder"
      @action="handleOrderAction"
    />

    <!-- 回复弹窗 -->
    <ReplyModal
      v-model="replyVisible"
      :after-sales-data="currentRecord"
      @submit="handleReplySubmit"
    />

    <!-- 售后备注弹窗 -->
    <AfterSalesRemarkModal
      v-model="remarkVisible"
      :after-sales-data="currentRecord"
      @submit="handleRemarkSubmit"
    />

    <!-- 回复历史弹窗 -->
    <ReplyHistoryModal
      v-model="historyVisible"
      :after-sales-data="currentRecord"
    />

    <!-- 更换售后员弹窗 -->
    <ChangeStaffModal
      v-model="staffVisible"
      :after-sales-data="currentRecord"
      @submit="handleStaffSubmit"
    />

    <!-- 复制问题弹窗 -->
    <CopyIssueModal
      v-model="copyVisible"
      :after-sales-data="currentRecord"
      @submit="handleCopySubmit"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'

// 导入组件
import SimpleTable from './components/AfterSalesTable/simple.vue'
import OrderDetailDrawer from './components/OrderDetailDrawer/index.vue'
import ReplyModal from './components/ReplyModal/index.vue'
import AfterSalesRemarkModal from './components/AfterSalesRemarkModal/index.vue'
import ReplyHistoryModal from './components/ReplyHistoryModal/index.vue'
import ChangeStaffModal from './components/ChangeStaffModal/index.vue'
import CopyIssueModal from './components/CopyIssueModal/index.vue'

// 定义页面路由元信息
definePageMeta({
  name: "afterSales",
  path: "/master/supply/afterSales",
});

// 路由实例
const router = useRouter()

// 页面状态
const loading = ref(false)

// 弹窗控制
const detailVisible = ref(false)
const replyVisible = ref(false)
const remarkVisible = ref(false)
const historyVisible = ref(false)
const staffVisible = ref(false)
const copyVisible = ref(false)

// 当前操作的记录
const currentRecord = ref({})
const currentOrderId = ref(null)

// 模拟数据
const mockData = [
  {
    id: 1,
    orderNumber: '319444034949',
    afterSalesNumber: '795705209920716800',
    orderType: '商城自然单',
    afterSalesTime: '2025年06月23日 17:28:25',
    replyTime: '2025年06月23日 17:31:09',
    orderSource: '京东',
    // 支持多商品格式
    products: [
      {
        name: '灵龙八方硅胶仿真娃娃培训教学婴儿模型小儿针灸实训模具GB36O1-基础款',
        code: '100122791581',
        image: 'https://img.alicdn.com/imgextra/i1/2208857268292/O1CN01QXJYjM1rLQJ5QJ5Qj_!!2208857268292.jpg',
        quantity: 2,
        salePrice: '1085.12',
        costPrice: '640'
      },
      {
        name: '医用听诊器双头听诊器成人儿童心肺音听诊器',
        code: '100122791582',
        image: 'https://img.alicdn.com/imgextra/i2/2208857268292/O1CN01XYZ123rLQJ5QJ5Qk_!!2208857268292.jpg',
        quantity: 1,
        salePrice: '89.00',
        costPrice: '45.00'
      }
    ],
    // 兼容旧格式
    productName: '灵龙八方硅胶仿真娃娃培训教学婴儿模型小儿针灸实训模具GB36O1-基础款',
    productCode: '100122791581',
    productImage: 'https://img.alicdn.com/imgextra/i1/2208857268292/O1CN01QXJYjM1rLQJ5QJ5Qj_!!2208857268292.jpg',
    quantity: 2,
    salePrice: '1085.12',
    costPrice: '640',
    deliveryAddress: '广东广州市天河区龙洞街道广州市天河区龙洞迎龙路481号-62736',
    isOwnBrand: '是',
    actualSupplier: '灵龙八方医疗器械有限公司',
    afterSalesType: '是',
    afterSalesContent: '产品存在质量缺陷，客户要求退换货',
    afterSalesStatus: '处理中',
    afterSalesProgress: '已联系供应商',
    businessReturn: '是',
    customerServiceReturn: '否',
    returnAddress: '广东省广州市天河区退货仓库',
    returnNumber: 'RT202506230001',
    customerDemand: '要求全额退款并赔偿损失',
    attachments: ['complaint1.jpg', 'complaint2.jpg'],
    replyContent: '已联系供应商核实情况，将在3个工作日内给出处理方案',
    replyAttachments: ['reply1.pdf'],
    responsibility: '供应商责任',
    erpSystem: '已同步',
    remark: '客户情绪激动，需要优先处理',
    remarkAttachments: [
      {
        name: 'remark_doc1.pdf',
        url: '/files/remark_doc1.pdf',
        size: 1024000,
        type: 'application/pdf'
      },
      {
        name: 'remark_image1.jpg',
        url: '/files/remark_image1.jpg',
        size: 512000,
        type: 'image/jpeg'
      }
    ],
    status: 'processing', // all, pending, processing, completed
    // 新增字段用于回复组件
    productBrand: '广东八天科技发展有限公司',
    sourceStore: '供应链-胡晓梅',
    afterSalesStore: '供应链-胡晓梅',
    proposer: '广东八天科技发展有限公司',
    follower: '张三',
    salesDepartment: '华南销售部',
    afterSalesStaff: '李四',
    purchaser: '王五',
    actualDeliveryAddress: '广东广州市天河区龙洞街道广州市天河区龙洞迎龙路481号-62736',
    shippingNumber: 'SF1234567890'
  },
  {
    id: 2,
    orderNumber: '319444034950',
    afterSalesNumber: '795705209920716801',
    orderType: '竞价自然单',
    afterSalesTime: '2025年06月22日 14:15:30',
    replyTime: '2025年06月22日 16:20:45',
    orderSource: '天猫',
    // 单商品格式
    productName: '智能血压计家用全自动上臂式电子血压测量仪',
    productCode: '100122791582',
    productImage: 'https://img.alicdn.com/imgextra/i3/2208857268292/O1CN01ABC123rLQJ5QJ5Ql_!!2208857268292.jpg',
    quantity: 1,
    salePrice: '299.00',
    costPrice: '180',
    deliveryAddress: '北京市朝阳区建国路88号SOHO现代城',
    isOwnBrand: '否',
    actualSupplier: '欧姆龙医疗器械有限公司',
    afterSalesType: '功能异常',
    afterSalesContent: '血压计显示屏出现花屏现象，无法正常使用',
    afterSalesStatus: '已完成',
    afterSalesProgress: '已换货完成',
    businessReturn: '否',
    customerServiceReturn: '是',
    returnAddress: '北京市朝阳区退货中心',
    returnNumber: 'RT202506220001',
    customerDemand: '要求换货',
    attachments: ['screen_issue.jpg'],
    replyContent: '已安排换货，新产品将在2个工作日内发出',
    replyAttachments: ['exchange_notice.pdf'],
    responsibility: '产品质量问题',
    erpSystem: '已同步',
    remark: '客户满意度较高，处理及时',
    remarkAttachments: [
      {
        name: 'remark_doc2.docx',
        url: '/files/remark_doc2.docx',
        size: 768000,
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      }
    ],
    status: 'completed',
    // 新增字段用于回复组件
    productBrand: '欧姆龙医疗器械有限公司',
    sourceStore: '供应链-赵六',
    afterSalesStore: '供应链-赵六',
    proposer: '欧姆龙医疗器械有限公司',
    follower: '赵六',
    salesDepartment: '华北销售部',
    afterSalesStaff: '钱七',
    purchaser: '孙八',
    actualDeliveryAddress: '北京市朝阳区建国路88号SOHO现代城',
    shippingNumber: 'YTO9876543210'
  }
]

// 请求处理
const handleRequest = (params) => {
  console.log('请求参数:', params)
  loading.value = true

  // 模拟API请求
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

// 操作事件处理函数
const handleDetail = (record) => {
  currentRecord.value = record
  currentOrderId.value = record.id
  detailVisible.value = true
}

const handleReply = (record) => {
  console.log('主页面 - 处理回复事件:', record)
  currentRecord.value = record
  replyVisible.value = true
  console.log('主页面 - replyVisible设置为:', replyVisible.value)
}

const handleAfterSalesRemark = (record) => {
  console.log('主页面 - 处理备注事件:', record)
  currentRecord.value = record
  remarkVisible.value = true
  console.log('主页面 - remarkVisible设置为:', remarkVisible.value)
}

const handleReplyHistory = (record) => {
  console.log('主页面 - 处理历史事件:', record)
  currentRecord.value = record
  historyVisible.value = true
  console.log('主页面 - historyVisible设置为:', historyVisible.value)
}

const handleChangeStaff = (record) => {
  console.log('主页面 - 处理更换售后员事件:', record)
  currentRecord.value = record
  staffVisible.value = true
  console.log('主页面 - staffVisible设置为:', staffVisible.value)
}

const handleCopyIssue = (record) => {
  console.log('主页面 - 处理复制问题事件:', record)
  currentRecord.value = record
  copyVisible.value = true
  console.log('主页面 - copyVisible设置为:', copyVisible.value)
}

// 表单提交处理
const handleReplySubmit = (data) => {
  console.log('提交回复:', data)
  Message.success('回复提交成功')
}

const handleRemarkSubmit = (data) => {
  console.log('更新售后备注:', data)
  Message.success('售后备注更新成功')
}

const handleStaffSubmit = (data) => {
  console.log('更换售后员:', data)
  Message.success('售后员更换成功')
}

const handleCopySubmit = (data) => {
  console.log('复制问题:', data)
  Message.success('问题复制成功')
}

// 处理订单编辑
const handleEditOrder = (orderData) => {
  console.log('编辑订单:', orderData)
  Message.info('编辑功能开发中...')
}

// 处理订单操作
const handleOrderAction = (action, orderData) => {
  const actionMap = {
    remark: '售后备注',
    history: '回复历史',
    staff: '更换售后员',
    copy: '复制问题'
  }

  console.log(`执行操作: ${actionMap[action]}`, orderData)

  // 根据操作类型打开对应的弹窗
  switch (action) {
    case 'remark':
      handleAfterSalesRemark(orderData)
      break
    case 'history':
      handleReplyHistory(orderData)
      break
    case 'staff':
      handleChangeStaff(orderData)
      break
    case 'copy':
      handleCopyIssue(orderData)
      break
    default:
      Message.info(`${actionMap[action]}功能开发中...`)
  }
}

// 处理售后状态变更
const handleStatusChange = (data) => {
  console.log('售后状态已变更:', data)

  // 可以在这里添加额外的逻辑，比如：
  // 1. 刷新列表数据
  // 2. 记录操作日志
  // 3. 发送通知等

  // 如果需要刷新数据，可以调用：
  // handleRequest({})
}


</script>

<style scoped>
.ma-content-block {
  background-color: #ffffff;
  border-radius: var(--border-radius-medium);
}

.test-content {
  padding: 20px;
}

.data-item {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fafafa;
}

.data-item p {
  margin: 8px 0;
}

.data-item button {
  margin-right: 8px;
}
</style>