<template>
  <div class="test-table">
    <ma-crud
      ref="crudRef"
      :options="crudOptions"
      :columns="columns"
    >
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const crudRef = ref()

// 最简单的API函数
const testApi = async () => {
  return {
    data: [
      {
        id: 1,
        name: '测试数据1',
        status: '正常'
      },
      {
        id: 2,
        name: '测试数据2',
        status: '异常'
      }
    ],
    total: 2
  }
}

// 最简单的配置
const crudOptions = reactive({
  api: testApi,
  add: false,
  edit: false,
  delete: false,
  search: false
})

// 最简单的列配置
const columns = [
  {
    title: "ID",
    dataIndex: "id",
    width: 100
  },
  {
    title: "名称",
    dataIndex: "name",
    width: 200
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 100
  }
]
</script>

<style scoped>
.test-table {
  padding: 20px;
}
</style>
