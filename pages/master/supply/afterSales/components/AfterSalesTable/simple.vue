<template>
  <div class="simple-table">
    <!-- ma-crud配置 - 添加多个插槽 -->
    <ma-crud
      ref="crudRef"
      :options="crudOptions"
      :columns="columns"
    >
      <!-- 订单详情插槽 -->
      <template #orderInfo="{ record }">
        <div class="order-info-card">
          <div class="info-row">
            <span class="label">订单编号：</span>
            <span>{{ record.orderNumber }}</span>
          </div>
          <div class="info-row">
            <span class="label">售后编号：</span>
            <span>{{ record.afterSalesNumber }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单类型：</span>
            <span>{{ record.orderType }}</span>
          </div>
          <div class="info-row">
            <span class="label">售后时间：</span>
            <span>{{ record.afterSalesTime }}</span>
          </div>
          <div class="info-row">
            <span class="label">回复时间：</span>
            <span>{{ record.replyTime }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单来源：</span>
            <span>{{ record.orderSource }}</span>
          </div>
        </div>
      </template>

      <!-- 商品详情插槽 - 支持多商品和图片 -->
      <template #productInfo="{ record }">
        <div class="product-info-card">
          <!-- 如果是多个商品 -->
          <div v-if="record.products && record.products.length > 0" class="multiple-products">
            <div v-for="(product, index) in record.products" :key="index" class="product-item">
              <div class="product-header">
                <img
                  :src="product.image || '/not-image.png'"
                  :alt="product.name"
                  class="product-image"
                  @error="handleImageError"
                />
                <div class="product-basic-info">
                  <div class="product-name">{{ product.name }}</div>
                  <div class="product-code">商品编码：{{ product.code }}</div>
                </div>
              </div>
              <div class="product-details">
                <div class="detail-row">
                  <span class="detail-label">数量：</span>
                  <span>{{ product.quantity }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">销售价：</span>
                  <span class="price">¥{{ product.salePrice }}</span>
                </div>
                <div class="detail-row">
                  <span class="detail-label">成本价：</span>
                  <span class="price">¥{{ product.costPrice }}</span>
                </div>
              </div>
              <div v-if="index < record.products.length - 1" class="product-divider"></div>
            </div>
            <div class="delivery-address">
              <span class="address-label">实际收货地址：</span>
              <span>{{ record.deliveryAddress }}</span>
            </div>
          </div>

          <!-- 如果是单个商品（兼容旧数据格式） -->
          <div v-else class="single-product">
            <div class="product-header">
              <img
                :src="record.productImage || '/not-image.png'"
                :alt="record.productName"
                class="product-image"
                @error="handleImageError"
              />
              <div class="product-basic-info">
                <div class="product-name">{{ record.productName }}</div>
                <div class="product-code">商品编码：{{ record.productCode }}</div>
              </div>
            </div>
            <div class="product-details">
              <div class="detail-row">
                <span class="detail-label">数量：</span>
                <span>{{ record.quantity }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">销售价：</span>
                <span class="price">¥{{ record.salePrice }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">成本价：</span>
                <span class="price">¥{{ record.costPrice }}</span>
              </div>
            </div>
            <div class="delivery-address">
              <span class="address-label">实际收货地址：</span>
              <span>{{ record.deliveryAddress }}</span>
            </div>
          </div>
        </div>
      </template>

      <!-- 售后信息插槽 - 包含实际供应商和是否为自主品牌 -->
      <template #afterSalesInfo="{ record }">
        <div class="after-sales-info-card">
          <div class="info-item">
            <span class="info-label">售后类型：</span>
            <span>{{ record.afterSalesType }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">售后内容：</span>
            <span>{{ record.afterSalesContent }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">售后状态：</span>
            <a-select
              :model-value="record.afterSalesStatus"
              @change="(value) => handleStatusChange(record, value)"
              :style="{ width: '120px' }"
              size="small"
            >
              <a-option value="待处理">待处理</a-option>
              <a-option value="处理中">处理中</a-option>
              <a-option value="已完成">已完成</a-option>
              <a-option value="已关闭">已关闭</a-option>
            </a-select>
          </div>
          <div class="info-item">
            <span class="info-label">售后进度：</span>
            <a-select
              :model-value="record.afterSalesProgress"
              @change="(value) => handleProgressChange(record, value)"
              :style="{ width: '140px' }"
              size="small"
            >
              <a-option value="跟进补发单号">跟进补发单号</a-option>
              <a-option value="跟进退货">跟进退货</a-option>
              <a-option value="跟进退款">跟进退款</a-option>
              <a-option value="跟进换货">跟进换货</a-option>
              <a-option value="跟进维修结果">跟进维修结果</a-option>
              <a-option value="待客户回复">待客户回复</a-option>
              <a-option value="待供应商回复">待供应商回复</a-option>
              <a-option value="补发维修中">补发维修中</a-option>
              <a-option value="理赔中">理赔中</a-option>
              <a-option value="跟进安装结果">跟进安装结果</a-option>
              <a-option value="跟进物流派送">跟进物流派送</a-option>
            </a-select>
          </div>
          <div class="info-item">
            <span class="info-label">是否自主品牌：</span>
            <a-tag :color="record.isOwnBrand === '是' ? 'green' : 'blue'">
              {{ record.isOwnBrand }}
            </a-tag>
          </div>
          <div class="info-item">
            <span class="info-label">实际供应商：</span>
            <span>{{ record.actualSupplier }}</span>
          </div>
        </div>
      </template>



      <!-- 是否退货插槽 -->
      <template #returnInfo="{ record }">
        <div class="return-info-card">
          <div class="return-item">
            <span class="return-label">业务-是否退货：</span>
            <a-tag :color="record.businessReturn === '是' ? 'green' : 'gray'">
              {{ record.businessReturn }}
            </a-tag>
          </div>
          <div class="return-item">
            <span class="return-label">客服-是否退货：</span>
            <a-tag :color="record.customerServiceReturn === '是' ? 'green' : 'gray'">
              {{ record.customerServiceReturn }}
            </a-tag>
          </div>
          <div class="return-item">
            <span class="return-label">退货地址：</span>
            <a-textarea
              :auto-size="{ minRows: 2, maxRows: 4 }"
              :model-value="record.returnAddress || ''"
              @input="(value) => handleReturnAddressInput(record, value)"
              @blur="(e) => handleReturnAddressChange(record, e.target.value)"
              :style="{ width: '200px' }"
              size="small"
              placeholder="请输入退货地址"
            />
          </div>
          <div class="return-item">
            <span class="return-label">退货单号：</span>
            <span>{{ record.returnNumber || '-' }}</span>
          </div>
        </div>
      </template>

      <!-- 客户维护插槽 -->
      <template #customerMaintenance="{ record }">
        <div class="customer-maintenance-card">
          <div class="maintenance-item">
            <span class="maintenance-label">客户诉求：</span>
            <span>{{ record.customerDemand }}</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">附件：</span>
            <span v-if="record.attachments && record.attachments.length > 0">
              <a-button
                type="text"
                size="mini"
                @click="handleViewAttachments(record, 'attachments')"
                class="attachment-btn"
              >
                <template #icon><icon-file /></template>
                {{ record.attachments.length }}个文件
              </a-button>
            </span>
            <span v-else>-</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">回复内容：</span>
            <span>{{ record.replyContent || '-' }}</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">回复附件：</span>
            <span v-if="record.replyAttachments && record.replyAttachments.length > 0">
              <a-button
                type="text"
                size="mini"
                @click="handleViewAttachments(record, 'replyAttachments')"
                class="attachment-btn"
              >
                <template #icon><icon-file /></template>
                {{ record.replyAttachments.length }}个文件
              </a-button>
            </span>
            <span v-else>-</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">责任归属：</span>
            <span>{{ record.responsibility }}</span>
          </div>
        </div>
      </template>

      <!-- ERP操作系统插槽 -->
      <template #erpSystem="{ record }">
        <a-select
          :model-value="record.erpSystem"
          @change="(value) => handleErpSystemChange(record, value)"
          :style="{ width: '140px' }"
          size="small"
        >
          <a-option value="完成erp系统操作">完成erp系统操作</a-option>
          <a-option value="已操作采购退料">已操作采购退料</a-option>
          <a-option value="已下推费用单">已下推费用单</a-option>
          <a-option value="其他">其他</a-option>
        </a-select>
      </template>

      <!-- 操作列插槽 -->
      <template #operation="{ record }">
        <a-space direction="vertical" size="small">
          <a-button
            type="text"
            size="small"
            status="normal"
            @click="handleDetail(record)"
          >
            <template #icon><icon-eye /></template>
            详情
          </a-button>
          <a-button
            type="text"
            size="small"
            status="success"
            @click="handleReply(record)"
          >
            <template #icon><icon-message /></template>
            回复
          </a-button>
          <a-button
            type="text"
            size="small"
            status="warning"
            @click="handleRemark(record)"
          >
            <template #icon><icon-edit /></template>
            售后备注
          </a-button>
          <a-button
            type="text"
            size="small"
            status="normal"
            @click="handleHistory(record)"
          >
            <template #icon><icon-history /></template>
            回复历史
          </a-button>
          <a-button
            type="text"
            size="small"
            status="danger"
            @click="handleChangeStaff(record)"
          >
            <template #icon><icon-user /></template>
            更换售后员
          </a-button>
          <a-button
            type="text"
            size="small"
            status="normal"
            @click="handleCopyIssue(record)"
          >
            <template #icon><icon-copy /></template>
            复制问题
          </a-button>
        </a-space>
      </template>
    </ma-crud>

    <!-- 附件查看弹窗 -->
    <a-modal
      v-model:visible="attachmentModalVisible"
      title="查看附件"
      :width="600"
      :footer="false"
    >
      <div class="attachment-list">
        <div
          v-for="(attachment, index) in currentAttachments"
          :key="index"
          class="attachment-item"
        >
          <div class="attachment-info">
            <icon-file class="file-icon" />
            <span class="file-name">{{ getFileName(attachment) }}</span>
            <span class="file-size">{{ getFileSize(attachment) }}</span>
          </div>
          <div class="attachment-actions">
            <a-button
              type="text"
              size="small"
              @click="handlePreviewAttachment(attachment)"
            >
              <template #icon><icon-eye /></template>
              预览
            </a-button>
            <a-button
              type="text"
              size="small"
              @click="handleDownloadAttachment(attachment)"
            >
              <template #icon><icon-download /></template>
              下载
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits([
  'detail',
  'reply',
  'remark',
  'history',
  'change-staff',
  'copy-issue',
  'request',
  'status-change'
])

const crudRef = ref()

// 附件查看相关状态
const attachmentModalVisible = ref(false)
const currentAttachments = ref([])
const currentAttachmentType = ref('')

// API函数 - 支持搜索和分页
const simpleApi = async (params) => {
  console.log('API请求参数:', params)

  // 触发外部请求事件
  emit('request', params)

  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 300))

  // 过滤数据
  let filteredData = [...props.data]

  // 搜索过滤
  if (params.orderNumber) {
    filteredData = filteredData.filter(item =>
      item.orderNumber && item.orderNumber.includes(params.orderNumber)
    )
  }
  if (params.afterSalesNumber) {
    filteredData = filteredData.filter(item =>
      item.afterSalesNumber && item.afterSalesNumber.includes(params.afterSalesNumber)
    )
  }
  if (params.afterSalesStatus) {
    filteredData = filteredData.filter(item =>
      item.afterSalesStatus === params.afterSalesStatus
    )
  }
  if (params.orderSource) {
    filteredData = filteredData.filter(item =>
      item.orderSource === params.orderSource
    )
  }
  if (params.afterSalesType) {
    filteredData = filteredData.filter(item =>
      item.afterSalesType === params.afterSalesType
    )
  }
  if (params.productCode) {
    filteredData = filteredData.filter(item =>
      item.productCode && item.productCode.includes(params.productCode)
    )
  }
  if (params.productName) {
    filteredData = filteredData.filter(item =>
      item.productName && item.productName.includes(params.productName)
    )
  }
  if (params.isOwnBrand) {
    filteredData = filteredData.filter(item =>
      item.isOwnBrand === params.isOwnBrand
    )
  }
  if (params.actualSupplier) {
    filteredData = filteredData.filter(item =>
      item.actualSupplier && item.actualSupplier.includes(params.actualSupplier)
    )
  }
  if (params.status) {
    filteredData = filteredData.filter(item =>
      item.status === params.status
    )
  }
  if (params.follower) {
    filteredData = filteredData.filter(item =>
      item.follower && item.follower.includes(params.follower)
    )
  }
  if (params.salesDepartment) {
    filteredData = filteredData.filter(item =>
      item.salesDepartment === params.salesDepartment
    )
  }
  if (params.customerServiceReturn) {
    filteredData = filteredData.filter(item =>
      item.customerServiceReturn === params.customerServiceReturn
    )
  }
  if (params.afterSalesStaff) {
    filteredData = filteredData.filter(item =>
      item.afterSalesStaff && item.afterSalesStaff.includes(params.afterSalesStaff)
    )
  }
  if (params.proposer) {
    filteredData = filteredData.filter(item =>
      item.proposer && item.proposer.includes(params.proposer)
    )
  }
  if (params.purchaser) {
    filteredData = filteredData.filter(item =>
      item.purchaser && item.purchaser.includes(params.purchaser)
    )
  }
  if (params.actualDeliveryAddress) {
    filteredData = filteredData.filter(item =>
      item.actualDeliveryAddress && item.actualDeliveryAddress.includes(params.actualDeliveryAddress)
    )
  }
  if (params.shippingNumber) {
    filteredData = filteredData.filter(item =>
      item.shippingNumber && item.shippingNumber.includes(params.shippingNumber)
    )
  }
  if (params.afterSalesTimeRange && params.afterSalesTimeRange.length === 2) {
    const [startDate, endDate] = params.afterSalesTimeRange
    filteredData = filteredData.filter(item => {
      if (!item.afterSalesTime) return false
      const itemDate = new Date(item.afterSalesTime).getTime()
      const start = new Date(startDate).getTime()
      const end = new Date(endDate).getTime()
      return itemDate >= start && itemDate <= end
    })
  }
  if (params.replyTimeRange && params.replyTimeRange.length === 2) {
    const [startDate, endDate] = params.replyTimeRange
    filteredData = filteredData.filter(item => {
      if (!item.replyTime) return false
      const itemDate = new Date(item.replyTime).getTime()
      const start = new Date(startDate).getTime()
      const end = new Date(endDate).getTime()
      return itemDate >= start && itemDate <= end
    })
  }

  // 分页处理
  const page = params.page || 1
  const pageSize = params.pageSize || 10
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const paginatedData = filteredData.slice(start, end)

  return {
    data: paginatedData,
    total: filteredData.length,
    current: page,
    size: pageSize
  }
}

// ma-crud配置 - 添加tabs功能
const crudOptions = reactive({
  api: simpleApi,
  add: false,
  edit: false,
  delete: false,
  search: true,
  searchShow: true,
  searchLabelWidth: "100px",
  searchColNumber: 3,
  showIndex: false,
  pageSize: 10,
  pageSizeOption: [10, 20, 50, 100],
  tablePagination: true,
  // 启用tabs功能
  tabs: {
    type: 'line',
    trigger: 'click',
    dataIndex: 'status',
    data: [
      { label: '全部', value: '' },
      { label: '待处理', value: 'pending' },
      { label: '处理中', value: 'processing' },
      { label: '已完成', value: 'completed' }
    ],
    defaultKey: '',
    searchKey: 'status'
  }
})

// 列配置 - 包含搜索功能
const columns = [
  // 搜索字段
  {
    title: "订单编号",
    dataIndex: "orderNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入订单编号"
  },
  {
    title: "售后编号",
    dataIndex: "afterSalesNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入售后编号"
  },
  {
    title: "售后状态",
    dataIndex: "afterSalesStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "待处理", value: "待处理" },
        { label: "处理中", value: "处理中" },
        { label: "已完成", value: "已完成" },
        { label: "已关闭", value: "已关闭" }
      ]
    },
    searchPlaceholder: "请选择售后状态"
  },
  {
    title: "订单来源",
    dataIndex: "orderSource",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "京东", value: "京东" },
        { label: "天猫", value: "天猫" },
        { label: "淘宝", value: "淘宝" },
        { label: "拼多多", value: "拼多多" }
      ]
    },
    searchPlaceholder: "请选择订单来源"
  },
  {
    title: "售后类型",
    dataIndex: "afterSalesType",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "质量问题", value: "质量问题" },
        { label: "功能异常", value: "功能异常" },
        { label: "物流问题", value: "物流问题" },
        { label: "服务问题", value: "服务问题" }
      ]
    },
    searchPlaceholder: "请选择售后类型"
  },
  {
    title: "商品编码",
    dataIndex: "productCode",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入商品编码"
  },
  {
    title: "商品名称",
    dataIndex: "productName",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入商品名称"
  },
  {
    title: "是否自主品牌",
    dataIndex: "isOwnBrand",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "是", value: "是" },
        { label: "否", value: "否" }
      ]
    },
    searchPlaceholder: "请选择是否自主品牌"
  },
  {
    title: "实际供应商",
    dataIndex: "actualSupplier",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入实际供应商"
  },
  {
    title: "跟单员",
    dataIndex: "follower",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入跟单员"
  },
  {
    title: "销售部门",
    dataIndex: "salesDepartment",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "华东销售部", value: "华东销售部" },
        { label: "华南销售部", value: "华南销售部" },
        { label: "华北销售部", value: "华北销售部" },
        { label: "华中销售部", value: "华中销售部" },
        { label: "西南销售部", value: "西南销售部" },
        { label: "西北销售部", value: "西北销售部" },
        { label: "东北销售部", value: "东北销售部" }
      ]
    },
    searchPlaceholder: "请选择销售部门"
  },
  {
    title: "客服是否退货",
    dataIndex: "customerServiceReturn",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "是", value: "是" },
        { label: "否", value: "否" }
      ]
    },
    searchPlaceholder: "请选择客服是否退货"
  },
  {
    title: "售后员",
    dataIndex: "afterSalesStaff",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入售后员"
  },
  {
    title: "提出人",
    dataIndex: "proposer",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入提出人"
  },
  {
    title: "采购员",
    dataIndex: "purchaser",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入采购员"
  },
  {
    title: "实际收货地址",
    dataIndex: "actualDeliveryAddress",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入实际收货地址"
  },
  {
    title: "寄出单号",
    dataIndex: "shippingNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入寄出单号"
  },
  {
    title: "售后时间",
    dataIndex: "afterSalesTimeRange",
    formType: "range",
    search: true,
    hide: true,
    searchPlaceholder: "请选择售后时间范围"
  },
  {
    title: "回复时间",
    dataIndex: "replyTimeRange",
    formType: "range",
    search: true,
    hide: true,
    searchPlaceholder: "请选择回复时间范围"
  },
  // 显示列
  {
    title: "订单详情",
    dataIndex: "orderInfo",
    width: 280,
    slot: true,
    search: false
  },
  {
    title: "商品详情",
    dataIndex: "productInfo",
    width: 350,
    slot: true,
    search: false
  },
  {
    title: "售后信息",
    dataIndex: "afterSalesInfo",
    width: 350,
    slot: true,
    search: false
  },
  {
    title: "是否退货",
    dataIndex: "returnInfo",
    width: 250,
    slot: true,
    search: false
  },
  {
    title: "客户维护",
    dataIndex: "customerMaintenance",
    width: 350,
    slot: true,
    search: false
  },
  {
    title: "ERP操作系统",
    dataIndex: "erpSystem",
    width: 120,
    align: "center",
    slot: true,
    search: false
  },
  {
    title: "备注",
    dataIndex: "remark",
    width: 200
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 180,
    fixed: "right",
    slot: true,
    search: false
  }
]

// 事件处理
const handleDetail = (record) => {
  console.log('点击详情按钮:', record)
  emit('detail', record)
}

const handleReply = (record) => {
  console.log('点击回复按钮:', record)
  emit('reply', record)
}

const handleRemark = (record) => {
  console.log('点击备注按钮:', record)
  emit('remark', record)
}

const handleHistory = (record) => {
  console.log('点击历史按钮:', record)
  emit('history', record)
}

const handleChangeStaff = (record) => {
  console.log('点击更换售后员按钮:', record)
  emit('change-staff', record)
}

const handleCopyIssue = (record) => {
  console.log('点击复制问题按钮:', record)
  emit('copy-issue', record)
}

// 处理售后状态变更
const handleStatusChange = async (record, newStatus) => {
  if (record.afterSalesStatus === newStatus) {
    return // 状态没有变化，不需要更新
  }

  const oldStatus = record.afterSalesStatus // 保存原状态

  try {
    console.log('更新售后状态:', record.id, newStatus)

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 模拟成功响应
    const response = { success: true }

    if (response.success) {
      // 更新本地数据
      record.afterSalesStatus = newStatus

      // 触发外部事件，通知父组件状态已更改
      emit('status-change', {
        record,
        oldStatus,
        newStatus
      })

      Message.success(`售后状态已更新为"${newStatus}"`)
    } else {
      Message.error(response.message || '状态更新失败')
    }
  } catch (error) {
    console.error('更新售后状态失败:', error)
    Message.error('状态更新失败，请稍后重试')
  }
}

// 处理售后进度变更
const handleProgressChange = async (record, newProgress) => {
  if (record.afterSalesProgress === newProgress) {
    return // 进度没有变化，不需要更新
  }

  const oldProgress = record.afterSalesProgress // 保存原进度

  try {
    console.log('更新售后进度:', record.id, newProgress)

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 模拟成功响应
    const response = { success: true }

    if (response.success) {
      // 更新本地数据
      record.afterSalesProgress = newProgress

      Message.success(`售后进度已更新为"${newProgress}"`)
    } else {
      Message.error('进度更新失败')
    }
  } catch (error) {
    console.error('更新售后进度失败:', error)
    Message.error('进度更新失败，请稍后重试')
  }
}

// 处理ERP系统状态变更
const handleErpSystemChange = async (record, newErpSystem) => {
  if (record.erpSystem === newErpSystem) {
    return // ERP状态没有变化，不需要更新
  }

  const oldErpSystem = record.erpSystem // 保存原状态

  try {
    console.log('更新ERP系统状态:', record.id, newErpSystem)

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 模拟成功响应
    const response = { success: true }

    if (response.success) {
      // 更新本地数据
      record.erpSystem = newErpSystem

      Message.success(`ERP系统状态已更新为"${newErpSystem}"`)
    } else {
      Message.error('ERP状态更新失败')
    }
  } catch (error) {
    console.error('更新ERP系统状态失败:', error)
    Message.error('ERP状态更新失败，请稍后重试')
  }
}

// 处理退货地址输入（实时更新本地数据）
const handleReturnAddressInput = (record, newAddress) => {
  // 实时更新本地数据，不调用API
  record.returnAddress = newAddress
}

// 处理退货地址变更（失焦时保存到服务器）
const handleReturnAddressChange = async (record, newAddress) => {
  // 这里可以添加防抖逻辑，避免频繁调用API
  const trimmedAddress = newAddress?.trim() || ''

  try {
    console.log('保存退货地址:', record.id, trimmedAddress)

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300))

    // 模拟成功响应
    const response = { success: true }

    if (response.success) {
      // 确保本地数据是最新的
      record.returnAddress = trimmedAddress

      Message.success('退货地址已保存')
    } else {
      Message.error('退货地址保存失败')
    }
  } catch (error) {
    console.error('保存退货地址失败:', error)
    Message.error('退货地址保存失败，请稍后重试')
  }
}

// 状态颜色处理（保留用于其他地方可能的使用）
const getAfterSalesStatusColor = (status) => {
  const colorMap = {
    '待处理': 'orange',
    '处理中': 'blue',
    '已完成': 'green',
    '已关闭': 'gray'
  }
  return colorMap[status] || 'gray'
}

// 处理附件查看
const handleViewAttachments = (record, attachmentType) => {
  console.log('查看附件:', record.id, attachmentType)

  currentAttachmentType.value = attachmentType
  currentAttachments.value = record[attachmentType] || []
  attachmentModalVisible.value = true
}

// 获取文件名
const getFileName = (attachment) => {
  if (typeof attachment === 'string') {
    return attachment.split('/').pop() || attachment
  }
  return attachment.name || attachment.fileName || '未知文件'
}

// 获取文件大小
const getFileSize = (attachment) => {
  if (typeof attachment === 'object' && attachment.size) {
    const size = attachment.size
    if (size < 1024) return `${size}B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
    return `${(size / (1024 * 1024)).toFixed(1)}MB`
  }
  return ''
}

// 处理附件预览
const handlePreviewAttachment = (attachment) => {
  console.log('预览附件:', attachment)

  let url = ''
  if (typeof attachment === 'string') {
    url = attachment
  } else if (attachment.url) {
    url = attachment.url
  } else if (attachment.path) {
    url = attachment.path
  }

  if (url) {
    // 判断文件类型
    const fileExtension = url.split('.').pop().toLowerCase()
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
    const pdfTypes = ['pdf']

    if (imageTypes.includes(fileExtension)) {
      // 图片预览
      window.open(url, '_blank')
    } else if (pdfTypes.includes(fileExtension)) {
      // PDF预览
      window.open(url, '_blank')
    } else {
      // 其他文件类型直接下载
      handleDownloadAttachment(attachment)
    }
  } else {
    Message.warning('无法预览此文件')
  }
}

// 处理附件下载
const handleDownloadAttachment = (attachment) => {
  console.log('下载附件:', attachment)

  let url = ''
  let fileName = ''

  if (typeof attachment === 'string') {
    url = attachment
    fileName = attachment.split('/').pop() || 'download'
  } else {
    url = attachment.url || attachment.path || ''
    fileName = attachment.name || attachment.fileName || 'download'
  }

  if (url) {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    Message.success('开始下载文件')
  } else {
    Message.error('无法下载此文件')
  }
}

// 图片错误处理
const handleImageError = (event) => {
  event.target.src = '/not-image.png'
}
</script>

<style scoped>
.simple-table {
  background-color: #ffffff;
}

/* 订单信息样式 */
.order-info-card,
.product-info-card,
.after-sales-info-card,
.return-info-card,
.customer-maintenance-card {
  padding: 8px;
  font-size: 12px;
  line-height: 1.4;
}

.info-row,
.detail-item,
.info-item,
.return-item,
.maintenance-item {
  margin-bottom: 4px;
  display: flex;
  align-items: flex-start;
}

.label,
.detail-label,
.info-label,
.return-label,
.maintenance-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

/* 商品相关样式 */
.multiple-products,
.single-product {
  width: 100%;
}

.product-item {
  margin-bottom: 12px;
}

.product-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
}

.product-image {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e5e6eb;
  margin-right: 10px;
  flex-shrink: 0;
}

.product-basic-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.3;
  word-break: break-all;
  font-size: 12px;
}

.product-code {
  font-size: 11px;
  color: #666;
}

.product-details {
  font-size: 11px;
  margin-left: 60px; /* 对齐图片右侧 */
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.price {
  font-weight: 500;
  color: #f53f3f;
}

.product-divider {
  height: 1px;
  background-color: #e5e6eb;
  margin: 8px 0;
}

.delivery-address {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #f2f3f5;
  font-size: 11px;
}

.address-label {
  font-weight: 500;
  color: #666;
}

.detail-label {
  min-width: 70px;
}

.info-label {
  min-width: 70px;
}

.return-label {
  min-width: 90px;
}

.maintenance-label {
  min-width: 70px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .order-info-card,
  .product-info-card,
  .after-sales-info-card {
    font-size: 11px;
  }

  .label,
  .detail-label,
  .info-label {
    min-width: 60px;
  }
}
</style>
