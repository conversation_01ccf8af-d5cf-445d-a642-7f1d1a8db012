<template>
  <div class="after-sales-table">
    <ma-crud
      ref="crudRef"
      v-model="crudBinding"
      :options="crudOptions"
      :columns="columns"
      @request="handleRequest"
    >
      <!-- 订单详情插槽 -->
      <template #orderInfo="{ record }">
        <div class="order-info-card">
          <div class="info-row">
            <span class="label">订单编号：</span>
            <span>{{ record.orderNumber }}</span>
          </div>
          <div class="info-row">
            <span class="label">售后编号：</span>
            <span>{{ record.afterSalesNumber }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单类型：</span>
            <span>{{ record.orderType }}</span>
          </div>
          <div class="info-row">
            <span class="label">售后时间：</span>
            <span>{{ record.afterSalesTime }}</span>
          </div>
          <div class="info-row">
            <span class="label">回复时间：</span>
            <span>{{ record.replyTime }}</span>
          </div>
          <div class="info-row">
            <span class="label">订单来源：</span>
            <span>{{ record.orderSource }}</span>
          </div>
        </div>
      </template>

      <!-- 商品详情插槽 -->
      <template #productInfo="{ record }">
        <div class="product-info-card">
          <div class="product-name">{{ record.productName }}</div>
          <div class="product-details">
            <div class="detail-item">
              <span class="detail-label">商品编码：</span>
              <span>{{ record.productCode }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">数量：</span>
              <span>{{ record.quantity }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">销售价：</span>
              <span>¥{{ record.salePrice }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">成本价：</span>
              <span>¥{{ record.costPrice }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">实际收货地址：</span>
              <span>{{ record.deliveryAddress }}</span>
            </div>
          </div>
        </div>
      </template>

      <!-- 售后信息插槽 -->
      <template #afterSalesInfo="{ record }">
        <div class="after-sales-info-card">
          <div class="info-item">
            <span class="info-label">售后类型：</span>
            <span>{{ record.afterSalesType }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">售后内容：</span>
            <span>{{ record.afterSalesContent }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">售后状态：</span>
            <a-tag :color="getAfterSalesStatusColor(record.afterSalesStatus)">
              {{ record.afterSalesStatus }}
            </a-tag>
          </div>
          <div class="info-item">
            <span class="info-label">售后进度：</span>
            <span>{{ record.afterSalesProgress }}</span>
          </div>
        </div>
      </template>

      <!-- 是否退货插槽 -->
      <template #returnInfo="{ record }">
        <div class="return-info-card">
          <div class="return-item">
            <span class="return-label">业务-是否退货：</span>
            <a-tag :color="record.businessReturn === '是' ? 'green' : 'gray'">
              {{ record.businessReturn }}
            </a-tag>
          </div>
          <div class="return-item">
            <span class="return-label">客服-是否退货：</span>
            <a-tag :color="record.customerServiceReturn === '是' ? 'green' : 'gray'">
              {{ record.customerServiceReturn }}
            </a-tag>
          </div>
          <div class="return-item">
            <span class="return-label">退货地址：</span>
            <span>{{ record.returnAddress || '-' }}</span>
          </div>
          <div class="return-item">
            <span class="return-label">退货单号：</span>
            <span>{{ record.returnNumber || '-' }}</span>
          </div>
        </div>
      </template>

      <!-- 客户维护插槽 -->
      <template #customerMaintenance="{ record }">
        <div class="customer-maintenance-card">
          <div class="maintenance-item">
            <span class="maintenance-label">客户诉求：</span>
            <span>{{ record.customerDemand }}</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">附件：</span>
            <span v-if="record.attachments && record.attachments.length > 0">
              {{ record.attachments.length }}个文件
            </span>
            <span v-else>-</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">回复内容：</span>
            <span>{{ record.replyContent || '-' }}</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">回复附件：</span>
            <span v-if="record.replyAttachments && record.replyAttachments.length > 0">
              {{ record.replyAttachments.length }}个文件
            </span>
            <span v-else>-</span>
          </div>
          <div class="maintenance-item">
            <span class="maintenance-label">责任归属：</span>
            <span>{{ record.responsibility }}</span>
          </div>
        </div>
      </template>

      <!-- 是否为自主品牌插槽 -->
      <template #isOwnBrand="{ record }">
        <a-tag :color="record.isOwnBrand === '是' ? 'green' : 'blue'">
          {{ record.isOwnBrand }}
        </a-tag>
      </template>

      <!-- ERP操作系统插槽 -->
      <template #erpSystem="{ record }">
        <a-tag :color="record.erpSystem === '已同步' ? 'green' : 'orange'">
          {{ record.erpSystem }}
        </a-tag>
      </template>

      <!-- 操作列插槽 -->
      <template #operation="{ record }">
        <a-space direction="vertical" fill>
          <a-button type="text" size="small" @click="handleReply(record)">
            回复
          </a-button>
          <a-button type="text" size="small" @click="handleAfterSalesRemark(record)">
            售后备注
          </a-button>
          <a-button type="text" size="small" @click="handleReplyHistory(record)">
            回复历史
          </a-button>
          <a-button type="text" size="small" @click="handleChangeStaff(record)">
            更换售后员
          </a-button>
          <a-button type="text" size="small" @click="handleCopyIssue(record)">
            复制问题
          </a-button>
        </a-space>
      </template>
    </ma-crud>

  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'reply',
  'after-sales-remark',
  'reply-history',
  'change-staff',
  'copy-issue',
  'request'
])

const crudRef = ref()
const crudBinding = ref({})

// 模拟API函数
const mockApi = async (params) => {
  console.log('API请求参数:', params)

  // 触发外部请求事件
  emit('request', params)

  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 300))

  // 过滤数据（如果有搜索条件）
  let filteredData = [...props.data]

  // 简单的搜索过滤逻辑
  if (params.orderNumber) {
    filteredData = filteredData.filter(item =>
      item.orderNumber.includes(params.orderNumber)
    )
  }
  if (params.afterSalesNumber) {
    filteredData = filteredData.filter(item =>
      item.afterSalesNumber.includes(params.afterSalesNumber)
    )
  }
  if (params.orderSource) {
    filteredData = filteredData.filter(item =>
      item.orderSource === params.orderSource
    )
  }
  if (params.afterSalesType) {
    filteredData = filteredData.filter(item =>
      item.afterSalesType === params.afterSalesType
    )
  }
  if (params.afterSalesStatus) {
    filteredData = filteredData.filter(item =>
      item.afterSalesStatus === params.afterSalesStatus
    )
  }
  if (params.status) {
    filteredData = filteredData.filter(item =>
      item.status === params.status
    )
  }

  // 分页处理
  const page = params.page || 1
  const pageSize = params.pageSize || 10
  const start = (page - 1) * pageSize
  const end = start + pageSize
  const paginatedData = filteredData.slice(start, end)

  // 返回符合ma-crud期望的数据格式
  return {
    data: paginatedData,
    total: filteredData.length,
    current: page,
    size: pageSize
  }
}

// 请求处理
const handleRequest = (params) => {
  emit('request', params)
}

// 操作事件处理
const handleReply = (record) => {
  emit('reply', record)
}

const handleAfterSalesRemark = (record) => {
  emit('after-sales-remark', record)
}

const handleReplyHistory = (record) => {
  emit('reply-history', record)
}

const handleChangeStaff = (record) => {
  emit('change-staff', record)
}

const handleCopyIssue = (record) => {
  emit('copy-issue', record)
}

// 状态颜色处理
const getAfterSalesStatusColor = (status) => {
  const colorMap = {
    '待处理': 'orange',
    '处理中': 'blue',
    '已完成': 'green',
    '已关闭': 'gray'
  }
  return colorMap[status] || 'gray'
}

// ma-crud配置 - 简化版本，避免配置问题
const crudOptions = reactive({
  api: mockApi,
  showIndex: false,
  pageSize: 10,
  pageSizeOption: [10, 20, 50, 100],
  tablePagination: true,
  operationColumn: false,
  searchLabelWidth: "100px",
  searchColNumber: 3,
  rowSelection: false,
  add: false,
  edit: false,
  delete: false,
  viewLayoutSetting: false,
  showTools: false,
  searchSpan: 6,
  search: true,
  searchShow: true,
  searchCollapse: false,
  searchReset: true,
  searchSubmit: true,
  autoRequest: true
  // 暂时禁用tabs功能，避免配置冲突
  // tabs: {
  //   type: 'line',
  //   trigger: 'click',
  //   dataIndex: 'status',
  //   data: [
  //     { label: '全部', value: '' },
  //     { label: '待处理', value: 'pending' },
  //     { label: '处理中', value: 'processing' },
  //     { label: '已完成', value: 'completed' }
  //   ],
  //   defaultKey: '',
  //   searchKey: 'status'
  // }
})

// 列配置 - 简化版本
const columns = [
  // 基本搜索字段
  {
    title: "订单编号",
    dataIndex: "orderNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入订单编号"
  },
  {
    title: "售后编号",
    dataIndex: "afterSalesNumber",
    formType: "input",
    search: true,
    hide: true,
    searchPlaceholder: "请输入售后编号"
  },
  {
    title: "售后状态",
    dataIndex: "afterSalesStatus",
    formType: "select",
    search: true,
    hide: true,
    dict: {
      data: [
        { label: "全部", value: "" },
        { label: "待处理", value: "待处理" },
        { label: "处理中", value: "处理中" },
        { label: "已完成", value: "已完成" },
        { label: "已关闭", value: "已关闭" }
      ]
    },
    searchPlaceholder: "请选择售后状态"
  },
  // 显示列配置
  {
    title: "订单详情",
    dataIndex: "orderInfo",
    width: 280,
    customRender: "orderInfo"
  },
  {
    title: "商品详情",
    dataIndex: "productInfo",
    width: 350,
    customRender: "productInfo"
  },
  {
    title: "是否为自主品牌",
    dataIndex: "isOwnBrand",
    width: 120,
    align: "center",
    customRender: "isOwnBrand"
  },
  {
    title: "实际供应商",
    dataIndex: "actualSupplier",
    width: 200
  },
  {
    title: "售后信息",
    dataIndex: "afterSalesInfo",
    width: 300,
    customRender: "afterSalesInfo"
  },
  {
    title: "ERP操作系统",
    dataIndex: "erpSystem",
    width: 120,
    align: "center",
    customRender: "erpSystem"
  },
  {
    title: "备注",
    dataIndex: "remark",
    width: 200
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 150,
    fixed: "right",
    customRender: "operation"
  }
]
</script>

<style scoped>
.after-sales-table {
  background-color: #ffffff;
}

.order-info-card,
.product-info-card,
.after-sales-info-card,
.return-info-card,
.customer-maintenance-card {
  padding: 8px;
  font-size: 12px;
  line-height: 1.4;
}

.info-row,
.detail-item,
.info-item,
.return-item,
.maintenance-item {
  margin-bottom: 4px;
  display: flex;
  align-items: flex-start;
}

.label,
.detail-label,
.info-label,
.return-label,
.maintenance-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
}

.product-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.3;
  word-break: break-all;
}

.product-details {
  font-size: 11px;
}

.detail-label {
  min-width: 70px;
}

.info-label {
  min-width: 70px;
}

.return-label {
  min-width: 90px;
}

.maintenance-label {
  min-width: 70px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .order-info-card,
  .product-info-card,
  .after-sales-info-card,
  .return-info-card,
  .customer-maintenance-card {
    font-size: 11px;
  }

  .label,
  .detail-label,
  .info-label,
  .return-label,
  .maintenance-label {
    min-width: 60px;
  }
}
</style>
