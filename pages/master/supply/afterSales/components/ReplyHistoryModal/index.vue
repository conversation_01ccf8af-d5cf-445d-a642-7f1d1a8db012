<template>
  <a-modal
    v-model:visible="visible"
    title="回复历史记录"
    width="1200px"
    :footer="false"
    :mask-closable="false"
  >
    <div class="reply-history-content">
      <!-- 表格展示回复历史 -->
      <a-table
        :columns="columns"
        :data="historyList"
        :pagination="paginationConfig"
        :loading="loading"
        row-key="id"
        :scroll="{ x: 1000 }"
      >
        <!-- 修改时间列 -->
        <template #modifyTime="{ record }">
          <span>{{ record.modifyTime }}</span>
        </template>

        <!-- 修改人列 -->
        <template #modifier="{ record }">
          <span>{{ record.modifier }}</span>
        </template>

        <!-- 回复内容列 -->
        <template #replyContent="{ record }">
          <div class="reply-content-cell">
            <a-tooltip :content="record.replyContent" position="top">
              <div class="content-text">{{ record.replyContent }}</div>
            </a-tooltip>
          </div>
        </template>

        <!-- 责任归属列 -->
        <template #responsibility="{ record }">
          <span>{{ record.responsibility }}</span>
        </template>

        <!-- 公司承担售后金额列 -->
        <template #companyAmount="{ record }">
          <span class="amount-text">{{ record.companyAmount }}</span>
        </template>

        <!-- 基础商承担金额列 -->
        <template #supplierAmount="{ record }">
          <span class="amount-text">{{ record.supplierAmount }}</span>
        </template>

        <!-- 供应商承担售后金额列 -->
        <template #supplierAfterSalesAmount="{ record }">
          <span class="amount-text">{{ record.supplierAfterSalesAmount }}</span>
        </template>

        <!-- 供应商赔偿用户列 -->
        <template #supplierCompensation="{ record }">
          <span>{{ record.supplierCompensation }}</span>
        </template>

        <!-- 是否退货列 -->
        <template #isReturn="{ record }">
          <a-tag :color="record.isReturn === '是' ? 'green' : 'gray'">
            {{ record.isReturn }}
          </a-tag>
        </template>

        <!-- 回复附件列 -->
        <template #replyAttachments="{ record }">
          <div v-if="record.replyAttachments && record.replyAttachments.length > 0">
            <a-button
              type="text"
              size="small"
              @click="handleViewAttachments(record.replyAttachments)"
            >
              <template #icon><icon-file /></template>
              {{ record.replyAttachments.length }}个文件
            </a-button>
          </div>
          <span v-else>-</span>
        </template>
      </a-table>

      <!-- 空状态 -->
      <div v-if="historyList.length === 0 && !loading" class="empty-state">
        <a-empty description="暂无数据" />
      </div>
    </div>

    <!-- 附件查看弹窗 -->
    <a-modal
      v-model:visible="attachmentModalVisible"
      title="查看附件"
      :width="600"
      :footer="false"
    >
      <div class="attachment-list">
        <div
          v-for="(attachment, index) in currentAttachments"
          :key="index"
          class="attachment-item"
        >
          <div class="attachment-info">
            <icon-file class="file-icon" />
            <span class="file-name">{{ getFileName(attachment) }}</span>
            <span class="file-size">{{ getFileSize(attachment) }}</span>
          </div>
          <div class="attachment-actions">
            <a-button
              type="text"
              size="small"
              @click="handlePreviewAttachment(attachment)"
            >
              <template #icon><icon-eye /></template>
              预览
            </a-button>
            <a-button
              type="text"
              size="small"
              @click="handleDownloadAttachment(attachment)"
            >
              <template #icon><icon-download /></template>
              下载
            </a-button>
          </div>
        </div>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  afterSalesData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(false)
const loading = ref(false)
const attachmentModalVisible = ref(false)
const currentAttachments = ref([])

// 表格列配置
const columns = [
  {
    title: '修改时间',
    dataIndex: 'modifyTime',
    width: 150,
    slotName: 'modifyTime'
  },
  {
    title: '修改人',
    dataIndex: 'modifier',
    width: 100,
    slotName: 'modifier'
  },
  {
    title: '回复内容',
    dataIndex: 'replyContent',
    width: 200,
    slotName: 'replyContent'
  },
  {
    title: '责任归属',
    dataIndex: 'responsibility',
    width: 100,
    slotName: 'responsibility'
  },
  {
    title: '公司承担售后金额',
    dataIndex: 'companyAmount',
    width: 120,
    slotName: 'companyAmount'
  },
  {
    title: '我司售后费用',
    dataIndex: 'supplierAmount',
    width: 120,
    slotName: 'supplierAmount'
  },
  {
    title: '供应商承担售后金额',
    dataIndex: 'supplierAfterSalesAmount',
    width: 140,
    slotName: 'supplierAfterSalesAmount'
  },
  {
    title: '供应商赔偿用户',
    dataIndex: 'supplierCompensation',
    width: 120,
    slotName: 'supplierCompensation'
  },
  {
    title: '售后总费用',
    dataIndex: 'totalAmount',
    width: 80,
    slotName: 'totalAmount'
  },
  {
    title: '是否退货',
    dataIndex: 'isReturn',
    width: 80,
    slotName: 'isReturn'
  },
  {
    title: '回复附件',
    dataIndex: 'replyAttachments',
    width: 100,
    slotName: 'replyAttachments'
  }
]

// 分页配置
const paginationConfig = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: ['10', '20', '50'],
  onChange: (page) => {
    paginationConfig.current = page
  },
  onPageSizeChange: (pageSize) => {
    paginationConfig.pageSize = pageSize
    paginationConfig.current = 1
  }
})

// 模拟回复历史数据
const historyList = computed(() => {
  if (!props.afterSalesData || typeof props.afterSalesData !== 'object' || !props.afterSalesData.id) return []

  // 这里应该根据售后ID从后端获取真实的回复历史数据
  const mockData = [
    {
      id: 1,
      modifyTime: '2025年06月23日 17:28:25',
      modifier: '客户',
      replyContent: props.afterSalesData.customerDemand || '客户提交售后申请，要求全额退款并赔偿损失',
      responsibility: '-',
      companyAmount: '-',
      supplierAmount: '-',
      supplierAfterSalesAmount: '-',
      supplierCompensation: '-',
      totalAmount: '-',
      isReturn: '-',
      replyAttachments: props.afterSalesData.attachments || []
    },
    {
      id: 2,
      modifyTime: '2025年06月23日 17:31:09',
      modifier: '客服小王',
      replyContent: props.afterSalesData.replyContent || '已收到您的售后申请，正在处理中，我们会尽快为您解决问题',
      responsibility: '供应商责任',
      companyAmount: '100.00',
      supplierAmount: '200.00',
      supplierAfterSalesAmount: '150.00',
      supplierCompensation: '全额退款',
      totalAmount: '856.00',
      isReturn: '是',
      replyAttachments: props.afterSalesData.replyAttachments || []
    },
    {
      id: 3,
      modifyTime: '2025年06月23日 18:15:30',
      modifier: '客服小王',
      replyContent: '已联系供应商核实产品质量问题，预计2个工作日内给出处理方案',
      responsibility: '供应商责任',
      companyAmount: '0.00',
      supplierAmount: '300.00',
      supplierAfterSalesAmount: '300.00',
      totalAmount: '1000.00',
      supplierCompensation: '换货',
      isReturn: '否',
      replyAttachments: []
    }
  ]

  // 更新分页总数
  paginationConfig.total = mockData.length

  return mockData
})

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    // 重置分页到第一页
    paginationConfig.current = 1
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 查看附件
const handleViewAttachments = (attachments) => {
  currentAttachments.value = attachments || []
  attachmentModalVisible.value = true
}

// 获取文件名
const getFileName = (attachment) => {
  if (typeof attachment === 'string') {
    return attachment.split('/').pop() || attachment
  }
  return attachment.name || attachment.fileName || '未知文件'
}

// 获取文件大小
const getFileSize = (attachment) => {
  if (typeof attachment === 'object' && attachment.size) {
    const size = attachment.size
    if (size < 1024) return `${size}B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
    return `${(size / (1024 * 1024)).toFixed(1)}MB`
  }
  return ''
}

// 预览附件
const handlePreviewAttachment = (attachment) => {
  console.log('预览附件:', attachment)

  let url = ''
  if (typeof attachment === 'string') {
    url = attachment
  } else if (attachment.url) {
    url = attachment.url
  } else if (attachment.path) {
    url = attachment.path
  }

  if (url) {
    // 判断文件类型
    const fileExtension = url.split('.').pop().toLowerCase()
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
    const pdfTypes = ['pdf']

    if (imageTypes.includes(fileExtension)) {
      // 图片预览
      window.open(url, '_blank')
    } else if (pdfTypes.includes(fileExtension)) {
      // PDF预览
      window.open(url, '_blank')
    } else {
      // 其他文件类型直接下载
      handleDownloadAttachment(attachment)
    }
  } else {
    Message.warning('无法预览此文件')
  }
}

// 下载附件
const handleDownloadAttachment = (attachment) => {
  console.log('下载附件:', attachment)

  let url = ''
  let fileName = ''

  if (typeof attachment === 'string') {
    url = attachment
    fileName = attachment.split('/').pop() || 'download'
  } else {
    url = attachment.url || attachment.path || ''
    fileName = attachment.name || attachment.fileName || 'download'
  }

  if (url) {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    Message.success('开始下载文件')
  } else {
    Message.error('无法下载此文件')
  }
}
</script>

<style scoped>
.reply-history-content {
  background-color: #ffffff;
}

.reply-content-cell {
  max-width: 200px;
}

.content-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.4;
  font-size: 14px;
  color: #333;
}

.amount-text {
  font-weight: 500;
  color: #f53f3f;
}

.empty-state {
  text-align: center;
  padding: 40px 0;
}

/* 附件相关样式 */
.attachment-list {
  max-height: 400px;
  overflow-y: auto;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e5e6eb;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.file-icon {
  color: #666;
  font-size: 16px;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.attachment-actions .arco-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}

/* 表格样式优化 */
:deep(.arco-table-th) {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
}

:deep(.arco-table-td) {
  padding: 12px 8px;
}

:deep(.arco-table-tbody .arco-table-tr:hover) {
  background-color: #f8f9fa;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .attachment-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .file-name {
    max-width: 100%;
  }
}
</style>
