<template>
  <a-modal
    v-model:visible="visible"
    title="更换售后员"
    width="600px"
    :ok-loading="submitLoading"
    ok-text="确认更换"
    cancel-text="取消"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="售后编号" field="afterSalesNumber">
        <a-input v-model="formData.afterSalesNumber" readonly />
      </a-form-item>
      
      <a-form-item label="当前售后员" field="currentStaff">
        <a-input v-model="formData.currentStaff" readonly />
      </a-form-item>
      
      <a-form-item label="新售后员" field="newStaff" required>
        <a-select 
          v-model="formData.newStaff" 
          placeholder="请选择新的售后员"
          allow-search
        >
          <a-option 
            v-for="staff in staffList" 
            :key="staff.id" 
            :value="staff.id"
            :label="staff.name"
          >
            {{ staff.name }} - {{ staff.department }}
          </a-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="更换原因" field="changeReason" required>
        <a-textarea 
          v-model="formData.changeReason" 
          placeholder="请输入更换售后员的原因"
          :rows="3"
        />
      </a-form-item>

      <a-alert
        type="warning"
        message="更换提醒"
        title="注:一旦更换，该售后记最的售后员就会更新为最新数售后员显示"
        show-icon
        class="mb-4"
      />
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  afterSalesData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const formRef = ref()
const visible = ref(false)
const submitLoading = ref(false)

// 售后员列表（模拟数据）
const staffList = ref([
  { id: 1, name: '张三', department: '售后部' },
  { id: 2, name: '李四', department: '售后部' },
  { id: 3, name: '王五', department: '售后部' },
  { id: 4, name: '赵六', department: '客服部' },
  { id: 5, name: '钱七', department: '客服部' }
])

// 表单数据
const formData = reactive({
  afterSalesNumber: '',
  currentStaff: '',
  newStaff: '',
  changeReason: ''
})

// 表单验证规则
const rules = {
  newStaff: [
    { required: true, message: '请选择新的售后员' }
  ],
  changeReason: [
    { required: true, message: '请输入更换原因' }
  ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.afterSalesData && typeof props.afterSalesData === 'object') {
    // 填充表单数据
    formData.afterSalesNumber = props.afterSalesData.afterSalesNumber || ''
    formData.currentStaff = props.afterSalesData.currentStaff || '客服小王'
    formData.newStaff = ''
    formData.changeReason = ''
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 提交处理
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      submitLoading.value = true

      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1000))

      const selectedStaff = staffList.value.find(staff => staff.id === formData.newStaff)
      emit('submit', {
        ...formData,
        newStaffName: selectedStaff?.name,
        newStaffDepartment: selectedStaff?.department,
        afterSalesId: props.afterSalesData.id,
        changeTime: new Date().toLocaleString()
      })

      Message.success(`售后员已更换为：${selectedStaff?.name}`)
      handleCancel()
    }
  } catch (error) {
    console.log('表单验证失败:', error)
    Message.error('请检查表单内容')
  } finally {
    submitLoading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  visible.value = false
  formRef.value?.resetFields()
}
</script>

<style scoped>
:deep(.arco-form-item-label) {
  font-weight: 500;
}
</style>
