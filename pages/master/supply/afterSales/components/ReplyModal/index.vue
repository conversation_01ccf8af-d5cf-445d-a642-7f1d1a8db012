<template>
  <a-modal
    v-model:visible="visible"
    :title="`回复售后-订单号${afterSalesData.orderNumber || ''}-售后编号${afterSalesData.afterSalesNumber || ''}`"
    width="1200px"
    :footer="false"
    @cancel="handleCancel"
  >
    <div class="reply-modal-content">
      <!-- 顶部基本信息 -->
      <div class="basic-info-section">
        <div class="info-row">
          <div class="info-item">
            <span class="label">跟单员：</span>
            <span>{{ afterSalesData.productBrand || '广东八天科技发展有限公司' }}</span>
          </div>
          <div class="info-item">
            <span class="label">采购员：</span>
            <span>{{ afterSalesData.sourceStore || '供应链-胡晓梅' }}</span>
          </div>
          <div class="info-item">
            <span class="label">售后店：</span>
            <span>{{ afterSalesData.afterSalesStore || '供应链-胡晓梅' }}</span>
          </div>
          <div class="info-item">
            <span class="label">提出人：</span>
            <span>{{ afterSalesData.proposer || '广东八天科技发展有限公司' }}</span>
          </div>
          <div class="info-item">
            <span class="label">回复者：</span>
            <span>{{ currentUser || '' }}</span>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧：售后问题 -->
        <div class="left-section">
          <h3 class="section-title">售后问题</h3>

          <div class="form-group">
            <label class="form-label">售后类型：</label>
            <span class="form-value">{{ afterSalesData.afterSalesType || '需运费补贴' }}</span>
          </div>

          <div class="form-group">
            <label class="form-label">售后内容：</label>
            <span class="form-value">{{ afterSalesData.afterSalesContent || '测' }}</span>
          </div>

          <div class="form-group">
            <label class="form-label">客户诉求：</label>
            <span class="form-value">{{ afterSalesData.customerDemand || '' }}</span>
          </div>

          <div class="form-group">
            <label class="form-label">附件：</label>
            <span class="form-value">{{ afterSalesData.attachments?.length > 0 ? '有' : '无' }}</span>
          </div>

          <div class="form-group">
            <label class="form-label">售后商品：</label>
            <div class="product-table">
              <table>
                <thead>
                  <tr>
                    <th>商品编码</th>
                    <th>商品名称</th>
                    <th>退货数量</th>
                    <th>售后原因</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="afterSalesData.products && afterSalesData.products.length > 0"
                      v-for="product in afterSalesData.products" :key="product.code">
                    <td>{{ product.code }}</td>
                    <td>{{ product.name }}</td>
                    <td>{{ product.quantity }}</td>
                    <td>{{ afterSalesData.afterSalesType }}</td>
                  </tr>
                  <tr v-else>
                    <td>{{ afterSalesData.productCode || '1628077676' }}</td>
                    <td>{{ afterSalesData.productName || '测试' }}</td>
                    <td>{{ afterSalesData.quantity || '2' }}</td>
                    <td>{{ afterSalesData.afterSalesType || '是' }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- 右侧：售后回复 -->
        <div class="right-section">
          <h3 class="section-title">售后回复</h3>

          <a-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            layout="vertical"
          >
            <a-form-item label="回复内容" field="replyContent" required>
              <a-textarea
                v-model="formData.replyContent"
                placeholder="请输入您的回复内容"
                :rows="4"
              />
            </a-form-item>

            <a-form-item label="责任归属" field="responsibility">
              <a-select
                v-model="formData.responsibility"
                placeholder="请选择责任归属"
              >
                <a-option value="供应商责任">供应商责任</a-option>
                <a-option value="平台责任">平台责任</a-option>
                <a-option value="客户责任">客户责任</a-option>
                <a-option value="物流责任">物流责任</a-option>
                <a-option value="其他">其他</a-option>
              </a-select>
            </a-form-item>

            <a-form-item label="公司承担售后金额" field="companyAmount">
              <a-input
                v-model="formData.companyAmount"
                placeholder="请输入公司承担售后金额"
              />
              <div class="amount-select">
                <span class="amount-label">我司赔偿用户：</span>
                <a-select
                  v-model="formData.companyCompensationType"
                  placeholder="请选择赔偿类型"
                  style="width: 150px;"
                >
                  <a-option value="全额退款">全额退款</a-option>
                  <a-option value="部分退款">部分退款</a-option>
                  <a-option value="换货">换货</a-option>
                  <a-option value="维修">维修</a-option>
                </a-select>
              </div>
            </a-form-item>

            <a-form-item label="供应商承担售后金额" field="supplierAmount">
              <a-input
                v-model="formData.supplierAmount"
                placeholder="请输入供应商承担售后金额"
              />
              <div class="amount-select">
                <span class="amount-label">供应商赔偿用户：</span>
                <a-select
                  v-model="formData.supplierCompensationType"
                  placeholder="请选择赔偿类型"
                  style="width: 150px;"
                >
                  <a-option value="全额退款">全额退款</a-option>
                  <a-option value="部分退款">部分退款</a-option>
                  <a-option value="换货">换货</a-option>
                  <a-option value="维修">维修</a-option>
                </a-select>
              </div>
            </a-form-item>

            <a-form-item label="售后总费用" field="totalAmount">
              <span class="total-amount">{{ computedTotalAmount }}</span>
            </a-form-item>

            <a-form-item label="是否退货" field="isReturn">
              <a-radio-group v-model="formData.isReturn">
                <a-radio value="是">是</a-radio>
                <a-radio value="否">否</a-radio>
              </a-radio-group>
            </a-form-item>

            <a-form-item label="回复附件" field="replyAttachments">
              <a-upload
                v-model:file-list="formData.replyAttachments"
                action="/api/upload"
                :limit="10"
                multiple
                accept=".doc,.docx,.xls,.xlsx,.jpg,.png,.pdf"
                :before-upload="beforeUpload"
              >
                <template #upload-button>
                  <a-button>
                    <template #icon><icon-upload /></template>
                    上传文件
                  </a-button>
                </template>
              </a-upload>
              <div class="upload-tip">
                支持的文件类型：.doc, .docx, .xls, .xlsx, .jpg, .png, .pdf，最多上传10个文件，单个文件不超过10M
              </div>
            </a-form-item>
          </a-form>
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="footer-actions">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  afterSalesData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const formRef = ref()
const visible = ref(false)
const submitLoading = ref(false)
const currentUser = ref('当前用户') // 这里应该从用户状态中获取

// 表单数据
const formData = reactive({
  replyContent: '',
  responsibility: '',
  companyAmount: '',
  companyCompensationType: '',
  supplierAmount: '',
  supplierCompensationType: '',
  isReturn: '否',
  replyAttachments: []
})

// 计算总费用
const computedTotalAmount = computed(() => {
  const companyAmount = parseFloat(formData.companyAmount) || 0
  const supplierAmount = parseFloat(formData.supplierAmount) || 0
  return (companyAmount + supplierAmount).toFixed(2)
})

// 表单验证规则
const rules = {
  replyContent: [
    { required: true, message: '请输入回复内容' }
  ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.afterSalesData && typeof props.afterSalesData === 'object') {
    // 重置表单数据
    resetFormData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 重置表单数据
const resetFormData = () => {
  formData.replyContent = ''
  formData.responsibility = ''
  formData.companyAmount = ''
  formData.companyCompensationType = ''
  formData.supplierAmount = ''
  formData.supplierCompensationType = ''
  formData.isReturn = '否'
  formData.replyAttachments = []
}

// 文件上传前验证
const beforeUpload = (file) => {
  const isValidType = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'application/pdf'
  ].includes(file.type)

  const isValidSize = file.size / 1024 / 1024 < 10 // 10MB

  if (!isValidType) {
    Message.error('文件类型不支持，请上传 .doc, .docx, .xls, .xlsx, .jpg, .png, .pdf 格式的文件')
    return false
  }

  if (!isValidSize) {
    Message.error('文件大小不能超过 10MB')
    return false
  }

  return true
}

// 提交处理
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      submitLoading.value = true

      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 1000))

      const submitData = {
        ...formData,
        afterSalesId: props.afterSalesData.id,
        orderNumber: props.afterSalesData.orderNumber,
        afterSalesNumber: props.afterSalesData.afterSalesNumber,
        totalAmount: computedTotalAmount.value,
        submitTime: new Date().toLocaleString(),
        submitter: currentUser.value
      }

      emit('submit', submitData)

      Message.success('回复提交成功')
      handleCancel()
    }
  } catch (error) {
    console.log('表单验证失败:', error)
    Message.error('请检查表单内容')
  } finally {
    submitLoading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  visible.value = false
  resetFormData()
  formRef.value?.resetFields()
}
</script>

<style scoped>
.reply-modal-content {
  padding: 0;
}

.basic-info-section {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  min-width: 200px;
}

.info-item .label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
}

.main-content {
  display: flex;
  gap: 24px;
  margin-bottom: 20px;
}

.left-section,
.right-section {
  flex: 1;
  background-color: #fff;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e6eb;
}

.form-group {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.form-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
  margin-right: 12px;
  line-height: 1.5;
}

.form-value {
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.product-table {
  width: 100%;
  margin-top: 8px;
}

.product-table table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e5e6eb;
}

.product-table th,
.product-table td {
  padding: 8px 12px;
  text-align: left;
  border: 1px solid #e5e6eb;
  font-size: 12px;
}

.product-table th {
  background-color: #f8f9fa;
  font-weight: 500;
  color: #666;
}

.product-table td {
  color: #333;
}

.amount-select {
  display: flex;
  align-items: center;
  margin-top: 8px;
  gap: 8px;
}

.amount-label {
  font-size: 14px;
  color: #666;
  min-width: 120px;
}

.total-amount {
  font-size: 16px;
  font-weight: 600;
  color: #f53f3f;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e5e6eb;
}

:deep(.arco-form-item-label) {
  font-weight: 500;
  color: #333;
}

:deep(.arco-modal-header) {
  border-bottom: 1px solid #e5e6eb;
}

:deep(.arco-modal-body) {
  padding: 20px;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .main-content {
    flex-direction: column;
  }

  .info-row {
    flex-direction: column;
    gap: 12px;
  }

  .info-item {
    min-width: auto;
  }
}
</style>
