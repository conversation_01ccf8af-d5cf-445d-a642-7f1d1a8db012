<template>
  <a-modal
    v-model:visible="visible"
    title="售后备注"
    width="600px"
    :ok-loading="submitLoading"
    ok-text="保存备注"
    cancel-text="取消"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="售后编号" field="afterSalesNumber">
        <a-input v-model="formData.afterSalesNumber" readonly />
      </a-form-item>
      
      <a-form-item label="当前备注" field="currentRemark">
        <a-textarea 
          v-model="formData.currentRemark" 
          readonly
          :rows="3"
          placeholder="暂无备注"
        />
      </a-form-item>
      
      <a-form-item label="新增备注" field="newRemark" required>
        <a-textarea
          v-model="formData.newRemark"
          placeholder="请输入新的备注内容"
          :rows="4"
        />
      </a-form-item>

      <a-form-item label="备注附件" field="remarkAttachments">
        <a-upload
          v-model:file-list="formData.remarkAttachments"
          action="/api/upload"
          :limit="10"
          multiple
          accept=".doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.pdf,.txt"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
        >
          <template #upload-button>
            <a-button>
              <template #icon><icon-upload /></template>
              上传附件
            </a-button>
          </template>
        </a-upload>
        <div class="upload-tip">
          支持的文件类型：.doc, .docx, .xls, .xlsx, .jpg, .jpeg, .png, .pdf, .txt，最多上传10个文件，单个文件不超过10M
        </div>
      </a-form-item>

      <!-- 显示已有附件 -->
      <a-form-item
        v-if="existingAttachments.length > 0"
        label="已有附件"
        field="existingAttachments"
      >
        <div class="existing-attachments">
          <div
            v-for="(attachment, index) in existingAttachments"
            :key="index"
            class="attachment-item"
          >
            <div class="attachment-info">
              <icon-file class="file-icon" />
              <span class="file-name">{{ getFileName(attachment) }}</span>
              <span class="file-size">{{ getFileSize(attachment) }}</span>
            </div>
            <div class="attachment-actions">
              <a-button
                type="text"
                size="small"
                @click="handlePreviewAttachment(attachment)"
              >
                <template #icon><icon-eye /></template>
                预览
              </a-button>
              <a-button
                type="text"
                size="small"
                @click="handleDownloadAttachment(attachment)"
              >
                <template #icon><icon-download /></template>
                下载
              </a-button>
            </div>
          </div>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  afterSalesData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'submit'])

const formRef = ref()
const visible = ref(false)
const submitLoading = ref(false)

// 表单数据
const formData = reactive({
  afterSalesNumber: '',
  currentRemark: '',
  newRemark: '',
  remarkAttachments: []
})

// 已有附件
const existingAttachments = ref([])

// 表单验证规则
const rules = {
  newRemark: [
    { required: true, message: '请输入备注内容' }
  ]
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.afterSalesData && typeof props.afterSalesData === 'object') {
    // 填充表单数据
    formData.afterSalesNumber = props.afterSalesData.afterSalesNumber || ''
    formData.currentRemark = props.afterSalesData.remark || ''
    formData.newRemark = ''
    formData.remarkAttachments = []

    // 设置已有附件
    existingAttachments.value = props.afterSalesData.remarkAttachments || []
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 文件上传前验证
const beforeUpload = (file) => {
  const isValidType = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'application/pdf',
    'text/plain'
  ].includes(file.type)

  const isValidSize = file.size / 1024 / 1024 < 10 // 10MB

  if (!isValidType) {
    Message.error('文件类型不支持，请上传 .doc, .docx, .xls, .xlsx, .jpg, .jpeg, .png, .pdf, .txt 格式的文件')
    return false
  }

  if (!isValidSize) {
    Message.error('文件大小不能超过 10MB')
    return false
  }

  return true
}

// 文件上传成功回调
const handleUploadSuccess = (response, file) => {
  console.log('文件上传成功:', response, file)
  Message.success(`文件 ${file.name} 上传成功`)
}

// 文件上传失败回调
const handleUploadError = (error, file) => {
  console.error('文件上传失败:', error, file)
  Message.error(`文件 ${file.name} 上传失败`)
}

// 获取文件名
const getFileName = (attachment) => {
  if (typeof attachment === 'string') {
    return attachment.split('/').pop() || attachment
  }
  return attachment.name || attachment.fileName || '未知文件'
}

// 获取文件大小
const getFileSize = (attachment) => {
  if (typeof attachment === 'object' && attachment.size) {
    const size = attachment.size
    if (size < 1024) return `${size}B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
    return `${(size / (1024 * 1024)).toFixed(1)}MB`
  }
  return ''
}

// 预览附件
const handlePreviewAttachment = (attachment) => {
  console.log('预览附件:', attachment)

  let url = ''
  if (typeof attachment === 'string') {
    url = attachment
  } else if (attachment.url) {
    url = attachment.url
  } else if (attachment.path) {
    url = attachment.path
  }

  if (url) {
    // 判断文件类型
    const fileExtension = url.split('.').pop().toLowerCase()
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']
    const pdfTypes = ['pdf']

    if (imageTypes.includes(fileExtension)) {
      // 图片预览
      window.open(url, '_blank')
    } else if (pdfTypes.includes(fileExtension)) {
      // PDF预览
      window.open(url, '_blank')
    } else {
      // 其他文件类型直接下载
      handleDownloadAttachment(attachment)
    }
  } else {
    Message.warning('无法预览此文件')
  }
}

// 下载附件
const handleDownloadAttachment = (attachment) => {
  console.log('下载附件:', attachment)

  let url = ''
  let fileName = ''

  if (typeof attachment === 'string') {
    url = attachment
    fileName = attachment.split('/').pop() || 'download'
  } else {
    url = attachment.url || attachment.path || ''
    fileName = attachment.name || attachment.fileName || 'download'
  }

  if (url) {
    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    link.target = '_blank'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    Message.success('开始下载文件')
  } else {
    Message.error('无法下载此文件')
  }
}

// 提交处理
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (valid) {
      submitLoading.value = true

      // 模拟API请求
      await new Promise(resolve => setTimeout(resolve, 800))

      const submitData = {
        ...formData,
        afterSalesId: props.afterSalesData.id,
        updateTime: new Date().toLocaleString(),
        // 包含新上传的附件信息
        newAttachments: formData.remarkAttachments.map(file => ({
          name: file.name,
          url: file.response?.url || file.url,
          size: file.size,
          type: file.type
        }))
      }

      emit('submit', submitData)

      Message.success('售后备注保存成功')
      handleCancel()
    }
  } catch (error) {
    console.log('表单验证失败:', error)
    Message.error('请检查表单内容')
  } finally {
    submitLoading.value = false
  }
}

// 取消处理
const handleCancel = () => {
  visible.value = false
  formRef.value?.resetFields()
  // 重置附件数据
  formData.remarkAttachments = []
  existingAttachments.value = []
}
</script>

<style scoped>
:deep(.arco-form-item-label) {
  font-weight: 500;
}

.upload-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.existing-attachments {
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  padding: 12px;
  background-color: #fafafa;
}

.attachment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e6eb;
}

.attachment-item:last-child {
  border-bottom: none;
}

.attachment-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.file-icon {
  color: #666;
  font-size: 16px;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.attachment-actions .arco-btn {
  padding: 4px 8px;
  height: auto;
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .attachment-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .attachment-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
