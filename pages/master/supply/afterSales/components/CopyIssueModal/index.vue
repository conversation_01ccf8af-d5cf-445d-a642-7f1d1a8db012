<template>
  <a-modal
    v-model:visible="visible"
    width="600px"
    :footer="false"
    @cancel="handleCancel"
  >
    <template #title>
      <div class="modal-title">
        <span class="title-text">售后问题复制</span>
      </div>
    </template>

    <div class="copy-issue-content">
      <div class="section-title">【售后问题】</div>

      <div class="info-list">
        <div class="info-item">
          <span class="label">日期：</span>
          <span class="value">{{ formatDate(afterSalesData.afterSalesTime) }}</span>
        </div>

        <div class="info-item">
          <span class="label">批号：</span>
          <span class="value">{{ afterSalesData.afterSalesNumber || '250326' }}</span>
        </div>

        <div class="info-item">
          <span class="label">商品全称：</span>
          <span class="value">{{ afterSalesData.productName || '测试' }}</span>
        </div>

        <div class="info-item">
          <span class="label">数量：</span>
          <span class="value">{{ afterSalesData.quantity || '2' }}</span>
        </div>

        <div class="info-item">
          <span class="label">成本价：</span>
          <span class="value">{{ afterSalesData.costPrice || '0' }}元</span>
        </div>

        <div class="info-item">
          <span class="label">销售价：</span>
          <span class="value">{{ afterSalesData.salePrice || '2' }}元</span>
        </div>

        <div class="info-item">
          <span class="label">地址：</span>
          <span class="value">{{ afterSalesData.deliveryAddress || '北京市朝阳区朝阳区11-222' }}</span>
        </div>

        <div class="info-item">
          <span class="label">采购供应商：</span>
          <span class="value"></span>
        </div>

        <div class="info-item">
          <span class="label">跟单员：</span>
          <span class="value">{{ afterSalesData.follower || '广东八天科技发展有限公司' }}</span>
        </div>

        <div class="info-item">
          <span class="label">来源店：</span>
          <span class="value">{{ afterSalesData.sourceStore || '供应链-胡晓梅' }}</span>
        </div>

        <div class="info-item">
          <span class="label">售后店：</span>
          <span class="value">{{ afterSalesData.afterSalesStore || '供应链-胡晓梅' }}</span>
        </div>

        <div class="info-item">
          <span class="label">发货快递：</span>
          <span class="value"></span>
        </div>

        <div class="info-item">
          <span class="label">售后问题：</span>
          <span class="value">{{ afterSalesData.afterSalesContent || '测' }}</span>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  afterSalesData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(false)

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '2025年06月13日 16:43:39'

  try {
    // 如果已经是格式化的日期字符串，直接返回
    if (dateString.includes('年') && dateString.includes('月') && dateString.includes('日')) {
      return dateString
    }

    // 否则尝试解析并格式化
    const date = new Date(dateString)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')

    return `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`
  } catch (error) {
    return '2025年06月13日 16:43:39'
  }
}

// 监听弹窗显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 取消处理
const handleCancel = () => {
  visible.value = false
}
</script>

<style scoped>
.modal-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.title-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.version-number {
  font-size: 18px;
  font-weight: 700;
  color: #f53f3f;
}

.copy-issue-content {
  padding: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e6eb;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  font-size: 14px;
  line-height: 1.5;
}

.label {
  color: #666;
  min-width: 80px;
  flex-shrink: 0;
  font-weight: 500;
}

.value {
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 特殊样式调整 */
.info-item:nth-child(3) .value,
.info-item:nth-child(13) .value {
  /* 商品全称和售后问题可能较长，需要换行 */
  word-break: break-word;
}

:deep(.arco-modal-header) {
  border-bottom: 1px solid #e5e6eb;
  padding: 16px 20px;
}

:deep(.arco-modal-body) {
  padding: 20px;
}

:deep(.arco-modal-title) {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .info-item {
    flex-direction: column;
    gap: 4px;
  }

  .label {
    min-width: auto;
  }
}
</style>
