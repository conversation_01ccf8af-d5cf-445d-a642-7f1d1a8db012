<!--
 - 用户余额变动表格组件
 - 用于展示用户的余额变动记录
 -->
<template>
  <div class="balance-table-container">
    <ma-crud :options="balanceOptions" :columns="balanceColumns">
      <!-- 自定义列 - 变动类型 -->
      <template #type="{ record }">
        <a-tag :color="record.type === 1 ? 'blue' : 'orange'">
          {{ record.type === 1 ? '充值' : '消费' }}
        </a-tag>
      </template>
      <!-- 自定义列 - 金额 -->
      <template #amount="{ record }">
        <span :class="record.type === 1 ? 'text-green-500' : 'text-red-500'">
          {{ record.type === 1 ? '+' : '-' }}{{ record.amount }}
        </span>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { reactive } from 'vue';

// 组件属性定义
const props = defineProps({
  // 用户ID
  userId: {
    type: [String, Number],
    default: ''
  }
});

// 日期格式化函数
function formatDate(timestamp) {
  if (!timestamp) return '-';
  
  try {
    // 处理BigInt类型
    if (typeof timestamp === 'bigint') {
      timestamp = Number(timestamp);
    }
    
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '-';
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (e) {
    console.error('日期格式化错误:', e);
    return '-';
  }
}

// 余额变动配置
const balanceOptions = reactive({
  api: () => {
    // 模拟数据
    return Promise.resolve({
      success: true,
      message: '获取余额变动成功',
      code: 200,
      data: {
        items: Array(5).fill(null).map((_, index) => ({
          id: index + 1,
          type: Math.random() > 0.5 ? 1 : 2,
          amount: (Math.random() * 100).toFixed(2),
          description: Math.random() > 0.5 ? '充值' : '商品购买',
          created_at: Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000
        })),
        pageInfo: {
          total: 5,
          currentPage: 1,
          totalPage: 1
        }
      }
    });
  },
  showIndex: true,
  operationColumn: false,
  pageLayout: "fixed"
});

// 余额变动列配置
const balanceColumns = reactive([
  { title: '变动类型', dataIndex: 'type' },
  { title: '变动金额', dataIndex: 'amount' },
  { title: '变动描述', dataIndex: 'description' },
  { title: '变动时间', dataIndex: 'created_at', render: (_, record) => formatDate(record.created_at) }
]);
</script>

<style scoped>
.balance-table-container {
  margin-bottom: 20px;
}
</style>
