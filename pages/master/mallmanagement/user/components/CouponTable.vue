<!--
 - 用户优惠券表格组件
 - 用于展示用户持有的优惠券
 -->
<template>
  <div class="coupon-table-container">
    <ma-crud :options="couponOptions" :columns="couponColumns">
      <!-- 自定义列 - 优惠券状态 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { reactive } from 'vue';

// 组件属性定义
const props = defineProps({
  // 用户ID
  userId: {
    type: [String, Number],
    default: ''
  }
});

// 日期格式化函数
function formatDate(timestamp) {
  if (!timestamp) return '-';
  
  try {
    // 处理BigInt类型
    if (typeof timestamp === 'bigint') {
      timestamp = Number(timestamp);
    }
    
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '-';
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (e) {
    console.error('日期格式化错误:', e);
    return '-';
  }
}

// 获取优惠券状态颜色
function getStatusColor(status) {
  const statusMap = {
    0: 'gray',    // 未使用
    1: 'green',   // 可使用
    2: 'red',     // 已使用
    3: 'orange'   // 已过期
  };
  return statusMap[status] || 'gray';
}

// 获取优惠券状态文本
function getStatusText(status) {
  const statusMap = {
    0: '未使用',
    1: '可使用',
    2: '已使用',
    3: '已过期'
  };
  return statusMap[status] || '未知状态';
}

// 优惠券配置
const couponOptions = reactive({
  api: () => {
    // 模拟数据
    return Promise.resolve({
      success: true,
      message: '获取优惠券成功',
      code: 200,
      data: {
        items: Array(5).fill(null).map((_, index) => ({
          id: index + 1,
          name: `${Math.floor(Math.random() * 50) + 10}元优惠券`,
          amount: Math.floor(Math.random() * 50) + 10,
          status: Math.floor(Math.random() * 4),
          valid_until: Date.now() + Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000,
          created_at: Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000
        })),
        pageInfo: {
          total: 5,
          currentPage: 1,
          totalPage: 1
        }
      }
    });
  },
  showIndex: true,
  operationColumn: false,
  pageLayout: "fixed"
});

// 优惠券列配置
const couponColumns = reactive([
  { title: '优惠券名称', dataIndex: 'name' },
  { title: '优惠金额', dataIndex: 'amount', render: (_, record) => `￥${record.amount}` },
  { title: '优惠券状态', dataIndex: 'status' },
  { title: '有效期至', dataIndex: 'valid_until', render: (_, record) => formatDate(record.valid_until) },
  { title: '领取时间', dataIndex: 'created_at', render: (_, record) => formatDate(record.created_at) }
]);
</script>

<style scoped>
.coupon-table-container {
  margin-bottom: 20px;
}
</style>
