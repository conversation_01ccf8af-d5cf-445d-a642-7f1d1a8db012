<!--
 - 商城用户详情抽屉组件
 - 用于展示商城用户的详细信息
 -->
<template>
  <a-drawer 
    :width="'65%'" 
    :visible="visible" 
    @ok="handleOk" 
    @cancel="handleCancel" 
    unmountOnClose
  >
    <template #title>
      用户详情
    </template>
    
    <div class="user-detail-container">
      <!-- 用户头像和基本信息 -->
      <div class="user-header mb-4 pb-4 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <a-avatar 
              :size="64" 
              :image-url="record.avatar || '/assets/images/default-avatar.png'" 
              class="mr-4"
            >
              <template #icon>
                <icon-user />
              </template>
            </a-avatar>
            <div>
              <h3 class="text-lg font-medium">{{ record.nickname || record.username || '网宝' }}</h3>
              <p class="text-gray-500">余额：{{ record.balance || '0.00' }}</p>
              <p class="text-gray-500">积分：{{ record.points || '30.00' }}</p>
            </div>
          </div>
          <div>
            <a-button type="primary" @click="handleEdit">编辑</a-button>
          </div>
        </div>
        
        <div class="user-stats grid grid-cols-2 gap-4">
          <div class="stat-item">
            <div class="text-gray-500">总订单数：</div>
            <div>{{ record.order_count || '0' }}</div>
          </div>
          <div class="stat-item">
            <div class="text-gray-500">总消费金额：</div>
            <div>{{ record.total_amount || '0.00' }}</div>
          </div>
          <div class="stat-item">
            <div class="text-gray-500">本月订单：</div>
            <div>{{ record.month_order_count || '0' }}</div>
          </div>
          <div class="stat-item">
            <div class="text-gray-500">本月消费金额：</div>
            <div>{{ record.month_amount || '0.00' }}</div>
          </div>
          <div class="stat-item">
            <div class="text-gray-500">累计优惠：</div>
            <div>{{ record.total_discount || '0' }}</div>
          </div>
          <div class="stat-item">
            <div class="text-gray-500">本月优惠：</div>
            <div>{{ record.month_discount || '0' }}</div>
          </div>
        </div>
      </div>
      
      <!-- 选项卡导航 -->
      <a-tabs default-active-key="1">
        <a-tab-pane key="1" title="用户信息">
          <div class="user-info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">基本信息</h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="info-item">
                <div class="text-gray-500">用户账号：</div>
                <div>{{ record.username || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">手机号码：</div>
                <div>{{ record.phone || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">生日：</div>
                <div>{{ record.birthday || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">性别：</div>
                <div>{{ record.gender === 1 ? '男' : record.gender === 2 ? '女' : '保密' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">用户邮箱：</div>
                <div>{{ record.email || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">用户地址：</div>
                <div>{{ record.address || '-' }}</div>
              </div>
            </div>
          </div>
          
          <div class="user-info-section mt-6">
            <h4 class="text-base font-medium mb-2 text-blue-500">实名信息</h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="info-item">
                <div class="text-gray-500">真实姓名：</div>
                <div>{{ record.real_name || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">认证状态：</div>
                <div>{{ record.verified ? '已认证' : '未认证' }}</div>
              </div>
            </div>
            <div class="text-gray-400 mt-2 text-sm">注：身份证正反面照片，点击查看。</div>
          </div>
          
          <div class="user-info-section mt-6">
            <h4 class="text-base font-medium mb-2 text-blue-500">密码</h4>
            <div class="grid grid-cols-1 gap-4">
              <div class="info-item">
                <div class="text-gray-500">登录密码：</div>
                <div>******</div>
              </div>
            </div>
          </div>
          
          <div class="user-info-section mt-6">
            <h4 class="text-base font-medium mb-2 text-blue-500">用户概况</h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="info-item">
                <div class="text-gray-500">用户状态：</div>
                <div>{{ record.status === 1 ? '正常' : '已冻结' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">用户等级：</div>
                <div>{{ record.level_name || 'VIP1' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">用户标签：</div>
                <div>{{ record.tags || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">用户分组：</div>
                <div>{{ record.group || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">推广渠道：</div>
                <div>{{ record.channel || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">推广人编号：</div>
                <div>{{ record.referrer || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">注册时间：</div>
                <div>{{ formatDate(record.created_at) || '-' }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">登录时间：</div>
                <div>{{ formatDate(record.last_login_time) || '-' }}</div>
              </div>
            </div>
          </div>
        </a-tab-pane>
        
        <a-tab-pane key="2" title="交易订单">
          <OrderTable :userId="record.id" />
        </a-tab-pane>
        
        <a-tab-pane key="3" title="积分明细">
          <PointsTable :userId="record.id" />
        </a-tab-pane>
        
        <a-tab-pane key="4" title="签到记录">
          <SignTable :userId="record.id" />
        </a-tab-pane>
        
        <a-tab-pane key="5" title="持有优惠券">
          <CouponTable :userId="record.id" />
        </a-tab-pane>
        
        <a-tab-pane key="6" title="余额变动">
          <BalanceTable :userId="record.id" />
        </a-tab-pane>
        
        <a-tab-pane key="7" title="用户绑定">
          <BindTable :userId="record.id" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-drawer>
  
  <!-- 用户编辑弹窗 -->
  <a-modal
    v-model:visible="editModalVisible"
    title="编辑用户"
    :width="700"
    @ok="handleEditSubmit"
    @cancel="editModalVisible = false"
  >
    <ma-form :model="editFormData" layout="vertical" ref="editFormRef">
      <!-- 基本信息 -->
      <ma-form-item label="用户名" field="username" :rules="[{ required: true, message: '用户名必填' }]">
        <a-input v-model="editFormData.username" placeholder="请输入用户名" />
      </ma-form-item>
      
      <ma-form-item label="用户昵称" field="nickname" :rules="[{ required: true, message: '用户昵称必填' }]">
        <a-input v-model="editFormData.nickname" placeholder="请输入用户昵称" />
      </ma-form-item>
      
      <ma-form-item label="邮箱" field="email" :rules="[{ required: true, type: 'email', message: '请输入正确的邮箱' }]">
        <a-input v-model="editFormData.email" placeholder="请输入邮箱" />
      </ma-form-item>
      
      <ma-form-item label="手机号" field="phone" :rules="[{ required: true, match: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: '请输入正确的手机号码' }]">
        <a-input v-model="editFormData.phone" placeholder="请输入手机号" />
      </ma-form-item>
      
      <ma-form-item label="状态" field="status">
        <a-radio-group v-model="editFormData.status">
          <a-radio :value="1">正常</a-radio>
          <a-radio :value="0">停用</a-radio>
        </a-radio-group>
      </ma-form-item>
      
      <ma-form-item label="用户头像" field="avatar">
        <a-upload
          action="/api/v1/master/system/uploadFile"
          list-type="picture-card"
          :file-list="avatarFileList"
          :limit="1"
          :accept="'.jpg,.jpeg,.png,.gif'"
          :size="2 * 1024 * 1024"
          :show-file-list="true"
          :multiple="false"
          :show-upload-button="true"
          @change="handleAvatarChange"
        >
          <template #upload-button>
            <div>
              <icon-plus />
              <div>上传头像</div>
            </div>
          </template>
          <template #upload-tip>
            <div class="text-gray-400 text-xs mt-1">建议上传正方形图片，大小不超过2MB</div>
          </template>
        </a-upload>
      </ma-form-item>
    </ma-form>
  </a-modal>
</template>

<script setup>
import { defineProps, defineEmits, ref, onMounted } from 'vue';
import { IconUser, IconPlus } from '@arco-design/web-vue/es/icon';
import { Message } from '@arco-design/web-vue';
import mallmanagementApi from '@/api/master/mallmanagement';
import OrderTable from './OrderTable.vue';
import PointsTable from './PointsTable.vue';
import SignTable from './SignTable.vue';
import CouponTable from './CouponTable.vue';
import BalanceTable from './BalanceTable.vue';
import BindTable from './BindTable.vue';

// 组件属性定义
const props = defineProps({
  // 是否可见
  visible: {
    type: Boolean,
    default: false
  },
  // 用户记录数据
  record: {
    type: Object,
    default: () => ({})
  },
  // 列配置
  columns: {
    type: Array,
    default: () => []
  },
  // CRUD组件引用
  crudRef: {
    type: Object,
    default: null
  }
});

// 组件事件定义
const emit = defineEmits(['update:visible', 'ok', 'cancel']);

// 日期格式化函数
function formatDate(timestamp) {
  if (!timestamp) return '-';
  
  try {
    // 处理BigInt类型
    if (typeof timestamp === 'bigint') {
      timestamp = Number(timestamp);
    }
    
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '-';
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (e) {
    console.error('日期格式化错误:', e);
    return '-';
  }
}

// 编辑弹窗可见性控制
const editModalVisible = ref(false);

// 编辑表单数据
const editFormData = ref({});

// 头像文件列表
const avatarFileList = ref([]);

// 编辑按钮处理函数
function handleEdit() {
  // 确保编辑弹窗层级高于抽屉
  ensureModalZIndex();
  
  // 初始化编辑表单数据
  editFormData.value = { ...props.record };
  
  // 初始化头像文件列表
  avatarFileList.value = [];
  if (editFormData.value.avatar) {
    avatarFileList.value = [{
      uid: '1',
      name: '用户头像',
      url: editFormData.value.avatar,
      status: 'done'
    }];
  }
  
  // 显示编辑弹窗
  editModalVisible.value = true;
}

// 编辑表单引用
const editFormRef = ref(null);

// 处理头像上传变化
function handleAvatarChange(fileList) {
  avatarFileList.value = fileList;
  
  // 如果有新上传的文件，则更新头像 URL
  const latestFile = fileList[fileList.length - 1];
  if (latestFile && latestFile.status === 'done' && latestFile.response) {
    // 服务器返回的数据中包含 data.url 字段
    if (latestFile.response.data && latestFile.response.data.url) {
      editFormData.value.avatar = latestFile.response.data.url;
    } else if (latestFile.response.url) {
      editFormData.value.avatar = latestFile.response.url;
    }
  } else if (fileList.length > 0 && fileList[0].url) {
    // 如果有现有文件的 URL
    editFormData.value.avatar = fileList[0].url;
  } else {
    // 如果没有文件，清空头像
    editFormData.value.avatar = '';
  }
}

// 处理编辑提交
function handleEditSubmit() {
  // 验证表单
  editFormRef.value.validate().then(valid => {
    if (valid) {
      // 处理表单数据
      const params = { ...editFormData.value };
      
      // 根据需要转换字段名称
      if (params.dept_id) {
        params.deptId = params.dept_id;
        delete params.dept_id;
      }
      
      if (params.role_id) {
        params.roleId = params.role_id;
        delete params.role_id;
      }
      
      // 删除不需要的字段
      delete params.created_at;
      delete params.last_login_time;
      delete params.order_count;
      delete params.total_amount;
      delete params.month_order_count;
      delete params.month_amount;
      delete params.total_discount;
      delete params.month_discount;
      
      // 调用更新API
      const userApi = mallmanagementApi.user;
      userApi.update(params.id, params).then(res => {
        if (res.code === 200) {
          Message.success('用户信息更新成功');
          // 关闭编辑弹窗
          editModalVisible.value = false;
          // 更新记录数据
          Object.assign(props.record, editFormData.value);
          // 如果提供了CRUD引用，则刷新列表
          if (props.crudRef) {
            props.crudRef.refresh();
          }
        } else {
          Message.error(res.message || '用户信息更新失败');
        }
      }).catch(err => {
        console.error('用户信息更新失败:', err);
        Message.error('用户信息更新失败');
      });
    }
  }).catch(errors => {
    console.error('表单验证失败:', errors);
    Message.error('表单验证失败，请检查输入');
  });
}

// 确保模态框层级高于抽屉
function ensureModalZIndex() {
  // 移除可能存在的旧样式
  const oldStyle = document.getElementById('edit-modal-style');
  if (oldStyle) {
    oldStyle.remove();
  }
  
  // 添加新样式确保编辑弹窗层级高于抽屉
  const styleElement = document.createElement('style');
  styleElement.id = 'edit-modal-style';
  styleElement.textContent = `
    .arco-modal {
      z-index: 2000 !important;
    }
    .arco-modal-mask {
      z-index: 1999 !important;
    }
    .arco-modal-container {
      z-index: 2001 !important;
    }
  `;
  document.head.appendChild(styleElement);
}

// 确认按钮处理函数
function handleOk() {
  emit('ok');
  emit('update:visible', false);
}

// 取消按钮处理函数
function handleCancel() {
  emit('cancel');
  emit('update:visible', false);
}

// 组件挂载时执行
onMounted(() => {
  // 确保模态框层级高于抽屉
  ensureModalZIndex();
});

</script>

<style scoped>
/* 用户详情容器样式 */
.user-detail-container {
  padding: 0 16px;
}

/* 用户头像和基本信息区域 */
.user-header {
  margin-bottom: 20px;
}

/* 统计数据项样式 */
.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  font-size: 14px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  font-size: 14px;
}

/* 信息区块样式 */
.user-info-section {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

/* 选项卡内容区域样式 */
.empty-content {
  min-height: 200px;
}
</style>
