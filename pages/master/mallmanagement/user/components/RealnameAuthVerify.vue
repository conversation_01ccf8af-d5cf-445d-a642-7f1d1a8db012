<!-- 实名认证审核组件 -->
<template>
  <div class="realname-auth-verify">
    <!-- 无认证信息时显示的提示 -->
    <div v-if="!authInfo || loading" class="empty-state">
      <a-spin v-if="loading" />
      <a-empty v-else description="该用户暂未提交实名认证信息" />
    </div>
    
    <!-- 认证信息展示区域 -->
    <div v-else class="auth-info-container">
      <!-- 认证状态 -->
      <div class="status-section mb-4">
        <div class="p-4 rounded-md" :class="getStatusClass(authInfo.auth_status)">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <icon-info v-if="authInfo.auth_status === 0" class="mr-2 text-gray-500" />
              <icon-exclamation-circle v-else-if="authInfo.auth_status === 1" class="mr-2 text-blue-500" />
              <icon-check-circle v-else-if="authInfo.auth_status === 2" class="mr-2 text-green-500" />
              <icon-close-circle v-else-if="authInfo.auth_status === 3" class="mr-2 text-red-500" />
              <span class="font-medium">当前状态：{{ getStatusText(authInfo.auth_status) }}</span>
            </div>
            <div v-if="authInfo.auth_status === 1">
              <a-button type="primary" size="small" class="mr-2" @click="handleApprove">通过审核</a-button>
              <a-button status="danger" size="small" @click="showRejectModal">驳回申请</a-button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 认证信息详情 -->
      <a-card title="认证信息" class="mb-4">
        <a-descriptions :column="2" size="large" bordered>
          <a-descriptions-item label="真实姓名">{{ authInfo.real_name }}</a-descriptions-item>
          <a-descriptions-item label="身份证号">
            <div class="flex items-center">
              <a-button type="text" size="mini" @click="toggleShowOriginal('idCard')" class="mr-2">
                <template #icon>
                  <icon-eye v-if="showOriginalData.idCard" />
                  <icon-eye-invisible v-else />
                </template>
              </a-button>
              {{ showOriginalData.idCard ? authInfo.identity_no : maskIdCard(authInfo.identity_no) }}
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="认证手机">
            <div class="flex items-center">
              <a-button type="text" size="mini" @click="toggleShowOriginal('phone')" class="mr-2">
                <template #icon>
                  <icon-eye v-if="showOriginalData.phone" />
                  <icon-eye-invisible v-else />
                </template>
              </a-button>
              {{ showOriginalData.phone ? authInfo.auth_phone : maskPhone(authInfo.auth_phone) }}
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="银行卡号">
            <div class="flex items-center">
              <a-button type="text" size="mini" @click="toggleShowOriginal('bankCard')" class="mr-2">
                <template #icon>
                  <icon-eye v-if="showOriginalData.bankCard" />
                  <icon-eye-invisible v-else />
                </template>
              </a-button>
              {{ showOriginalData.bankCard ? authInfo.bank_card_no : maskBankCard(authInfo.bank_card_no) }}
            </div>
          </a-descriptions-item>
          <a-descriptions-item label="提交时间">{{ formatTimestamp(authInfo.created_at) }}</a-descriptions-item>
          <a-descriptions-item label="更新时间">{{ formatTimestamp(authInfo.updated_at) }}</a-descriptions-item>
          <a-descriptions-item label="审核备注" :span="2">{{ authInfo.audit_remark || '暂无备注' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>
      
      <!-- 身份证照片 -->
      <a-card title="身份证照片" class="mb-4">
        <div class="grid grid-cols-2 gap-4">
          <div class="id-card-front">
            <p class="mb-2 text-gray-500">身份证正面</p>
            <a-image
              :src="authInfo.id_card_front_url"
              width="100%"
              height="200"
              fit="cover"
              :preview-visible="false"
              @click="previewImage(authInfo.id_card_front_url)"
            />
          </div>
          <div class="id-card-back">
            <p class="mb-2 text-gray-500">身份证反面</p>
            <a-image
              :src="authInfo.id_card_back_url"
              width="100%"
              height="200"
              fit="cover"
              :preview-visible="false"
              @click="previewImage(authInfo.id_card_back_url)"
            />
          </div>
        </div>
      </a-card>
      
      <!-- 审核记录将在后续版本添加 -->
    </div>
    
    <!-- 驳回原因弹窗 -->
    <a-modal
      v-model:visible="rejectModalVisible"
      title="驳回原因"
      @ok="handleReject"
      @cancel="rejectModalVisible = false"
      ok-text="确认驳回"
      cancel-text="取消"
    >
      <a-form :model="rejectForm" layout="vertical">
        <a-form-item field="reason" label="驳回原因" required>
          <a-textarea
            v-model="rejectForm.reason"
            placeholder="请输入驳回原因，将通知给用户"
            :auto-size="{ minRows: 4, maxRows: 6 }"
          />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 图片预览 -->
    <a-modal
      v-model:visible="imagePreviewVisible"
      footer-less
      :width="800"
    >
      <img :src="previewImageUrl" style="width: 100%;" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconEye, IconEyeInvisible, IconInfo, IconExclamationCircle, IconCheckCircle, IconCloseCircle } from '@arco-design/web-vue/es/icon';
import dayjs from 'dayjs';
import mallmanagementApi from '@/api/master/mallmanagement';

// 接收用户ID参数
const props = defineProps({
  userId: {
    type: [String, Number, BigInt],
    required: true
  }
});

// 状态变量
const loading = ref(false);
const authInfo = ref(null);

// 控制是否显示原始数据（未脱敏）
const showOriginalData = ref({
  idCard: false,
  phone: false,
  bankCard: false
});

const rejectModalVisible = ref(false);
const rejectForm = ref({
  reason: ''
});
const imagePreviewVisible = ref(false);
const previewImageUrl = ref('');

// 监听userId变化，重新加载数据
watch(() => props.userId, (newVal) => {
  if (newVal) {
    loadAuthInfo();
  }
});

// 加载实名认证信息
const loadAuthInfo = async () => {
  loading.value = true;
  
  try {
    const res = await mallmanagementApi.realnameAuth.getInfo(props.userId);
    console.log('实名认证信息返回数据:', res);
    
    if (res.code === 200 && res.data) {
      // 确保 auth_status 是数字类型
      const data = { ...res.data };
      data.auth_status = Number(data.auth_status);
      authInfo.value = data;
      console.log('处理后的实名认证信息:', authInfo.value);
    } else {
      authInfo.value = null;
    }
  } catch (error) {
    console.error('获取实名认证信息失败:', error);
    Message.error('获取实名认证信息失败');
    authInfo.value = null;
  } finally {
    loading.value = false;
  }
};



// 处理审核通过
const handleApprove = async () => {
  try {
    const res = await mallmanagementApi.realnameAuth.approve(props.userId);
    
    if (res.code === 200) {
      Message.success('审核通过成功');
      // 重新加载数据
      loadAuthInfo();
    } else {
      Message.error(res.message || '审核操作失败');
    }
  } catch (error) {
    console.error('审核操作失败:', error);
    Message.error('审核操作失败');
  }
};

// 显示驳回弹窗
const showRejectModal = () => {
  rejectForm.value.reason = '';
  rejectModalVisible.value = true;
};

// 处理驳回操作
const handleReject = async () => {
  if (!rejectForm.value.reason.trim()) {
    Message.warning('请输入驳回原因');
    return;
  }
  
  try {
    const res = await mallmanagementApi.realnameAuth.reject(props.userId, rejectForm.value.reason);
    
    if (res.code === 200) {
      Message.success('已驳回该认证申请');
      rejectModalVisible.value = false;
      // 重新加载数据
      loadAuthInfo();
    } else {
      Message.error(res.message || '驳回操作失败');
    }
  } catch (error) {
    console.error('驳回操作失败:', error);
    Message.error('驳回操作失败');
  }
};

// 预览图片
const previewImage = (url) => {
  previewImageUrl.value = url;
  imagePreviewVisible.value = true;
};

// 格式化时间戳
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '-';
  return dayjs(Number(timestamp)).format('YYYY-MM-DD HH:mm:ss');
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    0: '未认证',
    1: '等待审核',
    2: '认证通过',
    3: '认证失败'
  };
  return statusMap[status] || '未知状态';
};

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    0: 'bg-gray-100 text-gray-700', // 未认证
    1: 'bg-blue-100 text-blue-700', // 等待审核
    2: 'bg-green-100 text-green-700', // 认证通过
    3: 'bg-red-100 text-red-700' // 认证失败
  };
  return classMap[status] || 'bg-gray-100 text-gray-700';
};

// 获取图标类
const getIconClass = (status) => {
  const iconMap = {
    0: 'i-carbon-information text-gray-500',
    1: 'i-carbon-warning-alt text-blue-500',
    2: 'i-carbon-checkmark-filled text-green-500',
    3: 'i-carbon-close-filled text-red-500'
  };
  return iconMap[status] || 'i-carbon-information text-gray-500';
};

// 获取提示类型 (已不再使用，保留以备用)
const getAlertType = (status) => {
  const typeMap = {
    0: 'info',
    1: 'warning',
    2: 'success',
    3: 'error'
  };
  return typeMap[status] || 'info';
};



// 切换显示原始数据或脱敏数据
const toggleShowOriginal = (field) => {
  showOriginalData.value[field] = !showOriginalData.value[field];
};

// 隐藏部分身份证号
const maskIdCard = (idCard) => {
  if (!idCard) return '-';
  return idCard.replace(/^(.{6})(.*)(.{4})$/, '$1********$3');
};

// 隐藏部分手机号
const maskPhone = (phone) => {
  if (!phone) return '-';
  return phone.replace(/^(.{3})(.*)(.{4})$/, '$1****$3');
};

// 隐藏部分银行卡号
const maskBankCard = (cardNo) => {
  if (!cardNo) return '-';
  return cardNo.replace(/^(.{4})(.*)(.{4})$/, '$1 **** **** $3');
};

// 组件挂载时加载数据
onMounted(() => {
  if (props.userId) {
    loadAuthInfo();
  }
});
</script>

<style scoped>
.realname-auth-verify {
  padding: 16px 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.audit-log-item {
  padding: 8px 0;
}
</style>
