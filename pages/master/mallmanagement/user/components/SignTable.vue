<!--
 - 用户签到记录表格组件
 - 用于展示用户的签到历史
 -->
<template>
  <div class="sign-table-container">
    <ma-crud :options="signOptions" :columns="signColumns">
      <!-- 自定义列 - 签到状态 -->
      <template #status="{ record }">
        <a-tag :color="record.status === 1 ? 'green' : 'red'">
          {{ record.status === 1 ? '成功' : '失败' }}
        </a-tag>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { reactive } from 'vue';

// 组件属性定义
const props = defineProps({
  // 用户ID
  userId: {
    type: [String, Number],
    default: ''
  }
});

// 日期格式化函数
function formatDate(timestamp) {
  if (!timestamp) return '-';
  
  try {
    // 处理BigInt类型
    if (typeof timestamp === 'bigint') {
      timestamp = Number(timestamp);
    }
    
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '-';
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (e) {
    console.error('日期格式化错误:', e);
    return '-';
  }
}

// 签到记录配置
const signOptions = reactive({
  api: () => {
    // 模拟数据
    return Promise.resolve({
      success: true,
      message: '获取签到记录成功',
      code: 200,
      data: {
        items: Array(5).fill(null).map((_, index) => ({
          id: index + 1,
          status: Math.random() > 0.2 ? 1 : 0,
          points: Math.floor(Math.random() * 10) + 1,
          ip: `192.168.1.${Math.floor(Math.random() * 255)}`,
          created_at: Date.now() - index * 24 * 60 * 60 * 1000
        })),
        pageInfo: {
          total: 5,
          currentPage: 1,
          totalPage: 1
        }
      }
    });
  },
  showIndex: true,
  operationColumn: false,
  pageLayout: "fixed"
});

// 签到记录列配置
const signColumns = reactive([
  { title: '签到状态', dataIndex: 'status' },
  { title: '获得积分', dataIndex: 'points' },
  { title: '签到IP', dataIndex: 'ip' },
  { title: '签到时间', dataIndex: 'created_at', render: (_, record) => formatDate(record.created_at) }
]);
</script>

<style scoped>
.sign-table-container {
  margin-bottom: 20px;
}
</style>
