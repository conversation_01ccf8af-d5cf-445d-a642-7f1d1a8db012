<!--
 - 用户订单表格组件
 - 用于展示用户的订单记录，与订单管理页面保持一致
 -->
<template>
  <div class="order-table-container">
    <a-table :columns="columns" :data="tableData" :pagination="false" :span-method="spanMethod"
      class="order-table" :hoverable="false">
      
      <!-- 商品信息单元格 -->
      <template #productInfo-cell="{ record }">
        <div v-if="record.type === 'header'" class="order-header-cell">
          <div class="order-header-left">
            <span>系统订单编号： {{ record.originalId }}</span>
            <span v-if="record.thirdPartyOrderId">三方订单编号： {{ record.thirdPartyOrderId }}</span>
            <a-button shape="circle" size="mini" class="copy-btn-inline" @click="copyOrderId(record.originalId)">
              <icon-copy />
            </a-button>
            <span class="order-time-inline">下单时间： {{ record.orderTime }}</span>
            <span v-if="record.order_source">订单来源： {{ record.order_source }} </span>
          </div>
        </div>
        <div v-else class="product-info-cell-content">
          <a-image width="80" height="60" :src="record.imageUrl || '/placeholder.png'" class="product-image" />
          <div class="product-details">
            <span class="product-name">{{ record.productName }}</span>
            <span v-if="record.color" style="color: #999;">{{ record.color }}</span>
            <div class="product-meta">
              <div v-if="record.productId">系统SPU：{{ record.productId }}</div>
              <div v-if="record.merchantCode">商品编码：{{ record.merchantCode }}</div>
            </div>
          </div>
        </div>
      </template>
      
      <!-- 单价/数量单元格 -->
      <template #priceQuantity-cell="{ record }">
        <div v-if="record.type === 'details'" class="price-cell-content">
          <div class="price">¥{{ record.price }}</div>
          <div class="quantity">x{{ record.quantity }}</div>
        </div>
      </template>
      
      <!-- 收件信息单元格 -->
      <template #receiver-cell="{ record }">
        <div v-if="record.type === 'details' && record.productIndex === 0" class="receiver-info">
          {{ record.address || '未设置收货地址' }}
        </div>
      </template>
      
      <!-- 实付金额单元格 -->
      <template #payment-cell="{ record }">
        <div class="price highlight" v-if="record.type === 'details' && record.productIndex === 0">
          ¥{{ record.totalPayable }}
        </div>
      </template>
      
      <!-- 支付方式单元格 -->
      <template #payment-method-cell="{ record }">
        <div v-if="record.type === 'details' && record.productIndex === 0">
          {{ record.paymentMethod }}
        </div>
      </template>
      
      <!-- 配送方式单元格 -->
      <template #delivery-method-cell="{ record }">
        <div v-if="record.type === 'details' && record.productIndex === 0">
          {{ record.deliveryMethod }}
        </div>
      </template>
      
      <!-- 订单状态单元格 -->
      <template #status-cell="{ record }">
        <div v-if="record.type === 'details' && record.productIndex === 0" class="status-cell-content">
          <div class="status-tag">
            <a-tag :color="getOrderStatusColor(record.orderStatus)">
              {{ record.orderStatus }}
            </a-tag>
          </div>
          <div v-if="record.deliveryTime" class="delivery-time">
            预计时效{{ record.deliveryTime }}
          </div>
          <div v-if="record.deliveryDetail" class="delivery-detail">
            {{ record.deliveryDetail }}
          </div>
        </div>
      </template>
      
      <!-- 操作单元格 -->
      <template #operations-cell="{ record }">
        <div v-if="record.type === 'details' && record.productIndex === 0" class="operations-cell-content">
          <div class="operation-buttons">
            <a-button type="text" size="small" @click="viewOrderDetail(record.originalId)">查看详情</a-button>
            
            <!-- 待发货状态 -->
            <template v-if="record.orderStatus === '待发货' && record.isShippable">
              <a-button type="text" size="small" status="primary" @click="handleDelivery(record)">发货</a-button>
              <a-popconfirm content="是否确认取消订单？" @ok="handleCancelOrder(record.originalId)">
                <a-button type="text" size="small" status="warning">取消订单</a-button>
              </a-popconfirm>
            </template>
            
            <!-- 待付款状态 -->
            <template v-if="record.orderStatus === '待付款'">
              <a-popconfirm content="是否确认取消订单？" @ok="handleCancelOrder(record.originalId)">
                <a-button type="text" size="small" status="warning">取消订单</a-button>
              </a-popconfirm>
            </template>
            
            <!-- 已发货状态 -->
            <template v-if="record.orderStatus === '已发货'">
              <a-popconfirm content="是否确认收货？" @ok="handleConfirmReceipt(record.originalId)">
                <a-button type="text" size="small" status="primary">确认收货</a-button>
              </a-popconfirm>
            </template>
          </div>
        </div>
      </template>
    </a-table>
    
    <!-- 发货对话框组件 -->
    <OrderDeliveryDialog ref="deliveryDialogRef" />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { IconCopy } from '@arco-design/web-vue/es/icon';
import { useRouter } from 'vue-router';
import orderApi from '@/api/master/order';

// 引入发货对话框组件
const OrderDeliveryDialog = defineAsyncComponent(() => 
  import('@/pages/master/order/orderManage/components/OrderDeliveryDialog.vue')
);

// 发货对话框引用
const deliveryDialogRef = ref(null);

const props = defineProps({
  // 用户ID
  userId: {
    type: [String, Number],
    required: true
  }
});

// 获取路由实例
const router = useRouter();

// 原始订单数据
const originalOrders = ref([]);

// 在组件挂载时获取订单数据
onMounted(() => {
  getOrders();
});

// 获取订单数据
const getOrders = () => {
  // 使用订单列表页面的相同接口，但增加用户ID过滤
  orderApi.getOrders({ userId: props.userId }).then(res => {
    if (res.code === 200 && res.data && res.data.items) {
      originalOrders.value = res.data.items.map(order => transformOrderData(order));
      console.log('获取用户订单数据成功', res.data);
    } else {
      Message.error('获取用户订单数据失败');
      console.error('获取用户订单数据失败', res);
      // 使用模拟数据
      originalOrders.value = getMockOrders();
    }
  }).catch(err => {
    Message.error('获取用户订单数据失败');
    console.error('获取用户订单数据失败', err);
    // 使用模拟数据
    originalOrders.value = getMockOrders();
  });
};

// 将API返回的订单数据转换为表格需要的格式
// 与订单列表页面使用相同的转换函数
const transformOrderData = (order) => {
  // 构建商品数据
  const products = order.items && order.items.length > 0
    ? order.items.map(item => ({
      productId: item.id,
      productName: item.productName,
      imageUrl: item.productImage || '/placeholder.png',
      price: item.unitPrice,
      quantity: item.quantity,
      returnLink: null, // API中没有对应字段
      afterSaleStatus: '-', // API中没有对应字段
      merchantCode: item.thirdPartyProductCode || '-',
      color: item.skuSpecifications || '-'
    }))
    : [];

  return {
    id: order.id,
    order_source: order.orderSource,
    paymentMethod: order.paymentMethod || '未知',
    totalPayable: order.totalAmount,
    deliveryMethod: order.shippingMethod || '快递配送',
    paymentDetail: order.paymentSn || '-',
    address: order.shipping && order.shipping.id ? '已设置收货地址' : '未设置收货地址',
    consumer: order.userId || '-',
    orderStatus: order.orderStatusText,
    deliveryTime: null, // API中没有对应字段
    deliveryDetail: null, // API中没有对应字段
    isShippable: order.orderStatus === 1 && order.paymentStatus === 1 && order.shippingStatus === 0, // 已付款未发货的订单可发货
    hasAddressEdit: order.orderStatus < 2, // 未发货的订单可修改地址
    hasLogisticsCheck: order.shippingStatus > 0, // 已发货的订单可查看物流
    orderTime: formatDate(order.createdAt),
    products: products.length > 0 ? products : [{
      productId: 'unknown',
      productName: '无商品信息',
      imageUrl: '/placeholder.png',
      price: '0.00',
      quantity: 0,
      returnLink: null,
      afterSaleStatus: '-',
      merchantCode: '-',
      color: '-'
    }]
  };
};

// 获取模拟订单数据
const getMockOrders = () => {
  // 确保生成不同状态的订单，包括可发货的订单
  const orderStatuses = [
    '待付款',   // 第一个订单是待付款
    '待发货',   // 第二个订单是待发货
    '已发货'    // 第三个订单是已发货
  ];
  
  return Array(3).fill(null).map((_, index) => {
    // 根据订单状态设置对应的可发货状态
    const orderStatus = orderStatuses[index];
    const isShippable = orderStatus === '待发货'; // 只有待发货的订单可发货
    
    return {
      id: `ORD${100000 + index}`,
      order_source: '自采',
      paymentMethod: ['微信支付', '支付宝', '银行卡', '余额支付'][index % 4],
      totalPayable: (Math.random() * 1000).toFixed(2),
      deliveryMethod: '快递配送',
      paymentDetail: `PAY${200000 + index}`,
      address: '北京市海淀区西二旗路财富中心',
      consumer: props.userId,
      orderStatus: orderStatus,
      deliveryTime: '预计三天内送达',
      deliveryDetail: null,
      isShippable: isShippable,
      hasAddressEdit: orderStatus === '待付款' || orderStatus === '待发货',
      hasLogisticsCheck: orderStatus === '已发货',
      orderTime: formatDate(Date.now() - (30 - index * 10) * 24 * 60 * 60 * 1000),
      products: Array(Math.floor(Math.random() * 2) + 1).fill(null).map((_, itemIndex) => ({
        productId: `PRD${200000 + itemIndex}`,
        productName: `商品${itemIndex + 1}`,
        imageUrl: '/placeholder.png',
        price: (Math.random() * 200 + 100).toFixed(2),
        quantity: Math.floor(Math.random() * 3) + 1,
        returnLink: null,
        afterSaleStatus: '-',
        merchantCode: `SKU${300000 + itemIndex}`,
        color: `规格${itemIndex + 1}`
      }))
    };
  });
};

// 日期格式化函数
function formatDate(timestamp) {
  if (!timestamp) return '-';
  
  try {
    // 处理BigInt类型
    if (typeof timestamp === 'bigint') {
      timestamp = Number(timestamp);
    }
    
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '-';
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-');
  } catch (e) {
    console.error('日期格式化错误:', e);
    return '-';
  }
}

// 分页数据
const tableData = computed(() => {
  const data = [];
  originalOrders.value.forEach(order => {
    const productCount = order.products.length;
    data.push({ // 表头行数据
      rowId: `${order.id}-header`, // 表头唯一ID
      type: 'header',
      originalId: order.id,
      order_source: order.order_source,
      orderTime: order.orderTime,
      productCount: productCount, // 用于合并单元格计算
    });
    // 详情行数据 (每个商品一行)
    order.products.forEach((product, productIndex) => {
      data.push({
        // 订单级信息 (用于合并单元格或在详情行显示)
        originalId: order.id,
        order_source: order.order_source,
        paymentMethod: order.paymentMethod,
        deliveryMethod: order.deliveryMethod, // 添加配送方式字段
        totalPayable: order.totalPayable,
        paymentDetail: order.paymentDetail,
        address: order.address,
        consumer: order.consumer,
        orderStatus: order.orderStatus,
        deliveryTime: order.deliveryTime,
        deliveryDetail: order.deliveryDetail,
        isShippable: order.isShippable, // 订单级别的可发货状态
        hasAddressEdit: order.hasAddressEdit,
        hasLogisticsCheck: order.hasLogisticsCheck,
        // 商品级信息
        ...product,
        // 表格行属性
        rowId: `${order.id}-${product.productId}-details`, // 详情行唯一ID (订单ID + 商品ID)
        type: 'details',
        productIndex: productIndex, // 商品在订单中的索引 (0-based)
        productCount: productCount, // 订单总商品数
      });
    });
  });
  return data;
});

// 获取订单状态颜色
const getOrderStatusColor = (status) => {
  const statusMap = {
    '待付款': 'orange',
    '待发货': 'blue',
    '已发货': 'green',
    '已完成': 'green',
    '已关闭': 'red',
    '已取消': 'red'
  };
  return statusMap[status] || 'blue';
};

// 复制订单ID函数
const copyOrderId = async (id) => {
  try {
    await navigator.clipboard.writeText(id);
    Message.success(`订单号 ${id} 已复制`);
  } catch (err) {
    Message.error('复制失败');
    console.error('Failed to copy: ', err);
  }
};

// 跳转到订单详情页
const viewOrderDetail = (id) => {
  router.push(`/master/order/orderManage/detail/${id}`);
};

// 处理发货功能
const handleDelivery = (record) => {
  // 构建发货所需的数据
  const deliveryInfo = {
    receiver: record.consumer || '未设置收货人',
    phone: record.consumerPhone || '未设置联系电话',
    address: record.address || '未设置收货地址'
  };
  
  // 构建商品数据
  const goodsData = [
    {
      name: record.productName,
      vendor: record.merchantName || '自营',
      price: record.price,
      quantity: record.quantity,
      imageUrl: record.imageUrl
    }
  ];
  
  // 打开发货对话框
  if (deliveryDialogRef.value) {
    deliveryDialogRef.value.open({
      orderId: record.originalId,
      deliveryInfo,
      goodsData,
      onConfirm: (shippingData) => {
        // 调用发货API
        orderApi.shipOrder(record.originalId, shippingData).then(res => {
          if (res && res.code === 200) {
            Message.success('订单发货成功');
            // 刷新订单列表
            getOrders();
          } else {
            Message.error((res && res.message) || '订单发货失败');
          }
        }).catch(err => {
          console.error('订单发货失败', err);
          Message.error('订单发货失败: ' + (err.message || String(err)));
        });
      }
    });
  } else {
    Message.error('发货组件加载失败');
  }
};

// 取消订单函数
const handleCancelOrder = (orderId) => {
  const cancelReason = '管理员取消'; // 可以改为弹窗输入
  
  orderApi.cancelOrder(orderId, {cancelReason}).then(res => {
    if (res && res.code === 200) {
      Message.success('订单取消成功');
      // 刷新订单列表
      getOrders();
    } else {
      Message.error((res && res.message) || '订单取消失败');
    }
  }).catch(err => {
    console.error('订单取消失败', err);
    Message.error('订单取消失败: ' + (err.message || String(err)));
  });
};

// 确认收货函数
const handleConfirmReceipt = (orderId) => {
  orderApi.confirmReceipt(orderId).then(res => {
    if (res && res.code === 200) {
      Message.success('确认收货成功');
      // 刷新订单列表
      getOrders();
    } else {
      Message.error((res && res.message) || '确认收货失败');
    }
  }).catch(err => {
    console.error('确认收货失败', err);
    Message.error('确认收货失败: ' + (err.message || String(err)));
  });
};

// 用于合并单元格的 Span 方法
const spanMethod = ({ record, column, rowIndex }) => {
  if (record.type === 'header') { // 表头行: 第一列合并所有列
    if (column.slotName === 'productInfo-cell') {
      return { rowspan: 1, colspan: columns.length };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
  if (record.type === 'details') {
    // 详情行: 对特定列进行垂直合并
    const isFirstProduct = record.productIndex === 0;
    const rowspan = isFirstProduct ? record.productCount : 0;
    // 需要垂直合并的列 (订单级别信息)
    const colsToSpan = [
      'payment-method-cell',
      'delivery-method-cell',
      'payment-cell',
      'receiver-cell',
      'status-cell',
      'operations-cell',
    ];

    if (colsToSpan.includes(column.slotName)) {
      return { rowspan: rowspan, colspan: 1 };
    }
  }
  // 其他单元格不合并
  return { rowspan: 1, colspan: 1 };
};

// 表格列配置
const columns = reactive([
  {
    title: '商品信息',
    slotName: 'productInfo-cell',
    width: 380,
  },
  { title: '单价/数量', slotName: 'priceQuantity-cell', width: 120, align: 'center' },
  { title: '收件信息', slotName: 'receiver-cell', width: 150, align: 'center' },
  { title: '实付金额 ', slotName: 'payment-cell', width: 130 },
  { title: '支付方式', slotName: 'payment-method-cell', width: 100, align: 'center' },
  { title: '配送方式', slotName: 'delivery-method-cell', width: 100, align: 'center' },
  { title: '订单状态', slotName: 'status-cell', width: 150 },
  { title: '操作', slotName: 'operations-cell', width: 150, align: 'center', fixed: 'right' },
]);
</script>

<style scoped lang="less">
.order-table-container {
  background-color: #fff;
  border-radius: 4px;
  /* 添加底部内边距，避免分页组件遮挡内容 */
  padding-bottom: 20px;
}

.order-table {
  border: none;
}

/* 表头样式 */
:deep(.arco-table-th) {
  background-color: #f5f7f9 !important;
  color: #4E5969;
  /* 调整表头字体颜色 */
  font-weight: 500;
  font-size: 14px;
  text-align: center;
}

:deep(.arco-table-td),
:deep(.arco-table-th) {
  border: none;
  vertical-align: top;
}

:deep(.arco-table-container) {
  border: none;
}

:deep(.arco-table-thead) {
  display: none;
}

.copy-btn-inline {
  background: transparent;
  border: none;
  color: #999;
  padding: 0;
  height: auto;
  margin-left: -12px;
}

.order-time-inline {
  margin-left: 12px;
  color: #999;
}

.order-header-cell {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0; /* 左右内边距为0 */
  background-color: #f5f7f9;
  font-size: 13px;
  color: #666;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1px solid #f0f0f0;
}

.order-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-left: 0; /* 确保左侧没有内边距 */
}

.product-info-cell-content {
  display: flex;
  align-items: flex-start;
}

.product-image {
  width: 60px;
  height: 60px;
  margin-right: 10px;
  border-radius: 4px;
  object-fit: cover;
  border: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.product-details {
  font-size: 13px;
  flex-grow: 1;
  min-width: 0;
}

.product-name {
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
}

.product-meta {
  color: #999;
  font-size: 12px;
  line-height: 1.6;
}

.product-meta>div {
  margin-bottom: 2px;
}

.price-cell-content {
  text-align: center;
}

.price {
  font-weight: 500;
  color: #333;
  font-size: 13px;
}

.price.highlight {
  color: #FF7D00;
  font-weight: bold;
}

.quantity {
  color: #999;
  margin-top: 3px;
  font-size: 12px;
}

.receiver-info {
  font-size: 13px;
  color: #333;
  text-align: center;
}

.status-cell-content {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.status-tag {
  display: inline-block;
  font-size: 13px;
}

.delivery-time,
.delivery-detail {
  font-size: 12px;
  color: #999;
}

.operations-cell-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.operation-buttons {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

:deep(.arco-btn-text) {
  padding: 0;
  height: auto;
  line-height: 1.5;
  font-size: 13px;
}
</style>
