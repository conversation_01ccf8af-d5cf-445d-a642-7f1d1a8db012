<!--
 - 用户积分明细表格组件
 - 用于展示用户的积分变动记录
 -->
<template>
  <div class="points-table-container">
    <ma-crud :options="pointsOptions" :columns="pointsColumns">
      <!-- 自定义列 - 类型 -->
      <template #type="{ record }">
        <a-tag :color="record.type === '获取积分' ? 'blue' : 'orange'">
          {{ record.type }}
        </a-tag>
      </template>
      
      <!-- 自定义列 - 积分变动值 -->
      <template #pointsChange="{ record }">
        <span :style="{ color: record.type === '获取积分' ? '#165DFF' : '#FF7D00', fontWeight: 'bold' }">
          {{ record.type === '获取积分' ? '+' : '-' }}{{ record.pointsChange }}
        </span>
      </template>
      
      <!-- 自定义列 - 领取状态 -->
      <template #receiveStatus="{ record }">
        <a-tag :color="getReceiveStatusColor(record.receiveStatus)">
          {{ record.receiveStatus }}
        </a-tag>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { reactive } from 'vue';
import { Message } from '@arco-design/web-vue';

// 组件属性定义
const props = defineProps({
  // 用户ID
  userId: {
    type: [String, Number],
    required: true
  }
});

// 日期格式化函数
function formatDate(timestamp) {
  if (!timestamp) return '-';
  
  try {
    // 处理日期字符串
    if (typeof timestamp === 'string' && timestamp.includes('-')) {
      return timestamp;
    }
    
    // 处理BigInt类型
    if (typeof timestamp === 'bigint') {
      timestamp = Number(timestamp);
    }
    
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '-';
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-');
  } catch (e) {
    console.error('日期格式化错误:', e);
    return '-';
  }
}

// 获取领取状态颜色
function getReceiveStatusColor(status) {
  const statusMap = {
    '已领取': 'green',
    '已使用': 'blue',
    '未领取': 'orange',
    '已过期': 'red'
  };
  return statusMap[status] || 'gray';
}

// 模拟积分明细数据
const getMockPointsRecords = () => {
  return [
    {
      type: '获取积分',
      pointsType: '会员注册',
      memberNo: '14959',
      pointsChange: 30,
      currentPoints: 30,
      createTime: '2025-05-07 17:10:25',
      description: '注册赠送积分',
      pointsDate: '2025-05-07',
      interactionMember: 0,
      receiveStatus: '已领取',
      relatedOrderNo: ''
    },
    {
      type: '获取积分',
      pointsType: '购物奖励',
      memberNo: '14959',
      pointsChange: 50,
      currentPoints: 80,
      createTime: '2025-05-08 09:23:15',
      description: '购物奖励积分',
      pointsDate: '2025-05-08',
      interactionMember: 0,
      receiveStatus: '已领取',
      relatedOrderNo: 'ORD20250508001'
    },
    {
      type: '消费积分',
      pointsType: '积分兑换',
      memberNo: '14959',
      pointsChange: 20,
      currentPoints: 60,
      createTime: '2025-05-08 14:35:42',
      description: '积分兑换商品',
      pointsDate: '2025-05-08',
      interactionMember: 0,
      receiveStatus: '已使用',
      relatedOrderNo: 'EXC20250508003'
    },
    {
      type: '获取积分',
      pointsType: '活动奖励',
      memberNo: '14959',
      pointsChange: 100,
      currentPoints: 160,
      createTime: '2025-05-09 10:15:30',
      description: '参与促销活动奖励',
      pointsDate: '2025-05-09',
      interactionMember: 0,
      receiveStatus: '已领取',
      relatedOrderNo: 'ACT20250509002'
    },
    {
      type: '消费积分',
      pointsType: '优惠折扣',
      memberNo: '14959',
      pointsChange: 50,
      currentPoints: 110,
      createTime: '2025-05-09 13:45:20',
      description: '购物积分折扣',
      pointsDate: '2025-05-09',
      interactionMember: 0,
      receiveStatus: '已使用',
      relatedOrderNo: 'ORD20250509005'
    }
  ];
};

// 积分明细配置
const pointsOptions = reactive({
  api: () => {
    // 模拟数据
    return Promise.resolve({
      success: true,
      message: '获取积分明细成功',
      code: 200,
      data: {
        items: getMockPointsRecords(),
        pageInfo: {
          total: 5,
          currentPage: 1,
          totalPage: 1
        }
      }
    });
  },
  showIndex: true,
  operationColumn: false,
  pageLayout: "fixed"
});

// 积分明细列配置
const pointsColumns = reactive([
  { title: '类型', dataIndex: 'type', slotName: 'type' },
  { title: '积分类型', dataIndex: 'pointsType' },
  { title: '会员编号', dataIndex: 'memberNo' },
  { title: '积分变动值', dataIndex: 'pointsChange', slotName: 'pointsChange' },
  { title: '当前积分', dataIndex: 'currentPoints' },
  { title: '创建时间', dataIndex: 'createTime' },
  { title: '描述', dataIndex: 'description' },
  { title: '积分日期', dataIndex: 'pointsDate' },
  { title: '交互会员', dataIndex: 'interactionMember' },
  { title: '领取状态', dataIndex: 'receiveStatus', slotName: 'receiveStatus' },
  { title: '关联单号', dataIndex: 'relatedOrderNo' }
]);
</script>

<style scoped>
.points-table-container {
  margin-bottom: 20px;
}
</style>
