<!--
 - 用户绑定表格组件
 - 用于展示用户绑定的第三方账号
 -->
<template>
  <div class="bind-table-container">
    <ma-crud :options="bindOptions" :columns="bindColumns">
      <!-- 自定义列 - 绑定类型 -->
      <template #type="{ record }">
        <a-tag :color="getBindTypeColor(record.type)">
          {{ getBindTypeText(record.type) }}
        </a-tag>
      </template>
      <!-- 自定义列 - 绑定状态 -->
      <template #status="{ record }">
        <a-tag :color="record.status === 1 ? 'green' : 'red'">
          {{ record.status === 1 ? '已绑定' : '已解绑' }}
        </a-tag>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { reactive } from 'vue';

// 组件属性定义
const props = defineProps({
  // 用户ID
  userId: {
    type: [String, Number],
    default: ''
  }
});

// 日期格式化函数
function formatDate(timestamp) {
  if (!timestamp) return '-';
  
  try {
    // 处理BigInt类型
    if (typeof timestamp === 'bigint') {
      timestamp = Number(timestamp);
    }
    
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '-';
    
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  } catch (e) {
    console.error('日期格式化错误:', e);
    return '-';
  }
}

// 获取绑定类型颜色
function getBindTypeColor(type) {
  const typeMap = {
    1: 'blue',    // 微信
    2: 'green',   // QQ
    3: 'orange',  // 微博
    4: 'purple'   // 支付宝
  };
  return typeMap[type] || 'gray';
}

// 获取绑定类型文本
function getBindTypeText(type) {
  const typeMap = {
    1: '微信',
    2: 'QQ',
    3: '微博',
    4: '支付宝'
  };
  return typeMap[type] || '其他';
}

// 用户绑定配置
const bindOptions = reactive({
  api: () => {
    // 模拟数据
    return Promise.resolve({
      success: true,
      message: '获取绑定信息成功',
      code: 200,
      data: {
        items: Array(4).fill(null).map((_, index) => ({
          id: index + 1,
          type: index + 1,
          account: ['wxid_abc123', 'qq123456789', 'weibo_user123', 'alipay_user'][index],
          status: Math.random() > 0.3 ? 1 : 0,
          bind_time: Date.now() - Math.floor(Math.random() * 365) * 24 * 60 * 60 * 1000
        })),
        pageInfo: {
          total: 4,
          currentPage: 1,
          totalPage: 1
        }
      }
    });
  },
  showIndex: true,
  operationColumn: false,
  pageLayout: "fixed"
});

// 用户绑定列配置
const bindColumns = reactive([
  { title: '绑定类型', dataIndex: 'type' },
  { title: '绑定账号', dataIndex: 'account' },
  { title: '绑定状态', dataIndex: 'status' },
  { title: '绑定时间', dataIndex: 'bind_time', render: (_, record) => formatDate(record.bind_time) }
]);
</script>

<style scoped>
.bind-table-container {
  margin-bottom: 20px;
}
</style>
