<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">{{ record.status == 1 ? '正常' : '停用' }}</template>
      <!-- 实名认证状态列 -->
      <template #realname_auth_status="{ record }">
        <a-tag :color="getRealnameAuthStatusColor(record.realname_auth_status)">
          {{ getRealnameAuthStatusText(record.realname_auth_status) }}
        </a-tag>
      </template>
      <!-- 自定义列 - 用户头像 -->
      <template #avatar="{ record }">
        <a-image
          v-if="record.avatar"
          :src="record.avatar"
          width="60"
          height="60"
          :preview-visible="false"
          fit="contain"
        />
        <span v-else>暂无图片</span>
      </template>
      <!-- //操作 -->
      <template #operationBeforeExtend="{ record }">
        <a-button type="text" size="small" @click="openDetails(record)">
          <template #icon>
            <icon-list />
          </template>
          详情
        </a-button>
      </template>
      <!-- 自定义列 - 创建时间 -->

      <!-- <template #operationBeforeExtend="{ record }">
          <a-button type="text" size="small" @click="changePassword(record.id)">
              <template #icon><icon-refresh /></template>
              重置密码
            </a-button>
      </template>-->
    </ma-crud>
    <!-- 用户详情抽屉 -->
    <a-drawer 
      :width="'65%'" 
      :visible="detailVisible" 
      @ok="handleOk" 
      @cancel="closeDetail" 
      unmountOnClose
    >
      <template #title>
        用户详情
      </template>
      
      <div class="user-detail-container">
        <!-- 用户头像和基本信息 -->
        <div class="user-header mb-4 pb-4 border-b border-gray-200">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <a-avatar 
                :size="64" 
                :image-url="detailRecord.avatar || '/assets/images/default-avatar.png'" 
                class="mr-4"
              >
                <template #icon>
                  <icon-user />
                </template>
              </a-avatar>
              <div>
                <h3 class="text-lg font-medium">{{ detailRecord.nickname || detailRecord.username || '网宝' }}</h3>
                <p class="text-gray-500">余额：{{ detailRecord.balance || '0.00' }}</p>
                <p class="text-gray-500">积分：{{ detailRecord.points || '30.00' }}</p>
              </div>
            </div>
            <div>
              <a-button type="primary" @click="handleDetailEdit">编辑</a-button>
            </div>
          </div>
          
          <div class="user-stats grid grid-cols-2 gap-4">
            <div class="stat-item">
              <div class="text-gray-500">总订单数：</div>
              <div>{{ detailRecord.order_count || '0' }}</div>
            </div>
            <div class="stat-item">
              <div class="text-gray-500">总消费金额：</div>
              <div>{{ detailRecord.total_amount || '0.00' }}</div>
            </div>
            <div class="stat-item">
              <div class="text-gray-500">本月订单：</div>
              <div>{{ detailRecord.month_order_count || '0' }}</div>
            </div>
            <div class="stat-item">
              <div class="text-gray-500">本月消费金额：</div>
              <div>{{ detailRecord.month_amount || '0.00' }}</div>
            </div>
            <div class="stat-item">
              <div class="text-gray-500">累计优惠：</div>
              <div>{{ detailRecord.total_discount || '0' }}</div>
            </div>
            <div class="stat-item">
              <div class="text-gray-500">本月优惠：</div>
              <div>{{ detailRecord.month_discount || '0' }}</div>
            </div>
          </div>
        </div>
        
        <!-- 选项卡导航 -->
        <a-tabs default-active-key="1">
          <a-tab-pane key="1" title="用户信息">
            <div class="user-info-section">
              <h4 class="text-base font-medium mb-2 text-blue-500">基本信息</h4>
              <div class="grid grid-cols-2 gap-4">
                <div class="info-item">
                  <div class="text-gray-500">用户账号：</div>
                  <div>{{ detailRecord.username || '-' }}</div>
                </div>
                <div class="info-item">
                  <div class="text-gray-500">手机号码：</div>
                  <div>{{ detailRecord.phone || '-' }}</div>
                </div>
                <div class="info-item">
                  <div class="text-gray-500">生日：</div>
                  <div>{{ detailRecord.birthday || '-' }}</div>
                </div>
                <div class="info-item">
                  <div class="text-gray-500">性别：</div>
                  <div>{{ detailRecord.gender === 1 ? '男' : detailRecord.gender === 2 ? '女' : '保密' }}</div>
                </div>
                <div class="info-item">
                  <div class="text-gray-500">用户邮箱：</div>
                  <div>{{ detailRecord.email || '-' }}</div>
                </div>
                <div class="info-item">
                  <div class="text-gray-500">用户地址：</div>
                  <div>{{ detailRecord.address || '-' }}</div>
                </div>
              </div>
            </div>
            
            <div class="user-info-section mt-6">
              <h4 class="text-base font-medium mb-2 text-blue-500">会员信息</h4>
              <div class="grid grid-cols-2 gap-4">
                <div class="info-item">
                  <div class="text-gray-500">会员等级：</div>
                  <div>{{ detailRecord.level_name || '普通会员' }}</div>
                </div>
                <div class="info-item">
                  <div class="text-gray-500">会员编号：</div>
                  <div>{{ detailRecord.member_no || '-' }}</div>
                </div>
                <div class="info-item">
                  <div class="text-gray-500">推广人编号：</div>
                  <div>{{ detailRecord.referrer || '-' }}</div>
                </div>
                <div class="info-item">
                  <div class="text-gray-500">注册时间：</div>
                  <div>{{ formatDate(detailRecord.created_at) || '-' }}</div>
                </div>
                <div class="info-item">
                  <div class="text-gray-500">更新时间：</div>
                  <div>{{ formatDate(detailRecord.updated_at) || '-' }}</div>
                </div>
              </div>
            </div>
          </a-tab-pane>
          
          <a-tab-pane key="2" title="交易订单">
            <OrderTable :userId="detailRecord.id" />
          </a-tab-pane>
          
          <a-tab-pane key="3" title="积分明细">
            <PointsTable :userId="detailRecord.id" />
          </a-tab-pane>
          
          <a-tab-pane key="4" title="签到记录">
            <SignTable :userId="detailRecord.id" />
          </a-tab-pane>
          
          <a-tab-pane key="5" title="持有优惠券">
            <CouponTable :userId="detailRecord.id" />
          </a-tab-pane>
          
          <a-tab-pane key="6" title="余额变动">
            <BalanceTable :userId="detailRecord.id" />
          </a-tab-pane>
          
          <a-tab-pane key="7" title="用户绑定">
            <BindTable :userId="detailRecord.id" />
          </a-tab-pane>
          
          <a-tab-pane key="8" title="实名认证审核">
            <RealnameAuthVerify :userId="detailRecord.id" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </a-drawer>
  </div>
</template>
  
  <script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message, Modal } from "@arco-design/web-vue";
import { IconUser } from '@arco-design/web-vue/es/icon';
import mallmanagementApi from "@/api/master/mallmanagement";
import OrderTable from "./components/OrderTable.vue";
import PointsTable from "./components/PointsTable.vue";
import SignTable from "./components/SignTable.vue";
import CouponTable from "./components/CouponTable.vue";
import BalanceTable from "./components/BalanceTable.vue";
import BindTable from "./components/BindTable.vue";
import RealnameAuthVerify from "./components/RealnameAuthVerify.vue";

// 详情弹窗相关状态
const detailVisible = ref(false);
const detailRecord = ref({});
// 过滤掉 hide 字段的列用于详情展示
// 过滤掉 hide、序号和操作相关字段的列用于详情展示
// 详情弹窗专用字段过滤，不显示序号和操作扩展列
const detailColumns = computed(() =>
  columns.filter(
    col =>
      !col.hide &&
      col.dataIndex !== "__index" &&
      col.dataIndex !== "__operation"
  )
);
console.log(detailColumns, "detailColumns");

// 打开详情弹窗
function openDetails(record) {
  detailRecord.value = { ...record };
  detailVisible.value = true;
}
// 关闭详情抽屉
function closeDetail() {
  detailVisible.value = false;
}

// 确认按钮处理函数
function handleOk() {
  detailVisible.value = false;
}

/**
 * 格式化时间戳为可读的日期字符串
 * @param {number|string|bigint} timestamp - 时间戳（毫秒）或日期字符串
 * @param {Object} options - 格式化选项
 * @param {string} [options.format='datetime'] - 预设格式：'date'|'datetime'|'time'
 * @param {string} [options.locale='zh-CN'] - 区域设置
 * @param {Object} [options.custom] - 自定义Intl选项
 * @returns {string} 格式化后的日期字符串，失败时返回'-'
 */
 function formatDate(timestamp, options = {}) {
  if (timestamp === undefined || timestamp === null) return '-';
  
  try {
    // 转换不同类型的时间戳为Date对象
    let date;
    if (typeof timestamp === 'bigint') {
      date = new Date(Number(timestamp));
    } else if (typeof timestamp === 'string') {
      // 处理ISO字符串或数字字符串
      const num = Number(timestamp);
      date = isNaN(num) ? new Date(timestamp) : new Date(num);
    } else {
      date = new Date(timestamp);
    }
    
    // 验证日期有效性
    if (isNaN(date.getTime())) {
      console.warn('无效的时间戳:', timestamp);
      return '-';
    }
    
    // 预设格式配置
    const PRESET_FORMATS = {
      date: { year: 'numeric', month: '2-digit', day: '2-digit' },
      datetime: { 
        year: 'numeric', month: '2-digit', day: '2-digit',
        hour: '2-digit', minute: '2-digit', second: '2-digit',
        hour12: false 
      },
      time: { hour: '2-digit', minute: '2-digit', second: '2-digit', hour12: false }
    };
    
    // 合并选项
    const { 
      format = 'datetime', 
      locale = 'zh-CN', 
      custom = {} 
    } = options;
    
    // 构建最终选项
    const formatOptions = custom || PRESET_FORMATS[format] || PRESET_FORMATS.datetime;
    
    return date.toLocaleString(locale, formatOptions);
  } catch (error) {
    console.error('日期格式化失败:', error, '输入值:', timestamp);
    return '-';
  }
}

// 详情页编辑按钮处理函数
function handleDetailEdit() {
  // 直接调用CRUD组件的编辑功能
  if (crudRef.value) {
    crudRef.value.editAction(detailRecord.value);
  }
}

import MaButtonMenu from "~/components/master/layout/ma-buttonMenu.vue";
const userApi = mallmanagementApi.user;
definePageMeta({
  name: "master-mallmanagement-user",
  path: "/master/mallmanagement/user"
});

const roleData = ref([]);
const deptData = ref([]);
const crudRef = ref();

// 获取角色列表
const fetchRoleList = async () => {
  try {
    const res = await mallmanagementApi.role.getList();

    if (res.code == "200") {
      roleData.value = res.data.items || [];
    }
  } catch (error) {
    console.error("获取角色列表失败:", error);
  }
};

// 获取部门列表
const fetchDeptList = async () => {
  try {
    const res = await mallmanagementApi.dept.getDeptList();
    if (res.code == "200") {
      deptData.value = res.data.items || [];
    }
  } catch (error) {
    console.error("获取部门列表失败:", error);
  }
};

// 页面初始化
onMounted(() => {
  // 获取角色和部门数据
  fetchRoleList();
  fetchDeptList();
});

// CRUD配置
const crud = reactive({
  // API配置
  api: userApi.getList,
  showIndex: true,
  pageLayout: "fixed",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 300,
  add: { show: true, api: userApi.create },
  edit: { show: true, api: userApi.update },
  delete: {
    show: true,
    api: userApi.delete
  },
  // 添加前处理参数
  beforeAdd: params => {
    console.log("添加前原始参数:", params);
    params.deptId = params.dept_id;
    params.roleId = params.role_id;
    delete params.role_id;
    delete params.dept_id;
    delete params.created_at;
    return params;
  },

  // 编辑前处理参数
  beforeEdit: params => {
    console.log("编辑前原始参数:", params);
    params.deptId = params.dept_id;
    params.roleId = params.role_id;
    delete params.role_id;
    delete params.dept_id;
    delete params.created_at;
    return params;
  },
  
  // 编辑后处理响应
  afterEdit: (response, formData) => {
    // 编辑成功后关闭抽屉
    if (response.code === 200 && detailVisible.value && detailRecord.value.id === formData.id) {
      // 关闭详情抽屉
      detailVisible.value = false;
    }
  }
});

// 表格列配置
// 只包含业务字段，不包含序号和操作列
const columns = reactive([
  {
    title: "用户名",
    dataIndex: "username",
    search: true,
    // addDisplay: false,
    // editDisplay: false,
    commonRules: [{ required: true, message: "用户名必填" }]
  },
  {
    title: "用户昵称",
    dataIndex: "nickname",
    search: true,
    commonRules: [{ required: true, message: "用户昵称必填" }]
  },

  {
    title: "邮箱",
    dataIndex: "email",
    search: true,
    commonRules: [
      { required: true, type: "email", message: "请输入正确的邮箱" }
    ]
  },
  {
    title: "密码",
    dataIndex: "password",
    formType: "input-password",
    search: false,
    hide: true,
    commonRules: [{ required: true, message: "密码必填" }]
  },
  {
    title: "手机号",
    dataIndex: "phone",
    search: true,
    commonRules: [
      {
        required: true,
        match: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: "请输入正确的手机号码"
      }
    ]
  },
  {
    title: "用户头像",
    dataIndex: "avatar",
    formType: "upload",
    // 限制文件类型
    accept: ".jpg,.jpeg,.png,.gif",
    // 限制文件大小为2MB
    size: 2 * 1024 * 1024,
    // 限制上传数量为1张
    limit: 1,
    // 返回类型为URL
    returnType: "url",
    // 显示已上传图片列表
    showList: true,
    // 不允许多选
    multiple: false,
    // 提示信息
    tip: "建议上传正方形图片，大小不超过2MB",
    // 指定为图片上传
    type: "image",
    // 是否显示为圆形
    rounded: true
  },
  {
    title: "状态",
    dataIndex: "status",
    search: true,
    formType: "radio",
    addDefaultValue: 1,
    dict: {
      data: [
        { label: "正常", value: 1 },
        { label: "停用", value: 0 }
      ]
    }
  },
  {
    title: "实名认证状态",
    dataIndex: "realname_auth_status",
    search: true,
    addDisplay: false,
    editDisplay: false,
    formType: "select",
    dict: {
      data: [
        { label: "未认证", value: 0 },
        { label: "等待认证", value: 1 },
        { label: "认证通过", value: 2 },
        { label: "认证失败", value: 3 }
      ]
    }
  }
  // avatar
]);

// 获取实名认证状态文本
const getRealnameAuthStatusText = (status) => {
  switch (status) {
    case 0: return "未认证";
    case 1: return "等待认证";
    case 2: return "认证通过";
    case 3: return "认证失败";
    default: return "未知状态";
  }
};

// 获取实名认证状态标签颜色
const getRealnameAuthStatusColor = (status) => {
  switch (status) {
    case 0: return "gray"; // 未认证
    case 1: return "blue"; // 等待认证
    case 2: return "green"; // 认证通过
    case 3: return "red"; // 认证失败
    default: return "default";
  }
};

// 修改密码
const changePassword = async () => {
  Modal.info({
    title: "提示",
    content: "确定将该用户密码重置为 123456 吗？",
    simple: false,
    onBeforeOk: done => {
      resetPassword(id);
      done(true);
    }
  });
  return;
};
const resetPassword = async id => {
  const response = await userApi.updatePassword({
    id: id,
    new_password: "123456"
  });
  if (response.code === 200) {
    Message.success("密码重置成功");
  } else {
    Message.error("密码重置失败");
  }
};
</script>
  
  <script>
export default { name: "master-system-permission-user" };
</script>
  
  <style scoped></style>
  