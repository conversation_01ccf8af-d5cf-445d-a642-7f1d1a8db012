<template>
  <div class="message-management-container">
    <!-- 统计卡片 -->
    <div class="stats-cards mb-4">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="总消息数"
              :value="statistics.totalMessages"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <i class="i-carbon-email text-blue-500"></i>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="未读消息"
              :value="statistics.unreadMessages"
              :value-style="{ color: '#f5222d' }"
            >
              <template #prefix>
                <i class="i-carbon-notification text-red-500"></i>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="紧急消息"
              :value="statistics.urgentMessages"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <i class="i-carbon-warning text-orange-500"></i>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日消息"
              :value="statistics.todayMessages"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <i class="i-carbon-time text-green-500"></i>
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 消息列表 -->
    <ma-crud :options="crudOptions" :columns="columns" ref="crudRef">
      <!-- 表格前按钮 -->
      <template #tableBeforeButtons>
        <a-button type="primary" @click="showSendMessageDrawer = true">
          <template #icon>
            <i class="i-carbon-send-alt"></i>
          </template>
          发送消息
        </a-button>
        <a-button @click="showBulkSendDrawer = true">
          <template #icon>
            <i class="i-carbon-broadcast"></i>
          </template>
          批量发送
        </a-button>
      </template>

      <!-- 用户信息 -->
      <template #user="{ record }">
        <div class="user-info">
          <div class="user-name font-medium">{{ record.user?.nickname || record.user?.username || '未知用户' }}</div>
          <div class="user-id text-gray-500 text-xs">ID: {{ record.userId }}</div>
        </div>
      </template>

      <!-- 消息类型 -->
      <template #messageType="{ record }">
        <a-tag
          :color="{
            'system': 'blue',
            'order': 'green',
            'promotion': 'red',
            'service': 'purple'
          }[record.messageType]"
        >
          {{ {
            'system': '系统消息',
            'order': '订单消息',
            'promotion': '活动消息',
            'service': '客服消息'
          }[record.messageType] }}
        </a-tag>
      </template>

      <!-- 优先级 -->
      <template #priority="{ record }">
        <a-tag
          :color="{
            1: 'default',
            2: 'orange',
            3: 'red'
          }[record.priority]"
        >
          {{ {
            1: '普通',
            2: '重要',
            3: '紧急'
          }[record.priority] }}
        </a-tag>
      </template>

      <!-- 已读状态 -->
      <template #isRead="{ record }">
        <a-tag :color="record.isRead ? 'green' : 'red'">
          {{ record.isRead ? '已读' : '未读' }}
        </a-tag>
      </template>

      <!-- 创建时间 -->
      <template #createdAt="{ record }">
        {{ formatTime(record.createdAt) }}
      </template>

    </ma-crud>

    <!-- 发送消息组件 -->
    <SendMessageDrawer
      v-model:visible="showSendMessageDrawer"
      @success="handleSendSuccess"
    />

    <!-- 批量发送消息组件 -->
    <BulkSendDrawer
      v-model:visible="showBulkSendDrawer"
      @success="handleBulkSendSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import messageManagementApi from '~/api/master/messageManagement'
import SendMessageDrawer from '~/components/master/message/SendMessageDrawer.vue'
import BulkSendDrawer from '~/components/master/message/BulkSendDrawer.vue'

definePageMeta({
  name: 'mesManagement',
  path: '/master/mallmanagement/mesManagement'
})

// 组件引用
const crudRef = ref()

// 统计数据
const statistics = ref({
  totalMessages: 0,
  unreadMessages: 0,
  systemMessages: 0,
  orderMessages: 0,
  promotionMessages: 0,
  serviceMessages: 0,
  urgentMessages: 0,
  todayMessages: 0
})

// 抽屉状态
const showSendMessageDrawer = ref(false)
const showBulkSendDrawer = ref(false)

// ma-crud 配置
const crudOptions = reactive({
  api: async (params) => {
    try {
      const response = await messageManagementApi.getAllUserMessages(params)
      if (response.code === 200) {
        // 适配ma-crud的数据格式 - 根据config/crud.js的parseResponseData函数
        return {
          code: 200,
          message: response.message,
          data: {
            items: response.data.messages,  // ma-crud期望的是items字段
            pageInfo: {                     // ma-crud期望的是pageInfo字段
              total: response.data.pagination.total,
              currentPage: response.data.pagination.page,
              totalPage: response.data.pagination.totalPages,
              pageSize: response.data.pagination.pageSize
            }
          }
        }
      }
      return response
    } catch (error) {
      console.error('获取消息列表失败:', error)
      return {
        code: 500,
        message: '获取消息列表失败'
      }
    }
  },
  searchLabelWidth: '80px',
  tablePagination: true, // 启用分页
  pageSize: 20,
  searchColNumber: 3,
  operationColumn: true,
  operationColumnWidth: 120,
  delete: {
    show: true,
    api: async (id) => {
      try {
        const response = await messageManagementApi.deleteMessage(id)
        if (response.code === 200) {
          fetchStatistics() // 删除成功后刷新统计
        }
        return response
      } catch (error) {
        console.error('删除消息失败:', error)
        return {
          code: 500,
          message: '删除消息失败'
        }
      }
    },
    text: '删除'
  },
  beforeRequest: (params) => {
    // 处理搜索参数
    return params
  },
  requestSuccess: (res) => {
    // 请求成功后刷新统计数据
    fetchStatistics()
    return res
  }
})

// 表格列配置
const columns = [
  {
    title: '用户信息',
    dataIndex: 'user',
    width: 180,
    search: false
  },
  {
    title: '消息标题',
    dataIndex: 'title',
    width: 200,
    search: true,
    ellipsis: true
  },
  {
    title: '消息内容',
    dataIndex: 'content',
    width: 300,
    search: true,
    ellipsis: true
  },
  {
    title: '消息类型',
    dataIndex: 'messageType',
    width: 120,
    search: true,
    formType: 'select',
    dict: {
      data: [
        { label: '系统消息', value: 'system' },
        { label: '订单消息', value: 'order' },
      ]
    }
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    width: 100,
    search: true,
    formType: 'select',
    dict: {
      data: [
        { label: '普通', value: 1 },
        { label: '重要', value: 2 },
        { label: '紧急', value: 3 }
      ]
    }
  },
  {
    title: '已读状态',
    dataIndex: 'isRead',
    width: 100,
    search: true,
    formType: 'select',
    dict: {
      data: [
        { label: '未读', value: false },
        { label: '已读', value: true }
      ]
    }
  },
  {
    title: '用户ID',
    dataIndex: 'userId',
    width: 150,
    search: true,
    hide: true
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    width: 160,
    search: false
  }
]

// 获取统计信息
const fetchStatistics = async () => {
  try {
    const response = await messageManagementApi.getMessageStatistics()

    if (response.code === 200) {
      statistics.value = response.data
    } else {
      console.error('获取统计信息失败:', response.message)
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
  }
}



// 查看消息详情
const viewMessage = (message) => {
  // TODO: 实现消息详情查看
  console.log('查看消息:', message)
  Message.info('查看消息详情功能待实现')
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 发送消息成功处理
const handleSendSuccess = () => {
  // 刷新表格和统计数据
  crudRef.value?.refresh()
  fetchStatistics()
}

// 批量发送成功处理
const handleBulkSendSuccess = () => {
  // 刷新表格和统计数据
  crudRef.value?.refresh()
  fetchStatistics()
}

// 页面加载时获取统计数据
onMounted(() => {
  fetchStatistics()
})
</script>

<style scoped>
.message-management-container {
  background-color: white;
  padding: 24px;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.user-info {
  line-height: 1.4;
}

.user-name {
  font-weight: 500;
  color: #262626;
}



:deep(.ma-content-block) {
  background: transparent;
  padding: 0;
}

:deep(.ant-statistic-content) {
  font-size: 24px;
  font-weight: 600;
}

:deep(.ant-statistic-title) {
  margin-bottom: 8px;
  color: #8c8c8c;
}

</style>