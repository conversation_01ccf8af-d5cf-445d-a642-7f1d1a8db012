<!--
 - <PERSON><PERSON>d<PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">{{ record.status == 1 ? '正常' : '停用' }}</template>
       <!-- 是否系统内置
 -->
       <template #is_system="{ record }">{{ record.is_system == 1 ? '是' : '否' }}</template>
      <!-- 自定义列 - 用户头像 -->
      <template #avatar="{ record }">
        <a-image
          v-if="record.avatar"
          :src="record.avatar"
          width="60"
          height="60"
          :preview-visible="false"
          fit="contain"
        />
        <span v-else>暂无图片</span>
      </template>
      <!-- //操作 -->
      <template #operationBeforeExtend="{ record }">
        <a-button type="text" size="small" @click="openDetails(record)">
          <template #icon>
            <icon-list />
          </template>
          详情
        </a-button>
      </template>
      <!-- 自定义列 - 创建时间 -->

      <!-- <template #operationBeforeExtend="{ record }">
          <a-button type="text" size="small" @click="changePassword(record.id)">
              <template #icon><icon-refresh /></template>
              重置密码
            </a-button>
      </template>-->
    </ma-crud>
    <!-- 详情弹窗 -->
    <a-modal v-model:visible="detailVisible" title="用户等级详情" @cancel="closeDetail" :footer="false">
      <div class="divide-y divide-gray-100">
        <div
          v-for="col in detailColumns"
          :key="col.dataIndex"
          class="flex items-center py-3 px-2 hover:bg-gray-50 transition-all"
        >
          <div class="w-32 text-right pr-4 font-medium text-gray-500 bg-gray-50 rounded-l select-none">
            {{ col.title }}：
          </div>
          <div class="flex-1 pl-4 text-gray-900 border-l border-gray-200 min-h-[32px]">
  <template v-if="col.dataIndex === 'status'">
    {{ detailRecord.status === 1 ? '正常' : detailRecord.status === 0 ? '停用' : (detailRecord.status !== undefined ? detailRecord.status : '-') }}
  </template>
  <template v-else-if="col.dataIndex === 'is_system'">
    {{ detailRecord.is_system === 1 ? '是' : detailRecord.is_system === 0 ? '否' : (detailRecord.is_system !== undefined ? detailRecord.is_system : '-') }}
  </template>
  <template v-else-if="col.dataIndex === 'avatar'">
    <img
      v-if="detailRecord.avatar"
      :src="detailRecord.avatar"
      alt="用户头像"
      class="w-20 h-20 object-cover rounded-full border border-gray-200 bg-white"
    />
    <span v-else class="text-gray-400">暂无头像</span>
  </template>
  <template v-else>
    {{ detailRecord[col.dataIndex] !== undefined ? detailRecord[col.dataIndex] : '-' }}
  </template>
</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
  
  <script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message, Modal } from "@arco-design/web-vue";
import mallmanagementApi from "@/api/master/mallmanagement";

// 详情弹窗相关状态
const detailVisible = ref(false);
const detailRecord = ref({});
// 过滤掉 hide 字段的列用于详情展示
// 过滤掉 hide、序号和操作相关字段的列用于详情展示
// 详情弹窗专用字段过滤，不显示序号和操作扩展列
const detailColumns = computed(() =>
  columns.filter(
    col =>
      !col.hide &&
      col.dataIndex !== "__index" &&
      col.dataIndex !== "__operation"
  )
);
console.log(detailColumns, "detailColumns");

// 打开详情弹窗
function openDetails(record) {
  detailRecord.value = { ...record };
  detailVisible.value = true;
}
// 关闭详情弹窗
function closeDetail() {
  detailVisible.value = false;
}

import MaButtonMenu from "~/components/master/layout/ma-buttonMenu.vue";
const levelApi = mallmanagementApi.level;
definePageMeta({
  name: "master-mallmanagement-grade",
  path: "/master/mallmanagement/grade"
});

const roleData = ref([]);
const deptData = ref([]);
const crudRef = ref();

// 获取角色列表
const fetchRoleList = async () => {
  try {
    const res = await mallmanagementApi.role.getList();

    if (res.code == "200") {
      roleData.value = res.data.items || [];
    }
  } catch (error) {
    console.error("获取角色列表失败:", error);
  }
};

// 获取部门列表
const fetchDeptList = async () => {
  try {
    const res = await mallmanagementApi.dept.getDeptList();
    if (res.code == "200") {
      deptData.value = res.data.items || [];
    }
  } catch (error) {
    console.error("获取部门列表失败:", error);
  }
};

// 页面初始化
onMounted(() => {
  // 获取角色和部门数据
  fetchRoleList();
  fetchDeptList();
});

// CRUD配置
const crud = reactive({
  // API配置
  api: levelApi.getList,
  showIndex: true,
  pageLayout: "fixed",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 300,
  add: { show: true, api: levelApi.create },
  edit: { show: true, api: levelApi.update },
  delete: {
    show: true,
    api: levelApi.delete
  },
  // 添加前处理参数
  beforeAdd: params => {
    console.log("添加前原始参数:", params);
    params.deptId = params.dept_id;
    params.roleId = params.role_id;
    delete params.role_id;
    delete params.dept_id;
    delete params.created_at;
    return params;
  },

  // 编辑前处理参数
  beforeEdit: params => {
    console.log("编辑前原始参数:", params);
    params.deptId = params.dept_id;
    params.roleId = params.role_id;
    delete params.role_id;
    delete params.dept_id;
    delete params.created_at;
    return params;
  }
});

// 表格列配置
// 只包含业务字段，不包含序号和操作列
const columns = reactive([
  {
    title: "等级名称",
    dataIndex: "level_name",
    search: true,
    // addDisplay: false,
    // editDisplay: false,
    commonRules: [{ required: true, message: "等级名称必填" }]
  },
  {
    title: "是否系统内置",
    dataIndex: "is_system",
    search: true,
    formType: "radio",
    addDefaultValue: 1,
    dict: {
      data: [
        { label: "是", value: 1 },
        { label: "否", value: 0 }
      ]
    }
  },
  {
    title: "状态",
    dataIndex: "status",
    search: true,
    formType: "radio",
    addDefaultValue: 1,
    dict: {
      data: [
        { label: "正常", value: 1 },
        { label: "停用", value: 0 }
      ]
    }
  }
  // avatar
]);

// 修改密码
const changePassword = async () => {
  Modal.info({
    title: "提示",
    content: "确定将该用户密码重置为 123456 吗？",
    simple: false,
    onBeforeOk: done => {
      resetPassword(id);
      done(true);
    }
  });
  return;
};
const resetPassword = async id => {
  const response = await levelApi.updatePassword({
    id: id,
    new_password: "123456"
  });
  if (response.code === 200) {
    Message.success("密码重置成功");
  } else {
    Message.error("密码重置失败");
  }
};
</script>
  
  <script>
export default { name: "master-system-permission-user" };
</script>
  
  <style scoped></style>
  