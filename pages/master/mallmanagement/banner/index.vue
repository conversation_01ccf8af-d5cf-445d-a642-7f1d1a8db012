<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">{{ record.status == 1 ? '启用' : '禁用' }}</template>
      
      <!-- 网址类型列 -->
      <template #linkType="{ record }">
        <span v-if="record.linkType == 1" class="text-blue-600">内部链接</span>
        <span v-else-if="record.linkType == 2" class="text-green-600">外部链接</span>
        <span v-else class="text-gray-500">无链接</span>
      </template>
      
      <!-- 图片预览列 -->
      <template #image_url="{ record }">
        <a-image
          v-if="record.image_url"
          :src="record.image_url"
          width="80"
          height="50"
          :preview-visible="false"
          fit="cover"
          class="rounded"
        />
        <span v-else class="text-gray-400">暂无图片</span>
      </template>
      
      <!-- 跳转链接列 -->
      <template #link_url="{ record }">
        <span v-if="record.link_url" class="text-blue-600 truncate max-w-xs" :title="record.link_url">
          {{ record.link_url }}
        </span>
        <span v-else class="text-gray-400">无链接</span>
      </template>
      
      <!-- 轮播图片列 -->
      <template #imageUrl="{ record }">
        <a-image
          v-if="record.imageUrl"
          :src="record.imageUrl"
          width="80"
          height="50"
          :preview-visible="false"
          fit="cover"
          class="rounded"
        />
        <span v-else class="text-gray-400">暂无图片</span>
      </template>
    </ma-crud>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { Message, Modal } from "@arco-design/web-vue";
import mallmanagementApi from "@/api/master/mallmanagement";

const crudRef = ref();

// 页面初始化
onMounted(() => {
  // 可以在这里添加初始化逻辑
});

const bannerApi = mallmanagementApi.banner;
definePageMeta({
  name: "master-mallmanagement-banner",
  path: "/master/mallmanagement/banner"
});

// CRUD配置
const crud = reactive({
  // API配置
  api: bannerApi.getList,
  showIndex: true,
  pageLayout: "fixed",
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 300,
  add: { show: true, api: bannerApi.create },
  edit: { show: true, api: bannerApi.update },
  delete: {
    show: true,
    api: bannerApi.delete
  },
  // 添加前处理参数
  beforeAdd: params => {
    console.log("添加前原始参数:", params);
    // 处理字段映射
    params.image_url = params.imageUrl;
    params.sort_order = params.sortOrder;
    params.link_url = params.linkUrl;
    params.link_type = params.linkType;
    delete params.imageUrl;
    delete params.sortOrder;
    delete params.linkUrl;
    delete params.linkType;
    delete params.created_at;
    return params;
  },

  // 编辑前处理参数
  beforeEdit: params => {
    console.log("编辑前原始参数:", params);
    // 处理字段映射
    params.image_url = params.imageUrl;
    params.sort_order = params.sortOrder;
    params.link_url = params.linkUrl;
    params.link_type = params.linkType;
    delete params.imageUrl;
    delete params.sortOrder;
    delete params.linkUrl;
    delete params.linkType;
    delete params.created_at;
    return params;
  }
});

// 表格列配置
const columns = reactive([
  {
    title: "轮播图标题",
    dataIndex: "title",
    search: true,
    commonRules: [{ required: true, message: "轮播图标题必填" }]
  },
  {
    title: "轮播图片",
    dataIndex: "imageUrl",
    formType: "upload",
    addDisplay: true,
    editDisplay: true,
    hide: false,
    slot: true, 
    commonRules: [{ required: true, message: "轮播图片必填" }],
    type: "image",
    multiple: false,
    returnType: "url",
    accept: ".jpg,.jpeg,.png,.gif,.webp,.svg",
    size: 2 * 1024 * 1024,
    limit: 1,
    showList: true,
    tip: "建议尺寸800x800px，大小不超过2MB",
    rounded: false
  },
  {
    title: "排序",
    dataIndex: "sortOrder",
    formType: "input-number",
    addDefaultValue: 0,
    commonRules: [{ required: true, message: "排序必填" }],
    extra: {
      min: 0,
      precision: 0
    }
  },
  {
    title: "跳转链接",
    dataIndex: "linkUrl",
    formType: "input",
    addDisplay: true,
    editDisplay: true
  },
  {
    title: "链接类型",
    dataIndex: "linkType",
    search: true,
    formType: "radio",
    addDefaultValue: 3,
    dict: {
      data: [
        { label: "内部链接", value: 1 },
        { label: "外部链接", value: 2 },
        { label: "无链接", value: 3 }
      ]
    }
  },
  {
    title: "状态",
    dataIndex: "status",
    search: true,
    formType: "radio",
    addDefaultValue: 1,
    dict: {
      data: [
        { label: "启用", value: 1 },
        { label: "禁用", value: 0 }
      ]
    }
  },
  {
    title: "备注",
    dataIndex: "remark",
    formType: "textarea",
    addDisplay: true,
    editDisplay: true,
    hide: true
  }
]);
</script>

<script>
export default { name: "master-mallmanagement-banner" };
</script>

<style scoped></style>
