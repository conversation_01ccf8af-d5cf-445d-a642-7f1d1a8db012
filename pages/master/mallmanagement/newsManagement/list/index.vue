<template>
  <div class="ma-content-block p-4">
    <ma-crud
      :options="crudOptions"
      :columns="columns"
      ref="crudRef"
    >
      <!-- 新闻分类 -->
      <template #category_id="{ record }">
        <div>{{ record.category_name }}</div>
      </template>


      <!-- 状态 -->
      <template #is_enabled="{ record }">
        <a-tag :color="record.is_enabled == 1 ? 'green' : 'gray'">
          {{ record.is_enabled == 1 ? "启用" : "禁用" }}
        </a-tag>
      </template>

      <!-- 文章状态 -->
      <template #status="{ record }">
        <a-tag :color="getStatusColor(record.status)">
          {{ getStatusText(record.status) }}
        </a-tag>
      </template>

      <!-- 浏览次数 -->
      <template #view_count="{ record }">
        <span>{{ record.view_count || 0 }}</span>
      </template>

      <!-- 创建时间 -->
      <template #created_at="{ record }">
        <div v-time="record.created_at" v-if="record.created_at"></div>
        <div v-else>-</div>
      </template>

      <!-- 更新时间 -->
      <template #updated_at="{ record }">
        <div v-time="record.updated_at" v-if="record.updated_at"></div>
        <div v-else>-</div>
      </template>

      <!-- 操作列 -->
      <template #operation="{ record }">
        <a-space>
          <a-link @click="handleEdit(record)">编辑</a-link>
          <a-link @click="handlePreview(record)">预览</a-link>
          <a-popconfirm
            content="确定删除该新闻文章吗？"
            @ok="handleDelete(record)"
          >
            <a-link status="danger">删除</a-link>
          </a-popconfirm>
        </a-space>
      </template>

      <template #operationBeforeExtend="{ record }">
        <a-link type="primary" @click="handleEdit(record)">
          <icon-edit />编辑
        </a-link>
      </template>
    </ma-crud>

    <!-- 新增/编辑抽屉 -->
    <a-drawer
      v-model:visible="formDrawerVisible"
      :title="isEdit ? '编辑新闻文章' : '新增新闻文章'"
      width="800px"
      :footer="false"
      unmount-on-close
      @cancel="handleFormCancel"
      @close="handleFormCancel"
    >
      <NewsForm
        :visible="formDrawerVisible"
        :article-data="currentArticle"
        :is-edit="isEdit"
        @success="handleFormSuccess"
        @cancel="handleFormCancel"
      />
    </a-drawer>

    <!-- 预览抽屉 -->
    <a-drawer
      v-model:visible="previewDrawerVisible"
      title="预览新闻文章"
      width="900px"
      :footer="false"
      unmount-on-close
      @cancel="handlePreviewCancel"
      @close="handlePreviewCancel"
    >
      <NewsPreview
        :visible="previewDrawerVisible"
        :article-data="currentArticle"
      />
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconEdit } from '@arco-design/web-vue/es/icon';
import mallApi from '@/api/mall';
import NewsForm from './components/NewsForm.vue';
import NewsPreview from './components/NewsPreview.vue';

const newsApi = mallApi.news;

// 定义页面路由元信息
definePageMeta({
  name: "mallNewsManagementList",
  path: "/master/mallmanagement/newsManagement/list",
});

const crudRef = ref();
const categoryOptions = ref([]);

// 抽屉状态
const formDrawerVisible = ref(false);
const previewDrawerVisible = ref(false);
const isEdit = ref(false);
const currentArticle = ref({});

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1: return 'green';
    case 0: return 'orange';
    case -1: return 'red';
    default: return 'gray';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1: return '正常';
    case 0: return '隐藏';
    case -1: return '删除';
    default: return '未知';
  }
};

// 处理添加新闻
const handleAdd = () => {
  isEdit.value = false;
  currentArticle.value = {};
  formDrawerVisible.value = true;
};

// 处理编辑新闻
const handleEdit = (record) => {
  isEdit.value = true;
  currentArticle.value = { ...record };
  formDrawerVisible.value = true;
};

// 处理预览新闻
const handlePreview = (record) => {
  currentArticle.value = { ...record };
  previewDrawerVisible.value = true;
};

// 处理删除新闻
const handleDelete = async (record) => {
  try {
    await newsApi.list.delete(record.id);
    Message.success('删除成功');
    crudRef.value.refresh();
  } catch (error) {
    Message.error('删除失败：' + (error.message || '未知错误'));
  }
};

// 表单提交成功
const handleFormSuccess = () => {
  formDrawerVisible.value = false;
  crudRef.value.refresh();
};

// 表单取消
const handleFormCancel = () => {
  formDrawerVisible.value = false;
};

// 预览取消
const handlePreviewCancel = () => {
  previewDrawerVisible.value = false;
};



// ma-crud 配置项
const crudOptions = reactive({
  api: newsApi.list.getList,
  title: "新闻文章列表",
  searchLabelWidth: "80px",
  operationColumn: true,
  operationColumnWidth: 200,
  rowSelection: { showCheckedAll: true },
  add: { show: true, text: "新增文章", action: handleAdd },
  delete: { show: true, api: newsApi.list.delete },
  export: { show: false },
  actionColumn: {
    width: 180,
  },
  table: {
    rowKey: "id",
    border: true,
    size: "medium",
  },
  search: {
    collapsed: false,
    showCollapse: true,
    collapsePosition: "left",
  },
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 这里可以对搜索参数进行处理
    return params;
  },
});

// 启用状态选项
const enabledOptions = [
  { label: "全部", value: "" },
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 },
];

// 文章状态选项
const statusOptions = [
  { label: "全部", value: "" },
  { label: "正常", value: 1 },
  { label: "隐藏", value: 0 },
  { label: "删除", value: -1 },
];

// 表格列配置
const columns = reactive([
  {
    title: "文章标题",
    dataIndex: "title",
    width: 250,
    search: true,
    formType: "input",
    ellipsis: true,
  },

  {
    title: "新闻分类",
    dataIndex: "category_id",
    width: 150,
    search: true,
    formType: "select",
    dict: {
      data: async () => {
        const res = await newsApi.getList();
        const transform = (item) => {
          return {
            value: item.id,
            label: item.name,
          };
        };
        return res.data.items.map(transform);
      },
    },
  },
  {
    title: "启用状态",
    dataIndex: "is_enabled",
    width: 100,
    search: true,
    formType: "select",
    dict: { data: enabledOptions },
  },
  {
    title: "文章状态",
    dataIndex: "status",
    width: 100,
    search: true,
    formType: "select",
    dict: { data: statusOptions },
  },
  {
    title: "排序",
    dataIndex: "sort_order",
    width: 80,
    search: false,
  },
  {
    title: "浏览次数",
    dataIndex: "view_count",
    width: 100,
    search: false,
  },
  {
    title: "创建时间",
    dataIndex: "created_at",
    width: 160,
    search: false,
  },
  {
    title: "更新时间",
    dataIndex: "updated_at",
    width: 160,
    search: false,
  },
]);

// 生命周期钩子
onMounted(() => {
  // 页面加载时可以执行一些初始化操作
  console.log('新闻文章列表页面已加载');
});
</script>

<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: var(--border-radius-medium);
}

.no-image {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-fill-2);
  color: var(--color-text-3);
  font-size: 12px;
  border-radius: 4px;
}
</style>