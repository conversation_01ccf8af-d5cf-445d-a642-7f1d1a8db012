<template>
  <div class="news-form">
    <ma-form
      ref="formRef"
      :columns="columns"
      :model="formData"
      :loading="loading"
      @submit="handleSubmit"
    >
      <template #footer>
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" html-type="submit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </a-button>
        </a-space>
      </template>
    </ma-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import mallApi from '@/api/mall';
const newsApi = mallApi.news;

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  articleData: {
    type: Object,
    default: () => ({})
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'success', 'cancel']);

// 页面状态
const loading = ref(false);
const formRef = ref(null);

// 启用状态选项
const enabledOptions = [
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 },
];

// 文章状态选项
const statusOptions = [
  { label: "正常", value: 1 },
  { label: "隐藏", value: 0 },
];

// 表单数据
const formData = reactive({
  title: '',
  summary: '',
  content: '',
  category_id: '',
  image_url: '',
  sort_order: 0,
  is_enabled: 1,
  status: 1,
  seo_title: '',
  seo_keywords: '',
  seo_description: ''
});

// 表单字段配置
const columns = [
  {
    title: "文章标题",
    dataIndex: "title",
    formType: "input",
    rules: [{ required: true, message: "请输入文章标题" }],
    formProps: {
      placeholder: "请输入文章标题",
      maxLength: 200,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
  {
    title: "封面图片",
    dataIndex: "image_url",
    formType: "upload",
    type: "image",
    multiple: false,
    limit: 1,
    size: 2,
    accept: ".jpg,.jpeg,.png,.webp",
    tip: "最多上传1张图片，建议尺寸800*450px，大小不超过2MB",
    returnType: "url",
    colProps: { span: 24 },
  },
  {
    title: "文章摘要",
    dataIndex: "summary",
    formType: "textarea",
    formProps: {
      placeholder: "请输入文章摘要",
      rows: 4,
      maxLength: 500,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
  {
    title: "新闻分类",
    dataIndex: "category_id",
    formType: "select",
    dict: {
      data: async () => {
        const res = await newsApi.getList();
        const transform = (item) => {
          return {
            value: item.id,
            label: item.name,
          };
        };
        return res.data.items.map(transform);
      },
    },
    rules: [{ required: true, message: "请选择新闻分类" }],
    colProps: { span: 12 },
  },
  {
    title: "启用状态",
    dataIndex: "is_enabled",
    formType: "select",
    dict: { data: enabledOptions },
    rules: [{ required: true, message: "请选择启用状态" }],
    colProps: { span: 12 },
  },
  {
    title: "文章状态",
    dataIndex: "status",
    formType: "select",
    dict: { data: statusOptions },
    rules: [{ required: true, message: "请选择文章状态" }],
    colProps: { span: 12 },
  },
  {
    title: "排序权重",
    dataIndex: "sort_order",
    formType: "input-number",
    formProps: {
      min: 0,
      max: 9999,
      step: 1,
      placeholder: "数值越大越靠前",
    },
    colProps: { span: 12 },
  },
  {
    title: "文章内容",
    dataIndex: "content",
    formType: "wang-editor",
    placeholder: "请输入文章内容",
    height: 300,
    rules: [{ required: true, message: "请输入文章内容" }],
    colProps: { span: 24 },
  },
  {
    title: "SEO标题",
    dataIndex: "seo_title",
    formType: "input",
    formProps: {
      placeholder: "请输入SEO标题",
      maxLength: 200,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
  {
    title: "SEO关键词",
    dataIndex: "seo_keywords",
    formType: "input",
    formProps: {
      placeholder: "请输入SEO关键词，多个关键词用逗号分隔",
      maxLength: 500,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
  {
    title: "SEO描述",
    dataIndex: "seo_description",
    formType: "textarea",
    formProps: {
      placeholder: "请输入SEO描述",
      rows: 3,
      maxLength: 500,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
];

// 监听文章数据变化，填充表单
watch(() => props.articleData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    Object.keys(formData).forEach((key) => {
      if (newData[key] !== undefined) {
        formData[key] = newData[key];
      }
    });
  }
}, { immediate: true, deep: true });

// 监听visible变化，重置表单
watch(() => props.visible, (newVisible, oldVisible) => {
  if (!newVisible && oldVisible) {
    // 延迟重置表单，避免在抽屉关闭动画期间重置
    setTimeout(() => {
      resetForm();
    }, 300);
  }
});

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach(key => {
    if (key === 'is_enabled' || key === 'status') {
      formData[key] = 1;
    } else if (key === 'sort_order') {
      formData[key] = 0;
    } else {
      formData[key] = '';
    }
  });
  // 尝试调用表单重置方法，如果不存在则忽略
  try {
    if (formRef.value && typeof formRef.value.resetFields === 'function') {
      formRef.value.resetFields();
    } else if (formRef.value && typeof formRef.value.reset === 'function') {
      formRef.value.reset();
    } else if (formRef.value && typeof formRef.value.clearValidate === 'function') {
      formRef.value.clearValidate();
    }
  } catch (error) {
    console.warn('表单重置方法调用失败:', error);
  }
};

// 提交表单
const handleSubmit = async (values) => {
  try {
    loading.value = true;
    let data = {...values};
    
    // 确保数据类型正确
    data.is_enabled = Number(data.is_enabled);
    data.status = Number(data.status);
    data.category_id = Number(data.category_id);
    data.sort_order = Number(data.sort_order) || 0;
    
    let res;
    if (props.isEdit && props.articleData.id) {
      // 更新新闻文章
      res = await newsApi.list.update(props.articleData.id, data);
      Message.success("更新新闻文章成功");
    } else {
      // 创建新闻文章
      res = await newsApi.list.create(data);
      Message.success("创建新闻文章成功");
    }

    loading.value = false;
    emit('success', res);
  } catch (error) {
    Message.error(error?.message || "保存新闻文章失败");
    loading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
};
</script>

<style scoped>
.news-form {
  padding: 20px;
}
</style>
