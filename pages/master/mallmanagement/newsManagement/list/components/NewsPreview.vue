<template>
  <div class="news-preview">
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    <div v-else-if="articleData && Object.keys(articleData).length > 0" class="preview-content">
      <!-- 文章头部信息 -->
      <div class="article-header">
        <h1 class="article-title">{{ articleData.title }}</h1>
        <div class="article-meta">
          <a-space>
            <span class="meta-item">
              <icon-tag />
              {{ articleData.category_name || '未分类' }}
            </span>
            <span class="meta-item">
              <icon-eye />
              {{ articleData.view_count || 0 }} 次浏览
            </span>
            <span class="meta-item">
              <icon-clock-circle />
              <span v-time="articleData.created_at"></span>
            </span>
          </a-space>
        </div>
        <div class="article-status">
          <a-space>
            <a-tag :color="articleData.is_enabled == 1 ? 'green' : 'gray'">
              {{ articleData.is_enabled == 1 ? "启用" : "禁用" }}
            </a-tag>
            <a-tag :color="getStatusColor(articleData.status)">
              {{ getStatusText(articleData.status) }}
            </a-tag>
          </a-space>
        </div>
      </div>

      <!-- 封面图片 -->
      <div v-if="articleData.image_url" class="article-image">
        <a-image
          :src="articleData.image_url"
          :alt="articleData.title"
          fit="cover"
          width="100%"
          height="300"
        />
      </div>

      <!-- 文章摘要 -->
      <div v-if="articleData.summary" class="article-summary">
        <h3>文章摘要</h3>
        <p>{{ articleData.summary }}</p>
      </div>

      <!-- 文章内容 -->
      <div class="article-content">
        <h3>文章内容</h3>
        <div class="content-html" v-html="articleData.content"></div>
      </div>

      <!-- SEO信息 -->
      <div v-if="articleData.seo_title || articleData.seo_keywords || articleData.seo_description" class="seo-info">
        <h3>SEO信息</h3>
        <div class="seo-item" v-if="articleData.seo_title">
          <strong>SEO标题：</strong>{{ articleData.seo_title }}
        </div>
        <div class="seo-item" v-if="articleData.seo_keywords">
          <strong>SEO关键词：</strong>{{ articleData.seo_keywords }}
        </div>
        <div class="seo-item" v-if="articleData.seo_description">
          <strong>SEO描述：</strong>{{ articleData.seo_description }}
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="article-info">
        <h3>其他信息</h3>
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="排序权重">
            {{ articleData.sort_order || 0 }}
          </a-descriptions-item>
          <a-descriptions-item label="浏览次数">
            {{ articleData.view_count || 0 }}
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            <span v-time="articleData.created_at"></span>
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            <span v-time="articleData.updated_at"></span>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </div>
    <div v-else class="empty-content">
      <a-empty description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconTag, IconEye, IconClockCircle } from '@arco-design/web-vue/es/icon';
import mallApi from '@/api/mall';
const newsApi = mallApi.news;

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  articleId: {
    type: [String, Number],
    default: null
  },
  articleData: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits(['update:visible']);

// 页面状态
const loading = ref(false);
const articleData = ref({});

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1: return 'green';
    case 0: return 'orange';
    case -1: return 'red';
    default: return 'gray';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1: return '正常';
    case 0: return '隐藏';
    case -1: return '删除';
    default: return '未知';
  }
};

// 获取文章详情
const fetchArticleDetail = async (id) => {
  if (!id) return;
  
  try {
    loading.value = true;
    const res = await newsApi.list.getById(id);
    if (res && res.data) {
      articleData.value = res.data;
    }
    loading.value = false;
  } catch (error) {
    Message.error("获取新闻文章详情失败");
    loading.value = false;
  }
};

// 监听文章ID变化
watch(() => props.articleId, (newId) => {
  if (newId && props.visible) {
    fetchArticleDetail(newId);
  }
}, { immediate: true });

// 监听传入的文章数据
watch(() => props.articleData, (newData) => {
  if (newData && Object.keys(newData).length > 0) {
    articleData.value = newData;
  }
}, { immediate: true, deep: true });

// 监听visible变化
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.articleId) {
    fetchArticleDetail(props.articleId);
  }
});
</script>

<style scoped>
.news-preview {
  padding: 20px;
  max-height: 80vh;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.preview-content {
  max-width: 800px;
  margin: 0 auto;
}

.article-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-border-2);
}

.article-title {
  font-size: 28px;
  font-weight: bold;
  color: var(--color-text-1);
  margin-bottom: 12px;
  line-height: 1.4;
}

.article-meta {
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--color-text-3);
  font-size: 14px;
}

.article-status {
  margin-top: 8px;
}

.article-image {
  margin-bottom: 24px;
  text-align: center;
}

.article-summary {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--color-fill-1);
  border-radius: 6px;
}

.article-summary h3 {
  margin-bottom: 12px;
  color: var(--color-text-1);
}

.article-summary p {
  color: var(--color-text-2);
  line-height: 1.6;
  margin: 0;
}

.article-content {
  margin-bottom: 24px;
}

.article-content h3 {
  margin-bottom: 16px;
  color: var(--color-text-1);
}

.content-html {
  line-height: 1.8;
  color: var(--color-text-2);
}

.content-html :deep(h1),
.content-html :deep(h2),
.content-html :deep(h3),
.content-html :deep(h4),
.content-html :deep(h5),
.content-html :deep(h6) {
  margin: 16px 0 12px 0;
  color: var(--color-text-1);
}

.content-html :deep(p) {
  margin: 12px 0;
}

.content-html :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.seo-info {
  margin-bottom: 24px;
  padding: 16px;
  background-color: var(--color-fill-1);
  border-radius: 6px;
}

.seo-info h3 {
  margin-bottom: 12px;
  color: var(--color-text-1);
}

.seo-item {
  margin-bottom: 8px;
  color: var(--color-text-2);
}

.seo-item strong {
  color: var(--color-text-1);
}

.article-info {
  margin-bottom: 24px;
}

.article-info h3 {
  margin-bottom: 16px;
  color: var(--color-text-1);
}

.empty-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>
