<template>
  <div class="ma-content-block p-4">
    <a-card :title="formTitle" :bordered="false">
      <ma-form
        ref="formRef"
        :columns="columns"
        :options="formOptions"
        v-model="formData"
        @submit="handleSubmit"
      >
      </ma-form>
    </a-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRoute, useRouter } from "vue-router";
import { closeTag, modifyTag } from "~/utils/common.js";
import mallApi from "@/api/mall";
const newsApi = mallApi.news;

// 路由相关
const route = useRoute();
const router = useRouter();
const id = route.params.id;
// 判断是否为编辑模式
const isEdit = id != "add";

if (isEdit) {
  const currentTag = {
    path: route.fullPath,
    name: route.name,
  };
  modifyTag(currentTag, "编辑新闻文章");
}

// 页面状态
const loading = ref(false);
const formRef = ref(null);
const formTitle = computed(() => (isEdit ? "编辑新闻文章" : "新增新闻文章"));

// 启用状态选项
const enabledOptions = [
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 },
];

// 文章状态选项
const statusOptions = [
  { label: "正常", value: 1 },
  { label: "隐藏", value: 0 },
];

// 表单数据
const formData = reactive({
  title: '',
  summary: '',
  content: '',
  category_id: '',
  image_url: '',
  sort_order: 0,
  is_enabled: 1,
  status: 1,
  seo_title: '',
  seo_keywords: '',
  seo_description: ''
});

// 表单配置项
const formOptions = reactive({
  submitText: "保存",
  resetText: "重置",
  labelWidth: 100,
  layout: "horizontal",
  submitShowBtn: true,
  resetShowBtn: true,
});

// 表单字段配置
const columns = [
  {
    title: "文章标题",
    dataIndex: "title",
    formType: "input",
    rules: [{ required: true, message: "请输入文章标题" }],
    formProps: {
      placeholder: "请输入文章标题",
      maxLength: 200,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
  {
    title: "封面图片",
    dataIndex: "image_url",
    formType: "upload",
    type: "image",
    multiple: false,
    limit: 1,
    size: 2,
    accept: ".jpg,.jpeg,.png,.webp",
    tip: "最多上传1张图片，建议尺寸800*450px，大小不超过2MB",
    returnType: "url",
    colProps: { span: 24 },
  },
  {
    title: "文章摘要",
    dataIndex: "summary",
    formType: "textarea",
    formProps: {
      placeholder: "请输入文章摘要",
      rows: 4,
      maxLength: 500,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
  {
    title: "新闻分类",
    dataIndex: "category_id",
    formType: "select",
    dict: {
      data: async () => {
        const res = await newsApi.getList();
        const transform = (item) => {
          return {
            value: item.id,
            label: item.name,
          };
        };
        return res.data.items.map(transform);
      },
    },
    rules: [{ required: true, message: "请选择新闻分类" }],
    colProps: { span: 12 },
  },
  {
    title: "启用状态",
    dataIndex: "is_enabled",
    formType: "select",
    dict: { data: enabledOptions },
    rules: [{ required: true, message: "请选择启用状态" }],
    colProps: { span: 12 },
  },
  {
    title: "文章状态",
    dataIndex: "status",
    formType: "select",
    dict: { data: statusOptions },
    rules: [{ required: true, message: "请选择文章状态" }],
    colProps: { span: 12 },
  },
  {
    title: "排序权重",
    dataIndex: "sort_order",
    formType: "input-number",
    formProps: {
      min: 0,
      max: 9999,
      step: 1,
      placeholder: "数值越大越靠前",
    },
    colProps: { span: 12 },
  },
  {
    title: "文章内容",
    dataIndex: "content",
    formType: "wang-editor",
    placeholder: "请输入文章内容",
    height: 400,
    rules: [{ required: true, message: "请输入文章内容" }],
    colProps: { span: 24 },
  },
  {
    title: "SEO标题",
    dataIndex: "seo_title",
    formType: "input",
    formProps: {
      placeholder: "请输入SEO标题",
      maxLength: 200,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
  {
    title: "SEO关键词",
    dataIndex: "seo_keywords",
    formType: "input",
    formProps: {
      placeholder: "请输入SEO关键词，多个关键词用逗号分隔",
      maxLength: 500,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
  {
    title: "SEO描述",
    dataIndex: "seo_description",
    formType: "textarea",
    formProps: {
      placeholder: "请输入SEO描述",
      rows: 3,
      maxLength: 500,
      showWordLimit: true,
    },
    colProps: { span: 24 },
  },
];

// 获取新闻文章详情
const fetchNewsDetail = async () => {
  if (!isEdit) return;

  try {
    loading.value = true;

    // 判断当前地址，如果ID不是add则调用获取详情接口
    if (id !== 'add') {
      const res = await newsApi.list.getById(id);
      if (res && res.data) {
        const newsData = res.data;
        // 填充表单数据
        Object.keys(formData).forEach((key) => {
          if (newsData[key] !== undefined) {
            formData[key] = newsData[key];
          }
        });
      }
    }
    loading.value = false;
  } catch (error) {
    Message.error("获取新闻文章详情失败");
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async (values) => {
  try {
    loading.value = true;
    let data = {...values}

    // 确保数据类型正确
    data.is_enabled = Number(data.is_enabled);
    data.status = Number(data.status);
    data.category_id = Number(data.category_id);
    data.sort_order = Number(data.sort_order) || 0;

    let res;

    if (!isEdit) {
      // 创建新闻文章
      res = await newsApi.list.create(data);
      Message.success("新增新闻文章成功");
    } else {
      // 更新新闻文章
      res = await newsApi.list.update(id, data);
      Message.success("编辑新闻文章成功");
    }

    loading.value = false;
    // 返回列表页
    router.push("/master/mallmanagement/newsManagement/list");
  } catch (error) {
    Message.error(error?.message || "保存新闻文章失败");
    loading.value = false;
  }
};

// 页面加载时获取详情
onMounted(() => {
  fetchNewsDetail();
});

// 页面元数据
definePageMeta({
  name: "mallNewsManagementListDetail",
  path: "/master/mallmanagement/newsManagement/list/details/:id",
});
</script>
