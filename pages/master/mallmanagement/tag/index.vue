<!--
 - 用户标签管理页面
 - 提供用户标签的增删改查功能
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <div class="flex">
      <!-- 左侧分类列表 -->
      <div class="w-80 mr-4 border rounded">
        <div class="p-3 bg-gray-50 border-b flex justify-between items-center">
          <span class="font-medium">用户标签组列表</span>
        </div>
        
        <!-- 分类列表 -->
        <div class="category-list">
          <a-button class="w-full mb-2 mt-2" type="primary" size="small" @click="addCategory">+ 添加</a-button>
          <a-table 
            :columns="categoryColumns" 
            :data="categoryData" 
            :pagination="false"
            :bordered="false"
            size="small"
            @row-click="handleCategoryClick"
            :row-class="getRowClass"
          >
            <template #operations="{ record }">
              <div class="flex space-x-1">
                <a-button type="text" size="mini" @click="editCategory(record)">编辑</a-button>
                <a-button type="text" size="mini" @click="deleteCategory(record)">删除</a-button>
              </div>
            </template>
          </a-table>
        </div>
      </div>
      
      <!-- 右侧标签列表 -->
      <div class="flex-1 border rounded">
        <div class="p-3 bg-gray-50 border-b flex justify-between items-center">
          <div class="flex items-center">
            <span class="font-medium mr-2">标签组编码</span>
            <a-input v-model="selectedCategoryId" size="small" style="width: 200px" />
          </div>
          <div>
            <span class="font-medium mr-2">标签组名称</span>
            <a-input v-model="selectedCategoryName" size="small" style="width: 120px" />
          </div>
          <!-- <div>
            <span class="font-medium mr-2">排序</span>
            <a-input-number v-model="selectedSort" size="small" style="width: 80px" />
          </div> -->
          <div>
            <a-switch v-model="selectedStatus" size="small" @change="handleTagGroupStatusChange" />
          </div>
        </div>
        
        <!-- 标签列表 -->
        <div class="tag-list p-2">
          <a-button class="mb-2" type="primary" size="small" @click="addTag">+ 添加</a-button>
          <a-table 
            :columns="columns" 
            :data="tagData" 
            :pagination="{ pageSize: 10 }"
            size="small"
            :loading="loading"
            row-key="id"
          >
            <!-- 状态列 -->
            <template #status="{ record }">
              <a-switch :model-value="record.status === 1" size="small" @change="handleStatusChange(record, $event)" />
            </template>
            
            <!-- 是否系统内置 -->
            <template #is_system="{ record }">
              {{ record.is_system == 1 ? '是' : '否' }}
            </template>
            
            <!-- 所属标签组展示 -->
            <template #tag_group>
              <div>
                {{ currentCategory.value ? currentCategory.value.tag_group_name : '-' }}
              </div>
            </template>
            
            <!-- 操作按钮 -->
            <template #operations="{ record }">
              <div class="flex space-x-1">
                <a-button type="text" size="mini" @click="editTag(record)">编辑</a-button>
                <a-button type="text" size="mini" @click="deleteTag(record)">删除</a-button>
              </div>
            </template>
          </a-table>
        </div>
      </div>
    </div>
    
    <!-- 详情弹窗 -->
    <a-modal v-model:visible="detailVisible" title="标签详情" @cancel="closeDetail" :footer="false">
      <div class="divide-y divide-gray-100">
        <div
          v-for="col in detailFields"
          :key="col.dataIndex"
          class="flex items-center py-3 px-2 hover:bg-gray-50 transition-all"
        >
          <div class="w-32 text-right pr-4 font-medium text-gray-500 bg-gray-50 rounded-l select-none">
            {{ col.title }}：
          </div>
          <div class="flex-1 pl-4 text-gray-900 border-l border-gray-200 min-h-[32px]">
            <template v-if="col.dataIndex === 'status'">
              <a-tag :color="detailRecord.status === 1 ? 'green' : 'red'">
                {{ detailRecord.status === 1 ? '正常' : '停用' }}
              </a-tag>
            </template>
            <template v-else-if="col.dataIndex === 'is_system'">
              <a-tag :color="detailRecord.is_system === 1 ? 'blue' : 'gray'">
                {{ detailRecord.is_system === 1 ? '是' : '否' }}
              </a-tag>
            </template>
            <template v-else-if="col.dataIndex === 'tag_color'">
              <div class="flex items-center">
                <div 
                  class="w-6 h-6 rounded mr-2" 
                  :style="{ backgroundColor: detailRecord.tag_color || '#CCCCCC' }"
                ></div>
                {{ detailRecord.tag_color || '-' }}
              </div>
            </template>
            <template v-else-if="col.dataIndex === 'scene'">
              {{ detailRecord.scene === 'member' ? '会员管理' : 
                 detailRecord.scene === 'marketing' ? '营销活动' : 
                 detailRecord.scene === 'product' ? '商品推荐' : 
                 detailRecord.scene === 'other' ? '其他' : 
                 (detailRecord.scene !== undefined ? detailRecord.scene : '-') }}
            </template>
            <template v-else>
              {{ detailRecord[col.dataIndex] !== undefined ? detailRecord[col.dataIndex] : '-' }}
            </template>
          </div>
        </div>
      </div>
    </a-modal>
    
    <!-- 标签组模态框 -->
    <a-modal
      v-model:visible="tagGroupModalVisible"
      :title="tagGroupModalTitle"
      @ok="handleTagGroupModalOk"
      @cancel="tagGroupModalVisible = false"
    >
      <a-form layout="vertical" :model="tagGroupForm">
        <a-form-item label="标签组名称" required>
          <a-input v-model="tagGroupNameInput" placeholder="请输入标签组名称" />
        </a-form-item>
      </a-form>
    </a-modal>
    
    <!-- 标签模态框 -->
    <a-modal
      v-model:visible="tagModalVisible"
      :title="tagModalTitle"
      @ok="handleTagModalOk"
      @cancel="tagModalVisible = false"
    >
      <a-form layout="vertical" :model="tagForm">
        <a-form-item label="标签名称" required>
          <a-input v-model="tagTitleInput" placeholder="请输入标签名称" />
        </a-form-item>
        <a-form-item label="排序">
          <a-input-number v-model="tagSortInput" placeholder="排序值" style="width: 100%" />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea v-model="tagRemarkInput" placeholder="请输入备注信息" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
  
<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message, Modal } from "@arco-design/web-vue";
import mallmanagementApi from "@/api/master/mallmanagement";

// 分类数据（标签组）
const categoryData = ref([]);
const categoryColumns = [
  { title: "标签组名称", dataIndex: "tag_group_name", width: 180 },
  { title: "操作", slotName: "operations", width: 120 }
];

// 标签数据
const tagData = ref([]);

// 筛选条件
const selectedCategoryId = ref(null);
const selectedCategoryName = ref('');
const selectedParentId = ref(null);
const selectedSort = ref(1);
const selectedStatus = ref(false);

// 标签数据加载状态
const loading = ref(false);
// 当前选中的分类
const currentCategory = ref(null);

// 详情弹窗相关
const detailVisible = ref(false);
const detailRecord = ref({});

// 标签组相关状态变量
const tagGroupNameInput = ref('');
const tagGroupModalVisible = ref(false);
const tagGroupModalTitle = ref('添加标签组');
const tagGroupModalMode = ref('add');
const currentEditTagGroup = ref(null);

// 标签组表单数据
const tagGroupForm = reactive({
  tag_group_name: ''
});

// 标签相关状态变量
const tagTitleInput = ref('');
const tagSortInput = ref(0);
const tagRemarkInput = ref('');
const tagModalVisible = ref(false);
const tagModalTitle = ref('添加标签');
const tagModalMode = ref('add');
const currentEditTag = ref(null);

// 标签表单数据
const tagForm = reactive({
  tag_title: '',
  tag_sort: 0,
  remark: ''
});

// 处理分类点击事件
function handleCategoryClick(record) {
  currentCategory.value = record;
  selectedCategoryId.value = record.id;
  selectedCategoryName.value = record.tag_group_name;
  selectedStatus.value = record.tag_group_enable==1?true:false;
  // 加载该分类下的标签数据
  loadTagsByCategory(record.id);
}

// 获取行样式
function getRowClass(record) {
  return currentCategory.value && currentCategory.value.id === record.id ? 'bg-blue-50' : '';
}

// 加载指定分类下的标签数据
function loadTagsByCategory(categoryId) {
  if (!categoryId) return;
  
  loading.value = true;
  mallmanagementApi.tag.getTagsByGroupId(categoryId)
    .then(res => {
      if (res.code === 200 && res.data) {
        // 正确处理返回的数据格式
        tagData.value = Array.isArray(res.data) ? res.data : (res.data.list || []);
        console.log('加载标签数据成功:', tagData.value);
      } else {
        tagData.value = [];
        Message.error(res.message || '获取标签数据失败');
      }
    })
    .catch(err => {
      console.error('获取标签数据出错:', err);
      Message.error('获取标签数据失败');
      tagData.value = [];
    })
    .finally(() => {
      loading.value = false;
    });
}

// 打开详情弹窗
function openDetails(record) {
  detailRecord.value = { ...record };
  detailVisible.value = true;
}

// 关闭详情弹窗
function closeDetail() {
  detailVisible.value = false;
}

// 标签组相关方法
function addCategory() {
  // 添加标签组逻辑
  tagGroupNameInput.value = '';
  tagGroupModalTitle.value = '添加标签组';
  tagGroupModalMode.value = 'add';
  currentEditTagGroup.value = null;
  tagGroupModalVisible.value = true;
}

const handleTagGroupStatusChange = (record, status) => {
  console.log(record, status);
  mallmanagementApi.tagGroup.update(selectedCategoryId.value, {
      tag_group_enable: record?1:0
    }).then(res => {
      if (res.code === 200) {
        loadTagGroups();
      } else {
        return Promise.reject();
      }
    }).catch(err => {
      console.error('编辑标签组出错:', err);
      return Promise.reject();
    });
}

function editCategory(record) {
  // 编辑标签组逻辑
  tagGroupNameInput.value = record.tag_group_name || '';
  tagGroupModalTitle.value = '编辑标签组';
  tagGroupModalMode.value = 'edit';
  currentEditTagGroup.value = record;
  tagGroupModalVisible.value = true;
}

function handleTagGroupModalOk() {
  if (!tagGroupNameInput.value) {
    Message.error('请输入标签组名称');
    return Promise.reject();
  }
  
  if (tagGroupModalMode.value === 'add') {
    // 添加标签组
    return mallmanagementApi.tagGroup.create({
      tag_group_name: tagGroupNameInput.value,
      tag_group_sort: 0,
      tag_group_buildin: 0,
      tag_group_enable: 1,
      status: 1
    }).then(res => {
      if (res.code === 200) {
        Message.success(res.message || '添加标签组成功');
        loadTagGroups();
        tagGroupModalVisible.value = false;
      } else {
        Message.error(res.message || '添加标签组失败');
        return Promise.reject();
      }
    }).catch(err => {
      console.error('添加标签组出错:', err);
      Message.error('添加标签组失败');
      return Promise.reject();
    });
  } else {
    // 编辑标签组
    return mallmanagementApi.tagGroup.update(currentEditTagGroup.value.id, {
      tag_group_name: tagGroupNameInput.value,
      tag_group_sort: currentEditTagGroup.value.tag_group_sort,
      tag_group_buildin: currentEditTagGroup.value.tag_group_buildin,
      tag_group_enable: currentEditTagGroup.value.tag_group_enable,
      status: currentEditTagGroup.value.status
    }).then(res => {
      if (res.code === 200) {
        Message.success('编辑标签组成功');
        loadTagGroups();
        tagGroupModalVisible.value = false;
      } else {
        Message.error(res.message || '编辑标签组失败');
        return Promise.reject();
      }
    }).catch(err => {
      console.error('编辑标签组出错:', err);
      Message.error('编辑标签组失败');
      return Promise.reject();
    });
  }
}

function deleteCategory(record) {
  // 删除标签组逻辑
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除标签组 "${record.tag_group_name}" 吗？删除后该标签组下的所有标签将无法访问。`,
    onOk: () => {
      return mallmanagementApi.tagGroup.delete(record.id).then(res => {
        if (res.code === 200) {
          Message.success('删除标签组成功');
          loadTagGroups();
          // 如果当前选中的就是要删除的标签组，清空标签列表
          if (currentCategory.value && currentCategory.value.id === record.id) {
            currentCategory.value = null;
            tagData.value = [];
          }
        } else {
          Message.error(res.message || '删除标签组失败');
          return Promise.reject();
        }
      }).catch(err => {
        console.error('删除标签组出错:', err);
        Message.error('删除标签组失败');
        return Promise.reject();
      });
    }
  });
}

// 加载所有标签组
function loadTagGroups() {
  mallmanagementApi.tagGroup.getList()
    .then(res => {
      if (res.code === 200 && res.data) {
        // 正确处理返回的数据格式，提取list属性
        categoryData.value = res.data.list || [];
        console.log('加载标签组数据成功:', categoryData.value);
      } else {
        categoryData.value = [];
        Message.error(res.message || '获取标签组数据失败');
      }
    })
    .catch(err => {
      console.error('获取标签组数据出错:', err);
      Message.error('获取标签组数据失败');
      categoryData.value = [];
    });
}

// 标签相关方法
function addTag() {
  if (!currentCategory.value) {
    Message.warning('请先选择一个标签组');
    return;
  }
  
  // 添加标签逻辑
  tagTitleInput.value = '';
  tagSortInput.value = 0;
  tagRemarkInput.value = '';
  tagModalTitle.value = '添加标签';
  tagModalMode.value = 'add';
  currentEditTag.value = null;
  tagModalVisible.value = true;
}

function editTag(record) {
  // 编辑标签逻辑
  tagTitleInput.value = record.tag_title || '';
  tagSortInput.value = record.tag_sort || 0;
  tagRemarkInput.value = record.remark || '';
  tagModalTitle.value = '编辑标签';
  tagModalMode.value = 'edit';
  currentEditTag.value = record;
  tagModalVisible.value = true;
}

function handleTagModalOk() {
  if (!tagTitleInput.value) {
    Message.error('请输入标签名称');
    return Promise.reject();
  }
  
  if (tagModalMode.value === 'add') {
    // 添加标签
    return mallmanagementApi.tag.create({
      tag_name: tagTitleInput.value,
      tag_sort: tagSortInput.value,
      remark: tagRemarkInput.value,
      tag_group_id: currentCategory.value.id,
      status: 1
    }).then(res => {
      if (res.code === 200) {
        Message.success('添加标签成功');
        loadTagsByCategory(currentCategory.value.id);
        tagModalVisible.value = false;
      } else {
        Message.error(res.message || '添加标签失败');
        return Promise.reject();
      }
    }).catch(err => {
      console.error('添加标签出错:', err);
      Message.error('添加标签失败');
      return Promise.reject();
    });
  } else {
    // 编辑标签
    return mallmanagementApi.tag.update(currentEditTag.value.id, {
      tag_name: tagTitleInput.value,
      tag_sort: tagSortInput.value,
      remark: tagRemarkInput.value,
      tag_group_id: currentCategory.value.id,
      status: currentEditTag.value.status
    }).then(res => {
      if (res.code === 200) {
        Message.success('编辑标签成功');
        loadTagsByCategory(currentCategory.value.id);
        tagModalVisible.value = false;
      } else {
        Message.error(res.message || '编辑标签失败');
        return Promise.reject();
      }
    }).catch(err => {
      console.error('编辑标签出错:', err);
      Message.error('编辑标签失败');
      return Promise.reject();
    });
  }
}

// 根据标签组ID获取标签组名称
function getTagGroupName(groupId) {
  if (!groupId) return '-';
  const group = categoryData.value.find(item => item.id === groupId.toString());
  return group ? group.tag_group_name : '-';
}

// 处理标签状态切换
function handleStatusChange(record, newStatus) {
  const statusValue = newStatus ? 1 : 0;
  
  // 显示加载提示
  const loadingMsg = Message.loading({
    content: '正在更新标签状态...',
    duration: 0
  });
  
  // 调用API更新标签状态
  mallmanagementApi.tag.update(record.id, {
    status: statusValue
  }).then(res => {
    loadingMsg.close();
    if (res.code === 200) {
      Message.success('标签状态更新成功');
      // 更新本地数据，避免重新加载整个列表
      const index = tagData.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        tagData.value[index].status = statusValue;
      }
    } else {
      Message.error(res.message || '标签状态更新失败');
      // 状态更新失败，回滚UI状态
      setTimeout(() => {
        loadTagsByCategory(currentCategory.value.id);
      }, 500);
    }
  }).catch(err => {
    loadingMsg.close();
    console.error('更新标签状态出错:', err);
    Message.error('标签状态更新失败');
    // 状态更新失败，回滚UI状态
    setTimeout(() => {
      loadTagsByCategory(currentCategory.value.id);
    }, 500);
  });
}

function deleteTag(record) {
  // 删除标签逻辑
  Modal.confirm({
    title: "确认删除",
    content: `确定要删除标签 "${record.tag_title}" 吗？`,
    onOk: () => {
      return mallmanagementApi.tag.delete(record.id).then(res => {
        if (res.code === 200) {
          Message.success('删除标签成功');
          loadTagsByCategory(currentCategory.value.id);
        } else {
          Message.error(res.message || '删除标签失败');
          return Promise.reject();
        }
      }).catch(err => {
        console.error('删除标签出错:', err);
        Message.error('删除标签失败');
        return Promise.reject();
      });
    }
  });
}

// 表格列配置
const columns = reactive([
  {
    title: "标签ID",
    dataIndex: "id",
    width: 180
  },
  {
    title: "标签名称",
    dataIndex: "tag_title",
    width: 150
  },
  {
    title: "排序",
    dataIndex: "tag_sort",
    width: 60
  },
  {
    title: "状态",
    dataIndex: "status",
    width: 60,
    slotName: "status"
  },
  {
    title: "操作",
    slotName: "operations",
    width: 100
  }
]);

// 详情中显示的字段（包括表格中隐藏的字段）
const detailFields = [
  {
    title: "标签名称",
    dataIndex: "tag_name"
  },
  {
    title: "标签颜色",
    dataIndex: "tag_color"
  },
  {
    title: "排序",
    dataIndex: "sort"
  },
  {
    title: "状态",
    dataIndex: "status"
  },
  {
    title: "标签描述",
    dataIndex: "remark"
  },
  {
    title: "是否系统内置",
    dataIndex: "is_system"
  },
  {
    title: "创建时间",
    dataIndex: "created_at"
  }
];

// 页面初始化
onMounted(() => {
  // 初始化操作
  console.log("标签管理页面已加载");
  
  // 加载标签组数据
  loadTagGroups();
});
</script>
  
<script>
export default { name: "master-mallmanagement-tag" };
</script>
  
<style scoped></style>
