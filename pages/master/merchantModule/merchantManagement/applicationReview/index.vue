
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" @row-click="handleRowClick">
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="record.status === 0 ? 'orange' : (record.status === 1 ? 'green' : 'red')">
          {{ statusMap[record.status] || "未知" }}
        </a-tag>
      </template>
      <!-- 自定义列 - 创建时间 -->
      <template #created_at="{ record }">
        <div v-time="record.created_at"></div>
      </template>
      <!-- 自定义列 - 商家类型 -->
      <template #merchant_type="{ record }">
        <div>{{ merchantTypeMap[record.merchant_type] || "未知" }}</div>
      </template>
      <!-- 自定义列 - 店铺类型 -->
      <template #shop_type="{ record }">
        <div>{{ shopTypeMap[record.shop_type] || "未知" }}</div>
      </template>
      <!-- 自定义列 - 审核类型 -->
      <template #review_type="{ record }">
        <div>{{ reviewTypeMap[record.review_type] || "未知" }}</div>
      </template>
    </ma-crud>

    <!-- 商家审核抽屉组件 -->
    <MerchantReviewDrawer
      v-model:visible="drawerVisible"
      :merchant-detail="currentRecord"
      :review-mode="isReviewMode"
      :review-type-map="reviewTypeMap"
      @review="onReviewSubmit"
      @cancel="drawerVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
// 导入抽屉组件
import MerchantReviewDrawer from "./components/MerchantReviewDrawer.vue";
// import { useMerchantApi } from '@/api/master/merchant';
// const merchantApi = useMerchantApi();

definePageMeta({
  name: "applicationReview",
  path: "/master/merchantModule/merchantManagement/applicationReview",
});

const crudRef = ref();
const drawerVisible = ref(false);
const currentRecord = ref(null);
const isReviewMode = ref(false);
// 状态映射 - 保留用于显示状态列
const statusMap = {
  0: "待审核",
  1: "已通过",
  2: "已拒绝",
};

// 查看详情或审核
const viewDetail = (record, reviewMode = false) => {
  currentRecord.value = record;
  isReviewMode.value = reviewMode;
  drawerVisible.value = true;
};

// 行点击事件处理
const handleRowClick = (record) => {
  // 如果是待审核状态，打开审核弹窗
  if (record.status === 0) {
    viewDetail(record, true);
  } else {
    // 其他状态打开查看弹窗
    viewDetail(record, false);
  }
};

// 提交审核
const onReviewSubmit = (reviewData) => {
  // 模拟审核操作
  setTimeout(() => {
    // 更新模拟数据
    const index = mockData.list.findIndex(
      (item) => item.id === reviewData.id
    );
    if (index !== -1) {
      mockData.list[index].status = reviewData.status;
      mockData.list[index].remark = reviewData.remark;
    }

    Message.success("审核操作成功");
    drawerVisible.value = false;
    // 刷新列表数据
    crudRef.value?.refresh();
  }, 300);
};

// 模拟数据
// 商家类型映射
const merchantTypeMap = {
  1: "个人商家",
  2: "企业商家",
};

// 店铺类型映射
const shopTypeMap = {
  1: "专卖店",
  2: "旗舰店",
  3: "专营店",
  4: "个人卖家",
};

// 审核类型映射
const reviewTypeMap = {
  1: "入驻审核",
  2: "联系人信息审核",
  3: "财务信息审核",
  4: "框架协议审核",
  5: "授权类目审核",
  6: "商标信息审核",
  7: "附件审核",
};

const mockData = reactive({
  list: [
    {
      id: 1001,
      username: "merchant001",
      name: "上海智能科技有限公司",
      shop_type: 2,
      contact_person: "张三",
      contact_phone: "13800138001",
      merchant_type: 2,
      review_type: 1,
      license_number: "G123456789",
      license_image: "/placeholder.jpg",
      id_card_front: "/placeholder.jpg",
      id_card_back: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "上海市浦东新区张江路123号",
      created_at: "2025-04-15 10:30:25",
      status: 0,
      remark: "",
    },
    {
      id: 1002,
      username: "merchant002",
      name: "北京创新技术有限公司",
      shop_type: 1,
      contact_person: "李四",
      contact_phone: "13900139002",
      merchant_type: 2,
      review_type: 2,
      license_number: "B987654321",
      license_image: "/placeholder.jpg",
      id_card_front: "/placeholder.jpg",
      id_card_back: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "北京市海淀区中关村街道456号",
      created_at: "2025-04-20 14:25:40",
      status: 1,
      remark: "已审核通过",
    },
    {
      id: 1003,
      username: "merchant003",
      name: "广州互联网服务有限公司",
      shop_type: 3,
      contact_person: "王五",
      contact_phone: "13700137003",
      merchant_type: 2,
      review_type: 3,
      license_number: "C246813579",
      license_image: "/placeholder.jpg",
      id_card_front: "/placeholder.jpg",
      id_card_back: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "广州市天河区体育西路789号",
      created_at: "2025-04-25 09:15:10",
      status: 2,
      remark: "资料不完整，请补充提交",
    },
    {
      id: 1004,
      username: "merchant004",
      name: "深圳新华科技有限公司",
      shop_type: 2,
      contact_person: "赵六",
      contact_phone: "13600136004",
      merchant_type: 2,
      review_type: 5,
      license_number: "D135792468",
      license_image: "/placeholder.jpg",
      id_card_front: "/placeholder.jpg",
      id_card_back: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "深圳市南山区科技园路101号",
      created_at: "2025-05-01 16:45:30",
      status: 0,
      remark: "",
    },
    {
      id: 1005,
      username: "merchant005",
      name: "青岛海洋科技有限公司",
      shop_type: 4,
      contact_person: "孙七",
      contact_phone: "13500135005",
      merchant_type: 2,
      review_type: 6,
      license_number: "E024681357",
      license_image: "/placeholder.jpg",
      id_card_front: "/placeholder.jpg",
      id_card_back: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "青岛市市南区青岛路202号",
      created_at: "2025-05-05 11:20:15",
      status: 0,
      remark: "",
    },
  ],
  total: {
    total: 5,
    currentPage: 1,
    totalPage: null,
  },
});

// CRUD配置
const crud = reactive({
  // 使用模拟数据替代API调用
  api: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: "200",
          message: "操作成功",
          data: {
            items: mockData.list,
            pageInfo: mockData.total,
          },
        });
      }, 300); // 模拟网络请求延迟
    });
  },
  showIndex: false,
  pageLayout: "fixed",
  rowSelection: { showCheckedAll: true },
  cacheKey: "merchant_application_review",
  add: { show: false },
  edit: { show: false },
  delete: { show: false },
  enableRowHighlight: true
});

// 表格列配置
const columns = reactive([
  { title: "ID", dataIndex: "id", width: 80 },
  { title: "商家用户名", dataIndex: "username", width: 120 },
  { title: "商家名称", dataIndex: "name", search: true },
  { 
    title: "商家类型", 
    dataIndex: "merchant_type", 
    width: 100, 
    slotName: "merchant_type",
    filters: [
      { text: "个人商家", value: 1 },
      { text: "企业商家", value: 2 },
    ],
  },
  { 
    title: "店铺类型", 
    dataIndex: "shop_type", 
    width: 100, 
    slotName: "shop_type",
    filters: [
      { text: "专卖店", value: 1 },
      { text: "旗舰店", value: 2 },
      { text: "专营店", value: 3 },
      { text: "个人卖家", value: 4 },
    ],
  },
  { 
    title: "审核类型", 
    dataIndex: "review_type", 
    width: 130, 
    slotName: "review_type",
    filters: [
      { text: "入驻审核", value: 1 },
      { text: "联系人信息审核", value: 2 },
      { text: "财务信息审核", value: 3 },
      { text: "框架协议审核", value: 4 },
      { text: "授权类目审核", value: 5 },
      { text: "商标信息审核", value: 6 },
      { text: "附件审核", value: 7 },
    ],
  },
  { title: "联系人", dataIndex: "contact_person", width: 100, search: true },
  { title: "联系电话", dataIndex: "contact_phone", width: 120, search: true },
  {
    title: "审核状态",
    dataIndex: "status",
    width: 100,
    slotName: "status",
    filters: [
      { text: "待审核", value: 0 },
      { text: "已通过", value: 1 },
      { text: "已拒绝", value: 2 },
    ],
  },
  {
    title: "申请时间",
    dataIndex: "created_at",
    width: 180,
    slotName: "created_at",
  },
]);

// 页面初始化
onMounted(() => {
  // 获取初始数据
  crudRef.value?.refresh();
});
</script>

<script>
export default { name: "master-merchantManagement-applicationReview" };
</script>
