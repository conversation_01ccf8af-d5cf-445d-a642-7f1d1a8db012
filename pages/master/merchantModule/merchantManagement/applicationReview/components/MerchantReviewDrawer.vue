<template>
  <a-drawer
    :visible="visible"
    :width="1000"
    :title="
      '商户' +
      (reviewMode ? '审核' : '详情') +
      ' - ' +
      (merchantDetail?.name || '')
    "
    @cancel="handleCancel"
    :ok-text="reviewMode ? '提交审核' : null"
    :cancel-text="'关闭'"
    @ok="handleReview"
    unmountOnClose
    :footer="false"
  >
    <div v-if="merchantDetail" class="merchant-detail-container p-4">
      <!-- 基本信息部分 -->
      <div class="basic-info mb-6 bg-gray-50 p-4 rounded">
        <h4 class="text-base font-medium mb-4 text-blue-500 border-b pb-2">
          基本信息
        </h4>
        <div class="grid grid-cols-2 gap-4">
          <div class="info-item">
            <div class="text-gray-500">标记状态:</div>
            <div>正常</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">店铺状态:</div>
            <div>启用</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">
              用户名称 <span class="text-red-500">*</span>:
            </div>
            <div>{{ merchantDetail.name }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">
              手机号 <span class="text-red-500">*</span>:
            </div>
            <div>{{ merchantDetail.contact_phone }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">
              企业名称 <span class="text-red-500">*</span>:
            </div>
            <div>{{ merchantDetail.name }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">
              法定代表人 <span class="text-red-500">*</span>:
            </div>
            <div>{{ merchantDetail.contact_person }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">
              统一社会信用代码 <span class="text-red-500">*</span>:
            </div>
            <div>{{ merchantDetail.license_number }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">所在地区:</div>
            <div>
              <span class="mr-1">浙江省</span>
              <span>杭州市</span>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">详细地址:</div>
            <div>{{ merchantDetail.address }}</div>
          </div>
          <div class="info-item col-span-2">
            <div class="text-gray-500">经营范围:</div>
            <div class="flex-1">
              {{
                merchantDetail.business_scope ||
                "计算机软件、网络技术开发、技术转让、技术咨询、计算机系统集成、电子产品、通讯设备、网络设备销售、电子商务技术服务、企业管理咨询"
              }}
            </div>
          </div>
        </div>
      </div>

      <!-- 选项卡导航 -->
      <a-tabs default-active-key="1">
        <!-- 联系人信息 -->
        <a-tab-pane key="1" title="联系人信息">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">联系人详情</h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="info-item">
                <div class="text-gray-500">联系人姓名:</div>
                <div>{{ merchantDetail.contact_person }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">联系人手机:</div>
                <div>{{ merchantDetail.contact_phone }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">联系人邮箱:</div>
                <div>{{ merchantDetail.email }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">联系人职位:</div>
                <div>经理</div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 财务信息 -->
        <a-tab-pane key="2" title="财务信息">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">账户信息</h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="info-item">
                <div class="text-gray-500">开户名称:</div>
                <div>{{ merchantDetail.name }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">开户银行:</div>
                <div>中国银行杭州分行</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">银行账号:</div>
                <div>6214 **** **** 1234</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">开户凭证:</div>
                <div><a-button type="text" size="small">查看</a-button></div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 框架协议 -->
        <a-tab-pane key="3" title="框架协议">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">协议信息</h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="info-item">
                <div class="text-gray-500">签约状态:</div>
                <div>已签约</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">签约日期:</div>
                <div>2023-05-10</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">到期日期:</div>
                <div>2024-05-10</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">协议文件:</div>
                <div><a-button type="text" size="small">查看</a-button></div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 授权类目 -->
        <a-tab-pane key="4" title="授权类目（商品类目）">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">类目信息</h4>
            <a-table
              :columns="categoryColumns"
              :data="categoryData"
              :pagination="false"
              size="small"
              border
            >
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 商标信息 -->
        <a-tab-pane key="5" title="商标信息">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">商标详情</h4>
            <a-table
              :columns="trademarkColumns"
              :data="trademarkData"
              :pagination="false"
              size="small"
              border
            >
              <template #image="{ record }">
                <a-image
                  :src="record.image || '/placeholder.png'"
                  width="60"
                  height="60"
                  :preview-visible="false"
                />
              </template>
              <template #status="{ record }">
                <a-tag :color="record.status === '已注册' ? 'green' : 'blue'">
                  {{ record.status }}
                </a-tag>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 附件 -->
        <a-tab-pane key="6" title="附件">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">相关附件</h4>
            <div class="flex space-x-6">
              <div class="flex flex-col items-center">
                <a-image
                  :src="merchantDetail.license_image || '/placeholder.png'"
                  width="120"
                  height="160"
                  :preview-visible="false"
                />
                <span class="mt-1 text-xs">营业执照.pdf</span>
                <span class="text-xs text-gray-500">1.2KB</span>
              </div>
              <div class="flex flex-col items-center">
                <a-image
                  :src="merchantDetail.id_card_front || '/placeholder.png'"
                  width="120"
                  height="160"
                  :preview-visible="false"
                />
                <span class="mt-1 text-xs">组织机构代码.pdf</span>
                <span class="text-xs text-gray-500">850KB</span>
              </div>
              <div class="flex flex-col items-center">
                <a-image
                  :src="merchantDetail.id_card_back || '/placeholder.png'"
                  width="120"
                  height="160"
                  :preview-visible="false"
                />
                <span class="mt-1 text-xs">法人身份证.jpg</span>
                <span class="text-xs text-gray-500">220KB</span>
              </div>
              <div class="flex flex-col items-center">
                <a-image
                  :src="'/placeholder.png'"
                  width="120"
                  height="160"
                  :preview-visible="false"
                />
                <span class="mt-1 text-xs">开户许可证.jpg</span>
                <span class="text-xs text-gray-500">350KB</span>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>

      <a-divider />

      <!-- 只在审核模式下显示审核表单 -->
      <div v-if="reviewMode" class="mt-4">
        <a-form
          :model="reviewForm"
          ref="reviewFormRef"
          :label-col-props="{ span: 4 }"
          :wrapper-col-props="{ span: 20 }"
        >
          <a-form-item
            field="status"
            label="审核结果"
            :rules="[{ required: true, message: '请选择审核结果' }]"
          >
            <a-radio-group v-model="reviewForm.status">
              <a-radio :value="1">通过</a-radio>
              <a-radio :value="2">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            field="remark"
            label="审核意见"
            :rules="[{ required: true, message: '请填写审核意见' }]"
          >
            <a-textarea
              v-model="reviewForm.remark"
              placeholder="请输入审核意见"
              :max-length="200"
              show-word-limit
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </a-form-item>
        </a-form>

        <div class="flex justify-end mt-4">
          <a-button @click="handleCancel" class="mr-2">取消</a-button>
          <a-button type="primary" @click="handleReview">提交审核</a-button>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, computed, reactive, watch, h } from "vue";
import { Message } from "@arco-design/web-vue";
import { IconUser } from "@arco-design/web-vue/es/icon";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  merchantDetail: {
    type: Object,
    default: () => null,
  },
  // 是否为审核模式，默认为false（即查看模式）
  reviewMode: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:visible", "review", "cancel"]);

const reviewFormRef = ref(null);

// 审核表单
const reviewForm = reactive({
  id: null,
  status: 1, // 默认选中通过选项
  remark: "",
});

// 状态映射
const statusMap = {
  0: "待审核",
  1: "已通过",
  2: "已拒绝",
};

// 商家类型映射
const merchantTypeMap = {
  1: "个人商家",
  2: "企业商家",
};

// 商品类目表格列定义
const categoryColumns = [
  {
    title: "类目编码",
    dataIndex: "code",
  },
  {
    title: "类目名称",
    dataIndex: "name",
  },
  {
    title: "类目等级",
    dataIndex: "level",
  },
  {
    title: "授权状态",
    dataIndex: "status",
    slotName: "status",
  },
  {
    title: "授权时间",
    dataIndex: "authTime",
  },
];

// 模拟商品类目数据
const categoryData = [
  {
    code: "A001",
    name: "电子产品",
    level: "一级",
    status: "已授权",
    authTime: "2023-05-10 14:30:22",
  },
];

// 商标信息表格列定义
const trademarkColumns = [
  {
    title: "商标图片",
    slotName: "image",
    width: 80,
    align: "center",
  },
  {
    title: "商标名称",
    dataIndex: "name",
    width: 100,
  },
  {
    title: "注册号",
    dataIndex: "registrationNo",
    width: 100,
  },
  {
    title: "商标类型",
    dataIndex: "type",
    width: 100,
  },
  {
    title: "商标申请人",
    dataIndex: "applicant",
    width: 120,
  },
  {
    title: "有效期",
    dataIndex: "validPeriod",
    width: 160,
  },
  {
    title: "商标状态",
    slotName: "status",
    width: 90,
    align: "center",
  },
];

// 模拟商标数据
const trademarkData = ref([
  {
    name: computed(() => props.merchantDetail?.name || ""),
    registrationNo: "**********",
    image: "/placeholder.png",
    type: "组合商标",
    applicant: computed(() => props.merchantDetail?.name || ""),
    validPeriod: "2023-01-01 至 2033-01-01",
    status: "已注册",
    authorization: "已授权使用",
  },
]);

// 取消
const handleCancel = () => {
  emit("update:visible", false);
  emit("cancel");
};

// 提交审核
const handleReview = async () => {
  try {
    // 表单验证
    await reviewFormRef.value.validate();

    // 构建审核数据
    const reviewData = {
      id: props.merchantDetail.id,
      status: reviewForm.status,
      remark: reviewForm.remark,
    };

    // 提交给父组件处理
    emit("review", reviewData);
  } catch (e) {
    console.error(e);
    Message.error("审核操作失败");
  }
};

// 当merchantDetail改变时，重置表单
watch(
  () => props.merchantDetail,
  (newVal) => {
    if (newVal) {
      reviewForm.id = newVal.id;
      reviewForm.status = 1; // 默认选中通过选项
      reviewForm.remark = "";
      if (reviewFormRef.value) {
        reviewFormRef.value.resetFields();
      }
    }
  },
  { immediate: true }
);
</script>

<style scoped>
.a-collapse :deep(.arco-collapse-item-header) {
  font-weight: 600;
  font-size: 16px;
  background-color: #f5f7fa;
  padding: 10px 16px;
}

.a-collapse :deep(.arco-collapse-item-content-box) {
  padding: 16px;
}
</style>
