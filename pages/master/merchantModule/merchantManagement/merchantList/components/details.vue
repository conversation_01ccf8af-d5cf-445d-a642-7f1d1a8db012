<template>
  <a-drawer
    :visible="visible"
    :width="1000"
    :title="'商户详情 - ' + (merchantDetail?.entity_name || '')"
    @cancel="handleCancel"
    :cancel-text="'关闭'"
    unmountOnClose
  >
    <div v-if="merchantDetail" class="merchant-detail-container p-4">
      <!-- 基本信息部分 -->
      <div class="basic-info mb-6 bg-gray-50 p-4 rounded">
        <h4 class="text-base font-medium mb-4 text-blue-500 border-b pb-2">
          基本信息
        </h4>
        <div class="grid grid-cols-2 gap-4">
          <div class="info-item">
            <div class="text-gray-500">商户ID:</div>
            <div>{{ merchantDetail.id }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">账号:</div>
            <div>{{ merchantDetail.username }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">店铺类型:</div>
            <div>{{ getShopType(merchantDetail.shop_type) }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">主体名称:</div>
            <div>{{ merchantDetail.entity_name }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">店铺状态:</div>
            <div>
              <a-tag :color="merchantDetail.shop_status === 1 ? 'green' : 'red'">
                {{ getShopStatus(merchantDetail.shop_status) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">账号状态:</div>
            <div>
              <a-tag :color="merchantDetail.account_status === 1 ? 'green' : 'red'">
                {{ getAccountStatus(merchantDetail.account_status) }}
              </a-tag>
            </div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">注册时间:</div>
            <div v-time="merchantDetail.register_time"></div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">商户类型:</div>
            <div>{{ getMerchantType(merchantDetail.merchant_type) }}</div>
          </div>
          <div class="info-item">
            <div class="text-gray-500">备注:</div>
            <div>{{ merchantDetail.remark || '无' }}</div>
          </div>
        </div>
      </div>

      <!-- 选项卡导航 -->
      <a-tabs default-active-key="order">
        <a-tab-pane key="order" title="交易订单">
          <OrderTable :userId="'179125623762063360'" />
        </a-tab-pane>
        <!-- 联系人信息 -->
        <a-tab-pane key="1" title="联系人信息">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">联系人详情</h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="info-item">
                <div class="text-gray-500">联系人姓名:</div>
                <div>{{ merchantDetail.contact_person }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">联系人手机:</div>
                <div>{{ merchantDetail.contact_phone }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">联系人邮箱:</div>
                <div>{{ merchantDetail.email }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">联系人职位:</div>
                <div>经理</div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 财务信息 -->
        <a-tab-pane key="2" title="财务信息">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">账户信息</h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="info-item">
                <div class="text-gray-500">开户名称:</div>
                <div>{{ merchantDetail.name }}</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">开户银行:</div>
                <div>中国银行杭州分行</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">银行账号:</div>
                <div>6214 **** **** 1234</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">开户凭证:</div>
                <div><a-button type="text" size="small">查看</a-button></div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 框架协议 -->
        <a-tab-pane key="3" title="框架协议">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">协议信息</h4>
            <div class="grid grid-cols-2 gap-4">
              <div class="info-item">
                <div class="text-gray-500">签约状态:</div>
                <div>已签约</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">签约日期:</div>
                <div>2023-05-10</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">到期日期:</div>
                <div>2024-05-10</div>
              </div>
              <div class="info-item">
                <div class="text-gray-500">协议文件:</div>
                <div><a-button type="text" size="small">查看</a-button></div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 授权类目 -->
        <a-tab-pane key="4" title="授权类目（商品类目）">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">类目信息</h4>
            <a-table
              :columns="categoryColumns"
              :data="categoryData"
              :pagination="false"
              size="small"
              border
            >
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 商标信息 -->
        <a-tab-pane key="5" title="商标信息">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">商标详情</h4>
            <a-table
              :columns="trademarkColumns"
              :data="trademarkData"
              :pagination="false"
              size="small"
              border
            >
              <template #image="{ record }">
                <a-image
                  :src="record.image || '/placeholder.png'"
                  width="60"
                  height="60"
                  :preview-visible="false"
                />
              </template>
              <template #status="{ record }">
                <a-tag :color="record.status === '已注册' ? 'green' : 'blue'">
                  {{ record.status }}
                </a-tag>
              </template>
            </a-table>
          </div>
        </a-tab-pane>

        <!-- 附件 -->
        <a-tab-pane key="6" title="附件">
          <div class="info-section">
            <h4 class="text-base font-medium mb-2 text-blue-500">相关附件</h4>
            <div class="flex space-x-6">
              <div class="flex flex-col items-center">
                <a-image
                  :src="merchantDetail.license_image || '/placeholder.png'"
                  width="120"
                  height="160"
                  :preview-visible="false"
                />
                <span class="mt-1 text-xs">营业执照.pdf</span>
                <span class="text-xs text-gray-500">1.2KB</span>
              </div>
              <div class="flex flex-col items-center">
                <a-image
                  :src="merchantDetail.id_card_front || '/placeholder.png'"
                  width="120"
                  height="160"
                  :preview-visible="false"
                />
                <span class="mt-1 text-xs">组织机构代码.pdf</span>
                <span class="text-xs text-gray-500">850KB</span>
              </div>
              <div class="flex flex-col items-center">
                <a-image
                  :src="merchantDetail.id_card_back || '/placeholder.png'"
                  width="120"
                  height="160"
                  :preview-visible="false"
                />
                <span class="mt-1 text-xs">法人身份证.jpg</span>
                <span class="text-xs text-gray-500">220KB</span>
              </div>
              <div class="flex flex-col items-center">
                <a-image
                  :src="'/placeholder.png'"
                  width="120"
                  height="160"
                  :preview-visible="false"
                />
                <span class="mt-1 text-xs">开户许可证.jpg</span>
                <span class="text-xs text-gray-500">350KB</span>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>

      <a-divider />

      <!-- 只在审核模式下显示审核表单 -->
      <div v-if="reviewMode" class="mt-4">
        <a-form
          :model="reviewForm"
          ref="reviewFormRef"
          :label-col-props="{ span: 4 }"
          :wrapper-col-props="{ span: 20 }"
        >
          <a-form-item
            field="status"
            label="审核结果"
            :rules="[{ required: true, message: '请选择审核结果' }]"
          >
            <a-radio-group v-model="reviewForm.status">
              <a-radio :value="1">通过</a-radio>
              <a-radio :value="2">拒绝</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item
            field="remark"
            label="审核意见"
            :rules="[{ required: true, message: '请填写审核意见' }]"
          >
            <a-textarea
              v-model="reviewForm.remark"
              placeholder="请输入审核意见"
              :max-length="200"
              show-word-limit
              :auto-size="{ minRows: 3, maxRows: 5 }"
            />
          </a-form-item>
        </a-form>

        <div class="flex justify-end mt-4">
          <a-button @click="handleCancel" class="mr-2">取消</a-button>
          <a-button type="primary" @click="handleReview">提交审核</a-button>
        </div>
      </div>
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed, reactive } from 'vue';
import OrderTable from './OrderTable.vue';
const props = defineProps({
  // 显示状态
  visible: {
    type: Boolean,
    default: false,
  },
  // 商户详情数据
  merchantDetail: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['update:visible', 'cancel']);

const reviewFormRef = ref(null);

// 审核表单
const reviewForm = reactive({
  id: null,
  status: 1, // 默认选中通过选项
  remark: "",
});

// 状态映射
const statusMap = {
  0: "待审核",
  1: "已通过",
  2: "已拒绝",
};

// 商家类型映射
const merchantTypeMap = {
  1: "个人商家",
  2: "企业商家",
};

// 商品类目表格列定义
const categoryColumns = [
  {
    title: "类目编码",
    dataIndex: "code",
  },
  {
    title: "类目名称",
    dataIndex: "name",
  },
  {
    title: "类目等级",
    dataIndex: "level",
  },
  {
    title: "授权状态",
    dataIndex: "status",
    slotName: "status",
  },
  {
    title: "授权时间",
    dataIndex: "authTime",
  },
];

// 模拟商品类目数据
const categoryData = [
  {
    code: "A001",
    name: "电子产品",
    level: "一级",
    status: "已授权",
    authTime: "2023-05-10 14:30:22",
  },
];

// 商标信息表格列定义
const trademarkColumns = [
  {
    title: "商标图片",
    slotName: "image",
    width: 80,
    align: "center",
  },
  {
    title: "商标名称",
    dataIndex: "name",
    width: 100,
  },
  {
    title: "注册号",
    dataIndex: "registrationNo",
    width: 100,
  },
  {
    title: "商标类型",
    dataIndex: "type",
    width: 100,
  },
  {
    title: "商标申请人",
    dataIndex: "applicant",
    width: 120,
  },
  {
    title: "有效期",
    dataIndex: "validPeriod",
    width: 160,
  },
  {
    title: "商标状态",
    slotName: "status",
    width: 90,
    align: "center",
  },
];

// 模拟商标数据
const trademarkData = [
  {
    name: computed(() => props.merchantDetail?.name || ""),
    registrationNo: "**********",
    image: "/placeholder.png",
    type: "组合商标",
    applicant: computed(() => props.merchantDetail?.name || ""),
    validPeriod: "2023-01-01 至 2033-01-01",
    status: "已注册",
    authorization: "已授权使用",
  },
];

// 关闭抽屉
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
};

// 提交审核
const handleReview = () => {
  console.log('提交审核');
};

// 获取店铺类型文本
const getShopType = (type) => {
  const shopTypeMap = {
    1: "专卖店",
    2: "旗舰店",
    3: "专营店",
    4: "个人卖家",
  };
  return shopTypeMap[type] || "未知";
};

// 获取店铺状态文本
const getShopStatus = (status) => {
  const shopStatusMap = {
    0: "关闭",
    1: "营业中",
  };
  return shopStatusMap[status] || "未知";
};

// 获取账号状态文本
const getAccountStatus = (status) => {
  const accountStatusMap = {
    0: "禁用",
    1: "正常",
  };
  return accountStatusMap[status] || "未知";
};

// 获取商户类型文本
const getMerchantType = (type) => {
  const merchantTypeMap = {
    1: "个人商家",
    2: "企业商家",
  };
  return merchantTypeMap[type] || "未知";
};
</script>

<style scoped>
.merchant-detail-container .info-item {
  display: flex;
  margin-bottom: 8px;
}

.merchant-detail-container .info-item .text-gray-500 {
  min-width: 120px;
  font-weight: 500;
}

.merchant-detail-container .info-section {
  margin-bottom: 20px;
}
</style>
  