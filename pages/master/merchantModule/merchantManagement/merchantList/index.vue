<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 店铺类型列 -->
      <template #shop_type="{ record }">
        <div>{{ shopTypeMap[record.shop_type] || "未知" }}</div>
      </template>
      <!-- 店铺状态列 -->
      <template #shop_status="{ record }">
        <a-tag :color="record.shop_status === 1 ? 'green' : 'red'">
          {{ shopStatusMap[record.shop_status] || "未知" }}
        </a-tag>
      </template>
      <!-- 帐号状态列 -->
      <template #account_status="{ record }">
        <a-tag :color="record.account_status === 1 ? 'green' : 'red'">
          {{ accountStatusMap[record.account_status] || "未知" }}
        </a-tag>
      </template>
      <!-- 自定义列 - 注册时间 -->
      <template #register_time="{ record }">
        <div v-time="record.register_time"></div>
      </template>
      <!-- 自定义操作按钮 -->
      <template #operation="{ record }">
        <a-button type="text" size="small" @click="viewDetail(record)">
          <template #icon><icon-eye /></template>
          查看
        </a-button>
        <a-button
          type="text"
          status="warning"
          size="small"
          @click="editMerchant(record)"
        >
          <template #icon><icon-edit /></template>
          编辑
        </a-button>
        
      </template>
    </ma-crud>

    <!-- 商户详情抽屉组件 -->
    <MerchantDetails
      v-model:visible="drawerVisible"
      :merchant-detail="currentRecord"
      @cancel="drawerVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import MerchantDetails from "./components/details.vue";

definePageMeta({
  name: "merchantList",
  path: "/master/merchantModule/merchantManagement/merchantList",
});

const crudRef = ref();
const drawerVisible = ref(false);
const currentRecord = ref(null);

// 店铺类型映射
const shopTypeMap = {
  1: "专卖店",
  2: "旗舰店",
  3: "专营店",
  4: "个人卖家",
};

// 店铺状态映射
const shopStatusMap = {
  0: "关闭",
  1: "营业中",
};

// 帐号状态映射
const accountStatusMap = {
  0: "禁用",
  1: "正常",
};

// 查看详情
const viewDetail = (record) => {
  currentRecord.value = record;
  drawerVisible.value = true;
};

// 编辑商户
const editMerchant = (record) => {
  // 此处可以添加编辑商户的逻辑
  console.log("编辑商户:", record);
};

// 修改商户状态
const changeMerchantStatus = (record, status) => {
  // 此处可以添加修改商户状态的逻辑
  const statusText = status === 1 ? "启用" : "禁用";
  
  // 模拟API调用
  setTimeout(() => {
    // 更新模拟数据
    const index = mockData.list.findIndex(
      (item) => item.id === record.id
    );
    if (index !== -1) {
      mockData.list[index].account_status = status;
    }

    Message.success(`${statusText}操作成功`);
    // 刷新列表数据
    crudRef.value?.refresh();
  }, 300);
};

// 模拟数据
const mockData = reactive({
  list: [
    {
      id: 1001,
      username: "merchant001",
      shop_type: 2,
      entity_name: "上海智能科技有限公司",
      shop_status: 1,
      account_status: 1,
      register_time: "2025-04-15 10:30:25",
      contact_person: "张三",
      contact_phone: "***********",
      merchant_type: 2,
      license_number: "G123456789",
      license_image: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "上海市浦东新区张江路123号",
      remark: "旗舰店",
    },
    {
      id: 1002,
      username: "merchant002",
      shop_type: 1,
      entity_name: "北京创新技术有限公司",
      shop_status: 1,
      account_status: 1,
      register_time: "2025-04-20 14:25:40",
      contact_person: "李四",
      contact_phone: "***********",
      merchant_type: 2,
      license_number: "B987654321",
      license_image: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "北京市海淀区中关村街道456号",
      remark: "专卖店",
    },
    {
      id: 1003,
      username: "merchant003",
      shop_type: 3,
      entity_name: "广州互联网服务有限公司",
      shop_status: 0,
      account_status: 1,
      register_time: "2025-04-25 09:15:10",
      contact_person: "王五",
      contact_phone: "***********",
      merchant_type: 2,
      license_number: "C246813579",
      license_image: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "广州市天河区体育西路789号",
      remark: "休业整顿中",
    },
    {
      id: 1004,
      username: "merchant004",
      shop_type: 2,
      entity_name: "深圳新华科技有限公司",
      shop_status: 1,
      account_status: 0,
      register_time: "2025-04-28 16:45:30",
      contact_person: "赵六",
      contact_phone: "***********",
      merchant_type: 2,
      license_number: "D135792468",
      license_image: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "深圳市南山区科技园路101号",
      remark: "账号违规已禁用",
    },
    {
      id: 1005,
      username: "merchant005",
      shop_type: 4,
      entity_name: "王小明",
      shop_status: 1,
      account_status: 1,
      register_time: "2025-05-01 11:20:15",
      contact_person: "王小明",
      contact_phone: "***********",
      merchant_type: 1,
      id_card_number: "110101199001010123",
      id_card_front: "/placeholder.jpg",
      id_card_back: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "北京市朝阳区建国门外大街15号",
      remark: "个人卖家",
    },
    {
      id: 1006,
      username: "merchant006",
      shop_type: 3,
      entity_name: "杭州数智科技有限公司",
      shop_status: 1,
      account_status: 1,
      register_time: "2025-05-05 14:30:25",
      contact_person: "钱八",
      contact_phone: "***********",
      merchant_type: 2,
      license_number: "F135792468",
      license_image: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "杭州市西湖区文三路188号",
      remark: "新入驻商家",
    },
    {
      id: 1007,
      username: "merchant007",
      shop_type: 2,
      entity_name: "成都云科技有限公司",
      shop_status: 1,
      account_status: 1,
      register_time: "2025-05-08 09:45:10",
      contact_person: "周九",
      contact_phone: "***********",
      merchant_type: 2,
      license_number: "G246813579",
      license_image: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "成都市高新区天府大道999号",
      remark: "旗舰店",
    },
    {
      id: 1008,
      username: "merchant008",
      shop_type: 4,
      entity_name: "李小红",
      shop_status: 0,
      account_status: 1,
      register_time: "2025-05-10 16:20:35",
      contact_person: "李小红",
      contact_phone: "***********",
      merchant_type: 1,
      id_card_number: "******************",
      id_card_front: "/placeholder.jpg",
      id_card_back: "/placeholder.jpg",
      email: "<EMAIL>",
      address: "广州市越秀区中山路88号",
      remark: "店铺暂停营业",
    }
  ],
  total: {
    total: 5,
    currentPage: 1,
    totalPage: null,
  },
});

// CRUD配置
const crud = reactive({
  // 接口API配置
  api: () => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: "200",
          message: "操作成功",
          data: {
            items: mockData.list,
            pageInfo: mockData.total,
          },
        });
      }, 300); // 模拟网络请求延迟
    });
  },
  // 表格配置
  table: {
    // 是否显示序号
    showIndex: false,
    // 分页布局样式
    pageLayout: "fixed",
    // 行选择配置
    rowSelection: { showCheckedAll: true },
  },
  // 缓存key
  cacheKey: "merchant_list",
  // 搜索框配置
  search: {
    // 是否显示搜索按钮
    show: true,
    // 搜索项目
    items: [
      {
        field: "id",
        title: "ID",
        type: "input",
        defaultValue: "",
        props: { placeholder: "请输入商户ID" },
      },
      {
        field: "username",
        title: "账号",
        type: "input",
        defaultValue: "",
        props: { placeholder: "请输入账号" },
      },
      {
        field: "entity_name",
        title: "主体名称",
        type: "input",
        defaultValue: "",
        props: { placeholder: "请输入主体名称" },
      },
      {
        field: "shop_type",
        title: "店铺类型",
        type: "select",
        defaultValue: "",
        props: {
          placeholder: "请选择店铺类型",
          options: [
            { value: 1, label: "专卖店" },
            { value: 2, label: "旗舰店" },
            { value: 3, label: "专营店" },
            { value: 4, label: "个人卖家" },
          ],
        },
      },
      {
        field: "shop_status",
        title: "店铺状态",
        type: "select",
        defaultValue: "",
        props: {
          placeholder: "请选择店铺状态",
          options: [
            { value: 0, label: "关闭" },
            { value: 1, label: "营业中" },
          ],
        },
      },
      {
        field: "account_status",
        title: "账号状态",
        type: "select",
        defaultValue: "",
        props: {
          placeholder: "请选择账号状态",
          options: [
            { value: 0, label: "禁用" },
            { value: 1, label: "正常" },
          ],
        },
      },
    ],
  },
});

// 表格列配置
const columns = reactive([
  {
    title: "ID",
    dataIndex: "id",
    width: 80,
  },
  {
    title: "账号",
    dataIndex: "username",
    width: 120,
  },
  {
    title: "店铺类型",
    dataIndex: "shop_type",
    width: 120,
    slotName: "shop_type",
  },
  {
    title: "主体名称",
    dataIndex: "entity_name",
    width: 180,
  },
  {
    title: "店铺状态",
    dataIndex: "shop_status",
    width: 100,
    slotName: "shop_status",
  },
  {
    title: "账号状态",
    dataIndex: "account_status",
    width: 100,
    slotName: "account_status",
  },
  {
    title: "注册时间",
    dataIndex: "register_time",
    width: 160,
    slotName: "register_time",
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: 200,
    fixed: "right",
    slotName: "operation",
  },
]);
</script>