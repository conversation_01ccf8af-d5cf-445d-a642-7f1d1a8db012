<template>
  <a-modal
    :visible="visible"
    @update:visible="visible = $event"
    :title="`溯源码查询记录`"
    :footer="false"
    :mask-closable="false"
    :width="900"
    @cancel="handleClose"
  >
      <div class="p-4">
        <div class="mb-4 flex justify-between items-center">
          <div class="text-lg font-medium">
            <a-tag color="blue">商标ID: {{ currentBrandId || '-' }}
              <span class="ml-2">商标名称: {{ currentBrandName || '-' }}</span>
            </a-tag>
          </div>
        </div>

        <a-table 
          :loading="loading" 
          :data="tableData" 
          :columns="columns" 
          :pagination="pagination"
          @page-change="onPageChange"
        >
          <template #createTime="{ record }">
            {{ formatDate(record.createTime) }}
          </template>
          <template #firstQueryTime="{ record }">
            {{ formatDate(record.firstQueryTime) }}
          </template>
        </a-table>
      </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted, watch, computed, nextTick } from "vue";
import { Message } from "@arco-design/web-vue";
import dayjs from "dayjs";
import securityApi from "@/api/master/security";

// 控制弹窗显示
const visible = ref(false);

// 当前商标ID
const currentBrandId = ref(null);

// 当前商标名称
const currentBrandName = ref(null);

/**
 * 表格数据和配置
 */
const tableData = ref([]);
const loading = ref(false);

/**
 * 表格列定义
 */
const columns = reactive([
  {
    title: "溯源码",
    dataIndex: "sourceCode",
    width: 180
  },
  {
    title: "品牌名称",
    dataIndex: "brandName",
    width: 150
  },
  {
    title: "创建时间",
    dataIndex: "createTime",
    slotName: "createTime",
    width: 180
  },
  {
    title: "首次查询时间",
    dataIndex: "firstQueryTime",
    slotName: "firstQueryTime",
    width: 180
  },
  {
    title: "查询次数",
    dataIndex: "queryCount",
    width: 100
  }
]);

/**
 * 分页配置
 */
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showJumper: true,
  showPageSize: true,
  pageSizeOptions: [10, 20, 50, 100]
});

/**
 * 格式化日期
 */
const formatDate = timestamp => {
  if (!timestamp) return "-";
  // 处理 13 位时间戳，先转换为数字
  return dayjs(parseInt(timestamp)).format("YYYY-MM-DD HH:mm:ss");
};

/**
 * 打开弹窗
 */
const open = (row) => {
  console.log(row);
  currentBrandName.value = row.name;
  currentBrandId.value = row.id;
  visible.value = true;
  
  // 在弹窗打开后刷新数据
  nextTick(() => {
    fetchData();
  });
};

/**
 * 获取数据
 */
const fetchData = async () => {
  if (!currentBrandId.value) return;
  
  try {
    loading.value = true;
    const res = await securityApi.getSourceCodeQueryList({ 
      brandId: currentBrandId.value,
      page: pagination.current,
      pageSize: pagination.pageSize
    });
    
    if (res.code === 200) {
      tableData.value = res.data.rows || [];
      pagination.total = res.data.total || 0;
    } else {
      Message.error(res.message || '获取数据失败');
    }
  } catch (error) {
    console.error('获取数据失败:', error);
    Message.error('获取数据失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

/**
 * 分页切换
 */
const onPageChange = (page) => {
  pagination.current = page;
  fetchData();
};

/**
 * 关闭弹窗
 */
const handleClose = () => {
  visible.value = false;
};

// 暴露方法给父组件
defineExpose({
  open,
  close: handleClose
});


</script>
  
  <style scoped>
/* 可以添加额外的样式 */
</style>
  