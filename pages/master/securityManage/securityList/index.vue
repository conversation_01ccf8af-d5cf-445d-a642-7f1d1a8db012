<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crudOptions" :columns="columns" ref="crudRef" >
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-tag :color="record.status === 1 ? 'green' : 'red'">
          {{ record.status === 1 ? '启用' : '禁用' }}
        </a-tag>
      </template>
      
      <!-- 商标图片 -->
      <template #image="{ record }">
        <img v-if="record.image" :src="record.image" style="width: 50px; height: 50px;" />
        <span v-else>无图片</span>
      </template>
      
      <!-- 申请日期 -->
      <template #applyDate="{ record }">
        {{ record.applyDate ? dayjs(parseInt(record.applyDate)).format('YYYY-MM-DD') : '-' }}
      </template>
      
      <!-- 商品列表 -->
      <template #goodsList="{ record }">
        {{ record.goodsList && record.goodsList.length > 0 ? record.goodsList.map(item => item.goodsName).join(', ') : '-' }}
      </template>
      
      <!-- 商标公告 -->
      <template #issuesList="{ record }">
        {{ record.issuesList && record.issuesList.length > 0 ? record.issuesList.map(item => item.issueName).join(', ') : '-' }}
      </template>
      
      <!-- 自定义操作列 -->
      <template #operationBeforeExtend="{ record }">
        <a-button type="text" size="small" @click="handleViewDetail(record)">
          <template #icon><icon-eye /></template>详情</a-button>
        <a-button type="text" size="small" @click="handleEdit(record)">
          <template #icon><icon-edit /></template>修改</a-button>
        <a-button type="text" size="small" @click="handleGenerateCode(record)">
          <template #icon><icon-plus-circle /></template>生成溯源码</a-button>
        <a-button type="text" size="small" @click="handleViewQueryRecords(record)">
          <template #icon><icon-history /></template>查询记录</a-button>
      </template>
      <template #tableBeforeButtons>
        <a-button
              @click="openAdd()"
                type="primary" status="primary"
                class="w-full lg:w-auto mt-2 lg:mt-0"
              >
                <template #icon><icon-plus /></template>
                新增
              </a-button>
      </template>       
    </ma-crud>
  </div>

  <!-- 生成溯源码弹窗 -->
  <a-modal
    v-model:visible="generateCodeVisible"
    :title="generateModalTitle"
    @cancel="generateCodeVisible = false"
    :mask-closable="false"
  >
    <div class="p-4">
      <a-form :model="{ count: generateCodeCount }" layout="vertical">
        <a-form-item field="count" label="生成数量" :rules="[{ required: true, message: '请输入生成数量' }]">
          <a-input-number
            v-model="generateCodeCount"
            placeholder="请输入生成数量"
            :min="1"
            :max="1000"
            style="width: 100%"
            allow-clear
          />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-space>
        <a-button @click="generateCodeVisible = false">取消</a-button>
        <a-button type="primary" :loading="generateLoading" @click="confirmGenerateCode">确认生成</a-button>
      </a-space>
    </template>
  </a-modal>
  
  <!-- 查询记录弹窗 -->
  <QueryRecord ref="queryRecordRef" />
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { Message } from "@arco-design/web-vue";
import QueryRecord from "./components/QueryRecord.vue";
import dayjs from 'dayjs';
import securityApi from '@/api/master/security';

definePageMeta({
  name: 'master-securityManage',
  path: '/master/securityManage/securityList'
})

const crudRef = ref();
// 表格数据
const tableData = ref([]);

// 表格列定义
const columns = reactive([
  {
    title: '商标名称',
    dataIndex: 'name',
    align: 'center',
    search: true,
    width: 150,
    commonRules: [{ required: true, message: '商标名称必填' }],
  },
  {
    title: '品牌代码',
    dataIndex: 'trademarkCode',
    width: 150,
    align: 'center',
  },
  {
    title: '品牌所属地',
    width: 150,
    dataIndex: 'ownerAddress',
    align: 'center',
  },
  {
    title: '商标号',
    width: 150,
    dataIndex: 'registerId',
    align: 'center',
    search: true,
  },
  {
    title: '商标注册类别',
    width: 150,
    dataIndex: 'className',
    align: 'center',
  },
  {
    title: '注册日期',
    dataIndex: 'applyDate',
    formType: "date",
    width: 150,
    format: 'YYYY-MM-DD',
    slot: 'applyDate'
  },
  {
    title: '有效期至',
    dataIndex: 'exclusiveDateLimit',
    width: 200,
    align: 'center',
  },
  {
    title: '商标持有人',
    dataIndex: 'ownerName',
    width: 150,
    align: 'center',
    search: true,
  },
  {
    title: '商标状态',
    dataIndex: 'lastProcedureStatus',
    width: 150,
    align: 'center',
  },
  {
    title: '经营状态',
    dataIndex: 'status',
    search: true,
    width: 150,
    formType: 'select',
    addDefaultValue: 1,
    dict: {
      data: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 }
      ]
    },
    slot: 'status'
  },

]);

// CRUD 配置
const crudOptions = reactive({
  // 使用API数据源
  api: securityApi.getBrandList,
  rowKey: 'id',
  pageLayout: 'fixed',
  searchLabelWidth: '100px',
  operationColumn: true,
  operationColumnWidth: 440,
  // 搜索前处理参数
  beforeSearch: (params) => {
    // 如果有注册日期参数，转换为时间戳
    if(params.register_date){
      params.startRegisterTime = new Date(params.register_date[0]).getTime();
      params.endRegisterTime = new Date(params.register_date[1]).getTime();
      delete params.register_date;
    }else{
      delete params.startRegisterTime;
      delete params.endRegisterTime;
    }
    
    // 如果有有效期参数，转换为时间戳
    if(params.expire_date){
      params.startExpireTime = new Date(params.expire_date[0]).getTime();
      params.endExpireTime = new Date(params.expire_date[1]).getTime();
      delete params.expire_date;
    }else{
      delete params.startExpireTime;
      delete params.endExpireTime;
    }
    
    return params;
  },
});

// 预先获取router实例
const router = useRouter();

// 新增商标
const openAdd = () => {
  import("@/utils/common").then((module) => {
    module.navigateWithTag(router, `/master/securityManage/addSecurity/add`);
  });
};

// 溯源码生成相关变量
const generateCodeVisible = ref(false); // 控制弹窗显示
const generateCodeCount = ref(1); // 生成数量
const currentBrand = ref(null); // 当前选中的商标
const generateLoading = ref(false); // 生成加载状态
const generateModalTitle = computed(() => {
  return `生成商标「${currentBrand.value?.name || ''}」溯源码`;
});

// 处理生成溯源码
const handleGenerateCode = (record) => {
  // 显示弹窗，存储当前商标对象
  currentBrand.value = record;
  generateCodeCount.value = ""; // 默认生成数量为1
  generateCodeVisible.value = true;
};

// 确认生成溯源码
const confirmGenerateCode = async () => {
  if (generateCodeCount.value <= 0) {
    Message.error('生成数量必须大于0');
    return;
  }
  
  generateLoading.value = true;
  try {
    const res = await securityApi.generateSourceCode({ 
      brandId: currentBrand.value.id,
      count: generateCodeCount.value
    });
    
    if (res.code === 200) {
      Message.success(`成功生成${generateCodeCount.value}个溯源码`);
      generateCodeVisible.value = false; // 关闭弹窗
    } else {
      Message.error(res.message || '溯源码生成失败');
    }
  } catch (error) {
    console.error('生成溯源码失败：', error);
    Message.error('生成溯源码失败，请稍后重试');
  } finally {
    generateLoading.value = false;
  }
};

// 查询记录弹窗引用
const queryRecordRef = ref(null);

// 处理查询记录
const handleViewQueryRecords = (record) => {
  // 调用子组件的open方法
  queryRecordRef.value?.open(record);
};


// 处理详情
const handleViewDetail = (record) => {
  import("@/utils/common").then((module) => {
    module.navigateWithTag(router, `/master/securityManage/addSecurity/detail${record.id}`);
  });
};

// 处理编辑
const handleEdit = (record) => {
  import("@/utils/common").then((module) => {
    module.navigateWithTag(router, `/master/securityManage/addSecurity/${record.id}`);
  });
};

</script>

<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}
</style>