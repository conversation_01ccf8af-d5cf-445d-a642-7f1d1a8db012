<!--
 - Mine<PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
-->
<template>
  <a-card class="general-card">
      <a-form ref="formRef" :model="formData" @submit="handleSubmit" layout="horizontal" label-align="left">
        <!-- 基本信息 -->
        <div class="mb-4">
          <a-descriptions :column="2" class="descriptions-with-border custom-descriptions" :label-style="{ width: '150px', 'text-align': 'left' }" :column-style="{ width: '50%' }">
            <a-descriptions-item>
              <template #label><span class="required-field">商标名称</span></template>
              <a-form-item
                field="name"
                hide-label
                :rules="[{ required: true, message: '请输入商标名称' }]"
              >
                <a-input v-model="formData.name" placeholder="请输入商标名称" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>
            
            <a-descriptions-item>
              <template #label><span class="required-field">品牌代码</span></template>
              <a-form-item field="trademarkCode" hide-label :rules="[{ required: true, message: '请输入品牌代码' }]">
                <a-input v-model="formData.trademarkCode" placeholder="请输入品牌代码" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>

            <a-descriptions-item>
              <template #label><span class="required-field">注册号</span></template>
              <a-form-item field="registerId" hide-label :rules="[{ required: true, message: '请输入注册号' }]">
                <a-input v-model="formData.registerId" placeholder="请输入注册号" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>

            <a-descriptions-item>
              <template #label><span class="required-field">申请日期</span></template>
              <a-form-item field="applyDate" hide-label :rules="[{ required: true, message: '请选择申请日期' }]">
                <a-date-picker
                  v-model="formData.applyDate"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  allow-clear
                  :disabled="isDetailMode"
                />
              </a-form-item>
            </a-descriptions-item>

            <a-descriptions-item>
              <template #label><span class="required-field">商标状态</span></template>
              <a-form-item field="lastProcedureStatus" hide-label :rules="[{ required: true, message: '请输入商标状态' }]">
                <a-input v-model="formData.lastProcedureStatus" placeholder="请输入商标状态" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>

            <a-descriptions-item>
              <template #label><span class="required-field">商标类型</span></template>
              <a-form-item field="registrationType" hide-label :rules="[{ required: true, message: '请输入商标类型' }]">
                <a-input v-model="formData.registrationType" placeholder="请输入商标类型" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>

            <a-descriptions-item label="溯源码规则">
              <a-form-item field="sourceCodeRule" hide-label>
                <a-input v-model="formData.sourceCodeRule" placeholder="请输入溯源码规则" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>
            
            <a-descriptions-item label="品牌所属地">
              <a-form-item field="brandLocation" hide-label>
                <a-input v-model="formData.brandLocation" placeholder="请输入品牌所属地" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>

            <a-descriptions-item>
              <template #label><span class="required-field">经营状态</span></template>
              <a-form-item
                field="status"
                hide-label
                :rules="[{ required: true, message: '请选择状态' }]"
              >
                <a-select v-model="formData.status" placeholder="请选择状态" allow-clear style="width: 100%" :disabled="isDetailMode">
                  <a-option :value="1">启用</a-option>
                  <a-option :value="0">禁用</a-option>
                </a-select>
              </a-form-item>
            </a-descriptions-item>
            
            
            <a-descriptions-item label="初审公告日期">
              <a-form-item field="preAnnDate" hide-label>
                <a-date-picker
                  v-model="formData.preAnnDate"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  allow-clear
                  :disabled="isDetailMode"
                />
              </a-form-item>
            </a-descriptions-item>

            <a-descriptions-item label="注册公告日期">
              <a-form-item field="regAnnDate" hide-label>
                <a-date-picker
                  v-model="formData.regAnnDate"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  allow-clear
                  :disabled="isDetailMode"
                />
              </a-form-item>
            </a-descriptions-item>
            
            <a-descriptions-item>
              <template #label><span class="required-field">图片</span></template>
              <a-form-item field="image" hide-label :rules="[{ required: true, message: '请上传商标图片' }]">
                <ma-upload
                  v-model="formData.image"
                  type="image"
                  :multiple="false"
                  :limit="1"
                  accept=".jpg,.jpeg,.gif,.png,.svg,.bmp"
                  title="上传图片"
                  :disabled="isDetailMode"
                  returnType="url"
                />
              </a-form-item>
            </a-descriptions-item>
            <a-descriptions-item>
              <template #label><span class="required-field">申请人名称</span></template>
              <a-form-item field="ownerName" hide-label :rules="[{ required: true, message: '请输入申请人名称' }]">
                <a-input v-model="formData.ownerName" placeholder="请输入申请人名称" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>
            
            <a-descriptions-item>
              <template #label><span class="required-field">申请人地址</span></template>
              <a-form-item field="ownerAddress" hide-label :rules="[{ required: true, message: '请输入申请人地址' }]"> 
                <a-input v-model="formData.ownerAddress" placeholder="请输入申请人地址" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>
            <a-descriptions-item label="附件列表">
              <a-form-item field="brandImages" hide-label>
                <ma-upload
                  v-model="formData.brandImages"
                  type="file"
                  :multiple="true"
                  :limit="5"
                  accept=".jpg,.jpeg,.gif,.png,.svg,.bmp,.doc,.docx,.pdf,.xls,.xlsx"
                  title="上传附件"
                  tip="支持图片、Word、Excel、PDF等格式文件"
                  :disabled="isDetailMode"
                  returnType="url"
                />
              </a-form-item>
            </a-descriptions-item>

            <a-descriptions-item>
              <template #label><span class="required-field">专用权期限</span></template>
              <a-form-item field="exclusiveDateLimit" hide-label :rules="[{ required: true, message: '请输入专用权期限' }]">
                <a-input v-model="formData.exclusiveDateLimit" placeholder="请输入专用权期限" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>
          
          <!-- 类别信息 -->
              <a-descriptions-item :span="2">
              <template #label><span class="required-field">类别</span></template>
              <a-form-item field="classId" hide-label :rules="[{ required: true, message: '请输入类别信息' }]">
                <div class="flex items-center">
                  <span class="mr-2">第</span>
                  <a-input v-model="formData.classId" placeholder=""  allow-clear class="classId_input" :disabled="isDetailMode"/>
                  <span class="mx-2">类</span>-
                  <a-input style="margin-left: 4px;" v-model="formData.className"  allow-clear class="classId_input" :disabled="isDetailMode"/>
                </div>
              </a-form-item>
            </a-descriptions-item>
            
            <a-descriptions-item>
              <template #label><span class="required-field">代理组织机构</span></template>
              <a-form-item field="agency" hide-label :rules="[{ required: true, message: '请输入代理组织机构' }]">
                <a-input v-model="formData.agency" placeholder="请输入代理组织机构" allow-clear style="width: 100%" :disabled="isDetailMode" />
              </a-form-item>
            </a-descriptions-item>

        
          <!-- 商品服务和公告信息 -->
            <a-descriptions-item :span="2">
              <template #label><span class="required-field">商品/服务列表</span></template>
              <a-form-item field="goodsList" hide-label :rules="[{ required: true, message: '请添加商品/服务列表' }]">
                <div class="goods-list-container">
                  <div v-for="(item, index) in formData.goodsList" :key="index" class="goods-item mb-2 flex items-center">
                    <a-input v-model="item.goodsCode" placeholder="商标小类" class="mr-2" style="width: 120px" :disabled="isDetailMode" />
                    <a-input v-model="item.goodsName" placeholder="商品/服务名称" class="flex-1" :disabled="isDetailMode" />
                    <a-button v-if="!isDetailMode" type="text" status="danger" class="ml-2" @click="removeGoodsItem(index)">
                      <template #icon><icon-delete /></template>
                      删除
                    </a-button>
                  </div>
                  <a-button v-if="!isDetailMode" type="primary" status="success" class="mt-2" @click="addGoodsItem">
                    <template #icon><icon-plus /></template>
                    添加
                  </a-button>
                </div>
              </a-form-item>
            </a-descriptions-item>
            
            <a-descriptions-item label="商标公告" :span="2">
              <a-form-item field="issuesList" hide-label>
                <div class="issues-list-container">
                  <div v-for="(item, index) in formData.issuesList" :key="index" class="issues-item mb-2 flex items-center">
                    <a-date-picker
                      v-model="item.paaDate"
                      class="mr-2"
                      style="width: 160px"
                      format="YYYY-MM-DD"
                      allow-clear
                      :disabled="isDetailMode"
                    />
                    <a-input v-model="item.issueName" placeholder="商标公告内容" class="flex-1" :disabled="isDetailMode" />
                    <a-button v-if="!isDetailMode" type="text" status="danger" class="ml-2" @click="removeIssueItem(index)">
                      <template #icon><icon-delete /></template>
                      删除
                    </a-button>
                  </div>
                  <a-button v-if="!isDetailMode" type="primary" status="success" class="mt-2" @click="addIssueItem">
                    <template #icon><icon-plus /></template>
                    添加
                  </a-button>
                </div>
              </a-form-item>
            </a-descriptions-item>
          </a-descriptions>
        </div>

        <!-- 表单按钮 -->
        <div class="flex justify-center">
          <a-space>
            <a-button v-if="!isDetailMode" :loading="formLoading" type="primary" @click="handleSubmit">提交</a-button>
            <a-button v-if="!isDetailMode" @click="handleReset">重置</a-button>
            <a-button @click="handleCancel">返回</a-button>
          </a-space>
        </div>
      </a-form>
    </a-card>
</template>
  
  <script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter, useRoute } from "vue-router";
import securityApi from '@/api/master/security';

definePageMeta({
  name: "master-addSecurity",
  path: "/master/securityManage/addSecurity/:id"
});

const router = useRouter();
const route = useRoute();
const formLoading = ref(false);
// 判断是否为详情模式
const isDetailMode = ref(false);

// 表单数据
const formData = reactive({
  name: "", // 商标名称
  image: "", // logo图片url
  ownerName: "", // 申请人
  ownerAddress: "", // 申请人地址
  agency: "", // 代理组织机构
  registerId: "", // 注册id
  registrationType: "", // 商标类型
  preAnnDate: "", // 初审公告日期
  applyDate: "", // 申请日期
  regAnnDate: "", // 注册公告日期
  lastProcedureStatus: "", // 当前状态
  productDesc: "", // 商品服务列表（兼容旧数据）
  exclusiveDateLimit: "", // 专用权期限
  issues: "", // 商标公告
  status: 1, // 状态（1-启用，0-禁用）
  classId: "", // 商标类型id
  className: "", // 商标类名
  trademarkCode: "", // 品牌代码
  sourceCodeRule: "", // 溯源码规则
  brandLocation: "", // 品牌所属地
  brandImages: [], // 附件列表
  goodsList: [], // 商品/服务列表（新格式）
  issuesList: [] // 商标公告列表
});

// 注册上MA-UPLOAD
import MaUpload from '~/components/base/ma-upload/index.vue'

// 表单引用
const formRef = ref(null);

// 提交表单
const handleSubmit = async e => {
  // 防止默认提交行为
  e?.preventDefault();
  
  try {
    // 先进行表单验证
    const validResult = await formRef.value.validate();
    console.log('表单验证结果:', formData);
    
    // 如果验证不通过，直接返回
    if (validResult) {
      Message.error('请填写必填项');
      return;
    }
    
    formLoading.value = true;
    
    // 准备提交数据
    const submitData = { ...formData };
    
    // 处理image字段，如果为null则设置为空字符串
    if (submitData.image === null) {
      submitData.image = '';
    }
    
    // 处理日期为时间戳格式
    if (submitData.applyDate) {
      submitData.applyDate = new Date(submitData.applyDate).getTime();
    }
    if (submitData.preAnnDate) {
      submitData.preAnnDate = new Date(submitData.preAnnDate).getTime();
    }
    if (submitData.regAnnDate) {
      submitData.regAnnDate = new Date(submitData.regAnnDate).getTime();
    }
    
    // 获取路由参数中的ID
    const id = route.params.id;
    
    // 处理商品列表数据
    if (submitData.goodsList && submitData.goodsList.length > 0) {
      // 将商品列表转换为JSON字符串格式
      submitData.productDesc = JSON.stringify(submitData.goodsList);
      // 删除临时字段，避免影响提交
      delete submitData.goodsList;
    }
    
    // 处理商标公告列表数据
    if (submitData.issuesList && submitData.issuesList.length > 0) {
      // 将商标公告列表转换为 JSON 字符串
      submitData.issues = JSON.stringify(submitData.issuesList);
      // 删除临时字段，避免影响提交
      delete submitData.issuesList;
    }
    
    // 创建提交数据的副本，保持小驼峰格式
    const apiData = { ...submitData };
    
    // 处理品牌图片数组，标准化格式
    if (apiData.brandImages && Array.isArray(apiData.brandImages)) {
      // 处理数组中的每个元素，确保都是字符串格式
      apiData.brandImages = apiData.brandImages.map(item => {
        // 如果是对象并且有url属性，提取url值
        if (item && typeof item === 'object' && item.url) {
          return item.url;
        }
        // 如果已经是字符串，直接返回
        if (typeof item === 'string') {
          return item;
        }
        // 其他情况返回空字符串
        return '';
      }).filter(url => url); // 过滤掉空值
    }
    
    console.log('准备提交的数据:', apiData);
    console.log('附件数组格式:', apiData.brandImages);
    if (id && id !== 'add') { // add是默认的添加ID
      // 编辑模式
      const res = await securityApi.updateBrand(id, apiData);
      if (res.code == 200) {
        Message.success('更新成功');
        // 跳转回列表页
        router.push('/master/securityManage/securityList');
      } else {
        Message.error(res.message || '更新失败');
      }
    } else {
      // 新增模式
      console.log('新增商标', apiData);
      const res = await securityApi.createBrand(apiData);
      if (res.code == 200) {
        Message.success('添加成功');
        // 跳转回列表页
        router.push('/master/securityManage/securityList');
      } else {
        Message.error(res.message || '添加失败');
      }
    }
  } catch (error) {
    Message.error('操作失败: ' + (error.message || '未知错误'));
    console.error('操作失败:', error);
  } finally {
    formLoading.value = false;
  }
};

// 添加商品项
const addGoodsItem = () => {
  formData.goodsList.push({
    goodsCode: '',
    goodsName: ''
  });
};

// 删除商品项
const removeGoodsItem = (index) => {
  formData.goodsList.splice(index, 1);
};

// 添加商标公告项
const addIssueItem = () => {
  formData.issuesList.push({
    paaDate: '',
    issueName: ''
  });
};

// 删除商标公告项
const removeIssueItem = (index) => {
  formData.issuesList.splice(index, 1);
};

// 重置表单
const handleReset = () => {
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    if (key === 'status') {
      formData[key] = 1;
    } else if (key === 'brandImages' || key === 'goodsList') {
      formData[key] = [];
    } else {
      formData[key] = '';
    }
  });

  // 显示成功提示
  Message.success('表单已重置');
};

// 取消操作
const handleCancel = () => {
  // 返回列表页
  router.push("/master/securityManage/securityList");
};


const getDetail = async (id) => {
  formLoading.value = true;
  securityApi.getBrandDetail(id).then(res => {
    // 直接使用接口返回的小驼峰格式数据
    // 处理基本字段
    Object.keys(formData).forEach(key => {
      if (res.data[key] !== undefined) {
        formData[key] = res.data[key];
      }
    });
    
    // 处理日期字段格式 - 处理 13 位时间戳
    if (res.data.applyDate) {
      // 将字符串转换为数字并创建 Date 对象
      formData.applyDate = new Date(parseInt(res.data.applyDate));
    }
    if (res.data.preAnnDate) {
      formData.preAnnDate = new Date(parseInt(res.data.preAnnDate));
    }
    if (res.data.regAnnDate) {
      formData.regAnnDate = new Date(parseInt(res.data.regAnnDate));
    }
    
    // 处理商品列表数据
    if (res.data.productDesc) {
      try {
        // 尝试解析JSON格式
        const productData = JSON.parse(res.data.productDesc);
        formData.goodsList = productData.map(item => ({
          goodsCode: item.goodsCode,
          goodsName: item.goodsName
        }));
      } catch (e) {
        // 如果解析失败，尝试旧格式处理
        const lines = res.data.productDesc.split('\n').filter(line => line.trim());
        formData.goodsList = lines.map(line => {
          const parts = line.split(':');
          return {
            goodsCode: parts[0] ? parts[0].trim() : '',
            goodsName: parts[1] ? parts[1].trim() : line.trim()
          };
        });
      }
    }
    
    // 处理商标公告数据
    if (res.data.issues) {
      try {
        // 尝试解析 JSON 数据
        const issuesData = JSON.parse(res.data.issues);
        formData.issuesList = issuesData.map(item => ({
          // 商标公告中的日期是字符串格式，直接使用
          paaDate: item.paaDate,
          issueName: item.issueName
        }));
      } catch (e) {
        // 如果解析失败，创建空数组
        formData.issuesList = [];
      }
    }
    
    // 处理品牌图片数组
    if (res.data.brandImages) {
      if (Array.isArray(res.data.brandImages)) {
        // 如果是对象数组格式，需要提取 brandImage 属性
        if (typeof res.data.brandImages[0] === 'object' && res.data.brandImages[0].brandImage) {
          formData.brandImages = res.data.brandImages.map(item => item.brandImage);
        } else {
          // 如果已经是字符串数组格式，直接使用
          formData.brandImages = res.data.brandImages;
        }
      } else if (typeof res.data.brandImages === 'string') {
        // 如果是 JSON 字符串，尝试解析
        try {
          const parsedImages = JSON.parse(res.data.brandImages);
          if (Array.isArray(parsedImages)) {
            formData.brandImages = parsedImages;
          }
        } catch (e) {
          console.error('解析 brandImages 失败:', e);
          formData.brandImages = [];
        }
      }
    }
    
    // 如果商品列表为空，至少添加一个空项
    if (formData.goodsList.length === 0) {
      addGoodsItem();
    }
    
    // 如果商标公告列表为空，至少添加一个空项
    if (formData.issuesList.length === 0) {
      addIssueItem();
    }
    
    console.log('表单数据加载完成:', formData);
    formLoading.value = false;
  }).catch((error) => {
    console.error('获取详情失败:', error);
    formLoading.value = false;
  });
}
// 初始化
onMounted(() => {
  // 获取商标详情
  const id = route.params.id;
  
  // 判断是否为详情模式
  if (id && id.toString().startsWith('detail')) {
    isDetailMode.value = true;
    // 从路由参数中提取实际ID（即去掉detail前缀的部分）
    const realId = id.toString().replace('detail', '');
    getDetail(realId);
  } else if (id && id !== 'add') { // add是默认的添加ID
    getDetail(id);
  } else {
    // 新增模式，默认添加一个空商品项和空商标公告项
    addGoodsItem();
    addIssueItem();
  }
});
</script>
  
  <style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}

.general-card {
  margin-bottom: 16px;
}

.descriptions-with-border {
  border: 1px solid #e5e6eb;
  border-radius: 2px;
}

.descriptions-with-border :deep(.arco-descriptions-item-label),
.descriptions-with-border :deep(.arco-descriptions-item-value) {
  position: relative;
  padding: 12px 16px;
}

.descriptions-with-border :deep(.arco-descriptions-item-label)::after,
.descriptions-with-border :deep(.arco-descriptions-item-value)::after {
  content: '';
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 1px;
  background-color: #e5e6eb;
}

.descriptions-with-border :deep(.arco-descriptions-item-label)::before,
.descriptions-with-border :deep(.arco-descriptions-item-value)::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 1px;
  background-color: #e5e6eb;
}

.descriptions-with-border :deep(.arco-descriptions-item-label) {
  background-color: #f7f8fa;
  font-weight: 500;
}

/* 红色星号样式，用于必填项 */
.required-field::before {
  content: '*';
  color: #f53f3f;
  margin-right: 4px;
}

</style>