<!--
 - 客户管理模块 - 客户详情页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-0">
    <div class="p-4 mb-4 flex justify-between items-center bg-white">
      <div class="text-xl font-bold">客户详情</div>
      <a-button type="primary" @click="goBack">
        <template #icon><icon-arrow-left /></template>
        返回列表
      </a-button>
    </div>

    <a-spin :loading="loading">
      <a-row :gutter="16" class="px-4">
        <!-- 回访记录卡片 -->
        <a-col :span="8">
          <a-card class="mb-4">
            <template #title>
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <icon-message class="mr-2" />回访记录
                </div>
                <a-button type="primary" size="small" @click="showCommunicationModal">
                  <template #icon><icon-plus /></template>
                  添加记录
                </a-button>
              </div>
            </template>
            <a-timeline>
              <a-timeline-item v-for="(item, index) in communicationData.slice(0, 3)" :key="index" :label="item.time">
                <div class="font-bold">{{ item.type }}</div>
                <div>{{ item.content }}</div>
                <div class="text-gray-500 text-sm">操作人: {{ item.operator }}</div>
              </a-timeline-item>
              <a-timeline-item v-if="communicationData.length > 3">
                <a class="text-blue-500 cursor-pointer" @click="showAllCommunicationModal">查看更多记录...</a>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>

        <!-- 售后及投诉卡片 -->
        <a-col :span="8">
          <a-card class="mb-4">
            <template #title>
              <div class="flex items-center">
                <icon-exclamation-circle class="mr-2" />售后及投诉
              </div>
            </template>
            <a-row :gutter="16">
              <a-col :span="12">
                <a-card class="cursor-pointer hover:shadow-md" @click="showAfterSalesModal">
                  <div class="text-center">
                    <div class="text-3xl font-bold mb-2">{{ afterSalesData.length }}</div>
                    <div>售后条数</div>
                  </div>
                </a-card>
              </a-col>
              <a-col :span="12">
                <a-card class="cursor-pointer hover:shadow-md" @click="showComplaintsModal">
                  <div class="text-center">
                    <div class="text-3xl font-bold mb-2">{{ complaintsData.length }}</div>
                    <div>投诉条数</div>
                  </div>
                </a-card>
              </a-col>
            </a-row>
          </a-card>
        </a-col>

        <!-- 数据信息卡片 - 不可编辑 -->
        <a-col :span="8">
          <a-card class="mb-4">
            <template #title>
              <div class="flex items-center">
                <icon-file class="mr-2" />数据信息
              </div>
            </template>
            <a-descriptions :data="dataInfoDesc" :column="1" />
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="16" class="px-4">
        <!-- 基本信息卡片 - 可编辑 -->
        <a-col :span="8">
          <a-card class="mb-4">
            <template #title>
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <icon-user class="mr-2" />基本信息
                </div>
                <a-button type="text" @click="toggleBasicInfoEdit">
                  {{ isBasicInfoEditing ? '保存' : '编辑' }}
                </a-button>
              </div>
            </template>
            
            <div v-if="isBasicInfoEditing">
              <MaForm ref="basicInfoFormRef" v-model="basicInfoForm" :columns="basicInfoColumns" />
            </div>
            <div v-else>
              <a-descriptions :data="basicInfoDesc" :column="1" />
            </div>
          </a-card>
        </a-col>

        <!-- 客户标签卡片 - 可编辑 -->
        <a-col :span="8">
          <a-card class="mb-4">
            <template #title>
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <icon-tag class="mr-2" />客户标签
                </div>
                <a-button type="text" @click="toggleTagsEdit">
                  {{ isTagsEditing ? '保存' : '编辑' }}
                </a-button>
              </div>
            </template>
            
            <div v-if="isTagsEditing">
              <MaForm ref="tagsFormRef" v-model="tagsForm" :columns="tagsColumns" />
            </div>
            <div v-else>
              <a-descriptions :data="tagsDesc" :column="1" />
            </div>
          </a-card>
        </a-col>

        <!-- 沟通情况卡片 - 可编辑 -->
        <a-col :span="8">
          <a-card class="mb-4">
            <template #title>
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <icon-message class="mr-2" />沟通情况
                </div>
                <a-button type="text" @click="toggleCommunicationEdit">
                  {{ isCommunicationEditing ? '保存' : '编辑' }}
                </a-button>
              </div>
            </template>
            
            <div v-if="isCommunicationEditing">
              <MaForm ref="communicationFormRef" v-model="communicationForm" :columns="communicationColumns" />
            </div>
            <div v-else>
              <a-descriptions :data="communicationDesc" :column="1" />
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 订单记录表格 -->
      <div class="px-4 mb-4">
        <a-card>
          <template #title>
            <div class="flex items-center">
              <icon-shopping-bag class="mr-2" />订单记录
            </div>
          </template>
          <a-table :columns="orderColumns" :data="orderData" :pagination="{ pageSize: 5 }" />
        </a-card>
      </div>
    </a-spin>

    <!-- 添加沟通记录弹窗 -->
    <a-modal v-model:visible="communicationVisible" title="添加沟通记录" @cancel="communicationVisible = false" @before-ok="handleAddCommunication">
      <MaForm ref="communicationModalFormRef" v-model="communicationModalForm" :columns="communicationModalColumns" />
    </a-modal>

    <!-- 所有沟通记录弹窗 -->
    <a-modal v-model:visible="allCommunicationVisible" title="所有沟通记录" @cancel="allCommunicationVisible = false" width="800">
      <a-table :columns="communicationTableColumns" :data="communicationData" :pagination="{ pageSize: 10 }" />
    </a-modal>

    <!-- 售后记录弹窗 -->
    <a-modal v-model:visible="afterSalesVisible" title="售后记录" @cancel="afterSalesVisible = false" width="800">
      <a-table :columns="afterSalesColumns" :data="afterSalesData" :pagination="{ pageSize: 5 }" />
    </a-modal>

    <!-- 投诉记录弹窗 -->
    <a-modal v-model:visible="complaintsVisible" title="投诉记录" @cancel="complaintsVisible = false" width="800">
      <a-table :columns="complaintsColumns" :data="complaintsData" :pagination="{ pageSize: 5 }" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, h } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter, useRoute } from 'vue-router';
import MaForm from '~/components/base/ma-form/index.vue';

definePageMeta({
  name: 'master-customer-detail',
  path: '/master/customer/detail/:id'
});

const router = useRouter();
const route = useRoute();
const loading = ref(true);
const customerId = ref(null);
const customerData = ref(null);

// 编辑状态控制
const isBasicInfoEditing = ref(false);
const isTagsEditing = ref(false);
const isCommunicationEditing = ref(false);

// 弹窗控制
const communicationVisible = ref(false);
const allCommunicationVisible = ref(false);
const afterSalesVisible = ref(false);
const complaintsVisible = ref(false);

// 基本信息表单
const basicInfoFormRef = ref();
const basicInfoForm = reactive({
  city_linkage: { province: '', city: '', area: '' },
  address_detail: '',
  recipient_name: '',
  recipient_phone: '',
});

// 客户标签表单
const tagsFormRef = ref();
const tagsForm = reactive({
  gender: '',
  age_group: '',
  marital_status: '',
  level: '',
  department: '',
  annual_budget: '',
  purchase_frequency: '',
  customer_level: ''
});

// 沟通情况表单
const communicationFormRef = ref();
const communicationForm = reactive({
  preferred_communication: '',
  convenient_time: '',
  shopping_preference: '',
  price_sensitivity: '',
  purchasing_platform: []
});

// 沟通记录相关
const communicationModalFormRef = ref();
const communicationModalForm = reactive({
  type: '',
  content: '',
});

// 模拟数据
const mockCustomers = [
  {
    id: 1,
    name: "北京科技有限公司",
    phone: "13800138000",
    email: "<EMAIL>",
    address: "北京市 海淀区 中关村",
    address_detail: "中关村科技园区8号楼501室",
    group_id: 1,
    group_name: "VIP客户",
    source: "官网",
    status: 1,
    remark: "重要客户，需重点关注",
    created_at: "2025-03-14 07:30:04",
    contact_person: "张经理",
    recipient_name: "张经理",
    recipient_phone: "13800138000",
    contact_position: "采购总监",
    industry: "互联网/IT",
    registered_capital: "1000万",
    annual_revenue: "1000-5000万",
    employee_count: "50-100人",
    follow_person: "王小明",
    customer_level: "A级",
    // 客户标签
    gender: "男",
    age_group: "80后",
    marital_status: "已婚",
    level: "高级",
    department: "信息技术部",
    annual_budget: "500万",
    purchase_frequency: "高频",
    // 沟通情况
    preferred_communication: "微信",
    convenient_time: "工作日上午",
    shopping_preference: "电子设备、办公用品",
    price_sensitivity: "低",
    purchasing_platform: ["京东", "招标"],
    // 数据信息
    platform_amount: "568000.00",
    platform_orders: "32",
    natural_amount: "320000.00",
    natural_orders: "18",
    reported_amount: "248000.00",
    reported_orders: "14",
    purchased_categories: "电脑设备、办公用品、网络设备"
  },
  {
    id: 2,
    name: "上海贸易有限公司",
    phone: "13900139000",
    email: "<EMAIL>",
    address: "上海市 浦东新区 陆家嘴",
    address_detail: "陆家嘴金融中心2号楼1203室",
    group_id: 2,
    group_name: "普通客户",
    source: "电话营销",
    status: 1,
    remark: "",
    created_at: "2025-03-20 06:53:53",
    contact_person: "李总",
    recipient_name: "李总",
    recipient_phone: "13900139000",
    contact_position: "总经理",
    industry: "贸易/批发",
    registered_capital: "500万",
    annual_revenue: "1000-5000万",
    employee_count: "50-100人",
    follow_person: "李梅",
    customer_level: "B级",
    // 客户标签
    gender: "男",
    age_group: "70后",
    marital_status: "已婚",
    level: "VIP",
    department: "采购部",
    annual_budget: "300万",
    purchase_frequency: "中频",
    // 沟通情况
    preferred_communication: "电话",
    convenient_time: "工作日下午",
    shopping_preference: "办公耗材、办公家具",
    price_sensitivity: "中",
    purchasing_platform: ["京东", "军网"],
    // 数据信息
    platform_amount: "320000.00",
    platform_orders: "24",
    natural_amount: "180000.00",
    natural_orders: "15",
    reported_amount: "140000.00",
    reported_orders: "9",
    purchased_categories: "办公家具、办公耗材、电脑配件"
  },
  {
    id: 3,
    name: "广州电子科技公司",
    phone: "13700137000",
    email: "<EMAIL>",
    address: "广州市 天河区 高新区",
    address_detail: "高新技术产业开发区科技路12号",
    group_id: 3,
    group_name: "潜在客户",
    source: "社交媒体",
    status: 2,
    remark: "需跟进",
    created_at: "2025-04-01 10:15:22",
    contact_person: "王工",
    recipient_name: "王工",
    recipient_phone: "13700137000",
    contact_position: "技术总监",
    industry: "电子/半导体",
    registered_capital: "300万",
    annual_revenue: "500-1000万",
    employee_count: "10-50人",
    follow_person: "张伟",
    customer_level: "C级",
    // 客户标签
    gender: "男",
    age_group: "90后",
    marital_status: "未婚",
    level: "普通",
    department: "研发部",
    annual_budget: "150万",
    purchase_frequency: "低频",
    // 沟通情况
    preferred_communication: "短信",
    convenient_time: "周末",
    shopping_preference: "电子元器件、测试设备",
    price_sensitivity: "高",
    purchasing_platform: ["招标"],
    // 数据信息
    platform_amount: "120000.00",
    platform_orders: "8",
    natural_amount: "80000.00",
    natural_orders: "5",
    reported_amount: "40000.00",
    reported_orders: "3",
    purchased_categories: "电子元器件、测试设备、实验室耗材"
  }
];

// 模拟沟通记录数据
const communicationData = ref([
  {
    time: '2025-04-15 10:30',
    type: '电话沟通',
    content: '讨论了新产品需求，客户表示有意向采购',
    operator: '销售专员-王小明'
  },
  {
    time: '2025-04-10 14:15',
    type: '邮件沟通',
    content: '发送了产品报价单，等待客户确认',
    operator: '销售经理-李梅'
  },
  {
    time: '2025-04-05 09:00',
    type: '现场拜访',
    content: '拜访客户总部，进行了产品演示，反馈良好',
    operator: '销售总监-张伟'
  }
]);

// 模拟售后记录数据
const afterSalesData = ref([
  {
    id: 1001,
    order_no: 'DD202504050001',
    type: '退换货',
    content: '产品包装破损，申请更换',
    status: '已处理',
    created_at: '2025-04-05 14:30:22',
    handler: '售后专员-赵丽'
  },
  {
    id: 1002,
    order_no: 'DD202503200003',
    type: '维修',
    content: '设备故障，需要维修',
    status: '处理中',
    created_at: '2025-03-25 09:15:30',
    handler: '技术支持-刘强'
  }
]);

// 模拟投诉记录数据
const complaintsData = ref([
  {
    id: 2001,
    order_no: 'DD202503150002',
    content: '送货时间延误，影响了使用',
    level: '一般',
    status: '已解决',
    created_at: '2025-03-16 11:20:15',
    handler: '客服主管-张明'
  }
]);

// 售后记录表格列定义
const afterSalesColumns = [
  {
    title: '工单编号',
    dataIndex: 'id',
  },
  {
    title: '关联订单',
    dataIndex: 'order_no',
  },
  {
    title: '类型',
    dataIndex: 'type',
  },
  {
    title: '内容',
    dataIndex: 'content',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
  },
  {
    title: '处理人',
    dataIndex: 'handler',
  }
];

// 投诉记录表格列定义
const complaintsColumns = [
  {
    title: '投诉编号',
    dataIndex: 'id',
  },
  {
    title: '关联订单',
    dataIndex: 'order_no',
  },
  {
    title: '投诉内容',
    dataIndex: 'content',
  },
  {
    title: '投诉等级',
    dataIndex: 'level',
  },
  {
    title: '状态',
    dataIndex: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
  },
  {
    title: '处理人',
    dataIndex: 'handler',
  }
];

// 基本信息表单配置
const basicInfoColumns = reactive([
  {
    dataIndex: 'city_linkage',
    title: '省市区',
    labelWidth: '100px',
    rules: [{ required: true, message: '省市区必选' }],
    formType: 'city-linkage',
    type: 'select',
    mode: 'name'
  },
  {
    dataIndex: 'address_detail',
    title: '收货地址',
    labelWidth: '100px',
    rules: [{ required: true, message: '收货地址必填' }],
    formType: 'textarea',
    placeholder: '请输入详细收货地址'
  },
  {
    dataIndex: 'recipient_name',
    title: '联系人',
    labelWidth: '100px',
    rules: [{ required: true, message: '联系人必填' }],
    formType: 'input',
    placeholder: '请输入联系人姓名'
  },
  {
    dataIndex: 'recipient_phone',
    title: '电话',
    labelWidth: '100px',
    rules: [
      { required: true, message: '联系电话必填' },
      { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
    ],
    formType: 'input',
    placeholder: '请输入联系电话'
  }
]);

// 客户标签表单配置
const tagsColumns = reactive([
  {
    dataIndex: 'gender',
    title: '性别',
    labelWidth: '100px',
    formType: 'select',
    dict: {
      data: [
        { label: '男', value: '男' },
        { label: '女', value: '女' }
      ]
    },
    placeholder: '请选择性别'
  },
  {
    dataIndex: 'age_group',
    title: '年龄',
    labelWidth: '100px',
    formType: 'select',
    dict: {
      data: [
        { label: '00后', value: '00后' },
        { label: '90后', value: '90后' },
        { label: '80后', value: '80后' },
        { label: '70后', value: '70后' },
        { label: '60后', value: '60后' },
        { label: '50后', value: '50后' }
      ]
    },
    placeholder: '请选择年龄段'
  },
  {
    dataIndex: 'marital_status',
    title: '婚姻状况',
    labelWidth: '100px',
    formType: 'select',
    dict: {
      data: [
        { label: '已婚', value: '已婚' },
        { label: '未婚', value: '未婚' }
      ]
    },
    placeholder: '请选择婚姻状况'
  },
  {
    dataIndex: 'level',
    title: '级别',
    labelWidth: '100px',
    formType: 'select',
    dict: {
      data: [
        { label: '普通', value: '普通' },
        { label: '高级', value: '高级' },
        { label: 'VIP', value: 'VIP' }
      ]
    },
    placeholder: '请选择级别'
  },
  {
    dataIndex: 'department',
    title: '科室',
    labelWidth: '100px',
    formType: 'input',
    placeholder: '请输入科室'
  },
  {
    dataIndex: 'annual_budget',
    title: '年度预算',
    labelWidth: '100px',
    formType: 'input',
    placeholder: '请输入年度预算'
  },
  {
    dataIndex: 'purchase_frequency',
    title: '购买频率',
    labelWidth: '100px',
    formType: 'select',
    dict: {
      data: [
        { label: '高频', value: '高频' },
        { label: '中频', value: '中频' },
        { label: '低频', value: '低频' }
      ]
    },
    placeholder: '请选择购买频率'
  },
  {
    dataIndex: 'customer_level',
    title: '客户等级',
    labelWidth: '100px',
    formType: 'select',
    dict: {
      data: [
        { label: 'A级', value: 'A级' },
        { label: 'B级', value: 'B级' },
        { label: 'C级', value: 'C级' },
        { label: 'D级', value: 'D级' }
      ]
    },
    placeholder: '请选择客户等级'
  }
]);

// 沟通情况表单配置
const communicationColumns = reactive([
  {
    dataIndex: 'preferred_communication',
    title: '沟通偏好方式',
    labelWidth: '120px',
    formType: 'select',
    dict: {
      data: [
        { label: '微信', value: '微信' },
        { label: '电话', value: '电话' },
        { label: '短信', value: '短信' },
        { label: '其他', value: '其他' }
      ]
    },
    placeholder: '请选择沟通偏好方式'
  },
  {
    dataIndex: 'convenient_time',
    title: '交流方便时间',
    labelWidth: '120px',
    formType: 'picker',
    placeholder: '请选择交流方便时间'
  },
  {
    dataIndex: 'shopping_preference',
    title: '购物偏好品类',
    labelWidth: '120px',
    formType: 'input',
    placeholder: '请输入购物偏好品类'
  },
  {
    dataIndex: 'price_sensitivity',
    title: '价格敏感度',
    labelWidth: '120px',
    formType: 'select',
    dict: {
      data: [
        { label: '高', value: '高' },
        { label: '中', value: '中' },
        { label: '低', value: '低' }
      ]
    },
    placeholder: '请选择价格敏感度'
  },
  {
    dataIndex: 'purchasing_platform',
    title: '采购平台',
    labelWidth: '120px',
    formType: 'checkbox',
    dict: {
      data: [
        { label: '京东', value: '京东' },
        { label: '军网', value: '军网' },
        { label: '招标', value: '招标' }
      ]
    }
  }
]);

// 沟通记录弹窗表单配置
const communicationModalColumns = reactive([
  {
    dataIndex: 'type',
    title: '沟通类型',
    labelWidth: '80px',
    rules: [{ required: true, message: '沟通类型必选' }],
    formType: 'select',
    dict: {
      data: [
        { label: '电话沟通', value: '电话沟通' },
        { label: '邮件沟通', value: '邮件沟通' },
        { label: '现场拜访', value: '现场拜访' },
        { label: '线上会议', value: '线上会议' },
        { label: '其他', value: '其他' }
      ]
    },
    placeholder: '请选择沟通类型'
  },
  {
    dataIndex: 'content',
    title: '沟通内容',
    labelWidth: '80px',
    rules: [{ required: true, message: '沟通内容必填' }],
    formType: 'textarea',
    placeholder: '请输入沟通内容'
  }
]);

// 沟通记录表格列定义
const communicationTableColumns = [
  {
    title: '时间',
    dataIndex: 'time',
  },
  {
    title: '类型',
    dataIndex: 'type',
  },
  {
    title: '内容',
    dataIndex: 'content',
  },
  {
    title: '操作人',
    dataIndex: 'operator',
  }
];

// 基本信息描述列表
const basicInfoDesc = computed(() => {
  if (!customerData.value) return [];
  
  // 处理地址信息
  const addressParts = customerData.value.address ? customerData.value.address.split(' ') : ['', '', ''];
  const province = addressParts[0] || '';
  const city = addressParts[1] || '';
  const area = addressParts[2] || '';
  
  return [
    {
      label: '客户名称',
      value: customerData.value.name
    },
    {
      label: '省份',
      value: province
    },
    {
      label: '城市',
      value: city
    },
    {
      label: '区县',
      value: area
    },
    {
      label: '收货地址',
      value: customerData.value.address_detail || customerData.value.address
    },
    {
      label: '联系人',
      value: customerData.value.recipient_name || customerData.value.contact_person
    },
    {
      label: '电话',
      value: customerData.value.recipient_phone || customerData.value.phone
    }
  ];
});

// 客户标签描述列表
const tagsDesc = computed(() => {
  if (!customerData.value) return [];
  
  return [
    {
      label: '性别',
      value: customerData.value.gender || '未设置'
    },
    {
      label: '年龄',
      value: customerData.value.age_group || '未设置'
    },
    {
      label: '婚姻状况',
      value: customerData.value.marital_status || '未设置'
    },
    {
      label: '级别',
      value: customerData.value.level || '未设置'
    },
    {
      label: '科室',
      value: customerData.value.department || '未设置'
    },
    {
      label: '年度预算',
      value: customerData.value.annual_budget || '未设置'
    },
    {
      label: '购买频率',
      value: customerData.value.purchase_frequency || '未设置'
    },
    {
      label: '客户等级',
      value: customerData.value.customer_level || '未设置'
    }
  ];
});

// 沟通情况描述列表
const communicationDesc = computed(() => {
  if (!customerData.value) return [];
  
  return [
    {
      label: '沟通偏好方式',
      value: customerData.value.preferred_communication || '未设置'
    },
    {
      label: '交流方便时间',
      value: customerData.value.convenient_time || '未设置'
    },
    {
      label: '购物偏好品类',
      value: customerData.value.shopping_preference || '未设置'
    },
    {
      label: '价格敏感度',
      value: customerData.value.price_sensitivity || '未设置'
    },
    {
      label: '采购平台',
      value: Array.isArray(customerData.value.purchasing_platform) 
        ? customerData.value.purchasing_platform.join(', ') 
        : (customerData.value.purchasing_platform || '未设置')
    }
  ];
});

// 数据信息描述列表
const dataInfoDesc = computed(() => {
  if (!customerData.value) return [];
  
  return [
    {
      label: '平台累计金额',
      value: h('a', { 
        onClick: () => showOrderHistoryModal('platform_amount'),
        class: 'text-blue-500 cursor-pointer'
      }, `¥${customerData.value.platform_amount || '0.00'}`)
    },
    {
      label: '平台累计单量',
      value: h('a', { 
        onClick: () => showOrderHistoryModal('platform_orders'),
        class: 'text-blue-500 cursor-pointer'
      }, customerData.value.platform_orders || '0')
    },
    {
      label: '自然单金额',
      value: h('a', { 
        onClick: () => showOrderHistoryModal('natural_amount'),
        class: 'text-blue-500 cursor-pointer'
      }, `¥${customerData.value.natural_amount || '0.00'}`)
    },
    {
      label: '自然单数量',
      value: h('a', { 
        onClick: () => showOrderHistoryModal('natural_orders'),
        class: 'text-blue-500 cursor-pointer'
      }, customerData.value.natural_orders || '0')
    },
    {
      label: '报备单金额',
      value: h('a', { 
        onClick: () => showOrderHistoryModal('reported_amount'),
        class: 'text-blue-500 cursor-pointer'
      }, `¥${customerData.value.reported_amount || '0.00'}`)
    },
    {
      label: '报备单数量',
      value: h('a', { 
        onClick: () => showOrderHistoryModal('reported_orders'),
        class: 'text-blue-500 cursor-pointer'
      }, customerData.value.reported_orders || '0')
    },
    {
      label: '采购品目',
      value: customerData.value.purchased_categories || '暂无数据'
    }
  ];
});

// 返回列表页
const goBack = () => {
  router.push('/master/customer/list');
};

// 切换基本信息编辑状态
const toggleBasicInfoEdit = async () => {
  if (isBasicInfoEditing.value) {
    // 保存编辑
    const valid = await basicInfoFormRef.value.validate();
    if (valid) return;
    
    // 处理地址信息
    const cityLinkage = basicInfoForm.city_linkage || {};
    const province = cityLinkage.province || '';
    const city = cityLinkage.city || '';
    const area = cityLinkage.area || '';
    const fullAddress = [province, city, area].filter(Boolean).join(' ');
    
    // 更新客户数据
    if (customerData.value) {
      customerData.value.address = fullAddress;
      customerData.value.address_detail = basicInfoForm.address_detail;
      customerData.value.recipient_name = basicInfoForm.recipient_name;
      customerData.value.recipient_phone = basicInfoForm.recipient_phone;
      
      // TODO: 调用API保存数据
      Message.success('基本信息保存成功');
    }
  } else {
    // 进入编辑模式，初始化表单数据
    if (customerData.value) {
      const addressParts = customerData.value.address ? customerData.value.address.split(' ') : ['', '', ''];
      
      basicInfoForm.city_linkage = {
        province: addressParts[0] || '',
        city: addressParts[1] || '',
        area: addressParts[2] || ''
      };
      basicInfoForm.address_detail = customerData.value.address_detail || '';
      basicInfoForm.recipient_name = customerData.value.recipient_name || customerData.value.contact_person || '';
      basicInfoForm.recipient_phone = customerData.value.recipient_phone || customerData.value.phone || '';
    }
  }
  
  isBasicInfoEditing.value = !isBasicInfoEditing.value;
};

// 切换客户标签编辑状态
const toggleTagsEdit = async () => {
  if (isTagsEditing.value) {
    // 保存编辑
    const valid = await tagsFormRef.value.validate();
    if (valid) return;
    
    // 更新客户数据
    if (customerData.value) {
      Object.assign(customerData.value, tagsForm);
      
      // TODO: 调用API保存数据
      Message.success('客户标签保存成功');
    }
  } else {
    // 进入编辑模式，初始化表单数据
    if (customerData.value) {
      Object.assign(tagsForm, {
        gender: customerData.value.gender || '',
        age_group: customerData.value.age_group || '',
        marital_status: customerData.value.marital_status || '',
        level: customerData.value.level || '',
        department: customerData.value.department || '',
        annual_budget: customerData.value.annual_budget || '',
        purchase_frequency: customerData.value.purchase_frequency || '',
        customer_level: customerData.value.customer_level || ''
      });
    }
  }
  
  isTagsEditing.value = !isTagsEditing.value;
};

// 切换沟通情况编辑状态
const toggleCommunicationEdit = async () => {
  if (isCommunicationEditing.value) {
    // 保存编辑
    const valid = await communicationFormRef.value.validate();
    if (valid) return;
    
    // 更新客户数据
    if (customerData.value) {
      Object.assign(customerData.value, communicationForm);
      
      // TODO: 调用API保存数据
      Message.success('沟通情况保存成功');
    }
  } else {
    // 进入编辑模式，初始化表单数据
    if (customerData.value) {
      Object.assign(communicationForm, {
        preferred_communication: customerData.value.preferred_communication || '',
        convenient_time: customerData.value.convenient_time || '',
        shopping_preference: customerData.value.shopping_preference || '',
        price_sensitivity: customerData.value.price_sensitivity || '',
        purchasing_platform: customerData.value.purchasing_platform || []
      });
    }
  }
  
  isCommunicationEditing.value = !isCommunicationEditing.value;
};

// 显示添加沟通记录弹窗
const showCommunicationModal = () => {
  communicationModalForm.type = '';
  communicationModalForm.content = '';
  communicationVisible.value = true;
};

// 显示售后记录弹窗
const showAfterSalesModal = () => {
  afterSalesVisible.value = true;
};

// 显示投诉记录弹窗
const showComplaintsModal = () => {
  complaintsVisible.value = true;
};

// 显示订单历史弹窗
const showOrderHistoryModal = (type) => {
  console.log('显示订单历史', type);
  // TODO: 实现订单历史弹窗
};

// 显示所有沟通记录弹窗
const showAllCommunicationModal = () => {
  allCommunicationVisible.value = true;
};

// 添加沟通记录
const handleAddCommunication = async (done) => {
  const valid = await communicationModalFormRef.value.validate();
  if (valid) return false;

  // 添加新的沟通记录
  communicationData.value.unshift({
    time: new Date().toLocaleString(),
    type: communicationModalForm.type,
    content: communicationModalForm.content,
    operator: '当前用户-系统管理员' // 实际应用中应该获取当前登录用户信息
  });

  Message.success('添加沟通记录成功');
  done();
};

// 获取客户详情
const fetchCustomerDetail = async () => {
  loading.value = true;
  try {
    // 实际应用中应该调用API获取客户详情
    // const response = await fetchCustomerById(customerId.value);
    // customerData.value = response.data;
    
    // 模拟API调用
    setTimeout(() => {
      customerData.value = mockCustomers.find(c => c.id === parseInt(customerId.value)) || null;
      if (!customerData.value) {
        Message.error('未找到该客户信息');
        router.push('/master/customer/list');
      }
      loading.value = false;
    }, 500);
  } catch (error) {
    console.error('获取客户详情失败', error);
    Message.error('获取客户详情失败');
    loading.value = false;
  }
};

onMounted(() => {
  customerId.value = parseInt(route.params.id);
  fetchCustomerDetail();
});
</script>

<script>
export default { name: "master-customer-detail" };
</script>

<style scoped>
.ma-content-block {
  min-height: calc(100vh - 64px);
  background-color: #f5f7fa;
}

.arco-descriptions-item {
  padding: 8px 0;
}

.arco-card {
  height: 100%;
}

.arco-timeline {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.arco-spin) {
  display: block;
  width: 100%;
}
</style>
