<template>
  <div class="ma-content-block p-4">
    <div class="mb-4 flex justify-between items-center">
      <div class="text-xl font-bold">我的客户</div>
      <a-button type="primary" @click="handleAssign">
        <template #icon><icon-plus /></template>
        分配客户
      </a-button>
    </div>

    <a-tabs v-model:active-key="activeTab">
      <a-tab-pane key="all" title="全部客户">
        <a-table :columns="columns" :data="filteredCustomers" :pagination="{ pageSize: 10 }">
          <template #status="{ record }">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '正常' : '禁用' }}
            </a-tag>
          </template>
          <template #operation="{ record }">
            <div>
              <a-button type="text" size="small" @click="viewDetail(record)">
                <template #icon><icon-eye /></template>
                查看
              </a-button>
              <a-button type="text" size="small" @click="addCommunication(record)">
                <template #icon><icon-message /></template>
                沟通
              </a-button>
            </div>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="vip" title="VIP客户">
        <a-table :columns="columns" :data="vipCustomers" :pagination="{ pageSize: 10 }">
          <template #status="{ record }">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '正常' : '禁用' }}
            </a-tag>
          </template>
          <template #operation="{ record }">
            <div>
              <a-button type="text" size="small" @click="viewDetail(record)">
                <template #icon><icon-eye /></template>
                查看
              </a-button>
              <a-button type="text" size="small" @click="addCommunication(record)">
                <template #icon><icon-message /></template>
                沟通
              </a-button>
            </div>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="normal" title="普通客户">
        <a-table :columns="columns" :data="normalCustomers" :pagination="{ pageSize: 10 }">
          <template #status="{ record }">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '正常' : '禁用' }}
            </a-tag>
          </template>
          <template #operation="{ record }">
            <div>
              <a-button type="text" size="small" @click="viewDetail(record)">
                <template #icon><icon-eye /></template>
                查看
              </a-button>
              <a-button type="text" size="small" @click="addCommunication(record)">
                <template #icon><icon-message /></template>
                沟通
              </a-button>
            </div>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="potential" title="潜在客户">
        <a-table :columns="columns" :data="potentialCustomers" :pagination="{ pageSize: 10 }">
          <template #status="{ record }">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '正常' : '禁用' }}
            </a-tag>
          </template>
          <template #operation="{ record }">
            <div>
              <a-button type="text" size="small" @click="viewDetail(record)">
                <template #icon><icon-eye /></template>
                查看
              </a-button>
              <a-button type="text" size="small" @click="addCommunication(record)">
                <template #icon><icon-message /></template>
                沟通
              </a-button>
            </div>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>

    <!-- 分配客户弹窗 -->
    <a-modal v-model:visible="assignVisible" title="分配客户" @cancel="assignVisible = false" @before-ok="handleAssignSubmit">
      <MaForm ref="assignFormRef" v-model="assignForm" :columns="assignColumns" />
    </a-modal>

    <!-- 添加沟通记录弹窗 -->
    <a-modal v-model:visible="communicationVisible" title="添加沟通记录" @cancel="communicationVisible = false" @before-ok="handleAddCommunication">
      <MaForm ref="communicationFormRef" v-model="communicationForm" :columns="communicationColumns" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, h } from "vue";
import { Message } from "@arco-design/web-vue";
import { useRouter } from 'vue-router';
import MaForm from '~/components/base/ma-form/index.vue';

definePageMeta({
  name: 'master-customer-mycustomers',
  path: '/master/customer/mycustomers'
});

const router = useRouter();
const activeTab = ref('all');

// 模拟数据 - 我的客户列表
const myCustomers = ref([
  {
    id: 1,
    name: "北京科技有限公司",
    phone: "13800138000",
    email: "<EMAIL>",
    address: "北京市海淀区中关村科技园",
    group_id: 1,
    group_name: "VIP客户",
    source: "官网",
    status: 1,
    remark: "重要客户，需重点关注",
    created_at: "2025-03-14 07:30:04",
    last_contact_time: "2025-04-15 10:30",
    next_contact_time: "2025-04-20 14:00",
    follow_status: "跟进中"
  },
  {
    id: 2,
    name: "上海贸易有限公司",
    phone: "13900139000",
    email: "<EMAIL>",
    address: "上海市浦东新区陆家嘴金融中心",
    group_id: 2,
    group_name: "普通客户",
    source: "电话营销",
    status: 1,
    remark: "",
    created_at: "2025-03-20 06:53:53",
    last_contact_time: "2025-04-10 14:15",
    next_contact_time: "2025-04-18 09:30",
    follow_status: "跟进中"
  },
  {
    id: 3,
    name: "广州电子科技公司",
    phone: "13700137000",
    email: "<EMAIL>",
    address: "广州市天河区高新技术产业开发区",
    group_id: 3,
    group_name: "潜在客户",
    source: "社交媒体",
    status: 1,
    remark: "需跟进",
    created_at: "2025-04-01 10:15:22",
    last_contact_time: "2025-04-05 09:00",
    next_contact_time: "2025-04-25 10:00",
    follow_status: "待跟进"
  },
  {
    id: 4,
    name: "深圳智能科技有限公司",
    phone: "13600136000",
    email: "<EMAIL>",
    address: "深圳市南山区科技园",
    group_id: 1,
    group_name: "VIP客户",
    source: "合作伙伴",
    status: 1,
    remark: "高潜力客户",
    created_at: "2025-03-25 11:22:33",
    last_contact_time: "2025-04-12 16:20",
    next_contact_time: "2025-04-22 15:00",
    follow_status: "跟进中"
  },
  {
    id: 5,
    name: "杭州网络科技公司",
    phone: "13500135000",
    email: "<EMAIL>",
    address: "杭州市西湖区互联网创业园",
    group_id: 2,
    group_name: "普通客户",
    source: "官网",
    status: 1,
    remark: "",
    created_at: "2025-04-02 09:45:18",
    last_contact_time: "2025-04-08 11:30",
    next_contact_time: "2025-04-19 14:30",
    follow_status: "跟进中"
  }
]);

// 按分组过滤客户
const vipCustomers = computed(() => myCustomers.value.filter(c => c.group_id === 1));
const normalCustomers = computed(() => myCustomers.value.filter(c => c.group_id === 2));
const potentialCustomers = computed(() => myCustomers.value.filter(c => c.group_id === 3));
const filteredCustomers = computed(() => {
  if (activeTab.value === 'all') return myCustomers.value;
  if (activeTab.value === 'vip') return vipCustomers.value;
  if (activeTab.value === 'normal') return normalCustomers.value;
  if (activeTab.value === 'potential') return potentialCustomers.value;
  return myCustomers.value;
});

// 表格列定义
const columns = [
  {
    title: '客户名称',
    dataIndex: 'name',
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
  },
  {
    title: '客户分组',
    dataIndex: 'group_name',
  },
  {
    title: '最近联系',
    dataIndex: 'last_contact_time',
  },
  {
    title: '下次联系',
    dataIndex: 'next_contact_time',
  },
  {
    title: '跟进状态',
    dataIndex: 'follow_status',
    render: ({ record }) => {
      const color = record.follow_status === '跟进中' ? 'blue' : 
                    record.follow_status === '待跟进' ? 'orange' : 
                    record.follow_status === '已成交' ? 'green' : 'gray';
      return h('a-tag', { color: color }, record.follow_status);
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status'
  },
  {
    title: '操作',
    slotName: 'operation'
  }
];

// 分配客户相关
const assignVisible = ref(false);
const assignFormRef = ref();
const assignForm = reactive({
  customer_ids: [],
  follow_status: '待跟进',
  next_contact_time: '',
  remark: ''
});

// 模拟未分配客户列表
const unassignedCustomers = [
  { label: '成都数据科技有限公司', value: 101 },
  { label: '武汉软件开发有限公司', value: 102 },
  { label: '西安智能设备有限公司', value: 103 },
  { label: '南京网络科技有限公司', value: 104 },
  { label: '重庆电子商务有限公司', value: 105 }
];

const assignColumns = reactive([
  {
    dataIndex: 'customer_ids',
    title: '选择客户',
    labelWidth: '80px',
    rules: [{ required: true, message: '请选择客户' }],
    formType: 'checkbox',
    dict: {
      data: unassignedCustomers
    }
  },
  {
    dataIndex: 'follow_status',
    title: '跟进状态',
    labelWidth: '80px',
    formType: 'select',
    dict: {
      data: [
        { label: '待跟进', value: '待跟进' },
        { label: '跟进中', value: '跟进中' }
      ]
    }
  },
  {
    dataIndex: 'next_contact_time',
    title: '下次联系时间',
    labelWidth: '80px',
    rules: [{ required: true, message: '请选择下次联系时间' }],
    formType: 'date-picker',
    placeholder: '请选择下次联系时间'
  },
  {
    dataIndex: 'remark',
    title: '备注',
    labelWidth: '80px',
    formType: 'textarea',
    placeholder: '请输入备注信息'
  }
]);

// 沟通记录相关
const communicationVisible = ref(false);
const communicationFormRef = ref();
const currentCustomer = ref(null);
const communicationForm = reactive({
  type: '',
  content: '',
  next_contact_time: '',
  follow_status: ''
});

const communicationColumns = reactive([
  {
    dataIndex: 'type',
    title: '沟通类型',
    labelWidth: '80px',
    rules: [{ required: true, message: '沟通类型必选' }],
    formType: 'select',
    dict: {
      data: [
        { label: '电话沟通', value: '电话沟通' },
        { label: '邮件沟通', value: '邮件沟通' },
        { label: '现场拜访', value: '现场拜访' },
        { label: '线上会议', value: '线上会议' },
        { label: '其他', value: '其他' }
      ]
    },
    placeholder: '请选择沟通类型'
  },
  {
    dataIndex: 'content',
    title: '沟通内容',
    labelWidth: '80px',
    rules: [{ required: true, message: '沟通内容必填' }],
    formType: 'textarea',
    placeholder: '请输入沟通内容'
  },
  {
    dataIndex: 'next_contact_time',
    title: '下次联系时间',
    labelWidth: '80px',
    formType: 'date-picker',
    placeholder: '请选择下次联系时间'
  },
  {
    dataIndex: 'follow_status',
    title: '更新跟进状态',
    labelWidth: '80px',
    formType: 'select',
    dict: {
      data: [
        { label: '保持当前状态', value: '' },
        { label: '待跟进', value: '待跟进' },
        { label: '跟进中', value: '跟进中' },
        { label: '已成交', value: '已成交' },
        { label: '已流失', value: '已流失' }
      ]
    },
    placeholder: '请选择跟进状态'
  }
]);

// 查看客户详情
const viewDetail = (record) => {
  router.push(`/master/customer/detail/${record.id}`);
};

// 打开分配客户弹窗
const handleAssign = () => {
  Object.assign(assignForm, {
    customer_ids: [],
    follow_status: '待跟进',
    next_contact_time: '',
    remark: ''
  });
  assignVisible.value = true;
};

// 提交分配客户
const handleAssignSubmit = async (done) => {
  const valid = await assignFormRef.value.validate();
  if (valid) return false;

  // 模拟API调用
  try {
    // 实际应用中应该调用API
    const selectedCustomers = unassignedCustomers
      .filter(c => assignForm.customer_ids.includes(c.value))
      .map(c => ({
        id: c.value,
        name: c.label,
        phone: `1${Math.floor(Math.random() * 9000000000) + 1000000000}`,
        email: `contact@${c.label.substring(0, 2).toLowerCase()}.com`,
        address: "待补充",
        group_id: 3, // 默认为潜在客户
        group_name: "潜在客户",
        source: "分配获取",
        status: 1,
        remark: assignForm.remark || "",
        created_at: new Date().toLocaleString(),
        last_contact_time: "-",
        next_contact_time: new Date(assignForm.next_contact_time).toLocaleString(),
        follow_status: assignForm.follow_status
      }));
    
    // 添加到我的客户列表
    myCustomers.value = [...myCustomers.value, ...selectedCustomers];
    
    Message.success('客户分配成功');
    done();
  } catch (error) {
    console.error('客户分配失败', error);
    Message.error('客户分配失败');
    done(false);
  }
};

// 添加沟通记录
const addCommunication = (record) => {
  currentCustomer.value = record;
  Object.assign(communicationForm, {
    type: '',
    content: '',
    next_contact_time: '',
    follow_status: ''
  });
  communicationVisible.value = true;
};

// 提交沟通记录
const handleAddCommunication = async (done) => {
  const valid = await communicationFormRef.value.validate();
  if (valid) return false;

  try {
    // 实际应用中应该调用API
    // 更新客户信息
    const index = myCustomers.value.findIndex(c => c.id === currentCustomer.value.id);
    if (index !== -1) {
      myCustomers.value[index].last_contact_time = new Date().toLocaleString();
      
      if (communicationForm.next_contact_time) {
        myCustomers.value[index].next_contact_time = new Date(communicationForm.next_contact_time).toLocaleString();
      }
      
      if (communicationForm.follow_status) {
        myCustomers.value[index].follow_status = communicationForm.follow_status;
      }
    }
    
    Message.success('沟通记录添加成功');
    done();
  } catch (error) {
    console.error('沟通记录添加失败', error);
    Message.error('沟通记录添加失败');
    done(false);
  }
};

onMounted(() => {
  // 实际应用中应该调用API获取我的客户列表
  // fetchMyCustomers();
});
</script>

<script>
export default { name: "master-customer-mycustomers" };
</script>

<style scoped></style>