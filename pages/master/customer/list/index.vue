<!--
 - 客户管理模块 - 客户列表页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" :data="tableData">
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-switch :checked-value="1" unchecked-value="2" @change="changeStatus($event, record.id)"
          :default-checked="record.status == 1" />
      </template>

      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-button type="text" size="small" @click="viewDetail(record)">
            <template #icon><icon-eye /></template>
            查看
          </a-button>
          <a-popconfirm content="确定要删除该客户吗?" position="bottom" @ok="handleDelete(record.id)">
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>
    </ma-crud>

    <!-- 自定义弹窗组件 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @cancel="modalVisible = false" @before-ok="handleSubmit"
      :width="900">
      <MaForm ref="formRef" v-model="formData" :columns="customerColumns" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import MaForm from '~/components/base/ma-form/index.vue'
import { useRouter } from 'vue-router'

definePageMeta({
  name: 'master-customer-list',
  path: '/master/customer/list'
})

const router = useRouter()
const groups = ref([
  { id: 1, name: "VIP客户" },
  { id: 2, name: "普通客户" },
  { id: 3, name: "潜在客户" }
]);

// 客户等级选项
const customerLevels = ref([
  { label: "A级", value: "A级" },
  { label: "B级", value: "B级" },
  { label: "C级", value: "C级" },
  { label: "D级", value: "D级" }
]);

const crudRef = ref();
const formRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增客户');
const currentId = ref(null);

const formData = reactive({
  name: '',
  phone: '',
  email: '',
  address: '',
  group_id: null,
  source: '',
  status: 1,
  remark: '',
  // 新增字段
  recipient_name: '',
  recipient_phone: '',
  follow_person: '',
  customer_level: '',
  // 地址信息
  province: '',
  city: '',
  district: '',
  address_detail: '',
  // 客户标签
  gender: '',
  age_group: '',
  marital_status: '',
  department: '',
  annual_budget: '',
  purchase_frequency: '',
  // 沟通情况
  preferred_communication: '',
  convenient_time: '',
  shopping_preference: '',
  price_sensitivity: '',
  purchasing_platform: []
});

const customerColumns = reactive([
  {
    dataIndex: 'name',
    title: '客户名称',
    labelWidth: '100px',
    rules: [{ required: true, message: '客户名称必填' }],
    formType: 'input',
    placeholder: '请输入客户名称'
  },
  {
    dataIndex: 'address_info',
    title: '地址信息',
    formType: 'divider',
    dividerDirection: 'horizontal'
  },
  {
    dataIndex: 'city_linkage',
    title: '省市区',
    labelWidth: '100px',
    rules: [{ required: true, message: '省市区必选' }],
    formType: 'city-linkage',
    type: 'select',
    mode: 'name'
  },
  {
    dataIndex: 'address_detail',
    title: '收货地址',
    labelWidth: '100px',
    rules: [{ required: true, message: '收货地址必填' }],
    formType: 'textarea',
    placeholder: '请输入详细收货地址'
  },
  {
    dataIndex: 'recipient_name',
    title: '联系人',
    labelWidth: '100px',
    rules: [{ required: true, message: '联系人必填' }],
    formType: 'input',
    placeholder: '请输入联系人姓名'
  },
  {
    dataIndex: 'recipient_phone',
    title: '电话',
    labelWidth: '100px',
    rules: [
      { required: true, message: '联系电话必填' },
      { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号' }
    ],
    formType: 'input',
    placeholder: '请输入联系电话'
  },
  {
    dataIndex: 'customer_info',
    title: '客户信息',
    formType: 'divider',
    dividerDirection: 'horizontal'
  },
  {
    dataIndex: 'group_id',
    title: '客户分组',
    labelWidth: '100px',
    rules: [{ required: true, message: '客户分组必选' }],
    formType: 'select',
    dict: {
      data: computed(() => groups.value.map(item => ({ label: item.name, value: item.id })))
    },
    placeholder: '请选择客户分组'
  },
  {
    dataIndex: 'customer_level',
    title: '客户等级',
    labelWidth: '100px',
    formType: 'select',
    dict: {
      data: customerLevels.value
    },
    placeholder: '请选择客户等级'
  },
  {
    dataIndex: 'follow_person',
    title: '跟进人',
    labelWidth: '100px',
    formType: 'input',
    placeholder: '请输入跟进人姓名'
  },
  {
    dataIndex: 'source',
    title: '客户来源',
    labelWidth: '100px',
    formType: 'select',
    dict: {
      data: [
        { label: '官网', value: '官网' },
        { label: '电话营销', value: '电话营销' },
        { label: '合作伙伴', value: '合作伙伴' },
        { label: '社交媒体', value: '社交媒体' },
        { label: '其他', value: '其他' }
      ]
    },
    placeholder: '请选择客户来源'
  },
  {
    dataIndex: 'status',
    title: '状态',
    labelWidth: '100px',
    formType: 'select',
    dict: {
      data: [
        { label: '正常', value: 1 },
        { label: '禁用', value: 2 }
      ]
    }
  },
  {
    dataIndex: 'remark',
    title: '备注',
    labelWidth: '100px',
    formType: 'textarea',
    placeholder: '请输入备注信息'
  }
])

const tableData = ref([
  {
    id: 1,
    name: "北京科技有限公司",
    phone: "13800138000",
    email: "<EMAIL>",
    address: "北京市海淀区中关村科技园",
    group_id: 1,
    group_name: "VIP客户",
    source: "官网",
    status: 1,
    remark: "重要客户，需重点关注",
    created_at: "2025-03-14 07:30:04",
    recipient_name: "张经理",
    recipient_phone: "13800138000",
    follow_person: "王小明",
    customer_level: "A级"
  },
  {
    id: 2,
    name: "上海贸易有限公司",
    phone: "13900139000",
    email: "<EMAIL>",
    address: "上海市浦东新区陆家嘴金融中心",
    group_id: 2,
    group_name: "普通客户",
    source: "电话营销",
    status: 1,
    remark: "",
    created_at: "2025-03-20 06:53:53",
    recipient_name: "李总",
    recipient_phone: "13900139000",
    follow_person: "李梅",
    customer_level: "B级"
  },
  {
    id: 3,
    name: "广州电子科技公司",
    phone: "13700137000",
    email: "<EMAIL>",
    address: "广州市天河区高新技术产业开发区",
    group_id: 3,
    group_name: "潜在客户",
    source: "社交媒体",
    status: 2,
    remark: "需跟进",
    created_at: "2025-04-01 10:15:22",
    recipient_name: "王工",
    recipient_phone: "13700137000",
    follow_person: "张伟",
    customer_level: "C级"
  }
]);

// 打开新增弹窗
const handleAdd = () => {
  modalTitle.value = '新增客户';
  Object.assign(formData, {
    name: '',
    phone: '',
    email: '',
    address: '',
    group_id: null,
    source: '',
    status: 1,
    remark: '',
    // 新增字段
    recipient_name: '',
    recipient_phone: '',
    follow_person: '',
    customer_level: '',
    // 地址信息
    city_linkage: { province: '', city: '', area: '' },
    address_detail: '',
    // 重置其他字段
    gender: '',
    age_group: '',
    marital_status: '',
    department: '',
    annual_budget: '',
    purchase_frequency: '',
    preferred_communication: '',
    convenient_time: '',
    shopping_preference: '',
    price_sensitivity: '',
    purchasing_platform: []
  });
  currentId.value = null;
  modalVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = (record) => {
  modalTitle.value = '编辑客户';
  
  // 解析地址为省市区
  const addressParts = record.address ? record.address.split(' ') : ['', '', ''];
  const province = addressParts[0] || '';
  const city = addressParts[1] || '';
  const area = addressParts[2] || '';
  
  Object.assign(formData, {
    name: record.name,
    phone: record.phone,
    email: record.email,
    group_id: record.group_id,
    source: record.source,
    status: record.status,
    remark: record.remark,
    // 新增字段
    recipient_name: record.recipient_name || '',
    recipient_phone: record.recipient_phone || '',
    follow_person: record.follow_person || '',
    customer_level: record.customer_level || '',
    // 地址信息
    city_linkage: { province, city, area },
    address_detail: record.address_detail || ''
  });
  currentId.value = record.id;
  modalVisible.value = true;
};

// 查看客户详情
const viewDetail = (record) => {
  router.push(`/master/customer/detail/${record.id}`)
};

// 处理删除
const handleDelete = (id) => {
  tableData.value = tableData.value.filter(item => item.id !== id);
  Message.success('删除成功');
};

// 处理表单提交
const handleSubmit = async (done) => {
  const valid = await formRef.value.validate();
  if (valid) return false; // 验证失败则阻止关闭弹窗

  const dataToSubmit = { ...formData };
  
  // 处理地址信息
  const cityLinkage = dataToSubmit.city_linkage || {};
  const province = cityLinkage.province || '';
  const city = cityLinkage.city || '';
  const area = cityLinkage.area || '';
  const fullAddress = [province, city, area].filter(Boolean).join(' ');
  
  let response;
  if (currentId.value === null) { // 新增
    // TODO: 调用新增客户API
    response = { success: true, message: '客户添加成功 (模拟)' }; // 模拟成功
    tableData.value.push({ 
      id: Date.now(), 
      name: dataToSubmit.name,
      phone: dataToSubmit.recipient_phone,
      email: dataToSubmit.email || '',
      address: fullAddress,
      address_detail: dataToSubmit.address_detail,
      group_id: dataToSubmit.group_id, 
      group_name: groups.value.find(r => r.id === dataToSubmit.group_id)?.name, 
      source: dataToSubmit.source,
      status: dataToSubmit.status,
      remark: dataToSubmit.remark,
      created_at: new Date().toLocaleString(),
      recipient_name: dataToSubmit.recipient_name,
      recipient_phone: dataToSubmit.recipient_phone,
      follow_person: dataToSubmit.follow_person,
      customer_level: dataToSubmit.customer_level
    });
    console.log("新增客户", dataToSubmit);
  } else {
    // TODO: 调用更新客户 API，传入 dataToSubmit
    response = { success: true, message: '客户更新成功 (模拟)' }; // 模拟成功
    const index = tableData.value.findIndex(item => item.id === currentId.value);
    if (index !== -1) {
      Object.assign(tableData.value[index], { 
        name: dataToSubmit.name,
        phone: dataToSubmit.recipient_phone,
        email: dataToSubmit.email || '',
        address: fullAddress,
        address_detail: dataToSubmit.address_detail,
        group_id: dataToSubmit.group_id, 
        group_name: groups.value.find(r => r.id === dataToSubmit.group_id)?.name, 
        source: dataToSubmit.source,
        status: dataToSubmit.status,
        remark: dataToSubmit.remark,
        recipient_name: dataToSubmit.recipient_name,
        recipient_phone: dataToSubmit.recipient_phone,
        follow_person: dataToSubmit.follow_person,
        customer_level: dataToSubmit.customer_level
      });
    }
    console.log("更新客户", currentId.value, dataToSubmit);
  }

  if (response.success) {
    Message.success(response.message);
    done();
  } else {
    Message.error(response.message);
    done(false);
  }
};

// 修改客户状态
const changeStatus = (status, id) => {
  const index = tableData.value.findIndex(item => item.id === id);
  if (index !== -1) {
    tableData.value[index].status = status;
    Message.success(`状态修改成功`);
  }
};

const crud = reactive({
  api: () => Promise.resolve({ rows: tableData.value }),
  showIndex: false,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 200,
  add: { show: false },
  edit: { show: false },
  delete: { show: false }
});

const columns = reactive([
  { title: 'ID', dataIndex: 'id', width: 80 },
  {
    title: '客户名称',
    dataIndex: 'name',
    search: true,
    width: 180
  },
  {
    title: '收货地址',
    dataIndex: 'address',
    search: true,
    width: 200
  },
  {
    title: '收货人姓名',
    dataIndex: 'recipient_name',
    search: true,
    width: 120
  },
  {
    title: '收货电话',
    dataIndex: 'recipient_phone',
    search: true,
    width: 150
  },
  {
    title: '跟进人',
    dataIndex: 'follow_person',
    search: true,
    width: 120
  },
  {
    title: '客户等级',
    dataIndex: 'customer_level',
    search: true,
    width: 100
  },
  {
    title: '客户分组',
    dataIndex: 'group_name',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slotName: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180
  },
]);
</script>

<script>
export default { name: "master-customer-list" };
</script>

<style scoped></style>
