<!--
 - 订单详情页面
 - 展示订单的详细信息、商品信息、收货信息、物流信息等
-->
<template>
  <div class="ma-content-block lg:p-4 p-2">
    <!-- 页面标题 -->
    <div class="page-header flex items-center justify-between mb-4">
      <div class="flex items-center">
        <h2 class="text-lg font-medium">订单详情</h2>
      </div>
      <div>
        <a-button type="primary" @click="printOrder">
          <template #icon><icon-printer /></template>
          打印订单
        </a-button>
      </div>
    </div>

    <!-- 订单基本信息卡片 -->
    <a-card class="mb-4 order-card" title="订单信息">
      <a-row :gutter="[16, 16]">
        <a-col :span="8" :xs="24" :sm="12" :md="8" v-for="(item, index) in orderBaseInfo" :key="index">
          <div class="info-item">
            <span class="info-label">{{ item.label }}：</span>
            <span class="info-value">{{ item.value }}</span>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 商品信息卡片 -->
    <a-card class="mb-4 order-card" title="商品信息">
      <a-table 
        :columns="productColumns" 
        :data="orderDetail.products || []" 
        :pagination="false" 
        :bordered="{ cell: true }"
        class="goods-table"
      >
        <template #name="{ record }">
          <div class="flex">
            <div class="product-image-container mr-3">
              <img :src="record.image || 'https://via.placeholder.com/60x60'" class="product-image" />
            </div>
            <div class="product-info">
              <div class="product-name">{{ record.product_name }}</div>
              <div class="product-specs">{{ record.spec }}</div>
            </div>
          </div>
        </template>
        <template #price="{ record }">
          <span class="text-price">￥{{ record.price }}</span>
        </template>
        <template #quantity="{ record }">
          <span>{{ record.quantity }}</span>
        </template>
        <template #subtotal="{ record }">
          <span class="text-price">￥{{ (record.price * record.quantity) || '0.00' }}</span>
        </template>
        <template #empty>
          <div class="empty-state">
            <icon-inbox :size="64" />
            <p>暂无商品信息</p>
          </div>
        </template>
      </a-table>
      
      <!-- 订单金额信息 -->
      <div class="price-summary">
        <div class="price-row">
          <span>商品总额：</span>
          <span class="price-value">￥{{ orderDetail.total_amount || '0.00' }}</span>
        </div>
        <div class="price-row">
          <span>运费金额：</span>
          <span class="price-value">￥{{ orderDetail.shipping_fee || '0.00' }}</span>
        </div>
        <div class="price-row">
          <span>优惠金额：</span>
          <span class="price-value">￥{{ orderDetail.discount_amount || '0.00' }}</span>
        </div>
        <div class="price-row total-row">
          <span>支付金额：</span>
          <span class="price-value total-price">￥{{ orderDetail.actual_amount || '0.00' }}</span>
        </div>
        <div class="price-row payment-method-row">
          <span>支付方式：</span>
          <span class="payment-method">{{ orderDetail.payment_method_text || '微信支付' }}</span>
        </div>
      </div>
    </a-card>

    <!-- 收货信息卡片 -->
    <a-card class="mb-4 order-card" title="收货信息">
      <a-row :gutter="[16, 16]">
        <a-col :span="8" :xs="24" :sm="12" :md="8" v-for="(item, index) in receiverInfo" :key="index">
          <div class="info-item">
            <span class="info-label">{{ item.label }}：</span>
            <span class="info-value">{{ item.value }}</span>
          </div>
        </a-col>
      </a-row>
    </a-card>

    <!-- 物流信息卡片 -->
    <a-card v-if="orderDetail.logistics" class="mb-4 order-card" title="物流信息">
      <a-row :gutter="[16, 16]">
        <a-col :span="8" :xs="24" :sm="12" :md="8" v-for="(item, index) in logisticsInfo" :key="index">
          <div class="info-item">
            <span class="info-label">{{ item.label }}：</span>
            <span class="info-value">{{ item.value }}</span>
          </div>
        </a-col>
      </a-row>
      
      <div class="mt-4">
        <a-button type="primary" @click="checkLogistics">
          <template #icon><icon-search /></template>
          查看物流详情
        </a-button>
      </div>
    </a-card>

    <!-- 订单日志卡片 -->
    <a-card class="order-card" title="订单日志">
      <a-timeline class="order-timeline">
        <a-timeline-item 
          v-for="(log, index) in orderDetail.logs" 
          :key="index"
          :dot-color="getLogColor(log.action_type)"
        >
          <div class="timeline-content">
            <div class="timeline-title">
              {{ log.action_type_text }}
            </div>
            <div class="timeline-detail">
              {{ log.content }}
            </div>
            <div class="timeline-meta">
              <span class="timeline-operator">操作人：{{ log.operator_name }}</span>
              <span class="timeline-time">（{{ log.created_at }}）</span>
            </div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-card>
    
    <!-- 底部操作区 -->
    <div class="bottom-actions mt-4 flex justify-center">
      <a-button type="primary" @click="handleShip" class="mx-2">
        发货
      </a-button>
      <a-button @click="handleCancel" class="mx-2">
        取消订单
      </a-button>
  
      <a-button @click="goBack" class="mx-2">
        返回
      </a-button>
    </div>
  </div>
</template>

<script>
export default { name: "master-customer-ordermanagement-detail" };
</script>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import orderApi from '@/api/master/order';

// 页面元数据
definePageMeta({
  name: 'master-customer-ordermanagement-detail',
  path: '/master/customer/ordermanagement/detail/:id'
});

const route = useRoute();
const router = useRouter();
const orderId = ref(route.params.id);
const orderDetail = ref({});
const loading = ref(false);

// 商品表格列配置
const productColumns = [
  { 
    title: '商品图片', 
    dataIndex: 'image',
    width: 100,
    render: ({ record }) => {
      return h('div', { class: 'product-image' }, [
        h('img', {
          src: record.image || '/assets/images/default-product.png',
          alt: record.product_name,
          style: 'width: 60px; height: 60px; object-fit: cover; border-radius: 4px;'
        })
      ]);
    }
  },
  { 
    title: '商品名称', 
    dataIndex: 'product_name',
    render: ({ record }) => {
      return h('div', {}, [
        h('div', { class: 'product-name font-medium' }, record.product_name),
        h('div', { class: 'product-sku text-xs text-gray-500' }, `规格: ${record.spec || '标准'}`)
      ]);
    }
  },
  { title: '单价', dataIndex: 'price', render: ({ record }) => `￥${record.price || '0.00'}` },
  { title: '数量', dataIndex: 'quantity' },
  { 
    title: '小计', 
    dataIndex: 'subtotal',
    render: ({ record }) => `￥${(record.price * record.quantity) || '0.00'}`
  },
];

// 订单基本信息
const orderBaseInfo = computed(() => {
  if (!orderDetail.value) return [];
  
  return [
    { label: '订单编号', value: orderDetail.value.order_no },
    { label: '下单时间', value: orderDetail.value.order_time },
    { label: '支付时间', value: orderDetail.value.payment_time || '-' },
    { label: '订单来源', value: orderDetail.value.order_source_text },
    { label: '订单类型', value: orderDetail.value.order_type_text },
    { label: '订单状态', value: getStatusText(orderDetail.value.order_status) },
    { label: '支付方式', value: orderDetail.value.payment_method_text || '微信支付' },
    { label: '业务员', value: orderDetail.value.salesman_name },
    { label: '费率', value: `${orderDetail.value.rate || 0}%` },
  ];
});

// 收货人信息
const receiverInfo = computed(() => {
  if (!orderDetail.value) return [];
  
  return [
    { label: '收货人', value: orderDetail.value.receiver_name },
    { label: '联系电话', value: orderDetail.value.receiver_phone },
    { label: '收货地址', value: orderDetail.value.receiver_address }
  ];
});

// 物流信息
const logisticsInfo = computed(() => {
  if (!orderDetail.value || !orderDetail.value.logistics) return [];
  
  return [
    { label: '物流公司', value: orderDetail.value.logistics.company_name },
    { label: '物流单号', value: orderDetail.value.logistics.tracking_no },
    { label: '发货时间', value: orderDetail.value.logistics.ship_time }
  ];
});

// 状态映射
const statusMap = {
  'pending': { text: '待接单', color: 'orange' },
  'toShip': { text: '待发货', color: 'blue' },
  'shipped': { text: '待收货', color: 'purple' },
  'toPay': { text: '待付款', color: 'red' },
  'success': { text: '交易成功', color: 'green' },
  'closed': { text: '已关闭', color: 'gray' },
  'exception': { text: '异常订单', color: 'magenta' }
};

// 获取状态文本
const getStatusText = (status) => {
  return statusMap[status]?.text || status;
};

// 获取日志颜色
const getLogColor = (actionType) => {
  const colorMap = {
    'create': 'blue',
    'pay': 'green',
    'ship': 'purple',
    'receive': 'cyan',
    'cancel': 'red',
    'refund': 'orange',
    'comment': 'gray'
  };
  
  return colorMap[actionType] || 'blue';
};

// 返回订单列表
const goBack = () => {
  router.push('/master/customer/ordermanagement');
};

// 打印订单
const printOrder = () => {
  window.print();
};

// 查看物流详情
const checkLogistics = () => {
  if (!orderDetail.value?.logistics?.tracking_no) {
    Message.warning('暂无物流信息');
    return;
  }
  
  // 实际项目中应该跳转到物流查询页面或打开物流查询弹窗
  Message.info('查看物流详情功能待实现');
};

// 发货处理
const handleShip = () => {
  Message.success('订单已发货');
  // 实际项目中应该调用API更新订单状态
  // orderApi.shipOrder(orderId.value).then(res => {
  //   if (res.code === 200) {
  //     Message.success('订单已发货');
  //     fetchOrderDetail();
  //   }
  // });
};

// 取消订单
const handleCancel = () => {
  Message.warning('订单已取消');
  // 实际项目中应该弹出确认对话框并调用API取消订单
  // Modal.confirm({
  //   title: '确认取消该订单吗？',
  //   content: '取消后将无法恢复，请确认操作。',
  //   onOk: () => {
  //     orderApi.cancelOrder(orderId.value).then(res => {
  //       if (res.code === 200) {
  //         Message.success('订单已取消');
  //         fetchOrderDetail();
  //       }
  //     });
  //   }
  // });
};

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!orderId.value) return;
  
  loading.value = true;
  try {
    // 实际项目中应该调用API获取订单详情
    // const res = await orderApi.getOrderDetail(orderId.value);
    // orderDetail.value = res.data;
    
    // 模拟数据
    setTimeout(() => {
      orderDetail.value = {
        id: orderId.value,
        order_no: '20250402095123456',
        order_time: '2025-04-02 10:18:28',
        payment_time: '2025-04-02 10:30:45',
        order_source: 'online',
        order_source_text: '官网商城',
        order_type: 'normal',
        order_type_text: '普通订单',
        order_status: 'success',
        payment_method_text: '微信支付',
        rate: 5,
        salesman_name: '陈某',
        total_amount: 3000,
        shipping_fee: 0,
        discount_amount: 0,
        actual_amount: 3000,
        receiver_name: '周小姐',
        receiver_phone: '13800138000',
        receiver_address: '广州市天河区棠树街118号',
        logistics: {
          company_name: '顺丰快递',
          tracking_no: 'SF1234567910',
          ship_time: '2025-04-03 12:40'
        },
        products: [
          {
            product_name: 'EARMCRF行动者 M3型装备 作业通电子作业多功能战术机',
            spec: '战术版',
            price: 1000,
            quantity: 2,
            image: '/assets/images/default-product.png'
          },
          {
            product_name: 'EARMCRF行动者 M3型装备 作业通电子作业多功能战术机',
            spec: '战术版',
            price: 1000,
            quantity: 1,
            image: '/assets/images/default-product.png'
          }
        ],
        logs: [
          {
            action_type: 'create',
            action_type_text: '订单创建',
            content: '用户提交订单',
            operator_name: '张小明',
            created_at: '2025-04-02 09:51'
          },
          {
            action_type: 'pay',
            action_type_text: '订单支付',
            content: '用户完成支付',
            operator_name: '系统自动',
            created_at: '2025-04-03 12:40'
          },
          {
            action_type: 'ship',
            action_type_text: '订单发货',
            content: '订单已发货',
            operator_name: '李明',
            created_at: '2025-04-04 12:40'
          }
        ]
      };
      loading.value = false;
    }, 500);
    
  } catch (error) {
    console.error('获取订单详情失败:', error);
    Message.error('获取订单详情失败');
    loading.value = false;
  }
};

// 初始化
onMounted(() => {
  fetchOrderDetail();
});
</script>

<style scoped>
/* 卡片样式 */
.order-card {
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.order-card :deep(.arco-card-header) {
  border-bottom: 1px solid #f2f3f5;
  padding: 12px 20px;
}

.order-card :deep(.arco-card-body) {
  padding: 16px 20px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  align-items: flex-start;
  line-height: 1.6;
}

.info-label {
  color: #86909c;
  min-width: 80px;
  margin-right: 8px;
  font-size: 14px;
}

.info-value {
  color: #1d2129;
  font-size: 14px;
  word-break: break-all;
}

/* 商品表格样式 */
.goods-table {
  margin-bottom: 16px;
}

.goods-table :deep(.arco-table-th) {
  background-color: #f7f8fa;
  font-weight: 500;
  color: #1d2129;
  padding: 12px 16px;
  border-color: #e5e6eb;
}

.goods-table :deep(.arco-table-td) {
  padding: 16px;
  border-color: #e5e6eb;
}

.goods-table :deep(.arco-table-tr:hover) {
  background-color: #f7f8fa;
}

.product-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #f2f3f5;
  background-color: #f7f8fa;
}

.product-info {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  color: #1d2129;
  line-height: 1.5;
  margin-bottom: 4px;
}

.product-specs {
  font-size: 13px;
  color: #86909c;
}

.text-price {
  color: #1d2129;
  font-size: 14px;
}

/* 价格汇总样式 */
.price-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 16px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  width: 200px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #1d2129;
}

.price-value {
  font-weight: 500;
}

.total-row {
  font-weight: 500;
}

.total-price {
  color: #f53f3f;
}

.payment-method-row {
  margin-top: 8px;
}

.payment-method {
  color: #ff7d00;
}

/* 时间线样式 */
.order-timeline {
  padding: 8px 0;
}

.timeline-content {
  margin-bottom: 16px;
}

.timeline-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: #1d2129;
}

.timeline-detail {
  margin-bottom: 4px;
  color: #4e5969;
}

.timeline-meta {
  font-size: 12px;
  color: #86909c;
}

.timeline-operator {
  margin-right: 8px;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 0;
  color: #86909c;
}

.empty-state p {
  margin-top: 8px;
}

/* 底部操作区样式 */
.bottom-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px dashed #e5e6eb;
}

.bottom-actions .arco-btn {
  min-width: 90px;
}

@media print {
  .page-header, .arco-btn, .bottom-actions {
    display: none;
  }
}
</style>
