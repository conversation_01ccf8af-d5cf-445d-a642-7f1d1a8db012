<!--
 - 客户管理模块 - 客户分组页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef" :data="tableData">
      <!-- 状态列 -->
      <template #status="{ record }">
        <a-switch :checked-value="1" unchecked-value="2" @change="changeStatus($event, record.id)"
          :default-checked="record.status == 1" />
      </template>

      <!-- 操作列 -->
      <template #operationCell="{ record }">
        <div>
          <a-button type="text" size="small" @click="handleEdit(record)">
            <template #icon><icon-edit /></template>
            编辑
          </a-button>
          <a-popconfirm content="确定要删除该分组吗?" position="bottom" @ok="handleDelete(record.id)">
            <a-button type="text" status="danger" size="small">
              <template #icon><icon-delete /></template>
              删除
            </a-button>
          </a-popconfirm>
        </div>
      </template>

      <!-- 表格顶部按钮 -->
      <template #tableButtons>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>新增
        </a-button>
      </template>
    </ma-crud>

    <!-- 自定义弹窗组件 -->
    <a-modal v-model:visible="modalVisible" :title="modalTitle" @cancel="modalVisible = false" @before-ok="handleSubmit"
      :width="600">
      <MaForm ref="formRef" v-model="formData" :columns="groupColumns" />
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from "vue";
import { Message } from "@arco-design/web-vue";
import MaForm from '~/components/base/ma-form/index.vue'

definePageMeta({
  name: 'master-customer-group',
  path: '/master/customer/group'
})

const crudRef = ref();
const formRef = ref();
const modalVisible = ref(false);
const modalTitle = ref('新增分组');
const currentId = ref(null);

const formData = reactive({
  name: '',
  description: '',
  discount_rate: 100,
  status: 1,
  sort: 0
});

const groupColumns = reactive([
  {
    dataIndex: 'name',
    title: '分组名称',
    labelWidth: '80px',
    rules: [{ required: true, message: '分组名称必填' }],
    formType: 'input',
    placeholder: '请输入分组名称'
  },
  {
    dataIndex: 'description',
    title: '分组描述',
    labelWidth: '80px',
    formType: 'textarea',
    placeholder: '请输入分组描述'
  },
  {
    dataIndex: 'discount_rate',
    title: '折扣率(%)',
    labelWidth: '80px',
    rules: [
      { required: true, message: '折扣率必填' },
      { type: 'number', min: 0, max: 100, message: '折扣率必须在0-100之间' }
    ],
    formType: 'input-number',
    placeholder: '请输入折扣率，如95表示95折'
  },
  {
    dataIndex: 'sort',
    title: '排序',
    labelWidth: '80px',
    formType: 'input-number',
    placeholder: '请输入排序值，数值越小越靠前'
  },
  {
    dataIndex: 'status',
    title: '状态',
    labelWidth: '80px',
    formType: 'radio',
    dict: {
      data: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 2 }
      ]
    }
  }
])

const tableData = ref([
  {
    id: 1,
    name: "VIP客户",
    description: "高价值客户，提供最优质的服务和折扣",
    discount_rate: 90,
    sort: 0,
    status: 1,
    customer_count: 12,
    created_at: "2025-03-14 07:30:04"
  },
  {
    id: 2,
    name: "普通客户",
    description: "常规客户，提供标准服务",
    discount_rate: 95,
    sort: 1,
    status: 1,
    customer_count: 45,
    created_at: "2025-03-20 06:53:53"
  },
  {
    id: 3,
    name: "潜在客户",
    description: "尚未完成交易的潜在客户",
    discount_rate: 100,
    sort: 2,
    status: 1,
    customer_count: 28,
    created_at: "2025-04-01 10:15:22"
  }
]);

// 打开新增弹窗
const handleAdd = () => {
  modalTitle.value = '新增分组';
  Object.assign(formData, {
    name: '',
    description: '',
    discount_rate: 100,
    status: 1,
    sort: 0
  });
  currentId.value = null;
  modalVisible.value = true;
};

// 打开编辑弹窗
const handleEdit = (record) => {
  modalTitle.value = '编辑分组';
  Object.assign(formData, {
    name: record.name,
    description: record.description,
    discount_rate: record.discount_rate,
    status: record.status,
    sort: record.sort
  });
  currentId.value = record.id;
  modalVisible.value = true;
};

// 处理删除
const handleDelete = (id) => {
  const group = tableData.value.find(item => item.id === id);
  if (group && group.customer_count > 0) {
    Message.error(`该分组下还有${group.customer_count}个客户，无法删除`);
    return;
  }
  tableData.value = tableData.value.filter(item => item.id !== id);
  Message.success('删除成功');
};

// 处理表单提交
const handleSubmit = async (done) => {
  const valid = await formRef.value.validate();
  if (valid) return false; // 验证失败则阻止关闭弹窗

  const dataToSubmit = { ...formData };

  let response;
  if (currentId.value === null) { // 新增
    // TODO: 调用新增分组API
    response = { success: true, message: '分组添加成功 (模拟)' }; // 模拟成功
    tableData.value.push({ 
      id: Date.now(), 
      ...dataToSubmit, 
      customer_count: 0,
      created_at: new Date().toLocaleString() 
    });
    console.log("新增分组", dataToSubmit);
  } else {
    // TODO: 调用更新分组 API，传入 dataToSubmit
    response = { success: true, message: '分组更新成功 (模拟)' }; // 模拟成功
    const index = tableData.value.findIndex(item => item.id === currentId.value);
    if (index !== -1) {
      const customerCount = tableData.value[index].customer_count;
      Object.assign(tableData.value[index], dataToSubmit, { customer_count: customerCount });
    }
    console.log("更新分组", currentId.value, dataToSubmit);
  }

  if (response.success) {
    Message.success(response.message);
    done();
  } else {
    Message.error(response.message);
    done(false);
  }
};

// 修改分组状态
const changeStatus = (status, id) => {
  const index = tableData.value.findIndex(item => item.id === id);
  if (index !== -1) {
    tableData.value[index].status = status;
    Message.success(`状态修改成功`);
  }
};

const crud = reactive({
  api: () => Promise.resolve({ rows: tableData.value }),
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 150,
  add: { show: false },
  edit: { show: false },
  delete: { show: false }
});

const columns = reactive([
  { title: 'ID', dataIndex: 'id', width: 80 },
  {
    title: '分组名称',
    dataIndex: 'name',
    search: true,
    width: 150
  },
  {
    title: '分组描述',
    dataIndex: 'description',
    width: 250
  },
  {
    title: '折扣率(%)',
    dataIndex: 'discount_rate',
    width: 120
  },
  {
    title: '客户数量',
    dataIndex: 'customer_count',
    width: 120
  },
  {
    title: '排序',
    dataIndex: 'sort',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    slotName: 'status'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180
  },
]);
</script>

<script>
export default { name: "master-customer-group" };
</script>

<style scoped></style>
