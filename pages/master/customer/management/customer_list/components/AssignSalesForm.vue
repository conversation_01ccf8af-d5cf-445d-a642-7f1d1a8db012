<!--
 - 分配销售表单组件
 - 
 - <AUTHOR>
-->
<template>
  <a-modal
    :visible="visible"
    :title="title"
    @cancel="handleClose"
    @before-ok="handleSubmit"
  >
    <a-form auto-label-width :model="form" layout="horizontal" :label-col-props="{ span: 4, style: { textAlign: 'left' } }" :wrapper-col-props="{ span: 20 }">
      <a-form-item field="customerId" label="客户名称">
        <a-input v-model="form.customerName" disabled style="width: 100%" />
      </a-form-item>
      <a-form-item field="salesIds" label="销售人员" required>
        <a-select 
          v-model="form.salesIds" 
          placeholder="请选择销售人员"
          :loading="salesLoading"
          multiple
          allow-clear
        >
          <a-option v-for="item in salesOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-option>
        </a-select>
        <div class="text-xs text-gray-500 mt-1">可选择多个销售人员共同负责</div>
      </a-form-item>
      <a-form-item field="remark" label="分配说明">
        <a-textarea v-model="form.remark" placeholder="请输入分配说明" style="width: 100%" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '分配销售人员'
  },
  customer: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'submit', 'close']);

// 表单数据
const form = reactive({
  customerId: undefined,
  customerName: '',
  salesIds: [],
  salesNames: [],
  remark: ''
});

// 销售人员选项
const salesOptions = ref([]);
const salesLoading = ref(false);

// 监听customer变化，更新表单
watch(() => props.customer, (newVal) => {
  if (newVal && newVal.id) {
    form.customerId = newVal.id;
    form.customerName = newVal.name;
    
    // 如果有已分配的销售人员，设置为默认选中
    if (newVal.salesId) {
      if (typeof newVal.salesId === 'string' && newVal.salesId.includes(',')) {
        form.salesIds = newVal.salesId.split(',').map(id => Number(id));
      } else {
        form.salesIds = [Number(newVal.salesId)];
      }
    } else {
      form.salesIds = [];
    }
    
    form.remark = '';
  }
}, { immediate: true, deep: true });

// 加载销售人员选项
const loadSalesOptions = async () => {
  salesLoading.value = true;
  try {
    // 这里应该调用实际的API
    // const res = await getSalesList();
    // salesOptions.value = res.data.map(item => ({
    //   label: item.name,
    //   value: item.id
    // }));
    
    // 模拟数据
    setTimeout(() => {
      salesOptions.value = [
        { label: '张三', value: 1 },
        { label: '李四', value: 2 },
        { label: '王五', value: 3 },
        { label: '赵六', value: 4 },
        { label: '刘七', value: 5 }
      ];
      salesLoading.value = false;
    }, 500);
  } catch (error) {
    console.error('加载销售人员失败', error);
    Message.error('加载销售人员失败');
    salesLoading.value = false;
  }
};

// 打开弹窗时加载销售人员选项
watch(() => props.visible, (val) => {
  if (val) {
    loadSalesOptions();
  }
});

// 处理关闭
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 准备提交数据
const prepareSubmitData = () => {
  try {
    // 验证必填字段
    if (form.salesIds.length === 0) {
      Message.warning('请选择销售人员');
      return false;
    }

    // 获取销售人员名称
    form.salesNames = form.salesIds.map(id => {
      const option = salesOptions.value.find(item => item.value === id);
      return option ? option.label : '';
    });

    // 准备要保存的数据
    const submitData = {
      ...form,
      salesIdStr: form.salesIds.join(','),
      salesNameStr: form.salesNames.join(','),
      assignTime: new Date().toISOString(),
      status: 'assigned'
    };

    return submitData;
  } catch (error) {
    console.error('准备提交数据失败', error);
    Message.error('准备提交数据失败');
    return false;
  }
};

// 处理提交
const handleSubmit = (done) => {
  const submitData = prepareSubmitData();
  
  if (!submitData) {
    done(false);
    return;
  }

  // 模拟API调用
  setTimeout(() => {
    try {
      // 通知父组件分配成功
      emit('submit', submitData, (success) => {
        if (success !== false) {
          // 关闭弹窗
          emit('update:visible', false);
          Message.success('销售人员分配成功');
          done(true);
        } else {
          Message.error('销售人员分配失败');
          done(false);
        }
      });
    } catch (error) {
      console.error('提交分配销售请求失败', error);
      Message.error('提交分配销售请求失败');
      done(false);
    }
  }, 300);
};
</script>

<style scoped>
</style>
