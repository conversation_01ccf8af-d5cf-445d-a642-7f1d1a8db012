<template>
  <a-modal
    :visible="visible"
    @update:visible="visible = $event"
    :title="title"
    @cancel="handleClose"
    @before-ok="handleSave"
  >
    <a-form :model="form" auto-label-width ref="formRef" layout="horizontal" :label-col-props="{ span: 4, style: { textAlign: 'left' } }" :wrapper-col-props="{ span: 20 }">
      <a-form-item field="name" label="联系人姓名" required>
        <a-input v-model="form.name" placeholder="请输入联系人姓名" style="width: 100%" />
      </a-form-item>
      <a-form-item field="position" label="职位">
        <a-input v-model="form.position" placeholder="请输入职位" style="width: 100%" />
      </a-form-item>
      <a-form-item field="phone" label="电话">
        <a-input v-model="form.phone" placeholder="请输入电话" style="width: 100%" />
      </a-form-item>
      <a-form-item field="wechat" label="微信号">
        <a-input v-model="form.wechat" placeholder="请输入微信号" style="width: 100%" />
      </a-form-item>
      <a-form-item field="email" label="电子邮箱">
        <a-input v-model="form.email" placeholder="请输入电子邮箱" style="width: 100%" />
      </a-form-item>
      <a-form-item field="importance" label="重要程度">
        <a-rate v-model="form.importance" :count="5" />
      </a-form-item>
      <a-form-item field="remark" label="备注">
        <a-textarea v-model="form.remark" placeholder="请输入备注" :auto-size="{ minRows: 3, maxRows: 6 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';

// 接收父组件传递的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增联系人'
  },
  customer: {
    type: Object,
    default: () => ({})
  },
  contact: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits([
  'update:visible',
  'save',
  'close',
  'refresh'
]);

// 表单引用
const formRef = ref(null);

// 表单数据
const form = reactive({
  id: undefined,
  customerId: undefined,
  name: '',
  position: '',
  wechat: '',
  phone: '',
  email: '',
  importance: 3,
  remark: '',
  fromCommunication: false // 标记是否从沟通记录中打开
});

// 监听客户信息变化，自动填充客户ID
watch(() => props.customer, (newVal) => {
  if (newVal && newVal.id) {
    form.customerId = newVal.id;
  }
}, { immediate: true });

// 重置表单数据
const resetFormData = () => {
  form.id = undefined;
  form.customerId = props.customer?.id;
  form.name = '';
  form.position = '';
  form.wechat = '';
  form.phone = '';
  form.email = '';
  form.importance = 3;
  form.remark = '';
  form.fromCommunication = props.contact?.fromCommunication || false;
};

// 监听联系人信息变化，编辑模式时填充表单
watch(() => props.contact, (newVal) => {
  if (newVal && newVal.id) {
    // 编辑模式
    Object.keys(form).forEach(key => {
      if (newVal[key] !== undefined) {
        form[key] = newVal[key];
      }
    });
  } else {
    // 新增模式
    resetFormData();
  }
}, { immediate: true });

// 保存联系人数据
const saveContact = () => {
  try {
    // 表单验证
    if (!form.name) {
      Message.error('请输入联系人姓名');
      return false;
    }

    // 构建保存数据
    const saveData = {
      ...form,
      customerId: form.customerId || props.customer?.id
    };
    
    // 如果是新增模式，生成一个临时ID
    if (!saveData.id) {
      saveData.id = Date.now();
    }

    return saveData;
  } catch (error) {
    console.error('保存联系人失败', error);
    Message.error('保存联系人失败');
    return false;
  }
};

// 保存表单
const handleSave = (done) => {
  const saveData = saveContact();
  
  if (!saveData) {
    done(false);
    return;
  }
  
  // 模拟API调用
  setTimeout(() => {
    // 通知父组件保存成功
    emit('save', saveData, (success) => {
      if (success !== false) {
        // 关闭弹窗
        emit('update:visible', false);
        Message.success(form.id ? '编辑联系人成功' : '添加联系人成功');
        
        // 刷新数据
        emit('refresh');
        done(true);
      } else {
        done(false);
      }
    });
  }, 300);
};

// 关闭弹窗
const handleClose = () => {
  emit('close');
  emit('update:visible', false);
  resetFormData();
};
</script>

<style scoped>
.arco-form {
  margin-bottom: 0;
}
</style>
