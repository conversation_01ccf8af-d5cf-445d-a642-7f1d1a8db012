<template>
  <a-modal
    :visible="visible"
    @update:visible="visible = $event"
    :title="title"
    width="70%"
    :footer="true"
    @cancel="handleClose"
  >
    <template #footer>
      <div class="flex justify-end space-x-2">
        <a-button @click="resetForm">重置</a-button>
        <a-button @click="handleClose">取消</a-button>
        <a-button type="primary" status="primary" @click="handleSave">确定</a-button>
      </div>
    </template>
    <a-form :model="form" ref="formRef" :model-value="form" layout="horizontal" :label-col-props="{ span: 4, style: { textAlign: 'left' } }" :wrapper-col-props="{ span: 20 }">
      <!-- 基础资料 -->
      <div class="mb-4">
        <div class="text-lg font-bold mb-2">基础资料</div>
        <div class="bg-gray-50 p-4 rounded-lg">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="name" label="客户名称" required>
                <a-input v-model="form.name" placeholder="请输入客户名称" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="industry" label="所属行业" required>
                <a-select v-model="form.industry" placeholder="请选择行业" style="width: 100%">
                  <a-option v-for="item in industryOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item field="source" label="客户来源">
                <a-select v-model="form.source" placeholder="请选择来源" style="width: 100%">
                  <a-option v-for="item in sourceOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="status" label="客户状态" required>
                <a-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                  <a-option v-for="item in statusOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="scale" label="企业规模">
                <a-select v-model="form.scale" placeholder="请选择企业规模" style="width: 100%">
                  <a-option v-for="item in scaleOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="star" label="客户星级" required>
                <a-rate v-model="form.star" :count="5" allow-half />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
      
      <!-- 联系信息 -->
      <div class="mb-4">
        <div class="text-lg font-bold mb-2">联系信息</div>
        <div class="bg-gray-50 p-4 rounded-lg">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="contact" label="联系人姓名" required>
                <a-input v-model="form.contact" placeholder="请输入联系人姓名" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="phone" label="联系电话" required>
                <a-input v-model="form.phone" placeholder="请输入联系电话" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="wechat" label="微信号">
                <a-input v-model="form.wechat" placeholder="请输入微信号" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="department" label="所属部门">
                <a-input v-model="form.department" placeholder="请输入所属部门" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="position" label="公司职位">
                <a-input v-model="form.position" placeholder="请输入公司职位" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="email" label="电子邮箱">
                <a-input v-model="form.email" placeholder="请输入电子邮箱" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="birthday" label="生日">
                <a-date-picker v-model="form.birthday" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="region" label="所在地区">
                <a-cascader
                  v-model="form.region"
                  :options="regionOptions"
                  placeholder="请选择所在地区"
                  expand-trigger="hover"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="address" label="详细地址">
                <a-textarea v-model="form.address" placeholder="请输入详细地址" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="remark" label="备注">
                <a-textarea v-model="form.remark" placeholder="请输入备注信息" style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="attachments" label="附件" :style="{ textAlign: 'left' }">
                <a-upload
                  action="/api/upload"
                  :file-list="form.attachments"
                  @change="handleAttachmentChange"
                  multiple
                  :limit="5"
                  :auto-upload="false"
                  :style="{ textAlign: 'left' }"
                >
                  <template #upload-button>
                    <a-button>
                      <template #icon><icon-upload /></template>
                      选择文件
                    </a-button>
                  </template>
                </a-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
      
      <!-- 跟进信息 -->
      <div class="mb-4">
        <div class="text-lg font-bold mb-2">跟进信息</div>
        <div class="bg-gray-50 p-4 rounded-lg">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item field="salesId" label="跟进人员" required>
                <a-select
                  v-model="form.salesId"
                  placeholder="请选择跟进人员"
                  :loading="salesLoading"
                  multiple
                  allow-clear
                >
                  <a-option v-for="item in salesOptions" :key="item.value" :value="item.value">
                    {{ item.label }}
                  </a-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="followTime" label="跟进时间" required>
                <a-date-picker v-model="form.followTime" show-time style="width: 100%" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item field="followContent" label="跟进内容" required :style="{ textAlign: 'left' }">
                <a-textarea v-model="form.followContent" placeholder="请输入跟进内容" :auto-size="{ minRows: 3, maxRows: 6 }" style="width: 100%; text-align: left" />
              </a-form-item>
            </a-col>
          </a-row>
        </div>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import dayjs from 'dayjs';

// 控制弹窗显示
const visible = ref(false);
const title = ref('新增客户');
const type = ref('add'); // add 或 edit

// 表单引用
const formRef = ref(null);

// 表单数据
const form = reactive({
  id: undefined,
  // 基础资料
  name: '',
  industry: undefined,
  status: undefined,
  star: 3,
  source: undefined,
  scale: undefined,
  // 联系信息
  contact: '',
  department: '',
  position: '',
  phone: '',
  region: [],
  email: '',
  wechat: '',
  birthday: null,
  address: '',
  remark: '',
  attachments: [],
  // 跟进信息
  salesId: [],
  followTime: new Date(),
  followContent: ''
});

// 选项数据
const industryOptions = ref([
  { label: '互联网', value: 1 },
  { label: '金融', value: 2 },
  { label: '教育', value: 3 },
  { label: '医疗', value: 4 },
  { label: '制造业', value: 5 },
  { label: '服务业', value: 6 }
]);

const sourceOptions = ref([
  { label: '主动获取', value: 1 },
  { label: '推荐介绍', value: 2 },
  { label: '复购', value: 3 }
]);

const statusOptions = ref([
  { label: '了解产品', value: 1 },
  { label: '正在跟进', value: 2 },
  { label: '准备购买', value: 3 },
  { label: '复购', value: 4 }
]);

const scaleOptions = ref([
  { label: '初创企业', value: 1 },
  { label: '中小企业', value: 2 },
  { label: '中型企业', value: 3 },
  { label: '大型企业', value: 4 },
  { label: '集团企业', value: 5 }
]);

const regionOptions = ref([
  {
    value: '110000',
    label: '北京市',
    children: [
      {
        value: '110100',
        label: '北京市',
        children: [
          { value: '110101', label: '东城区' },
          { value: '110102', label: '西城区' },
          { value: '110105', label: '朝阳区' },
          { value: '110106', label: '丰台区' },
          { value: '110108', label: '海淀区' }
        ]
      }
    ]
  },
  {
    value: '440000',
    label: '广东省',
    children: [
      {
        value: '440100',
        label: '广州市',
        children: [
          { value: '440103', label: '荔湾区' },
          { value: '440104', label: '越秀区' },
          { value: '440105', label: '海珠区' },
          { value: '440106', label: '天河区' },
          { value: '440111', label: '白云区' },
          { value: '440112', label: '黄埔区' }
        ]
      },
      {
        value: '440300',
        label: '深圳市',
        children: [
          { value: '440303', label: '罗湖区' },
          { value: '440304', label: '福田区' },
          { value: '440305', label: '南山区' },
          { value: '440306', label: '宝安区' },
          { value: '440307', label: '龙岗区' },
          { value: '440308', label: '盐田区' }
        ]
      }
    ]
  }
]);

// 销售人员选项
const salesOptions = ref([]);
const salesLoading = ref(false);

// 打开弹窗
const open = (record) => {
  if (record && record.id) {
    // 编辑模式
    title.value = '编辑客户';
    type.value = 'edit';
    // 填充表单数据
    for (const key in form) {
      if (Object.prototype.hasOwnProperty.call(record, key)) {
        form[key] = record[key];
      }
    }
  } else {
    // 新增模式
    title.value = '新增客户';
    type.value = 'add';
    // 重置表单
    resetFormData();
    // 设置默认值
    form.star = 3;
    form.followTime = new Date();
  }
  
  // 加载销售人员选项
  loadSalesOptions();
  
  // 显示弹窗
  visible.value = true;
};

// 加载销售人员选项
const loadSalesOptions = async () => {
  salesLoading.value = true;
  
  try {
    // 这里应该调用实际的API
    // const res = await getSalesList();
    // salesOptions.value = res.data.map(item => ({
    //   label: item.name,
    //   value: item.id
    // }));
    
    // 模拟数据
    setTimeout(() => {
      salesOptions.value = [
        { label: '张三', value: 1 },
        { label: '李四', value: 2 },
        { label: '王五', value: 3 },
        { label: '赵六', value: 4 },
        { label: '刘七', value: 5 }
      ];
      salesLoading.value = false;
    }, 500);
  } catch (error) {
    console.error('加载销售人员失败', error);
    Message.error('加载销售人员失败');
    salesLoading.value = false;
  }
};

// 重置表单数据
const resetFormData = () => {
  for (const key in form) {
    if (typeof form[key] === 'string') {
      form[key] = '';
    } else if (Array.isArray(form[key])) {
      form[key] = [];
    } else if (form[key] instanceof Date) {
      form[key] = new Date();
    } else {
      form[key] = undefined;
    }
  }
};

// 重置表单
const resetForm = () => {
  formRef.value.resetFields();
  form.star = 3;
  form.followTime = new Date();
  form.attachments = [];
  form.region = [];
};

// 处理附件变更
const handleAttachmentChange = (fileList) => {
  form.attachments = fileList;
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// 保存表单
const handleSave = async () => {
  try {
    // 调用保存方法
    const result = await handleSaveCustomer();
    if (result) {
      // 显示成功消息
      Message.success(`${type.value === 'add' ? '新增' : '编辑'}客户成功`);
    }
  } catch (error) {
    console.error(`${type.value === 'add' ? '新增' : '编辑'}客户失败`, error);
    Message.error(`${type.value === 'add' ? '新增' : '编辑'}客户失败`);
  }
};

// 定义暴露给父组件的事件
const emit = defineEmits(['save']);

// 保存客户数据
const handleSaveCustomer = async () => {
  try {
    // 验证表单
    const errors = await formRef.value.validate();
    if (errors) return false;

    // 检查必填字段
    if (!form.name) {
      Message.error('客户名称不能为空');
      return false;
    }
    if (!form.contact) {
      Message.error('联系人不能为空');
      return false;
    }
    if (!form.phone) {
      Message.error('联系电话不能为空');
      return false;
    }
    if (!form.salesId || form.salesId.length === 0) {
      Message.error('跟进人员不能为空');
      return false;
    }
    if (!form.followTime) {
      Message.error('跟进时间不能为空');
      return false;
    }
    if (!form.followContent) {
      Message.error('跟进内容不能为空');
      return false;
    }

    // 转换数据格式
    const customerData = { ...form };
    if (customerData.birthday) {
      customerData.birthday = dayjs(customerData.birthday).format('YYYY-MM-DD');
    }
    if (customerData.followTime) {
      customerData.followTime = dayjs(customerData.followTime).format('YYYY-MM-DD HH:mm:ss');
    }

    // 调用保存API
    // 在实际项目中这里应该调用真实的API
    // const api = form.id ? updateCustomer : addCustomer;
    // const res = await api(customerData);
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 通知父组件保存成功
    emit('save', customerData);
    
    // 关闭弹窗
    visible.value = false;
    
    return true;
  } catch (error) {
    console.error('保存客户失败:', error);
    Message.error('保存客户失败');
    return false;
  }
};

// 暴露方法给父组件
defineExpose({
  open,
});

</script>

<style scoped>
.form-tabs .arco-tabs-header {
  padding: 0 16px;
}
</style>
