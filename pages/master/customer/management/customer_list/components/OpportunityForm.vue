<template>
  <a-modal
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
    :title="title"
    @cancel="handleClose"
    @before-ok="handleSave"
  >
    <a-form :model="form" auto-label-width ref="formRef" layout="horizontal" :label-col-props="{ span: 4, style: { textAlign: 'left' } }" :wrapper-col-props="{ span: 20 }">
      <a-form-item field="name" label="商机名称" required>
        <a-input v-model="form.name" placeholder="请输入商机名称" style="width: 100%" />
      </a-form-item>
      <a-form-item field="amount" label="预计金额" required>
        <a-input-number v-model="form.amount" placeholder="请输入预计金额" style="width: 100%" />
      </a-form-item>
      <a-form-item field="stage" label="商机阶段" required>
        <a-select v-model="form.stage" placeholder="请选择商机阶段" style="width: 100%">
          <a-option value="初步接触">初步接触</a-option>
          <a-option value="需求确认">需求确认</a-option>
          <a-option value="方案制定">方案制定</a-option>
          <a-option value="商务谈判">商务谈判</a-option>
          <a-option value="合同签订">合同签订</a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="remark" label="备注">
        <a-textarea v-model="form.remark" placeholder="请输入备注信息" style="width: 100%" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import dayjs from 'dayjs';

// 接收父组件传递的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增商机'
  },
  customer: {
    type: Object,
    default: () => ({})
  },
  opportunity: {
    type: Object,
    default: () => ({})
  }
});

// 定义事件
const emit = defineEmits([
  'update:visible',
  'save',
  'close'
]);

// 表单引用
const formRef = ref(null);

// 表单数据
const form = reactive({
  id: undefined,
  customerId: undefined,
  name: '',
  amount: '',
  stage: '初步接触',
  owner: '当前用户', // 实际项目中应该获取当前登录用户
  createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
  remark: ''
});

// 监听客户信息变化，自动填充客户ID
watch(() => props.customer, (newVal) => {
  if (newVal && newVal.id) {
    form.customerId = newVal.id;
  }
}, { immediate: true });

// 重置表单
const resetForm = () => {
  form.id = undefined;
  form.customerId = props.customer?.id;
  form.name = '';
  form.amount = '';
  form.stage = '初步接触';
  form.owner = '当前用户';
  form.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
  form.remark = '';
};

// 监听商机信息变化，编辑模式时填充表单
watch(() => props.opportunity, (newVal) => {
  if (newVal && newVal.id) {
    form.id = newVal.id;
    form.customerId = newVal.customerId || props.customer?.id;
    form.name = newVal.name || '';
    form.amount = newVal.amount || '';
    form.stage = newVal.stage || '初步接触';
    form.owner = newVal.owner || '当前用户';
    form.createTime = newVal.createTime || dayjs().format('YYYY-MM-DD HH:mm:ss');
    form.remark = newVal.remark || '';
  } else {
    // 新增模式，重置表单
    resetForm();
  }
}, { immediate: true });

// 保存商机数据
const handleSave = (done) => {
  try {
    // 表单验证
    if (!form.name) {
      Message.error('请输入商机名称');
      done(false);
      return;
    }
    
    if (!form.amount) {
      Message.error('请输入预计金额');
      done(false);
      return;
    }
    
    if (!form.stage) {
      Message.error('请选择商机阶段');
      done(false);
      return;
    }
    
    // 构建保存数据
    const saveData = {
      ...form,
      customerId: form.customerId || props.customer?.id
    };
    
    // 实际项目中这里可以直接调用API进行保存
    // 这里演示直接在组件内进行模拟保存操作
    saveOpportunity(saveData, done);
  } catch (error) {
    console.error('保存商机失败', error);
    Message.error('保存商机失败');
    done(false);
  }
};

// 保存商机（模拟API调用）
const saveOpportunity = (data, done) => {
  // 模拟后端保存操作
  setTimeout(() => {
    // 准备保存的数据，包括ID生成
    const saveData = {
      ...data,
      // 如果是新增，生成一个临时ID
      id: data.id || Date.now()
    };
    
    // 触发父组件的保存事件
    emit('save', saveData, (success) => {
      if (success !== false) {
        Message.success(data.id ? '商机更新成功' : '商机创建成功');
        done();
      } else {
        done(false);
      }
    });
  }, 300);
};

// 关闭弹窗
const handleClose = () => {
  emit('close');
  emit('update:visible', false);
};
</script>

<style scoped>
.arco-form {
  margin-bottom: 0;
}
</style>
