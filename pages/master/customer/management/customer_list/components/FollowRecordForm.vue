<template>
  <a-modal
    :visible="visible"
    @update:visible="visible = $event"
    title="添加跟进记录"
    @cancel="handleClose"
    @before-ok="handleSave"
  >
    <a-form auto-label-width :model="form" ref="formRef" layout="horizontal" :label-col-props="{ span: 4, style: { textAlign: 'left' } }" :wrapper-col-props="{ span: 20 }" >
      <a-form-item field="time" label="跟进时间" required>
        <a-date-picker v-model="form.time" show-time style="width: 100%" />
      </a-form-item>
      <a-form-item field="status" label="客户状态" required>
        <a-select v-model="form.status" placeholder="请选择客户状态">
          <a-option v-for="item in statusOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-option>
        </a-select>
      </a-form-item>
      <a-form-item field="content" label="跟进内容" required>
        <a-textarea v-model="form.content" placeholder="请输入跟进内容" :auto-size="{ minRows: 3, maxRows: 6 }" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import dayjs from 'dayjs';

// 接收父组件传递的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '添加跟进记录'
  },
  customer: {
    type: Object,
    default: () => ({})
  },
  statusOptions: {
    type: Array,
    default: () => [
      { label: '了解产品', value: 1 },
      { label: '正在跟进', value: 2 },
      { label: '准备购买', value: 3 },
      { label: '复购', value: 4 }
    ]
  }
});

// 定义事件
const emit = defineEmits([
  'update:visible',
  'save',
  'close',
  'refresh'
]);

// 表单引用
const formRef = ref(null);

// 表单数据
const form = reactive({
  id: undefined,
  customerId: undefined,
  time: new Date(),
  user: '当前用户', // 实际项目中应该获取当前登录用户
  content: '',
  status: undefined
});

// 监听客户信息变化，自动填充客户ID和状态
watch(() => props.customer, (newVal) => {
  if (newVal && newVal.id) {
    form.customerId = newVal.id;
    form.status = newVal.status || 1;
  }
}, { immediate: true });

// 重置表单数据
const resetForm = () => {
  form.id = undefined;
  form.customerId = props.customer?.id;
  form.time = new Date();
  form.user = '当前用户';
  form.content = '';
  form.status = props.customer?.status || 1;
};

// 保存跟进记录数据
const saveFollowRecord = () => {
  try {
    // 验证数据
    if (!form.time) {
      Message.error('请选择跟进时间');
      return false;
    }

    if (!form.status) {
      Message.error('请选择客户状态');
      return false;
    }

    if (!form.content || form.content.trim() === '') {
      Message.error('请输入跟进内容');
      return false;
    }

    // 构建保存数据
    const saveData = {
      ...form,
      customerId: form.customerId || props.customer?.id,
      time: form.time instanceof Date ? dayjs(form.time).format('YYYY-MM-DD HH:mm:ss') : form.time,
      createdAt: new Date().toISOString()
    };
    
    // 如果是新增模式，生成一个临时ID
    if (!saveData.id) {
      saveData.id = Date.now();
    }

    return saveData;
  } catch (error) {
    console.error('保存跟进记录失败', error);
    Message.error('保存跟进记录失败');
    return false;
  }
};

// 保存表单
const handleSave = (done) => {
  const saveData = saveFollowRecord();
  
  if (!saveData) {
    done(false);
    return;
  }
  
  // 模拟API调用
  setTimeout(() => {
    // 通知父组件保存成功
    emit('save', saveData, (success) => {
      if (success !== false) {
        // 关闭弹窗
        emit('update:visible', false);
        Message.success('跟进记录保存成功');
        
        // 刷新数据
        emit('refresh');
        done(true);
      } else {
        done(false);
      }
    });
  }, 300);
};

// 关闭弹窗
const handleClose = () => {
  emit('close');
  emit('update:visible', false);
  resetForm();
};
</script>

<style scoped>
.arco-form {
  margin-bottom: 0;
}
</style>
