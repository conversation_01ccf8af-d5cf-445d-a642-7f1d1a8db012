<template>
  <a-modal
    :visible="visible"
    @update:visible="visible = $event"
    title="新增沟通记录"
    @cancel="handleClose"
    @before-ok="handleSave"
    :width="700"
  >
    <a-form :model="form" ref="formRef">
      <a-form-item field="contactId" label="联系人" required>
        <a-select 
          v-model="form.contactId" 
          placeholder="请选择联系人"
          style="width: 100%"
        >
          <a-option v-for="item in contactOptions" :key="item.id" :value="item.id">
            {{ item.name }}
          </a-option>
        </a-select>
        <div class="flex justify-end mt-1">
          <a-button type="text" size="small" @click="handleAddContact">
            <template #icon><icon-plus /></template>
            新增联系人
          </a-button>
        </div>
      </a-form-item>
      <a-form-item field="method" label="沟通方式" required>
        <a-radio-group v-model="form.method">
          <a-radio value="电话">电话</a-radio>
          <a-radio value="微信">微信</a-radio>
          <a-radio value="邮件">邮件</a-radio>
          <a-radio value="面谈">面谈</a-radio>
          <a-radio value="其他">其他</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="time" label="沟通时间" required>
        <a-date-picker v-model="form.time" show-time style="width: 100%" />
      </a-form-item>
      <a-form-item field="content" label="沟通内容" required>
        <div class="rich-text-editor border rounded" style="width: 100%">
          <div class="toolbar flex items-center p-1 border-b bg-gray-50">
            <a-space>
              <a-button size="mini" @click="applyFormat('bold')">
                <template #icon><icon-bold /></template>
              </a-button>
              <a-button size="mini" @click="applyFormat('italic')">
                <template #icon><icon-italic /></template>
              </a-button>
              <a-button size="mini" @click="applyFormat('underline')">
                <template #icon><icon-underline /></template>
              </a-button>
              <a-divider direction="vertical" />
              <a-button size="mini" @click="applyFormat('h1')">H1</a-button>
              <a-button size="mini" @click="applyFormat('h2')">H2</a-button>
              <a-button size="mini" @click="applyFormat('h3')">H3</a-button>
              <a-divider direction="vertical" />
              <a-button size="mini" @click="applyFormat('ul')">
                <template #icon><icon-unordered-list /></template>
              </a-button>
              <a-button size="mini" @click="applyFormat('ol')">
                <template #icon><icon-ordered-list /></template>
              </a-button>
              <a-divider direction="vertical" />
              <a-button size="mini" @click="openImageSelector">
                <template #icon><icon-image /></template>
              </a-button>
              <input type="file" ref="imageInput" accept="image/*" style="display: none" @change="insertImage" />
              <a-button size="mini" @click="clearFormat">
                <template #icon><icon-format-clear /></template>
              </a-button>
            </a-space>
          </div>
          <a-textarea
            v-model="form.content"
            placeholder="请输入沟通内容"
            class="border-0 focus:shadow-none w-full"
            ref="contentTextarea"
            :auto-size="{ minRows: 8, maxRows: 15 }"
          />
          <div v-if="previewMode" class="p-3 border-t bg-gray-50">
            <div class="text-xs text-gray-500 mb-1">预览：</div>
            <div class="rich-text-preview min-h-[150px] max-h-[300px] overflow-auto p-2" v-html="formattedContent"></div>
          </div>
          <div class="flex justify-end p-1 bg-gray-50 border-t">
            <a-switch v-model="previewMode" type="round" size="small">
              <template #checked>预览</template>
              <template #unchecked>编辑</template>
            </a-switch>
          </div>
        </div>
      </a-form-item>
      <a-form-item field="attachments" label="附件">
        <a-upload
          action="/api/upload"
          :file-list="form.attachments"
          @change="handleAttachmentChange"
          multiple
          :limit="5"
          :auto-upload="false"
        >
          <template #upload-button>
            <a-button type="primary">
              <template #icon><icon-upload /></template>
              选择文件
            </a-button>
          </template>
          <template #extra>
            <div class="text-gray-500 text-xs mt-1">支持上传最大 10MB 的文件，最多 5 个文件</div>
          </template>
        </a-upload>
      </a-form-item>
      <a-form-item field="nextPlan" label="下一步计划">
        <a-textarea v-model="form.nextPlan" placeholder="请输入下一步计划" rows="2" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import dayjs from 'dayjs';

// 接收父组件传递的属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '新增沟通记录'
  },
  customer: {
    type: Object,
    default: () => ({})
  },
  contacts: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits([
  'update:visible',
  'save',
  'close',
  'refresh',
  'add-contact'
]);

// 表单引用
const formRef = ref(null);
const contentTextarea = ref(null);
const imageInput = ref(null);

// 联系人选项 - 根据传入的contacts生成
const contactOptions = computed(() => {
  return props.contacts.map(item => ({
    id: item.id,
    name: item.name
  }));
});

// 表单数据
const form = reactive({
  contactId: undefined,
  method: '电话',
  time: new Date(),
  content: '',
  attachments: []
});

// 预览模式
const previewMode = ref(false);

// 计算格式化后的内容
const formattedContent = computed(() => {
  return form.content;
});

// 监听客户信息变化，自动填充客户ID
watch(() => props.customer, (newVal) => {
  if (newVal && newVal.id) {
    form.customerId = newVal.id;
  }
}, { immediate: true });

// 重置表单
const resetForm = () => {
  form.contactId = undefined;
  form.customerId = props.customer?.id;
  form.method = '电话';
  form.time = new Date();
  form.content = '';
  form.attachments = [];
};

// 添加联系人
const handleAddContact = () => {
  // 触发新增联系人事件
  emit('add-contact');
};

// 应用格式
const applyFormat = (type) => {
  const textarea = contentTextarea.value?.$el?.querySelector('textarea');
  if (!textarea) return;
  
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const selectedText = form.content.substring(start, end);
  let replacement = '';
  let newStart = start;
  let newEnd = end;
  
  switch (type) {
    case 'bold':
      replacement = `<strong>${selectedText}</strong>`;
      newStart = start + 8;
      newEnd = end + 8;
      break;
    case 'italic':
      replacement = `<em>${selectedText}</em>`;
      newStart = start + 4;
      newEnd = end + 4;
      break;
    case 'underline':
      replacement = `<u>${selectedText}</u>`;
      newStart = start + 3;
      newEnd = end + 3;
      break;
    case 'h1':
      replacement = `<h1>${selectedText}</h1>`;
      newStart = start + 4;
      newEnd = end + 4;
      break;
    case 'h2':
      replacement = `<h2>${selectedText}</h2>`;
      newStart = start + 4;
      newEnd = end + 4;
      break;
    case 'h3':
      replacement = `<h3>${selectedText}</h3>`;
      newStart = start + 4;
      newEnd = end + 4;
      break;
    case 'ul':
      if (selectedText.includes('\n')) {
        const lines = selectedText.split('\n');
        replacement = '<ul>\n' + lines.map(line => `  <li>${line}</li>`).join('\n') + '\n</ul>';
      } else {
        replacement = `<ul>\n  <li>${selectedText}</li>\n</ul>`;
      }
      break;
    case 'ol':
      if (selectedText.includes('\n')) {
        const lines = selectedText.split('\n');
        replacement = '<ol>\n' + lines.map(line => `  <li>${line}</li>`).join('\n') + '\n</ol>';
      } else {
        replacement = `<ol>\n  <li>${selectedText}</li>\n</ol>`;
      }
      break;
    default:
      return;
  }
  
  // 替换文本
  form.content = form.content.substring(0, start) + replacement + form.content.substring(end);
  
  // 恢复焦点并设置选择范围
  setTimeout(() => {
    textarea.focus();
    if (['ul', 'ol'].includes(type)) {
      // 对于列表，我们选择整个列表
      textarea.setSelectionRange(start, start + replacement.length);
    } else {
      // 对于其他格式，我们选择内部文本
      textarea.setSelectionRange(newStart, newEnd);
    }
  }, 0);
};

// 打开图片选择器
const openImageSelector = () => {
  imageInput.value.click();
};

// 插入图片
const insertImage = (event) => {
  const files = event.target.files;
  if (!files || !files.length) return;
  
  const file = files[0];
  const reader = new FileReader();
  
  reader.onload = (e) => {
    const imgTag = `<img src="${e.target.result}" alt="Uploaded Image" style="max-width: 100%;" />`;
    const textarea = contentTextarea.value?.$el?.querySelector('textarea');
    
    if (textarea) {
      const start = textarea.selectionStart;
      form.content = form.content.substring(0, start) + imgTag + form.content.substring(start);
      
      // 重置文件输入，以便同一文件可以再次选择
      imageInput.value.value = '';
      
      // 将焦点重新放在文本区域并移动到图片后
      setTimeout(() => {
        textarea.focus();
        const newPosition = start + imgTag.length;
        textarea.setSelectionRange(newPosition, newPosition);
      }, 0);
    }
  };
  
  reader.readAsDataURL(file);
};

// 清除格式
const clearFormat = () => {
  const textarea = contentTextarea.value?.$el?.querySelector('textarea');
  if (!textarea) return;
  
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  const selectedText = form.content.substring(start, end);
  
  // 使用正则表达式移除HTML标签
  const cleanText = selectedText.replace(/<[^>]*>/g, '');
  
  // 替换文本
  form.content = form.content.substring(0, start) + cleanText + form.content.substring(end);
  
  // 恢复焦点并设置选择范围
  setTimeout(() => {
    textarea.focus();
    const newEnd = start + cleanText.length;
    textarea.setSelectionRange(start, newEnd);
  }, 0);
};

// 处理附件变更
const handleAttachmentChange = (fileList) => {
  form.attachments = fileList;
};

// 保存沟通记录（完整业务逻辑实现）
const saveCommunication = () => {
  try {
    // 验证数据
    if (!form.contactId) {
      Message.error('请选择联系人');
      return false;
    }

    if (!form.method) {
      Message.error('请选择沟通方式');
      return false;
    }

    if (!form.time) {
      Message.error('请选择沟通时间');
      return false;
    }

    if (!form.content || form.content.trim() === '') {
      Message.error('请输入沟通内容');
      return false;
    }

    // 构建保存数据
    const saveData = {
      id: Date.now(), // 模拟ID生成
      customerId: props.customer?.id,
      contactId: form.contactId,
      contactName: contactOptions.value.find(item => item.id === form.contactId)?.name || '',
      method: form.method,
      time: form.time instanceof Date ? dayjs(form.time).format('YYYY-MM-DD HH:mm:ss') : form.time,
      content: form.content,
      attachments: form.attachments || [],
      user: '当前用户' // 实际应该获取当前登录用户
    };

    return saveData;
  } catch (error) {
    console.error('保存沟通记录失败', error);
    Message.error('保存沟通记录失败');
    return false;
  }
};

// 保存表单
const handleSave = (done) => {
  const saveData = saveCommunication();
  
  if (!saveData) {
    done(false);
    return;
  }
  
  // 直接在组件内部模拟API调用
  setTimeout(() => {
    // 通知父组件保存成功
    emit('save', saveData);
    
    // 通知父组件刷新数据
    emit('refresh');
    
    // 关闭弹窗
    emit('update:visible', false);
    
    Message.success('添加沟通记录成功');
    done(true);
  }, 300);
};

// 关闭弹窗
const handleClose = () => {
  emit('close');
  emit('update:visible', false);
  resetForm();
};
</script>

<style scoped>
.arco-form {
  margin-bottom: 0;
}

.rich-text-preview {
  line-height: 1.5;
}

.rich-text-preview img {
  max-width: 100%;
  height: auto;
}

.rich-text-editor {
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  overflow: hidden;
}

.rich-text-preview img {
  max-width: 100%;
  height: auto;
}
</style>
