<!--
 - 我的客户页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <a-tabs v-model:active-key="activeTab" @tab-click="handleTabChange">
      <a-tab-pane key="all" title="全部客户">
        <ma-crud :options="crud" :columns="columns" ref="crudRef">
          <!-- 自定义客户名称列 -->
          <template #name="{ record }">
            <a-link @click="viewDetail(record)">{{ record.name }}</a-link>
          </template>
          
          <!-- 自定义跟进状态列 -->
          <template #followUpStatus="{ record }">
            <a-tag :color="getFollowUpStatusColor(record.followUpStatus)">
              {{ getFollowUpStatusText(record.followUpStatus) }}
            </a-tag>
          </template>
          
          <!-- 自定义最近沟通时间列 -->
          <template #lastContactTime="{ record }">
            <div>
              <div>{{ record.lastContactTime || '暂无记录' }}</div>
              <div v-if="record.nextContactTime" class="text-xs text-gray-500">
                下次：{{ record.nextContactTime }}
              </div>
            </div>
          </template>
          
          <!-- 自定义操作列 -->
          <template #operation="{ record }">
            <a-button type="text" size="small" @click="viewDetail(record)">
              <template #icon><icon-eye /></template>查看
            </a-button>
            <a-button type="text" size="small" @click="addCommunication(record)">
              <template #icon><icon-message /></template>沟通
            </a-button>
            <a-button type="text" size="small" @click="handleTransfer(record)">
              <template #icon><icon-swap /></template>转交
            </a-button>
          </template>

          <template #tableBeforeButtons>
            <a-button
              type="primary" status="primary"
              class="w-full lg:w-auto mt-2 lg:mt-0"
              @click="handleAddCustomer"
            >
              <template #icon><icon-plus /></template>
              新增客户
            </a-button>
          </template>
        </ma-crud>
      </a-tab-pane>
      <a-tab-pane key="today" title="今日跟进">
        <ma-crud :options="crud" :columns="columns" ref="crudRef">
          <!-- 与全部客户相同的插槽 -->
          <template #name="{ record }">
            <a-link @click="viewDetail(record)">{{ record.name }}</a-link>
          </template>
          <template #followUpStatus="{ record }">
            <a-tag :color="getFollowUpStatusColor(record.followUpStatus)">
              {{ getFollowUpStatusText(record.followUpStatus) }}
            </a-tag>
          </template>
          <template #lastContactTime="{ record }">
            <div>
              <div>{{ record.lastContactTime || '暂无记录' }}</div>
              <div v-if="record.nextContactTime" class="text-xs text-gray-500">
                下次：{{ record.nextContactTime }}
              </div>
            </div>
          </template>
          <template #operation="{ record }">
            <a-button type="text" size="small" @click="viewDetail(record)">
              <template #icon><icon-eye /></template>查看
            </a-button>
            <a-button type="text" size="small" @click="addCommunication(record)">
              <template #icon><icon-message /></template>沟通
            </a-button>
            <a-button type="text" size="small" @click="handleTransfer(record)">
              <template #icon><icon-swap /></template>转交
            </a-button>
          </template>
          <template #tableBeforeButtons>
            <a-button
              type="primary" status="primary"
              class="w-full lg:w-auto mt-2 lg:mt-0"
              @click="handleAddCustomer"
            >
              <template #icon><icon-plus /></template>
              新增客户
            </a-button>
          </template>
        </ma-crud>
      </a-tab-pane>
      <a-tab-pane key="week" title="本周跟进">
        <ma-crud :options="crud" :columns="columns" ref="crudRef"></ma-crud>
      </a-tab-pane>
      <a-tab-pane key="month" title="本月跟进">
        <ma-crud :options="crud" :columns="columns" ref="crudRef"></ma-crud>
      </a-tab-pane>
      <a-tab-pane key="overdue" title="已逾期">
        <ma-crud :options="crud" :columns="columns" ref="crudRef"></ma-crud>
      </a-tab-pane>
    </a-tabs>

    <!-- 新增客户弹窗 -->
    <a-modal
      v-model:visible="customerModal.visible"
      title="新增客户"
      @cancel="customerModal.visible = false"
      @before-ok="handleSaveCustomer"
    >
      <a-form :model="customerForm" ref="customerFormRef">
        <a-form-item field="name" label="客户名称" required>
          <a-input v-model="customerForm.name" placeholder="请输入客户名称" />
        </a-form-item>
        <a-form-item field="contact" label="联系人">
          <a-input v-model="customerForm.contact" placeholder="请输入联系人" />
        </a-form-item>
        <a-form-item field="phone" label="联系电话">
          <a-input v-model="customerForm.phone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item field="email" label="电子邮箱">
          <a-input v-model="customerForm.email" placeholder="请输入电子邮箱" />
        </a-form-item>
        <a-form-item field="industry" label="所属行业">
          <a-select v-model="customerForm.industry" placeholder="请选择行业">
            <a-option v-for="item in industryOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="address" label="详细地址">
          <a-textarea v-model="customerForm.address" placeholder="请输入详细地址" />
        </a-form-item>
        <a-form-item field="nextContactTime" label="下次跟进时间">
          <a-date-picker 
            v-model="customerForm.nextContactTime" 
            placeholder="请选择下次跟进时间" 
            show-time 
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item field="remark" label="备注">
          <a-textarea v-model="customerForm.remark" placeholder="请输入备注信息" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 转交客户弹窗 -->
    <a-modal
      v-model:visible="transferModal.visible"
      title="转交客户"
      @cancel="transferModal.visible = false"
      @before-ok="handleTransferSubmit"
    >
      <a-form :model="transferForm">
        <a-form-item field="customerId" label="客户名称">
          <a-input v-model="transferForm.customerName" disabled />
        </a-form-item>
        <a-form-item field="targetUserId" label="转交给" required>
          <a-select v-model="transferForm.targetUserId" placeholder="请选择接收人">
            <a-option v-for="item in userOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="reason" label="转交原因">
          <a-textarea v-model="transferForm.reason" placeholder="请输入转交原因" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';


definePageMeta({
  name: "master-my_customers",
  path: "/master/customer/management/my_customers",
})


const router = useRouter();
const activeTab = ref('all');

// ma-crud引用
const crudRef = ref(null);

// 表格列定义
const columns = [
  {
    title: '客户名称',
    dataIndex: 'name',
    width: 180,
    search: true,
    slotName: 'name',
  },
  {
    title: '联系人',
    dataIndex: 'contact',
    width: 120,
    search: true,
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    width: 150,
    search: true,
  },
  {
    title: '所属行业',
    dataIndex: 'industryName',
    width: 120,
  },
  {
    title: '跟进状态',
    dataIndex: 'followUpStatus',
    width: 120,
    search: true,
    slotName: 'followUpStatus',
  },
  {
    title: '最近沟通时间',
    dataIndex: 'lastContactTime',
    width: 180,
    slotName: 'lastContactTime',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 200,
    fixed: 'right',
  },
];

// CRUD配置
const crud = reactive({
  // API配置 - 这里需要替换为实际的API
  api: async (params) => {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟数据
    const data = {
      items: [
        {
          id: 1,
          name: '阿里巴巴',
          contact: '马云',
          phone: '13800138000',
          email: '<EMAIL>',
          industryName: '互联网',
          industry: 1,
          followUpStatus: 4,
          lastContactTime: '2025-05-01 14:30:00',
          nextContactTime: '2025-05-10 10:00:00',
          address: '杭州市余杭区文一西路969号',
          remark: '商务谈判中，需要准备详细方案'
        },
        {
          id: 2,
          name: '腾讯科技',
          contact: '马化腾',
          phone: '13900139000',
          email: '<EMAIL>',
          industryName: '互联网',
          industry: 1,
          followUpStatus: 3,
          lastContactTime: '2025-05-03 16:00:00',
          nextContactTime: '2025-05-12 14:00:00',
          address: '深圳市南山区高新科技园',
          remark: '方案制定中，需要技术支持'
        },
        {
          id: 3,
          name: '百度科技',
          contact: '李彦宏',
          phone: '13700137000',
          email: '<EMAIL>',
          industryName: '互联网',
          industry: 1,
          followUpStatus: 5,
          lastContactTime: '2025-04-28 11:00:00',
          nextContactTime: '2025-05-15 09:30:00',
          address: '北京市海淀区西北旺东路10号',
          remark: '合同已签订，准备交付'
        }
      ],
      total: 3
    };
    
    // 根据当前标签页过滤数据
    let filteredItems = [...data.items];
    if (activeTab.value !== 'all') {
      // 实际项目中应该在后端进行过滤
      if (activeTab.value === 'today') {
        // 仅模拟数据处理
        filteredItems = filteredItems.filter(item => {
          const nextDate = dayjs(item.nextContactTime);
          return nextDate.isSame(dayjs(), 'day');
        });
      }
    }
    
    return {
      code: 200,
      message: '操作成功',
      data: {
        items: filteredItems,
        total: filteredItems.length
      }
    };
  },
  
  // 表格配置
  table: {
    rowKey: 'id',
    bordered: true,
    size: 'small',
    pagination: {
      showTotal: true,
      showJumper: true,
      showPageSize: true,
    },
  },
  
  // 数据处理配置
  dataField: 'data.items',
  totalField: 'data.total',
  showIndex: true,
  pageLayout: 'fixed',
  
  // 搜索配置
  search: {
    labelWidth: 80,
    showResetButton: true,
    collapsed: false,
    formProps: {
      labelAlign: 'right',
    },
  },
  
  // 操作按钮配置
  rowSelection: {
    showCheckedAll: true,
  },
  add: { show: false },
  edit: { show: false },
  delete: { show: false },
  export: { show: false },
  import: { show: false },
  refresh: { show: true },
  columnSetting: { show: true },
});

// 行业选项
const industryOptions = ref([
  { label: '互联网', value: 1 },
  { label: '金融', value: 2 },
  { label: '教育', value: 3 },
  { label: '医疗', value: 4 },
  { label: '制造业', value: 5 },
  { label: '零售', value: 6 },
  { label: '其他', value: 7 },
]);

// 跟进状态选项
const followUpStatusOptions = ref([
  { label: '初次接触', value: 1 },
  { label: '需求确认', value: 2 },
  { label: '方案制定', value: 3 },
  { label: '商务谈判', value: 4 },
  { label: '合同签订', value: 5 },
  { label: '售后服务', value: 6 },
  { label: '已流失', value: 7 },
]);

// 用户选项（用于转交）
const userOptions = ref([
  { label: '张三', value: 1 },
  { label: '李四', value: 2 },
  { label: '王五', value: 3 },
]);

// 客户表单弹窗
const customerModal = reactive({
  visible: false,
});

// 客户表单
const customerForm = reactive({
  name: '',
  contact: '',
  phone: '',
  email: '',
  industry: undefined,
  address: '',
  nextContactTime: null,
  remark: ''
});
const customerFormRef = ref(null);

// 转交弹窗
const transferModal = reactive({
  visible: false
});

// 转交表单
const transferForm = reactive({
  customerId: undefined,
  customerName: '',
  targetUserId: undefined,
  reason: ''
});

// 获取跟进状态文本
const getFollowUpStatusText = (status) => {
  const option = followUpStatusOptions.value.find(item => item.value === status);
  return option ? option.label : '未知状态';
};

// 获取跟进状态颜色
const getFollowUpStatusColor = (status) => {
  const colorMap = {
    1: 'blue',    // 初次接触
    2: 'cyan',    // 需求确认
    3: 'green',   // 方案制定
    4: 'orange',  // 商务谈判
    5: 'purple',  // 合同签订
    6: 'gray',    // 售后服务
    7: 'red',     // 已流失
  };
  return colorMap[status] || 'default';
};

// 标签页切换
const handleTabChange = () => {
  // 刷新数据
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 刷新数据
const refresh = () => {
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 新增客户
const handleAddCustomer = () => {
  for (const key in customerForm) {
    customerForm[key] = '';
  }
  customerForm.industry = undefined;
  customerForm.nextContactTime = null;
  customerModal.visible = true;
};

// 保存客户
const handleSaveCustomer = async (done) => {
  try {
    // 这里应该调用实际的API
    // await addCustomer(customerForm);
    
    Message.success('新增客户成功');
    refresh();
    done();
  } catch (error) {
    console.error('新增客户失败', error);
    Message.error('新增客户失败');
    done(false);
  }
};

// 查看客户详情
const viewDetail = (record) => {
  
  router.push(`/master/customer/management/my_customers/detail/${record.id}`);
};

// 添加沟通记录
const addCommunication = (record) => {
  router.push(`/master/customer/management/my_customers/communication/${record.id}`);
};

// 打开转交弹窗
const handleTransfer = (record) => {
  transferForm.customerId = record.id;
  transferForm.customerName = record.name;
  transferForm.targetUserId = undefined;
  transferForm.reason = '';
  transferModal.visible = true;
};

// 处理转交提交
const handleTransferSubmit = async (done) => {
  try {
    // 这里应该调用实际的API
    // await transferCustomer(transferForm);
    
    Message.success('转交客户成功');
    refresh();
    done();
  } catch (error) {
    console.error('转交客户失败', error);
    Message.error('转交客户失败');
    done(false);
  }
};

// 页面加载时初始化
onMounted(() => {
  // 页面加载时不需要手动获取数据，ma-crud会自动调用api函数
});
</script>

<style scoped>
.arco-form {
  margin-bottom: 0;
}
</style>
