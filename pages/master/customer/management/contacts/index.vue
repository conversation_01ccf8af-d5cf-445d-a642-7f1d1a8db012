<!--
 - 联系人管理页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义联系人姓名列 -->
      <template #name="{ record }">
        <a-link @click="viewDetail(record)">{{ record.name }}</a-link>
      </template>
      
      <!-- 自定义重要程度列 -->
      <template #importance="{ record }">
        <a-rate :model-value="record.importance" readonly allow-half :count="3" />
      </template>
      
      <!-- 自定义操作列 -->
      <template #operation="{ record }">
        <a-button type="text" size="small" @click="viewDetail(record)">
          <template #icon><icon-eye /></template>查看
        </a-button>
        <a-button type="text" size="small" @click="editContact(record)">
          <template #icon><icon-edit /></template>编辑
        </a-button>
        <a-button type="text" size="small" @click="addCommunication(record)">
          <template #icon><icon-message /></template>沟通
        </a-button>
      </template>

      <template #tableBeforeButtons>
        <a-button
          type="primary" status="primary"
          class="w-full lg:w-auto mt-2 lg:mt-0"
          @click="handleAddContact"
        >
          <template #icon><icon-plus /></template>
          新增联系人
        </a-button>
      </template>
    </ma-crud>

    <!-- 新增/编辑联系人弹窗 -->
    <a-modal
      v-model:visible="contactModal.visible"
      :title="contactModal.title"
      @cancel="contactModal.visible = false"
      @before-ok="handleSaveContact"
    >
      <a-form :model="contactForm" ref="contactFormRef">
        <a-form-item field="name" label="联系人姓名" required>
          <a-input v-model="contactForm.name" placeholder="请输入联系人姓名" />
        </a-form-item>
        <a-form-item field="customerId" label="所属客户" required>
          <a-select 
            v-model="contactForm.customerId" 
            placeholder="请选择所属客户"
            :filter-option="true"
            :loading="customerLoading"
          >
            <a-option v-for="item in customerOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="phone" label="联系电话">
          <a-input v-model="contactForm.phone" placeholder="请输入联系电话" />
        </a-form-item>
        <a-form-item field="email" label="电子邮箱">
          <a-input v-model="contactForm.email" placeholder="请输入电子邮箱" />
        </a-form-item>
        <a-form-item field="position" label="职位">
          <a-input v-model="contactForm.position" placeholder="请输入职位" />
        </a-form-item>
        <a-form-item field="department" label="部门">
          <a-input v-model="contactForm.department" placeholder="请输入部门" />
        </a-form-item>
        <a-form-item field="importance" label="重要程度">
          <a-rate v-model="contactForm.importance" allow-half :count="3" />
        </a-form-item>
        <a-form-item field="birthday" label="生日">
          <a-date-picker v-model="contactForm.birthday" placeholder="请选择生日" style="width: 100%" />
        </a-form-item>
        <a-form-item field="wechat" label="微信号">
          <a-input v-model="contactForm.wechat" placeholder="请输入微信号" />
        </a-form-item>
        <a-form-item field="remark" label="备注">
          <a-textarea v-model="contactForm.remark" placeholder="请输入备注信息" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';

definePageMeta({
  name: "master-contacts",
  path: "/master/customer/management/contacts",
});

const router = useRouter();
const customerLoading = ref(false);

// ma-crud引用
const crudRef = ref(null);

// 表格列定义
const columns = [
  {
    title: '联系人姓名',
    dataIndex: 'name',
    width: 150,
    search: true,
    slotName: 'name',
  },
  {
    title: '所属客户',
    dataIndex: 'customerName',
    width: 150,
    search: true,
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    width: 150,
    search: true,
  },
  {
    title: '职位',
    dataIndex: 'position',
    width: 120,
    search: true,
  },
  {
    title: '部门',
    dataIndex: 'department',
    width: 120,
  },
  {
    title: '电子邮箱',
    dataIndex: 'email',
    width: 180,
  },
  {
    title: '重要程度',
    dataIndex: 'importance',
    width: 120,
    slotName: 'importance',
  },
  {
    title: '最近沟通时间',
    dataIndex: 'lastContactTime',
    width: 180,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 300,
    fixed: 'right',
  },
];

// CRUD配置
const crud = reactive({
  // API配置 - 这里需要替换为实际的API
  api: async (params) => {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟数据
    const data = {
      items: [
        {
          id: 1,
          name: '张三',
          customerId: 1,
          customerName: '阿里巴巴',
          phone: '13800138001',
          email: '<EMAIL>',
          position: '采购经理',
          department: '采购部',
          importance: 3,
          birthday: '1985-01-15',
          wechat: 'zhangsan_ali',
          lastContactTime: '2025-05-01 14:30:00',
          remark: '决策者，关键联系人'
        },
        {
          id: 2,
          name: '李四',
          customerId: 1,
          customerName: '阿里巴巴',
          phone: '13800138002',
          email: '<EMAIL>',
          position: '技术总监',
          department: '技术部',
          importance: 2.5,
          birthday: '1988-03-22',
          wechat: 'lisi_tech',
          lastContactTime: '2025-04-28 10:00:00',
          remark: '技术评估负责人'
        },
        {
          id: 3,
          name: '王五',
          customerId: 2,
          customerName: '腾讯科技',
          phone: '13900139001',
          email: '<EMAIL>',
          position: '财务总监',
          department: '财务部',
          importance: 2,
          birthday: '1982-07-08',
          wechat: 'wangwu_tx',
          lastContactTime: '2025-05-03 16:00:00',
          remark: '负责预算审批'
        }
      ],
      total: 3
    };
    
    return {
      code: 200,
      message: '操作成功',
      data: {
        items: data.items,
        total: data.total
      }
    };
  },
  
  // 表格配置
  table: {
    rowKey: 'id',
    bordered: true,
    size: 'small',
    pagination: {
      showTotal: true,
      showJumper: true,
      showPageSize: true,
    },
  },
  
  // 数据处理配置
  dataField: 'data.items',
  totalField: 'data.total',
  showIndex: true,
  pageLayout: 'fixed',
  
  // 搜索配置
  search: {
    labelWidth: 80,
    showResetButton: true,
    collapsed: false,
    formProps: {
      labelAlign: 'right',
    },
  },
  
  // 操作按钮配置
  rowSelection: {
    showCheckedAll: true,
  },
  add: { show: false },
  edit: { show: false },
  delete: { show: false },
  export: { show: false },
  import: { show: false },
  refresh: { show: true },
  columnSetting: { show: true },
});

// 客户选项
const customerOptions = ref([]);

// 联系人弹窗
const contactModal = reactive({
  visible: false,
  title: '新增联系人',
  type: 'add' // add 或 edit
});

// 联系人表单
const contactForm = reactive({
  id: undefined,
  name: '',
  customerId: undefined,
  phone: '',
  email: '',
  position: '',
  department: '',
  importance: 1,
  birthday: null,
  wechat: '',
  remark: ''
});
const contactFormRef = ref(null);

// 刷新数据
const refresh = () => {
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 获取客户选项
const fetchCustomerOptions = async () => {
  customerLoading.value = true;
  try {
    // 这里应该调用实际的API
    // const res = await getCustomerOptions();
    // customerOptions.value = res.data;
    
    // 模拟数据
    setTimeout(() => {
      customerOptions.value = [
        { label: '阿里巴巴', value: 1 },
        { label: '腾讯科技', value: 2 },
        { label: '百度科技', value: 3 },
        { label: '京东科技', value: 4 },
        { label: '小米科技', value: 5 }
      ];
      customerLoading.value = false;
    }, 300);
  } catch (error) {
    console.error('获取客户选项失败', error);
    Message.error('获取客户选项失败');
    customerLoading.value = false;
  }
};

// 新增联系人
const handleAddContact = () => {
  contactModal.title = '新增联系人';
  contactModal.type = 'add';
  for (const key in contactForm) {
    contactForm[key] = '';
  }
  contactForm.id = undefined;
  contactForm.customerId = undefined;
  contactForm.importance = 1;
  contactForm.birthday = null;
  contactModal.visible = true;
  
  // 获取客户选项
  if (customerOptions.value.length === 0) {
    fetchCustomerOptions();
  }
};

// 编辑联系人
const editContact = (record) => {
  contactModal.title = '编辑联系人';
  contactModal.type = 'edit';
  for (const key in contactForm) {
    contactForm[key] = record[key];
  }
  contactModal.visible = true;
  
  // 获取客户选项
  if (customerOptions.value.length === 0) {
    fetchCustomerOptions();
  }
};

// 保存联系人
const handleSaveContact = async (done) => {
  try {
    // 这里应该调用实际的API
    // if (contactModal.type === 'add') {
    //   await addContact(contactForm);
    // } else {
    //   await updateContact(contactForm);
    // }
    
    Message.success(`${contactModal.type === 'add' ? '新增' : '编辑'}联系人成功`);
    refresh();
    done();
  } catch (error) {
    console.error(`${contactModal.type === 'add' ? '新增' : '编辑'}联系人失败`, error);
    Message.error(`${contactModal.type === 'add' ? '新增' : '编辑'}联系人失败`);
    done(false);
  }
};

// 查看联系人详情
const viewDetail = (record) => {
  router.push(`/master/customer/management/contacts/detail/${record.id}`);
};

// 添加沟通记录
const addCommunication = (record) => {
  router.push(`/master/customer/management/contacts/communication/${record.id}`);
};

// 页面加载时初始化
onMounted(() => {
  // 页面加载时不需要手动获取数据，ma-crud会自动调用api函数
});
</script>

<style scoped>
.arco-form {
  margin-bottom: 0;
}
</style>
