<!--
 - 行业分类管理页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 自定义行业名称列 -->
      <template #name="{ record }">
        <a-link @click="editIndustry(record)">{{ record.name }}</a-link>
      </template>
      
      <!-- 自定义状态列 -->
      <template #status="{ record }">
        <a-tag :color="record.status === 1 ? 'green' : 'red'">
          {{ record.status === 1 ? '启用' : '禁用' }}
        </a-tag>
      </template>
      
      <!-- 自定义操作列 -->
      <template #operation="{ record }">
        <a-button type="text" size="small" @click="editIndustry(record)">
          <template #icon><icon-edit /></template>编辑
        </a-button>
        <a-button 
          type="text" 
          size="small" 
          :status="record.status === 1 ? 'danger' : 'success'"
          @click="toggleStatus(record)"
        >
          <template #icon>
            <icon-poweroff v-if="record.status === 1" />
            <icon-check v-else />
          </template>
          {{ record.status === 1 ? '禁用' : '启用' }}
        </a-button>
        <a-button 
          type="text" 
          size="small" 
          status="danger" 
          @click="handleDelete(record)"
          :disabled="record.hasChildren"
        >
          <template #icon><icon-delete /></template>删除
        </a-button>
      </template>

      <template #tableBeforeButtons>
        <a-button
          type="primary" status="primary"
          class="w-full lg:w-auto mt-2 lg:mt-0"
          @click="handleAddIndustry"
        >
          <template #icon><icon-plus /></template>
          新增行业分类
        </a-button>
      </template>
    </ma-crud>

    <!-- 新增/编辑行业分类弹窗 -->
    <a-modal
      v-model:visible="industryModal.visible"
      :title="industryModal.title"
      @cancel="industryModal.visible = false"
      @before-ok="handleSaveIndustry"
    >
      <a-form :model="industryForm" ref="industryFormRef">
        <a-form-item field="parentId" label="上级行业">
          <a-tree-select
            v-model="industryForm.parentId"
            :data="industryTreeData"
            placeholder="请选择上级行业，不选择则为顶级行业"
            allow-clear
            tree-checkable="false"
            :loading="treeLoading"
          />
        </a-form-item>
        <a-form-item field="name" label="行业名称" required>
          <a-input v-model="industryForm.name" placeholder="请输入行业名称" />
        </a-form-item>
        <a-form-item field="code" label="行业代码" required>
          <a-input v-model="industryForm.code" placeholder="请输入行业代码" />
        </a-form-item>
        <a-form-item field="level" label="行业级别">
          <a-select v-model="industryForm.level" placeholder="请选择行业级别">
            <a-option v-for="item in levelOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="sort" label="排序">
          <a-input-number v-model="industryForm.sort" placeholder="请输入排序值" :min="0" :max="999" />
        </a-form-item>
        <a-form-item field="description" label="行业描述">
          <a-textarea v-model="industryForm.description" placeholder="请输入行业描述" />
        </a-form-item>
        <a-form-item field="status" label="状态">
          <a-radio-group v-model="industryForm.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';

// 页面元数据
definePageMeta({
  name: 'industry_classification_management',
  path: '/master/customer/management/industry_classification_management',
});

// 行业级别选项
const levelOptions = ref([
  { label: '一级行业', value: 1 },
  { label: '二级行业', value: 2 },
  { label: '三级行业', value: 3 },
]);

// 表格列定义
const columns = [
  {
    title: '行业名称',
    dataIndex: 'name',
    slotName: 'name',
    search: true,
  },
  {
    title: '行业代码',
    dataIndex: 'code',
    search: true,
  },
  {
    title: '行业级别',
    dataIndex: 'levelName',
    search: {
      name: 'level',
      component: 'select',
      componentProps: {
        options: levelOptions.value,
      },
    },
  },
  {
    title: '排序',
    dataIndex: 'sort',
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    slotName: 'operation',
    width: 300,
    fixed: 'right',
  },
];

// 行业树形数据
const treeLoading = ref(false);
const industryTreeData = ref([]);

// 行业弹窗
const industryModal = reactive({
  visible: false,
  title: '新增行业分类',
  type: 'add' // add 或 edit
});

// 行业表单
const industryForm = reactive({
  id: undefined,
  parentId: undefined,
  name: '',
  code: '',
  level: 1,
  sort: 0,
  description: '',
  status: 1
});
const industryFormRef = ref(null);

// ma-crud 引用
const crudRef = ref(null);


// ma-crud 配置
const crud = reactive({
  // API 配置
  api: async (params) => {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟数据
    const data = {
      items: [
        {
          id: 1,
          parentId: null,
          name: '制造业',
          code: 'MFG',
          level: 1,
          levelName: '一级行业',
          sort: 1,
          description: '制造业包括各种产品的生产和加工',
          status: 1,
          createTime: '2025-01-01 12:00:00',
          hasChildren: true
        },
        {
          id: 2,
          parentId: 1,
          name: '电子设备制造',
          code: 'MFG-ELE',
          level: 2,
          levelName: '二级行业',
          sort: 1,
          description: '电子设备制造包括电脑、手机等电子产品的生产',
          status: 1,
          createTime: '2025-01-01 12:30:00',
          hasChildren: false
        },
        {
          id: 3,
          parentId: null,
          name: '信息技术',
          code: 'IT',
          level: 1,
          levelName: '一级行业',
          sort: 2,
          description: '信息技术行业包括软件开发、IT服务等',
          status: 1,
          createTime: '2025-01-02 10:00:00',
          hasChildren: true
        },
        {
          id: 4,
          parentId: 3,
          name: '软件开发',
          code: 'IT-SW',
          level: 2,
          levelName: '二级行业',
          sort: 1,
          description: '软件开发包括各类应用软件、系统软件的开发',
          status: 1,
          createTime: '2025-01-02 10:30:00',
          hasChildren: false
        },
        {
          id: 5,
          parentId: 3,
          name: '云计算服务',
          code: 'IT-CLOUD',
          level: 2,
          levelName: '二级行业',
          sort: 2,
          description: '云计算服务包括IaaS、PaaS、SaaS等服务',
          status: 0,
          createTime: '2025-01-02 11:00:00',
          hasChildren: false
        }
      ],
      total: 5
    };
    
    // 过滤数据
    let filteredItems = [...data.items];
    
    // 按名称搜索
    if (params.name) {
      filteredItems = filteredItems.filter(item => 
        item.name.toLowerCase().includes(params.name.toLowerCase())
      );
    }
    
    // 按代码搜索
    if (params.code) {
      filteredItems = filteredItems.filter(item => 
        item.code.toLowerCase().includes(params.code.toLowerCase())
      );
    }
    
    // 按级别搜索
    if (params.level !== undefined && params.level !== null && params.level !== '') {
      filteredItems = filteredItems.filter(item => 
        item.level === Number(params.level)
      );
    }
    
    // 返回符合ma-crud期望的数据格式
    return {
      code: 200,
      message: '操作成功',
      data: {
        items: filteredItems,
        total: filteredItems.length
      }
    };
  },
  
  // 表格配置
  table: {
    rowKey: 'id',
    bordered: true,
    size: 'small',
    pagination: {
      showTotal: true,
      showJumper: true,
      showPageSize: true,
    },
  },
  
  // 数据处理配置
  dataField: 'data.items',
  totalField: 'data.total',
  showIndex: true,
  pageLayout: 'fixed',
  
  // 搜索配置
  search: {
    labelWidth: 80,
    showResetButton: true,
    collapsed: false,
    formProps: {
      labelAlign: 'right',
    },
  },
  
  // 操作按钮配置
  rowSelection: {
    showCheckedAll: true,
  },
  add: { show: false },
  edit: { show: false },
  delete: { show: false },
  refresh: { show: true },
  columnSetting: { show: true },
});

// 刷新数据
const refresh = () => {
  if (crudRef.value) {
    crudRef.value.refresh();
  }
};

// 获取行业树形数据
const fetchIndustryTree = async () => {
  treeLoading.value = true;
  try {
    // 这里应该调用实际的API
    // const res = await getIndustryTree();
    
    // 模拟数据
    setTimeout(() => {
      industryTreeData.value = [
        {
          key: 1,
          title: '制造业',
          children: [
            {
              key: 2,
              title: '电子设备制造',
            }
          ]
        },
        {
          key: 3,
          title: '信息技术',
          children: [
            {
              key: 4,
              title: '软件开发',
            },
            {
              key: 5,
              title: '云计算服务',
            }
          ]
        }
      ];
      treeLoading.value = false;
    }, 300);
  } catch (error) {
    console.error('获取行业树形数据失败', error);
    Message.error('获取行业树形数据失败');
    treeLoading.value = false;
  }
};

// 新增行业
const handleAddIndustry = () => {
  industryModal.title = '新增行业分类';
  industryModal.type = 'add';
  for (const key in industryForm) {
    industryForm[key] = '';
  }
  industryForm.id = undefined;
  industryForm.parentId = undefined;
  industryForm.level = 1;
  industryForm.sort = 0;
  industryForm.status = 1;
  industryModal.visible = true;
  
  // 获取行业树形数据
  fetchIndustryTree();
};

// 编辑行业
const editIndustry = (record) => {
  industryModal.title = '编辑行业分类';
  industryModal.type = 'edit';
  for (const key in industryForm) {
    industryForm[key] = record[key];
  }
  industryModal.visible = true;
  
  // 获取行业树形数据
  fetchIndustryTree();
};

// 保存行业
const handleSaveIndustry = async (done) => {
  try {
    // 这里应该调用实际的API
    // if (industryModal.type === 'add') {
    //   await addIndustry(industryForm);
    // } else {
    //   await updateIndustry(industryForm);
    // }
    
    Message.success(`${industryModal.type === 'add' ? '新增' : '编辑'}行业分类成功`);
    refresh();
    done();
  } catch (error) {
    console.error(`${industryModal.type === 'add' ? '新增' : '编辑'}行业分类失败`, error);
    Message.error(`${industryModal.type === 'add' ? '新增' : '编辑'}行业分类失败`);
    done(false);
  }
};

// 切换状态
const toggleStatus = (record) => {
  Modal.confirm({
    title: `确认${record.status === 1 ? '禁用' : '启用'}该行业分类吗？`,
    content: record.status === 1 ? '禁用后，该行业分类将不可用' : '启用后，该行业分类将可正常使用',
    onOk: async () => {
      try {
        // 这里应该调用实际的API
        // await updateIndustryStatus({
        //   id: record.id,
        //   status: record.status === 1 ? 0 : 1
        // });
        
        Message.success(`${record.status === 1 ? '禁用' : '启用'}行业分类成功`);
        refresh();
      } catch (error) {
        console.error(`${record.status === 1 ? '禁用' : '启用'}行业分类失败`, error);
        Message.error(`${record.status === 1 ? '禁用' : '启用'}行业分类失败`);
      }
    }
  });
};

// 删除行业
const handleDelete = (record) => {
  if (record.hasChildren) {
    Message.warning('该行业下存在子行业，无法删除');
    return;
  }
  
  Modal.confirm({
    title: '确认删除该行业分类吗？',
    content: '删除后将无法恢复，请谨慎操作',
    onOk: async () => {
      try {
        // 这里应该调用实际的API
        // await deleteIndustry(record.id);
        
        Message.success('删除行业分类成功');
        refresh();
      } catch (error) {
        console.error('删除行业分类失败', error);
        Message.error('删除行业分类失败');
      }
    }
  });
};
</script>

<style scoped>
.arco-form {
  margin-bottom: 0;
}
</style>
