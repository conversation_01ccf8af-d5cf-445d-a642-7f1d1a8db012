<!--
 - 客户工作台页面（重构版）
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <div class="mb-4">
      <div class="text-xl font-bold">工作台</div>
    </div>

    <!-- 数据统计 -->
    <statistics-overview 
      :statistics="statistics" 
      :today-data="todayData" 
      class="mb-6"
    />

    <!-- 待办事项 -->
    <todo-tasks 
      :data="todoTasksRef" 
      class="mb-6"
    />

    <!-- 团队概况 -->
    <team-overview 
      :data="teamOverviewDataRef" 
      class="mb-6"
    />

    <!-- 我的概况 -->
    <personal-overview 
      :data="personalOverviewDataRef" 
      class="mb-6"
    />
    
    <!-- 销售数据分析 -->
    <sales-analysis 
      :data="salesAnalysisDataRef" 
      class="mb-6"
    />
  </div>
  
  <!-- 悬浮快捷操作按钮 -->
  <quick-actions />
</template>

<script setup>
import { ref } from 'vue';
import StatisticsOverview from '~/components/master/customer/workbench/statistics-overview.vue';
import TodoTasks from '~/components/master/customer/workbench/todo-tasks.vue';
import QuickActions from '~/components/master/customer/workbench/quick-actions.vue';
import TeamOverview from '~/components/master/customer/workbench/team-overview.vue';
import PersonalOverview from '~/components/master/customer/workbench/personal-overview.vue';
import SalesAnalysis from '~/components/master/customer/workbench/sales-analysis.vue';

import {
  statistics as statsData,
  todoData,
  teamOverviewData,
  personalOverviewData,
  salesAnalysisData
} from '~/mock/customer-workbench-new';

// 数据统计
const statistics = ref(statsData);
const todayData = ref(statsData.todayData);

// 待办事项数据
const todoTasksRef = ref(todoData);

// 团队概况数据
const teamOverviewDataRef = ref(teamOverviewData);

// 我的概况数据
const personalOverviewDataRef = ref(personalOverviewData);

// 销售数据分析
const salesAnalysisDataRef = ref(salesAnalysisData);
</script>

<script>
export default { name: "master-customer-workbench" };
</script>

<style scoped>
.arco-card {
  height: 100%;
}
</style>
