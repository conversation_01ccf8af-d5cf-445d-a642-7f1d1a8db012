<!--
 - 商机管理页面
 - 
 - <AUTHOR>
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <ma-crud :options="crud" :columns="columns" ref="crudRef">
      <!-- 销售阶段列 -->
      <template #stage="{ record }">
        <a-tag :color="getStageColor(record.stage)">
          {{ getStageText(record.stage) }}
        </a-tag>
      </template>
      
      <!-- 客户来源列 -->
      <template #source="{ record }">
        {{ getSourceText(record.source) }}
      </template>
      
      <!-- 跟进时间列 -->
      <template #nextFollowUpTime="{ record }">
        <div class="whitespace-normal">
          <div class="break-all">{{ record.nextFollowUpTime || '暂无计划' }}</div>
          <div v-if="record.lastFollowUpTime" class="text-xs text-gray-500 break-all">
            上次：{{ record.lastFollowUpTime }}
          </div>
        </div>
      </template>
      
      <!-- 自定义操作按钮 -->
      <template #operationBeforeExtend="{ record }">
        <div class="flex flex-col gap-1">
          <div>
            <a-button type="text" size="small" @click="handleViewDetail(record)" class="w-full text-left">
              <template #icon><icon-eye /></template>
              查看详情
            </a-button>
          </div>
          <div>
            <a-button type="text" size="small" @click="handleAddFollowUp(record)" class="w-full text-left">
              <template #icon><icon-plus /></template>
              添加跟进
            </a-button>
          </div>
          <div>
            <a-button type="text" status="success" size="small" @click="handleConvertToOrder(record)" class="w-full text-left">
              <template #icon><icon-check-circle /></template>
              转为订单
            </a-button>
          </div>
          <div>
            <a-button type="text" size="small" @click="handleTransferOpportunity(record)" class="w-full text-left">
              <template #icon><icon-swap /></template>
              转移商机
            </a-button>
          </div>
        </div>
      </template>
    </ma-crud>
    
    <!-- 添加跟进记录弹窗 -->
    <a-modal
      v-model:visible="followUpModal.visible"
      title="添加跟进记录"
      @cancel="followUpModal.visible = false"
      @before-ok="handleSaveFollowUp"
    >
      <ma-form
        ref="followUpFormRef"
        :model="followUpModal.formData"
        :rules="followUpModal.rules"
        layout="vertical"
      >
        <ma-form-item field="method" label="跟进方式" required>
          <a-select v-model="followUpModal.formData.method" placeholder="请选择跟进方式">
            <a-option value="电话">电话</a-option>
            <a-option value="邮件">邮件</a-option>
            <a-option value="微信">微信</a-option>
            <a-option value="拜访">拜访</a-option>
            <a-option value="其他">其他</a-option>
          </a-select>
        </ma-form-item>
        <ma-form-item field="content" label="跟进内容" required>
          <a-textarea v-model="followUpModal.formData.content" placeholder="请输入跟进内容" :auto-size="{ minRows: 3, maxRows: 6 }" />
        </ma-form-item>
        <ma-form-item field="stage" label="销售阶段">
          <a-select v-model="followUpModal.formData.stage" placeholder="请选择销售阶段" allow-clear>
            <a-option v-for="item in stageOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-option>
          </a-select>
        </ma-form-item>
        <ma-form-item field="nextFollowUpTime" label="下次跟进时间">
          <a-date-picker 
            v-model="followUpModal.formData.nextFollowUpTime" 
            placeholder="请选择下次跟进时间" 
            show-time 
            style="width: 100%"
          />
        </ma-form-item>
      </ma-form>
    </a-modal>
    
    <!-- 转移商机弹窗 -->
    <a-modal
      v-model:visible="transferModal.visible"
      title="转移商机"
      @cancel="transferModal.visible = false"
      @before-ok="handleTransferSubmit"
    >
      <ma-form
        ref="transferFormRef"
        :model="transferModal.formData"
        :rules="transferModal.rules"
        layout="vertical"
      >
        <ma-form-item field="opportunityTitle" label="商机标题">
          <a-input v-model="transferModal.formData.opportunityTitle" disabled />
        </ma-form-item>
        <ma-form-item field="targetSalesPersonId" label="转交给" required>
          <a-select v-model="transferModal.formData.targetSalesPersonId" placeholder="请选择接收人">
            <a-option v-for="item in salesPersonOptions" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-option>
          </a-select>
        </ma-form-item>
        <ma-form-item field="reason" label="转交原因">
          <a-textarea v-model="transferModal.formData.reason" placeholder="请输入转交原因" />
        </ma-form-item>
      </ma-form>
    </a-modal>
    
    <!-- 商机详情抽屉 -->
    <a-drawer
      v-model:visible="detailDrawer.visible"
      :width="600"
      title="商机详情"
      unmount-on-close
    >
      <a-descriptions :data="detailDrawer.data" layout="vertical" bordered size="large" :column="{ xs: 1, sm: 2, md: 2 }" />
      
      <!-- 跟进记录列表 -->
      <div class="mt-6">
        <div class="flex justify-between items-center mb-2">
          <h3 class="text-lg font-bold">跟进记录</h3>
          <a-button type="primary" size="small" @click="handleAddFollowUp(detailDrawer.record)">
            <template #icon><icon-plus /></template>
            添加跟进
          </a-button>
        </div>
        <a-timeline>
          <a-timeline-item v-for="(item, index) in detailDrawer.followUpRecords" :key="index" :dot-color="getFollowUpDotColor(item.method)">
            <div class="bg-gray-50 p-3 rounded">
              <div class="flex justify-between">
                <span class="font-medium">{{ item.method }}</span>
                <span class="text-gray-500 text-sm">{{ item.createTime }}</span>
              </div>
              <div class="mt-2">{{ item.content }}</div>
              <div class="mt-2 text-sm text-gray-500">
                <span>跟进人：{{ item.creatorName }}</span>
                <span class="ml-4" v-if="item.nextFollowUpTime">下次跟进：{{ item.nextFollowUpTime }}</span>
              </div>
              <div class="mt-2" v-if="item.stage">
                <a-tag :color="getStageColor(item.stage)">{{ getStageText(item.stage) }}</a-tag>
              </div>
            </div>
          </a-timeline-item>
          <a-timeline-item v-if="detailDrawer.followUpRecords.length === 0">
            <div class="text-gray-400">暂无跟进记录</div>
          </a-timeline-item>
        </a-timeline>
      </div>
    </a-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';

// 页面元数据
definePageMeta({
  name: 'master-customer-opportunity',
  path: '/master/customer/opportunity'
});

// CRUD 引用
const crudRef = ref();
const followUpFormRef = ref();
const transferFormRef = ref();

// 销售阶段选项
const stageOptions = [
  { label: '初步洽谈', value: 1 },
  { label: '需求确定', value: 2 },
  { label: '方案报价', value: 3 },
  { label: '商务谈判', value: 4 },
  { label: '商机成交', value: 5 },
  { label: '商机终止', value: 6 },
];

// 客户来源选项
const sourceOptions = [
  { label: '自主开发', value: 1 },
  { label: '推荐介绍', value: 2 },
  { label: '重复购买', value: 3 },
];

// 跟进人选项
const salesPersonOptions = [
  { label: '张三', value: 1 },
  { label: '李四', value: 2 },
  { label: '王五', value: 3 },
];

// 跟进方式颜色
const getFollowUpDotColor = (method) => {
  const methodColors = {
    '电话': 'blue',
    '邮件': 'green',
    '微信': 'purple',
    '拜访': 'red',
    '其他': 'gray',
  };
  return methodColors[method] || 'blue';
};

// 客户选项
const customerOptions = ref([]);
// 联系人选项
const contactOptions = ref([]);
// 产品选项
const productOptions = ref([]);

// 获取销售阶段文本
const getStageText = (stage) => {
  const option = stageOptions.find(item => item.value === stage);
  return option ? option.label : '';
};

// 获取销售阶段颜色
const getStageColor = (stage) => {
  const stageColors = {
    1: 'blue', // 初步洽谈
    2: 'green', // 需求确定
    3: 'orange', // 方案报价
    4: 'purple', // 商务谈判
    5: 'red', // 商机成交
    6: 'gray', // 商机终止
  };
  return stageColors[stage] || 'blue';
};

// 获取客户来源文本
const getSourceText = (source) => {
  const option = sourceOptions.find(item => item.value === source);
  return option ? option.label : '';
};

// 获取客户选项
const fetchCustomerOptions = async () => {
  try {
    // 这里应该调用实际的API
    // const res = await customerApi.getList();
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 300));
    
    customerOptions.value = [
      { label: '腾讯科技', value: 1 },
      { label: '阿里巴巴', value: 2 },
      { label: '百度科技', value: 3 },
    ];
  } catch (error) {
    console.error('获取客户选项失败:', error);
  }
};

// 获取联系人选项
const fetchContactOptions = async () => {
  try {
    // 这里应该调用实际的API
    // const res = await contactApi.getList();
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 300));
    
    contactOptions.value = [
      { label: '马化腾', value: 1 },
      { label: '马云', value: 2 },
      { label: '李彦宏', value: 3 },
    ];
  } catch (error) {
    console.error('获取联系人选项失败:', error);
  }
};

// 获取产品选项
const fetchProductOptions = async () => {
  try {
    // 这里应该调用实际的API
    // const res = await productApi.getList();
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 300));
    
    productOptions.value = [
      { label: '企业微信定制开发', value: 1 },
      { label: '阿里云企业版', value: 2 },
      { label: 'AI智能解决方案', value: 3 },
    ];
  } catch (error) {
    console.error('获取产品选项失败:', error);
  }
};

// 页面初始化
onMounted(() => {
  // 获取选项数据
  fetchCustomerOptions();
  fetchContactOptions();
  fetchProductOptions();
});

// 商机详情抽屉数据
const detailDrawer = reactive({
  visible: false,
  record: null,
  data: [],
  followUpRecords: [],
});

// 添加跟进记录弹窗数据
const followUpModal = reactive({
  visible: false,
  opportunityId: null,
  formData: {
    method: '',
    content: '',
    stage: undefined,
    nextFollowUpTime: null,
  },
  rules: {
    method: [{ required: true, message: '请选择跟进方式' }],
    content: [{ required: true, message: '请输入跟进内容' }],
  },
});

// 转移商机弹窗数据
const transferModal = reactive({
  visible: false,
  formData: {
    opportunityId: '',
    opportunityTitle: '',
    targetSalesPersonId: undefined,
    reason: '',
  },
  rules: {
    targetSalesPersonId: [{ required: true, message: '请选择接收人' }],
  },
});

// 查看商机详情
const handleViewDetail = (record) => {
  detailDrawer.visible = true;
  detailDrawer.record = record;
  
  // 准备描述列表数据
  detailDrawer.data = [
    { label: '商机编号', value: record.code },
    { label: '商机标题', value: record.title },
    { label: '关联客户', value: record.customerName },
    { label: '客户地址', value: record.customerAddress || '暂无' },
    { label: '客户来源', value: getSourceText(record.source) },
    { label: '下单平台', value: record.platform || '暂无' },
    { label: '关联联系人', value: record.contactName || '暂无' },
    { label: '关联商品', value: record.productName || '暂无' },
    { label: '销售阶段', value: getStageText(record.stage) },
    { label: '跟进人', value: record.salesPersonName },
    { label: '预计成交金额', value: record.expectedAmount ? `￥${record.expectedAmount}` : '暂无' },
    { label: '预计成交时间', value: record.expectedCloseTime || '暂无' },
    { label: '上次跟进时间', value: record.lastFollowUpTime || '暂无' },
    { label: '下次跟进时间', value: record.nextFollowUpTime || '暂无' },
    { label: '商机描述', value: record.description || '暂无', span: 2 },
    { label: '创建人', value: record.creatorName },
    { label: '创建时间', value: record.createTime },
  ];
  
  // 获取跟进记录
  fetchFollowUpRecords(record.id);
};

// 获取跟进记录
const fetchFollowUpRecords = async (opportunityId) => {
  try {
    // 这里应该调用实际的API
    // const res = await followUpApi.getList({ opportunityId });
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 300));
    
    detailDrawer.followUpRecords = [
      {
        id: 1,
        opportunityId: opportunityId,
        method: '电话',
        content: '与客户进行了首次电话沟通，客户对我们的产品非常感兴趣，希望了解更多详情。',
        stage: 1,
        nextFollowUpTime: '2025-05-05 10:00:00',
        creatorName: '李四',
        createTime: '2025-05-01 09:30:00',
      },
      {
        id: 2,
        opportunityId: opportunityId,
        method: '邮件',
        content: '向客户发送了产品详细介绍和报价单，客户表示会讨论后回复。',
        stage: 2,
        nextFollowUpTime: '2025-05-08 14:00:00',
        creatorName: '李四',
        createTime: '2025-05-03 16:00:00',
      },
    ];
    
    // 根据商机ID过滤记录
    if (opportunityId !== 1) {
      detailDrawer.followUpRecords = [];
    }
  } catch (error) {
    console.error('获取跟进记录失败:', error);
    detailDrawer.followUpRecords = [];
  }
};

// CRUD配置
const crud = reactive({
  // API配置 - 这里需要替换为实际的API
  api: async (params) => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟数据
    const data = {
      items: [
        {
          id: 1,
          code: 'SJ20250501001',
          title: '腾讯企业微信定制开发',
          customerId: 1,
          customerName: '腾讯科技',
          customerAddress: '深圳市南山区科技园中一路33号',
          source: 1,
          sourceName: '自主开发',
          platform: '线上咨询',
          contactId: 1,
          contactName: '马化腾',
          productId: 1,
          productName: '企业微信定制开发',
          stage: 3,
          salesPersonId: 2,
          salesPersonName: '李四',
          expectedAmount: 50000,
          expectedCloseTime: '2025-06-30',
          description: '这是一个商机描述，详细说明了客户需求和解决方案。',
          lastFollowUpTime: '2025-05-03 16:00:00',
          nextFollowUpTime: '2025-05-12 14:00:00',
          creatorName: '系统管理员',
          createTime: '2025-05-01 09:00:00',
        },
        {
          id: 2,
          code: 'SJ20250425002',
          title: '阿里云解决方案',
          customerId: 2,
          customerName: '阿里巴巴',
          customerAddress: '杭州市余杭区文一西路969号',
          source: 2,
          sourceName: '推荐介绍',
          platform: '线上咨询',
          contactId: 2,
          contactName: '马云',
          productId: 2,
          productName: '阿里云企业版',
          stage: 2,
          salesPersonId: 1,
          salesPersonName: '张三',
          expectedAmount: 30000,
          expectedCloseTime: '2025-06-15',
          description: '阿里云企业版解决方案，包含云服务器、数据库和安全服务。',
          lastFollowUpTime: '2025-04-25 10:30:00',
          nextFollowUpTime: '2025-05-08 09:00:00',
          creatorName: '系统管理员',
          createTime: '2025-04-25 09:00:00',
        },
        {
          id: 3,
          code: 'SJ20250428001',
          title: '百度AI解决方案',
          customerId: 3,
          customerName: '百度科技',
          customerAddress: '北京市海淀区西北旺东路10号',
          source: 3,
          sourceName: '重复购买',
          platform: '电话咨询',
          contactId: 3,
          contactName: '李彦宏',
          productId: 3,
          productName: 'AI智能解决方案',
          stage: 5,
          salesPersonId: 3,
          salesPersonName: '王五',
          expectedAmount: 80000,
          expectedCloseTime: '2025-05-30',
          description: 'AI智能解决方案，包含语音识别、图像识别和自然语言处理。',
          lastFollowUpTime: '2025-04-28 11:00:00',
          nextFollowUpTime: '2025-05-15 09:30:00',
          creatorName: '系统管理员',
          createTime: '2025-04-28 09:00:00',
        }
      ],
      total: 3,
    };
    
    return {
      code: 200,
      message: '操作成功',
      data: {
        items: data.items,
        total: data.total,
      }
    };
  },
  
  // 表格配置
  showIndex: true,
  pageLayout: 'fixed',
  rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 250,
  
  // 添加配置
  add: { 
    show: true, 
    api: async (params) => {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { code: 200, message: '添加成功' };
    },
  },
  
  // 编辑配置
  edit: { 
    show: true, 
    api: async (params) => {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { code: 200, message: '编辑成功' };
    },
  },
  
  // 删除配置
  delete: {
    show: true,
    api: async (params) => {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      return { code: 200, message: '删除成功' };
    },
  },
  
  // 添加前处理参数
  beforeAdd: (params) => {
    console.log('添加前原始参数:', params);
    return params;
  },
  
  // 编辑前处理参数
  beforeEdit: (params) => {
    console.log('编辑前原始参数:', params);
    return params;
  },
  
  // 详情配置
  detail: {
    show: true,
    handler: handleViewDetail,
  },
});

// 表格列配置
const columns = reactive([
  {
    title: '商机编号',
    dataIndex: 'code',
    width: 120,
    addDisplay: false,
    editDisplay: false,
  },
  {
    title: '商机标题',
    dataIndex: 'title',
    search: true,
    commonRules: [{ required: true, message: '商机标题必填' }],
  },
  {
    title: '关联客户',
    dataIndex: 'customerId',
    formType: 'select',
    commonRules: [{ required: true, message: '关联客户必选' }],
    dict: { data: customerOptions, props: { label: 'label', value: 'value' } },
    formatter: (row) => row.customerName,
  },
  {
    title: '客户来源',
    dataIndex: 'source',
    formType: 'select',
    commonRules: [{ required: true, message: '客户来源必选' }],
    dict: { data: sourceOptions, props: { label: 'label', value: 'value' } },
  },
  {
    title: '关联联系人',
    dataIndex: 'contactId',
    formType: 'select',
    dict: { data: contactOptions, props: { label: 'label', value: 'value' } },
    formatter: (row) => row.contactName,
  },
  {
    title: '下单平台',
    dataIndex: 'platform',
  },
  {
    title: '关联商品',
    dataIndex: 'productId',
    formType: 'select',
    dict: { data: productOptions, props: { label: 'label', value: 'value' } },
    formatter: (row) => row.productName,
  },
  {
    title: '销售阶段',
    dataIndex: 'stage',
    formType: 'select',
    commonRules: [{ required: true, message: '销售阶段必选' }],
    dict: { data: stageOptions, props: { label: 'label', value: 'value' } },
    addDefaultValue: 1,
  },
  {
    title: '跟进人',
    dataIndex: 'salesPersonId',
    formType: 'select',
    commonRules: [{ required: true, message: '跟进人必选' }],
    dict: { data: salesPersonOptions, props: { label: 'label', value: 'value' } },
    formatter: (row) => row.salesPersonName,
  },
  {
    title: '预计成交金额',
    dataIndex: 'expectedAmount',
    formType: 'input-number',
    addDefaultValue: 0,
  },
  {
    title: '预计成交时间',
    dataIndex: 'expectedCloseTime',
    formType: 'date-picker',
  },
  {
    title: '下次跟进时间',
    dataIndex: 'nextFollowUpTime',
    addDisplay: false,
    editDisplay: false,
    width: 180,
  },
  {
    title: '商机描述',
    dataIndex: 'description',
    formType: 'textarea',
    hide: true,
  },
]);

// 添加跟进记录
const handleAddFollowUp = (record) => {
  followUpModal.visible = true;
  followUpModal.opportunityId = record.id;
  followUpModal.formData = {
    method: '',
    content: '',
    stage: record.stage,
    nextFollowUpTime: null,
  };
};

// 保存跟进记录
const handleSaveFollowUp = async () => {
  try {
    await followUpFormRef.value.validate();
    
    // 这里应该调用实际的API
    // await addFollowUpRecord({
    //   opportunityId: followUpModal.opportunityId,
    //   ...followUpModal.formData,
    // });
    
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 500));
    
    Message.success('添加跟进记录成功');
    followUpModal.visible = false;
    
    // 刷新列表
    crudRef.value.refresh();
    return true;
  } catch (error) {
    console.error('添加跟进记录失败', error);
    Message.error('添加跟进记录失败');
    return false;
  }
};

// 转为订单
const handleConvertToOrder = (record) => {
  Modal.info({
    title: '转为订单',
    content: `确定要将商机"${record.title}"转为订单吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 这里应该调用实际的API
        // await convertToOrder(record.id);
        
        // 模拟转换
        await new Promise(resolve => setTimeout(resolve, 500));
        
        Message.success('转为订单成功');
        
        // 刷新列表
        crudRef.value.refresh();
      } catch (error) {
        console.error('转为订单失败', error);
        Message.error('转为订单失败');
      }
    }
  });
};

// 转移商机
const handleTransferOpportunity = (record) => {
  transferModal.visible = true;
  transferModal.formData = {
    opportunityId: record.id,
    opportunityTitle: record.title,
    targetSalesPersonId: undefined,
    reason: '',
  };
};

// 提交转移商机
const handleTransferSubmit = async () => {
  try {
    await transferFormRef.value.validate();
    
    // 这里应该调用实际的API
    // await transferOpportunity({
    //   opportunityId: transferModal.formData.opportunityId,
    //   targetSalesPersonId: transferModal.formData.targetSalesPersonId,
    //   reason: transferModal.formData.reason,
    // });
    
    // 模拟转移
    await new Promise(resolve => setTimeout(resolve, 500));
    
    Message.success('转移商机成功');
    transferModal.visible = false;
    
    // 刷新列表
    crudRef.value.refresh();
    return true;
  } catch (error) {
    console.error('转移商机失败', error);
    Message.error('转移商机失败');
    return false;
  }
};
</script>
