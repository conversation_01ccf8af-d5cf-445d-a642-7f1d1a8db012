<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
-->
<template>
  <div class="ma-content-block p-4">
    <!-- CRUD 组件 -->
    <MaCrud :options="crud" :columns="columns" ref="crudRef">
      <!-- 状态列 -->
      <template #status="{ record }">{{ record.status == 1 ? '正常' : '停用' }}</template>
      <!-- 平台ID列 -->
      <template #platform_id="{ record }">
        {{ getPlatformName(record.platform_id) }}
      </template>
      <!-- 爬虫类型列 -->
      <template #spider_type="{ record }">
        {{ getSpiderTypeName(record.spider_type) }}
      </template>
      <!-- 自定义列 - 创建时间 -->
      <template #created_at="{ record }">
        <div v-time="record.created_at"></div>
      </template>
      <template #search-platform_id="{ searchForm }">
        <a-cascader
          v-model="searchForm.platform_id"
          :options="options"
          placeholder="请选择平台"
          allow-clear
        />
      </template>
      <template #form-platform_id>
        <a-cascader
          v-model="formModel.platform_id"
          :options="options"
          placeholder="请选择平台"
          allow-clear
        />
      </template>
    </MaCrud>
  </div>
</template>
  
<script setup>
import { ref, reactive, onMounted } from "vue";
import { Message } from "@arco-design/web-vue";
import systemApi from "@/api/master/system";
import orderApi from "@/api/master/order";

definePageMeta({
  name: "master-crawlerManage-crawlerList",
  path: "/master/crawlerManage/crawlerList"
});

const formModel = ref({});
const crudRef = ref();
// 订单同步 商品同步 发票同步 报备同步

// 表格列定义
const columns = [
 
  {
    title: "爬虫名称",
    dataIndex: "name",
    commonRules: [{ required: true, message: '爬虫名称必填' }],
  },
  {
    title: "爬虫代码",
    dataIndex: "code",
    commonRules: [{ required: true, message: '爬虫代码必填' }],
  },

  {
    title: "平台ID",
    dataIndex: "platform_id",
    formType: "cascader",
    search: true,
    placeholder: "请选择平台",
    // commonRules: [{ required: true, message: '平台ID必填' }],
  },

  {
    title: "爬虫类型",
    dataIndex: "spider_type",
    formType: "select",
    search: true,
    commonRules: [{ required: true, message: '爬虫类型必填' }],
    dict: {
      data: [
        { label: '订单同步', value: 'order' },
        { label: '商品同步', value: 'goods' },
        { label: '发票同步', value: 'invoice' },
        { label: '报备同步', value: 'report' },
      ]
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    formType: "radio",
    search: true,
    addDefaultValue: 1,
    dict: {
      data: [
        { label: '正常', value: 1 },
        { label: '停用', value: 0 }
      ]
    },
  },
  {
    title: "备注",
    dataIndex: "remark",
  },

];

// CRUD 配置
const crud = reactive({
  // 使用本地数据源而不是直接调用API
  api: systemApi.getSpiderTasks,
  showIndex: true,
  pageLayout: "fixed",
  // rowSelection: { showCheckedAll: true },
  operationColumn: true,
  operationColumnWidth: 200,
  searchLabelWidth: "150px",
  add: { show: true, api: systemApi.createSpiderTask },
  edit: { show: true, api: systemApi.updateSpiderTask }, // 使用自定义编辑
  delete: { show: true, api: systemApi.deleteSpiderTask }, // 使用自定义删除
  //export: { show: true },//导出
  // 搜索前处理参数
//   beforeSearch: params => {
//     return params;
//   },
  beforeAdd: (params) => {
    params.platform_id = formModel.value.platform_id || '';
    return params;
  },
  beforeOpenAdd: () => {
    formModel.value.platform_id = "";
    return true;
  },
  beforeOpenEdit: (record) => {
    console.log('beforeOpenEdit', record);
    formModel.value.platform_id = record.platform_id || '';
    return true;
  },
  beforeEdit: (params) => {
    if(formModel.value.platform_id){
        params.platform_id = formModel.value.platform_id || "";
    }
    return params;
  },

});
const options = ref([]);

const fetchOptionsList = async () => {
  // 只处理前两级数据的转换函数
  const transform = item => {
    const result = {
      value: item.id,
      label: item.name
    };

    // 只处理第一级的子元素（第二级），不再递归处理更深层级
    if (item.children) {
      result.children = item.children.map(childItem => ({
        value: childItem.id,
        label: childItem.name
      }));
    }

    return result;
  };
  const res = await orderApi.getAllPlatformManagement();
  options.value = res.data.map(transform);
};
// 获取平台名称的方法
const getPlatformName = (platformId) => {
  if (!platformId) return '-';
  
  // 递归查找平台名称
  const findPlatform = (items, id) => {
    for (const item of items) {
      if (item.value === id) {
        return item.label;
      }
      if (item.children && item.children.length > 0) {
        const found = findPlatform(item.children, id);
        if (found) return found;
      }
    }
    return null;
  };
  
  const platformName = findPlatform(options.value, platformId);
  return platformName || platformId;
};

// 获取爬虫类型名称的方法
const getSpiderTypeName = (type) => {
  if (!type) return '-';
  
  const typeMap = {
    'order': '订单同步',
    'goods': '商品同步',
    'invoice': '发票同步',
    'report': '报备同步'
  };
  
  return typeMap[type] || type;
};

onMounted(() => {
  fetchOptionsList();
});
</script>
  
<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}
</style>
  