<template>
  <div class="container mx-auto px-4 py-4">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/category" class="hover:text-red-600">全部商品分类</NuxtLink> &gt; 
      <NuxtLink :to="`/mall/category/${currentCategory.id}`" class="hover:text-red-600">{{ currentCategory.name }}</NuxtLink> &gt;
      <span>{{ product.name }}</span>
    </div>

    <!-- 商品详情主体 -->
    <div class="product-detail bg-white rounded-lg p-6 mb-4">
      <div class="flex">
        <!-- 左侧商品图片区域 -->
        <div class="product-gallery w-96 flex-shrink-0 mr-8">
          <!-- 主图 -->
          <div class="main-image bg-gray-50 rounded-lg mb-4 flex items-center justify-center h-96">
            <img :src="currentImage" :alt="product.name" class="max-h-full max-w-full object-contain">
          </div>
          
          <!-- 缩略图列表 -->
          <div class="thumbnail-list flex space-x-2">
            <div v-for="(image, index) in product.images" :key="index" 
                 :class="['thumbnail-item w-20 h-20 bg-gray-50 rounded flex items-center justify-center cursor-pointer border-2', 
                          currentImageIndex === index ? 'border-red-600' : 'border-transparent']"
                 @click="selectImage(index)">
              <img :src="image" :alt="`${product.name}-${index}`" class="max-h-full max-w-full object-contain">
            </div>
          </div>
        </div>
        
        <!-- 右侧商品信息区域 -->
        <div class="product-info flex-1">
          <!-- 商品标题 -->
          <h1 class="text-2xl font-bold mb-2">{{ product.name }}</h1>
          
          <!-- 商品副标题 -->
          <div class="text-gray-500 mb-4">{{ product.subtitle }}</div>
          
          <!-- 商品评分 -->
<!--          <div class="product-rating flex items-center mb-4">-->
<!--            <div class="rating-stars text-yellow-400 mr-2">-->
<!--              <span v-for="i in 5" :key="i" :class="i <= Math.round(product?.rating) ? 'text-yellow-400' : 'text-gray-300'">★</span>-->
<!--            </div>-->
<!--            <div class="rating-score text-red-600 font-medium mr-2">{{ product?.rating?.toFixed(1) }}</div>-->
<!--            <div class="review-count text-gray-500">{{ product?.reviewCount }}条评价</div>-->
<!--          </div>-->
          
          <!-- 商品价格 -->
          <div class="product-price-section bg-gray-50 p-4 rounded-lg mb-4">
            <div class="flex items-baseline mb-2">
              <div class="text-sm text-gray-500 mr-2">价格</div>
              <div class="text-red-600 text-3xl font-bold" v-if="skuList.length>0">¥{{ skuList[currentSkuIndex].salesPrice }}</div>
              <div class="text-gray-400 line-through text-sm ml-2" v-if="skuList.length>0">¥{{skuList[currentSkuIndex].marketPrice }}</div>
            </div>
            <div class="promotion-tags flex space-x-2 mb-2">
              <span v-if="product.discount" class="bg-red-600 text-white text-xs px-2 py-0.5 rounded">{{ product.discount }}折</span>
              <span v-if="product.isNew" class="bg-blue-600 text-white text-xs px-2 py-0.5 rounded">新品</span>
              <span v-if="product.isHot" class="bg-orange-600 text-white text-xs px-2 py-0.5 rounded">热销</span>
            </div>
          </div>
          
          <!-- 配送信息 -->
          <div class="delivery-info mb-4">
            <div class="flex items-center mb-2" v-if="!freightInfo.noFreightTemplate">
              <div class="text-sm text-gray-500 w-16">配送</div>
              <div class="text-sm">
                <span class="mr-2">{{ freightInfo.templateInfo?.matchRegion || localStore.locationInfo.cityName || '全国' }}</span>
                <span v-if="freightInfo.loading">运费计算中...</span>
                <span v-else-if="freightInfo.isFreeShipping">包邮</span>
                <span v-else>
                  <span>运费: ¥{{ freightInfo.freight }} 元</span>
                </span>
              </div>
            </div>
            <div class="flex items-center" v-if="product.services.length>0">
              <div class="text-sm text-gray-500 w-16">服务</div>
              <div class="text-sm flex items-center">
                <span v-for="(service, index) in product.services" :key="index" class="mr-4 flex items-center">
                  <span class="text-red-600 mr-1">✓</span>
                  <span>{{ service.name }}</span>
                </span>
              </div>
            </div>
          </div>
          
          <!-- 商品规格选择 -->
          <div class="product-specs mb-6">
            <div v-for="(spec, specIndex) in specGroups" :key="specIndex" class="spec-group mb-4">
              <div class="text-sm text-gray-500 mb-2">{{ spec.name }}</div>
              <div class="spec-options flex flex-wrap">
                <div v-for="(option, optionIndex) in spec.options" :key="optionIndex"
                     :class="['spec-option border rounded mr-2 mb-2 px-3 py-1 cursor-pointer text-sm', 
                              selectedSpecs[spec.name] === option ? 'border-red-600 text-red-600' : 'border-gray-300 text-gray-700']"
                     @click="selectSpec(spec.name, option)">
                  {{ option.name }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- 数量选择 -->
          <div class="quantity-selector flex items-center mb-6">
            <div class="text-sm text-gray-500 w-16">数量</div>
            <div class="flex items-center">
              <button class="border border-gray-300 w-8 h-8 flex items-center justify-center rounded-l"
                      @click="decreaseQuantity" :disabled="quantity <= 1">
                -
              </button>
              <input type="number" v-model="quantity" min="1" max="99" 
                     class="border-t border-b border-gray-300 w-12 h-8 text-center">
              <button class="border border-gray-300 w-8 h-8 flex items-center justify-center rounded-r"
                      @click="increaseQuantity" :disabled="quantity >= 99">
                +
              </button>
              <div class="text-sm text-gray-500 ml-4" v-if="skuList.length>0">库存 {{ skuList[currentSkuIndex].stock }} 件</div>
            </div>
          </div>
          
          <!-- 购买按钮 -->
          <div class="purchase-actions flex space-x-4">
            <button class="buy-now-btn bg-red-600 text-white px-8 py-3 rounded-full hover:bg-red-700 transition"
                    @click="buyNow">
              立即购买
            </button>
            <a-popconfirm
              content="确定要将该商品加入购物车吗？"
              @ok="addToCart"
            >
              <button class="add-to-cart-btn border-2 border-red-600 text-red-600 px-8 py-3 rounded-full hover:bg-red-50 transition">
                <span class="mr-1">🛒</span>
                <span>加入购物车</span>
              </button>
            </a-popconfirm>
            <button
              class="favorite-btn px-4 py-3 rounded-full transition flex items-center"
              :class="isFavorited ? 'border border-red-500 text-red-500 bg-red-50 hover:bg-red-100' : 'border border-gray-300 text-gray-600 hover:bg-gray-50'"
              @click="handleFavoriteClick"
              :disabled="favoriteLoading"
            >
              <span class="mr-1" :class="isFavorited ? 'text-red-500' : 'text-gray-600'">
                {{ isFavorited ? '❤️' : '🤍' }}
              </span>
              <span>{{ isFavorited ? '已收藏' : '收藏' }}</span>
              <span v-if="favoriteLoading" class="ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"></div>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 商品详情选项卡 -->
    <div class="product-tabs bg-white rounded-lg mb-4">
      <!-- 选项卡导航 -->
      <div class="tab-nav border-b flex">
        <div v-for="(tab, index) in tabs" :key="index"
             :class="['tab-item px-8 py-4 text-center cursor-pointer border-b-2 font-medium', 
                      activeTab === index ? 'border-red-600 text-red-600' : 'border-transparent text-gray-600']"
             @click="activeTab = index">
          {{ tab.name }}
        </div>
      </div>
      
      <!-- 选项卡内容 -->
      <div class="tab-content p-6">
        <!-- 商品详情 -->
        <div v-if="activeTab === 0" class="product-description">
          <div v-if="product.description" v-html="product.description"></div>
          <div class="section-title text-lg font-bold mb-4">商品介绍</div>
          <div class="product-params mb-6">
            <table class="w-full border-collapse">
              <tbody>
                <tr v-for="(param, index) in product.params" :key="index" :class="index % 2 === 0 ? 'bg-gray-50' : 'bg-white'">
                  <td class="border border-gray-200 p-2 w-1/5 text-gray-500">{{ param.name }}</td>
                  <td class="border border-gray-200 p-2">{{ param.value }}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="product-detail-images">
            <img v-for="(image, index) in product.images" :key="index"
                 :src="image"
                 class="w-full mb-4">
          </div>
        </div>
        
        <!-- 规格参数 -->
        <div v-if="activeTab === 1" class="product-specifications">
          <div class="section-title text-lg font-bold mb-4">规格参数</div>
          <table class="w-full border-collapse">
            <tbody>
              <tr v-for="(attr, index) in product.attributes" :key="index" :class="index % 2 === 0 ? 'bg-gray-50' : 'bg-white'">
                <td class="border border-gray-200 p-2 w-1/5 text-gray-500">{{ attr.name }}</td>
                <td class="border border-gray-200 p-2">{{ attr.value }}</td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- 包装清单 -->
        <!-- <div v-if="activeTab === 2" class="product-package">
          <div class="section-title text-lg font-bold mb-4">包装清单</div>
          <div class="package-list">
            <div v-for="(item, index) in product.packageList" :key="index" class="package-item mb-2 flex items-center">
              <span class="text-red-600 mr-2">•</span>
              <span>{{ item }}</span>
            </div>
          </div>
        </div> -->
        
        <!-- 售后保障 -->
        <!-- <div v-if="activeTab === 3" class="after-sales">
          <div class="section-title text-lg font-bold mb-4">售后保障</div>
          <div class="after-sales-content">
            <div v-for="(item, index) in product.afterSales" :key="index" class="after-sales-item mb-4">
              <div class="font-medium mb-1">{{ item.title }}</div>
              <div class="text-gray-600 text-sm">{{ item.content }}</div>
            </div>
          </div>
        </div> -->
        
        <!-- 商品评价 -->
        <div v-if="activeTab === 2" class="product-reviews">
          <!-- 评价统计 -->
          <div class="review-stats bg-gray-50 p-4 rounded-lg mb-6" v-if="reviewStatsComputed">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="text-3xl font-bold text-red-600 mr-4">{{ reviewStatsComputed.averageRating?.toFixed(1) || '0.0' }}</div>
                <div>
                  <div class="rating-stars text-yellow-400 mb-1">
                    <span v-for="i in 5" :key="i" :class="i <= Math.round(reviewStatsComputed.averageRating || 0) ? 'text-yellow-400' : 'text-gray-300'">★</span>
                  </div>
                  <div class="text-sm text-gray-500">共{{ reviewStatsComputed.total || 0 }}条评价</div>
                </div>
              </div>
              <div class="flex space-x-6 text-sm">
                <div class="text-center">
                  <div class="text-lg font-medium">{{ reviewStatsComputed.goodRatePercentage || 0 }}%</div>
                  <div class="text-gray-500">好评率</div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-medium">{{ reviewStatsComputed.goodCount || 0 }}</div>
                  <div class="text-gray-500">好评</div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-medium">{{ reviewStatsComputed.mediumCount || 0 }}</div>
                  <div class="text-gray-500">中评</div>
                </div>
                <div class="text-center">
                  <div class="text-lg font-medium">{{ reviewStatsComputed.badCount || 0 }}</div>
                  <div class="text-gray-500">差评</div>
                </div>
              </div>
            </div>

            <!-- 评分分布详情 -->
            <div class="mt-4 pt-4 border-t border-gray-200">
              <div class="text-sm font-medium text-gray-700 mb-3">评分分布</div>
              <div class="space-y-2">
                <div v-for="rating in [5, 4, 3, 2, 1]" :key="rating" class="flex items-center">
                  <div class="w-12 text-sm text-gray-600">{{ rating }}星</div>
                  <div class="flex-1 mx-3">
                    <div class="bg-gray-200 rounded-full h-2">
                      <div
                        class="h-2 rounded-full transition-all duration-300"
                        :class="rating >= 4 ? 'bg-green-400' : rating === 3 ? 'bg-yellow-400' : 'bg-red-400'"
                        :style="{ width: reviewStatsComputed.ratingPercentages[rating] + '%' }"
                      ></div>
                    </div>
                  </div>
                  <div class="w-16 text-sm text-gray-600 text-right">
                    {{ reviewStatsComputed.ratingDistribution[rating] || 0 }} ({{ reviewStatsComputed.ratingPercentages[rating] }}%)
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="review-header flex justify-between items-center mb-4">
            <div class="section-title text-lg font-bold">商品评价 ({{ reviewStatsComputed?.total || 0 }})</div>
            <div class="review-filter flex">
              <div v-for="(filter, index) in reviewFilters" :key="index"
                   :class="['filter-item px-3 py-1 text-sm rounded cursor-pointer mr-2',
                            currentReviewFilter === index ? 'bg-red-600 text-white' : 'bg-gray-100 text-gray-600']"
                   @click="changeReviewFilter(index)">
                {{ filter.name }}
                <span v-if="filter.count !== undefined" class="ml-1">({{ filter.count }})</span>
              </div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="reviewsLoading" class="text-center py-8">
            <a-spin size="large" />
            <div class="text-gray-500 mt-2">加载评价中...</div>
          </div>

          <div v-else class="review-list">
            <!-- 暂无评论提示 -->
            <div v-if="!reviews || reviews.length === 0" class="empty-reviews py-10 text-center">
              <div class="text-gray-400 text-lg mb-2">暂无评论</div>
              <div class="text-gray-400 text-sm">该商品暂时还没有评论，购买后可以来发表评论哦~</div>
            </div>
            <div v-else v-for="(review, index) in reviews" :key="review.id" class="review-item border-b pb-4 mb-4">
              <div class="review-user flex items-center mb-2">
                <div class="user-avatar w-8 h-8 rounded-full bg-gray-200 mr-2 overflow-hidden flex items-center justify-center">
                  <span class="text-gray-500 text-xs">{{ review.userName?.charAt(0) || '匿' }}</span>
                </div>
                <div class="user-name text-sm">{{ review.userName || '匿名用户' }}</div>
                <div class="review-time text-gray-400 text-xs ml-auto">{{ formatTimestamp(review.createdAt) }}</div>
              </div>
              <div class="review-rating flex items-center mb-2">
                <div class="rating-stars text-yellow-400 mr-4">
                  <span v-for="i in 5" :key="i" :class="i <= review.overallRating ? 'text-yellow-400' : 'text-gray-300'">★</span>
                </div>
                <div class="rating-details text-xs text-gray-500 flex space-x-4">
                  <span>质量: {{ review.qualityRating }}分</span>
                  <span>服务: {{ review.serviceRating }}分</span>
                  <span>物流: {{ review.logisticsRating }}分</span>
                </div>
              </div>
              <div v-if="review.skuSpecifications" class="review-spec text-gray-500 text-xs mb-2">
                规格: {{ formatSkuSpecifications(review.skuSpecifications) }}
              </div>
              <div class="review-content mb-2">{{ review.reviewContent }}</div>
              <div v-if="review.images && review.images.length > 0" class="review-images flex space-x-2 mb-2">
                <div v-for="(image, imgIndex) in review.images" :key="imgIndex"
                     class="review-image w-16 h-16 bg-gray-100 rounded overflow-hidden cursor-pointer"
                     @click="previewImage(image)">
                  <img :src="image" :alt="`评价图片${imgIndex}`" class="w-full h-full object-cover">
                </div>
              </div>
              <div v-if="review.adminReply" class="review-reply bg-gray-50 p-2 rounded text-sm mt-2">
                <span class="text-gray-500">商家回复：</span>{{ review.adminReply }}
                <div class="text-gray-400 text-xs mt-1">{{ formatTimestamp(review.adminReplyAt) }}</div>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination flex justify-center mt-6" v-if="reviewTotal > 0">
            <a-pagination
              v-model:current="reviewPage"
              show-total
              show-jumper
              show-page-size
              :total="reviewTotal"
              :page-size="reviewPageSize"
              :page-size-options="['5', '10', '20', '50']"
              @change="handleReviewPageChange"
              @page-size-change="handleReviewPageSizeChange"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 猜你喜欢 -->
    <ProductList
      title="猜你喜欢"
      :rightSwitch="true"
      :imageWidth="0"
      :imageHeight="0"
      :columnsPerRow="2"
      :columnsPerRowMd="3"
      :columnsPerRowLg="6"
      :count="6"
      :enableResponsiveCount="false"
      :autoLoad="true"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMallCartStore } from '~/store/mall/cart';
import { useMallGoodsStore } from '~/store/mall/goods';
import { useMallImmediatePurchaseStore } from '~/store/mall/immediate-purchase';
import { useMallHistoryStore } from '~/store/mall/history';
import { useMallLocalStore } from '~/store/mall/local';
import { useMallUserStore } from '~/store/mall/user';
import { Message } from '@arco-design/web-vue';
import { ProductList } from '~/components/mall/user/product-list';
import goodsApi from '~/api/mall/goods';
import reviewApi from '~/api/mall/review';
import mallApi from '~/api/mall';
definePageMeta({
  layout: 'mall'
});
// 收藏相关状态
const isFavorited = ref(false);
const favoriteLoading = ref(false);
const route = useRoute();
const router = useRouter();
const productId = computed(() => String(route.params.id));
const currentSkuIndex = ref(0)
// 商品图片相关
const currentImageIndex = ref(0);

// 当前商品分类
const currentCategory = ref({})

// 商品的sku列表
const skuList = ref([])
const skuSpecMatrix = ref([])
const specGroups = ref([])
const currentImage = computed(() => product.value.images[currentImageIndex.value]);

const selectImage = (index) => {
  currentImageIndex.value = index;
};

const unorderedEqual = (arr1, arr2) => {
  if (arr1.length !== arr2.length) return false;
  // 先对数组进行排序，再进行浅比较
  const sorted1 = [...arr1].sort();
  const sorted2 = [...arr2].sort();
  if (sorted1.length !== sorted2.length) return false;
  return sorted1.every((val, i) => Number.parseInt(val) === Number.parseInt( sorted2[i]));
}

// 商品规格选择
const selectedSpecs = ref({});

const changeCurrentSkuIndex = (id) => {
  for (let i in skuList.value) {
    let item = skuList.value[i]
    if(id===item.id){
      currentSkuIndex.value = i
      return
    }
  }
}
// 更新当前的sku 匹配skuSpecMatrix
const updateCurrentSku = () => {
  let specIds = []
  for (let key in selectedSpecs.value) {
    let item = selectedSpecs.value[key]
    specIds.push(item.id)
  }
  for (let key in skuSpecMatrix.value) {
    let item = skuSpecMatrix.value[key]
    let targetIds = key.split('_')
    if(unorderedEqual(specIds,targetIds)){
      return changeCurrentSkuIndex(item.skuId)
    }
  }
}
// 更新当前的sku 匹配skuList
// const updateCurrentSku = () => {
//   let flag = true
//   for (let key in skuList.value) {
//     let item = skuList.value[key]
//     let specIds = item.specIds || {}
//     flag = true
//     for (let specIdsKey in specIds) {
//       let specItem = specIds[specIdsKey]
//       if (specItem !== selectedSpecs.value[specIdsKey].id){
//         flag = false
//         break
//       }
//     }
//     if (flag){
//       console.log(item,'sku')
//       return changeCurrentSkuIndex(item.id)
//     }
//   }
// }
const selectSpec = (specName, option) => {
  selectedSpecs.value[specName] = option;
  if(Object.keys(selectedSpecs.value).length === specGroups.value.length){
    updateCurrentSku()
  }

};

// 商品数量
const quantity = ref(1);

const increaseQuantity = () => {
  if (quantity.value < 99) {
    quantity.value++;
  }
};

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--;
  }
};

// 选项卡
const tabs = [
  { name: '商品介绍' },
  { name: '规格参数' },
  // { name: '包装清单' },
  // { name: '售后保障' },
  { name: '商品评价' }
];
const activeTab = ref(0);

// 评价相关数据
const reviews = ref([]);
const reviewStats = ref(null);
const reviewsLoading = ref(false);
const reviewTotal = ref(0);
const reviewPage = ref(1);
const reviewPageSize = ref(10);
const currentReviewFilter = ref(0);

// 评价筛选选项
const reviewFilters = ref([
  { name: '全部评价', value: 'all', count: 0 },
  { name: '好评', value: 'good', count: 0 },
  { name: '中评', value: 'medium', count: 0 },
  { name: '差评', value: 'bad', count: 0 },
  { name: '有图', value: 'with_images', count: 0 }
]);

// 时间格式化函数
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';

  try {
    let ts = Number(timestamp);
    if (isNaN(ts) || ts <= 0) return '';

    if (ts.toString().length === 10) {
      ts = ts * 1000;
    }

    const date = new Date(ts);
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('格式化时间戳失败:', error);
    return '';
  }
};

// 格式化SKU规格
const formatSkuSpecifications = (specifications) => {
  if (!specifications) return '';

  try {
    if (typeof specifications === 'string') {
      const specs = JSON.parse(specifications);
      return Object.entries(specs).map(([key, value]) => `${key}: ${value}`).join(', ');
    } else if (typeof specifications === 'object') {
      return Object.entries(specifications).map(([key, value]) => `${key}: ${value}`).join(', ');
    }
  } catch (error) {
    console.error('格式化规格失败:', error);
  }

  return specifications.toString();
};

// 图片预览
const previewImage = (imageUrl) => {
  // 这里可以实现图片预览功能，比如使用模态框
  window.open(imageUrl, '_blank');
};

// 统计有图片的评价数量
const updateImageReviewCount = async () => {
  try {
    // 获取所有评价来统计有图片的数量
    const response = await reviewApi.getProductReviews(productId.value, {
      page: 1,
      pageSize: 1000, // 获取足够多的数据来统计
      hasImages: true
    });

    if (response.code === 200) {
      reviewFilters.value[4].count = response.data.total || 0;
    }
  } catch (error) {
    console.error('统计有图片评价数量失败:', error);
  }
};

// 计算评价统计的详细信息
const reviewStatsComputed = computed(() => {
  if (!reviewStats.value) return null;

  const stats = reviewStats.value;
  const distribution = stats.ratingDistribution || {};

  return {
    ...stats,
    // 计算各星级的百分比
    ratingPercentages: {
      5: stats.total > 0 ? Math.round((distribution['5'] || 0) / stats.total * 100) : 0,
      4: stats.total > 0 ? Math.round((distribution['4'] || 0) / stats.total * 100) : 0,
      3: stats.total > 0 ? Math.round((distribution['3'] || 0) / stats.total * 100) : 0,
      2: stats.total > 0 ? Math.round((distribution['2'] || 0) / stats.total * 100) : 0,
      1: stats.total > 0 ? Math.round((distribution['1'] || 0) / stats.total * 100) : 0,
    },
    // 计算好中差评数量
    goodCount: (distribution['4'] || 0) + (distribution['5'] || 0),
    mediumCount: distribution['3'] || 0,
    badCount: (distribution['1'] || 0) + (distribution['2'] || 0)
  };
});

// 商品数据
const product = ref({
  id: 0,
  name: '加载中...',
  subtitle: '',
  categoryId: 0,
  categoryName: '',
  price: '0.00',
  originalPrice: '0.00',
  discount: null,
  rating: 5.0,
  reviewCount: 0,
  stock: 0,
  location: '北京',
  deliveryFee: '0.00',
  isNew: false,
  isHot: false,
  services: [],
  images: [],
  specGroups: [],
  params: [],
  detailImages: [],
  specifications: [],
  packageList: [],
  afterSales: [],
  reviews: [],
  attributes: [] // 添加attributes字段用于存储规格参数
});

// 运费计算相关数据
const freightInfo = ref({
  freight: 0,
  isFreeShipping: false,
  calculationDetails: null,
  templateInfo: null,
  loading: false
});

const validateSpecSelection = () => {
    // 检查是否所有规格都已选择
    const unselectedSpecs = [];
    product.value.specGroups.forEach(spec => {
      if (!selectedSpecs.value[spec.name]) {
        unselectedSpecs.push(spec.name);
      }
    });
    
    if (unselectedSpecs.length > 0) {
      Message.error(`请选择${unselectedSpecs.join('、')}规格后再继续`);
      return false;
    }
    return true;
}

// 推荐商品
const recommendedProducts = ref([]);

// 购物车状态
const cartStore = useMallCartStore();
// 商品状态
const goodsStore = useMallGoodsStore();
// 立即购买状态
const immediatePurchaseStore = useMallImmediatePurchaseStore();
// 历史记录状态
const historyStore = useMallHistoryStore();
// 本地存储状态（获取城市代码）
const localStore = useMallLocalStore();
// 用户状态
const userStore = useMallUserStore();

// 添加商品到购物车
const addToCart = async () => {
  try {
    if (!validateSpecSelection()) {
      return;
    }
    // 检查库存是否足够
    const currentStock = skuList.value[currentSkuIndex.value].stock;
    if (currentStock < quantity.value) {
      Message.error(`库存不足，当前库存仅剩${currentStock}件`);
      return;
    }
    
    // 收集已选规格
    const specs = {};
    product.value.specGroups.forEach(spec => {
      specs[spec.name] = selectedSpecs.value[spec.name].name;
    });
    product.value.originalPrice = skuList.value[currentSkuIndex.value].marketPrice
    product.value.price = skuList.value[currentSkuIndex.value].salesPrice
    product.value.stock = skuList.value[currentSkuIndex.value].stock
    product.value.skuCode = skuList.value[currentSkuIndex.value].skuCode
    product.value.skuName = skuList.value[currentSkuIndex.value].skuName
    product.value.skuId = skuList.value[currentSkuIndex.value].id
    
    // 将运费信息添加到商品数据中
    product.value.freightInfo  = freightInfo.value;
    
    const result = await cartStore.addToCart(
      product.value,
      quantity.value,
      specs
    );
    
    if (result.success) {
      Message.success(result.message);
    } else {
      Message.error(result.message || '加入购物车失败');
    }
  } catch (error) {
    console.error('加入购物车出错:', error);
    Message.error('加入购物车失败，请稍后再试');
  }
};

// 立即购买
const buyNow = async () => {
  try {
    if (!validateSpecSelection()) {
      return;
    }
    // 检查库存是否足够
    const currentStock = skuList.value[currentSkuIndex.value].stock;
    if (currentStock < quantity.value) {
      Message.error(`库存不足，当前库存仅剩${currentStock}件`);
      return;
    }
    
    // 收集已选规格
    const specs = {};
    product.value.specGroups.forEach(spec => {
      specs[spec.name] = selectedSpecs.value[spec.name].name;
    });
    
    // 设置价格
    product.value.originalPrice = skuList.value[currentSkuIndex.value].marketPrice;
    product.value.price = skuList.value[currentSkuIndex.value].salesPrice;
    product.value.stock = skuList.value[currentSkuIndex.value].stock
    product.value.skuCode = skuList.value[currentSkuIndex.value].skuCode
    product.value.skuName = skuList.value[currentSkuIndex.value].skuName
    product.value.skuId = skuList.value[currentSkuIndex.value].id
    // 设置立即购买的商品
    const result = await immediatePurchaseStore.setPurchaseItem(
      product.value,
      quantity.value,
      specs
    );
    
    if (result.success) {
      // 跳转到结账页面
      router.push('/mall/checkout');
    } else {
      Message.error(result.message || '立即购买失败');
    }
  } catch (error) {
    console.error('立即购买出错:', error);
    Message.error('立即购买失败，请稍后再试');
  }
};

// 计算运费
const calculateFreight = async () => {
  try {
    if (!product.value.id || !skuList.value[currentSkuIndex.value]) {
      return;
    }
    
    freightInfo.value.loading = true;
    
    const params = {
      spuId: product.value.id,
      skuId: skuList.value[currentSkuIndex.value].id,
      quantity: quantity.value,
      provinceCode: localStore.locationInfo.cityCode || '440000' // 默认使用广东省代码
    };
    
    const result = await goodsApi.calculateSingleProductFreight(params);
    
    if (result.code === 200) {
      console.log(result.data,'result.data')
      freightInfo.value = {
        ...result.data,
        loading: false,
        noFreightTemplate: false
      };
    } else {
      freightInfo.value.loading = false;
      // 处理"商品未配置运费模板"的错误情况
      if (result.code === 400 && result.message === '商品未配置运费模板') {
        // 设置一个标志，表示不应显示运费信息
        freightInfo.value.noFreightTemplate = true;
        freightInfo.value.isFreeShipping = false;
      }
      console.error('运费计算失败:', result.message);
    }
  } catch (error) {
    freightInfo.value.loading = false;
    console.error('运费计算出错:', error);
  }
};

// 加载评价统计数据
const loadReviewStats = async () => {
  try {
    const response = await reviewApi.getProductReviewStats(productId.value);
    if (response.code === 200) {
      reviewStats.value = response.data;

      // 更新筛选选项的数量
      const distribution = response.data.ratingDistribution || {};
      reviewFilters.value[0].count = response.data.total || 0;
      reviewFilters.value[1].count = (distribution['4'] || 0) + (distribution['5'] || 0); // 好评 (4-5星)
      reviewFilters.value[2].count = distribution['3'] || 0; // 中评 (3星)
      reviewFilters.value[3].count = (distribution['1'] || 0) + (distribution['2'] || 0); // 差评 (1-2星)
      reviewFilters.value[4].count = 0; // 有图评价数量，需要从评价列表中统计
    }
  } catch (error) {
    console.error('加载评价统计失败:', error);
  }
};

// 加载评价列表
const loadReviews = async () => {
  try {
    reviewsLoading.value = true;

    const params = {
      page: reviewPage.value,
      pageSize: reviewPageSize.value,
      sortBy: 'created_at',
      sortOrder: 'desc'
    };

    // 根据筛选条件添加参数
    const filterValue = reviewFilters.value[currentReviewFilter.value].value;
    if (filterValue === 'good') {
      params.rating = 'good';
    } else if (filterValue === 'medium') {
      params.rating = 'medium';
    } else if (filterValue === 'bad') {
      params.rating = 'bad';
    } else if (filterValue === 'with_images') {
      params.hasImages = true;
    }

    const response = await reviewApi.getProductReviews(productId.value, params);
    if (response.code === 200) {
      reviews.value = response.data.list || [];
      reviewTotal.value = response.data.total || 0;

      // 统计有图片的评价数量（只在第一次加载全部评价时统计）
      if (currentReviewFilter.value === 0 && reviewPage.value === 1) {
        updateImageReviewCount();
      }
    }
  } catch (error) {
    console.error('加载评价列表失败:', error);
    reviews.value = [];
    reviewTotal.value = 0;
  } finally {
    reviewsLoading.value = false;
  }
};

// 切换评价筛选
const changeReviewFilter = (index) => {
  currentReviewFilter.value = index;
  reviewPage.value = 1; // 重置到第一页
  loadReviews();
};

// 处理评价分页变化
const handleReviewPageChange = (page) => {
  reviewPage.value = page;
  loadReviews();
};

// 处理评价每页大小变化
const handleReviewPageSizeChange = (pageSize) => {
  reviewPageSize.value = pageSize;
  reviewPage.value = 1; // 重置到第一页
  loadReviews();
};

// 加载商品数据
const loadProductData = async () => {
  try {
    const resp = await goodsStore.getGoodsDetail(String(productId.value));
    product.value = resp.data;
    skuList.value = resp.data.skuList;
    skuSpecMatrix.value = resp.data.skuSpecMatrix;
    specGroups.value = resp.data.specGroups;
    currentCategory.value = resp.data.categories[0];

    // 加载商品数据后计算运费
    calculateFreight();
  } catch (error) {
    console.error('加载商品数据失败:', error);
  }
};
// 检查收藏状态
const checkFavoriteStatus = async () => {
  // 检查用户是否登录
  if (!userStore.loggedIn) {
    isFavorited.value = false;
    return;
  }

  // 检查商品是否存在
  if (!product.value || !product.value.id) {
    return;
  }

  try {
    const response = await mallApi.favorite.checkFavoriteStatus({
      targetId: product.value.id,
      targetType: 1
    });

    if (response.code === 200) {
      isFavorited.value = response.data.isFavorited || false;
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error);
    isFavorited.value = false;
  }
};

// 处理收藏按钮点击
const handleFavoriteClick = async () => {
  // 检查用户是否登录
  if (!userStore.loggedIn) {
    Message.warning('请先登录后再收藏商品');
    // 可以跳转到登录页面
    router.push('/login');
    return;
  }

  // 检查商品是否存在
  if (!product.value || !product.value.id) {
    Message.error('商品信息不完整，无法收藏');
    return;
  }

  favoriteLoading.value = true;

  try {
    const response = await mallApi.favorite.toggleFavorite(
      product.value.id,
      isFavorited.value,
      1,
      `${isFavorited.value ? '取消收藏' : '收藏'}商品: ${product.value.name}`
    );

    if (response.code === 200) {
      isFavorited.value = !isFavorited.value;
      Message.success(isFavorited.value ? '收藏成功' : '取消收藏成功');
    } else {
      Message.error(response.message || '操作失败，请稍后再试');
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    Message.error('操作失败，请稍后再试');
  } finally {
    favoriteLoading.value = false;
  }
};

// 页面加载时初始化数据
onMounted(async () => {
  // 尝试恢复用户会话
  if (!userStore.loggedIn) {
    await userStore.restoreSession();
  }

  loadProductData();
});

// 监听选项卡切换，当切换到评价选项卡时加载评价数据
watch(() => activeTab.value, (newTab) => {
  if (newTab === 2) { // 评价选项卡
    if (!reviewStats.value) {
      loadReviewStats();
    }
    if (reviews.value.length === 0) {
      loadReviews();
    }
  }
});

// 监听SKU变化，重新计算运费
watch(() => currentSkuIndex.value, () => {
  calculateFreight();
});

// 监听数量变化，重新计算运费
watch(() => quantity.value, () => {
  calculateFreight();
});

// 监听商品数据变化，将商品添加到浏览历史
watch(() => product.value, (newProduct) => {
  if (newProduct && newProduct.id) {
    console.log('商品数据变化:', newProduct);
        // 收集已选规格
        const specs = {};
        newProduct.specGroups.forEach(spec => {
      specs[spec?.name] = selectedSpecs.value[spec?.name]?.name;
    });
    newProduct.originalPrice = newProduct.skuList[0].marketPrice
    newProduct.price = newProduct.skuList[0].salesPrice
    newProduct.stock = newProduct.skuList[0].stock
    newProduct.skuCode = newProduct.skuList[0].skuCode
    newProduct.skuName = newProduct.skuList[0].skuName
    newProduct.skuId = newProduct.skuList[0].id
    // 将商品添加到浏览历史记录
    historyStore.addToHistory(newProduct);
    // 检查收藏状态
    checkFavoriteStatus();
  }
}, { deep: true });
</script>

<style scoped>
.section-title {
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 25%;
  height: 50%;
  width: 4px;
  background-color: #e4393c;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card {
  background-color: #fff;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.product-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transform: translateY(-2px);
  border-color: #e4393c;
}

.pagination-btn {
  transition: all 0.2s;
}

.buy-now-btn, .add-to-cart-btn, .favorite-btn {
  transition: all 0.3s;
}

.tab-item {
  transition: all 0.3s;
}

.spec-option {
  transition: all 0.2s;
}

.spec-option:hover {
  border-color: #e4393c;
  color: #e4393c;
}

.thumbnail-item {
  transition: all 0.2s;
}

.thumbnail-item:hover {
  border-color: #e4393c;
}
</style>
