<template>
  <div class="container mx-auto px-4 py-4">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <span>全部商品分类</span>
    </div>

    <!-- 分类列表内容 -->
    <div class="bg-white rounded-lg p-6 mb-4">
      <h1 class="text-xl font-bold mb-6 pb-2 border-b">全部商品分类</h1>
      
      <!-- 分类列表 -->
      <div class="category-list">
        <div v-for="(category, index) in categories" :key="index" class="category-section mb-8">
          <div class="category-header flex items-center mb-4">
            <NuxtLink :to="`/mall/category/${category.id}`" class="flex items-center hover:text-red-600 w-full">
              <div class="category-icon w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                <img :src="category.icon || 'https://placehold.co/48x48/f3f4f6/666666?text=' + category.name.substring(0, 1)" 
                    :alt="category.name" class="w-8 h-8 object-contain">
              </div>
              <h2 class="text-lg font-bold">{{ category.name }}</h2>
            </NuxtLink>
          </div>
          
          <div class="category-content pl-16">
            <!-- 二级分类 -->
            <div class="grid grid-cols-6 gap-4">
              <div v-for="(subCategory, subIndex) in category.children" :key="subIndex" 
                   class="subcategory-item">
                <NuxtLink :to="`/mall/category/${subCategory.id}`" class="block">
                  <div class="subcategory-image bg-gray-50 rounded-lg p-2 mb-2 flex items-center justify-center h-24">
                    <img :src="subCategory.image || 'https://placehold.co/80x80/f9fafb/666666?text=' + subCategory.name.substring(0, 2)" 
                         :alt="subCategory.name" class="max-h-20 max-w-full object-contain">
                  </div>
                  <div class="subcategory-name text-center text-sm font-medium truncate hover:text-red-600">
                    {{ subCategory.name }}
                  </div>
                </NuxtLink>
                
                <!-- 三级分类 
                <div v-if="subCategory.children && subCategory.children.length > 0" class="third-level-categories mt-2">
                  <div class="flex flex-wrap">
                    <div v-for="(thirdCategory, thirdIndex) in subCategory.children" :key="thirdIndex" 
                         class="w-full mb-1">
                      <NuxtLink :to="`/mall/category/${thirdCategory.id}`" 
                                class="text-xs text-gray-600 hover:text-red-600 truncate block">
                        {{ thirdCategory.name }}
                      </NuxtLink>
                      
                      <div v-if="thirdCategory.children && thirdCategory.children.length > 0" class="fourth-level-categories ml-2 mt-1">
                        <div class="flex flex-wrap">
                          <div v-for="(fourthCategory, fourthIndex) in thirdCategory.children" :key="fourthIndex" 
                               class="w-full mb-1">
                            <NuxtLink :to="`/mall/category/${fourthCategory.id}`" 
                                      class="text-xs text-gray-500 hover:text-red-600 truncate block">
                              - {{ fourthCategory.name }}
                            </NuxtLink>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门推荐 -->
    <ProductList
      title="热门推荐"
      :rightSwitch="true"
      :imageWidth="0"
      :imageHeight="0"
      :columnsPerRow="2"
      :columnsPerRowMd="3"
      :columnsPerRowLg="6"
      :count="6"
      :enableResponsiveCount="false"
      :autoLoad="true"
    />

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useMallCategoryStore } from '@/store/mall/category';
import { ProductList } from '~/components/mall/user/product-list';
definePageMeta({
  layout: 'mall'
});

// 使用分类状态管理
const categoryStore = useMallCategoryStore();

// 分类数据
const categories = ref([]);

// 加载分类数据
const loadCategories = async () => {
  // 如果已经有分类数据，直接使用
  if (categoryStore.hasCategories) {
    categories.value = categoryStore.categoryTree;
    return;
  }
  
  // 否则从API获取分类数据
  const result = await categoryStore.getCategoryTree();
  if (result.success) {
    categories.value = categoryStore.categoryTree;
  }
};

// 页面加载时获取分类数据
onMounted(() => {
  loadCategories();
});

// 热门推荐产品
const hotProducts = ref([
  { 
    id: 1001, 
    name: 'HP惠普 Color LaserJet Pro MFP M479fdw彩色激光多功能一体机', 
    price: '4599.00', 
    sales: 2358, 
    image: 'https://placehold.co/200x200/ffffff/333333?text=惠普打印机' 
  },
  { 
    id: 1002, 
    name: '联想(Lenovo)小新Pro 14英寸全面屏轻薄笔记本电脑', 
    price: '5299.00', 
    sales: 3421, 
    image: 'https://placehold.co/200x200/ffffff/333333?text=联想笔记本' 
  },
  { 
    id: 1003, 
    name: '得力(deli)33123 碎纸机 5级保密 办公商用大功率电动', 
    price: '999.00', 
    sales: 1245, 
    image: 'https://placehold.co/200x200/ffffff/333333?text=得力碎纸机' 
  },
  { 
    id: 1004, 
    name: '齐心(COMIX)B2111 高清视频会议摄像头 1080P高清广角', 
    price: '699.00', 
    sales: 876, 
    image: 'https://placehold.co/200x200/ffffff/333333?text=会议摄像头' 
  },
  { 
    id: 1005, 
    name: '西部数据(WD)2TB USB3.0移动硬盘Elements 新元素系列', 
    price: '459.00', 
    sales: 4532, 
    image: 'https://placehold.co/200x200/ffffff/333333?text=WD移动硬盘' 
  },
]);

// 最近浏览产品
const recentlyViewed = ref([
  { 
    id: 2001, 
    name: '罗技(Logitech)MX Keys无线蓝牙键盘', 
    price: '799.00', 
    image: 'https://placehold.co/140x140/ffffff/333333?text=罗技键盘' 
  },
  { 
    id: 2002, 
    name: '戴尔(DELL)P2422H 23.8英寸显示器', 
    price: '1299.00', 
    image: 'https://placehold.co/140x140/ffffff/333333?text=戴尔显示器' 
  },
  { 
    id: 2003, 
    name: '得力(deli)5548 强力订书机 50页大号重型订书器', 
    price: '35.80', 
    image: 'https://placehold.co/140x140/ffffff/333333?text=订书机' 
  },
  { 
    id: 2004, 
    name: '晨光(M&G)APYY8C75 中性笔 0.5mm 黑色 12支/盒', 
    price: '13.90', 
    image: 'https://placehold.co/140x140/ffffff/333333?text=中性笔' 
  },
  { 
    id: 2005, 
    name: '齐心(COMIX)B2112 无线蓝牙音箱 便携式', 
    price: '89.00', 
    image: 'https://placehold.co/140x140/ffffff/333333?text=蓝牙音箱' 
  },
  { 
    id: 2006, 
    name: '飞利浦(PHILIPS)BDM4350UC 43英寸4K显示器', 
    price: '3899.00', 
    image: 'https://placehold.co/140x140/ffffff/333333?text=飞利浦显示器' 
  },
]);
</script>

<style scoped>
.section-title {
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 25%;
  height: 50%;
  width: 4px;
  background-color: #e4393c;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card {
  background-color: #fff;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.product-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transform: translateY(-2px);
  border-color: #e4393c;
}

.subcategory-item:hover .subcategory-name {
  color: #e4393c;
}
</style>
