<template>
  <div class="container mx-auto px-4 py-4">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/category" class="hover:text-red-600">全部商品分类</NuxtLink> &gt; 
      <span>{{ currentCategory.name }}</span>
    </div>

    <!-- 分类筛选区域 -->
    <div class="bg-white rounded-lg p-4 mb-4">
      <!-- 当前分类信息 -->
      <div class="category-info flex items-center mb-4 pb-4 border-b">
        <div class="category-icon w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
          <img :src="currentCategory.imageUrl || 'https://placehold.co/48x48/f3f4f6/666666?text=' + currentCategory.name?.substring(0, 1)"
               :alt="currentCategory.name" class="w-8 h-8 object-contain">
        </div>
        <div>
          <h1 class="text-xl font-bold">{{ currentCategory.name }}</h1>
          <div class="text-gray-500 text-sm mt-1">共 {{ totalProducts }} 件商品</div>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <!-- 品牌筛选 -->
        <div class="filter-row flex items-center py-3 border-b">
          <div class="filter-label w-20 text-gray-600 flex-shrink-0">品牌：</div>
          <div class="filter-options flex-1 flex flex-wrap items-center relative">
            <div v-for="(brand, index) in brands" :key="brand.id || index"
                 :class="['filter-option mr-4 mb-2 px-3 py-1 rounded cursor-pointer text-sm flex items-center',
                          selectedBrand.id === brand.id ? 'bg-red-600 text-white' : 'bg-gray-100 hover:bg-gray-200']"
                 @click="selectBrand(brand)">
              <span>{{ brand.name }}</span>
              <span v-if="brand.productCount > 0" class="ml-1 text-xs opacity-75">({{ brand.productCount }})</span>
            </div>
            <div 
              class="more-btn ml-2 mb-2 px-3 py-1 rounded cursor-pointer text-sm bg-gray-100 hover:bg-gray-200 flex items-center"
              @click="toggleBrandsDisplay"
            >
              <span>{{ isAllBrandsLoaded ? '收起' : '展开' }}</span>
            </div>
          </div>
        </div>

        <!-- 价格筛选 -->
        <div class="filter-row flex items-center py-3 border-b">
          <div class="filter-label w-20 text-gray-600 flex-shrink-0">价格：</div>
          <div class="filter-options flex-1 flex flex-wrap items-center">
            <div v-for="(priceRange, index) in priceRanges" :key="index" 
                 :class="['filter-option mr-4 mb-2 px-3 py-1 rounded cursor-pointer text-sm', 
                          selectedPriceRange === priceRange ? 'bg-red-600 text-white' : 'bg-gray-100 hover:bg-gray-200']"
                 @click="selectPriceRange(priceRange)">
              {{ priceRange.label }}
            </div>
            <div class="custom-price-range flex items-center">
              <a-input-number size="mini"  v-model="customPriceMin"  placeholder="¥" class="w-20 h-8 border rounded px-2 text-sm"/>
              <span class="mx-2">-</span>
              <a-input-number size="mini"  :min="customPriceMin"  v-model="customPriceMax" placeholder="¥" class="w-20 h-8 border rounded px-2 text-sm"/>
              <a-button class="ml-2 bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded text-sm"
                      @click="applyCustomPriceRange">确定</a-button>
            </div>
          </div>
        </div>

        <!-- 其他筛选条件 -->
        <div class="filter-row flex items-center py-3 border-b">
          <div class="filter-label w-20 text-gray-600 flex-shrink-0">标签选项：</div>
          <div class="filter-options flex-1 flex flex-wrap items-center">
            <div v-for="(option, index) in filterOptions" :key="index" 
                 :class="['filter-option mr-4 mb-2 px-3 py-1 rounded cursor-pointer text-sm flex items-center', 
                          option.selected ? 'bg-red-600 text-white' : 'bg-gray-100 hover:bg-gray-200']"
                 @click="toggleOption(index)">
              <span v-if="option.selected" class="mr-1">✓</span>
              {{ option.label }}
            </div>
          </div>
        </div>
        
        <!-- 服务选项 -->
        <div class="filter-row flex items-center py-3">
          <div class="filter-label w-20 text-gray-600 flex-shrink-0">服务选项：</div>
          <div class="filter-options flex-1 flex flex-wrap items-center">
            <div v-for="(service, index) in services" :key="service.id || index" 
                 :class="['filter-option mr-4 mb-2 px-3 py-1 rounded cursor-pointer text-sm flex items-center', 
                          service.selected ? 'bg-red-600 text-white' : 'bg-gray-100 hover:bg-gray-200']"
                 @click="toggleService(index)">
              <span v-if="service.selected" class="mr-1">✓</span>
              {{ service.name }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品排序 -->
    <div class="bg-white rounded-lg p-3 mb-4 flex justify-between items-center">
      <div class="sort-options flex">
        <div v-for="(sortOption, index) in sortOptions" :key="index"
             :class="['sort-option px-4 py-2 cursor-pointer text-sm border-r', 
                      currentSort === sortOption.value ? 'text-red-600 font-medium' : 'text-gray-600']"
             @click="changeSort(sortOption.value)">
          {{ sortOption.label }}
          <span v-if="sortOption.value === 'price'" class="ml-1">
            <span v-if="currentSort === 'price' && sortDirection === 'asc'">↑</span>
            <span v-else-if="currentSort === 'price' && sortDirection === 'desc'">↓</span>
          </span>
        </div>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="bg-white rounded-lg p-4 mb-4">
      <div class="grid grid-cols-4 gap-6">
        <div v-for="(product, index) in allProducts" :key="index" class="product-card p-4 rounded">
          <NuxtLink :to="`/mall/product/${product.id}`" class="block">
            <div class="w-full h-full mb-3 flex items-center justify-center">
              <a-image :src="product.imageUrl || `/placeholder.png?text=${encodeURIComponent(product.name?.substring(0, 1) || '商')}`" 
                     :alt="product.name" 
                     class="max-h-full max-w-full object-contain"
                     :preview="false"
                     fallback="/placeholder.png"
                     width="100%"
                     height="316"
                     fit="contain" />
            </div>
            <div class="product-name text-sm mb-2 line-clamp-2 h-10">{{ product.name }}</div>
            <div class="product-price text-red-600 font-bold mb-2">¥{{ product.price }}</div>
            <div class="product-meta flex justify-between items-center text-xs text-gray-500 mb-3">
              <span>销量: {{ product.totalSales }}</span>
<!--              <span>评价: {{ product.reviews }}</span>-->
            </div>
<!--            <div class="product-shop text-xs text-gray-500 mb-3 truncate">{{ product.shop }}</div>-->
          </NuxtLink>

        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination flex justify-center mt-8">
        <a-pagination
          v-model:current="currentPage"
          show-total
          show-jumper 
          show-page-size
          :total="totalProducts"
          :page-size="pageSize"
          :page-size-options="pageSizeOptions"
          @change="handlePageChange"
          @page-size-change="handlePageSizeChange"
        />
      </div>
    </div>

    <!-- 猜你喜欢 -->
    <ProductList
      title="猜你喜欢"
      :rightSwitch="true"
      :imageWidth="0"
      :imageHeight="0"
      :columnsPerRow="2"
      :columnsPerRowMd="3"
      :columnsPerRowLg="6"
      :count="6"
      :autoLoad="true"
      :enableResponsiveCount="false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMallCartStore } from '~/store/mall/cart';
import { Message } from '@arco-design/web-vue';
import { useMallGoodsStore } from '~/store/mall/goods';
import { useMallCategoryStore } from '~/store/mall/category';
import { storeToRefs } from 'pinia';
import { ProductList } from '~/components/mall/user/product-list';
import { useMallImmediatePurchaseStore } from '~/store/mall/immediate-purchase';
definePageMeta({
  layout: 'mall'
});

const route = useRoute();
const router = useRouter();
const categoryId = computed(() => String(route.params.id));

// 当前分类信息
const currentCategory = ref({
  id: 0,
  name: '加载中...',
  icon: ''
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(16);
const pageSizeOptions = ref([4, 8, 16,32]);
const totalProducts = ref(0);
// 计算总页数
const totalPages = computed(() => Math.ceil(totalProducts.value / pageSize.value));

// 购物车状态
const cartStore = useMallCartStore();
// 商品状态
const goodsStore = useMallGoodsStore();
const { searchParams } = storeToRefs(goodsStore);
// 商品分类状态
const categoryStore = useMallCategoryStore();
// 筛选相关
const brands = ref([]);
const isAllBrandsLoaded = ref(false);
const selectedBrand = ref({ id: null, name: '全部' });
const priceRanges = ref([
  { label: '全部', min: 0, max: Infinity },
  { label: '0-100元', min: 0, max: 100 },
  { label: '100-500元', min: 100, max: 500 },
  { label: '500-1000元', min: 500, max: 1000 },
  { label: '1000-5000元', min: 1000, max: 5000 },
  { label: '5000元以上', min: 5000, max: Infinity },
]);
const selectedPriceRange = ref(priceRanges.value[0]);
const customPriceMin = ref('');
const customPriceMax = ref('');

const filterOptions = ref([
  { label: '仅看有货', selected: false },
  { label: '京东配送', selected: false },
  { label: '可选服务', selected: false },
  { label: '新品', selected: false },
  { label: '促销商品', selected: false },
]);

// 服务选项
const services = ref([]);

// 排序相关
const sortOptions = ref([
  { label: '综合排序', value: 'default' },
  { label: '销量', value: 'sales' },
  { label: '评论数', value: 'reviews' },
  { label: '价格', value: 'price' }
]);
const currentSort = ref('default');
const sortDirection = ref('asc');

// 商品数据
const allProducts = ref([]);
const displayProducts = computed(() => {
  // 根据筛选条件过滤商品
  let filtered = allProducts.value.filter(product => {
    // 品牌筛选
    if (selectedBrand.value.id && product.brandId !== selectedBrand.value.id) {
      return false;
    }
    
    // 服务筛选
    const selectedServices = services.value.filter(service => service.selected);
    if (selectedServices.length > 0) {
      // 检查商品是否提供所有选中的服务
      const productServiceIds = product.serviceIds || [];
      for (const service of selectedServices) {
        if (!productServiceIds.includes(service.id)) {
          return false;
        }
      }
    }
    
    // 价格筛选
    const price = parseFloat(product.price);
    if (price < selectedPriceRange.value.min || price > selectedPriceRange.value.max) {
      return false;
    }
    
    // 其他筛选条件
    if (filterOptions.value[0].selected && !product.inStock) {
      return false;
    }
    if (filterOptions.value[1].selected && !product.jdDelivery) {
      return false;
    }
    if (filterOptions.value[2].selected && !product.hasService) {
      return false;
    }
    if (filterOptions.value[3].selected && !product.isNew) {
      return false;
    }
    if (filterOptions.value[4].selected && !product.isPromotion) {
      return false;
    }
    
    return true;
  });
  
  // 排序
  filtered.sort((a, b) => {
    if (currentSort.value === 'default') {
      return 0;
    } else if (currentSort.value === 'sales') {
      return b.sales - a.sales;
    } else if (currentSort.value === 'reviews') {
      return b.reviews - a.reviews;
    } else if (currentSort.value === 'price') {
      const priceA = parseFloat(a.price);
      const priceB = parseFloat(b.price);
      return sortDirection.value === 'asc' ? priceA - priceB : priceB - priceA;
    }
    return 0;
  });
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filtered.slice(start, end);
});

// 分页显示逻辑
const displayPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;
  
  if (totalPages.value <= maxPagesToShow) {
    // 如果总页数小于等于要显示的最大页数，则显示所有页
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    // 显示当前页附近的页码
    const halfMaxPages = Math.floor(maxPagesToShow / 2);
    let startPage = Math.max(currentPage.value - halfMaxPages, 1);
    let endPage = startPage + maxPagesToShow - 1;
    
    if (endPage > totalPages.value) {
      endPage = totalPages.value;
      startPage = Math.max(endPage - maxPagesToShow + 1, 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
  }
  
  return pages;
});

// 推荐商品
const recommendedProducts = ref([
  { 
    id: '2001', 
    name: '罗技(Logitech)MX Keys无线蓝牙键盘', 
    price: 799.00, 
    imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=罗技键盘',
    totalSales: 125
  },
  { 
    id: '2002', 
    name: '戴尔(DELL)P2422H 23.8英寸显示器', 
    price: 1299.00, 
    imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=戴尔显示器',
    totalSales: 89
  },
  { 
    id: '2003', 
    name: '得力(deli)5548 强力订书机 50页大号重型订书器', 
    price: 35.80, 
    imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=订书机',
    totalSales: 256
  },
  { 
    id: '2004', 
    name: '晨光(M&G)AGPY9507 中性笔 0.5mm', 
    price: 13.90, 
    imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=中性笔',
    totalSales: 1024
  },
  { 
    id: '2005', 
    name: '齐心(COMIX)B2112 无线蓝牙音箱 便携式', 
    price: 89.00, 
    imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=蓝牙音箱',
    totalSales: 76
  },
  { 
    id: '2006', 
    name: '飞利浦(PHILIPS)BDM4350UC 43英寸4K显示器', 
    price: 3899.00, 
    imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=飞利浦显示器',
    totalSales: 32
  },
]);


// 方法
const selectBrand = (brand) => {
  selectedBrand.value = brand.id ? brand : { id: null, name: '全部' };
  loadOnPageNotPageOne()
};

const selectPriceRange = (priceRange) => {
  selectedPriceRange.value = priceRange;
  loadOnPageNotPageOne()
};


const applyCustomPriceRange = () => {
  const min = customPriceMin.value ? parseFloat(customPriceMin.value) : 0;
  const max = customPriceMax.value ? parseFloat(customPriceMax.value) : Infinity;
  
  if (min >= 0 && max >= min) {
    selectedPriceRange.value = { 
      label: `${min}-${max === Infinity ? '∞' : max}元`, 
      min, 
      max 
    };
    loadOnPageNotPageOne()
  }
};

const toggleOption = (index) => {
  filterOptions.value[index].selected = !filterOptions.value[index].selected;
  loadOnPageNotPageOne()
};

const toggleService = (index) => {
  services.value[index].selected = !services.value[index].selected;
  loadOnPageNotPageOne()
};

const changeSort = (sort) => {
  if (sort === 'price' && currentSort.value === 'price') {
    // 如果已经按价格排序，则切换排序方向
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    currentSort.value = sort;
    if (sort === 'price') {
      sortDirection.value = 'asc';
    }
  }
  loadOnPageNotPageOne()
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page) => {
  currentPage.value = page;
};

const buyNow = async (product) => {
  try {
    const immediatePurchaseStore = useMallImmediatePurchaseStore();
    product.price = product.skus[0].salesPrice
    product.stock = product.skus[0].stock
    product.skuId = product.skus[0].id
    product.image = product.imageUrl
    const result = await immediatePurchaseStore.setPurchaseItem(
      product,
      1,
      {}
    );
    
    if (result.success) {
      // 跳转到结账页面
      router.push('/mall/checkout');
    } else {
      Message.error(result.message || '立即购买失败');
    }
  } catch (error) {
    console.error('立即购买出错:', error);
    Message.error('立即购买失败，请稍后再试');
  }
};

// 添加商品到购物车
const addToCart = async (product) => {
  try {
    product.price = product.skus[0].salesPrice
    product.stock = product.skus[0].stock
    product.skuId = product.skus[0].id
    product.image = product.imageUrl
    
    const result = await cartStore.addToCart(product, 1);
    if (result.success) {
      Message.success(result.message);
    } else {
      Message.error(result.message || '加入购物车失败');
    }
  } catch (error) {
    console.error('加入购物车出错:', error);
    Message.error('加入购物车失败，请稍后再试');
  }
};


const changePageInfo = (pageInfo) =>{
  currentPage.value = pageInfo.value.currentPage
  totalProducts.value = pageInfo.value.total
  totalPages.value = pageInfo.value.totalPage
}

const buildTags = () => {
  // 标签选项
  const tagOptions = []
  filterOptions.value.forEach(item=>{
    if(item.selected){
      tagOptions.push(item.id)
    }
  })
  
  // 服务选项ID
  const serviceIds = []
  services.value.forEach(item=>{
    if(item.selected){
      serviceIds.push(item.id)
    }
  })
  
  return {
    tagOptions: tagOptions.join(','),
    serviceIds: serviceIds.join(',')
  }
}

const initQueryParams = ()=> {
  const minPrice = selectedPriceRange.value.min || 0;
  const maxPrice = !isNaN(parseFloat(selectedPriceRange.value.max)) && isFinite(selectedPriceRange.value.max) ? selectedPriceRange.value.max || null : null;
  let sortBy = null
  let sortOrder = null
  const { tagOptions, serviceIds } = buildTags()
  if(currentSort.value !== 'default'){
    sortBy = currentSort.value
    sortOrder = sortDirection.value
  }
  return {
    categoryId: currentCategory?.value.id || null,
    brandId: selectedBrand.value.id || null,
    keyword: searchParams.value.keyword,
    minPrice: minPrice,
    maxPrice: maxPrice,
    tags: tagOptions,
    serviceIds: serviceIds,
    sortBy: sortBy,
    sortOrder: sortOrder,
    page:currentPage.value,
    pageSize:pageSize.value,
  }
}

// 加载商品数据
const loadGoodsData = async () => {
  try {
    let products = [];
    const pageInfo = {};
    
        // 请求商品数据
    const goodsResponse = await goodsStore.queryGoods(initQueryParams());
    
    // 处理商品数据
    if (goodsResponse && goodsResponse.success) {
      products = goodsResponse.data.items || [];
      pageInfo.value = goodsResponse.data.pageInfo || {};
      changePageInfo(pageInfo)
    }
    allProducts.value = products;
    

  } catch (error) {
    console.error('加载商品数据失败:', error);
    Message.error('加载商品数据失败，请稍后再试');
  }
};
//加载分类下的标签数据

const loadCategoryTags = async () => {
  const resp = await categoryStore.getCategoryTags({
    page:1,
    pageSize:10,
  })
  if (resp && resp.success){
    filterOptions.value = resp.data.map(item=>{
      return {
        ...item,
        label:item.name,
        selected:false,
      }
    })

  }
}

// 加载分类数据
const loadCategoryData = async () => {
  // 模拟数据加载
  // 在实际应用中，这里应该是一个API调用
  const resp = await categoryStore.getCategoryInfoById(categoryId.value)
  await loadCategoryTags()
  
  // 并行请求品牌数据和服务数据
  const [brandsResponse, servicesResponse] = await Promise.all([
    loadCategoryBrands({ page: 1, pageSize: 10 }), // 使用新的分类品牌接口
    categoryStore.getServices({ page: 1, pageSize: 10 }) // 设置默认分页参数
  ]);
  isAllBrandsLoaded.value = false;

  // 处理品牌数据
  if (brandsResponse && brandsResponse.success) {
    // 将API返回的品牌数据保留id和name，并添加商品数量信息
    brands.value = [
      { id: null, name: '全部', productCount: 0 },
      ...brandsResponse.data.list.map(brand => ({
        id: brand.id,
        name: brand.name,
        logoUrl: brand.logoUrl,
        description: brand.description,
        productCount: brand.productCount
      }))
    ];
  } else {
    // 如果API请求失败，使用默认品牌列表作为备选
    brands.value = [
      { id: null, name: '全部', productCount: 0 },
      { id: '1', name: '惠普', productCount: 0 },
      { id: '2', name: '佳能', productCount: 0 },
      { id: '3', name: '爱普生', productCount: 0 },
      { id: '4', name: '联想', productCount: 0 },
      { id: '5', name: '戴尔', productCount: 0 },
      { id: '6', name: '华为', productCount: 0 },
      { id: '7', name: '小米', productCount: 0 },
      { id: '8', name: '三星', productCount: 0 },
      { id: '9', name: '得力', productCount: 0 },
      { id: '10', name: '齐心', productCount: 0 }
    ];
  }
  
  // 处理服务数据
  if (servicesResponse && servicesResponse.success) {
    // 将API返回的服务数据转换为前端需要的格式，并添加selected属性
    services.value = servicesResponse.data.items.map(item => ({
      ...item,
      selected: false
    })) || [];
  } else {
    // 如果API请求失败，使用默认服务列表作为备选
    services.value = [
      { id: '1', name: '7天无理由退换', selected: false },
      { id: '2', name: '京东配送', selected: false },
      { id: '3', name: '京东安装', selected: false },
      { id: '4', name: '京东保修', selected: false },
      { id: '5', name: '京东运费险', selected: false }
    ];
  }
  if (resp && resp.success){
    currentCategory.value = resp.data
  }else{
    currentCategory.value = {
      "id": null,
      "goodsParentCategoryId": "0",
      "name": "全部",
      "imageUrl": null,
      "description": "全部",
      "metaTitle": "",
      "metaKeywords": "",
      "metaDescription": "",
      "sortOrder": 1,
      "isEnabled": 1,
      "level": 0,
      "children": []
    }
  }
  await loadGoodsData();
};

const loadOnPageNotPageOne = () => {
  if (currentPage.value === 1){
    loadGoodsData()
  }else{
    currentPage.value = 1; // 重置页码
  }
}
// 加载分类品牌数据
const loadCategoryBrands = async (params = {}) => {
  try {
    const { page = 1, pageSize = 10 } = params;
    const response = await $fetch(`/api/v1/mall/categorybrands`, {
      method: 'GET',
      params: {
        categoryid: categoryId.value,
        page,
        pageSize
      }
    });

    return {
      success: response.code === 200,
      data: response.data,
      message: response.message
    };
  } catch (error) {
    console.error('加载分类品牌数据失败:', error);
    return {
      success: false,
      data: { list: [], pagination: {} },
      message: '加载品牌数据失败'
    };
  }
};

// 加载所有品牌数据
const loadAllBrands = async () => {
  try {
    const brandsResponse = await loadCategoryBrands({ page: 1, pageSize: 9999 });

    if (brandsResponse && brandsResponse.success) {
      // 将API返回的所有品牌数据保留id和name，并添加商品数量信息
      brands.value = [
        { id: null, name: '全部', productCount: 0 },
        ...brandsResponse.data.list.map(brand => ({
          id: brand.id,
          name: brand.name,
          logoUrl: brand.logoUrl,
          description: brand.description,
          productCount: brand.productCount
        }))
      ];
      isAllBrandsLoaded.value = true;
    } else {
      Message.error('加载品牌数据失败');
    }
  } catch (error) {
    console.error('加载所有品牌数据失败:', error);
    Message.error('加载品牌数据失败，请稍后再试');
  }
};

// 切换品牌展示状态（展开/收起）
const toggleBrandsDisplay = async () => {
  try {
    if (isAllBrandsLoaded.value) {
      // 如果已经展开，则收起（恢复到默认的10个品牌）
      const brandsResponse = await loadCategoryBrands({ page: 1, pageSize: 10 });

      if (brandsResponse && brandsResponse.success) {
        brands.value = [
          { id: null, name: '全部', productCount: 0 },
          ...brandsResponse.data.list.map(brand => ({
            id: brand.id,
            name: brand.name,
            logoUrl: brand.logoUrl,
            description: brand.description,
            productCount: brand.productCount
          }))
        ];
        isAllBrandsLoaded.value = false;
      } else {
        Message.error('加载品牌数据失败');
      }
    } else {
      // 如果未展开，则加载所有品牌
      await loadAllBrands();
    }
  } catch (error) {
    console.error('切换品牌展示状态失败:', error);
    Message.error('操作失败，请稍后再试');
  }
};

// 页面加载时初始化数据
onMounted(() => {
  console.log(route.query,route.params,'route')
  if(route.query.brandId){
    selectedBrand.value.id = route.query.brandId
  }
  loadCategoryData();
});

watch(
    () => searchParams.value.keyword, // 仅监听 keyword 参数
    (newKeyword, oldKeyword) => {
      if (newKeyword !== oldKeyword) {
        loadOnPageNotPageOne()
      }
    }
);

// 处理页码变化
const handlePageChange = (page) => {
  currentPage.value = page;
  loadGoodsData();
};

// 处理每页显示数量变化
const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; // 切换页面大小时重置为第一页
  loadGoodsData();
};

watch(currentPage,(newVal,oldVal)=>{
  loadGoodsData()
})
</script>

<style scoped>
.section-title {
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 25%;
  height: 50%;
  width: 4px;
  background-color: #e4393c;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card {
  background-color: #fff;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.product-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transform: translateY(-2px);
  border-color: #e4393c;
}

.filter-option {
  transition: all 0.2s;
}

.pagination-btn {
  transition: all 0.2s;
}
</style>
