<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/cart" class="hover:text-red-600">购物车</NuxtLink> &gt;
      <span>结算</span>
    </div>
    
    <!-- 结算页主体 -->
    <div class="checkout-container">
      <!-- 收货地址 -->
      <div class="address-section bg-white rounded-lg p-6 mb-4">
        <div class="section-title mb-4">收货地址</div>
        
        <div v-if="addresses.length > 0">
          <div class="grid grid-cols-3 gap-4">
            <div v-for="address in addresses" :key="address.id"
                 :class="['address-card border rounded-lg p-4 relative cursor-pointer',
                          selectedAddressId === address.id ? 'border-red-600' : 'border-gray-200']"
                 @click="selectAddress(address.id)">
              <div class="flex justify-between mb-2">
                <div class="font-medium">{{ address.name }}</div>
                <div class="text-gray-500">{{ address.phone }}</div>
              </div>
              <div class="text-gray-600 mb-2">
                {{ address.province }} {{ address.city }} {{ address.district }} {{ address.detail }}
              </div>
              <div class="text-sm text-gray-500">
                <span v-if="address.isDefault" class="text-red-600 mr-2">[默认]</span>
                <span v-if="address.tag">{{ address.tag }}</span>
              </div>
              
              <div v-if="selectedAddressId === address.id" class="absolute -top-2 -right-2 bg-red-600 text-white rounded-full p-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
              </div>
            </div>
            
            <!-- 添加新地址卡片 -->
            <div class="add-address-card border border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center cursor-pointer hover:border-red-600"
                 @click="showAddressForm = true">
              <div class="text-center text-gray-500 hover:text-red-600">
                <div class="flex justify-center mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                </div>
                <div>添加新地址</div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else class="empty-address text-center py-8">
          <p class="text-gray-500 mb-4">您还没有收货地址</p>
          <button class="bg-red-600 text-white px-4 py-2 rounded-full hover:bg-red-700 transition"
                  @click="showAddressForm = true">
            添加收货地址
          </button>
        </div>
        
        <!-- 添加地址表单 -->
        <AddressFormModal
          v-model:visible="showAddressForm"
          :edit-address="editingAddress"
          :region-data="regionData"
          @submit="handleAddressSubmit"
          @cancel="showAddressForm = false"
        />
      </div>

      <!-- 商品清单 -->
      <div class="order-items bg-white rounded-lg p-6 mb-4">
        <div class="section-title mb-4">商品清单</div>
        
        <div class="order-item-header grid grid-cols-12 gap-4 pb-2 border-b text-gray-500 text-sm">
          <div class="col-span-6">商品信息</div>
          <div class="col-span-2 text-center">单价</div>
          <div class="col-span-2 text-center">数量</div>
          <div class="col-span-2 text-center">小计</div>
        </div>
        
        <div class="order-items-list">
          <div v-for="item in selectedItems" :key="item.itemKey" class="order-item grid grid-cols-12 gap-4 py-4 border-b">
            <div class="col-span-6 flex items-center">
              <div class="product-image flex-shrink-0 w-16 h-16 bg-gray-50 rounded flex items-center justify-center mr-4">
                <img :src="item.image" :alt="item.name" class="max-h-full max-w-full object-contain">
              </div>
              <div class="product-info">
                <div class="product-name text-sm mb-1 line-clamp-2">{{ item.name }}</div>
                <div class="product-specs text-xs text-gray-500" v-if="item.specText">
                  {{ item.specText }}
                </div>
              </div>
            </div>
            
            <div class="col-span-2 text-center">
              <div class="price text-red-600">¥{{ item.price }}</div>
            </div>
            
            <div class="col-span-2 text-center">
              <div class="quantity">{{ item.quantity }}</div>
            </div>
            
            <div class="col-span-2 text-center">
              <div class="subtotal text-red-600 font-medium">¥{{ (item.price * item.quantity).toFixed(2) }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 发票信息 -->
      <div class="invoice-section bg-white rounded-lg shadow-sm p-6 mb-4">
        <div class="section-title mb-6">发票信息</div>

        <div class="invoice-options flex space-x-8 mb-6">
          <label class="inline-flex items-center cursor-pointer group">
            <input type="radio" v-model="invoiceType" value="none" class="form-radio h-5 w-5 text-red-600 focus:ring-red-500">
            <span class="ml-3 text-gray-700 group-hover:text-red-600 transition-colors">不开发票</span>
          </label>
          <label class="inline-flex items-center cursor-pointer group">
            <input type="radio" v-model="invoiceType" value="personal" class="form-radio h-5 w-5 text-red-600 focus:ring-red-500">
            <span class="ml-3 text-gray-700 group-hover:text-red-600 transition-colors">个人发票</span>
          </label>
          <label class="inline-flex items-center cursor-pointer group">
            <input type="radio" v-model="invoiceType" value="company" class="form-radio h-5 w-5 text-red-600 focus:ring-red-500">
            <span class="ml-3 text-gray-700 group-hover:text-red-600 transition-colors">企业发票</span>
          </label>
        </div>

        <!-- 个人发票抬头选择 -->
        <div v-if="invoiceType === 'personal'" class="personal-invoice-form">
          <div class="form-group">
            <div class="flex items-center justify-between mb-3">
              <label class="block text-gray-700 font-medium">选择抬头</label>
              <button
                @click="openNewHeaderModal('personal')"
                class="flex items-center space-x-1 px-3 py-1.5 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors text-xs font-medium"
              >
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <span>新建个人抬头</span>
              </button>
            </div>
            <div v-if="personalHeaders.length > 0">
              <div class="flex overflow-x-auto pb-4 space-x-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <!-- 现有抬头卡片 -->
                <div v-for="header in personalHeaders" :key="header.id"
                     :class="['invoice-header-card border rounded-lg p-4 relative cursor-pointer transition-all flex-shrink-0 w-64',
                              selectedHeaderId === header.id ? 'border-red-600 bg-red-50' : 'border-gray-200 hover:border-red-300']"
                     @click="selectInvoiceHeader(header.id)">
                  <div class="flex justify-between items-start mb-2">
                    <div class="font-medium text-gray-900 truncate pr-2">{{ header.headerName }}</div>
                    <button
                      @click.stop="editHeader(header)"
                      class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors flex-shrink-0"
                      title="编辑抬头"
                    >
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    </button>
                  </div>
                  <div class="text-sm text-gray-600 mb-3">个人发票</div>
                  <div v-if="header.isDefault" class="inline-block px-2 py-1 bg-red-100 text-red-600 text-xs rounded">默认抬头</div>

                  <!-- 选中状态指示器 -->
                  <div v-if="selectedHeaderId === header.id"
                       class="absolute top-2 right-12 w-5 h-5 bg-red-600 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-headers text-center py-8">
              <p class="text-gray-500 mb-4">您还没有个人发票抬头</p>
              <button class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
                      @click="openNewHeaderModal('personal')">
                添加个人抬头
              </button>
            </div>
          </div>
        </div>

        <!-- 企业发票抬头选择 -->
        <div v-if="invoiceType === 'company'" class="company-invoice-form">
          <div class="form-group mb-4">
            <div class="flex items-center justify-between mb-3">
              <label class="block text-gray-700 font-medium">选择抬头</label>
              <button
                @click="openNewHeaderModal('company')"
                class="flex items-center space-x-1 px-3 py-1.5 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors text-xs font-medium"
              >
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                <span>新建企业抬头</span>
              </button>
            </div>
            <div v-if="companyHeaders.length > 0">
              <div class="flex overflow-x-auto pb-4 space-x-4 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                <!-- 现有抬头卡片 -->
                <div v-for="header in companyHeaders" :key="header.id"
                     :class="['invoice-header-card border rounded-lg p-4 relative cursor-pointer transition-all flex-shrink-0 w-80',
                              selectedHeaderId === header.id ? 'border-red-600 bg-red-50' : 'border-gray-200 hover:border-red-300']"
                     @click="selectInvoiceHeader(header.id)">
                  <div class="flex justify-between items-start mb-3">
                    <div class="font-medium text-gray-900 truncate pr-2">{{ header.headerName }}</div>
                    <button
                      @click.stop="editHeader(header)"
                      class="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors flex-shrink-0"
                      title="编辑抬头"
                    >
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    </button>
                  </div>

                  <div class="text-sm text-gray-600 space-y-1 mb-3">
                    <div class="truncate"><span class="font-medium">税号：</span>{{ header.taxNumber }}</div>
                    <div v-if="header.companyAddress" class="truncate"><span class="font-medium">地址：</span>{{ header.companyAddress }}</div>
                    <div v-if="header.companyPhone" class="truncate"><span class="font-medium">电话：</span>{{ header.companyPhone }}</div>
                    <div v-if="header.bankName" class="truncate"><span class="font-medium">开户银行：</span>{{ header.bankName }}</div>
                    <div v-if="header.bankAccount" class="truncate"><span class="font-medium">银行账户：</span>{{ header.bankAccount }}</div>
                  </div>

                  <div class="flex items-center justify-between">
                    <div class="text-xs text-gray-500">企业发票</div>
                    <div v-if="header.isDefault" class="px-2 py-1 bg-red-100 text-red-600 text-xs rounded">默认抬头</div>
                  </div>

                  <!-- 选中状态指示器 -->
                  <div v-if="selectedHeaderId === header.id"
                       class="absolute top-2 right-12 w-5 h-5 bg-red-600 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                  </div>
                </div>

                <!-- 新建抬头卡片 -->
                <div class="add-invoice-header-card border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center cursor-pointer hover:border-red-600 transition-colors flex-shrink-0 w-80"
                     @click="openNewHeaderModal('company')">
                  <div class="text-center text-gray-500 hover:text-red-600">
                    <div class="flex justify-center mb-2">
                      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                      </svg>
                    </div>
                    <div class="font-medium">新建企业抬头</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="empty-headers text-center py-8">
              <p class="text-gray-500 mb-4">您还没有企业发票抬头</p>
              <button class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors"
                      @click="openNewHeaderModal('company')">
                添加企业抬头
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 订单备注 -->
      <div class="remark-section bg-white rounded-lg p-6 mb-4">
        <div class="section-title mb-4">订单备注</div>
        
        <textarea v-model="orderRemark" class="w-full border rounded-lg px-3 py-2 focus:outline-none focus:border-red-600" rows="2" placeholder="选填，请先和商家协商一致"></textarea>
      </div>
      
      <!-- 订单金额 -->
      <div class="order-amount bg-white rounded-lg p-6 mb-4">
        <div class="section-title mb-4">订单金额</div>
        
        <div class="amount-details space-y-2">
          <div class="flex justify-between">
            <div class="text-gray-600">商品金额：</div>
            <div>¥{{ itemsTotalPrice }}</div>
          </div>
          <div class="flex justify-between">
            <div class="text-gray-600">运费：</div>
            <div>¥{{ shippingFee.toFixed(2) }}</div>
          </div>
          <div class="flex justify-between">
            <div class="text-gray-600">优惠：</div>
            <div class="text-red-600">-¥{{ discount.toFixed(2) }}</div>
          </div>
          <div class="flex justify-between border-t pt-2 mt-2">
            <div class="text-gray-600">应付金额：</div>
            <div class="text-red-600 text-xl font-bold">¥{{ totalAmount.toFixed(2) }}</div>
          </div>
        </div>
      </div>
      
      <!-- 提交订单 -->
      <div class="submit-order-section flex justify-between items-center bg-white rounded-lg p-6">
        <div class="order-summary">
          <div class="text-gray-600">
            共 <span class="text-red-600 font-medium">{{ itemsCount }}</span> 件商品，
            应付金额：<span class="text-red-600 text-xl font-bold">¥{{ totalAmount.toFixed(2) }}</span>
          </div>
        </div>
        
        <button class="submit-order-btn bg-red-600 text-white px-8 py-3 rounded-full hover:bg-red-700 transition"
                @click="submitOrder"
                :disabled="!canSubmitOrder"
                :class="{'opacity-50 cursor-not-allowed': !canSubmitOrder}">
          提交订单
        </button>
      </div>
    </div>
  </div>

  <!-- 发票抬头弹窗 -->
  <div v-if="showHeaderModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="closeHeaderModal">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto" @click.stop>
      <!-- 弹窗头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ modalMode === 'create' ? '新建' : '编辑' }}{{ modalType === 'personal' ? '个人' : '企业' }}发票抬头
        </h3>
        <button @click="closeHeaderModal" class="text-gray-400 hover:text-gray-600 transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- 弹窗内容 -->
      <div class="p-6">
        <!-- 个人发票表单 -->
        <div v-if="modalType === 'personal'">
          <div class="form-group mb-4">
            <label class="block text-gray-700 font-medium mb-2">个人姓名 <span class="text-red-500">*</span></label>
            <input
              type="text"
              v-model="modalForm.headerName"
              class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
              placeholder="请输入个人姓名"
            >
          </div>
        </div>

        <!-- 企业发票表单 -->
        <div v-if="modalType === 'company'">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="form-group">
              <label class="block text-gray-700 font-medium mb-2">企业名称 <span class="text-red-500">*</span></label>
              <input
                type="text"
                v-model="modalForm.headerName"
                class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                placeholder="请输入企业名称"
              >
            </div>
            <div class="form-group">
              <label class="block text-gray-700 font-medium mb-2">纳税人识别号 <span class="text-red-500">*</span></label>
              <input
                type="text"
                v-model="modalForm.taxNumber"
                class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                placeholder="请输入纳税人识别号"
              >
            </div>
            <div class="form-group">
              <label class="block text-gray-700 font-medium mb-2">公司地址</label>
              <input
                type="text"
                v-model="modalForm.companyAddress"
                class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                placeholder="请输入公司地址（选填）"
              >
            </div>
            <div class="form-group">
              <label class="block text-gray-700 font-medium mb-2">公司电话</label>
              <input
                type="text"
                v-model="modalForm.companyPhone"
                class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                placeholder="请输入公司电话（选填）"
              >
            </div>
            <div class="form-group">
              <label class="block text-gray-700 font-medium mb-2">开户银行</label>
              <input
                type="text"
                v-model="modalForm.bankName"
                class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                placeholder="请输入开户银行（选填）"
              >
            </div>
            <div class="form-group">
              <label class="block text-gray-700 font-medium mb-2">银行账户</label>
              <input
                type="text"
                v-model="modalForm.bankAccount"
                class="w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
                placeholder="请输入银行账户（选填）"
              >
            </div>
          </div>
        </div>

        <!-- 默认抬头选项 -->
        <div class="flex items-center mt-6 pt-4 border-t border-gray-200">
          <input
            type="checkbox"
            id="modal-default"
            v-model="modalForm.isDefault"
            class="form-checkbox h-5 w-5 text-red-600 rounded focus:ring-red-500"
          >
          <label for="modal-default" class="ml-3 text-gray-700 font-medium">
            {{ modalMode === 'create' ? '保存为常用抬头' : '设为默认抬头' }}
          </label>
        </div>
      </div>

      <!-- 弹窗底部 -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
        <button
          @click="closeHeaderModal"
          class="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 font-medium transition-colors"
        >
          取消
        </button>
        <button
          @click="saveHeaderModal"
          class="px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 font-medium transition-colors"
          :disabled="!modalForm.headerName || (modalType === 'company' && !modalForm.taxNumber)"
          :class="{'opacity-50 cursor-not-allowed': !modalForm.headerName || (modalType === 'company' && !modalForm.taxNumber)}"
        >
          {{ modalMode === 'create' ? '保存' : '更新' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'mall'
});

import { ref, computed, onMounted, watch } from 'vue';
import { useRuntimeConfig } from '#app';
import { useRouter } from 'vue-router';
import { useMallCartStore } from '~/store/mall/cart';
import AddressFormModal from '~/components/mall/address/AddressFormModal.vue';
import { useMallUserStore } from '~/store/mall/user';
import { useMallImmediatePurchaseStore } from '~/store/mall/immediate-purchase';
import { Message } from '@arco-design/web-vue';
import addressApi from '~/api/mall/address';
import orderApi from '~/api/mall/order';
import masterOrderApi from '~/api/master/order';
import mallGoodsApi from '~/api/mall/goods';
import invoiceHeaderApi from '~/api/mall/invoiceHeader';
import { InvoiceTypeEnum } from '~/constants/InvoiceTypeEnum';
import { InvoiceHeaderTypeEnum } from '~/constants/InvoiceHeaderTypeEnum';

const router = useRouter();
const cartStore = useMallCartStore();
const userStore = useMallUserStore();
const immediatePurchaseStore = useMallImmediatePurchaseStore();
const config = useRuntimeConfig();

// 是否是立即购买模式
const isImmediatePurchase = computed(() => immediatePurchaseStore.isImmediatePurchase);

// 选中的商品 - 根据模式获取不同来源的商品
const selectedItems = computed(() => {
  // 如果是立即购买模式，返回立即购买的商品
  if (isImmediatePurchase.value) {
    return immediatePurchaseStore.getPurchaseItems();
  }
  // 否则返回购物车中选中的商品
  return cartStore.cartItems.filter(item => item.selected);
});

// 商品总价
const itemsTotalPrice = computed(() => {
  if (isImmediatePurchase.value) {
    return immediatePurchaseStore.totalPrice;
  }
  return cartStore.selectedTotalPrice;
});

// 商品总数
const itemsCount = computed(() => {
  if (isImmediatePurchase.value) {
    return immediatePurchaseStore.itemCount;
  }
  return cartStore.selectedCount;
});

// 收货地址
const addresses = ref([]);
const selectedAddressId = ref('');

// 地址表单
const showAddressForm = ref(false);
const editingAddress = ref(null); // 当前编辑的地址对象

// 省市区数据
const regionData = ref({
  provinces: [],
  cities: {},
  districts: {}
});

// 支付方式
const paymentMethods = ref([]);
const selectedPayment = ref('');

// 加载支付方式
const loadPaymentMethods = async () => {
  try {
    const response = await masterOrderApi.getPaymentMethods();
    console.log('获取支付方式响应:', response);
    
    if (response.code === 200 && Array.isArray(response.data)) {
      // 根据支付方式名称匹配图标
      const processedPaymentMethods = response.data.map(payment => {
        let icon = 'icon-other-pay'; // 默认图标
        
        // 根据名称匹配图标
        if (payment.name.includes('支付宝')) {
          icon = 'icon-alipay';
        } else if (payment.name.includes('微信')) {
          icon = 'icon-wechat';
        } else if (payment.name.includes('银联')) {
          icon = 'icon-unionpay';
        } else if (payment.name.includes('货到付款')) {
          icon = 'icon-daofu';
        }
        
        return {
          id: payment.id,
          name: payment.name,
          icon: icon,
          createdAt: payment.created_at,
          updatedAt: payment.updated_at
        };
      });
      
      paymentMethods.value = processedPaymentMethods;
      
      // 如果有支付方式，默认选中第一个
      if (paymentMethods.value.length > 0) {
        selectedPayment.value = paymentMethods.value[0].id;
      }
    } else {
      // 如果接口调用失败，使用默认支付方式
      paymentMethods.value = [
        { id: '2', name: '支付宝', icon: 'icon-alipay' },
        { id: '1', name: '微信支付', icon: 'icon-wechat' },
        { id: '3', name: '银联', icon: 'icon-unionpay' },
        { id: '4', name: '货到付款', icon: 'icon-daofu' },
        { id: '5', name: '其他', icon: 'icon-other-pay' }
      ];
      selectedPayment.value = '2'; // 默认选中支付宝
    }
  } catch (error) {
    console.error('加载支付方式失败:', error);
    // 出错时使用默认支付方式
    paymentMethods.value = [
      { id: '2', name: '支付宝', icon: 'icon-alipay' },
      { id: '1', name: '微信支付', icon: 'icon-wechat' },
      { id: '3', name: '银联', icon: 'icon-unionpay' },
      { id: '4', name: '货到付款', icon: 'icon-daofu' },
      { id: '5', name: '其他', icon: 'icon-other-pay' }
    ];
    selectedPayment.value = '2'; // 默认选中支付宝
  }
};

// 发票信息
const invoiceType = ref('none');
const invoiceHeaders = ref([]); // 用户的发票抬头列表
const selectedHeaderId = ref(''); // 选中的抬头ID
const newHeaderForm = ref({
  headerName: '',
  taxNumber: '',
  companyAddress: '',
  companyPhone: '',
  bankName: '',
  bankAccount: '',
  saveHeader: false
});

// 弹窗相关状态
const showHeaderModal = ref(false); // 是否显示弹窗
const modalMode = ref('create'); // 弹窗模式：create 或 edit
const modalType = ref('personal'); // 弹窗类型：personal 或 company
const modalForm = ref({
  id: '',
  headerName: '',
  taxNumber: '',
  companyAddress: '',
  companyPhone: '',
  bankName: '',
  bankAccount: '',
  isDefault: false
});

// 编辑发票抬头相关状态（保留用于兼容）
const editingHeader = ref(null); // 当前编辑的抬头对象
const editHeaderForm = ref({
  id: '',
  headerName: '',
  taxNumber: '',
  companyAddress: '',
  companyPhone: '',
  bankName: '',
  bankAccount: '',
  isDefault: false
});

// 计算属性：个人抬头列表
const personalHeaders = computed(() => {
  return invoiceHeaders.value.filter(header => header.headerType === 1);
});

// 计算属性：企业抬头列表
const companyHeaders = computed(() => {
  return invoiceHeaders.value.filter(header => header.headerType === 2);
});

// 订单备注
const orderRemark = ref('');

// 订单金额计算
const shippingFee = ref(0.00); // 运费
const discount = ref(0.00); // 优惠金额

// 存储商品运费信息
const freightInfos = ref([]);

// 总金额
const totalAmount = computed(() => {
  return parseFloat(itemsTotalPrice.value) + shippingFee.value - discount.value;
});

// 是否可以提交订单
const canSubmitOrder = computed(() => {
  return selectedItems.value.length > 0 && selectedAddressId.value;
});

// 选择收货地址
const selectAddress = (addressId) => {
  selectedAddressId.value = addressId;
};

// 选择发票抬头
const selectInvoiceHeader = (headerId) => {
  selectedHeaderId.value = headerId;
};

// 选择支付方式
const selectPayment = (paymentId) => {
  selectedPayment.value = paymentId;
};

// 处理地址表单提交
const handleAddressSubmit = async (formData) => {
  try {
    // 清除之前的提示信息
    // successMessage.value = '';
    // errorMessage.value = '';
    
    // 构建请求数据
    const addressData = {
      name: formData.name,
      phone: formData.phone,
      province: formData.province,
      city: formData.city,
      district: formData.district,
      address: formData.address, // 注意这里的字段名是 address
      postcode: formData.postcode || '',
      is_default: formData.isDefault, // 后端使用 is_default
      tag: formData.tag || ''
    };
    
    console.log('发送地址数据:', addressData);
    
    let response;
    if (editingAddress.value?.id) {
      // 更新地址
      response = await addressApi.address.update(editingAddress.value.id, addressData);
      console.log('更新地址响应:', response);
    } else {
      // 创建地址
      response = await addressApi.address.create(addressData);
      console.log('创建地址响应:', response);
    }
    
    if (response.code === 201 || response.code === 200) {
      // 操作成功，使用返回的地址数据
      const newAddress = response.data;
      
      // 重新加载地址列表
      const addressResponse = await userStore.getUserAddresses();
      if (addressResponse.success && Array.isArray(addressResponse.data)) {
        addresses.value = addressResponse.data;
        
        // 选中新创建/更新的地址
        selectedAddressId.value = newAddress.id;
      }
      
      // 关闭表单
      showAddressForm.value = false;
      
      // 重置编辑状态
      editingAddress.value = null;
      
      Message.success(editingAddress.value ? '地址更新成功' : '地址添加成功');
    } else {
      Message.error(response.message || (editingAddress.value ? '更新地址失败' : '添加地址失败'));
    }
  } catch (error) {
    console.error('保存地址失败:', error);
    Message.error('保存地址失败，请稍后再试');
  }
};

// 提交订单
const submitOrder = async () => {
  try {
    // 清除之前的提示信息
    // successMessage.value = '';
    // errorMessage.value = '';
    
    if (!canSubmitOrder.value) {
      if (!selectedAddressId.value) {
        Message.error('请选择收货地址');
      }
      return;
    }
    
    // 获取选中的地址
    const address = addresses.value.find(addr => addr.id === selectedAddressId.value);
    if (!address) {
      Message.error('收货地址信息不完整');
      return;
    }
    
    // 处理发票信息
    let invoiceData = null;
    if (invoiceType.value !== 'none') {
      // 验证发票信息
      if (invoiceType.value === 'personal' || invoiceType.value === 'company') {
        if (!selectedHeaderId.value) {
          Message.error('请选择发票抬头');
          return;
        }
      }

      // 获取选中的发票抬头信息
      const selectedHeader = invoiceHeaders.value.find(header => header.id === selectedHeaderId.value);
      if (!selectedHeader) {
        Message.error('选中的发票抬头不存在');
        return;
      }

      invoiceData = {
        invoiceType: selectedHeader.headerType, // 使用抬头的实际类型
        headerId: selectedHeaderId.value,
        invoiceAmount: totalAmount.value,
        remark: null
      };
    }

    console.log('选中商品:', selectedItems.value);
    console.log('发票信息:', invoiceData);

    // 准备订单商品项
    const orderItems = selectedItems.value.map(item => ({
      goodsSpuId: item.spuId || item.id || '0', // 确保不为空
      goodsSkuId: item.skuId || item.id || '0', // 确保不为空
      productName: item.name || '商品',
      skuSpecifications: item.specifications || '',
      productImage: item.image || '/uploads/products/default.jpg',
      unitPrice: item.price,
      quantity: item.quantity,
      spuCodeSnapshot: item.spuCode || '',
      spuNameSnapshot: item.name || '商品',
      skuCode: item.skuCode || '',
      thirdPartySpuId: item.thirdPartySpuId || '',
      thirdPartySkuId: item.thirdPartySkuId || '',
      thirdPartyProductCode: item.thirdPartyProductCode || '',
      thirdPartyItemSnapshot: item.thirdPartyItemSnapshot || {
        externalId: '',
        externalData: {}
      }
    }));
    
    // 构建符合接口要求的订单数据
    const orderData = {
      customerName: address.name,
      customerPhone: address.phone,
      customerId: userStore.user?.id || '0',
      // 根据错误提示，订单类型和来源必须是数字
      // orderType: 1 - 商城订单，2 - 系统订单
      // orderSource: 2 - 商城下单
      // 这里不设置，由 API 层统一设置
      channelId: config.public.MALL_CHANNEL_ID || '185653478876647424', // 添加渠道ID
      discountAmount: discount.value,
      couponAmount: 0,
      shippingFee: shippingFee.value,
      remark: orderRemark.value,
      invoice: invoiceData, // 添加发票信息
      items: orderItems,
      shipping: {
        recipientName: address.name,
        recipientPhone: address.phone,
        regionProvinceId: address.province_id || 0, // 从地址中获取省份 ID
        regionCityId: address.city_id || 0, // 从地址中获取城市 ID
        regionDistrictId: address.district_id || 0, // 从地址中获取区县 ID
        regionPathName: `${address.province}/${address.city}/${address.district}`, // 不包含详细地址
        streetAddress: address.address || address.detail,
        postalCode: address.postcode || '100000'
      }
    };
    
    console.log('准备提交订单数据:', orderData);
    
    // 调用创建订单接口
    const response = await orderApi.createOrder(orderData);
    console.log('创建订单响应:', response);
    
    if (response.code === 201 || response.code === 200) {
      // 订单创建成功
      Message.success('订单提交成功！');
      
      // 根据购买模式处理购买后的操作
      if (isImmediatePurchase.value) {
        // 立即购买模式：清除立即购买的商品
        immediatePurchaseStore.clearPurchaseItem();
      } else {
        // 购物车模式：从购物车中移除已购买的商品
        for (const item of selectedItems.value) {
          await cartStore.removeFromCart(item.itemKey);
        }
      }
      
      // 跳转到订单详情页
      setTimeout(() => {
        router.push(`/mall/user/order-details/${response.data.id}`);
      }, 1500); // 等待 1.5 秒后跳转，让用户看到成功提示
    } else {
      Message.error(response.message || '创建订单失败');
    }
    
  } catch (error) {
    console.error('提交订单失败:', error);
    Message.error('提交订单失败，请稍后再试');
  }
};

// 获取省市区数据
const loadRegionData = async () => {
  try {
    console.log('开始加载省市区数据');
    const response = await addressApi.address.getRegionData();
    console.log('获取省市区数据响应:', response);
    
    if (response.code === 200 && Array.isArray(response.data)) {
      // 提取省份列表
      const provinces = response.data;
      
      // 构建城市和区县的映射关系
      const cities = {};
      const districts = {};
      
      // 遍历省份数据，构建城市映射
      provinces.forEach(province => {
        if (Array.isArray(province.children)) {
          cities[province.name] = province.children;
          
          // 遍历城市数据，构建区县映射
          province.children.forEach(city => {
            if (Array.isArray(city.children)) {
              districts[city.code] = city.children;
            }
          });
        }
      });
      
      // 更新地区数据
      regionData.value = {
        provinces,
        cities,
        districts
      };
      
      console.log('省市区数据加载成功:', regionData.value);
    } else {
      console.error('省市区数据格式不正确:', response);
      regionData.value = {
        provinces: [],
        cities: {},
        districts: {}
      };
    }
  } catch (error) {
    console.error('加载省市区数据失败:', error);
    regionData.value = {
      provinces: [],
      cities: {},
      districts: {}
    };
  }
};

// 加载用户的发票抬头列表
const loadInvoiceHeaders = async () => {
  try {
    if (!userStore.user?.id) {
      console.log('用户未登录，跳过加载发票抬头');
      return;
    }

    const response = await invoiceHeaderApi.getUserHeaders();
    console.log('获取发票抬头响应:', response);

    if (response.code === 200 && response.data) {
      invoiceHeaders.value = response.data;
      console.log('发票抬头列表已更新:', invoiceHeaders.value);

      // 如果有默认抬头，自动选中
      const defaultHeader = invoiceHeaders.value.find(header => header.isDefault);
      if (defaultHeader) {
        selectedHeaderId.value = defaultHeader.id;
        // 根据抬头类型设置发票类型
        if (defaultHeader.headerType === 1) {
          invoiceType.value = 'personal';
        } else if (defaultHeader.headerType === 2) {
          invoiceType.value = 'company';
        }
      }
    } else {
      console.error('获取发票抬头失败:', response.message);
      invoiceHeaders.value = [];
    }
  } catch (error) {
    console.error('加载发票抬头失败:', error);
  }
};

// 保存新建的发票抬头
const saveNewHeader = async () => {
  try {
    if (!newHeaderForm.value.headerName) {
      Message.error('请输入抬头名称');
      return false;
    }

    if (invoiceType.value === 'company' && !newHeaderForm.value.taxNumber) {
      Message.error('请输入纳税人识别号');
      return false;
    }

    const headerData = {
      headerType: invoiceType.value === 'personal' ? 1 : 2,
      headerName: newHeaderForm.value.headerName,
      taxNumber: newHeaderForm.value.taxNumber || null,
      companyAddress: newHeaderForm.value.companyAddress || null,
      companyPhone: newHeaderForm.value.companyPhone || null,
      bankName: newHeaderForm.value.bankName || null,
      bankAccount: newHeaderForm.value.bankAccount || null,
      isDefault: newHeaderForm.value.saveHeader,
      createdBy: userStore.user?.id // 添加创建人ID
    };

    const response = await invoiceHeaderApi.createHeader(headerData);
    if (response.code === 200 && response.data) {
      // 重新加载抬头列表
      await loadInvoiceHeaders();

      // 选中新创建的抬头
      selectedHeaderId.value = response.data.id;

      // 重置表单
      newHeaderForm.value = {
        headerName: '',
        taxNumber: '',
        companyAddress: '',
        companyPhone: '',
        bankName: '',
        bankAccount: '',
        saveHeader: false
      };

      Message.success('发票抬头保存成功');
      return true;
    } else {
      Message.error(response.message || '保存发票抬头失败');
      return false;
    }
  } catch (error) {
    console.error('保存发票抬头失败:', error);
    Message.error('保存发票抬头失败');
    return false;
  }
};

// 手动保存发票抬头
const saveNewHeaderManually = async () => {
  try {
    if (!userStore.user?.id) {
      Message.error('请先登录');
      return;
    }

    if (!newHeaderForm.value.headerName) {
      Message.error('请输入抬头名称');
      return;
    }

    if (invoiceType.value === 'company' && !newHeaderForm.value.taxNumber) {
      Message.error('请输入纳税人识别号');
      return;
    }

    const headerData = {
      headerType: invoiceType.value === 'personal' ? 1 : 2,
      headerName: newHeaderForm.value.headerName,
      taxNumber: newHeaderForm.value.taxNumber || null,
      companyAddress: newHeaderForm.value.companyAddress || null,
      companyPhone: newHeaderForm.value.companyPhone || null,
      bankName: newHeaderForm.value.bankName || null,
      bankAccount: newHeaderForm.value.bankAccount || null,
      isDefault: newHeaderForm.value.saveHeader,
      createdBy: userStore.user?.id
    };

    const response = await invoiceHeaderApi.createHeader(headerData);
    console.log('保存发票抬头响应:', response);

    if (response.code === 200 && response.data) {
      // 重新加载抬头列表
      await loadInvoiceHeaders();

      // 选中新创建的抬头
      selectedHeaderId.value = response.data.id;

      // 重置表单
      newHeaderForm.value = {
        headerName: '',
        taxNumber: '',
        companyAddress: '',
        companyPhone: '',
        bankName: '',
        bankAccount: '',
        saveHeader: false
      };

      Message.success('发票抬头保存成功');
    } else {
      Message.error(response.message || '保存发票抬头失败');
    }
  } catch (error) {
    console.error('保存发票抬头失败:', error);
    Message.error('保存发票抬头失败');
  }
};

// 打开新建抬头弹窗
const openNewHeaderModal = (type) => {
  modalMode.value = 'create';
  modalType.value = type;
  modalForm.value = {
    id: '',
    headerName: '',
    taxNumber: '',
    companyAddress: '',
    companyPhone: '',
    bankName: '',
    bankAccount: '',
    isDefault: false
  };
  showHeaderModal.value = true;
};

// 打开编辑抬头弹窗
const openEditHeaderModal = (header) => {
  modalMode.value = 'edit';
  modalType.value = header.headerType === InvoiceHeaderTypeEnum.PERSONAL ? 'personal' : 'company';
  modalForm.value = {
    id: header.id,
    headerName: header.headerName,
    taxNumber: header.taxNumber || '',
    companyAddress: header.companyAddress || '',
    companyPhone: header.companyPhone || '',
    bankName: header.bankName || '',
    bankAccount: header.bankAccount || '',
    isDefault: header.isDefault
  };
  showHeaderModal.value = true;
};

// 关闭弹窗
const closeHeaderModal = () => {
  showHeaderModal.value = false;
  modalForm.value = {
    id: '',
    headerName: '',
    taxNumber: '',
    companyAddress: '',
    companyPhone: '',
    bankName: '',
    bankAccount: '',
    isDefault: false
  };
};

// 保存弹窗表单
const saveHeaderModal = async () => {
  try {
    if (!modalForm.value.headerName) {
      Message.error('请输入抬头名称');
      return;
    }

    if (modalType.value === 'company' && !modalForm.value.taxNumber) {
      Message.error('请输入纳税人识别号');
      return;
    }

    const headerData = {
      headerType: modalType.value === 'personal' ? InvoiceHeaderTypeEnum.PERSONAL : InvoiceHeaderTypeEnum.COMPANY,
      headerName: modalForm.value.headerName,
      taxNumber: modalForm.value.taxNumber || null,
      companyAddress: modalForm.value.companyAddress || null,
      companyPhone: modalForm.value.companyPhone || null,
      bankName: modalForm.value.bankName || null,
      bankAccount: modalForm.value.bankAccount || null,
      isDefault: modalForm.value.isDefault
    };

    let response;
    if (modalMode.value === 'create') {
      headerData.createdBy = userStore.user?.id;
      response = await invoiceHeaderApi.createHeader(headerData);
    } else {
      response = await invoiceHeaderApi.updateHeader(modalForm.value.id, headerData);
    }

    console.log('保存发票抬头响应:', response);

    if (response.code === 200 && response.data) {
      // 保存当前的发票类型，避免被loadInvoiceHeaders的默认抬头逻辑覆盖
      const currentInvoiceType = modalType.value === 'personal' ? 'personal' : 'company';

      // 重新加载抬头列表
      await loadInvoiceHeaders();

      // 恢复当前的发票类型（如果是新建的话）
      if (modalMode.value === 'create') {
        invoiceType.value = currentInvoiceType;
      }

      // 选中新创建或更新的抬头
      selectedHeaderId.value = response.data.id;

      // 关闭弹窗
      closeHeaderModal();

      Message.success(modalMode.value === 'create' ? '发票抬头保存成功' : '发票抬头更新成功');
    } else {
      Message.error(response.message || '操作失败');
    }
  } catch (error) {
    console.error('保存发票抬头失败:', error);
    Message.error('操作失败');
  }
};

// 编辑发票抬头（修改为调用弹窗）
const editHeader = (header) => {
  openEditHeaderModal(header);
  editingHeader.value = header;
  editHeaderForm.value = {
    id: header.id,
    headerName: header.headerName,
    taxNumber: header.taxNumber || '',
    companyAddress: header.companyAddress || '',
    companyPhone: header.companyPhone || '',
    bankName: header.bankName || '',
    bankAccount: header.bankAccount || '',
    isDefault: header.isDefault
  };

  // 切换到对应的发票类型
  invoiceType.value = header.headerType === 1 ? 'personal' : 'company';

  // 选中编辑模式
  selectedHeaderId.value = 'edit';
};

// 保存编辑的发票抬头
const saveEditedHeader = async () => {
  try {
    if (!editHeaderForm.value.headerName) {
      Message.error('请输入抬头名称');
      return;
    }

    if (editingHeader.value.headerType === 2 && !editHeaderForm.value.taxNumber) {
      Message.error('请输入纳税人识别号');
      return;
    }

    const headerData = {
      headerType: editingHeader.value.headerType,
      headerName: editHeaderForm.value.headerName,
      taxNumber: editHeaderForm.value.taxNumber || null,
      companyAddress: editHeaderForm.value.companyAddress || null,
      companyPhone: editHeaderForm.value.companyPhone || null,
      bankName: editHeaderForm.value.bankName || null,
      bankAccount: editHeaderForm.value.bankAccount || null,
      isDefault: editHeaderForm.value.isDefault
    };

    const response = await invoiceHeaderApi.updateHeader(editHeaderForm.value.id, headerData);
    console.log('更新发票抬头响应:', response);

    if (response.code === 200 && response.data) {
      // 重新加载抬头列表
      await loadInvoiceHeaders();

      // 选中更新后的抬头
      selectedHeaderId.value = editHeaderForm.value.id;

      // 重置编辑状态
      editingHeader.value = null;
      editHeaderForm.value = {
        id: '',
        headerName: '',
        taxNumber: '',
        companyAddress: '',
        companyPhone: '',
        bankName: '',
        bankAccount: '',
        isDefault: false
      };

      Message.success('发票抬头更新成功');
    } else {
      Message.error(response.message || '更新发票抬头失败');
    }
  } catch (error) {
    console.error('更新发票抬头失败:', error);
    Message.error('更新发票抬头失败');
  }
};

// 取消编辑
const cancelEdit = () => {
  editingHeader.value = null;
  editHeaderForm.value = {
    id: '',
    headerName: '',
    taxNumber: '',
    companyAddress: '',
    companyPhone: '',
    bankName: '',
    bankAccount: '',
    isDefault: false
  };
  selectedHeaderId.value = '';
};

// 监听发票类型变化，重置选中的抬头
watch(invoiceType, (newType) => {
  selectedHeaderId.value = '';
  newHeaderForm.value = {
    headerName: '',
    taxNumber: '',
    companyAddress: '',
    companyPhone: '',
    bankName: '',
    bankAccount: '',
    saveHeader: false
  };
});

// 获取省市区代码
const getLocationCodes = () => {
  // 获取当前选择的地址中的省份代码
  const selectedAddress = addresses.value.find(addr => addr.id === selectedAddressId.value);
  if (!selectedAddress) {
    console.warn('未选择收货地址，无法计算运费');
    return { provinceCode: '440000', cityCode: '440100' }; // 默认使用广东省广州市
  }
  
  // 获取省份代码
  const selectedProvince = regionData.value.provinces.find(province => province.name === selectedAddress.province);
  const provinceCode = selectedProvince?.code || '440000'; // 默认使用广东省
  
  // 获取城市代码
  let cityCode = '440100'; // 默认使用广州市
  if (selectedProvince && selectedAddress.city) {
    const cities = regionData.value.cities[selectedAddress.province] || [];
    const selectedCity = cities.find(city => city.name === selectedAddress.city);
    if (selectedCity) {
      cityCode = selectedCity.code;
    }
  }
  
  return { provinceCode, cityCode };
};

// 批量计算所有商品运费
const calculateAllFreight = async () => {
  if (!selectedItems.value || selectedItems.value.length === 0) return;
  
  try {
    // 重置运费
    shippingFee.value = 0;
    freightInfos.value = [];
    
    // 获取地址的省市代码
    const { provinceCode, cityCode } = getLocationCodes();
    
    // 准备批量运费计算的商品数据
    const items = selectedItems.value.map(item => ({
      spuId: item.spuId,
      skuId: item.skuId,
      quantity: item.quantity
    }));
    
    // 构建批量运费计算参数
    const batchFreightParams = { items, provinceCode, cityCode };
    console.log('批量计算运费参数:', batchFreightParams);
    
    // 调用批量运费计算API
    const response = await mallGoodsApi.calculateBatchProductsFreight(batchFreightParams);
    console.log('批量运费计算结果:', response);
    
    if (response.code === 200 && response.data) {
      const { totalFreight, items: freightItems } = response.data;
      
      // 设置总运费
      shippingFee.value = parseFloat(totalFreight || 0);
      
      // 将每个商品的运费信息赋值给对应商品
      if (Array.isArray(freightItems)) {
        for (const freightItem of freightItems) {
          const targetItem = selectedItems.value.find(
            item => item.spuId === freightItem.spuId && item.skuId === freightItem.skuId
          );
          
          if (targetItem) {
            targetItem.freightInfo = {
              freight: freightItem.freight,
              isFreeShipping: freightItem.isFreeShipping,
              noFreightTemplate: freightItem.noFreightTemplate
            };
            freightInfos.value.push(targetItem.freightInfo);
          }
        }
      }
      
      console.log('所有商品运费计算完成，总运费:', shippingFee.value);
    } else {
      console.warn('批量计算运费失败:', response.message);
      // 设置默认运费信息
      selectedItems.value.forEach(item => {
        item.freightInfo = {
          freight: 0,
          isFreeShipping: false,
          noFreightTemplate: true
        };
      });
    }
  } catch (error) {
    console.error('批量计算运费出错:', error);
    // 设置默认运费信息
    selectedItems.value.forEach(item => {
      item.freightInfo = {
        freight: 0,
        isFreeShipping: false,
        noFreightTemplate: true
      };
    });
  }
};

// 监听地址变化，重新计算运费
watch(selectedAddressId, async (newAddressId) => {
  if (newAddressId) {
    await calculateAllFreight();
  }
});

// 页面加载时初始化数据
onMounted(async () => {
  // 检查是否有商品可结算
  if (!isImmediatePurchase.value && cartStore.selectedCount === 0) {
    Message.error('请先在购物车中选择要结算的商品');
    router.push('/mall/cart');
    return;
  }
  
  // 检查用户是否登录
  if (!userStore.loggedIn) {
    console.log('用户未登录，尝试从本地恢复会话...');
    
    // 尝试从本地恢复用户会话
    const restoreResult = await userStore.restoreSession();
    
    // 如果本地恢复失败，则跳转到登录页面
    if (!restoreResult.success) {
      console.log('本地恢复失败，需要重新登录');
      Message.error('请先登录再进行结算');
      router.push('/mall/login?redirect=/mall/cart');
      return;
    }
    
    console.log('本地恢复成功，继续结算流程');
  }
  
  // 加载省市区数据
  await loadRegionData();

  // 加载支付方式
  await loadPaymentMethods();

  // 加载发票抬头
  await loadInvoiceHeaders();

  // 加载用户地址
  try {
    const response = await userStore.getUserAddresses();
    console.log('获取地址列表响应:', response);
    
    if (response.success && Array.isArray(response.data)) {
      addresses.value = response.data;
      
      // 如果有默认地址，则自动选中
      const defaultAddress = addresses.value.find(addr => addr.is_default);
      if (defaultAddress) {
        selectedAddressId.value = defaultAddress.id;
      } else if (addresses.value.length > 0) {
        selectedAddressId.value = addresses.value[0].id;
      }
      
      // 计算商品运费
      await calculateAllFreight();
    } else {
      console.error('地址数据格式不正确:', response);
      addresses.value = [];
    }
  } catch (error) {
    console.error('加载地址失败:', error);
    addresses.value = [];
  }
});
</script>

<style scoped>
.section-title {
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 25%;
  height: 50%;
  width: 4px;
  background-color: #e4393c;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.address-card, .payment-option, .invoice-header-card {
  transition: all 0.3s;
}

.address-card:hover, .payment-option:hover, .invoice-header-card:hover {
  border-color: #e4393c;
}

.add-address-card, .add-invoice-header-card {
  transition: all 0.3s;
}

.add-address-card:hover, .add-invoice-header-card:hover {
  border-color: #e4393c;
  color: #e4393c;
}

.invoice-header-card {
  min-height: 120px;
}

.invoice-header-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 自定义滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 6px;
}

.scrollbar-track-gray-100::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 6px;
}

.scrollbar-thin::-webkit-scrollbar {
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: #f3f4f6;
  border-radius: 6px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}
</style>
