<template>
  <div class="container mx-auto px-4 py-4">
    <!-- 移动端菜单等部分 ... (无修改) ... -->
    <div class="md:hidden flex justify-between items-center mb-4">
      <button @click="mobileMenuOpen = !mobileMenuOpen" class="bg-white p-2 rounded-md shadow-sm">
        <span class="block w-6 h-0.5 bg-gray-600 mb-1"></span>
        <span class="block w-6 h-0.5 bg-gray-600 mb-1"></span>
        <span class="block w-6 h-0.5 bg-gray-600"></span>
      </button>
      <div class="text-lg font-bold">企业商城</div>
      <NuxtLink to="/mall/cart" class="relative">
        <span class="material-icons text-gray-600">shopping_cart</span>
        <span v-if="cartStore.cartItemCount > 0" class="absolute -top-1 -right-1 bg-red-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
          {{ cartStore.cartItemCount }}
        </span>
      </NuxtLink>
    </div>

    <div v-if="mobileMenuOpen" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-50" @click="mobileMenuOpen = false">
      <div class="bg-white w-3/4 h-full overflow-y-auto" @click.stop>
        <div class="p-4 border-b flex justify-between items-center">
          <div class="font-bold">商品分类</div>
          <button @click="mobileMenuOpen = false" class="text-gray-500">×</button>
        </div>
        <div class="p-2">
          <div v-for="(category, index) in categories" :key="index" class="mb-2">
            <div class="flex justify-between items-center p-2 bg-gray-50 rounded"
                 @click="toggleMobileCategory(index)">
              <NuxtLink :to="`/mall/category/${category.id}`" class="flex-1">
                {{ category.name }}
              </NuxtLink>
              <span class="material-icons text-sm" v-if="category.children && category.children.length">
                {{ mobileCategoryOpen === index ? 'expand_less' : 'expand_more' }}
              </span>
            </div>
            <div v-if="mobileCategoryOpen === index && category.children && category.children.length" class="pl-4 mt-1">
              <div v-for="(subCategory, subIdx) in category.children" :key="subIdx" class="p-2">
                <NuxtLink :to="`/mall/category/${subCategory.id}`" class="block text-sm">
                  {{ subCategory.name }}
                </NuxtLink> <!-- <--- 这里是修正点 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex flex-col md:flex-row bg-gray-50 rounded-lg  mb-4">
      <!-- 左侧分类菜单 (无修改) -->
      <div class="overflow-visible md:block w-44 lg:w-48 xl:w-52 category-menu bg-white relative p-4" style="z-index: 100;">
        <!-- ... 分类菜单内部代码无修改 ... -->
        <div v-for="(category, index) in categories" :key="index" 
             class="relative "
             @mouseenter="activeCategory = index"
             @mouseleave="activeCategory = null">
          <NuxtLink :to="`/mall/category/${category.id}`"
             class="category-item cursor-pointer hover:bg-gray-50 block " style="padding: 6px 6px 6px 16px;">
            <div class="category-name text-sm font-medium">{{ category.name }}</div>
            <div class="category-subtext text-gray-400 text-xs">{{ category.description }}</div>
          </NuxtLink>
          <div v-if="activeCategory === index" 
               class="subcategory-panel absolute bg-white shadow-md rounded p-4 z-50"
                style="width: 280px; min-height: 30vh; left: 100%; top: 0; max-width: calc(100vw - 100%);"
               @mouseenter="activeCategory = index"
               @mouseleave="activeCategory = null">
             <div class="font-medium mb-3">{{ category.name }} - 全部分类</div>
            <div v-if="category.children && category.children.length > 0" class="grid grid-cols-3 gap-2">
              <div v-for="(subCategory, subIdx) in category.children" :key="subIdx" 
                   class="mb-3 relative category-level-2"
                   @mouseenter="activeSecondCategory = {parentIndex: index, index: subIdx}"
                   @mouseleave="activeSecondCategory = null">
                <NuxtLink 
                  :to="`/mall/category/${subCategory.id}`"
                  class="text-sm font-medium text-gray-800 hover:text-red-600 block mb-1">
                  {{ subCategory.name }}
                </NuxtLink>
                <div v-if="activeSecondCategory && 
                          activeSecondCategory.parentIndex === index && 
                          activeSecondCategory.index === subIdx && 
                          subCategory.children && 
                          subCategory.children.length > 0" 
                     class="third-category-panel absolute bg-white shadow-md rounded p-3 z-50"
                     style="width: 220px; min-height: 25vh; max-height: 30vh; left: 100%; top: -0.5rem; max-width: calc(100vw - 100%);">
                  <div class="text-sm font-medium mb-2">{{ subCategory.name }} - 子分类</div>
                  <div class="pl-1">
                    <div v-for="(thirdCategory, thirdIdx) in subCategory.children" :key="thirdIdx"
                         class="relative category-level-3 mb-2"
                         @mouseenter="activeThirdCategory = {parentIndex: index, parentSubIndex: subIdx, index: thirdIdx}"
                         @mouseleave="activeThirdCategory = null">
                      <NuxtLink 
                        :to="`/mall/category/${thirdCategory.id}`"
                        class="text-xs text-gray-600 hover:text-red-600 block">
                        {{ thirdCategory.name }}
                      </NuxtLink>
                      <div v-if="activeThirdCategory && 
                                activeThirdCategory.parentIndex === index && 
                                activeThirdCategory.parentSubIndex === subIdx && 
                                activeThirdCategory.index === thirdIdx && 
                                thirdCategory.children && 
                                thirdCategory.children.length > 0" 
                           class="fourth-category-panel absolute bg-white shadow-md rounded p-3 z-50"
                           style="width: 180px; min-height: 20vh; max-height: 30vh; left: 100%; top: -0.5rem; max-width: calc(100vw - 100%);">
                        <div class="text-xs font-medium mb-2">{{ thirdCategory.name }} - 子分类</div>
                        <div class="pl-1">
                          <NuxtLink 
                            v-for="(fourthCategory, fourthIdx) in thirdCategory.children" 
                            :key="fourthIdx"
                            :to="`/mall/category/${fourthCategory.id}`"
                            class="text-xs text-gray-600 hover:text-red-600 block mb-1">
                            {{ fourthCategory.name }}
                          </NuxtLink>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center text-gray-500 py-4">
              暂无子分类
            </div>
          </div>
        </div>
      </div>

      <!-- Banner区域 -->
      <div class="flex-1 p-4 bg-white">
        <div class="banner-container rounded-lg overflow-hidden flex flex-col md:flex-row md:gap-4">
          
          <div class="banner-left hidden xl:flex xl:w-1/5 rounded-lg relative">
            <a-image :src="activeImageUrl" class="w-full h-full object-cover rounded-lg" alt="活动图" />
          </div>
          
          <div class="banner-center flex flex-1 bg-white rounded-lg overflow-hidden relative">
            <!-- 轮播图加载状态 -->
            <div v-if="loading" class="w-full h-full flex items-center justify-center bg-gray-100">
              <div class="text-gray-500">轮播图加载中...</div>
            </div>
            <!-- 轮播图为空状态 -->
            <div v-else-if="bannerSlides.length === 0" class="w-full h-full flex items-center justify-center bg-gray-100">
              <div class="text-gray-500">暂无轮播图</div>
            </div>
            <!-- 轮播图内容 -->
            <div v-else class="carousel-container relative w-full h-full">
              <div class="carousel-slides w-full h-full flex transition-transform duration-500 ease-in-out"
                   :style="{ transform: `translateX(-${activeSlide * 100}%)` }">
                <div v-for="(slide, index) in bannerSlides" :key="index" class="carousel-slide w-full h-full flex-shrink-0">
                  <div v-if="slide.linkUrl && slide.linkType !== 3"
                       @click="handleBannerClick(slide)"
                       class="w-full h-full cursor-pointer">
                    <a-image :src="slide.image" class="w-full h-full object-cover" :alt="slide.title" />
                  </div>
                  <div v-else class="w-full h-full">
                    <a-image :src="slide.image" class="w-full h-full object-cover" :alt="slide.title" />
                  </div>
                </div>
              </div>
              <div v-if="bannerSlides.length > 1" class="carousel-indicators absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                <button v-for="(slide, index) in bannerSlides" :key="index"
                        @click="activeSlide = index"
                        class="w-2 h-2 rounded-full transition-all duration-300"
                        :class="index === activeSlide ? 'bg-red-600 w-4' : 'bg-gray-300'"></button>
              </div>
              <button v-if="bannerSlides.length > 1" @click="prevSlide" class="absolute left-2 top-1/2 -translate-y-1/2 bg-white/50 hover:bg-white/80 rounded-full p-2 text-gray-800">
                <span class="block transform rotate-180">➔</span>
              </button>
              <button v-if="bannerSlides.length > 1" @click="nextSlide" class="absolute right-2 top-1/2 -translate-y-1/2 bg-white/50 hover:bg-white/80 rounded-full p-2 text-gray-800">
                <span class="block">➔</span>
              </button>
            </div>
          </div>
          
          <div class="banner-right hidden md:flex flex-col md:w-1/4 xl:w-1/5 bg-blue-100 rounded-lg p-4">
            <div class="bg-white rounded p-3 mb-3">
              <div v-if="userStore.loggedIn" class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-gray-200 flex-shrink-0 mr-2 overflow-hidden">
                  <a-image :src="userStore.user.avatar" class="w-full h-full object-cover" alt="用户头像" />
                </div>
                <div class="flex-1">
                  <div class="font-medium text-sm">{{ userStore.user.nickname }}</div>
                  <div class="text-xs text-gray-500">会员等级: {{ userStore.userLevel }}</div>
                </div>
              </div>
              <div v-else class="text-center">
                <div class="text-sm mb-2">您还未登录</div>
                <div class="flex justify-center gap-2">
                  <NuxtLink to="/mall/login" class="bg-red-600 text-white text-xs px-3 py-1 rounded">登录</NuxtLink>
                  <NuxtLink to="/mall/register" class="border border-red-600 text-red-600 text-xs px-3 py-1 rounded">注册</NuxtLink>
                </div>
              </div>
            </div>
            <div class="grid grid-cols-2 gap-2 mb-3">
              <div v-for="(item, index) in quickLinks" :key="index" class="bg-white p-2 rounded text-center text-xs">
                {{ item }}
              </div>
            </div>
            <div class="bg-white rounded p-2 mb-2">
              <div class="text-center font-bold text-sm mb-1">新人专享</div>
              <div class="text-center text-red-600 text-xs">首单享优惠</div>
            </div>
            <div class="bg-white rounded p-2">
              <div class="text-center font-bold text-sm mb-1">企业阅读</div>
              <div class="text-center text-red-600 text-xs">专属活动折扣</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 页面其余部分 ... (无修改) ... -->
    <ProductList
      title="企采精选"
      :rightSwitch="true"
      :imageWidth="0"
      :imageHeight="0"
      :columnsPerRow="2"
      :columnsPerRowMd="2"
      :columnsPerRowLg="4"
      :count="4"
      :enableResponsiveCount="false"
      :autoLoad="true"
      :gap="2"
      class="product-container"
    />
    <ProductList
      title="节日礼篮"
      :rightSwitch="true"
      :imageWidth="0"
      :imageHeight="0"
      :columnsPerRow="2"
      :columnsPerRowMd="3"
      :columnsPerRowLg="6"
      :countLg="6"
      :countMd="6"
      :countsm="4"
      :enableResponsiveCount="true"
      :autoLoad="true"
      :gap="2"
      class="product-container"
    />
    <div class="bg-white rounded-lg p-4 mb-4">
      <div class="flex justify-between items-center mb-4">
        <div class="section-title">热销品牌</div>
      </div>
      <div class="brand-scroll-container relative">
        <div class="brand-scroll-wrapper overflow-x-auto hide-scrollbar">
          <div class="brand-scroll-content flex flex-wrap md:flex-nowrap space-x-0 gap-3 py-2">
            <NuxtLink v-for="(brand, index) in popularBrands" :key="index"
               :to="`/mall/category/0?brandId=${brand.id}`"
               class="brand-item flex-shrink-0 bg-gray-50 hover:bg-gray-100 rounded-lg p-3 transition-all duration-300 flex flex-col items-center justify-center w-1/3 md:w-auto mb-2 md:mb-0">
              <div class="brand-logo w-16 h-16 flex items-center justify-center mb-2">
                <img :src="brand.logo" :alt="brand.name" class="max-w-full max-h-full object-contain">
              </div>
              <div class="brand-name text-xs text-center">{{brand.name}}</div>
            </NuxtLink>
          </div>
        </div>
        <button @click="scrollBrands('left')" class="absolute left-0 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white shadow-md rounded-full p-2 z-10">
          <span class="block transform rotate-180 text-gray-600">➔</span>
        </button>
        <button @click="scrollBrands('right')" class="absolute right-0 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white shadow-md rounded-full p-2 z-10">
          <span class="block text-gray-600">➔</span>
        </button>
      </div>
    </div>
    <ProductList
      title="为你推荐"
      :rightSwitch="true"
      :imageWidth="0"
      :imageHeight="0"
      :columnsPerRow="2"
      :columnsPerRowMd="3"
      :columnsPerRowLg="5"
      :countMd="6"
      :countLg="5"
      :countsm="4"
      :enableResponsiveCount="true"
      :autoLoad="true"
      :gap="2"
      class="product-container"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useMallUserStore } from '~/store/mall/user';
import { useMallCartStore } from '~/store/mall/cart';
import { useMallCategoryStore } from '~/store/mall/category';
import { Message } from '@arco-design/web-vue';
import ProductList from '~/components/mall/user/product-list/ProductList.vue';
import goodsApi from '~/api/mall/goods.js';
import bannerApi from '~/api/mall/banner.js';
definePageMeta({
  layout: 'mall'
});
const activeImageUrl = ref('https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/dd997a1ead505bbd10ee34c355a0215c.jpg')
// 移动端菜单状态
const mobileMenuOpen = ref(false);
const mobileCategoryOpen = ref(null);

// 分类悬浮状态
const activeCategory = ref(null); // 一级分类悬浮状态
const activeSecondCategory = ref(null); // 二级分类悬浮状态 {parentIndex, index}
const activeThirdCategory = ref(null); // 三级分类悬浮状态 {parentIndex, parentSubIndex, index}

// 切换移动端分类展开状态
const toggleMobileCategory = (index) => {
  mobileCategoryOpen.value = mobileCategoryOpen.value === index ? null : index;
};

// 分类数据，初始为空，将通过API获取
const categories = ref([]);

// 从API获取的分类数据更新到前端显示
const updateCategoriesFromApi = () => {
  if (categoryStore.categoryTree && categoryStore.categoryTree.length > 0) {
    // 将API返回的分类数据转换为前端需要的格式
    categories.value = categoryStore.categoryTree.map(category => {
      return {
        id: category.id,
        name: category.name,
        subtext: category.description || '查看全部商品',
        children: category.children || []
      };
    });
  }
};

const quickLinks = ref([
  '企业采购', '会员特惠', '办公家具', 
  '打印机', '电脑配件', '办公设备',
  '办公文具', '劳保用品', '企业礼品'
]);

const featuredProducts = ref([
  { tag: '企业办公耗材专场', name: '惠普 (HP) 粉盒 硒鼓 适用LaserJet Pro M15a/M15w打印机', price: '359.00', imageUrl: 'https://placehold.co/160x160/ffffff/333333?text=办公耗材' },
  { tag: '立马省电脑专场', name: '联想(Lenovo)扬天V330 14英寸商用办公笔记本电脑', price: '3799.00', imageUrl: 'https://placehold.co/160x160/ffffff/333333?text=笔记本电脑' },
  { tag: '企业办公家具专场', name: '西昊人体工学椅 电脑椅家用 办公椅子靠背升降转椅', price: '399.00', imageUrl: 'https://placehold.co/160x160/ffffff/333333?text=办公椅' },
  { tag: '企业办公文具专场', name: '得力(deli)10本装A5记事本子商务办公软抄本', price: '39.50', imageUrl: 'https://placehold.co/160x160/ffffff/333333?text=记事本' },
]);

// 状态管理
const userStore = useMallUserStore();
const cartStore = useMallCartStore();
const categoryStore = useMallCategoryStore();

// 加载状态
const loading = ref(false);

// 在组件挂载后恢复用户会话并获取分类数据
onMounted(async () => {
  try {
    loading.value = true;
    // 恢复用户会话
    await userStore.restoreSession();

    // 获取商品分类数据
    if (!categoryStore.hasCategories) {
      const result = await categoryStore.getCategoryTree();
      if (result.success) {
        // 更新分类数据
        updateCategoriesFromApi();
      } else {
        console.error('获取商品分类失败:', result.message);
      }
    } else {
      // 使用已有的分类数据
      updateCategoriesFromApi();
    }

    // 获取轮播图数据
    await fetchBanners();

    // 获取热销品牌数据
    await fetchFeaturedBrands();
  } catch (error) {
    console.error('初始化数据失败:', error);
  } finally {
    loading.value = false;
  }
});

// 切换登录状态的方法(仅用于演示)
const toggleLogin = () => {
  if (userStore.loggedIn) {
    userStore.logout();
  } else {
    // 模拟登录
    const userData = {
      id: 1001,
      username: 'testuser',
      nickname: '测试用户',
      avatar: 'https://placehold.co/100x100/ffffff/333333?text=User',
      level: '黄金会员',
      token: 'sample-token-123456'
    };
    userStore.setUser(userData);
  }
};

// 轮播图片数据
const activeSlide = ref(0);
const bannerSlides = ref([]);

// 获取轮播图数据
const fetchBanners = async () => {
  try {
    const response = await bannerApi.getBanners({
      limit: 10
    });

    if (response && response.code === 200 && response.data) {
      bannerSlides.value = response.data.map(banner => ({
        id: banner.id,
        title: banner.title,
        image: banner.imageUrl,
        linkUrl: banner.linkUrl,
        linkType: banner.linkType
      }));

      // 获取到轮播图数据后启动自动播放
      startAutoplay();
    } else {
      console.error('获取轮播图失败:', response?.message || '未知错误');
    }
  } catch (error) {
    console.error('获取轮播图数据失败:', error);
  }
};

// 轮播控制方法
const nextSlide = () => {
  if (bannerSlides.value.length > 0) {
    activeSlide.value = (activeSlide.value + 1) % bannerSlides.value.length;
  }
};

const prevSlide = () => {
  if (bannerSlides.value.length > 0) {
    activeSlide.value = (activeSlide.value - 1 + bannerSlides.value.length) % bannerSlides.value.length;
  }
};

// 自动轮播
let autoplayInterval;

onUnmounted(() => {
  clearInterval(autoplayInterval);
});

const startAutoplay = () => {
  if (bannerSlides.value.length > 1) {
    autoplayInterval = setInterval(() => {
      nextSlide();
    }, 5000); // 5秒切换一次
  }
};

const stopAutoplay = () => {
  clearInterval(autoplayInterval);
};

// 处理轮播图点击事件
const handleBannerClick = (slide) => {
  if (!slide.linkUrl || slide.linkType === 3) return;

  if (slide.linkType === 1) {
    // 内部链接，使用路由跳转
    navigateTo(slide.linkUrl);
  } else if (slide.linkType === 2) {
    // 外部链接，新窗口打开
    window.open(slide.linkUrl, '_blank');
  }
};

const holidayGifts = ref([
  { name: '五芳斋粽子礼盒 端午节礼品', price: '129.00', imageUrl: 'https://placehold.co/120x120/ffffff/333333?text=粽子礼盒' },
  { name: '北京稻香村月饼礼盒 中秋送礼', price: '168.00', imageUrl: 'https://placehold.co/120x120/ffffff/333333?text=月饼礼盒' },
  { name: '茅台葡萄酒 750ml*6瓶整箱装', price: '1299.00', imageUrl: 'https://placehold.co/120x120/ffffff/333333?text=葡萄酒' },
  { name: '八马茶业 安溪铁观音礼盒装', price: '298.00', imageUrl: 'https://placehold.co/120x120/ffffff/333333?text=铁观音' },
  { name: '苏泊尔厨具套装 公司年会奖品', price: '899.00', imageUrl: 'https://placehold.co/120x120/ffffff/333333?text=厨具套装' },
  { name: '周大福黄金吊坠 企业福利礼品', price: '2999.00', imageUrl: 'https://placehold.co/120x120/ffffff/333333?text=黄金吊坠' },
]);

// 获取分类的所有子级分类数量
const getCategoryChildrenCount = (category) => {
  let count = 0;
  if (category.children && category.children.length > 0) {
    count += category.children.length;
    category.children.forEach(subCategory => {
      if (subCategory.children && subCategory.children.length > 0) {
        count += subCategory.children.length;
      }
    });
  }
  return count;
};

// 获取子分类面板的垂直位置
const getSubcategoryPanelTop = (index) => {
  try {
    // 获取分类项 - 注意现在分类项是a标签而不是div
    const categoryItems = document.querySelectorAll('a.category-item');
    if (!categoryItems || !categoryItems[index]) return '0px';
    
    // 获取分类项的位置信息
    const rect = categoryItems[index].getBoundingClientRect();
    
    // 获取容器信息
    const container = document.querySelector('.category-menu');
    if (!container) return '0px';
    const containerRect = container.getBoundingClientRect();
    
    // 面板高度和容器高度
    const panelHeight = 400;
    const containerBottom = containerRect.bottom;
    
    // 如果分类项底部距离容器底部小于面板高度的一半，则从下往上显示
    if (containerBottom - rect.bottom < panelHeight / 2) {
      // 计算从下往上的位置，使面板底部与容器底部对齐
      return `${containerBottom - panelHeight}px`;
    }
    
    // 否则从上往下显示，与分类项顶部对齐
    return `${rect.top}px`;
  } catch (error) {
    console.error('Error calculating panel position:', error);
    return '0px';
  }
};

// 获取子分类面板的水平位置
const getSubcategoryPanelLeft = () => {
  try {
    const categoryMenu = document.querySelector('.category-menu');
    if (categoryMenu) {
      const rect = categoryMenu.getBoundingClientRect();
      return `${rect.right}px`;
    }
    return '100%'; // 使用相对单位
  } catch (error) {
    console.error('Error calculating panel horizontal position:', error);
    return '192px';
  }
};

// 热销品牌数据
const popularBrands = ref([]);

// 获取热销品牌数据
const fetchFeaturedBrands = async () => {
  try {
    const params = {
      count: 15, // 获取15个品牌
      random: true // 随机获取
    };
    const response = await goodsApi.getFeaturedBrands(params);
    if (response && response.code === 200 && response.data) {
      // 将API返回的品牌数据转换为前端需要的格式
      popularBrands.value = response.data.map(brand => ({
        name: brand.name,
        logo: brand.imageUrl || `https://placehold.co/80x80/ffffff/333333?text=${brand.name}`,
        id: brand.id
      }));
    } else {
      console.error('获取热销品牌失败:', response?.message || '未知错误');
      // 使用默认数据作为备选
      setDefaultBrands();
    }
  } catch (error) {
    console.error('获取热销品牌出错:', error);
    // 发生错误时使用默认数据
    setDefaultBrands();
  }
};

// 设置默认品牌数据（当API请求失败时使用）
const setDefaultBrands = () => {
  popularBrands.value = [
    { name: '联想', logo: 'https://placehold.co/80x80/ffffff/333333?text=联想', categoryId: '101' },
    { name: '华为', logo: 'https://placehold.co/80x80/ffffff/333333?text=华为', categoryId: '102' },
    { name: '戴尔', logo: 'https://placehold.co/80x80/ffffff/333333?text=戴尔', categoryId: '103' },
    { name: '苹果', logo: 'https://placehold.co/80x80/ffffff/333333?text=苹果', categoryId: '105' },
    { name: '小米', logo: 'https://placehold.co/80x80/ffffff/333333?text=小米', categoryId: '106' }
  ];
};

// 品牌滚动方法
const scrollBrands = (direction) => {
  const container = document.querySelector('.brand-scroll-wrapper');
  if (!container) return;
  
  const scrollAmount = 200; // 每次滚动的像素数
  const currentScroll = container.scrollLeft;
  
  if (direction === 'left') {
    container.scrollTo({
      left: currentScroll - scrollAmount,
      behavior: 'smooth'
    });
  } else {
    container.scrollTo({
      left: currentScroll + scrollAmount,
      behavior: 'smooth'
    });
  }
};

const recommendedProducts = ref([
  { name: 'Apple iPad 2021款 10.2英寸平板电脑', price: '2499.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=iPad' },
  { name: '戴尔(DELL)灵越14英寸轻薄笔记本电脑', price: '4799.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=戴尔笔记本' },
  { name: '美的(Midea)1.5匹变频冷暖空调挂机', price: '2399.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=美的空调' },
  { name: '华为手机 HUAWEI P50 Pro 8GB+256GB', price: '6488.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=华为手机' },
  { name: '飞利浦(PHILIPS)55英寸4K超高清智能电视', price: '3199.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=飞利浦电视' },
  { name: '罗技(Logitech)MX Keys无线蓝牙键盘', price: '799.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=罗技键盘' },
  { name: '小米智能门锁 Pro 指纹锁密码锁', price: '1599.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=小米门锁' },
  { name: 'OPPO Find X5 Pro 5G手机 12GB+256GB', price: '5999.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=OPPO手机' },
  { name: '惠普(HP)打印一体机 彩色喷墨打印复印扫描', price: '899.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=惠普打印机' },
  { name: '佳能(Canon)EOS R10 微单相机', price: '6999.00', imageUrl: 'https://placehold.co/140x140/ffffff/333333?text=佳能相机' },
]);

// 添加商品到购物车
const addToCart = async (product) => {
  try {
    const result = await cartStore.addToCart(product, 1);
    if (result.success) {
      Message.success('商品已成功加入购物车');
    } else {
      Message.error(result.message || '加入购物车失败');
    }
  } catch (error) {
    console.error('加入购物车出错:', error);
    Message.error('加入购物车失败，请稍后再试');
  }
};
</script>

<style scoped>
/* 响应式样式 */
/* 移动端样式 */
@media (max-width: 768px) {
  .subcategory-panel,
  .third-category-panel,
  .fourth-category-panel {
    display: none !important;
  }
  
  .carousel-indicators {
    bottom: 2px;
  }
  
  .carousel-indicators button {
    width: 6px;
    height: 6px;
  }
}

/* 针对1536px以下屏幕的样式 */
@media (max-width: 1536px) {
  .product-container {
    overflow-x: hidden;
  }
  
  .brand-scroll-content {
    flex-wrap: wrap;
  }
  
  .banner-container {
    margin: 0 -0.5rem;
  }
  
  .banner-center {
    min-width: 0;
  }
  
  .subcategory-panel,
  .third-category-panel,
  .fourth-category-panel {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    z-index: 1000;
  }
}

/* 针对1280px以下屏幕的样式 */
@media (max-width: 1280px) {
  .banner-left,
  .banner-right {
    width: 20% !important;
  }
  
  .banner-center {
    flex: 1;
  }
}

/* 在这里可以添加页面特定的样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 菜单项样式 - 现在是a标签 */
.category-item {
  padding: 0.5rem 0.5rem 0.5rem 1rem;
  transition: background-color 0.2s ease;
  text-decoration: none;
  color: inherit;
  display: block;
}

.category-name {
  font-size: 0.875rem;
}

.category-subtext {
  font-size: 0.75rem;
}

.subcategory-panel {
  border: 1px solid #eaeaea;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000; /* 确保子分类面板显示在最上层 */
  transform: translateX(0); /* 确保子分类面板不受overflow:hidden影响 */
  overflow-y: visible;
  max-height: 80vh;
}

.third-category-panel,
.fourth-category-panel {
  border: 1px solid #eaeaea;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  max-height: 70vh;
}

/* 品牌滚动容器样式 */
.brand-scroll-container {
  position: relative;
  padding: 0 0.625rem;
}

.brand-scroll-wrapper {
  overflow-x: auto;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.brand-item {
  width: 6.25rem;
  height: 6.25rem;
  border: 1px solid #f0f0f0;
}

@media (max-width: 768px) {
  .brand-item {
    width: 100%;
    height: 5rem;
  }
}

/* 隐藏滚动条但保留滚动功能 */
.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 移动端菜单样式 */
.mobile-menu-enter-active,
.mobile-menu-leave-active {
  transition: transform 0.3s ease;
}

.mobile-menu-enter-from,
.mobile-menu-leave-to {
  transform: translateX(-100%);
}

.mobile-menu-enter-to,
.mobile-menu-leave-from {
  transform: translateX(0);
}
:deep(.arco-image-img){
 height: 100%;
}
</style>
