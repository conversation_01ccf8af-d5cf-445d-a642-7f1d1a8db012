<template>
  <div class="wechat-auth-container">
    <!-- 授权处理中状态 -->
    <div v-if="loading" class="auth-status-box">
      <a-spin :loading="true" size="large" />
      <p class="mt-4 text-lg">授权处理中，请稍候...</p>
    </div>
    
    <!-- 授权成功状态 -->
    <div v-else-if="authSuccess" class="auth-status-box success">
      <a-icon type="check-circle" class="text-green-500 text-5xl mb-4" />
      <p class="text-xl font-medium mb-2">授权成功</p>
      <p class="text-gray-500 mb-6">{{ message }}</p>
      <p class="text-sm text-gray-400">{{ countdown }}秒后自动关闭...</p>
    </div>
    
    <!-- 授权失败状态 -->
    <div v-else class="auth-status-box error">
      <a-icon type="close-circle" class="text-red-500 text-5xl mb-4" />
      <p class="text-xl font-medium mb-2">授权失败</p>
      <p class="text-gray-500 mb-6">{{ error }}</p>
      <a-button type="primary" @click="retryAuth">重新授权</a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { useMallUserStore } from '~/store/mall/user';
import { useRoute } from 'vue-router';
import { navigateTo } from '#app';
import mallApi from '~/api/mall';

definePageMeta({
  layout: 'empty'
});

// 状态变量
const loading = ref(true);
const authSuccess = ref(false);
const message = ref('');
const error = ref('');
const countdown = ref(3);
let countdownTimer = null;

// 获取用户存储
const userStore = useMallUserStore();

// 获取路由参数
const route = useRoute();

// 处理微信授权
const handleWechatAuth = async () => {
  try {
    loading.value = true;
    
    // 直接从 URL 参数中获取令牌和用户信息
    const { token, userId, nickname, avatar } = route.query;
    
    console.log('微信授权回调参数:', { token, userId, nickname, avatar });
    
    // 如果没有令牌，尝试从 localStorage 中获取
    let authToken = token;
    let authUserId = userId;
    let authNickname = nickname || '';
    let authAvatar = avatar || '';
    
    if ((!authToken || !authUserId) && process.client) {
      // 尝试从 localStorage 中获取令牌
      authToken = localStorage.getItem('mall_user_token');
      // 如果还是没有令牌，才报错
      if (!authToken) {
        throw new Error('缺少必要的授权参数');
      }
      // 如果有令牌但没有用户ID，尝试从已登录用户中获取
      if (!authUserId) {
        const currentUser = userStore.getUserInfo;
        if (currentUser && currentUser.id) {
          authUserId = currentUser.id;
          authNickname = currentUser.nickname || '';
          authAvatar = currentUser.avatar || '';
        }
      }
    }
    
    // 再次检查是否有必要的参数
    if (!authToken) {
      throw new Error('缺少必要的授权参数');
    }
    
    // 构造用户信息对象
    const userInfo = {
      id: authUserId,
      nickname: authNickname,
      avatar: authAvatar
    };
    
    // 使用 store 的微信登录方法处理登录
    try {
      console.log('尝试使用 userStore.handleWechatLogin 处理微信登录');
      
      // 准备登录数据
      const wechatLoginData = {
        token: authToken,
        userInfo: userInfo
      };
      
      // 调用 store 的微信登录方法
      const loginResult = await userStore.handleWechatLogin(wechatLoginData);
      console.log('微信授权页面登录结果:', loginResult);
      
      if (!loginResult.success) {
        console.warn('微信授权页面登录失败:', loginResult.message);
        
        // 即使 store 方法失败，仍然尝试直接保存 token 到 localStorage
        if (process.client) {
          localStorage.setItem('mall_user_token', authToken);
          console.log('备用方式：Token 已保存到 localStorage:', authToken);
        }
      }
    } catch (storeError) {
      console.error('微信授权页面处理登录失败:', storeError);
      
      // 如果使用 store 方法失败，则直接保存 token 到 localStorage
      if (process.client) {
        localStorage.setItem('mall_user_token', authToken);
        console.log('备用方式：Token 已保存到 localStorage:', authToken);
      }
    }
    
    // 显示成功信息
    authSuccess.value = true;
    message.value = '登录成功，您已完成微信授权';
    
    // 启动倒计时
    startCountdown();
    
    // 这是在手机微信浏览器中打开的页面，不需要与父窗口通信
    console.log('微信授权成功，已保存令牌');
    
    // 如果是在微信内打开，则显示成功页面即可
    // 商城页面会通过轮询接口获知登录成功
  } catch (error) {
    console.error('微信授权处理失败:', error);
    authSuccess.value = false;
    error.value = error.message || '授权处理失败，请重试';
  } finally {
    loading.value = false;
  }
};

// 启动倒计时
const startCountdown = () => {
  countdownTimer = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value--;
    } else {
      // 倒计时结束，关闭窗口
      clearInterval(countdownTimer);
      
      // 直接跳转到会员中心，因为这是在微信浏览器中打开的页面
      console.log('倒计时结束，准备跳转到会员中心');
      if (process.client) {
        // 在客户端运行时使用 window.location
        window.location.href = '/mall/user/center';
      } else {
        // 在服务端渲染时使用 navigateTo
        navigateTo('/mall/user/center');
      }
    }
  }, 1000);
};

// 重新授权
const retryAuth = () => {
  if (process.client) {
    window.location.reload();
  }
};

// 判断是否在微信浏览器中
const isWechatBrowser = () => {
  if (!process.client) return false;
  const ua = navigator.userAgent.toLowerCase();
  return ua.indexOf('micromessenger') !== -1;
};

// 页面加载时处理授权
onMounted(() => {
  handleWechatAuth();
});

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});
</script>

<style scoped>
.wechat-auth-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  padding: 20px;
}

.auth-status-box {
  width: 100%;
  max-width: 320px;
  background-color: white;
  border-radius: 12px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.success {
  border-top: 4px solid #52c41a;
}

.error {
  border-top: 4px solid #f5222d;
}
</style>
