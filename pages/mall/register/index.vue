<template>
  <div class="register-container bg-white">
    <!-- 头部区域 -->
    <div class="header-container w-full border-b border-gray-200">
      <div class="container mx-auto px-4 py-4 flex items-center">
        <div class="logo-container cursor-pointer" @click="navigateToHome">
          <a-image :preview="false" src="https://jlc-4.oss-cn-guangzhou.aliyuncs.com/avatar/cf49eb9e51b3c935200b32595fc5bf56.png" width="120" alt="八灵logo" />
        </div>
        <div class="ml-4 text-lg text-gray-500">欢迎注册</div>
      </div>
    </div>
    <div class="flex-grow flex items-center justify-center py-10">
      <div class="register-box p-0 max-w-md w-full">
      <div class="shadow-md rounded-lg overflow-hidden bg-white">
        <!-- 注册区域 -->
        <div class="py-10 px-8 bg-white">
          <h2 class="text-2xl font-bold text-gray-800 mb-6">会员注册</h2>
          
          <!-- 注册表单 -->
          
          <!-- 注册表单 -->
          <a-form :model="formState" class="register-form" @submit.prevent="handleRegister">
            <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
              <a-input v-model="formState.phone" placeholder="请输入手机号" size="large" class="rounded" />

            </a-form-item>
            
            <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
              <div class="flex relative w-full">
                <a-input v-model="formState.verificationCode" placeholder="请输入验证码" size="large" class="rounded-l" />

                <a-button @click="sendVerificationCode" size="large" class="code-btn" :disabled="cooldown > 0">
                  {{ cooldown > 0 ? `${cooldown}秒后重发` : '获取验证码' }}
                </a-button>
              </div>
            </a-form-item>
            
            <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
              <a-input v-model="formState.username" placeholder="请设置用户名" size="large" class="rounded" />

            </a-form-item>
            
            <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
              <a-input-password v-model="formState.password" placeholder="请设置密码" size="large" class="rounded" />

            </a-form-item>
            
            <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
              <a-input-password v-model="formState.confirmPassword" placeholder="请再次输入密码" size="large" class="rounded" />

            </a-form-item>
            
            <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
              <div class="flex items-center">
                <a-checkbox v-model="formState.agreement">
                  我已阅读并同意
                  <a class="text-red-600 hover:text-red-800">《用户协议》</a>
                  和
                  <a class="text-red-600 hover:text-red-800">《隐私政策》</a>
                </a-checkbox>

              </div>
            </a-form-item>
            
            <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
              <a-button type="primary" class="w-full rounded" size="large" :style="{backgroundColor: '#e53e3e', borderColor: '#e53e3e'}" @click="handleRegister" :loading="isLoading">
                {{ isLoading ? '注册中...' : '立即注册' }}
              </a-button>
            </a-form-item>
            
            <a-form-item :style="{marginBottom: '0'}" :hide-label="true">
              <div class="flex justify-center items-center">
                <span class="text-gray-600">已有账号？</span>
                <NuxtLink to="/mall/login" class="text-red-600 hover:text-red-800 ml-1">立即登录</NuxtLink>
              </div>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
    </div>
    
    <!-- 底部区域 -->
    <div class="footer-container w-full border-t border-gray-200 bg-gray-50">
      <div class="container mx-auto px-4 py-6">
        <div class="text-center text-gray-500 text-xs">
          <div class="links flex justify-center space-x-4 mb-2">
            <a href="#" class="hover:text-red-600">关于我们</a>
            <span>|</span>
            <a href="#" class="hover:text-red-600">联系我们</a>
            <span>|</span>
            <a href="#" class="hover:text-red-600">人才招聘</a>
            <span>|</span>
            <a href="#" class="hover:text-red-600">商家入驻</a>
            <span>|</span>
            <a href="#" class="hover:text-red-600">广告服务</a>
            <span>|</span>
            <a href="#" class="hover:text-red-600">手机商城</a>
            <span>|</span>
            <a href="#" class="hover:text-red-600">友情链接</a>
            <span>|</span>
            <a href="#" class="hover:text-red-600">销售联盟</a>
          </div>
          <div class="copyright">
            Copyright © 2023-2025 八灵云 版权所有
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { Message } from '@arco-design/web-vue';
import { useMallUserStore } from '~/store/mall/user';
definePageMeta({
  layout: 'empty'
});

const router = useRouter();
const userStore = useMallUserStore();

// 表单数据
const formState = reactive({
  phone: '',
  verificationCode: '',
  username: '',
  password: '',
  confirmPassword: '',
  agreement: false
});

// 表单错误信息
const errors = reactive({
  phone: '',
  username: '',
  password: '',
  confirmPassword: '',
  agreement: '',
  verificationCode: ''
});

// 状态变量
const cooldown = ref(0);
const isLoading = ref(false);

// 点击logo返回首页
const navigateToHome = () => {
  navigateTo('/mall');
};

// 验证手机号
const validatePhone = () => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(formState.phone)) {
    errors.phone = '请输入正确的手机号码';
    return false;
  }
  errors.phone = '';
  return true;
};

// 发送验证码
const sendVerificationCode = async () => {
  // 验证手机号
  if (!validatePhone()) {
    return;
  }

  try {
    // 调用发送注册短信验证码接口
    const result = await userStore.sendRegisterSmsCode(formState.phone);

    if (result.success) {
      // 开始倒计时
      cooldown.value = 60;
      const timer = setInterval(() => {
        cooldown.value--;
        if (cooldown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);

      // 显示发送成功提示
      Message.success(result.message || '验证码已发送到您的手机');
    } else {
      Message.error(result.message || '发送验证码失败，请稍后再试');
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    Message.error('发送验证码失败，请稍后再试');
  }
};

// 验证表单
const validateForm = () => {
  let isValid = true;
  
  // 验证手机号
  if (!formState.phone) {
    Message.error('请输入手机号码');
    isValid = false;
  } else if (!/^1[3-9]\d{9}$/.test(formState.phone)) {
    Message.error('请输入正确的手机号码');
    isValid = false;
  }
  
  // 验证用户名
  if (!formState.username) {
    Message.error('请输入用户名');
    isValid = false;
  } else if (formState.username.length < 4 || formState.username.length > 20) {
    Message.error('用户名长度应为4-20个字符');
    isValid = false;
  }
  
  // 验证密码
  if (!formState.password) {
    Message.error('请输入密码');
    isValid = false;
  } else if (formState.password.length < 6) {
    Message.error('密码长度不能少于6个字符');
    isValid = false;
  }
  
  // 验证确认密码
  if (!formState.confirmPassword) {
    Message.error('请再次输入密码');
    isValid = false;
  } else if (formState.password !== formState.confirmPassword) {
    Message.error('两次输入的密码不一致');
    isValid = false;
  }
  
  // 验证验证码
  if (!formState.verificationCode) {
    Message.error('请输入验证码');
    isValid = false;
  }
  
  // 验证协议勾选
  if (!formState.agreement) {
    Message.error('请阅读并同意用户协议和隐私政策');
    isValid = false;
  }
  
  return isValid;
};

// 处理注册
const handleRegister = async () => {
  // 开始注册流程
  
  // 验证表单
  if (!validateForm()) {
    return;
  }
  
  try {
    isLoading.value = true;
    
    // 准备注册数据
    const registerData = {
      username: formState.username,
      password: formState.password,
      phone: formState.phone,
      captcha: formState.verificationCode,
      nickname: formState.username // 默认昵称与用户名相同
    };
    
    // 使用store中的注册方法
    const result = await userStore.register(registerData);
    
    if (result.success) {
      Message.success('注册成功！即将跳转到登录页面...');
      
      // 注册成功后跳转到登录页面
      setTimeout(() => {
        router.push({
          path: '/mall/login',
          query: { registered: 'success', username: formState.username }
        });
      }, 1500);
    } else {
      Message.error(result.message || '注册失败，请稍后再试');
    }
  } catch (error) {
    console.error('注册失败:', error);
    Message.error('注册失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.register-container {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.register-box {
  width: 100%;
  max-width: 660px;
}

.code-btn {
  position: absolute;
    right: 0;
    color: red;
    z-index: 9999;
}

/* 覆盖Arco Design的主题色 */
:deep(.arco-btn-primary) {
  background-color: #dc2626;
  border-color: #dc2626;
}

:deep(.arco-btn-primary:hover) {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

:deep(.arco-checkbox-checked .arco-checkbox-icon) {
  background-color: #dc2626;
  border-color: #dc2626;
}
</style>
