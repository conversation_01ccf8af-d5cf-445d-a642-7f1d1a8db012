<template>
  <div class="news-detail-page">
    <!-- 面包屑导航
    <div class="breadcrumb-container bg-gray-50 py-3">
      <div class="container mx-auto px-4">
        <nav class="text-sm text-gray-600">
          <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink>
          <span class="mx-2">/</span>
          <NuxtLink to="/mall/news" class="hover:text-red-600">新闻资讯</NuxtLink>
          <span class="mx-2">/</span>
          <span v-if="article.category_name" class="text-gray-500">{{ article.category_name }}</span>
          <span class="mx-2">/</span>
          <span class="text-gray-500">正文</span>
        </nav>
      </div>
    </div> -->

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 文章内容区域 -->
        <div class="lg:col-span-3">
          <div v-if="loading" class="flex justify-center items-center h-64">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
          </div>
          
          <div v-else-if="article.id" class="bg-white rounded-lg shadow-sm p-6">
            <!-- 文章头部 -->
            <div class="article-header mb-6">
              <h1 class="text-3xl font-bold text-gray-900 mb-4 leading-tight">
                {{ article.title }}
              </h1>
              
              <div class="flex flex-wrap items-center text-sm text-gray-500 mb-4">
                <span class="flex items-center mr-6">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                  </svg>
                  <span v-time="article.created_at"></span>
                </span>
                
                <span class="flex items-center mr-6">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                  </svg>
                  {{ article.view_count || 0 }} 次浏览
                </span>
                
                <span v-if="article.category_name" class="flex items-center">
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                  </svg>
                  {{ article.category_name }}
                </span>
              </div>
            </div>

            <!-- 文章摘要 -->
            <div v-if="article.summary" class="article-summary bg-gray-50 p-4 rounded-lg mb-6">
              <h3 class="text-lg font-semibold text-gray-800 mb-2">文章摘要</h3>
              <p class="text-gray-700 leading-relaxed">{{ article.summary }}</p>
            </div>

            <!-- 封面图片 -->
            <div v-if="article.image_url" class="article-image mb-6">
              <img 
                :src="article.image_url" 
                :alt="article.title"
                class="w-full h-auto rounded-lg shadow-sm"
                @error="handleImageError"
              />
            </div>

            <!-- 文章内容 -->
            <div class="article-content prose prose-lg max-w-none">
              <div v-html="article.content" class="leading-relaxed"></div>
            </div>

            <!-- 文章底部信息 -->
            <div class="article-footer mt-8 pt-6 border-t border-gray-200">
              <div class="flex flex-wrap items-center justify-between">
                <div class="text-sm text-gray-500">
                  最后更新：<span v-time="article.updated_at"></span>
                </div>
                <div class="flex items-center space-x-4">
                  <button 
                    @click="shareArticle"
                    class="flex items-center text-gray-600 hover:text-red-600 transition-colors"
                  >
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"></path>
                    </svg>
                    分享
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="bg-white rounded-lg shadow-sm p-6 text-center">
            <div class="text-gray-500">
              <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
              </svg>
              <p class="text-lg">文章不存在或已下线</p>
              <NuxtLink to="/mall" class="text-red-600 hover:text-red-700 mt-2 inline-block">
                返回首页
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="lg:col-span-1">
          <!-- 相关文章 -->
          <div v-if="relatedArticles.length > 0" class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">相关文章</h3>
            <div class="space-y-4">
              <div 
                v-for="relatedArticle in relatedArticles" 
                :key="relatedArticle.id"
                class="border-b border-gray-100 pb-3 last:border-b-0 last:pb-0"
              >
                <NuxtLink 
                  :to="`/mall/news/${relatedArticle.id}`"
                  class="block hover:text-red-600 transition-colors"
                >
                  <h4 class="text-sm font-medium line-clamp-2 mb-1">
                    {{ relatedArticle.title }}
                  </h4>
                  <div class="text-xs text-gray-500 flex items-center">
                    <span v-time="relatedArticle.created_at"></span>
                    <span class="mx-1">·</span>
                    <span>{{ relatedArticle.view_count || 0 }} 浏览</span>
                  </div>
                </NuxtLink>
              </div>
            </div>
          </div>

          <!-- 最新文章 -->
          <div v-if="latestArticles.length > 0" class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">最新资讯</h3>
            <div class="space-y-4">
              <div 
                v-for="latestArticle in latestArticles" 
                :key="latestArticle.id"
                class="border-b border-gray-100 pb-3 last:border-b-0 last:pb-0"
              >
                <NuxtLink 
                  :to="`/mall/news/${latestArticle.id}`"
                  class="block hover:text-red-600 transition-colors"
                >
                  <h4 class="text-sm font-medium line-clamp-2 mb-1">
                    {{ latestArticle.title }}
                  </h4>
                  <div class="text-xs text-gray-500">
                    <span v-time="latestArticle.created_at"></span>
                  </div>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import mallApi from '@/api/mall';

// 定义页面元数据
definePageMeta({
  layout: 'mall',
  name: 'NewsDetail',
  path: '/mall/news/:id'
});

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const article = ref({});
const relatedArticles = ref([]);
const latestArticles = ref([]);

// 获取文章详情
const fetchArticleDetail = async (id) => {
  try {
    loading.value = true;
    const response = await mallApi.news.getArticleDetail(id);
    
    if (response.code === 200) {
      article.value = response.data;
      
      // 设置页面标题
      if (response.data.title) {
        useHead({
          title: response.data.title,
          meta: [
            { name: 'description', content: response.data.summary || response.data.title },
            { name: 'keywords', content: response.data.seo_keywords || '' },
            { property: 'og:title', content: response.data.seo_title || response.data.title },
            { property: 'og:description', content: response.data.seo_description || response.data.summary || '' },
            { property: 'og:image', content: response.data.image_url || '' }
          ]
        });
      }
      
      // 获取相关文章
      if (response.data.id && response.data.category_id) {
        fetchRelatedArticles(response.data.id);
      }
    } else {
      console.error('获取文章详情失败:', response.message);
    }
  } catch (error) {
    console.error('获取文章详情失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取相关文章
const fetchRelatedArticles = async (articleId) => {
  try {
    const response = await mallApi.news.getRelatedArticles(articleId, { limit: 5 });
    if (response.code === 200) {
      relatedArticles.value = response.data;
    }
  } catch (error) {
    console.error('获取相关文章失败:', error);
  }
};

// 获取最新文章
const fetchLatestArticles = async () => {
  try {
    const response = await mallApi.news.getLatestArticles({ limit: 8 });
    if (response.code === 200) {
      latestArticles.value = response.data.filter(item => item.id !== article.value.id);
    }
  } catch (error) {
    console.error('获取最新文章失败:', error);
  }
};

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none';
};

// 分享文章
const shareArticle = () => {
  if (navigator.share) {
    navigator.share({
      title: article.value.title,
      text: article.value.summary,
      url: window.location.href
    });
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href).then(() => {
      alert('链接已复制到剪贴板');
    });
  }
};

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    fetchArticleDetail(newId);
  }
}, { immediate: true });

// 页面挂载时获取最新文章
onMounted(() => {
  fetchLatestArticles();
});
</script>

<style scoped>
.news-detail-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 文章内容样式 */
.article-content :deep(h1),
.article-content :deep(h2),
.article-content :deep(h3),
.article-content :deep(h4),
.article-content :deep(h5),
.article-content :deep(h6) {
  margin: 1.5rem 0 1rem 0;
  font-weight: 600;
  color: #1f2937;
}

.article-content :deep(p) {
  margin: 1rem 0;
  line-height: 1.8;
  color: #374151;
}

.article-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.article-content :deep(ul),
.article-content :deep(ol) {
  margin: 1rem 0;
  padding-left: 2rem;
}

.article-content :deep(li) {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.article-content :deep(blockquote) {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #6b7280;
}

.article-content :deep(code) {
  background-color: #f3f4f6;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.article-content :deep(pre) {
  background-color: #f3f4f6;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}
</style>
