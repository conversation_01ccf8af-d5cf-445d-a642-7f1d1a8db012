<template>
  <div class="news-list-page">


    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 新闻列表区域 -->
        <div class="lg:col-span-3">
          <!-- 页面标题 -->
          <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">新闻资讯</h1>
            <p class="text-gray-600">了解最新动态，掌握行业资讯</p>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="flex justify-center items-center h-64">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
          </div>

          <!-- 新闻列表 -->
          <div v-else-if="articles.length > 0" class="space-y-6">
            <div 
              v-for="article in articles" 
              :key="article.id"
              class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow p-6"
            >
              <div class="flex flex-col md:flex-row gap-4">
                <!-- 文章图片 -->
                <div v-if="article.image_url" class="md:w-48 flex-shrink-0">
                  <img 
                    :src="article.image_url" 
                    :alt="article.title"
                    class="w-full h-32 md:h-24 object-cover rounded-lg"
                    @error="handleImageError"
                  />
                </div>

                <!-- 文章信息 -->
                <div class="flex-1">
                  <div class="flex items-center text-xs text-gray-500 mb-2">
                    <span v-if="article.category_name" class="bg-red-100 text-red-600 px-2 py-1 rounded mr-2">
                      {{ article.category_name }}
                    </span>
                    <span v-time="article.created_at"></span>
                    <span class="mx-2">·</span>
                    <span>{{ article.view_count || 0 }} 浏览</span>
                  </div>

                  <h2 class="text-xl font-semibold text-gray-900 mb-2 hover:text-red-600 transition-colors">
                    <NuxtLink :to="`/mall/news/${article.id}`">
                      {{ article.title }}
                    </NuxtLink>
                  </h2>

                  <p v-if="article.summary" class="text-gray-600 text-sm line-clamp-2 mb-3">
                    {{ article.summary }}
                  </p>

                  <div class="flex items-center justify-between">
                    <div class="text-xs text-gray-500">
                      更新时间：<span v-time="article.updated_at"></span>
                    </div>
                    <NuxtLink 
                      :to="`/mall/news/${article.id}`"
                      class="text-red-600 hover:text-red-700 text-sm font-medium"
                    >
                      阅读全文 →
                    </NuxtLink>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="bg-white rounded-lg shadow-sm p-12 text-center">
            <div class="text-gray-500">
              <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
              </svg>
              <p class="text-lg">暂无新闻资讯</p>
              <p class="text-sm mt-1">请稍后再来查看</p>
            </div>
          </div>

          <!-- 分页 -->
          <div v-if="pageInfo.totalPages > 1" class="mt-8 flex justify-center">
            <nav class="flex items-center space-x-2">
              <button 
                @click="changePage(pageInfo.page - 1)"
                :disabled="pageInfo.page <= 1"
                class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                上一页
              </button>
              
              <span class="px-3 py-2 text-sm text-gray-700">
                第 {{ pageInfo.page }} 页，共 {{ pageInfo.totalPages }} 页
              </span>
              
              <button 
                @click="changePage(pageInfo.page + 1)"
                :disabled="pageInfo.page >= pageInfo.totalPages"
                class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                下一页
              </button>
            </nav>
          </div>
        </div>

        <!-- 侧边栏 -->
        <div class="lg:col-span-1">
          <!-- 新闻分类 -->
          <div v-if="categories.length > 0" class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">新闻分类</h3>
            <div class="space-y-2">
              <NuxtLink 
                to="/mall/news"
                :class="[
                  'block px-3 py-2 text-sm rounded-md transition-colors',
                  !selectedCategoryId ? 'bg-red-100 text-red-600' : 'text-gray-600 hover:bg-gray-100'
                ]"
              >
                全部分类
              </NuxtLink>
              <NuxtLink 
                v-for="category in categories" 
                :key="category.id"
                :to="`/mall/news?category=${category.id}`"
                :class="[
                  'block px-3 py-2 text-sm rounded-md transition-colors',
                  selectedCategoryId === category.id ? 'bg-red-100 text-red-600' : 'text-gray-600 hover:bg-gray-100'
                ]"
              >
                {{ category.name }}
              </NuxtLink>
            </div>
          </div>

          <!-- 热门文章 -->
          <div v-if="hotArticles.length > 0" class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">热门文章</h3>
            <div class="space-y-4">
              <div 
                v-for="(hotArticle, index) in hotArticles" 
                :key="hotArticle.id"
                class="flex items-start space-x-3"
              >
                <span class="flex-shrink-0 w-6 h-6 bg-red-600 text-white text-xs font-bold rounded-full flex items-center justify-center">
                  {{ index + 1 }}
                </span>
                <div class="flex-1">
                  <NuxtLink 
                    :to="`/mall/news/${hotArticle.id}`"
                    class="text-sm font-medium text-gray-900 hover:text-red-600 transition-colors line-clamp-2"
                  >
                    {{ hotArticle.title }}
                  </NuxtLink>
                  <div class="text-xs text-gray-500 mt-1">
                    {{ hotArticle.view_count || 0 }} 浏览
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import mallApi from '@/api/mall';

// 定义页面元数据
definePageMeta({
  layout: 'mall',
  name: 'NewsList',
  path: '/mall/news'
});

// 设置页面标题
useHead({
  title: '新闻资讯',
  meta: [
    { name: 'description', content: '了解最新动态，掌握行业资讯' },
    { name: 'keywords', content: '新闻,资讯,动态,行业' }
  ]
});

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const articles = ref([]);
const categories = ref([]);
const hotArticles = ref([]);
const pageInfo = ref({
  page: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0
});

// 计算属性
const selectedCategoryId = computed(() => {
  return route.query.category ? parseInt(route.query.category) : null;
});

// 获取新闻列表
const fetchArticles = async (page = 1) => {
  try {
    loading.value = true;
    const params = {
      page,
      pageSize: 10
    };
    
    if (selectedCategoryId.value) {
      params.category_id = selectedCategoryId.value;
    }
    
    const response = await mallApi.news.getArticles(params);
    
    if (response.code === 200) {
      articles.value = response.data.items || [];
      pageInfo.value = response.data.pageInfo || pageInfo.value;
    }
  } catch (error) {
    console.error('获取新闻列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取新闻分类
const fetchCategories = async () => {
  try {
    const response = await mallApi.news.getCategories({ limit: 10 });
    if (response.code === 200) {
      categories.value = response.data;
    }
  } catch (error) {
    console.error('获取新闻分类失败:', error);
  }
};

// 获取热门文章
const fetchHotArticles = async () => {
  try {
    const response = await mallApi.news.getHotArticles({ limit: 5 });
    if (response.code === 200) {
      hotArticles.value = response.data;
    }
  } catch (error) {
    console.error('获取热门文章失败:', error);
  }
};

// 切换页面
const changePage = (page) => {
  if (page >= 1 && page <= pageInfo.value.totalPages) {
    fetchArticles(page);
  }
};

// 处理图片加载错误
const handleImageError = (event) => {
  event.target.style.display = 'none';
};

// 监听路由查询参数变化
watch(() => route.query, () => {
  fetchArticles(1);
}, { deep: true });

// 页面挂载时获取数据
onMounted(() => {
  fetchArticles();
  fetchCategories();
  fetchHotArticles();
});
</script>

<style scoped>
.news-list-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
