<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex">
        <!-- 左侧图片区域 -->
        <div class="w-1/2 bg-gradient-to-r from-orange-500 to-orange-600 flex flex-col justify-center items-center p-8 text-white">
          <img src="https://placehold.co/400x300/ffffff/333333?text=找回密码" alt="找回密码插图" class="w-full max-w-xs mb-6 rounded-lg shadow-lg">
          <h2 class="text-2xl font-bold mb-4">找回密码</h2>
          <p class="text-center mb-6">通过手机验证码重置您的账户密码</p>
          <div class="flex items-center space-x-4">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <span class="ml-2">安全保障</span>
            </div>
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <span class="ml-2">快速重置</span>
            </div>
          </div>
        </div>
        
        <!-- 右侧找回密码表单 -->
        <div class="w-1/2 p-8">
          <h2 class="text-2xl font-bold text-gray-800 mb-6">找回密码</h2>
        
        <!-- 操作成功提示 -->
        <div v-if="resetSuccess" class="mb-4 p-3 bg-green-50 text-green-600 rounded-md text-sm">
          {{ resetSuccess }}
        </div>
        
        <!-- 操作失败提示 -->
        <div v-if="resetError" class="mb-4 p-3 bg-red-50 text-red-600 rounded-md text-sm">
          {{ resetError }}
        </div>
        
        <!-- 找回密码表单 -->
        <form @submit.prevent="handleResetPassword" v-if="currentStep === 1">
          <div class="mb-4">
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">手机号码</label>
            <input 
              type="tel" 
              id="phone" 
              v-model="resetForm.phone" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="请输入注册时的手机号码"
              required
              pattern="^1[3-9]\d{9}$"
            >
            <p v-if="errors.phone" class="mt-1 text-sm text-red-600">{{ errors.phone }}</p>
          </div>
          
          <div class="mb-4">
            <label for="verificationCode" class="block text-sm font-medium text-gray-700 mb-1">验证码</label>
            <div class="flex">
              <input 
                type="text" 
                id="verificationCode" 
                v-model="resetForm.verificationCode" 
                class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                placeholder="请输入验证码"
                required
              >
              <button 
                type="button" 
                class="w-32 bg-gray-100 text-gray-700 py-2 px-4 border border-gray-300 rounded-r-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                @click="sendVerificationCode"
                :disabled="cooldown > 0"
              >
                {{ cooldown > 0 ? `${cooldown}秒后重发` : '获取验证码' }}
              </button>
            </div>
            <p v-if="errors.verificationCode" class="mt-1 text-sm text-red-600">{{ errors.verificationCode }}</p>
            <p v-if="verificationCodeSent" class="mt-1 text-xs text-green-600">
              {{ verificationCodeSent }}
            </p>
          </div>
          
          <div class="mb-6">
            <button 
              type="submit" 
              class="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              :disabled="isLoading"
            >
              {{ isLoading ? '验证中...' : '下一步' }}
            </button>
          </div>
        </form>

        <!-- 设置新密码表单 -->
        <form @submit.prevent="handleSetNewPassword" v-if="currentStep === 2">
          <div class="mb-4">
            <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
            <input 
              type="password" 
              id="newPassword" 
              v-model="resetForm.newPassword" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="请设置新密码"
              required
              minlength="6"
            >
            <p v-if="errors.newPassword" class="mt-1 text-sm text-red-600">{{ errors.newPassword }}</p>
          </div>
          
          <div class="mb-6">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
            <input 
              type="password" 
              id="confirmPassword" 
              v-model="resetForm.confirmPassword" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
              placeholder="请再次输入新密码"
              required
            >
            <p v-if="errors.confirmPassword" class="mt-1 text-sm text-red-600">{{ errors.confirmPassword }}</p>
          </div>
          
          <div class="mb-6">
            <button 
              type="submit" 
              class="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              :disabled="isLoading"
            >
              {{ isLoading ? '提交中...' : '重置密码' }}
            </button>
          </div>
        </form>
          
        <div class="text-center">
          <p class="text-sm text-gray-600">
            记起密码了? 
            <NuxtLink to="/mall/login" class="text-orange-600 hover:text-orange-800">返回登录</NuxtLink>
          </p>
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';

definePageMeta({
  layout: 'mall'
});

const router = useRouter();
const userStore = useMallUserStore();

// 当前步骤：1-验证手机号，2-设置新密码
const currentStep = ref(1);

// 重置密码表单数据
const resetForm = reactive({
  phone: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
});

// 表单错误信息
const errors = reactive({
  phone: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
});

// 状态管理
const isLoading = ref(false);
const cooldown = ref(0);
const verificationCodeSent = ref('');
const resetSuccess = ref('');
const resetError = ref('');
// 保存后端返回的验证码
const serverCaptcha = ref('');

// 验证手机号
const validatePhone = () => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(resetForm.phone)) {
    errors.phone = '请输入正确的手机号码';
    return false;
  }
  errors.phone = '';
  return true;
};

// 发送验证码
const sendVerificationCode = async () => {
  // 验证手机号
  if (!validatePhone()) {
    return;
  }
  
  try {
    // 调用发送验证码接口
    const result = await userStore.getForgotPasswordCaptcha(resetForm.phone);
    
    if (result.success) {
      // 开始倒计时
      cooldown.value = 60;
      const timer = setInterval(() => {
        cooldown.value--;
        if (cooldown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
      
      // 保存后端返回的验证码
      serverCaptcha.value = result.data?.captcha||'';
      console.log('后端返回的验证码:', serverCaptcha.value);
      
      // 显示发送成功提示
      verificationCodeSent.value = result.message || '验证码已发送到您的手机';
    } else {
      errors.phone = result.message || '发送验证码失败，请稍后再试';
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    errors.phone = '发送验证码失败，请稍后再试';
  }
};

// 验证第一步表单
const validateStepOne = () => {
  let isValid = true;
  
  // 验证手机号
  if (!validatePhone()) {
    isValid = false;
  }
  
  // 验证验证码
  if (!resetForm.verificationCode) {
    errors.verificationCode = '请输入验证码';
    isValid = false;
  } else {
    errors.verificationCode = '';
  }
  
  return isValid;
};

// 验证第二步表单
const validateStepTwo = () => {
  let isValid = true;
  
  // 验证密码
  if (resetForm.newPassword.length < 6) {
    errors.newPassword = '密码长度不能少于6个字符';
    isValid = false;
  } else {
    errors.newPassword = '';
  }
  
  // 验证确认密码
  if (resetForm.newPassword !== resetForm.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  } else {
    errors.confirmPassword = '';
  }
  
  return isValid;
};

// 处理第一步验证
const handleResetPassword = async () => {
  // 清除之前的提示
  resetSuccess.value = '';
  resetError.value = '';
  // 验证表单
  if (!validateStepOne()) {
    return;
  }
  
  try {
    isLoading.value = true;
    
    // 本地验证验证码
    if (serverCaptcha.value && resetForm.verificationCode === serverCaptcha.value) {
      // 验证成功，进入第二步
      currentStep.value = 2;
    } else {
      resetError.value = '验证码不正确，请重新输入';
    }
  } catch (error) {
    console.error('验证失败:', error);
    resetError.value = '验证失败，请稍后再试';
  } finally {
    isLoading.value = false;
  }
};

// 处理设置新密码
const handleSetNewPassword = async () => {
  // 清除之前的提示
  resetSuccess.value = '';
  resetError.value = '';
  
  // 验证表单
  if (!validateStepTwo()) {
    return;
  }
  
  try {
    isLoading.value = true;
    
    // 调用重置密码接口
    const response = await fetch('/api/v1/mall/user/reset-password', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: resetForm.phone,
        captcha: serverCaptcha.value, // 使用保存的验证码
        newPassword: resetForm.newPassword
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200) {
      resetSuccess.value = '密码重置成功！即将跳转到登录页面...';
      
      // 重置成功后跳转到登录页面
      setTimeout(() => {
        router.push({
          path: '/mall/login',
          query: { reset: 'success' }
        });
      }, 1500);
    } else {
      resetError.value = result.message || '密码重置失败，请稍后再试';
    }
  } catch (error) {
    console.error('密码重置失败:', error);
    resetError.value = '密码重置失败，请稍后再试';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* 找回密码页面特定样式 */
</style>
