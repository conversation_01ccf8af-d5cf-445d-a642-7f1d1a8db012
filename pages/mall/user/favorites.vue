<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/" class="hover:text-red-600">首页</NuxtLink> &gt;
      <NuxtLink to="/user/center" class="hover:text-red-600">会员中心</NuxtLink> &gt;
      <span>我的收藏</span>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载收藏商品...</p>
      </div>
      
      <!-- 未登录状态提示 -->
      <div v-else-if="!userStore.loggedIn" class="p-8 text-center">
        <div class="mb-6">
          <img src="https://placehold.co/120x120/ffffff/333333?text=未登录" alt="未登录" class="mx-auto mb-4 rounded-full border-4 border-gray-200">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">您尚未登录</h2>
          <p class="text-gray-600 mb-6">登录后可查看您收藏的商品</p>
          <div class="flex justify-center space-x-4">
            <NuxtLink to="/login" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              立即登录
            </NuxtLink>
            <NuxtLink to="/register" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition">
              注册账号
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- 已登录状态 - 我的收藏 -->
      <div v-else class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <div class="flex justify-between items-center mb-6 pb-2 border-b">
            <h2 class="text-xl font-bold">我的收藏</h2>
            <div class="flex items-center">
              <!-- 使用普通按钮替代单选按钮组 -->
              <div class="flex border rounded-lg overflow-hidden mr-2">
                <button 
                  class="px-3 py-1.5 flex items-center text-sm" 
                  :class="viewMode === 'grid' ? 'bg-blue-50 text-blue-600' : 'bg-white text-gray-600 hover:bg-gray-50'"
                  @click="viewMode = 'grid'"
                >
                  <i class="i-carbon-grid mr-1"></i>
                  网格视图
                </button>
                <button 
                  class="px-3 py-1.5 flex items-center text-sm border-l" 
                  :class="viewMode === 'list' ? 'bg-blue-50 text-blue-600' : 'bg-white text-gray-600 hover:bg-gray-50'"
                  @click="viewMode = 'list'"
                >
                  <i class="i-carbon-list mr-1"></i>
                  列表视图
                </button>
              </div>
              
              <a-dropdown trigger="click" @select="handleBatchAction">
                <a-button class="ml-2">
                  批量操作
                  <i class="i-carbon-chevron-down ml-1"></i>
                </a-button>
                <template #content>
                  <a-doption value="select-all">全选</a-doption>
                  <a-doption value="unselect-all">取消全选</a-doption>
                  <!-- <a-doption value="add-to-cart" v-if="selectedItems.length > 0">加入购物车</a-doption> -->
                  <a-doption value="remove" v-if="selectedItems.length > 0">
                    <span class="text-red-500">删除选中</span>
                  </a-doption>
                </template>
              </a-dropdown>
            </div>
          </div>
          
          <!-- 收藏商品列表 - 网格视图 -->
          <div v-if="favorites.length > 0 && viewMode === 'grid'" class="grid grid-cols-4 gap-4">
            <div v-for="(item, index) in favorites" :key="item.id"
                 class="product-card bg-white rounded-lg border border-gray-200 hover:shadow-md transition overflow-hidden relative">
              <div class="relative">
                <!-- 选择框 -->
                <div class="absolute top-2 left-2 z-10">
                  <a-checkbox v-model="item.selected" @change="updateSelectedItems"></a-checkbox>
                </div>

                <!-- 收藏时间 -->
                <div class="absolute top-2 right-2 z-10 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
                  {{ item.collectTime }}
                </div>

                <!-- 商品图片 -->
                <NuxtLink :to="`/mall/product/${item.targetId}`">
                  <img :src="item.image" :alt="item.name || '商品'" class="w-full h-48 object-cover">
                </NuxtLink>
              </div>

              <!-- 商品信息 -->
              <div class="p-3">
                <NuxtLink :to="`/mall/product/${item.targetId}`" class="block">
                  <h3 class="text-base font-bold text-gray-800 line-clamp-2 h-10 mb-2 text-ellipsis">{{ item.name || '未命名商品' }}</h3>
                </NuxtLink>

                <div class="flex justify-between items-center mb-2">
                  <div class="text-red-600 font-bold">¥{{ formatPrice(item.price) }}</div>
                </div>

                <div class="flex space-x-2">
                  <!-- <a-button size="small" @click="handleAddToCart(item)">
                    <template #icon><i class="i-carbon-shopping-cart"></i></template>
                    加入购物车
                  </a-button> -->

                  <a-popconfirm
                    content="确定要取消收藏该商品吗？"
                    @ok="handleRemoveFavorite(item.targetId)"
                  >
                    <a-button size="small" status="danger">
                      <template #icon><i class="i-carbon-close"></i></template>
                      取消收藏
                    </a-button>
                  </a-popconfirm>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 收藏商品列表 - 列表视图 -->
          <div v-else-if="favorites.length > 0 && viewMode === 'list'" class="divide-y divide-gray-200">
            <div v-for="(item, index) in favorites" :key="item.id"
                 class="product-row py-4 flex items-center hover:bg-gray-50 transition">
              <!-- 选择框 -->
              <div class="mr-4">
                <a-checkbox v-model="item.selected" @change="updateSelectedItems"></a-checkbox>
              </div>

              <!-- 商品图片 -->
              <div class="w-24 h-24 flex-shrink-0 mr-4">
                <NuxtLink :to="`/mall/product/${item.targetId}`">
                  <img :src="item.image" :alt="item.name || '商品'" class="w-full h-full object-cover rounded">
                </NuxtLink>
              </div>

              <!-- 商品信息 -->
              <div class="flex-grow mr-4">
                <NuxtLink :to="`/mall/product/${item.targetId}`" class="block">
                  <h3 class="text-lg font-bold text-gray-800 line-clamp-2 text-ellipsis">{{ item.name || '未命名商品' }}</h3>
                </NuxtLink>
                <div class="text-sm text-gray-500 mt-1">收藏于 {{ item.collectTime }}</div>
              </div>

              <!-- 价格和收藏时间 -->
              <div class="flex flex-col mx-6">
                <div class="text-red-600 font-bold text-lg">¥{{ formatPrice(item.price) }}</div>
                <div class="text-xs text-gray-500">{{ item.collectTime }}</div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex space-x-2">
                <!-- <a-button size="small" @click="handleAddToCart(item)">
                  <template #icon><i class="i-carbon-shopping-cart"></i></template>
                  加入购物车
                </a-button> -->

                <a-popconfirm
                  content="确定要取消收藏该商品吗？"
                  @ok="handleRemoveFavorite(item.targetId)"
                >
                  <a-button size="small" status="danger">
                    <template #icon><i class="i-carbon-close"></i></template>
                    取消收藏
                  </a-button>
                </a-popconfirm>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-if="favorites.length === 0" class="text-center py-16">
            <img src="https://placehold.co/120x120/ffffff/333333?text=空收藏" alt="空收藏" class="mx-auto mb-4">
            <p class="text-gray-600 mb-6">您还没有收藏任何商品</p>
            <NuxtLink to="/" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              去逛逛
            </NuxtLink>
          </div>
          
          <!-- 分页组件 -->
          <div v-if="totalItems > 0" class="mt-6 flex justify-center">
            <a-pagination
              v-model:current="currentPage"
              v-model:page-size="pageSize"
              :total="totalItems"
              show-total
              show-jumper
              show-page-size
              :page-size-options="[8, 16, 24, 32]"
              @change="handlePageChange"
              @page-size-change="handlePageSizeChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import UserSideNav from '~/components/mall/user/UserSideNav.vue';
import { useMallUserStore } from '~/store/mall/user';
import { useMallCartStore } from '~/store/mall/cart';
import { useFavorite } from '~/composables/useFavorite';
import mallApi from '~/api/mall';

definePageMeta({
  layout: 'mall'
});

// 用户状态管理
const userStore = useMallUserStore();
const cartStore = useMallCartStore();

// 使用收藏功能组合函数
const {
  isLoading: favoriteLoading,
  batchRemoveFromFavorites,
  getFavoriteList
} = useFavorite();

// 状态变量
const isLoading = ref(false);
const favorites = ref([]);
const selectedItems = ref([]);
const viewMode = ref('grid'); // 'grid' 或 'list'
const currentPage = ref(1);
const pageSize = ref(8); // 默认每页显示8条记录
const totalItems = ref(0);

// 更新选中项
const updateSelectedItems = () => {
  selectedItems.value = favorites.value
    .filter(item => item.selected)
    .map(item => item.id);
};

// 批量操作处理
const handleBatchAction = (key) => {
  switch (key) {
    case 'select-all':
      favorites.value.forEach(item => item.selected = true);
      updateSelectedItems();
      break;
    case 'unselect-all':
      favorites.value.forEach(item => item.selected = false);
      updateSelectedItems();
      break;
    case 'add-to-cart':
      handleBatchAddToCart();
      break;
    case 'remove':
      handleBatchRemove();
      break;
  }
};

// 批量加入购物车
const handleBatchAddToCart = async () => {
  if (selectedItems.value.length === 0) {
    Message.warning('请先选择商品');
    return;
  }
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 获取选中的商品
    const selectedProducts = favorites.value.filter(item => item.selected);
    
    // 加入购物车
    for (const product of selectedProducts) {
      cartStore.addToCart({
        id: product.id,
        name: product.name,
        price: product.price,
        image: product.image,
        quantity: 1
      });
    }
    
    Message.success(`成功将${selectedProducts.length}件商品加入购物车`);
  } catch (error) {
    console.error('批量加入购物车失败:', error);
    Message.error('批量加入购物车失败，请稍后再试');
  }
};

// 批量取消收藏
const handleBatchRemove = async () => {
  if (selectedItems.value.length === 0) {
    Message.warning('请先选择商品');
    return;
  }

  try {
    // 获取选中商品的targetId
    const selectedProducts = favorites.value.filter(item => item.selected);
    const targetIds = selectedProducts.map(item => item.targetId);

    // 调用批量取消收藏API
    const response = await mallApi.favorite.batchCancelFavorite(targetIds, 1, '批量取消收藏');

    if (response.code === 200) {
      Message.success(`成功取消收藏${targetIds.length}件商品`);
      // 重新加载收藏列表
      await loadFavorites();
      // 重置选中项
      selectedItems.value = [];
    } else {
      Message.error(response.message || '批量取消收藏失败');
    }
  } catch (error) {
    console.error('批量取消收藏失败:', error);
    Message.error('批量取消收藏失败，请稍后再试');
  }
};

// 加入购物车
const handleAddToCart = async (product) => {
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 加入购物车
    cartStore.addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      image: product.image,
      quantity: 1
    });
    
    Message.success('成功加入购物车');
  } catch (error) {
    console.error('加入购物车失败:', error);
    Message.error('加入购物车失败，请稍后再试');
  }
};

// 取消收藏
const handleRemoveFavorite = async (targetId) => {
  try {
    // 调用取消收藏API
    const response = await mallApi.favorite.cancelFavorite({
      targetId: [targetId],
      targetType: 1,
      remark: '取消收藏商品'
    });

    if (response.code === 200) {
      Message.success('取消收藏成功');
      // 重新加载收藏列表
      await loadFavorites();
    } else {
      Message.error(response.message || '取消收藏失败');
    }
  } catch (error) {
    console.error('取消收藏失败:', error);
    Message.error('取消收藏失败，请稍后再试');
  }
};

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page;
  loadFavorites();
};

// 处理每页显示数量变化
const handlePageSizeChange = (size) => {
  pageSize.value = size;
  // 当改变每页显示数量时，如果当前页不是第一页，可能会导致数据不足，所以重置为第一页
  if (currentPage.value > Math.ceil(totalItems.value / size)) {
    currentPage.value = 1;
  }
  loadFavorites();
};

// 由于新的API返回结构已经包含了商品信息，不再需要单独获取商品详情

// 加载收藏数据
const loadFavorites = async () => {
  try {
    isLoading.value = true;

    // 调用获取收藏列表，默认targetType为1
    const result = await getFavoriteList({
      page: currentPage.value,
      pageSize: pageSize.value,
      targetType: 1
    });

    if (result.success) {
      const { list, pagination } = result.data;

      // 处理收藏数据，使用新的数据结构
      favorites.value = list.map(item => {
        const goodsInfo = item.goodsInfo || {};
        return {
          id: item.id,
          targetId: item.targetId,
          targetType: item.targetType,
          name: goodsInfo.name || `商品 ${item.targetId}`,
          subtitle: goodsInfo.subtitle,
          price: parseFloat(goodsInfo.price) || 0,
          image: goodsInfo.image || `https://placehold.co/300x300/f5f5f5/333333?text=商品${item.targetId}`,
          collectTime: new Date(item.createdAt).toLocaleDateString(),
          remark: item.remark,
          selected: false
        };
      });

      // 更新分页信息
      totalItems.value = pagination.total;
      currentPage.value = pagination.page;
      pageSize.value = pagination.pageSize;
    } else {
      favorites.value = [];
      totalItems.value = 0;
    }
  } catch (error) {
    console.error('加载收藏失败:', error);
    favorites.value = [];
    totalItems.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 格式化价格显示
const formatPrice = (price) => {
  if (typeof price === 'number') {
    return price.toFixed(2);
  } else if (typeof price === 'string') {
    // 尝试将字符串转换为数字并格式化
    const num = parseFloat(price);
    return isNaN(num) ? price : num.toFixed(2);
  }
  return '0.00';
};

// 监听视图模式变化
watch(viewMode, () => {
  // 切换视图模式时重置选中状态
  favorites.value.forEach(item => item.selected = false);
  selectedItems.value = [];
});

// 暴露方法供其他组件使用
defineExpose({
  loadFavorites
});

// 在组件挂载后加载数据
onMounted(async () => {
  // 尝试恢复用户会话
  if (!userStore.loggedIn) {
    await userStore.restoreSession();
  }

  // 加载收藏数据
  if (userStore.loggedIn) {
    await loadFavorites();
  }
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  border-color: #3b82f6;
}

.text-ellipsis {
  overflow: hidden;
  width: 95%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
