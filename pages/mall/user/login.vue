<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex">
        <!-- 左侧图片区域 -->
        <div class="w-1/2 bg-gradient-to-r from-red-500 to-red-600 flex flex-col justify-center items-center p-8 text-white">
          <img src="https://placehold.co/400x300/ffffff/333333?text=会员登录" alt="登录插图" class="w-full max-w-xs mb-6 rounded-lg shadow-lg">
          <h2 class="text-2xl font-bold mb-4">欢迎回来</h2>
          <p class="text-center mb-6">登录您的账户，享受企业采购专属服务和优惠</p>
          <div class="flex items-center space-x-4">
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <span class="ml-2">安全保障</span>
            </div>
            <div class="flex items-center">
              <div class="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <span class="ml-2">快速响应</span>
            </div>
          </div>
        </div>
        
        <!-- 右侧登录表单 -->
        <div class="w-1/2 p-8">
          <h2 class="text-2xl font-bold text-gray-800 mb-6">会员登录</h2>
        
        <!-- 登录表单 -->
        <div v-if="loginError" class="mb-4 p-3 bg-red-50 text-red-600 rounded-md text-sm">
          {{ loginError }}
        </div>
        
        <form @submit.prevent="handleLogin">
          <div class="mb-4">
            <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名/手机号</label>
            <input 
              type="text" 
              id="username" 
              v-model="loginForm.username" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              placeholder="请输入用户名或手机号"
              required
            >
          </div>
          
          <div class="mb-4">
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
            <input 
              type="password" 
              id="password" 
              v-model="loginForm.password" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
              placeholder="请输入密码"
              required
            >
          </div>
          
          <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
              <input 
                type="checkbox" 
                id="remember" 
                v-model="loginForm.remember" 
                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              >
              <label for="remember" class="ml-2 block text-sm text-gray-700">记住我</label>
            </div>
            <div class="text-sm">
              <NuxtLink to="/mall/forgot-password" class="text-red-600 hover:text-red-800">忘记密码?</NuxtLink>
            </div>
          </div>
          
          <div class="mb-6">
            <button 
              type="submit" 
              class="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              :disabled="isLoading"
            >
              {{ isLoading ? '登录中...' : '登录' }}
            </button>
          </div>
          
          <div class="text-center">
            <p class="text-sm text-gray-600">
              还没有账号? 
              <NuxtLink to="/mall/register" class="text-red-600 hover:text-red-800">立即注册</NuxtLink>
            </p>
          </div>
        </form>
        
        <!-- 第三方登录 -->
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-gray-300"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-white text-gray-500">其他登录方式</span>
            </div>
          </div>
          
          <div class="mt-6 flex justify-center space-x-6">
            <button type="button" class="flex flex-col items-center">
              <div class="w-10 h-10 rounded-full bg-green-500 flex align-center items-center justify-center text-white mb-1">
                <i class="iconfont icon-wechat " style="font-size: 24px;"></i>
              </div>
              <span class="text-xs">微信</span>
            </button>
            <button type="button" class="flex flex-col items-center">
              <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white mb-1">
                <i class="iconfont icon-QQ" style="font-size: 24px;"></i>
              </div>
              <span class="text-xs">QQ</span>
            </button>
            <button type="button" class="flex flex-col items-center">
              <div class="w-10 h-10 rounded-full bg-blue-400 flex items-center justify-center text-white mb-1">
                <i class="iconfont icon-alipay" style="font-size: 24px;"></i>
              </div>
              <span class="text-xs">支付宝</span>
            </button>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';
import { navigateTo } from '#app';

definePageMeta({
  layout: 'mall'
});

const router = useRouter();
const route = useRoute();
const userStore = useMallUserStore();

// 在组件挂载后检查是否已经登录
onMounted(async () => {
  try {
    // 如果用户已登录，跳转到首页
    if (userStore.loggedIn) {
      router.push('/mall');
    }
    
    // 检查URL参数，如果有registered=success，显示注册成功提示
    if (route.query.registered === 'success') {
      // 这里可以添加注册成功的提示
      console.log('注册成功，请登录');
    }
  } catch (error) {
    console.error('检查登录状态失败:', error);
  }
});

// 登录表单数据
const loginForm = reactive({
  username: '18593415401',
  password: '123456',
  remember: false
});

// 加载状态
const isLoading = ref(false);
const loginError = ref('');

// 处理登录
const handleLogin = async () => {
  try {
    console.log('开始登录处理...');
    
    // 表单验证
    // const valid = await this.$refs.loginForm.validate();
    // if (!valid) {
    //   console.log('表单验证失败');
    //   return;
    // }
    
    // 检查是否是手机号
    const isPhone = /^1[3-9]\d{9}$/.test(loginForm.username);
    console.log('用户名是否为手机号:', isPhone);
    
    // 登录请求
    console.log('发送登录请求:', {
      username: loginForm.username,
      password: loginForm.password ? '******' : null,
      isPhone
    });
    
    isLoading.value = true;
    loginError.value = '';
    
    const result = await userStore.login(loginForm);
    
    console.log('登录结果:', result);
    
    if (!result.success) {
      // 如果是手机号登录失败，提供更具体的错误信息
      if (isPhone) {
        loginError.value = result.message || '手机号或密码错误，请重试';
      } else {
        loginError.value = result.message || '用户名或密码错误，请重试';
      }
      return;
    }
    
    // 如果选择了"记住我"，可以在localStorage中保存token
    if (loginForm.remember && process.client) {
      localStorage.setItem('mall_user_token', userStore.token);
    } else if (process.client) {
      sessionStorage.setItem('mall_user_token', userStore.token);
    }
    
    // 登录成功后跳转到会员中心首页
    await navigateTo('/mall/user/center');
    // 如果navigateTo不起作用，尝试使用window.location
    if (process.client) {
      window.location.href = '/mall/user/center';
    }
    
  } catch (error) {
    console.error('登录过程中出错:', error);
    loginError.value = '登录失败: ' + (error.message || '未知错误');
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* 登录页面特定样式 */
</style>
