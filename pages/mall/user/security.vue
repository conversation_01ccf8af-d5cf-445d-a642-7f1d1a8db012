<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/user/center" class="hover:text-red-600">会员中心</NuxtLink> &gt; 
      <span>账户安全</span>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载账户安全信息...</p>
      </div>
      
      <!-- 未登录状态提示 -->
      <div v-else-if="!userStore.loggedIn" class="p-8 text-center">
        <div class="mb-6">
          <img src="https://placehold.co/120x120/ffffff/333333?text=未登录" alt="未登录" class="mx-auto mb-4 rounded-full border-4 border-gray-200">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">您尚未登录</h2>
          <p class="text-gray-600 mb-6">登录后可查看和管理您的账户安全信息</p>
          <div class="flex justify-center space-x-4">
            <NuxtLink to="/mall/login" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              立即登录
            </NuxtLink>
            <NuxtLink to="/mall/register" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition">
              注册账号
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- 已登录状态 - 安全中心 -->
      <div v-else class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <h2 class="text-xl font-bold mb-6 pb-2 border-b">账户安全中心</h2>
          
          <!-- 安全评分 -->
          <div class="security-score bg-white p-6 rounded-lg mb-6 shadow-sm border border-gray-100">
            <div class="flex items-center">
              <div class="w-1/4">
                <div class="score-circle-container mx-auto">
                  <svg width="120" height="120" viewBox="0 0 120 120">
                    <!-- 背景圆环 -->
                    <circle 
                      cx="60" 
                      cy="60" 
                      r="54" 
                      fill="none" 
                      stroke="#f1f5f9" 
                      stroke-width="12"
                    />
                    <!-- 进度圆环 -->
                    <circle 
                      cx="60" 
                      cy="60" 
                      r="54" 
                      fill="none" 
                      stroke="#3b82f6" 
                      stroke-width="12"
                      stroke-dasharray="339.3"
                      :stroke-dashoffset="339.3 * (1 - securityScore/100)"
                      transform="rotate(-90 60 60)"
                    />
                  </svg>
                  <!-- 中间的数字 -->
                  <div class="score-text">
                    <span class="score-value">{{ securityScore }}</span>
                  </div>
                </div>
                <div class="text-center mt-2">
                  <div class="font-medium">安全评分</div>
                </div>
              </div>
              <div class="w-3/4 pl-6">
                <div class="text-lg font-medium mb-2">
                  {{ getSecurityLevelText() }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- 安全设置列表 -->
          <div class="security-settings bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <!-- 登录密码 -->
            <div class="security-item border-b py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center">
                  <i class="i-carbon-password text-xl text-blue-600 mr-2"></i>
                  <span class="font-medium">登录密码</span>
                </div>
                <div class="text-sm text-gray-500 mt-1">
                  {{ userInfo.hasPassword ? '已设置，建议定期修改密码以保护账户安全' : '未设置，建议立即设置密码' }}
                </div>
              </div>
              <a-button type="primary" @click="showPasswordModal = true">
                {{ userInfo.hasPassword ? '修改' : '设置' }}
              </a-button>
            </div>
            
            <!-- 手机绑定 -->
            <div class="security-item border-b py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center">
                  <i class="i-carbon-phone text-xl text-blue-600 mr-2"></i>
                  <span class="font-medium">手机绑定</span>
                </div>
                <div class="text-sm text-gray-500 mt-1">
                  {{ userInfo.phone ? `已绑定手机 ${maskPhone(userInfo.phone)}` : '未绑定，绑定后可通过手机找回密码' }}
                </div>
              </div>
              <a-button type="primary" @click="showPhoneModal = true">
                {{ userInfo.phone ? '修改' : '绑定' }}
              </a-button>
            </div>
            
            <!-- 邮箱绑定 -->
            <div class="security-item border-b py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center">
                  <i class="i-carbon-email text-xl text-blue-600 mr-2"></i>
                  <span class="font-medium">邮箱绑定</span>
                </div>
                <div class="text-sm text-gray-500 mt-1">
                  {{ userInfo.email ? `已绑定邮箱 ${maskEmail(userInfo.email)}` : '未绑定，绑定后可通过邮箱找回密码' }}
                </div>
              </div>
              <a-button type="primary" @click="showEmailModal = true">
                {{ userInfo.email ? '修改' : '绑定' }}
              </a-button>
            </div>
            
            <!-- 微信绑定 -->
            <div class="security-item border-b py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center">
                  <i class="i-carbon-logo-wechat text-xl text-green-600 mr-2"></i>
                  <span class="font-medium">微信绑定</span>
                </div>
                <div class="text-sm text-gray-500 mt-1">
                  {{ userInfo.wechatOpenid ? '已绑定微信账号，可使用微信扫码快速登录' : '未绑定，绑定后可使用微信扫码快速登录' }}
                </div>
              </div>
              <a-button type="primary" @click="showWechatBindModal = true">
                {{ userInfo.wechatOpenid ? '重新绑定' : '立即绑定' }}
              </a-button>
            </div>
            
            <!-- 实名认证 -->
            <div class="security-item border-b py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center">
                  <i class="i-carbon-id-management text-xl text-blue-600 mr-2"></i>
                  <span class="font-medium">实名认证</span>
                </div>
                <div class="text-sm mt-1" :class="{
                  'text-gray-500': userInfo.isVerified === 0 || userInfo.isVerified === 1,
                  'text-green-600': userInfo.isVerified === 2,
                  'text-red-600': userInfo.isVerified === 3
                }">
                  <template v-if="userInfo.isVerified === 0">
                    <span>未认证，认证后享受更多会员特权</span>
                  </template>
                  <template v-else-if="userInfo.isVerified === 1">
                    <span>实名认证信息已提交，等待审核</span>
                  </template>
                  <template v-else-if="userInfo.isVerified === 2">
                    <span>已完成实名认证 <span v-if="userInfo.verifiedTime">，认证时间：{{ userInfo.verifiedTime || '未知' }}</span> </span>
                  </template>
                  <template v-else-if="userInfo.isVerified === 3">
                    <span>实名认证失败，原因：{{ userInfo.verifyFailReason || '认证信息不符合要求' }}</span>
                  </template>
                </div>
              </div>
              <a-button 
                @click="showVerifyModal = true" 
                :type="getVerifyButtonType(userInfo.isVerified)" 
                :status="getVerifyButtonStatus(userInfo.isVerified)"
              >
                <template v-if="userInfo.isVerified === 0">去认证</template>
                <template v-else-if="userInfo.isVerified === 1">查看</template>
                <template v-else-if="userInfo.isVerified === 2">查看</template>
                <template v-else-if="userInfo.isVerified === 3">重新认证</template>
              </a-button>
            </div>
            
            <!-- 安全问题 -->
            <div class="security-item py-4 flex items-center justify-between">
              <div>
                <div class="flex items-center">
                  <i class="i-carbon-help text-xl text-blue-600 mr-2"></i>
                  <h3 class="font-medium">安全问题</h3>
                </div>
                <p class="text-gray-500 text-sm mt-1">
                  {{ userInfo.hasSecurityQuestion ? '已设置安全问题，可用于找回密码' : '未设置安全问题，建议设置以提高账户安全性' }}
                </p>
              </div>
              <a-button type="primary" @click="showSecurityQuestionModal = true">
                {{ userInfo.hasSecurityQuestion ? '修改' : '设置' }}
              </a-button>
            </div>
          </div>
          
          <!-- 安全提示 -->
          <div class="security-tips mt-6 bg-white p-6 rounded-lg shadow-sm border border-gray-100">
            <h3 class="font-medium text-lg mb-3 flex items-center">
              <i class="i-carbon-information text-xl text-blue-600 mr-2"></i>
              安全提示
            </h3>
            <ul class="text-gray-600 text-sm">
              <li class="mb-2 flex items-start">
                <i class="i-carbon-checkmark text-green-500 mr-2 mt-0.5"></i>
                定期修改密码，不要使用与其他网站相同的密码
              </li>
              <li class="mb-2 flex items-start">
                <i class="i-carbon-checkmark text-green-500 mr-2 mt-0.5"></i>
                设置复杂密码，包含字母、数字和特殊符号
              </li>
              <li class="mb-2 flex items-start">
                <i class="i-carbon-checkmark text-green-500 mr-2 mt-0.5"></i>
                绑定手机和邮箱，方便找回密码和接收安全通知
              </li>
              <li class="flex items-start">
                <i class="i-carbon-checkmark text-green-500 mr-2 mt-0.5"></i>
                不要在不信任的设备上登录账户
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 微信绑定模态框 -->
    <WechatBindModal 
      v-model:visible="showWechatBindModal" 
      :title="userInfo.wechatOpenid ? '重新绑定微信账号' : '绑定微信账号'" 
      @success="handleWechatBindSuccess" 
    />
    
    <!-- 修改密码模态框 -->
    <a-modal v-model:visible="showPasswordModal" title="修改登录密码" @cancel="showPasswordModal = false" width="500px">
      <div class="password-form">
        <a-form :model="passwordForm" layout="vertical">
          <a-form-item field="oldPassword" label="原密码">
            <a-input v-model="passwordForm.oldPassword" placeholder="请输入原密码" type="password" />
          </a-form-item>
          <a-form-item field="newPassword" label="新密码">
            <a-input v-model="passwordForm.newPassword" placeholder="请输入新密码" type="password" />
            <div class="text-xs text-gray-500 mt-1">密码长度不少于6位，建议使用字母、数字和符号的组合</div>
          </a-form-item>
          <a-form-item field="confirmPassword" label="确认新密码">
            <a-input v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码" type="password" />
          </a-form-item>
          <a-form-item v-show="false" field="verificationCode" label="手机验证码" v-if="userInfo.phone">
            <div class="flex">
              <a-input v-model="passwordForm.verificationCode" placeholder="请输入验证码" class="flex-1 mr-2" />
              <a-button @click="sendVerificationCode('password')" :disabled="passwordCountdown > 0">
                {{ passwordCountdown > 0 ? `${passwordCountdown}秒后重新获取` : '获取验证码' }}
              </a-button>
            </div>
            <div class="text-xs text-gray-500 mt-1">验证码将发送至 {{ maskPhone(userInfo.phone) }}</div>
          </a-form-item>
        </a-form>
      </div>
      <template #footer>
        <a-button @click="showPasswordModal = false">取消</a-button>
        <a-button type="primary" @click="handlePasswordChange">确认修改</a-button>
      </template>
    </a-modal>

    <!-- 绑定手机模态框 -->
    <a-modal v-model:visible="showPhoneModal" :title="userInfo.phone ? '修改手机绑定' : '绑定手机'" @cancel="showPhoneModal = false" width="500px">
      <div class="phone-form">
        <a-form :model="phoneForm" layout="vertical">
          <a-form-item field="phone" label="手机号">
            <a-input v-model="phoneForm.phone" placeholder="请输入手机号" />
          </a-form-item>
          <a-form-item field="verificationCode" label="验证码">
            <div class="flex">
              <a-input v-model="phoneForm.verificationCode" placeholder="请输入验证码" class="flex-1 mr-2" />
              <a-button @click="sendVerificationCode('phone')" :disabled="phoneCountdown > 0">
                {{ phoneCountdown > 0 ? `${phoneCountdown}秒后重新获取` : '获取验证码' }}
              </a-button>
            </div>
          </a-form-item>
        </a-form>
      </div>
      <template #footer>
        <a-button @click="showPhoneModal = false">取消</a-button>
        <a-button type="primary" @click="handlePhoneBinding">确认</a-button>
      </template>
    </a-modal>

    <!-- 绑定邮箱模态框 -->
    <a-modal v-model:visible="showEmailModal" :title="userInfo.email ? '修改邮箱绑定' : '绑定邮箱'" @cancel="showEmailModal = false" width="500px">
      <div class="email-form">
        <a-form :model="emailForm" layout="vertical">
          <a-form-item field="email" label="邮箱">
            <a-input v-model="emailForm.email" placeholder="请输入邮箱" />
          </a-form-item>
          <a-form-item field="verificationCode" label="验证码">
            <div class="flex">
              <a-input v-model="emailForm.verificationCode" placeholder="请输入验证码" class="flex-1 mr-2" />
              <a-button @click="sendVerificationCode('email')" :disabled="emailCountdown > 0">
                {{ emailCountdown > 0 ? `${emailCountdown}秒后重新获取` : '获取验证码' }}
              </a-button>
            </div>
          </a-form-item>
        </a-form>
      </div>
      <template #footer>
        <a-button @click="showEmailModal = false">取消</a-button>
        <a-button type="primary" @click="handleEmailBinding">确认</a-button>
      </template>
    </a-modal>

    <!-- 实名认证模态框 -->
    <a-modal v-model:visible="showVerifyModal" :title="getVerifyModalTitle(userInfo.isVerified)" @cancel="showVerifyModal = false" width="600px">
      <div class="verify-form">
        <a-form :model="verifyForm" layout="vertical">
          <!-- 未认证状态提示 -->
          <div v-if="userInfo.isVerified === 0" class="mb-4 p-3 bg-yellow-50 rounded">
            <div class="flex items-center">
              <i class="i-carbon-information text-yellow-600 text-xl mr-2"></i>
              <span class="font-medium text-yellow-600">你还未进行实名认证，请完成认证。</span>
            </div>
            <div class="mt-1 ml-7 text-sm text-gray-600">
              实名认证后可享受更多会员特权和安全保障。
            </div>
          </div>
          
          <!-- 审核中或已认证状态提示 -->
          <div v-if="userInfo.isVerified === 1 || userInfo.isVerified === 2" class="mb-4 p-3 rounded" :class="{'bg-blue-50': userInfo.isVerified === 1, 'bg-green-50': userInfo.isVerified === 2}">
            <div class="flex items-center">
              <i :class="{'i-carbon-time text-blue-600': userInfo.isVerified === 1, 'i-carbon-checkmark-filled text-green-600': userInfo.isVerified === 2}" class="text-xl mr-2"></i>
              <span class="font-medium" :class="{'text-blue-600': userInfo.isVerified === 1, 'text-green-600': userInfo.isVerified === 2}">
                {{ userInfo.isVerified === 1 ? '实名认证正在审核中，请耐心等待' : '您已通过实名认证' }}
              </span>
            </div>
            <div v-if="userInfo.isVerified === 2 && userInfo.verifiedTime" class="mt-1 ml-7 text-sm text-gray-600">
              认证时间：{{ userInfo.verifiedTime }}
            </div>
          </div>
          
          <!-- 认证失败提示 -->
          <div v-if="userInfo.isVerified === 3" class="mb-4 p-3 bg-red-50 rounded">
            <div class="flex items-center">
              <i class="i-carbon-warning-filled text-red-600 text-xl mr-2"></i>
              <span class="font-medium text-red-600">实名认证失败</span>
            </div>
            <div class="mt-1 ml-7 text-sm text-gray-600">
              失败原因：{{ userInfo.verifyFailReason || '认证信息不符合要求' }}
            </div>
          </div>
          
          <a-form-item field="realName" label="真实姓名">
            <a-input 
              v-model="verifyForm.realName" 
              placeholder="请输入真实姓名" 
              :readonly="userInfo.isVerified === 1 || userInfo.isVerified === 2"
            />
          </a-form-item>
          <a-form-item field="idCardNumber" label="身份证号">
            <a-input 
              v-model="verifyForm.idCardNumber" 
              placeholder="请输入身份证号" 
              :readonly="userInfo.isVerified === 1 || userInfo.isVerified === 2"
            />
          </a-form-item>
          
          <a-form-item field="authPhone" label="认证手机号">
            <a-input 
              v-model="verifyForm.authPhone" 
              placeholder="请输入认证手机号" 
              :readonly="userInfo.isVerified === 1 || userInfo.isVerified === 2"
            />
            <div class="text-xs text-gray-500 mt-1">请输入您的手机号，用于实名认证信息确认</div>
          </a-form-item>
          
          <a-form-item field="bankCardNo" label="银行卡号">
            <a-input 
              v-model="verifyForm.bankCardNo" 
              placeholder="请输入银行卡号" 
              :readonly="userInfo.isVerified === 1 || userInfo.isVerified === 2"
            />
            <div class="text-xs text-gray-500 mt-1">请输入您的银行卡号，用于提现和账户验证</div>
          </a-form-item>
          <a-form-item field="idCardFront" label="身份证正面照片">
            <div class="relative w-64 h-40 border border-gray-300 rounded-md overflow-hidden mb-2">
              <img 
                v-if="verifyForm.idCardFront" 
                :src="verifyForm.idCardFront" 
                alt="身份证正面" 
                class="w-full h-full object-cover"
              />
              <div 
                v-else 
                class="w-full h-full flex flex-col items-center justify-center bg-gray-50"
              >
                <i class="i-carbon-id-management text-3xl text-gray-400"></i>
                <div class="mt-2 text-sm text-gray-500">点击上传人像面</div>
              </div>
              <div 
                v-if="userInfo.isVerified !== 1 && userInfo.isVerified !== 2"
                class="absolute inset-0 flex items-center justify-center hover:bg-black/30 transition-all cursor-pointer"
                @click="openIdCardFrontUpload"
              >
                <div v-if="!verifyForm.idCardFront" class="hidden hover:flex items-center justify-center">
                  <i class="i-carbon-upload text-white text-xl"></i>
                </div>
                <div v-else class="hidden hover:flex items-center justify-center bg-black/50 w-full h-full">
                  <button type="button" class="text-white bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded-md text-sm">
                    更换照片
                  </button>
                </div>
              </div>
            </div>
            <input 
              type="file" 
              ref="idCardFrontInput" 
              accept="image/jpeg,image/png,image/gif,image/webp" 
              class="hidden" 
              @change="handleIdCardFrontChange"
            >
            <div class="text-xs text-gray-500">
              {{ userInfo.isVerified === 1 || userInfo.isVerified === 2 ? '身份证人像面照片' : '请上传清晰的身份证人像面照片' }}
            </div>
          </a-form-item>
          <a-form-item field="idCardBack" label="身份证反面照片">
            <div class="relative w-64 h-40 border border-gray-300 rounded-md overflow-hidden mb-2">
              <img 
                v-if="verifyForm.idCardBack" 
                :src="verifyForm.idCardBack" 
                alt="身份证反面" 
                class="w-full h-full object-cover"
              />
              <div 
                v-else 
                class="w-full h-full flex flex-col items-center justify-center bg-gray-50"
              >
                <i class="i-carbon-id-management text-3xl text-gray-400"></i>
                <div class="mt-2 text-sm text-gray-500">点击上传国徽面</div>
              </div>
              <div 
                v-if="userInfo.isVerified !== 1 && userInfo.isVerified !== 2"
                class="absolute inset-0 flex items-center justify-center hover:bg-black/30 transition-all cursor-pointer"
                @click="openIdCardBackUpload"
              >
                <div v-if="!verifyForm.idCardBack" class="hidden hover:flex items-center justify-center">
                  <i class="i-carbon-upload text-white text-xl"></i>
                </div>
                <div v-else class="hidden hover:flex items-center justify-center bg-black/50 w-full h-full">
                  <button type="button" class="text-white bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded-md text-sm">
                    更换照片
                  </button>
                </div>
              </div>
            </div>
            <input 
              type="file" 
              ref="idCardBackInput" 
              accept="image/jpeg,image/png,image/gif,image/webp" 
              class="hidden" 
              @change="handleIdCardBackChange"
            >
            <div class="text-xs text-gray-500">
              {{ userInfo.isVerified === 1 || userInfo.isVerified === 2 ? '身份证国徽面照片' : '请上传清晰的身份证国徽面照片' }}
            </div>
          </a-form-item>
          <div class="text-gray-500 text-sm mt-4 bg-gray-50 p-3 rounded">
            <div class="flex items-center">
              <i class="i-carbon-information text-blue-600 mr-2"></i>
              <span>温馨提示：</span>
            </div>
            <ul class="list-disc list-inside mt-1 ml-6">
              <li>实名认证信息将用于订单、发票等信息的确认</li>
              <li>我们将严格保护您的个人信息安全</li>
              <li>认证成功后，身份信息将无法修改</li>
            </ul>
          </div>
        </a-form>
      </div>
      <template #footer>
        <a-button @click="showVerifyModal = false">关闭</a-button>
        <a-button 
          v-if="userInfo.isVerified === 0 || userInfo.isVerified === 3" 
          type="primary" 
          @click="handleVerification"
        >提交认证</a-button>
      </template>
    </a-modal>

    <!-- 安全问题模态框 -->
    <a-modal v-model:visible="showSecurityQuestionModal" title="设置安全问题" @cancel="showSecurityQuestionModal = false" width="600px">
      <div class="security-question-form">
        <a-form :model="securityQuestionForm" layout="vertical">
          <div class="text-gray-500 text-sm mb-4 bg-gray-50 p-3 rounded">
            安全问题可以帮助您在忘记密码时，通过回答问题找回账户。请设置三个不同的安全问题及答案。
          </div>
          
          <a-form-item field="question1" label="安全问题1">
            <a-select v-model="securityQuestionForm.question1" placeholder="请选择安全问题">
              <a-option value="您的出生地是？">您的出生地是？</a-option>
              <a-option value="您母亲的姓名是？">您母亲的姓名是？</a-option>
              <a-option value="您父亲的姓名是？">您父亲的姓名是？</a-option>
              <a-option value="您就读的小学名称是？">您就读的小学名称是？</a-option>
              <a-option value="您的宠物名字是？">您的宠物名字是？</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="answer1" label="答案1">
            <a-input v-model="securityQuestionForm.answer1" placeholder="请输入答案" />
          </a-form-item>
          
          <a-form-item field="question2" label="安全问题2">
            <a-select v-model="securityQuestionForm.question2" placeholder="请选择安全问题">
              <a-option value="您最喜欢的颜色是？">您最喜欢的颜色是？</a-option>
              <a-option value="您最喜欢的食物是？">您最喜欢的食物是？</a-option>
              <a-option value="您最喜欢的电影是？">您最喜欢的电影是？</a-option>
              <a-option value="您最好的朋友名字是？">您最好的朋友名字是？</a-option>
              <a-option value="您的第一辆车的品牌是？">您的第一辆车的品牌是？</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="answer2" label="答案2">
            <a-input v-model="securityQuestionForm.answer2" placeholder="请输入答案" />
          </a-form-item>
          
          <a-form-item field="question3" label="安全问题3">
            <a-select v-model="securityQuestionForm.question3" placeholder="请选择安全问题">
              <a-option value="您的理想职业是？">您的理想职业是？</a-option>
              <a-option value="您最向往的旅行地点是？">您最向往的旅行地点是？</a-option>
              <a-option value="您的座右铭是？">您的座右铭是？</a-option>
              <a-option value="您最喜欢的运动是？">您最喜欢的运动是？</a-option>
              <a-option value="您最喜欢的歌手是？">您最喜欢的歌手是？</a-option>
            </a-select>
          </a-form-item>
          <a-form-item field="answer3" label="答案3">
            <a-input v-model="securityQuestionForm.answer3" placeholder="请输入答案" />
          </a-form-item>
        </a-form>
      </div>
      <template #footer>
        <a-button @click="showSecurityQuestionModal = false">取消</a-button>
        <a-button type="primary" @click="handleSecurityQuestionSetting">确认设置</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'mall'
});

import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue';
import { Message } from '@arco-design/web-vue';
import userApi from '@/api/mall/user';
import mallApi from '@/api/mall';
import { apiBaseUrl } from '@/api/config';
import UserSideNav from '~/components/mall/user/UserSideNav.vue'; // 导入UserSideNav组件
import { useMallUserStore } from '~/store/mall/user'; // 导入useMallUserStore
import WechatBindModal from '@/components/mall/user/WechatBindModal.vue'; // 导入WechatBindModal组件

// 用户状态管理
const userStore = useMallUserStore();
const userInfoData = ref({}); // 重命名为userInfoData
const isLoading = ref(true);
const isLoadingAuthInfo = ref(false); // 加载实名认证信息状态
const isVerifying = ref(false); // 实名认证提交状态
const securityScore = ref(0);

// 验证码倒计时状态
const phoneCountdown = ref(0); // 手机验证码倒计时
const emailCountdown = ref(0); // 邮箱验证码倒计时
const passwordCountdown = ref(0); // 密码验证码倒计时

// 倒计时器实例
let countdownTimer = null;

// 模态框显示状态
const showPasswordModal = ref(false);
const showPhoneModal = ref(false);
const showEmailModal = ref(false);
const showVerifyModal = ref(false);
const showWechatBindModal = ref(false);
const showSecurityQuestionModal = ref(false);

// 监听安全问题模态框打开状态
watch(showSecurityQuestionModal, async (newVal) => {
  if (newVal) {
    // 当模态框打开时，获取最新的安全问题
    await fetchSecurityQuestions();
  }
});

// 表单数据
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
  verificationCode: ''
});

const phoneForm = ref({
  phone: '',
  verificationCode: ''
});

const emailForm = ref({
  email: '',
  verificationCode: ''
});

const verifyForm = ref({
  realName: '',
  idCardNumber: '',
  idCardFront: null,
  idCardBack: null,
  authPhone: '',
  bankCardNo: ''
});

const securityQuestionForm = ref({
  question1: '',
  answer1: '',
  question2: '',
  answer2: '',
  question3: '',
  answer3: ''
});

// 用户信息
const userInfo = computed(() => {
  if (!userStore.user) return {};
  
  return {
    hasPassword: userStore.user.hasPassword !== false,
    phone: userStore.user.phone || '',
    email: userStore.user.email || '',
    isVerified: userStore.user.isVerified || 0,
    hasSecurityQuestion: userStore.user.hasSecurityQuestion || false,
    verifyFailReason: userStore.user.verifyFailReason || '',
    wechatOpenid: userStore.user.wechatOpenid || ''
  };
});

// 计算安全评分
const calculateSecurityScore = () => {
  let score = 20; // 基础分
  
  // 密码设置 默认一定会存在密码
  if (userInfo.value.hasPassword) score += 20;
  
  // 手机绑定
  if (userInfo.value.phone) score += 20;
  
  // 邮箱绑定
  if (userInfo.value.email) score += 15;
  
  // 实名认证
  if (userInfo.value.isVerified) score += 15;
  
  // 安全问题
  if (userInfo.value.hasSecurityQuestion) score += 10;
  
  // 微信绑定
  if (userInfo.value.wechatOpenid) score += 15;
  
  securityScore.value = score;
};

// 获取安全等级文本
const getSecurityLevelText = () => {
  if (securityScore.value >= 90) return '您的账户安全级别很高，请继续保持';
  if (securityScore.value >= 70) return '您的账户安全级别良好，可以进一步提升';
  if (securityScore.value >= 50) return '您的账户安全级别中等，建议完善安全设置';
  return '您的账户安全级别较低，请尽快完善安全设置';
};

// 隐藏手机号中间四位
const maskPhone = (phone) => {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// 隐藏邮箱用户名部分
const maskEmail = (email) => {
  if (!email) return '';
  const [username, domain] = email.split('@');
  if (username.length <= 2) {
    return `${username}***@${domain}`;
  }
  return `${username.substring(0, 2)}***@${domain}`;
};

// 获取认证头信息
const getAuthHeaders = () => {
  // 确保在客户端环境中执行
  if (typeof window !== 'undefined') {
    const token = localStorage.getItem('mall_user_token') || localStorage.getItem('token_master') || '';
    return {
      'Authorization': token ? `Bearer ${token}` : ''
    };
  }
  return {};
};

// 身份证照片上传相关引用
const idCardFrontInput = ref(null);
const idCardBackInput = ref(null);
const isIdCardFrontUploading = ref(false);
const isIdCardBackUploading = ref(false);

// 打开身份证正面照片上传
const openIdCardFrontUpload = () => {
  idCardFrontInput.value.click();
};

// 打开身份证反面照片上传
const openIdCardBackUpload = () => {
  idCardBackInput.value.click();
};

// 处理身份证正面照片变更
const handleIdCardFrontChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // 检查文件类型
  if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
    Message.error('只支持 JPG、PNG、GIF、WEBP 格式的图片');
    return;
  }
  
  // 检查文件大小
  if (file.size > 2 * 1024 * 1024) {
    Message.error('图片大小不能超过 2MB');
    return;
  }
  
  // 上传身份证正面照片
  isIdCardFrontUploading.value = true;
  
  try {
    // 显示上传中提示
    const loadingInstance = Message.loading('正在上传身份证正面照片...');
    
    // 上传文件
    console.log('开始上传身份证正面照片...');
    const response = await userApi.profile.uploadAvatar(file);
    console.log('上传身份证正面照片响应:', response);
    
    // 关闭加载提示
    loadingInstance.close();
    
    // 判断上传是否成功
    if (response.code !== 0 && response.code !== 200) {
      // 上传失败
      console.error('上传身份证正面照片失败，状态码:', response.code, '消息:', response.message);
      Message.error('上传身份证正面照片失败: ' + (response.message || '未知错误'));
      return;
    }
    
    // 获取文件URL
    if (!response.data || !response.data.fileUrl) {
      console.error('响应数据中没有fileUrl字段');
      Message.error('获取上传文件地址失败');
      return;
    }
    
    const fileUrl = response.data.fileUrl;
    console.log('获取到的身份证正面照片URL:', fileUrl);
    
    // 更新身份证正面照片地址
    verifyForm.value.idCardFront = fileUrl;
    
    Message.success('身份证正面照片上传成功');
  } catch (error) {
    console.error('上传身份证正面照片失败:', error);
    Message.error('上传身份证正面照片失败，请稍后再试');
  } finally {
    isIdCardFrontUploading.value = false;
    // 清空文件输入框，以便可以重新选择同一文件
    event.target.value = '';
  }
};

// 处理身份证反面照片变更
const handleIdCardBackChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // 检查文件类型
  if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
    Message.error('只支持 JPG、PNG、GIF、WEBP 格式的图片');
    return;
  }
  
  // 检查文件大小
  if (file.size > 2 * 1024 * 1024) {
    Message.error('图片大小不能超过 2MB');
    return;
  }
  
  // 上传身份证反面照片
  isIdCardBackUploading.value = true;
  
  try {
    // 显示上传中提示
    const loadingInstance = Message.loading('正在上传身份证反面照片...');
    
    // 上传文件
    console.log('开始上传身份证反面照片...');
    const response = await userApi.profile.uploadAvatar(file);
    console.log('上传身份证反面照片响应:', response);
    
    // 关闭加载提示
    loadingInstance.close();
    
    // 判断上传是否成功
    if (response.code !== 0 && response.code !== 200) {
      // 上传失败
      console.error('上传身份证反面照片失败，状态码:', response.code, '消息:', response.message);
      Message.error('上传身份证反面照片失败: ' + (response.message || '未知错误'));
      return;
    }
    
    // 获取文件URL
    if (!response.data || !response.data.fileUrl) {
      console.error('响应数据中没有fileUrl字段');
      Message.error('获取上传文件地址失败');
      return;
    }
    
    const fileUrl = response.data.fileUrl;
    console.log('获取到的身份证反面照片URL:', fileUrl);
    
    // 更新身份证反面照片地址
    verifyForm.value.idCardBack = fileUrl;
    
    Message.success('身份证反面照片上传成功');
  } catch (error) {
    console.error('上传身份证反面照片失败:', error);
    Message.error('上传身份证反面照片失败，请稍后再试');
  } finally {
    isIdCardBackUploading.value = false;
    // 清空文件输入框，以便可以重新选择同一文件
    event.target.value = '';
  }
};

// 加载用户安全信息
const loadSecurityInfo = async () => {
  isLoading.value = true;
  
  try {
    // 如果用户未登录，尝试恢复会话
    if (!userStore.loggedIn) {
      await userStore.restoreSession();
    }
    
    // 如果用户已登录，获取最新的用户信息
    if (userStore.loggedIn) {
      // 获取最新的用户信息，包括微信绑定状态
      try {
        const userInfoResponse = await userApi.profile.getUserInfo();
        if (userInfoResponse && userInfoResponse.code === 200 && userInfoResponse.data) {
          // 更新用户信息
          userStore.updateUser(userInfoResponse.data);
          console.log('获取到最新用户信息:', userInfoResponse.data);
        }
      } catch (userInfoError) {
        console.error('获取用户信息失败:', userInfoError);
      }
      
      // 并行请求，提高加载速度
      await Promise.all([
        fetchRealnameAuthInfo(),
        fetchSecurityQuestions()
      ]);
    }
    
    // 计算安全评分
    calculateSecurityScore();
    
    return true;
  } catch (error) {
    console.error('加载安全信息失败:', error);
    return false;
  } finally {
    isLoading.value = false;
  }
};

// 获取实名认证按钮类型
const getVerifyButtonType = (status) => {
  switch (status) {
    case 0: // 未认证
      return 'primary';
    case 1: // 等待认证
      return 'outline';
    case 2: // 认证通过
      return 'outline';
    case 3: // 认证失败
      return 'primary';
    default:
      return 'primary';
  }
};

// 获取实名认证按钮状态
const getVerifyButtonStatus = (status) => {
  switch (status) {
    case 0: // 未认证
      return '';
    case 1: // 等待认证
      return 'warning';
    case 2: // 认证通过
      return 'success';
    case 3: // 认证失败
      return 'danger';
    default:
      return '';
  }
};

// 获取实名认证模态框标题
const getVerifyModalTitle = (status) => {
  switch (status) {
    case 0: // 未认证
      return '实名认证';
    case 1: // 等待认证
      return '实名认证信息（审核中）';
    case 2: // 认证通过
      return '实名认证信息（已认证）';
    case 3: // 认证失败
      return '实名认证（需重新提交）';
    default:
      return '实名认证';
  }
};

// 获取用户实名认证信息
const fetchRealnameAuthInfo = async () => {
  try {
    isLoadingAuthInfo.value = true;
    const response = await userApi.profile.getRealnameAuth();
    
    if (response && response.code === 200) {
      // 处理嵌套的数据结构，正确获取authInfo
      const authInfo = response.data && response.data.authInfo;
      
      if (authInfo) {
        // 设置实名认证状态
        const authStatus = authInfo.auth_status || 0;
        console.log('authInfo', authInfo);
        // 更新 userStore 中的认证状态，这样 userInfo 计算属性也会更新
        userStore.user = {
          ...userStore.user,
          isVerified: authStatus,
          verifyFailReason: authInfo.audit_remark
        };
        console.log('userStore.user', userStore.user);
        // 如果有实名认证信息，填充到表单
        if (authInfo.real_name) {
          // 预填充表单
          verifyForm.value = {
            realName: authInfo.real_name || '',
            idCardNumber: authInfo.identity_no || '',
            idCardFront: authInfo.id_card_front_url || null,
            idCardBack: authInfo.id_card_back_url || null,
            authPhone: authInfo.auth_phone || '',
            bankCardNo: authInfo.bank_card_no || ''
          };
        }
      } else {
        // 用户未进行实名认证，设置为未认证状态(0)
        userStore.user = {
          ...userStore.user,
          isVerified: 0
        };
      }
    }
  } catch (error) {
    console.error('获取实名认证信息失败:', error);
  } finally {
    isLoadingAuthInfo.value = false;
  }
};

// 在组件挂载后加载数据
onMounted(async () => {
  await loadSecurityInfo();
});

// 修改密码
const handlePasswordChange = async () => {
  // 表单验证
  if (!passwordForm.value.oldPassword) {
    Message.error('请输入原密码');
    return;
  }
  
  if (!passwordForm.value.newPassword) {
    Message.error('请输入新密码');
    return;
  }
  
  if (passwordForm.value.newPassword.length < 6) {
    Message.error('新密码长度不能少于6位');
    return;
  }
  
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    Message.error('两次输入的密码不一致');
    return;
  }
  
  try {
    // 显示加载状态
    isLoading.value = true;
    
    // 调用修改密码接口
    const result = await mallApi.user.profile.changePassword({
      oldPassword: passwordForm.value.oldPassword,
      newPassword: passwordForm.value.newPassword
    });
    
    if (result.code === 200) {      
      Message.success(result.message || '密码修改成功，请重新登录');
      showPasswordModal.value = false;
      
      // 重置表单
      passwordForm.value = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
        verificationCode: ''
      };
      
      // 重新计算安全评分
      calculateSecurityScore();
      
      // 如果服务器要求重新登录，则执行退出操作
      if (result.message && result.message.includes('密码修改成功，请重新登录')) {
        // 等待一秒后执行退出操作，让用户有时间看到成功消息
        setTimeout(async () => {
          await userStore.logout();
          navigateTo('/mall/login');
        }, 1500);
      }
    } else {
      Message.error(result.message || '修改密码失败');
    }
  } catch (error) {
    console.error('修改密码失败:', error);
    Message.error('修改密码失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};

// 开始倒计时
const startCountdown = (type) => {
  // 清除之前的倒计时器
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
  
  // 设置倒计时初始值
  if (type === 'phone') {
    phoneCountdown.value = 60;
  } else if (type === 'email') {
    emailCountdown.value = 60;
  } else if (type === 'password') {
    passwordCountdown.value = 60;
  }
  
  // 启动倒计时器
  countdownTimer = setInterval(() => {
    if (type === 'phone' && phoneCountdown.value > 0) {
      phoneCountdown.value--;
      if (phoneCountdown.value === 0) {
        clearInterval(countdownTimer);
      }
    } else if (type === 'email' && emailCountdown.value > 0) {
      emailCountdown.value--;
      if (emailCountdown.value === 0) {
        clearInterval(countdownTimer);
      }
    } else if (type === 'password' && passwordCountdown.value > 0) {
      passwordCountdown.value--;
      if (passwordCountdown.value === 0) {
        clearInterval(countdownTimer);
      }
    }
  }, 1000);
};

// 发送验证码
const sendVerificationCode = async (type) => {
  // 检查是否在倒计时中
  if ((type === 'phone' && phoneCountdown.value > 0) || 
      (type === 'email' && emailCountdown.value > 0) || 
      (type === 'password' && passwordCountdown.value > 0)) {
    return; // 如果在倒计时中，不允许再次发送
  }
  
  try {
    let target = '';
    
    if (type === 'phone') {
      if (!phoneForm.value.phone) {
        Message.error('请输入手机号');
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(phoneForm.value.phone)) {
        Message.error('请输入正确的手机号');
        return;
      }
      target = phoneForm.value.phone;
      
      // 调用手机验证码接口
      try {
        console.log('请求手机验证码，手机号:', target);
        const response = await userApi.profile.getPhoneCaptcha(target);
        if (response.code !== 200) {
          Message.error(response.message);
          return;
        }
        console.log('手机验证码接口返回:', response);
        Message.success(`验证码已发送至手机`);
        // 开始倒计时
        startCountdown('phone');
        return;
      } catch (apiError) {
        console.error('请求手机验证码失败:', apiError);
        const errorMsg = apiError.response?.data?.message || '发送验证码失败，请稍后再试';
        Message.error(errorMsg);
        return;
      }
    } else if (type === 'email') {
      if (!emailForm.value.email) {
        Message.error('请输入邮箱');
        return;
      }
      target = emailForm.value.email;
      if (!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(target)) {
        Message.error('请输入正确的邮箱');
        return;
      }
      // 调用邮箱验证码接口
      try {
        console.log('请求邮箱验证码，邮箱:', target);
        const response = await userApi.profile.getEmailCaptcha(target);
        if (response.code !== 200) {
          Message.error(response.message);
          return;
        }
        console.log('邮箱验证码接口返回:', response);
        Message.success(`验证码已发送至邮箱`);
        // 开始倒计时
        startCountdown('email');
        return;
      } catch (apiError) {
        console.error('请求邮箱验证码失败:', apiError);
        const errorMsg = apiError.response?.data?.message || '发送验证码失败，请稍后再试';
        Message.error(errorMsg);
        return;
      }
    } else if (type === 'password') {
      target = userInfo.phone;
    }
    
    // 其他类型仍使用模拟发送验证码
    console.log(`模拟发送验证码到 ${type} 类型的目标: ${target}`);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    Message.success(`验证码已发送至${type === 'email' ? '邮箱' : '手机'}`);
    
    // 开始倒计时
    startCountdown(type);
  } catch (error) {
    console.error('发送验证码失败:', error);
    Message.error('发送验证码失败，请稍后再试');
  }
};

// 清除倒计时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});

// 绑定或修改手机
const handlePhoneBinding = async () => {
  // 表单验证
  if (!phoneForm.value.phone) {
    Message.error('请输入手机号');
    return;
  }
  
  if (!/^1[3-9]\d{9}$/.test(phoneForm.value.phone)) {
    Message.error('请输入正确的手机号');
    return;
  }
  
  if (!phoneForm.value.verificationCode) {
    Message.error('请输入验证码');
    return;
  }
  
  try {
    // 显示加载中状态
    const loadingInstance = Message.loading('正在提交修改请求...');
    
    // 调用修改手机号码接口
    const response = await userApi.profile.updatePhone(
      phoneForm.value.phone, 
      phoneForm.value.verificationCode
    );
    
    console.log('修改手机号码响应:', response);
    
    // 检查响应中是否包含用户信息
    if (response && response.code === 200 && response.data && response.data.user) {
      // 使用响应中的完整用户信息更新本地存储
      try {
        // 将完整的用户信息保存到本地存储
        localStorage.setItem('mall_user', JSON.stringify(response.data.user));
        console.log('使用返回的完整用户信息更新本地存储');
        
        // 更新store中的用户信息
        userStore.user = response.data.user;
      } catch (storageError) {
        console.error('更新本地存储失败:', storageError);
      }
    } else {
      if (response && response.code != 200) {
        // 关闭加载提示
        loadingInstance.close();  
       return Message.error(response?.message || '手机号码修改失败，请稍后再试');
      }
      // 如果响应中没有完整用户信息，仅更新手机号
      try {
        // 从本地存储中读取用户信息
        const userStr = localStorage.getItem('mall_user');
        if (userStr) {
          const userData = JSON.parse(userStr);
          // 更新手机号
          userData.phone = phoneForm.value.phone;
          // 保存回本地存储
          localStorage.setItem('mall_user', JSON.stringify(userData));
          console.log('本地用户信息手机号已更新');
          
          // 更新store中的用户信息
          if (userStore.user) {
            userStore.user.phone = phoneForm.value.phone;
          }
        }
      } catch (storageError) {
        console.error('更新本地存储失败:', storageError);
      }
    }
    
    // 关闭加载提示
    loadingInstance.close();
    
    Message.success('手机号码修改成功');
    showPhoneModal.value = false;
    
    // 重置表单
    phoneForm.value = {
      phone: '',
      verificationCode: ''
    };
    
    // 重新计算安全评分
    calculateSecurityScore();
  } catch (error) {
    console.error('修改手机号码失败:', error);
    const errorMsg = error.response?.data?.message || '修改手机号码失败，请稍后再试';
    Message.error(errorMsg);
  }
};

// 处理邮箱绑定成功
const handleEmailBindSuccess = () => {
  refreshUserInfo();
  showEmailModal.value = false;
  Message.success('邮箱绑定成功');
};

// 处理微信绑定成功
const handleWechatBindSuccess = () => {
  refreshUserInfo();
  Message.success('微信绑定成功！');
};

// 绑定或修改邮箱
const handleEmailBinding = async () => {
  // 表单验证
  if (!emailForm.value.email) {
    Message.error('请输入邮箱');
    return;
  }
  
  if (!/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(emailForm.value.email)) {
    Message.error('请输入正确的邮箱');
    return;
  }
  
  if (!emailForm.value.verificationCode) {
    Message.error('请输入验证码');
    return;
  }
  
  try {
    // 显示加载中状态
    const loadingInstance = Message.loading('正在提交绑定请求...');
    
    // 调用绑定邮箱接口
    const response = await userApi.profile.bindEmail(
      emailForm.value.email, 
      emailForm.value.verificationCode
    );
    
    console.log('绑定邮箱响应:', response);
    loadingInstance.close();
    // 检查响应中是否包含用户信息
    if (response && response.code === 200 && response.data && response.data.user) {
      // 使用响应中的完整用户信息更新本地存储
      try {
        // 将完整的用户信息保存到本地存储
        localStorage.setItem('mall_user', JSON.stringify(response.data.user));
        console.log('使用返回的完整用户信息更新本地存储');
        
        // 更新store中的用户信息
        userStore.user = response.data.user;
      } catch (storageError) {
        console.error('更新本地存储失败:', storageError);
      }
    } else {
      // 如果响应中没有完整的用户信息，只更新邮箱字段
      if (response && response.code === 200) {
        userStore.updateUserEmail({
          email: emailForm.value.email
        });
      }
    }
    if (response && response.code === 200) {
      Message.success(response?.message || '邮箱绑定成功');
    }else{
      return Message.error(response?.message || '邮箱绑定失败，请稍后再试');
    }
    showEmailModal.value = false;
    // 重置表单
    emailForm.value = {
      email: '',
      verificationCode: ''
    };
    
    // 重新计算安全评分
    calculateSecurityScore();
  } catch (error) {
    console.error('绑定邮箱失败:', error);
    Message.error(error.response?.data?.message || '绑定邮箱失败，请稍后再试');
  }
};
// 银行卡号验证（结合Luhn算法和长度校验）
const validateBankCard = (cardNumber) => {
  // 移除所有空格和连字符
  cardNumber = cardNumber.replace(/\s+|-/g, '');
  
  // 1. 长度校验（主流银行卡通常为16-19位）
  if (!/^\d{16,19}$/.test(cardNumber)) {
    return false;
  }
  return true;
}

// 实名认证
const handleVerification = async () => {
  // 表单验证
  if (!verifyForm.value.realName) {
    Message.error('请输入真实姓名');
    return;
  }
  
  if (!verifyForm.value.idCardNumber) {
    Message.error('请输入身份证号');
    return;
  }
  
  // 简单的身份证号验证
  if (!/^\d{17}[\dXx]$/.test(verifyForm.value.idCardNumber)) {
    Message.error('请输入正确的身份证号');
    return;
  }
  
  if (!verifyForm.value.authPhone) {
    Message.error('请输入认证手机号');
    return;
  }
  
  // 手机号验证
  if (!/^1[3-9]\d{9}$/.test(verifyForm.value.authPhone)) {
    Message.error('请输入正确的手机号');
    return;
  }
  
  if (!verifyForm.value.bankCardNo) {
    Message.error('请输入银行卡号');
    return;
  }
  
  // 银行卡号验证（简单验证，长度在6-19位之间）
  if (!validateBankCard(verifyForm.value.bankCardNo.trim())) {
    Message.error('请输入正确的银行卡号');
    return;
  }
  
  if (!verifyForm.value.idCardFront || !verifyForm.value.idCardBack) {
    Message.error('请上传身份证正反面照片');
    return;
  }
  
  try {
    // 显示加载中状态
    const loadingInstance = Message.loading('正在提交实名认证信息...');
    
    try {
      isVerifying.value = true;
      // 调用保存实名认证信息接口
      const response = await userApi.profile.saveRealnameAuth({
        realName: verifyForm.value.realName,
        idCardNumber: verifyForm.value.idCardNumber,
        idCardFront: verifyForm.value.idCardFront,
        idCardBack: verifyForm.value.idCardBack,
        authPhone: verifyForm.value.authPhone,
        bankCardNo: verifyForm.value.bankCardNo
      });
      
      console.log('实名认证响应:', response);
      
      // 关闭加载提示
      loadingInstance.close();
      
      if (response && response.code === 200) {
        // 获取认证状态
        let authStatus = 1; // 默认为待审核状态
        
        // 如果响应中包含认证信息，使用返回的状态
        if (response.data && response.data.authInfo) {
          authStatus = response.data.authInfo.auth_status;
        }
        
        Message.success(response.message || '实名认证信息提交成功，等待审核');
        showVerifyModal.value = false;
        
        // 重置表单
        verifyForm.value = {
          realName: '',
          idCardNumber: '',
          idCardFront: null,
          idCardBack: null
        };
        
        // 重新计算安全评分
        calculateSecurityScore();
        
        // 刷新实名认证信息
        await fetchRealnameAuthInfo();
      } else {
        Message.error(response?.message || '实名认证提交失败');
      }
    } catch (apiError) {
      // 关闭加载提示
      loadingInstance.close();
      
      console.error('调用实名认证接口失败:', apiError);
      Message.error(apiError.response?.data?.message || '实名认证失败，请稍后再试');
    }
  } catch (error) {
    console.error('实名认证流程异常:', error);
    Message.error('实名认证失败，请稍后再试');
  }
};

// 获取用户安全问题
const fetchSecurityQuestions = async () => {
  try {
    const response = await userApi.profile.getSecurityQuestions();
    console.log('获取安全问题响应:', response);
    
    if (response && response.code === 200 && response.data && response.data.questions) {
      const questions = response.data.questions;
      
      // 如果有安全问题，更新用户信息
      if (questions.length > 0) {
        userStore.user = {
          ...userStore.user,
          hasSecurityQuestion: true
        };
        
        // 如果打开了安全问题模态框，填充默认值
        if (showSecurityQuestionModal.value) {
          // 按照问题序号排序
          questions.sort((a, b) => a.question_order - b.question_order);
          
          // 填充表单
          if (questions.length >= 1) {
            securityQuestionForm.value.question1 = questions[0].question_text;
          }
          
          if (questions.length >= 2) {
            securityQuestionForm.value.question2 = questions[1].question_text;
          }
          
          if (questions.length >= 3) {
            securityQuestionForm.value.question3 = questions[2].question_text;
          }
        }
      } else {
        userStore.user = {
          ...userStore.user,
          hasSecurityQuestion: false
        };
      }
      
      // 重新计算安全评分
      calculateSecurityScore();
    }
  } catch (error) {
    console.error('获取安全问题失败:', error);
  }
};

// 设置安全问题
const handleSecurityQuestionSetting = async () => {
  // 表单验证
  if (!securityQuestionForm.value.question1 || !securityQuestionForm.value.answer1) {
    Message.error('请完善第一个安全问题及答案');
    return;
  }
  
  if (!securityQuestionForm.value.question2 || !securityQuestionForm.value.answer2) {
    Message.error('请完善第二个安全问题及答案');
    return;
  }
  
  if (!securityQuestionForm.value.question3 || !securityQuestionForm.value.answer3) {
    Message.error('请完善第三个安全问题及答案');
    return;
  }
  
  // 检查是否有重复的问题
  const questions = [
    securityQuestionForm.value.question1,
    securityQuestionForm.value.question2,
    securityQuestionForm.value.question3
  ];
  
  if (new Set(questions).size !== questions.length) {
    Message.error('安全问题不能重复，请选择不同的问题');
    return;
  }
  
  try {
    // 显示加载中状态
    const loadingInstance = Message.loading('正在保存安全问题...');
    
    // 准备请求数据
    const questionsData = [
      {
        question_text: securityQuestionForm.value.question1,
        answer: securityQuestionForm.value.answer1
      },
      {
        question_text: securityQuestionForm.value.question2,
        answer: securityQuestionForm.value.answer2
      },
      {
        question_text: securityQuestionForm.value.question3,
        answer: securityQuestionForm.value.answer3
      }
    ];
    
    // 调用保存安全问题接口
    const response = await userApi.profile.saveSecurityQuestions(questionsData);
    
    // 关闭加载提示
    loadingInstance.close();
    
    console.log('保存安全问题响应:', response);
    
    if (response && response.code === 200) {
      // 更新用户信息
      userStore.user = {
        ...userStore.user,
        hasSecurityQuestion: true
      };
      
      Message.success(response.message || '安全问题设置成功');
      showSecurityQuestionModal.value = false;
      
      // 重置表单
      securityQuestionForm.value = {
        question1: '',
        answer1: '',
        question2: '',
        answer2: '',
        question3: '',
        answer3: ''
      };
      
      // 重新计算安全评分
      calculateSecurityScore();
    } else {
      Message.error(response?.message || '设置安全问题失败');
    }
  } catch (error) {
    console.error('设置安全问题失败:', error);
    Message.error(error.response?.data?.message || '设置安全问题失败，请稍后再试');
  }
};
</script>

<style scoped>
/* 安全中心特定样式 */
.security-item {
  transition: all 0.3s ease;
}

.security-item:hover {
  background-color: #f9fafb;
}

.security-item:last-child {
  border-bottom: none;
}

/* 安全评分圆环样式 */
.score-circle-container {
  position: relative;
  width: 120px;
  height: 120px;
}

.score-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-value {
  font-size: 28px;
  font-weight: bold;
  color: #3b82f6;
}
</style>
