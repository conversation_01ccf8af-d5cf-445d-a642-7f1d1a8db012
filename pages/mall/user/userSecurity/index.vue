<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/user/center" class="hover:text-red-600">会员中心</NuxtLink> &gt; 
      <span>账户安全</span>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载账户安全信息...</p>
      </div>
      
      <!-- 未登录状态提示 -->
      <div v-else-if="!userStore.loggedIn" class="p-8 text-center">
        <div class="mb-6">
          <img src="https://placehold.co/120x120/ffffff/333333?text=未登录" alt="未登录" class="mx-auto mb-4 rounded-full border-4 border-gray-200">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">您尚未登录</h2>
          <p class="text-gray-600 mb-6">登录后可查看和管理您的账户安全信息</p>
          <div class="flex justify-center space-x-4">
            <NuxtLink to="/mall/login" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              立即登录
            </NuxtLink>
            <NuxtLink to="/mall/register" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition">
              注册账号
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- 已登录状态 - 安全中心 -->
      <div v-else class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <h2 class="text-xl font-bold mb-6 pb-2 border-b">账户安全中心</h2>
          
          <!-- 安全评分组件 -->
          <SecurityScore :score="securityScore" />
          
          <!-- 安全设置列表组件 -->
          <SecuritySettings 
            :userInfo="userInfo" 
            @show-modal="handleShowModal" 
          />
          
          <!-- 安全提示组件 -->
          <SecurityTips />
        </div>
      </div>
    </div>
    
    <!-- 各种模态框组件 -->
    <PasswordModal 
      v-model:visible="showPasswordModal"
      :phone="userInfo.phone"
      :countdown="countdown"
      @submit="handlePasswordChange"
      @send-code="sendVerificationCode"
      @cancel="showPasswordModal = false"
    />
    
    <PhoneModal 
      v-model:visible="showPhoneModal"
      :phone="userInfo.phone"
      :countdown="countdown"
      @submit="handlePhoneBinding"
      @send-code="sendVerificationCode"
      @cancel="showPhoneModal = false"
    />
    
    <EmailModal 
      v-model:visible="showEmailModal"
      :email="userInfo.email"
      :countdown="countdown"
      @submit="handleEmailBinding"
      @send-code="sendVerificationCode"
      @cancel="showEmailModal = false"
    />
    
    <VerifyModal 
      v-model:visible="showVerifyModal"
      :verifyForm="verifyForm"
      :isVerified="userInfo.isVerified"
      :verifiedTime="userInfo.verifiedTime"
      :verifyFailReason="userInfo.verifyFailReason"
      @submit="handleVerification"
      @open-upload="handleOpenUpload"
      @cancel="showVerifyModal = false"
    />
    
    <SecurityQuestionModal 
      v-model:visible="showSecurityQuestionModal"
      :questions="securityQuestions"
      @submit="handleSecurityQuestionSetting"
      @cancel="showSecurityQuestionModal = false"
    />
    
    <!-- 微信绑定模态框 -->
    <WechatBindModal 
      v-model:visible="showWechatBindModal" 
      :title="userInfo.wechatOpenid ? '重新绑定微信账号' : '绑定微信账号'" 
      @success="handleWechatBindSuccess" 
    />
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'mall'
});

// 导入所需的组件和API
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import userApi from '@/api/mall/user';
import mallApi from '@/api/mall';
import { apiBaseUrl } from '@/api/config';
import { useMallUserStore } from '@/store/mall/user';
import WechatBindModal from '@/components/mall/user/WechatBindModal.vue';



// 导入自定义组件
import SecurityScore from '@/components/mall/security/SecurityScore.vue';
import SecuritySettings from '@/components/mall/security/SecuritySettings.vue';
import SecurityTips from '@/components/mall/security/SecurityTips.vue';
import PasswordModal from '@/components/mall/security/modals/PasswordModal.vue';
import PhoneModal from '@/components/mall/security/modals/PhoneModal.vue';
import EmailModal from '@/components/mall/security/modals/EmailModal.vue';
import VerifyModal from '@/components/mall/security/modals/VerifyModal.vue';
import SecurityQuestionModal from '@/components/mall/security/modals/SecurityQuestionModal.vue';
import UserSideNav from '~/components/mall/user/UserSideNav.vue'; // 导入UserSideNav组件

// 获取用户存储


// 加载状态


// 用户状态管理
const userStore = useMallUserStore();
const isLoading = ref(true);
const isLoadingAuthInfo = ref(false); // 加载实名认证信息状态
const securityScore = ref(0);

// 实名认证表单数据
const verifyForm = ref({
  realName: '',
  idCardNumber: '',
  idCardFront: null,
  idCardBack: null,
  authPhone: '',
  bankCardNo: ''
});

// 用户信息 - 使用计算属性从store获取
const userInfo = computed(() => {
  if (!userStore.user) return {};
  
  return {
    hasPassword: userStore.user.hasPassword !== false,
    phone: userStore.user.phone || '',
    email: userStore.user.email || '',
    isVerified: userStore.user.isVerified || 0,
    hasSecurityQuestion: userStore.user.hasSecurityQuestion || false,
    verifyFailReason: userStore.user.verifyFailReason || '',
    wechatOpenid: userStore.user.wechatOpenid || '',
    verifiedTime: userStore.user.verifiedTime || ''
  };
});

// 模态框显示状态
const showPasswordModal = ref(false);
const showPhoneModal = ref(false);
const showEmailModal = ref(false);
const showVerifyModal = ref(false);
const showSecurityQuestionModal = ref(false);
const showWechatBindModal = ref(false);

// 倒计时相关
const countdown = ref(0);
let countdownTimer = null;

// 安全问题列表
const securityQuestions = ref([]);


// 监听安全问题模态框打开状态
watch(showSecurityQuestionModal, async (newVal) => {
  if (newVal) {
    // 当模态框打开时，获取最新的安全问题
    await fetchSecurityQuestions();
  }
});

// 加载用户安全信息
const loadSecurityInfo = async () => {
  console.log('加载用户安全信息...');
  try {
    // 如果用户未登录，尝试恢复会话
    if (!userStore.loggedIn) {
      await userStore.restoreSession();
    }
    console.log('用户已登录:', userStore.loggedIn);
    // 如果用户已登录，获取实名认证信息和安全问题
    if (userStore.loggedIn) {
      // 并行请求，提高加载速度
      await Promise.all([
        fetchRealnameAuthInfo(),
        fetchSecurityQuestions()
      ]);
    }
    
    // 计算安全评分
    calculateSecurityScore();
    
    return true;
  } catch (error) {
    console.error('加载安全信息失败:', error);
    Message.error('加载安全信息失败，请刷新页面重试');
    return false;
  } finally {
    isLoading.value = false;
  }
};

// 计算安全评分
const calculateSecurityScore = () => {
  let score = 5; // 基础分
  
  // 密码设置 默认一定会存在密码
  if (userInfo.value.hasPassword) score += 20;
  
  // 手机绑定
  if (userInfo.value.phone) score += 20;
  
  // 邮箱绑定
  if (userInfo.value.email) score += 15;
  
  // 实名认证
  if (userInfo.value.isVerified === 2) score += 15;
  
  // 安全问题
  if (userInfo.value.hasSecurityQuestion) score += 10;
  
  // 微信绑定
  if (userInfo.value.wechatOpenid) score += 15;
  
  securityScore.value = score;
};

// 获取用户实名认证信息
const fetchRealnameAuthInfo = async () => {
  try {
    isLoadingAuthInfo.value = true;
    const response = await userApi.profile.getRealnameAuth();
    
    if (response && response.code === 200) {
      // 处理嵌套的数据结构，正确获取authInfo
      const authInfo = response.data && response.data.authInfo;
      
      if (authInfo) {
        // 设置实名认证状态
        const authStatus = authInfo.auth_status || 0;
        
        // 更新 userStore 中的认证状态，这样 userInfo 计算属性也会更新
        userStore.user = {
          ...userStore.user,
          isVerified: authStatus,
          verifyFailReason: authInfo.audit_remark,
          verifiedTime: authInfo.verified_time || ''
        };
        
        // 如果有实名认证信息，填充到表单
        if (authInfo.real_name) {
          // 预填充表单
          verifyForm.value = {
            realName: authInfo.real_name || '',
            idCardNumber: authInfo.identity_no || '',
            idCardFront: authInfo.id_card_front_url || null,
            idCardBack: authInfo.id_card_back_url || null,
            authPhone: authInfo.auth_phone || '',
            bankCardNo: authInfo.bank_card_no || ''
          };
        }
      } else {
        // 用户未进行实名认证，设置为未认证状态(0)
        userStore.user = {
          ...userStore.user,
          isVerified: 0
        };
      }
    }
  } catch (error) {
    console.error('获取实名认证信息失败:', error);
  } finally {
    isLoadingAuthInfo.value = false;
  }
};
// 处理显示模态框
const handleShowModal = (type) => {
  switch (type) {
    case 'password':
      showPasswordModal.value = true;
      break;
    case 'phone':
      showPhoneModal.value = true;
      break;
    case 'email':
      showEmailModal.value = true;
      break;
    case 'verify':
      showVerifyModal.value = true;
      break;
    case 'securityQuestion':
      showSecurityQuestionModal.value = true;
      break;
    case 'wechat':
      showWechatBindModal.value = true;
      break;
  }
};

// 处理上传图片
const handleOpenUpload = (type) => {
  // 这里可以调用上传组件或者触发文件选择
  console.log('打开上传', type);
};

// 身份证照片上传相关引用
const idCardFrontInput = ref(null);
const idCardBackInput = ref(null);
const isIdCardFrontUploading = ref(false);
const isIdCardBackUploading = ref(false);

// 开始倒计时
const startCountdown = (type) => {
  countdown.value = 60;
  
  // 清除可能存在的定时器
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
  
  // 创建新的定时器
  countdownTimer = setInterval(() => {
    countdown.value--;
    
    if (countdown.value <= 0) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
  }, 1000);
};

// 清除倒计时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
});

// 获取用户安全问题
const fetchSecurityQuestions = async () => {
  try {
    const response = await userApi.profile.getSecurityQuestions();
    console.log('获取安全问题响应:', response);
    
    if (response && response.code === 200 && response.data && response.data.questions) {
      const questions = response.data.questions;
      securityQuestions.value = questions;
      
      // 如果有安全问题，更新用户信息
      if (questions.length > 0) {
        userStore.user = {
          ...userStore.user,
          hasSecurityQuestion: true
        };
        
        // 如果打开了安全问题模态框，填充默认值
        if (showSecurityQuestionModal.value) {
          // 按照问题序号排序
          questions.sort((a, b) => a.question_order - b.question_order);
          
          // 填充表单
          if (questions.length >= 1) {
            securityQuestionForm.value.question1 = questions[0].question_text;
          }
          
          if (questions.length >= 2) {
            securityQuestionForm.value.question2 = questions[1].question_text;
          }
          
          if (questions.length >= 3) {
            securityQuestionForm.value.question3 = questions[2].question_text;
          }
        }
      } else {
        userStore.user = {
          ...userStore.user,
          hasSecurityQuestion: false
        };
      }
      
      // 重新计算安全评分
      calculateSecurityScore();
    }
  } catch (error) {
    console.error('获取安全问题失败:', error);
  }
};

// 在组件挂载时加载安全信息
onMounted(async () => {
  await loadSecurityInfo();
});

// 处理手机号绑定/修改
const handlePhoneBinding = async (formData) => {
  try {
    // 显示加载中状态
    const loadingInstance = Message.loading('正在提交修改请求...');
    
    // 获取表单数据
    const { newPhone } = formData;
    
    // 更新用户信息
    if (userStore.user) {
      userStore.user = {
        ...userStore.user,
        phone: newPhone
      };
      
      // 将完整的用户信息保存到本地存储
      localStorage.setItem('mall_user', JSON.stringify(userStore.user));
      console.log('使用返回的完整用户信息更新本地存储');
    }
    
    // 关闭加载提示
    loadingInstance.close();
    
    // 重新计算安全评分
    calculateSecurityScore();
    
    Message.success('手机号码修改成功');
  } catch (error) {
    console.error('处理手机号绑定/修改失败:', error);
    Message.error('手机号码修改失败，请稍后再试');
  }
};

// 处理微信绑定成功
const handleWechatBindSuccess = () => {
  // 刷新用户信息
  loadSecurityInfo();
  Message.success('微信绑定成功！');
};

// 处理邮箱绑定/修改
const handleEmailBinding = async (formData) => {
  try {
    // 显示加载中状态
    const loadingInstance = Message.loading('正在提交修改请求...');
    
    // 获取表单数据
    const { newEmail } = formData;
    
    // 更新用户信息
    if (userStore.user) {
      userStore.user = {
        ...userStore.user,
        email: newEmail
      };
      
      // 将完整的用户信息保存到本地存储
      localStorage.setItem('mall_user', JSON.stringify(userStore.user));
      console.log('使用返回的完整用户信息更新本地存储');
    }
    
    // 关闭加载提示
    loadingInstance.close();
    
    // 重新计算安全评分
    calculateSecurityScore();
    
    Message.success('邮箱修改成功');
  } catch (error) {
    console.error('处理邮箱绑定/修改失败:', error);
    Message.error('邮箱修改失败，请稍后再试');
  }
};

// 发送验证码
const sendVerificationCode = (type) => {
  // 开始倒计时
  startCountdown(type);
};
// 处理实名认证
const handleVerification = async (verifyFormParams) => {
  // 表单验证
  if (!verifyFormParams.realName) {
    Message.error('请输入真实姓名');
    return;
  }
  
  if (!verifyFormParams.idCardNumber) {
    Message.error('请输入身份证号');
    return;
  }
  
  // 简单的身份证号验证
  if (!/^\d{17}[\dXx]$/.test(verifyFormParams.idCardNumber)) {
    Message.error('请输入正确的身份证号');
    return;
  }
  
  if (!verifyFormParams.authPhone) {
    Message.error('请输入认证手机号');
    return;
  }
  
  // 手机号验证
  if (!/^1[3-9]\d{9}$/.test(verifyFormParams.authPhone)) {
    Message.error('请输入正确的手机号');
    return;
  }
  
  if (!verifyFormParams.bankCardNo) {
    Message.error('请输入银行卡号');
    return;
  }
  
  // 银行卡号验证（简单验证，长度在6-19位之间）
  if (!/^\d{6,19}$/.test(verifyFormParams.bankCardNo.trim())) {
    Message.error('请输入正确的银行卡号');
    return;
  }
  
  if (!verifyFormParams.idCardFront || !verifyFormParams.idCardBack) {
    Message.error('请上传身份证正反面照片');
    return;
  }
  
  try {
    // 显示加载中状态
    const loadingInstance = Message.loading('正在提交实名认证信息...');
    
    try {
      // 调用保存实名认证信息接口
      const response = await userApi.profile.saveRealnameAuth({
        realName: verifyFormParams.realName,
        idCardNumber: verifyFormParams.idCardNumber,
        idCardFront: verifyFormParams.idCardFront,
        idCardBack: verifyFormParams.idCardBack,
        authPhone: verifyFormParams.authPhone,
        bankCardNo: verifyFormParams.bankCardNo
      });
      
      console.log('实名认证响应:', response);
      
      // 关闭加载提示
      loadingInstance.close();
      
      if (response && response.code === 200) {
        // 获取认证状态
        let authStatus = 1; // 默认为待审核状态
        
        // 如果响应中包含认证信息，使用返回的状态
        if (response.data && response.data.authInfo) {
          authStatus = response.data.authInfo.auth_status;
        }
        
        Message.success(response.message || '实名认证信息提交成功，等待审核');
        showVerifyModal.value = false;
        
        // 重置表单
        verifyForm.value = {
          realName: '',
          idCardNumber: '',
          authPhone: '',
          bankCardNo: '',
          idCardFront: null,
          idCardBack: null
        };
        
        // 重新计算安全评分
        calculateSecurityScore();
        
        // 刷新实名认证信息
        await fetchRealnameAuthInfo();
      } else {
        Message.error(response?.message || '实名认证提交失败');
      }
    } catch (apiError) {
      // 关闭加载提示
      loadingInstance.close();
      
      console.error('调用实名认证接口失败:', apiError);
      Message.error(apiError.response?.data?.message || '实名认证失败，请稍后再试');
    }
  } catch (error) {
    console.error('实名认证流程异常:', error);
    Message.error('实名认证失败，请稍后再试');
  }
};
</script>

<style scoped>
/* 页面特定样式 */
</style>