<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/user/center" class="hover:text-red-600">会员中心</NuxtLink> &gt; 
      <span>我的积分</span>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载积分数据...</p>
      </div>
      
      <!-- 未登录状态提示 -->
      <div v-else-if="!userStore.loggedIn" class="p-8 text-center">
        <div class="mb-6">
          <img src="https://placehold.co/120x120/f5f5f5/333333?text=未登录" alt="未登录" class="mx-auto mb-4 rounded-full border-4 border-gray-200">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">您尚未登录</h2>
          <p class="text-gray-600 mb-6">登录后可查看您的积分信息</p>
          <div class="flex justify-center space-x-4">
            <NuxtLink to="/mall/login" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              立即登录
            </NuxtLink>
            <NuxtLink to="/mall/register" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition">
              注册账号
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- 已登录状态 - 积分中心 -->
      <div v-else class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <!-- 积分概览 -->
          <div class="mb-8">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-xl font-bold text-gray-800">我的积分</h2>
              <NuxtLink to="/mall/points/rules" class="text-blue-600 hover:text-blue-700 text-sm flex items-center">
                <i class="i-carbon-information mr-1"></i>
                积分规则
              </NuxtLink>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- 当前积分 -->
              <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white shadow-lg">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="text-sm opacity-80 mb-1">当前积分</div>
                    <div class="text-3xl font-bold">{{ userPoints.available }}</div>
                  </div>
                  <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                    <i class="i-carbon-badge text-2xl"></i>
                  </div>
                </div>
                <div class="mt-4 text-sm opacity-80">
                  今年已累计 {{ userPoints.yearTotal }} 积分
                </div>
              </div>
              
              <!-- 待生效积分 -->
              <div class="bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg p-6 text-white shadow-lg">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="text-sm opacity-80 mb-1">待生效积分</div>
                    <div class="text-3xl font-bold">{{ userPoints.pending }}</div>
                  </div>
                  <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                    <i class="i-carbon-time text-2xl"></i>
                  </div>
                </div>
                <div class="mt-4 text-sm opacity-80">
                  订单完成后积分将自动生效
                </div>
              </div>
              
              <!-- 即将过期 -->
              <div class="bg-gradient-to-r from-red-500 to-red-600 rounded-lg p-6 text-white shadow-lg">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="text-sm opacity-80 mb-1">即将过期积分</div>
                    <div class="text-3xl font-bold">{{ userPoints.expiring }}</div>
                  </div>
                  <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                    <i class="i-carbon-warning-alt text-2xl"></i>
                  </div>
                </div>
                <div class="mt-4 text-sm opacity-80">
                  {{ new Date().getFullYear() }}年12月31日前有效
                </div>
              </div>
            </div>
          </div>
          
          <!-- 积分明细 -->
          <div class="mb-8">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-800">积分明细</h3>
              <div class="flex space-x-2">
                <a-select v-model="filterType" placeholder="全部类型" style="width: 120px">
                  <a-option value="all">全部类型</a-option>
                  <a-option value="earn">积分获取</a-option>
                  <a-option value="spend">积分消费</a-option>
                  <a-option value="expire">积分过期</a-option>
                </a-select>
                <a-select v-model="filterTime" placeholder="全部时间" style="width: 120px">
                  <a-option value="all">全部时间</a-option>
                  <a-option value="month">近一个月</a-option>
                  <a-option value="quarter">近三个月</a-option>
                  <a-option value="year">近一年</a-option>
                </a-select>
              </div>
            </div>
            
            <!-- 积分记录表格 -->
            <a-table 
              :columns="columns" 
              :data="filteredPointsRecords" 
              :pagination="pagination"
              @page-change="onPageChange"
              border
              stripe
              :loading="tableLoading"
            >
              <!-- 积分变动列 -->
              <template #points="{ record }">
                <span :class="record.type === 'earn' ? 'text-green-600' : 'text-red-600'">
                  {{ record.type === 'earn' ? '+' : '-' }}{{ record.points }}
                </span>
              </template>
              
              <!-- 类型列 -->
              <template #type="{ record }">
                <a-tag :color="getTypeColor(record.type)">
                  {{ getTypeText(record.type) }}
                </a-tag>
              </template>
              
              <!-- 状态列 -->
              <template #status="{ record }">
                <a-tag :color="getStatusColor(record.status)">
                  {{ getStatusText(record.status) }}
                </a-tag>
              </template>
            </a-table>
          </div>
          
          <!-- 积分获取途径 -->
          <div class="mb-8">
            <h3 class="text-lg font-medium text-gray-800 mb-4">积分获取途径</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div class="border border-gray-200 rounded-lg p-4 flex items-center">
                <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                  <i class="i-carbon-shopping-cart text-blue-600 text-xl"></i>
                </div>
                <div>
                  <div class="font-medium">商品购买</div>
                  <div class="text-sm text-gray-500">消费1元获得1积分</div>
                </div>
              </div>
              
              <div class="border border-gray-200 rounded-lg p-4 flex items-center">
                <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mr-4">
                  <i class="i-carbon-star text-green-600 text-xl"></i>
                </div>
                <div>
                  <div class="font-medium">评价晒单</div>
                  <div class="text-sm text-gray-500">每次评价获得5积分</div>
                </div>
              </div>
              
              <div class="border border-gray-200 rounded-lg p-4 flex items-center">
                <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mr-4">
                  <i class="i-carbon-user-activity text-purple-600 text-xl"></i>
                </div>
                <div>
                  <div class="font-medium">每日签到</div>
                  <div class="text-sm text-gray-500">每日签到获得2积分</div>
                </div>
              </div>
              
              <div class="border border-gray-200 rounded-lg p-4 flex items-center">
                <div class="w-12 h-12 rounded-full bg-amber-100 flex items-center justify-center mr-4">
                  <i class="i-carbon-share text-amber-600 text-xl"></i>
                </div>
                <div>
                  <div class="font-medium">分享活动</div>
                  <div class="text-sm text-gray-500">每次分享获得3积分</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 积分使用规则 -->
          <div>
            <h3 class="text-lg font-medium text-gray-800 mb-4">积分使用规则</h3>
            <a-collapse>
              <a-collapse-item header="积分有效期" key="1">
                <p class="text-gray-600">
                  积分有效期为获得积分当年及次年，例如2025年获得的积分，有效期至2026年12月31日。
                </p>
              </a-collapse-item>
              <a-collapse-item header="积分使用方式" key="2">
                <p class="text-gray-600">
                  积分可用于商城购物抵扣，100积分=1元；也可兑换优惠券或参与积分专区活动。
                </p>
              </a-collapse-item>
              <a-collapse-item header="积分获取规则" key="3">
                <p class="text-gray-600">
                  商品购买：消费1元获得1积分<br>
                  评价晒单：每次评价获得5积分<br>
                  每日签到：每日签到获得2积分<br>
                  分享活动：每次分享获得3积分
                </p>
              </a-collapse-item>
              <a-collapse-item header="积分调整规则" key="4">
                <p class="text-gray-600">
                  订单取消或退货：相应积分将被扣除<br>
                  活动奖励：特殊活动可能有额外积分奖励<br>
                  积分清零：过期积分将自动清零
                </p>
              </a-collapse-item>
            </a-collapse>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useMallUserStore } from '~/store/mall/user';
import UserSideNav from '~/components/mall/user/UserSideNav.vue';
import { Message } from '@arco-design/web-vue';

definePageMeta({
  layout: 'mall'
});

const userStore = useMallUserStore();

// 状态变量
const isLoading = ref(true);
const tableLoading = ref(false);
const filterType = ref('all');
const filterTime = ref('all');
const currentPage = ref(1);
const pageSize = ref(10);

// 用户积分信息
const userPoints = reactive({
  available: 0,
  pending: 0,
  expiring: 0,
  yearTotal: 0
});

// 积分记录
const pointsRecords = ref([]);

// 表格列定义
const columns = [
  {
    title: '时间',
    dataIndex: 'time',
  },
  {
    title: '积分变动',
    slotName: 'points',
  },
  {
    title: '类型',
    slotName: 'type',
  },
  {
    title: '状态',
    slotName: 'status',
  },
  {
    title: '详情',
    dataIndex: 'description',
  }
];

// 分页配置
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
  showTotal: true,
  showJumper: true,
});

// 根据筛选条件过滤积分记录
const filteredPointsRecords = computed(() => {
  let filtered = [...pointsRecords.value];
  
  // 按类型筛选
  if (filterType.value !== 'all') {
    filtered = filtered.filter(record => record.type === filterType.value);
  }
  
  // 按时间筛选
  if (filterTime.value !== 'all') {
    const now = new Date();
    let startDate;
    
    if (filterTime.value === 'month') {
      startDate = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    } else if (filterTime.value === 'quarter') {
      startDate = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());
    } else if (filterTime.value === 'year') {
      startDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate());
    }
    
    filtered = filtered.filter(record => {
      const recordDate = new Date(record.time);
      return recordDate >= startDate;
    });
  }
  
  // 更新分页信息
  pagination.total = filtered.length;
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filtered.slice(start, end);
});

// 获取类型文本
const getTypeText = (type) => {
  const typeMap = {
    'earn': '积分获取',
    'spend': '积分消费',
    'expire': '积分过期'
  };
  return typeMap[type] || '未知类型';
};

// 获取类型颜色
const getTypeColor = (type) => {
  const colorMap = {
    'earn': 'green',
    'spend': 'blue',
    'expire': 'red'
  };
  return colorMap[type] || 'gray';
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'completed': '已完成',
    'pending': '处理中',
    'failed': '失败'
  };
  return statusMap[status] || '未知状态';
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'completed': 'green',
    'pending': 'orange',
    'failed': 'red'
  };
  return colorMap[status] || 'gray';
};

// 页码变化处理
const onPageChange = (page) => {
  currentPage.value = page;
};

// 加载积分数据
const loadPointsData = async () => {
  try {
    isLoading.value = true;
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // 模拟用户积分数据
    userPoints.available = 1250;
    userPoints.pending = 180;
    userPoints.expiring = 320;
    userPoints.yearTotal = 2430;
    
    // 模拟积分记录数据
    const mockRecords = [
      {
        id: 1,
        time: '2025-04-16 10:30:00',
        points: 120,
        type: 'earn',
        status: 'completed',
        description: '购物订单 #20250416001 获得积分'
      },
      {
        id: 2,
        time: '2025-04-15 15:45:00',
        points: 50,
        type: 'spend',
        status: 'completed',
        description: '兑换优惠券 满100减10'
      },
      {
        id: 3,
        time: '2025-04-14 09:20:00',
        points: 5,
        type: 'earn',
        status: 'completed',
        description: '评价订单 #20250410002 获得积分'
      },
      {
        id: 4,
        time: '2025-04-13 14:10:00',
        points: 2,
        type: 'earn',
        status: 'completed',
        description: '每日签到获得积分'
      },
      {
        id: 5,
        time: '2025-04-12 18:30:00',
        points: 80,
        type: 'earn',
        status: 'completed',
        description: '购物订单 #20250412003 获得积分'
      },
      {
        id: 6,
        time: '2025-04-11 11:25:00',
        points: 100,
        type: 'spend',
        status: 'completed',
        description: '积分商城兑换商品'
      },
      {
        id: 7,
        time: '2025-04-10 16:40:00',
        points: 3,
        type: 'earn',
        status: 'completed',
        description: '分享活动获得积分'
      },
      {
        id: 8,
        time: '2025-04-09 20:15:00',
        points: 2,
        type: 'earn',
        status: 'completed',
        description: '每日签到获得积分'
      },
      {
        id: 9,
        time: '2025-04-08 13:20:00',
        points: 150,
        type: 'earn',
        status: 'pending',
        description: '购物订单 #20250408004 获得积分'
      },
      {
        id: 10,
        time: '2025-04-07 09:30:00',
        points: 2,
        type: 'earn',
        status: 'completed',
        description: '每日签到获得积分'
      },
      {
        id: 11,
        time: '2025-04-06 14:45:00',
        points: 30,
        type: 'spend',
        status: 'completed',
        description: '兑换优惠券 满300减30'
      },
      {
        id: 12,
        time: '2025-04-05 11:10:00',
        points: 2,
        type: 'earn',
        status: 'completed',
        description: '每日签到获得积分'
      },
      {
        id: 13,
        time: '2025-04-04 16:20:00',
        points: 5,
        type: 'earn',
        status: 'completed',
        description: '评价订单 #20250401005 获得积分'
      },
      {
        id: 14,
        time: '2025-04-03 10:05:00',
        points: 2,
        type: 'earn',
        status: 'completed',
        description: '每日签到获得积分'
      },
      {
        id: 15,
        time: '2025-04-02 19:30:00',
        points: 200,
        type: 'earn',
        status: 'completed',
        description: '购物订单 #20250402006 获得积分'
      },
      {
        id: 16,
        time: '2025-03-15 14:25:00',
        points: 100,
        type: 'expire',
        status: 'completed',
        description: '2024年度积分过期'
      },
      {
        id: 17,
        time: '2025-03-10 09:15:00',
        points: 300,
        type: 'earn',
        status: 'completed',
        description: '购物订单 #20250310007 获得积分'
      },
      {
        id: 18,
        time: '2025-03-05 16:40:00',
        points: 150,
        type: 'spend',
        status: 'completed',
        description: '积分商城兑换商品'
      },
      {
        id: 19,
        time: '2025-03-01 11:20:00',
        points: 50,
        type: 'earn',
        status: 'completed',
        description: '生日特别奖励积分'
      },
      {
        id: 20,
        time: '2025-02-28 15:10:00',
        points: 180,
        type: 'earn',
        status: 'completed',
        description: '购物订单 #20250228008 获得积分'
      }
    ];
    
    pointsRecords.value = mockRecords;
    
    // 更新分页信息
    pagination.total = mockRecords.length;
    
  } catch (error) {
    console.error('加载积分数据失败:', error);
    Message.error('加载积分数据失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};

// 在组件挂载后加载数据
onMounted(async () => {
  // 尝试恢复用户会话
  if (!userStore.loggedIn) {
    await userStore.restoreSession();
  }
  
  // 加载积分数据
  await loadPointsData();
});
</script>

<style scoped>
/* 积分页面特定样式 */
.point-card {
  transition: all 0.3s ease;
}

.point-card:hover {
  transform: translateY(-5px);
}

:deep(.arco-table-th) {
  background-color: #f5f7fa;
}

:deep(.arco-collapse-item-header) {
  font-weight: 500;
}
</style>
