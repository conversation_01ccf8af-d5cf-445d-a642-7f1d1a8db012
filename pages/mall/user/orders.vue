<template>
  <div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-800">我的订单</h2>
            <p class="text-gray-500 text-sm mt-1">您可以查看所有订单并进行管理</p>
          </div>

          <!-- 订单状态筛选 -->
          <div class="mb-6 border-b border-gray-200">
            <div class="flex">
              <a @click.prevent="switchOrderStatus(null)" 
                 :class="[
                   'px-4 py-3 text-center border-b-2 font-medium cursor-pointer',
                   currentStatus === null ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'
                 ]">
                全部订单
              </a>
              <a @click.prevent="switchOrderStatus(0)" 
                 :class="[
                   'px-4 py-3 text-center border-b-2 font-medium cursor-pointer',
                   currentStatus === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'
                 ]">
                待付款
                <span v-if="orderStats.pendingPayment > 0" class="ml-1 text-xs bg-red-500 text-white rounded-full px-1.5 py-0.5">
                  {{ orderStats.pendingPayment }}
                </span>
              </a>
              <a @click.prevent="switchOrderStatus(1)" 
                 :class="[
                   'px-4 py-3 text-center border-b-2 font-medium cursor-pointer',
                   currentStatus === 1 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'
                 ]">
                待发货
                <span v-if="orderStats.pendingShipment > 0" class="ml-1 text-xs bg-red-500 text-white rounded-full px-1.5 py-0.5">
                  {{ orderStats.pendingShipment }}
                </span>
              </a>
              <a @click.prevent="switchOrderStatus(2)" 
                 :class="[
                   'px-4 py-3 text-center border-b-2 font-medium cursor-pointer',
                   currentStatus === 2 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'
                 ]">
                待收货
                <span v-if="orderStats.pendingReceipt > 0" class="ml-1 text-xs bg-red-500 text-white rounded-full px-1.5 py-0.5">
                  {{ orderStats.pendingReceipt }}
                </span>
              </a>
              <a @click.prevent="switchOrderStatus(3)" 
                 :class="[
                   'px-4 py-3 text-center border-b-2 font-medium cursor-pointer',
                   currentStatus === 3 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'
                 ]">
                交易成功
                <span v-if="orderStats.pendingReview > 0" class="ml-1 text-xs bg-red-500 text-white rounded-full px-1.5 py-0.5">
                  {{ orderStats.pendingReview }}
                </span>
              </a>
              <a @click.prevent="switchOrderStatus(4)" 
                 :class="[
                   'px-4 py-3 text-center border-b-2 font-medium cursor-pointer',
                   currentStatus === 4 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'
                 ]">
                已关闭
                <span v-if="orderStats.closed > 0" class="ml-1 text-xs bg-red-500 text-white rounded-full px-1.5 py-0.5">
                  {{ orderStats.closed }}
                </span>
              </a>
              <a @click.prevent="switchOrderStatus(5)" 
                 :class="[
                   'px-4 py-3 text-center border-b-2 font-medium cursor-pointer',
                   currentStatus === 5 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700'
                 ]">
                已退款
                <span v-if="orderStats.refunded > 0" class="ml-1 text-xs bg-red-500 text-white rounded-full px-1.5 py-0.5">
                  {{ orderStats.refunded }}
                </span>
              </a>
            </div>
          </div>

          <!-- 订单搜索 -->
          <div class="mb-6">
            <div class="flex">
              <input 
                type="text" 
                v-model="searchKeyword" 
                placeholder="输入订单号或备注搜索" 
                class="flex-1 px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                @keyup.enter="debouncedSearch()"
              >
              <button 
                @click="debouncedSearch()" 
                class="px-4 py-2 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 bg-blue-600 text-white hover:bg-blue-700"
              >
                搜索
              </button>
            </div>
            <div class="text-xs text-gray-500 mt-1">支持模糊搜索订单号和订单备注</div>
          </div>

          <!-- 加载中 -->
          <div v-if="isLoading" class="py-16">
            <div class="text-center">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              <p class="mt-2 text-gray-500">加载中...</p>
            </div>
          </div>

          <!-- 订单列表 -->
          <div v-else-if="orders.length > 0">
            <div v-for="order in orders" :key="order.id" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
              <!-- 订单头部 -->
              <div class="bg-gray-50 px-4 py-3 flex items-center justify-between">
                <div>
                  <span class="text-gray-500">订单号：</span>
                  <span class="text-gray-800 font-medium">{{ order.orderNumber }}</span>
                  <span class="text-gray-500 ml-4">{{ order.createTime }}</span>
                </div>
                <div>
                  <span :class="{
                    'text-blue-600 font-medium': order.status === 0,
                    'text-green-600 font-medium': order.status === 1,
                    'text-orange-600 font-medium': order.status === 2,
                    'text-green-700 font-medium': order.status === 3,
                    'text-gray-600 font-medium': order.status === 4,
                    'text-red-600 font-medium': order.status === 5
                  }">
                    {{ getOrderStatusText(order.status) }}
                  </span>
                </div>
              </div>
              
              <!-- 订单商品 -->
              <div class="p-4">
                <div v-for="item in order.items" :key="item.productId" class="flex items-center py-3 border-b border-gray-100 last:border-b-0">
                  <div class="w-16 h-16 mr-4">
                    <img :src="item.image" :alt="item.name" class="w-full h-full object-cover rounded">
                  </div>
                  <div class="flex-1">
                    <div class="text-gray-800 font-medium hover:text-blue-600 cursor-pointer" @click="goToProductDetail(item.productId)">
                      {{ item.name }}
                    </div>
                    <div class="text-gray-500 text-sm mt-1">{{ item.attributes }}</div>
                  </div>
                  <div class="text-gray-500 mx-4">x{{ item.quantity }}</div>
                  <div class="text-gray-800 font-medium w-24 text-right">
                    ¥{{ item.price.toFixed(2) }}
                  </div>
                </div>
              </div>
              
              <!-- 订单底部 -->
              <div class="bg-gray-50 px-4 py-3 flex items-center justify-between">
                <div class="text-gray-500 text-sm">
                  收货地址：{{ order.address.province }}{{ order.address.city }}{{ order.address.district }}{{ order.address.detail }}
                </div>
                <div class="flex items-center">
                  <div class="mr-4">
                    <span class="text-gray-500">实付款：</span>
                    <span class="text-red-600 font-bold">¥{{ parseFloat(order.totalAmount || 0) }}</span>
                  </div>
                  <div class="flex space-x-2">
                    <a @click.prevent="goToOrderDetail(order.id)" 
                       class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 hover:text-blue-600 cursor-pointer">
                      订单详情
                    </a>
                    
                    <!-- 根据订单状态显示不同的操作按钮 -->
                    <template v-if="order.status === 0"> <!-- 待付款 -->
                      <a @click.prevent="goToPayment(order.id)" class="px-3 py-1 border border-red-500 rounded text-sm text-red-600 hover:bg-red-50 cursor-pointer">
                        去支付
                      </a>
                      <a-popconfirm content="确定要取消订单吗？" @ok="cancelOrder(order.id)">
                        <a-button class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 cursor-pointer">
                          取消订单
                        </a-button>
                      </a-popconfirm>
                    </template>
                    
                    <template v-else-if="order.status === 1"> <!-- 待发货 -->
                      <a @click.prevent="remindDelivery(order.id)" class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 cursor-pointer">
                        提醒发货
                      </a>
                    </template>
                    
                    <template v-else-if="order.status === 2"> <!-- 待收货 -->
                      <a-popconfirm content="确定要确认收货吗？" @ok="confirmReceipt(order.id)">
                        <a-button status="warning" class="px-3 py-1 border border-blue-500 rounded text-sm text-blue-600 hover:bg-blue-50 cursor-pointer">
                          确认收货
                        </a-button>
                      </a-popconfirm>
                      <a @click.prevent="viewLogistics(order.id)" class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 cursor-pointer">
                        查看物流
                      </a>
                    </template>
                    
                    <template v-else-if="order.status === 3"> <!-- 交易成功 -->
                      <a @click.prevent="goToReview(order.id)" class="px-3 py-1 border border-blue-500 rounded text-sm text-blue-600 hover:bg-blue-50 cursor-pointer">
                        去评价
                      </a>
<!--                      <a @click.prevent="rebuy(order.id)" class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50 cursor-pointer">-->
<!--                        再次购买-->
<!--                      </a>-->
                    </template>
                    
<!--                    <template v-else-if="order.status === 4"> &lt;!&ndash; 已关闭 &ndash;&gt;-->
<!--                      <a @click.prevent="rebuy(order.id)" class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">-->
<!--                        再次购买-->
<!--                      </a>-->
<!--                    </template>-->
                    
<!--                    <template v-else-if="order.status === 5"> &lt;!&ndash; 已退款 &ndash;&gt;-->
<!--                      <a @click.prevent="rebuy(order.id)" class="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 hover:bg-gray-50">-->
<!--                        再次购买-->
<!--                      </a>-->
<!--                    </template>-->
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-else class="py-16 flex flex-col items-center justify-center">
            <i class="i-carbon-document text-6xl text-gray-300 mb-4"></i>
            <p class="text-gray-500">暂无相关订单</p>
            <a @click.prevent="goToMall" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              去购物
            </a>
          </div>
          
          <!-- 分页 -->
          <div class="mt-6 flex justify-center align-center">
          <!-- 分页 -->
          <div v-if="orders.length > 0" class="mt-6 flex justify-center">
            <a-pagination
              v-model:current="currentPage"
              show-total
              show-jumper 
              show-page-size
              :total="totalOrders"
              :page-size="pageSize"
              :page-size-options="pageSizeOptions"
              @change="handlePageChange"
              @page-size-change="handlePageSizeChange"
            />
          </div>
           </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import UserSideNav from '~/components/mall/user/UserSideNav.vue';
import { useNuxtApp } from '#app';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import orderApi from '@/api/mall/order';
import { Message } from '@arco-design/web-vue';
// 时间格式化函数
/**
 * 将时间戳格式化为可读的日期时间格式
 * @param {string|number} timestamp - 时间戳或时间字符串
 * @returns {string} 格式化后的日期时间字符串，如：2025-05-12 10:20:30
 */
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';
  
  try {
    const date = new Date(timestamp);
    if (isNaN(date.getTime())) return '';
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('格式化时间戳失败:', error);
    return '';
  }
};

const route = useRoute();
const router = useRouter();
const nuxtApp = useNuxtApp();

definePageMeta({
  layout: 'mall'
});

// 状态变量
const orders = ref([]);
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(5);
const totalPages = ref(1);
const totalOrders = ref(0);
const pageSizeOptions = ref([5, 10, 20, 50]);
const orderStats = ref({
  pendingPayment: 0,
  pendingShipment: 0,
  pendingReceipt: 0,
  pendingReview: 0,
  closed: 0,
  refunded: 0
});
const isLoading = ref(false);

// 当前选中的订单状态
const currentStatus = computed(() => {
  const status = route.query.orderStatus;
  return status ? parseInt(status) : null;
});

// 获取订单状态文本
const getOrderStatusText = (status) => {
  switch (parseInt(status)) {
    case 0: return '待付款';
    case 1: return '待发货';
    case 2: return '待收货';
    case 3: return '交易成功';
    case 4: return '已关闭';
    case 5: return '已退款';
    default: return '未知状态';
  }
};

// 获取订单状态数量
const getOrderStatusCounts = async () => {
  try {
    console.log('开始获取订单状态数量');
    const response = await orderApi.getOrderStatusCounts();
    console.log('订单状态数量响应:', response);
    
    if (response.code === 200 && response.data) {
      // 更新订单状态数量
      orderStats.value = {
        pendingPayment: response.data['0'] || 0,  // 待付款
        pendingShipment: response.data['1'] || 0, // 待发货
        pendingReceipt: response.data['2'] || 0,  // 待收货
        pendingReview: response.data['3'] || 0,   // 交易成功
        closed: response.data['4'] || 0,          // 已关闭
        refunded: response.data['5'] || 0          // 已退款
      };
      console.log('更新订单状态数量成功:', orderStats.value);
    } else {
      console.error('获取订单状态数量失败:', response.message);
    }
  } catch (error) {
    console.error('获取订单状态数量出错:', error);
  }
};

// 根据路由参数加载订单数据
const loadOrders = async () => {
  // 从路由参数中获取状态
  const routeStatus = route.query.orderStatus ? parseInt(route.query.orderStatus) : null;
  console.log('从路由参数加载订单数据，状态:', routeStatus);
  return loadOrdersWithStatus(routeStatus);
};

// 监听路由参数变化
watch(() => route.query.orderStatus, (newStatus) => {
  console.log('路由参数变化，订单状态变更为:', newStatus);
  loadOrders(); // 路由参数变化时重新加载数据
}, { immediate: false });

// 在组件挂载后自动加载订单数据和订单状态数量
onMounted(() => {
  console.log('订单页面加载，自动获取订单数据');
  loadOrders();
  getOrderStatusCounts();
});

// 加载指定状态的订单数据
const loadOrdersWithStatus = async (status) => {
  console.log('进入loadOrdersWithStatus函数，状态参数:', status);
  
  if (isLoading.value) {
    console.log('正在加载中，跳过该请求');
    return;
  }
  
  isLoading.value = true;
  NProgress.start(); // 启动顶部进度条
  console.log('NProgress进度条已启动');
  
  try {
    console.log('开始加载订单数据，状态:', status, '，关键词:', searchKeyword.value);
    
    // 构建查询参数
    const queryParams = {
      page: currentPage.value,
      pageSize: pageSize.value,
      sortField: 'created_at',
      sortOrder: 'desc'
    };
    
    // 添加订单状态筛选
    if (status !== null) {
      queryParams.orderStatus = status;
      console.log('添加订单状态筛选:', status);
    } else {
      console.log('不添加订单状态筛选，查询所有状态');
    }
    
    // 添加订单号搜索
    if (searchKeyword.value) {
      // 使用id作为订单号查询
      queryParams.id = searchKeyword.value;
      console.log('添加订单号搜索:', searchKeyword.value);
    }
    
    console.log('即将发送订单查询请求，参数:', JSON.stringify(queryParams));
    
    // 调用API获取订单数据
    console.log('开始调用orderApi.queryOwnOrders...');
    const response = await orderApi.queryOwnOrders(queryParams);
    console.log('订单API响应收到:', response);
    
    // 检查响应是否成功，并打印详细信息
    console.log('响应状态码:', response.code, '响应消息:', response.message);
    
    // 检查响应是否成功并且数据存在
    // 根据实际响应结构，成功状态码是200
    if (response.code === 200 && response.data) {
      // 新的响应格式处理
      const { items, pageInfo } = response.data;
      const { total, currentPage: respCurrentPage, totalPage } = pageInfo || { total: 0, currentPage: 1, totalPage: 1 };
      console.log('成功获取订单数据，总数:', total, '当前页:', respCurrentPage);
      
      // 处理订单数据，格式化为前端需要的格式
      const formattedOrders = items.map(order => ({
        id: order.id,
        orderNumber: order.id.toString(), // 使用id作为订单号
        status: order.orderStatus,
        createTime: formatTimestamp(order.createdAt),
        totalAmount: parseFloat(order.totalAmount),
        paymentMethod: order.paymentMethod,
        shippingFee: order.shippingFee,
        items: order.items.map(item => ({
          productId: item.productId,
          name: item.productName,
          image: item.productImage || 'https://placehold.co/200x200',
          price: parseFloat(item.unitPrice),
          quantity: item.quantity,
          attributes: item.skuSpecifications || ''
        })),
        address: order.shipping ? {
          name: order.shipping.recipientName,
          phone: order.shipping.recipientPhone,
          // 地区路径名称可能需要分割
          province: order.shipping.regionPathName.substring(0, 3),
          city: order.shipping.regionPathName.substring(3, 6),
          district: order.shipping.regionPathName.substring(6),
          detail: order.shipping.streetAddress
        } : {
          name: '',
          phone: '',
          province: '',
          city: '',
          district: '',
          detail: ''
        }
      }));
      
      orders.value = formattedOrders;
      totalOrders.value = total;
      totalPages.value = totalPage;
      currentPage.value = respCurrentPage;
      
      // 不在这里更新订单统计，而是通过专门的API获取
    } else {
      // 如果响应成功但状态码不是200，检查是否有数据
      if (response.data) {
        // 尝试从数据中提取信息
        console.log('响应消息包含“成功”，尝试提取数据:', response.data);
        const { items, pageInfo } = response.data;
        const { total, currentPage: respCurrentPage, totalPage } = pageInfo || { total: 0, currentPage: 1, totalPage: 1 };
        totalOrders.value = total || 0;
        
        // 处理订单数据，格式化为前端需要的格式
        const formattedOrders = items && items.length > 0 ? items.map(order => ({
          id: order.id,
          orderNumber: order.order_sn,
          status: order.order_status,
          createTime: formatTimestamp(order.created_at),
          totalAmount: parseFloat(order.total_amount),
          paymentMethod: order.payment_method,
          items: order.order_items && order.order_items.length > 0 ? order.order_items.map(item => ({
            productId: item.productId,
            name: item.productName,
            image: item.productImage || 'https://placehold.co/200x200',
            price: parseFloat(item.unitPrice),
            quantity: item.quantity,
          })) : [],
          address: order.order_shipping_info ? {
            name: order.order_shipping_info.receiver_name,
            phone: order.order_shipping_info.receiver_phone,
            province: order.order_shipping_info.province,
            city: order.order_shipping_info.city,
            district: order.order_shipping_info.district,
            detail: order.order_shipping_info.address_detail
          } : {
            name: '',
            phone: '',
            province: '',
            city: '',
            district: '',
            detail: ''
          }
        })) : [];
        
        orders.value = formattedOrders;
        totalOrders.value = total || 0;
        totalPages.value = totalPage || 1;
        currentPage.value = respCurrentPage || 1;
        
        // 获取订单状态数量
        getOrderStatusCount();
      } else {
        // 真正的错误情况
        console.error('获取订单数据失败:', response.message);
        if (window.$message) {
          window.$message.error(response.message || '获取订单数据失败');
        }
        orders.value = [];
        totalOrders.value = 0;
        totalPages.value = 1;
      }
    }
  } catch (error) {
    console.error('获取订单数据失败:', error);
    if (window.$message) {
      window.$message.error('获取订单数据失败');
    }
  } finally {
    isLoading.value = false;
    NProgress.done(); // 完成进度条
  }
};

// 防抖函数
const debounce = (fn, delay) => {
  let timer = null;
  return function() {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  };
};

// 搜索订单
const searchOrders = () => {
  console.log('开始搜索订单，关键词:', searchKeyword.value.trim());
  currentPage.value = 1; // 重置为第一页
  
  // 使用统一的加载函数
  loadOrders();
};

// 创建防抖版本的搜索函数
const debouncedSearch = debounce(searchOrders, 300);

// 获取分页查询字符串
const getPageQueryString = (page) => {
  const query = { ...route.query, page };
  return Object.entries(query)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');
};

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page;
  loadOrders();
};

const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1; // 重置为第一页
  loadOrders();
};

// 兼容旧的分页方法
const changePage = (page) => {
  handlePageChange(page);
};

// 切换订单状态
const switchOrderStatus = async (status) => {
  // 如果当前已经是这个状态，不需要重新加载
  if (status === currentStatus.value) {
    console.log('当前已经是该状态，不重新加载:', status);
    return;
  }
  
  try {
    console.log('开始切换订单状态为:', status);
    
    // 重置分页到第一页
    currentPage.value = 1;
    
    // 先更新路由参数，路由参数变化会触发监听器重新加载数据
    console.log('开始更新路由参数，状态:', status);
    await router.replace({
      path: '/mall/user/orders',
      query: status !== null ? { orderStatus: status } : {}
    });
    console.log('路由参数更新完成，将自动触发数据加载');
    // 注意：不需要手动调用loadOrders，因为路由变化会触发watch监听器自动加载
    
    // 确保Nuxt加载指示器被关闭
    if (nuxtApp && nuxtApp.$loading) {
      nuxtApp.$loading.finish();
    }
    
    // 手动修复Nuxt的加载状态
    const loadingBar = document.querySelector('.nuxt-progress');
    if (loadingBar) {
      loadingBar.style.opacity = '0';
      loadingBar.style.width = '0%';
    }
    
    // 移除任何可能存在的全局加载指示器
    const globalSpinner = document.querySelector('.nuxt-loading-indicator');
    if (globalSpinner) {
      globalSpinner.style.display = 'none';
    }
  } catch (error) {
    console.error('切换订单状态失败:', error);
    // 显示错误提示
    if (window.$message) {
      window.$message.error('切换订单状态失败，请稍后重试');
    }
  } finally {
    isLoading.value = false;
    NProgress.done(); // 完成进度条
  }
};

// 跳转到商品详情
const goToProductDetail = (productId) => {
  router.push(`/mall/product/${productId}`);
};

// 跳转到订单详情
const goToOrderDetail = (orderId) => {
  router.push(`/mall/user/order-details/${orderId}`);
};

// 跳转到支付页面
const goToPayment = (orderId) => {
  router.push(`/mall/payment/${orderId}`);
};



// 跳转到商城首页
const goToMall = () => {
  router.push('/mall');
};

// 取消订单
// 取消订单
const cancelOrder = async (orderId) => {
  if (!orderId) {
    Message.error('订单信息不完整，无法取消');
    return;
  }
  
  try {
    const result = await orderApi.cancelOrder({
      orderId: orderId,
      cancelReason: '商城用户取消订单'
    });
    if (result.code === 200 || result.code === 0 || result.success) {
      Message.success('订单取消成功');
      console.log('取消订单响应数据:', result);
    } else {
      Message.error(result.message || '取消订单失败');
    }
  } catch (err) {
    console.error('取消订单出错:', err);
    Message.error('取消订单失败，请稍后再试');
  } finally {
    loadOrders();
  }
};

// 提醒发货
const remindDelivery = async (orderId) => {
  try {
    // TODO: 实现提醒发货逻辑，需要添加相应的API
    Message.success('提醒发货成功');
    console.log('提醒发货成功');
  } catch (error) {
    Message.error('提醒发货失败，请稍后重试');
    console.error('提醒发货失败:', error);
  }
};

// 确认收货
const confirmReceipt = async (orderId) => {
  if (!orderId) {
    Message.error('订单信息不完整，无法确认收货');
    return;
  }
  
  try {
    const result = await orderApi.receiptOrder(orderId);
    if (result.code === 200) {
      Message.success(result.message || '确认收货成功');
      console.log('确认收货响应数据:', result);
      // 刷新订单列表
      loadOrders();
    } else {
      Message.error(result.message || '确认收货失败');
    }
  } catch (error) {
    console.error('确认收货失败:', error);
    Message.error('确认收货失败，请稍后重试');
  }
};

// 查看物流
const viewLogistics = async (orderId) => {
  try {
    // TODO: 实现查看物流逻辑，需要添加相应的API
    if (window.$message) {
      window.$message.info('查看物流功能开发中');
    }
  } catch (error) {
    console.error('查看物流失败:', error);
    if (window.$message) {
      window.$message.error('查看物流失败，请稍后重试');
    }
  }
};

// 去评价
const goToReview = (orderId) => {
  if (!orderId) {
    Message.error('订单信息不完整，无法进行评价');
    return;
  }

  // 跳转到评价页面
  router.push(`/mall/user/order-review/${orderId}`);
};

// 再次购买
const rebuy = async (orderId) => {
  try {
    // TODO: 实现再次购买逻辑，需要添加相应的API
    if (window.$message) {
      window.$message.info('再次购买功能开发中');
    }
  } catch (error) {
    console.error('再次购买失败:', error);
    if (window.$message) {
      window.$message.error('再次购买失败，请稍后重试');
    }
  }
};
</script>

<style scoped>
/* 订单列表页面特定样式 */
</style>
