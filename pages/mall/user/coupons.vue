<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/user/center" class="hover:text-red-600">会员中心</NuxtLink> &gt; 
      <span>我的优惠券</span>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载优惠券数据...</p>
      </div>
      
      <!-- 未登录状态提示 -->
      <div v-else-if="!userStore.loggedIn" class="p-8 text-center">
        <div class="mb-6">
          <img src="https://placehold.co/120x120/f5f5f5/333333?text=未登录" alt="未登录" class="mx-auto mb-4 rounded-full border-4 border-gray-200">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">您尚未登录</h2>
          <p class="text-gray-600 mb-6">登录后可查看您的优惠券信息</p>
          <div class="flex justify-center space-x-4">
            <NuxtLink to="/mall/login" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              立即登录
            </NuxtLink>
            <NuxtLink to="/mall/register" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition">
              注册账号
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- 已登录状态 - 优惠券中心 -->
      <div v-else class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <!-- 优惠券概览 -->
          <div class="mb-8">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-xl font-bold text-gray-800">我的优惠券</h2>
              <NuxtLink to="/mall/promotion/coupons" class="text-blue-600 hover:text-blue-700 text-sm flex items-center">
                <i class="i-carbon-add mr-1"></i>
                领取更多优惠券
              </NuxtLink>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- 可用优惠券 -->
              <div class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white shadow-lg">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="text-sm opacity-80 mb-1">可用优惠券</div>
                    <div class="text-3xl font-bold">{{ couponStats.available }}</div>
                  </div>
                  <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                    <i class="i-carbon-ticket text-2xl"></i>
                  </div>
                </div>
                <div class="mt-4 text-sm opacity-80">
                  可在商城购物时使用
                </div>
              </div>
              
              <!-- 即将过期 -->
              <div class="bg-gradient-to-r from-amber-500 to-amber-600 rounded-lg p-6 text-white shadow-lg">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="text-sm opacity-80 mb-1">即将过期</div>
                    <div class="text-3xl font-bold">{{ couponStats.expiring }}</div>
                  </div>
                  <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                    <i class="i-carbon-time text-2xl"></i>
                  </div>
                </div>
                <div class="mt-4 text-sm opacity-80">
                  7天内即将过期的优惠券
                </div>
              </div>
              
              <!-- 已使用/过期 -->
              <div class="bg-gradient-to-r from-gray-400 to-gray-500 rounded-lg p-6 text-white">
                <div class="flex justify-between items-center">
                  <div>
                    <div class="text-sm opacity-80 mb-1">已使用/已过期</div>
                    <div class="text-3xl font-bold">{{ couponStats.used + couponStats.expired }}</div>
                  </div>
                  <div class="w-12 h-12 rounded-full bg-white/20 flex items-center justify-center">
                    <i class="i-carbon-checkmark text-2xl"></i>
                  </div>
                </div>
                <div class="mt-4 text-sm opacity-80">
                  历史优惠券记录
                </div>
              </div>
            </div>
          </div>
          
          <!-- 优惠券分类标签 -->
          <div class="mb-6">
            <a-tabs v-model:active-key="activeTab" type="card-gutter">
              <a-tab-pane key="available" title="可用优惠券">
                <template #title>
                  <span class="flex items-center">
                    <i class="i-carbon-ticket mr-1"></i>
                    可用优惠券
                    <a-badge :count="couponStats.available" v-if="couponStats.available > 0" class="ml-2" />
                  </span>
                </template>
              </a-tab-pane>
              <a-tab-pane key="expiring" title="即将过期">
                <template #title>
                  <span class="flex items-center">
                    <i class="i-carbon-time mr-1"></i>
                    即将过期
                    <a-badge :count="couponStats.expiring" v-if="couponStats.expiring > 0" class="ml-2" />
                  </span>
                </template>
              </a-tab-pane>
              <a-tab-pane key="used" title="已使用">
                <template #title>
                  <span class="flex items-center">
                    <i class="i-carbon-checkmark mr-1"></i>
                    已使用
                  </span>
                </template>
              </a-tab-pane>
              <a-tab-pane key="expired" title="已过期">
                <template #title>
                  <span class="flex items-center">
                    <i class="i-carbon-close mr-1"></i>
                    已过期
                  </span>
                </template>
              </a-tab-pane>
            </a-tabs>
          </div>
          
          <!-- 优惠券列表 -->
          <div v-if="filteredCoupons.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div 
              v-for="coupon in filteredCoupons" 
              :key="coupon.id" 
              class="coupon-card relative overflow-hidden transition-all duration-300 hover:shadow-lg"
              :class="{'opacity-70': coupon.status !== 'available'}"
            >
              <!-- 优惠券状态标记 -->
              <div 
                v-if="coupon.status !== 'available'" 
                class="absolute top-0 right-0 w-20 h-20 overflow-hidden"
              >
                <div 
                  class="absolute top-0 right-0 transform rotate-45 translate-y-[-50%] translate-x-[50%] w-32 text-center py-1 text-white text-xs font-bold"
                  :class="{
                    'bg-gray-500': coupon.status === 'used',
                    'bg-red-500': coupon.status === 'expired'
                  }"
                >
                  {{ coupon.status === 'used' ? '已使用' : '已过期' }}
                </div>
              </div>
              
              <!-- 优惠券内容 -->
              <div 
                class="border rounded-lg overflow-hidden flex"
                :class="{
                  'border-red-200 bg-red-50': coupon.type === 'discount',
                  'border-blue-200 bg-blue-50': coupon.type === 'minus',
                  'border-green-200 bg-green-50': coupon.type === 'special'
                }"
              >
                <!-- 左侧金额 -->
                <div 
                  class="w-1/3 p-4 flex flex-col items-center justify-center text-white"
                  :class="{
                    'bg-gradient-to-br from-red-500 to-red-600': coupon.type === 'discount',
                    'bg-gradient-to-br from-blue-500 to-blue-600': coupon.type === 'minus',
                    'bg-gradient-to-br from-green-500 to-green-600': coupon.type === 'special'
                  }"
                >
                  <div class="text-sm mb-1">{{ getCouponTypeText(coupon.type) }}</div>
                  <div class="text-3xl font-bold">
                    <template v-if="coupon.type === 'discount'">{{ coupon.value }}折</template>
                    <template v-else-if="coupon.type === 'minus'">¥{{ coupon.value }}</template>
                    <template v-else>特惠</template>
                  </div>
                </div>
                
                <!-- 右侧详情 -->
                <div class="w-2/3 p-4">
                  <h3 class="font-medium text-gray-800 mb-1">{{ coupon.name }}</h3>
                  <div class="text-sm text-gray-600 mb-2">{{ coupon.description }}</div>
                  <div class="text-xs text-gray-600">
                    <div>使用条件：{{ coupon.condition }}</div>
                    <div>有效期至：{{ formatDate(coupon.expireDate) }}</div>
                  </div>
                  
                  <!-- 使用按钮 -->
                  <div v-if="coupon.status === 'available'" class="mt-3">
                    <NuxtLink 
                      :to="coupon.useUrl || '/mall'" 
                      class="inline-block px-4 py-1 text-sm rounded-full text-white"
                      :class="{
                        'bg-red-500 hover:bg-red-600': coupon.type === 'discount',
                        'bg-blue-500 hover:bg-blue-600': coupon.type === 'minus',
                        'bg-green-500 hover:bg-green-600': coupon.type === 'special'
                      }"
                    >
                      立即使用
                    </NuxtLink>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-else class="py-16 flex flex-col items-center justify-center">
            <i class="i-carbon-ticket text-6xl text-gray-300 mb-4"></i>
            <p class="text-gray-500">暂无{{ getEmptyText() }}</p>
            <NuxtLink to="/mall/promotion/coupons" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              去领取优惠券
            </NuxtLink>
          </div>
          
          <!-- 优惠券使用说明 -->
          <div class="mt-8">
            <h3 class="text-lg font-medium text-gray-800 mb-4">优惠券使用说明</h3>
            <a-collapse>
              <a-collapse-item header="优惠券类型说明" key="1">
                <p class="text-gray-600">
                  <strong>满减券：</strong>满足一定金额后可减免指定金额，如满100减10元<br>
                  <strong>折扣券：</strong>按商品金额的一定比例进行折扣，如8折<br>
                  <strong>特惠券：</strong>特定活动或商品专用，具体优惠以券面说明为准
                </p>
              </a-collapse-item>
              <a-collapse-item header="使用规则" key="2">
                <p class="text-gray-600">
                  1. 优惠券仅限在有效期内使用<br>
                  2. 部分优惠券有使用门槛，请查看具体优惠券的使用条件<br>
                  3. 每个订单限用一张优惠券，不可叠加使用<br>
                  4. 优惠券不可兑换现金，不设找零
                </p>
              </a-collapse-item>
              <a-collapse-item header="获取方式" key="3">
                <p class="text-gray-600">
                  1. 新用户注册赠送<br>
                  2. 商城活动领取<br>
                  3. 会员等级赠送<br>
                  4. 积分兑换<br>
                  5. 购物满额赠送
                </p>
              </a-collapse-item>
            </a-collapse>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useMallUserStore } from '~/store/mall/user';
import UserSideNav from '~/components/mall/user/UserSideNav.vue';
import { Message } from '@arco-design/web-vue';

definePageMeta({
  layout: 'mall'
});

const userStore = useMallUserStore();

// 状态变量
const isLoading = ref(true);
const activeTab = ref('available');

// 优惠券统计
const couponStats = reactive({
  available: 0,
  expiring: 0,
  used: 0,
  expired: 0
});

// 优惠券列表
const coupons = ref([]);

// 格式化日期
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 获取优惠券类型文本
const getCouponTypeText = (type) => {
  const typeMap = {
    'discount': '折扣券',
    'minus': '满减券',
    'special': '特惠券'
  };
  return typeMap[type] || '优惠券';
};

// 根据当前标签筛选优惠券
const filteredCoupons = computed(() => {
  if (activeTab.value === 'available') {
    return coupons.value.filter(coupon => coupon.status === 'available');
  } else if (activeTab.value === 'expiring') {
    // 获取7天内过期的优惠券
    const now = new Date();
    const sevenDaysLater = new Date(now);
    sevenDaysLater.setDate(now.getDate() + 7);
    
    return coupons.value.filter(coupon => {
      const expireDate = new Date(coupon.expireDate);
      return coupon.status === 'available' && expireDate <= sevenDaysLater && expireDate > now;
    });
  } else if (activeTab.value === 'used') {
    return coupons.value.filter(coupon => coupon.status === 'used');
  } else if (activeTab.value === 'expired') {
    return coupons.value.filter(coupon => coupon.status === 'expired');
  }
  
  return coupons.value;
});

// 获取空状态文本
const getEmptyText = () => {
  const textMap = {
    'available': '可用优惠券',
    'expiring': '即将过期的优惠券',
    'used': '已使用的优惠券',
    'expired': '已过期的优惠券'
  };
  return textMap[activeTab.value] || '优惠券';
};

// 加载优惠券数据
const loadCoupons = async () => {
  try {
    isLoading.value = true;
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // 模拟优惠券数据
    const mockCoupons = [
      {
        id: 1,
        name: '新人专享券',
        type: 'minus',
        value: 10,
        condition: '满100元可用',
        description: '新用户注册专享优惠',
        expireDate: '2025-05-16',
        status: 'available',
        useUrl: '/mall/category/new'
      },
      {
        id: 2,
        name: '春季促销折扣券',
        type: 'discount',
        value: 8.5,
        condition: '无门槛',
        description: '春季大促全场折扣',
        expireDate: '2025-04-30',
        status: 'available',
        useUrl: '/mall/promotion/spring'
      },
      {
        id: 3,
        name: '满减优惠券',
        type: 'minus',
        value: 50,
        condition: '满300元可用',
        description: '全品类通用',
        expireDate: '2025-04-20',
        status: 'available',
        useUrl: '/mall'
      },
      {
        id: 4,
        name: '数码专区优惠券',
        type: 'minus',
        value: 100,
        condition: '满1000元可用',
        description: '仅限数码产品使用',
        expireDate: '2025-05-10',
        status: 'available',
        useUrl: '/mall/category/digital'
      },
      {
        id: 5,
        name: '服装类目优惠券',
        type: 'discount',
        value: 7,
        condition: '满200元可用',
        description: '仅限服装类目使用',
        expireDate: '2025-04-18',
        status: 'available',
        useUrl: '/mall/category/clothing'
      },
      {
        id: 6,
        name: '会员专享券',
        type: 'special',
        value: 0,
        condition: '无门槛',
        description: '会员专享特惠商品',
        expireDate: '2025-06-30',
        status: 'available',
        useUrl: '/mall/vip'
      },
      {
        id: 7,
        name: '生日特惠券',
        type: 'minus',
        value: 30,
        condition: '满200元可用',
        description: '生日当月专享',
        expireDate: '2025-04-30',
        status: 'available',
        useUrl: '/mall'
      },
      {
        id: 8,
        name: '家电满减券',
        type: 'minus',
        value: 200,
        condition: '满2000元可用',
        description: '仅限家电类目使用',
        expireDate: '2025-03-15',
        status: 'expired',
        useUrl: '/mall/category/appliance'
      },
      {
        id: 9,
        name: '图书折扣券',
        type: 'discount',
        value: 5,
        condition: '满50元可用',
        description: '仅限图书类目使用',
        expireDate: '2025-05-20',
        status: 'used',
        useUrl: '/mall/category/books'
      },
      {
        id: 10,
        name: '美妆满减券',
        type: 'minus',
        value: 20,
        condition: '满150元可用',
        description: '仅限美妆类目使用',
        expireDate: '2025-02-28',
        status: 'expired',
        useUrl: '/mall/category/beauty'
      },
      {
        id: 11,
        name: '食品饮料券',
        type: 'minus',
        value: 15,
        condition: '满100元可用',
        description: '仅限食品饮料类目使用',
        expireDate: '2025-04-10',
        status: 'used',
        useUrl: '/mall/category/food'
      },
      {
        id: 12,
        name: '运动户外券',
        type: 'discount',
        value: 8,
        condition: '满300元可用',
        description: '仅限运动户外类目使用',
        expireDate: '2025-04-25',
        status: 'available',
        useUrl: '/mall/category/sports'
      }
    ];
    
    coupons.value = mockCoupons;
    
    // 计算优惠券统计数据
    const now = new Date();
    const sevenDaysLater = new Date(now);
    sevenDaysLater.setDate(now.getDate() + 7);
    
    couponStats.available = mockCoupons.filter(coupon => coupon.status === 'available').length;
    couponStats.used = mockCoupons.filter(coupon => coupon.status === 'used').length;
    couponStats.expired = mockCoupons.filter(coupon => coupon.status === 'expired').length;
    
    // 计算即将过期的优惠券数量
    couponStats.expiring = mockCoupons.filter(coupon => {
      const expireDate = new Date(coupon.expireDate);
      return coupon.status === 'available' && expireDate <= sevenDaysLater && expireDate > now;
    }).length;
    
  } catch (error) {
    console.error('加载优惠券数据失败:', error);
    Message.error('加载优惠券数据失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};

// 在组件挂载后加载数据
onMounted(async () => {
  // 尝试恢复用户会话
  if (!userStore.loggedIn) {
    await userStore.restoreSession();
  }
  
  // 加载优惠券数据
  await loadCoupons();
});
</script>

<style scoped>
/* 优惠券页面特定样式 */
.coupon-card {
  transition: all 0.3s ease;
}

.coupon-card:hover {
  transform: translateY(-5px);
}

:deep(.arco-tabs-nav) {
  margin-bottom: 0;
}

:deep(.arco-tabs-content) {
  padding: 16px 0;
}

:deep(.arco-collapse-item-header) {
  font-weight: 500;
}
</style>
