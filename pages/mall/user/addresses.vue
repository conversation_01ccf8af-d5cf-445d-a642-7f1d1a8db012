<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/user/center" class="hover:text-red-600">会员中心</NuxtLink> &gt; 
      <span>收货地址</span>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载收货地址信息...</p>
      </div>
      
      <!-- 未登录状态提示 -->
      <div v-else-if="!userStore.loggedIn" class="p-8 text-center">
        <div class="mb-6">
          <img src="https://placehold.co/120x120/ffffff/333333?text=未登录" alt="未登录" class="mx-auto mb-4 rounded-full border-4 border-gray-200">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">您尚未登录</h2>
          <p class="text-gray-600 mb-6">登录后可查看和管理您的收货地址</p>
          <div class="flex justify-center space-x-4">
            <NuxtLink to="/mall/login" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              立即登录
            </NuxtLink>
            <NuxtLink to="/mall/register" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition">
              注册账号
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- 已登录状态 - 收货地址管理 -->
      <div v-else class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <div class="flex justify-between items-center mb-6 pb-2 border-b">
            <h2 class="text-xl font-bold">收货地址管理</h2>
            <a-button type="primary" @click="showAddressModal = true">
              <template #icon><i class="i-carbon-add"></i></template>
              新增收货地址
            </a-button>
          </div>
          
          <!-- 地址列表 -->
          <div v-if="addresses.length > 0" class="address-list grid grid-cols-2 gap-4">
            <div v-for="(address, index) in addresses" :key="index" 
                 class="address-card bg-white p-4 rounded-lg border border-gray-200 hover:shadow-md transition relative">
              <!-- 默认地址标记 -->
              <div v-if="address.isDefault" class="absolute top-0 right-0 bg-red-500 text-white text-xs px-2 py-1 rounded-bl-lg">
                默认地址
              </div>
              
              <!-- 地址信息 -->
              <div class="mb-3">
                <div class="flex items-center mb-2">
                  <span class="font-medium text-lg">{{ address.name }}</span>
                  <span class="ml-3 text-gray-600">{{ address.phone }}</span>
                </div>
                <div class="text-gray-700">
                  {{ address.province }} {{ address.city }} {{ address.district }} {{ address.address }}
                </div>
                <div v-if="address.postcode" class="text-gray-500 text-sm mt-1">
                  邮政编码: {{ address.postcode }}
                </div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="flex justify-between items-center mt-4 pt-3 border-t border-gray-100">
                <div>
                  <a-switch 
                    v-model="address.isDefault" 
                    :disabled="address.isDefault"
                    @change="(checked) => handleSetDefault(index, checked)">
                    <template #checked>默认</template>
                    <template #unchecked>设为默认</template>
                  </a-switch>
                </div>
                <div class="flex space-x-2">
                  <a-button size="small" @click="handleEdit(index)">
                    <template #icon><i class="i-carbon-edit"></i></template>
                    编辑
                  </a-button>
                  <a-popconfirm
                    content="确定要删除这个地址吗？"
                    @ok="handleDelete(index)"
                    position="top"
                    ok-text="确定"
                    cancel-text="取消"
                  >
                    <a-button size="small" status="danger">
                      <template #icon><i class="i-carbon-trash-can"></i></template>
                      删除
                    </a-button>
                  </a-popconfirm>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-else class="empty-state p-8 text-center bg-gray-50 rounded-lg border border-dashed border-gray-300">
            <p class="text-gray-600 mb-4">您还没有添加收货地址</p>
            <a-button type="primary" @click="showAddressModal = true">
              <template #icon><i class="i-carbon-add"></i></template>
              添加收货地址
            </a-button>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 地址编辑模态框 -->
  <a-modal
    v-model:visible="showAddressModal"
    :title="editingIndex === -1 ? '新增收货地址' : '编辑收货地址'"
    @cancel="resetAddressForm"
    @before-ok="handleAddressSubmit"
  >
    <a-form ref="addressFormRef" :model="addressForm" layout="vertical" :rules="addressRules">
      <a-form-item field="name" label="收货人" validate-trigger="blur" required>
        <a-input v-model="addressForm.name" placeholder="请输入收货人姓名" allow-clear />
        <template #extra><span class="text-red-500">*</span> 必填项</template>
      </a-form-item>
      
      <a-form-item field="phone" label="手机号码" validate-trigger="blur" required>
        <a-input v-model="addressForm.phone" placeholder="请输入手机号码" allow-clear />
        <template #extra><span class="text-red-500">*</span> 必填项，请输入正确的11位手机号码</template>
      </a-form-item>
      
      <a-form-item label="所在地区" required>
        <a-space>
          <a-select v-model="addressForm.province" placeholder="省份" style="width: 120px" @change="handleProvinceChange">
            <a-option v-for="province in provinces" :key="province" :value="province">{{ province }}</a-option>
          </a-select>
          <a-select v-model="addressForm.city" placeholder="城市" style="width: 120px" @change="handleCityChange" :disabled="!addressForm.province">
            <a-option v-for="city in cities" :key="city" :value="city">{{ city }}</a-option>
          </a-select>
          <a-select v-model="addressForm.district" placeholder="区/县" style="width: 120px" @change="validateRegion" :disabled="!addressForm.city">
            <a-option v-for="district in districts" :key="district" :value="district">{{ district }}</a-option>
          </a-select>
        </a-space>
        <div v-if="regionError" class="text-red-500 text-xs mt-1">{{ regionError }}</div>
        <template #extra><span class="text-red-500">*</span> 必填项，请选择完整的省市区信息</template>
      </a-form-item>
      
      <a-form-item field="address" label="详细地址" validate-trigger="blur" required>
        <a-textarea v-model="addressForm.address" placeholder="请输入详细地址，如街道、门牌号等" allow-clear />
        <template #extra><span class="text-red-500">*</span> 必填项</template>
      </a-form-item>
      
      <a-form-item field="postcode" label="邮政编码">
        <a-input v-model="addressForm.postcode" placeholder="请输入邮政编码（选填）" allow-clear />
      </a-form-item>
      
      <a-form-item>
        <a-checkbox v-model="addressForm.isDefault">
          设为默认收货地址
        </a-checkbox>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import UserSideNav from '~/components/mall/user/UserSideNav.vue';
import { useMallUserStore } from '~/store/mall/user';
import mallApi from '~/api/mall';

definePageMeta({
  layout: 'mall'
});

// 用户状态管理
const userStore = useMallUserStore();

// 页面状态
const isLoading = ref(true);
const addresses = ref([]);
const showAddressModal = ref(false);
const editingIndex = ref(-1);

// 地址表单
const addressFormRef = ref(null);
const addressForm = reactive({
  name: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  address: '',
  postcode: '',
  isDefault: false
});

// 表单验证规则
const addressRules = {
  name: [
    { required: true, message: '请输入收货人姓名' },
  ],
  phone: [
    { required: true, message: '请输入手机号码' },
    { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式' }
  ],
  address: [
    { required: true, message: '请输入详细地址' },
  ]
};

// 验证地区选择
const validateRegion = () => {
  if (!addressForm.province || !addressForm.city || !addressForm.district) {
    regionError.value = '请选择完整的所在地区';
  } else {
    regionError.value = '';
  }
};

// 地区数据
const regionData = ref([]);
const provinces = ref([]);
const cities = ref([]);
const districts = ref([]);
const regionError = ref('');

// 加载省市区数据
const loadRegionData = async () => {
  try {
    const result = await mallApi.address.address.getRegionData();
    
    if (result.code === 200 && result.data) {
      regionData.value = result.data;
      // 提取省份列表
      provinces.value = regionData.value.map(province => province.name);
    } else {
      console.error('获取省市区数据失败:', result.message);
    }
  } catch (error) {
    console.error('获取省市区数据异常:', error);
  }
};

// 处理省份变化
const handleProvinceChange = (province, keepValues = false) => {
  // 如果不是编辑模式（keepValues = false），才清空 city 和 district
  if (!keepValues) {
    addressForm.city = '';
    addressForm.district = '';
  }
  
  // 根据选择的省份获取城市列表
  const selectedProvince = regionData.value.find(p => p.name === province);
  if (selectedProvince && selectedProvince.children) {
    cities.value = selectedProvince.children.map(city => city.name);
  } else {
    cities.value = [];
  }
  
  // 验证地区选择
  validateRegion();
};

// 处理城市变化
const handleCityChange = (city, keepValues = false) => {
  // 如果不是编辑模式（keepValues = false），才清空 district
  if (!keepValues) {
    addressForm.district = '';
  }
  
  // 根据选择的省份和城市获取区县列表
  const selectedProvince = regionData.value.find(p => p.name === addressForm.province);
  if (selectedProvince && selectedProvince.children) {
    const selectedCity = selectedProvince.children.find(c => c.name === city);
    if (selectedCity && selectedCity.children) {
      districts.value = selectedCity.children.map(district => district.name);
    } else {
      districts.value = [];
    }
  } else {
    districts.value = [];
  }
  
  // 验证地区选择
  validateRegion();
};

// 加载地址数据
const loadAddresses = async () => {
  try {
    isLoading.value = true;
    
    // 判断用户是否登录
    if (!userStore.loggedIn) {
      isLoading.value = false;
      return;
    }
    
    // 调用API获取地址列表
    const result = await mallApi.address.address.getList();
    
    if (result.code === 200 && result.data) {
      // 将后端返回的数据格式转换为前端使用的格式
      addresses.value = result.data.map(item => ({
        id: item.id,
        name: item.name,
        phone: item.phone,
        province: item.province,
        city: item.city,
        district: item.district,
        address: item.address,
        postcode: item.postcode || '',
        isDefault: item.is_default
      }));
      // userStore.updateAddresses(addresses.value);
    } else {
      console.error('获取地址列表失败:', result.message);
      if (result.code === 401) {
        Message.error('登录已过期，请重新登录');
        // 可以在这里处理登录过期的情况
      } else {
        Message.error(result.message || '获取地址列表失败');
      }
    }
  } catch (error) {
    console.error('加载地址失败:', error);
    Message.error('加载地址失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};

// 重置地址表单
const resetAddressForm = () => {
  Object.assign(addressForm, {
    name: '',
    phone: '',
    province: '',
    city: '',
    district: '',
    address: '',
    postcode: '',
    isDefault: false
  });
  editingIndex.value = -1;
};

// 处理编辑地址
const handleEdit = (index) => {
  const address = addresses.value[index];
  Object.assign(addressForm, {
    name: address.name,
    phone: address.phone,
    province: address.province,
    city: address.city,
    district: address.district,
    address: address.address,
    postcode: address.postcode || '',
    isDefault: address.isDefault
  });
  
  // 设置城市和区县选项，传入 keepValues = true 参数保留已选值
  handleProvinceChange(address.province, true);
  if (address.city) {
    handleCityChange(address.city, true);
  }
  
  editingIndex.value = index;
  showAddressModal.value = true;
};

// 处理删除地址
const handleDelete = async (index) => {
  console.log('开始删除地址，索引：', index);
  try {
    const deletedAddress = addresses.value[index];
    console.log('要删除的地址信息：', deletedAddress);
    console.log('地址ID：', deletedAddress.id);
    
    // 检查 mallApi 是否存在
    console.log('mallApi是否存在：', !!mallApi);
    console.log('mallApi.address是否存在：', !!mallApi?.address);
    console.log('mallApi.address.address是否存在：', !!mallApi?.address?.address);
    console.log('mallApi.address.address.delete是否存在：', !!mallApi?.address?.address?.delete);
    
    // 调用API删除地址
    console.log('准备调用删除API...');
    const result = await mallApi.address.address.delete(deletedAddress.id);
    console.log('删除API返回结果：', result);
    
    if (result.code === 200) {
      console.log('删除成功，从本地列表中移除');
      // 从本地列表中移除
      addresses.value.splice(index, 1);
      
      // 地址列表信息不需要存储在本地
      // userStore.updateAddresses(addresses.value); // 已移除
      
      Message.success(result.message || '删除地址成功');
      
      // 重新加载地址列表，确保与后端数据同步
      console.log('重新加载地址列表...');
      await loadAddresses();
      console.log('地址列表已重新加载');
    } else {
      console.log('删除失败，错误信息：', result.message);
      Message.error(result.message || '删除地址失败');
    }
  } catch (error) {
    console.error('删除地址失败，错误详情：', error);
    Message.error('删除地址失败，请稍后再试');
  }
};

// 处理设置默认地址
const handleSetDefault = async (index, checked) => {
  if (!checked) return;
  
  try {
    const address = addresses.value[index];
    
    // 调用API设置默认地址
    const result = await mallApi.address.address.setDefault(address.id);
    
    if (result.code === 200) {
      // 取消其他地址的默认状态
      addresses.value.forEach((addr, i) => {
        addr.isDefault = i === index;
      });
      
      // 地址列表信息不需要存储在本地
      // userStore.updateAddresses(addresses.value); // 已移除
      
      Message.success(result.message || '设置默认地址成功');
    } else {
      // 恢复原状态
      address.isDefault = false;
      Message.error(result.message || '设置默认地址失败');
    }
  } catch (error) {
    console.error('设置默认地址失败:', error);
    Message.error('设置默认地址失败，请稍后再试');
    // 恢复原状态
    addresses.value[index].isDefault = false;
  }
};

// 处理提交地址表单
const handleAddressSubmit = async () => {
  try {
    // 表单验证
    // 1. 验证收货人姓名
    if (!addressForm.name || addressForm.name.trim() === '') {
      Message.error('请输入收货人姓名');
      return false;
    }
    
    // 2. 验证手机号码
    if (!addressForm.phone) {
      Message.error('请输入手机号码');
      return false;
    }
    
    // 3. 验证手机号码格式
    if (!/^1[3-9]\d{9}$/.test(addressForm.phone)) {
      Message.error('请输入正确的手机号码');
      return false;
    }
    
    // 4. 验证省市区选择
    if (!addressForm.province || !addressForm.city || !addressForm.district) {
      Message.error('请选择完整的所在地区');
      return false;
    }
    
    // 5. 验证详细地址
    if (!addressForm.address || addressForm.address.trim() === '') {
      Message.error('请输入详细地址');
      return false;
    }
    
    // 准备请求数据
    const addressData = {
      name: addressForm.name,
      phone: addressForm.phone,
      province: addressForm.province,
      city: addressForm.city,
      district: addressForm.district,
      address: addressForm.address,
      postcode: addressForm.postcode || '',
      isDefault: addressForm.isDefault
    };
    
    let result;
    
    if (editingIndex.value === -1) {
      // 新增地址 - 调用创建接口
      result = await mallApi.address.address.create(addressData);
    } else {
      // 编辑地址 - 调用更新接口
      const addressId = addresses.value[editingIndex.value].id;
      result = await mallApi.address.address.update(addressId, addressData);
    }
    
    if ((result.code === 200 || result.code === 201) && result.data) {
      // 重新加载地址列表
      await loadAddresses();
      
      // 重置表单
      resetAddressForm();
      
      Message.success(result.message || (editingIndex.value === -1 ? '添加地址成功' : '更新地址成功'));
      return true;
    } else {
      Message.error(result.message || '保存地址失败');
      return false;
    }
  } catch (error) {
    console.error('保存地址失败:', error);
    Message.error('保存地址失败，请稍后再试');
    return false;
  }
};

// 在组件挂载后加载数据
onMounted(async () => {
  // 尝试恢复用户会话
  if (!userStore.loggedIn) {
    await userStore.restoreSession();
  }
  
  // 加载省市区数据
  await loadRegionData();
  
  // 加载地址数据
  await loadAddresses();
});
</script>

<style scoped>
.address-card {
  transition: all 0.3s ease;
}

.address-card:hover {
  border-color: #3b82f6;
}
</style>
