<template>
  <div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex">
        <!-- 左侧图片区域 -->
        <div class="w-1/2 bg-gradient-to-r from-blue-500 to-blue-600 flex flex-col justify-center items-center p-8 text-white">
          <img src="https://placehold.co/400x300/ffffff/333333?text=会员注册" alt="注册插图" class="w-full max-w-xs mb-6 rounded-lg shadow-lg">
          <h2 class="text-2xl font-bold mb-4">加入我们</h2>
          <p class="text-center mb-6">注册成为会员，享受更多优惠和专属服务</p>
          <div class="grid grid-cols-2 gap-4 w-full">
            <div class="bg-white/10 p-3 rounded-lg">
              <div class="font-bold mb-1">会员折扣</div>
              <div class="text-sm">享受会员专属价格和折扣</div>
            </div>
            <div class="bg-white/10 p-3 rounded-lg">
              <div class="font-bold mb-1">生日礼包</div>
              <div class="text-sm">生日当天送您专属礼包</div>
            </div>
            <div class="bg-white/10 p-3 rounded-lg">
              <div class="font-bold mb-1">积分奖励</div>
              <div class="text-sm">消费即可获得积分兑换奖品</div>
            </div>
            <div class="bg-white/10 p-3 rounded-lg">
              <div class="font-bold mb-1">企业采购</div>
              <div class="text-sm">企业会员享受采购特权</div>
            </div>
          </div>
        </div>
        
        <!-- 右侧注册表单 -->
        <div class="w-1/2 p-8">
          <h2 class="text-2xl font-bold text-gray-800 mb-6">会员注册</h2>
        
        <!-- 注册成功提示 -->
        <div v-if="registerSuccess" class="mb-4 p-3 bg-green-50 text-green-600 rounded-md text-sm">
          {{ registerSuccess }}
        </div>
        
        <!-- 注册失败提示 -->
        <div v-if="registerError" class="mb-4 p-3 bg-red-50 text-red-600 rounded-md text-sm">
          {{ registerError }}
        </div>
        
        <!-- 注册表单 -->
        <form @submit.prevent="handleRegister">
          <div class="mb-4">
            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">手机号码</label>
            <input 
              type="tel" 
              id="phone" 
              v-model="registerForm.phone" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请输入手机号码"
              required
              pattern="^1[3-9]\d{9}$"
            >
            <p v-if="errors.phone" class="mt-1 text-sm text-red-600">{{ errors.phone }}</p>
          </div>
          
          <div class="mb-4">
            <label for="verificationCode" class="block text-sm font-medium text-gray-700 mb-1">验证码</label>
            <div class="flex">
              <input 
                type="text" 
                id="verificationCode" 
                v-model="registerForm.verificationCode" 
                class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入验证码"
                required
              >
              <button 
                type="button" 
                class="w-32 bg-gray-100 text-gray-700 py-2 px-4 border border-gray-300 rounded-r-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                @click="sendVerificationCode"
                :disabled="cooldown > 0"
              >
                {{ cooldown > 0 ? `${cooldown}秒后重发` : '获取验证码' }}
              </button>
            </div>
            <p v-if="errors.verificationCode" class="mt-1 text-sm text-red-600">{{ errors.verificationCode }}</p>
            <p v-if="verificationCodeSent" class="mt-1 text-xs text-green-600">
              {{ verificationCodeSent }}
            </p>
          </div>
          
          <div class="mb-4">
            <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
            <input 
              type="text" 
              id="username" 
              v-model="registerForm.username" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请设置用户名"
              required
              minlength="4"
              maxlength="20"
            >
            <p v-if="errors.username" class="mt-1 text-sm text-red-600">{{ errors.username }}</p>
          </div>
          
          <div class="mb-4">
            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">密码</label>
            <input 
              type="password" 
              id="password" 
              v-model="registerForm.password" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请设置密码"
              required
              minlength="6"
            >
            <p v-if="errors.password" class="mt-1 text-sm text-red-600">{{ errors.password }}</p>
          </div>
          
          <div class="mb-6">
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">确认密码</label>
            <input 
              type="password" 
              id="confirmPassword" 
              v-model="registerForm.confirmPassword" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="请再次输入密码"
              required
            >
            <p v-if="errors.confirmPassword" class="mt-1 text-sm text-red-600">{{ errors.confirmPassword }}</p>
          </div>
          
          <div class="mb-6">
            <div class="flex items-center">
              <input 
                type="checkbox" 
                id="agreement" 
                v-model="registerForm.agreement" 
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                required
              >
              <label for="agreement" class="ml-2 block text-sm text-gray-700">
                我已阅读并同意
                <NuxtLink to="#" class="text-blue-600 hover:text-blue-800">《用户协议》</NuxtLink>
                和
                <NuxtLink to="#" class="text-blue-600 hover:text-blue-800">《隐私政策》</NuxtLink>
              </label>
            </div>
            <p v-if="errors.agreement" class="mt-1 text-sm text-red-600">{{ errors.agreement }}</p>
          </div>
          
          <div class="mb-6">
            <button 
              type="submit" 
              class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              :disabled="isLoading"
            >
              {{ isLoading ? '注册中...' : '立即注册' }}
            </button>
          </div>
          
          <div class="text-center">
            <p class="text-sm text-gray-600">
              已有账号? 
              <NuxtLink to="/mall/login" class="text-blue-600 hover:text-blue-800">立即登录</NuxtLink>
            </p>
          </div>
        </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';

definePageMeta({
  layout: 'mall'
});

const router = useRouter();
const userStore = useMallUserStore();

// 在组件挂载后检查是否已经登录
onMounted(async () => {
  try {
    // 初始化用户状态
    userStore.initUserState();
    
    // 如果用户已登录，跳转到首页
    if (userStore.loggedIn) {
      router.push('/mall');
    }
  } catch (error) {
    console.error('检查登录状态失败:', error);
  }
});

// 注册表单数据
const registerForm = reactive({
  phone: '',
  verificationCode: '',
  username: '',
  password: '',
  confirmPassword: '',
  agreement: false
});

// 表单错误信息
const errors = reactive({
  phone: '',
  username: '',
  password: '',
  confirmPassword: '',
  agreement: '',
  verificationCode: ''
});

// 加载状态
const isLoading = ref(false);
const cooldown = ref(0);
const verificationCodeSent = ref('');
const registerSuccess = ref('');
const registerError = ref('');

// 发送验证码
const sendVerificationCode = async () => {
  // 验证手机号
  if (!validatePhone()) {
    return;
  }
  
  try {
    // 调用发送验证码接口
    const result = await userStore.getCaptcha(registerForm.phone);
    
    if (result.success) {
      // 开始倒计时
      cooldown.value = 60;
      const timer = setInterval(() => {
        cooldown.value--;
        if (cooldown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
      
      // 显示发送成功提示
      verificationCodeSent.value = result.message || '验证码已发送到您的手机';
    } else {
      errors.phone = result.message || '发送验证码失败，请稍后再试';
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    errors.phone = '发送验证码失败，请稍后再试';
  }
};

// 验证手机号
const validatePhone = () => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(registerForm.phone)) {
    errors.phone = '请输入正确的手机号码';
    return false;
  }
  errors.phone = '';
  return true;
};

// 验证表单
const validateForm = () => {
  let isValid = true;
  
  // 验证手机号
  if (!validatePhone()) {
    isValid = false;
  }
  
  // 验证用户名
  if (registerForm.username.length < 4 || registerForm.username.length > 20) {
    errors.username = '用户名长度应为4-20个字符';
    isValid = false;
  } else {
    errors.username = '';
  }
  
  // 验证密码
  if (registerForm.password.length < 6) {
    errors.password = '密码长度不能少于6个字符';
    isValid = false;
  } else {
    errors.password = '';
  }
  
  // 验证确认密码
  if (registerForm.password !== registerForm.confirmPassword) {
    errors.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  } else {
    errors.confirmPassword = '';
  }
  
  // 验证验证码
  if (!registerForm.verificationCode) {
    errors.verificationCode = '请输入验证码';
    isValid = false;
  } else {
    errors.verificationCode = '';
  }
  
  // 验证协议勾选
  if (!registerForm.agreement) {
    errors.agreement = '请阅读并同意用户协议和隐私政策';
    isValid = false;
  } else {
    errors.agreement = '';
  }
  
  return isValid;
};

// 处理注册
const handleRegister = async () => {
  // 清除之前的提示
  registerSuccess.value = '';
  registerError.value = '';
  
  // 验证表单
  if (!validateForm()) {
    return;
  }
  
  try {
    isLoading.value = true;
    
    // 准备注册数据
    const registerData = {
      username: registerForm.username,
      password: registerForm.password,
      phone: registerForm.phone,
      captcha: registerForm.verificationCode,
      nickname: registerForm.username // 默认昵称与用户名相同
    };
    
    // 使用store中的注册方法
    const result = await userStore.register(registerData);
    
    if (result.success) {
      registerSuccess.value = '注册成功！即将跳转到登录页面...';
      
      // 注册成功后跳转到登录页面
      setTimeout(() => {
        router.push({
          path: '/mall/login',
          query: { registered: 'success', username: registerForm.username }
        });
      }, 1500);
    } else {
      registerError.value = result.message || '注册失败，请稍后再试';
    }
  } catch (error) {
    console.error('注册失败:', error);
    registerError.value = '注册失败，请稍后再试';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* 注册页面特定样式 */
</style>
