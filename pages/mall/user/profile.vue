<template>
  <div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-800">个人资料</h2>
            <p class="text-gray-500 text-sm mt-1">您可以在这里修改您的个人资料信息</p>
          </div>

          <!-- 成功提示 -->
          <div v-if="successMessage" class="mb-6 p-4 bg-green-50 text-green-700 rounded-md">
            {{ successMessage }}
          </div>

          <!-- 错误提示 -->
          <div v-if="errorMessage" class="mb-6 p-4 bg-red-50 text-red-700 rounded-md">
            {{ errorMessage }}
          </div>

          <div class="grid grid-cols-3 gap-8">
            <!-- 左侧头像上传 -->
            <div class="col-span-1">
              <div class="flex flex-col items-center">
                <div class="mb-4 relative">
                  <img :src="form.avatar || 'https://placehold.co/200x200/ffffff/333333?text=头像'" 
                       alt="用户头像" 
                       class="w-40 h-40 rounded-full object-cover border-4 border-gray-200">
                  <div class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                    <div class="bg-black/50 rounded-full w-full h-full flex items-center justify-center">
                      <button type="button" 
                              class="text-white text-sm bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded-md"
                              @click="openAvatarUpload">
                        更换头像
                      </button>
                    </div>
                  </div>
                </div>
                <p class="text-sm text-gray-500 text-center">
                  点击更换头像<br>支持 JPG、PNG、GIF、WEBP 格式，最大 2MB
                </p>
                <input type="file" 
                       ref="avatarInput" 
                       accept="image/jpeg,image/png,image/gif,image/webp" 
                       class="hidden" 
                       @change="handleAvatarChange">
              </div>
            </div>

            <!-- 右侧内容 -->
            <div class="col-span-2">
              <!-- 昵称输入框（表单外部） -->
              <div class="mb-4">
                <label for="nickname" class="block text-sm font-medium text-gray-700 mb-1">昵称</label>
                <div class="flex">
                  <input 
                    type="text"
                    id="nickname" 
                    v-model="form.nickname" 
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入昵称"
                    maxlength="20"
                  >
                  <button 
                    type="button"
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-r-md transition-colors"
                    :disabled="isNicknameUpdating || !form.nickname"
                    @click.stop.prevent="updateNickname"
                  >
                    {{ isNicknameUpdating ? '保存中...' : '保存' }}
                  </button>
                </div>
              </div>
              
              <!-- 其他表单内容 -->
              <form @submit.prevent="saveProfile">

                <div class="mb-4" v-show="false">
                  <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">性别</label>
                  <div class="flex items-center space-x-4">
                    <label class="inline-flex items-center">
                      <input type="radio" v-model="form.gender" value="male" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                      <span class="ml-2 text-gray-700">男</span>
                    </label>
                    <label class="inline-flex items-center">
                      <input type="radio" v-model="form.gender" value="female" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                      <span class="ml-2 text-gray-700">女</span>
                    </label>
                    <label class="inline-flex items-center">
                      <input type="radio" v-model="form.gender" value="secret" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                      <span class="ml-2 text-gray-700">保密</span>
                    </label>
                  </div>
                </div>

                <div class="mb-4" v-show="false">
                  <label for="birthday" class="block text-sm font-medium text-gray-700 mb-1">生日</label>
                  <input 
                    type="date" 
                    id="birthday" 
                    v-model="form.birthday" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                </div>

                <div class="mb-4">
                  <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">手机号码</label>
                  <div class="flex">
                    <input 
                      type="tel" 
                      id="phone" 
                      v-model="form.phone" 
                      class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="请输入手机号码"
                      readonly
                    >
                    <button 
                      type="button" 
                      class="w-32 bg-gray-100 text-gray-700 py-2 px-4 border border-gray-300 rounded-r-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      @click="goToSecurityPage"
                    >
                      修改绑定
                    </button>
                  </div>
                </div>

                <div class="mb-4">
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                  <div class="flex">
                    <input 
                      type="email" 
                      id="email" 
                      v-model="form.email" 
                      class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="请输入邮箱"
                      :readonly="form.emailVerified"
                    >
                    <button 
                      type="button" 
                      class="w-32 bg-gray-100 text-gray-700 py-2 px-4 border border-gray-300 rounded-r-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      @click="goToSecurityPage"
                    >
                      {{ form.emailVerified ? '修改绑定' : '绑定邮箱' }}
                    </button>
                  </div>
                </div>

                <div class="mb-6" v-show="false">
                  <label for="bio" class="block text-sm font-medium text-gray-700 mb-1">个人简介</label>
                  <textarea 
                    id="bio" 
                    v-model="form.bio" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                    placeholder="请输入您的个人简介"
                    rows="4"
                    maxlength="200"
                  ></textarea>
                  <div class="text-right text-sm text-gray-500 mt-1">{{ form.bio.length }}/200</div>
                </div>

<!--                <div class="flex justify-end">-->
<!--                  <button -->
<!--                    type="button" -->
<!--                    class="mr-4 px-6 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"-->
<!--                    @click="resetForm"-->
<!--                  >-->
<!--                    取消-->
<!--                  </button>-->
<!--                  <button -->
<!--                    type="submit" -->
<!--                    class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"-->
<!--                    :disabled="isLoading"-->
<!--                  >-->
<!--                    {{ isLoading ? '保存中...' : '保存修改' }}-->
<!--                  </button>-->
<!--                </div>-->
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';
import { getUserMessages } from '~/mock/mall/userCenter';
import UserSideNav from '~/components/mall/user/UserSideNav.vue'; // 确保导入UserSideNav组件
import userApi from '~/api/mall/user';

definePageMeta({
  layout: 'mall'
});

const router = useRouter();
const userStore = useMallUserStore();
const avatarInput = ref(null);

// 表单数据
const form = reactive({
  nickname: '',
  avatar: '',
  gender: 'secret',
  birthday: '',
  phone: '',
  email: '',
  emailVerified: false,
  bio: ''
});

// 状态变量
const isLoading = ref(false);
const isNicknameUpdating = ref(false);
const isAvatarUploading = ref(false);
const avatarPreview = ref('');
const successMessage = ref('');
const errorMessage = ref('');
const unreadMessages = ref(0);

// 在组件挂载后加载数据
onMounted(async () => {
  try {
    // 检查用户是否已登录
    if (!userStore.loggedIn) {
      // 未登录则跳转到登录页
      router.push('/mall/login');
      return;
    }

    // 获取用户ID
    const userId = userStore.user?.id || 1001; // 使用模拟用户ID

    // 加载用户信息
    form.nickname = userStore.user.nickname || '';
    form.avatar = userStore.user.avatar || '';
    form.phone = userStore.user?.phone || '';
    form.email = userStore.user?.email || '';
    form.emailVerified = userStore.user?.emailVerified || false;
    form.gender = userStore.user?.gender || 'secret';
    form.birthday = userStore.user?.birthday || '';
    form.bio = userStore.user?.bio || '';

    // 加载未读消息数量
    const unreadMsgs = getUserMessages(userId, false);
    unreadMessages.value = unreadMsgs.length;

  } catch (error) {
    console.error('加载个人资料数据失败:', error);
    errorMessage.value = '加载个人资料数据失败，请稍后再试';
  }
});

// 打开头像上传
const openAvatarUpload = () => {
  avatarInput.value.click();
};

// 处理头像变更
const handleAvatarChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // 清除消息
  successMessage.value = '';
  errorMessage.value = '';
  
  // 检查文件类型
  if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
    errorMessage.value = '只支持 JPG、PNG、GIF、WEBP 格式的图片';
    return;
  }
  
  // 检查文件大小
  if (file.size > 2 * 1024 * 1024) {
    errorMessage.value = '图片大小不能超过 2MB';
    return;
  }
  
  // 读取文件并预览
  const reader = new FileReader();
  reader.onload = (e) => {
    avatarPreview.value = e.target.result;
  };
  reader.readAsDataURL(file);
  
  // 上传头像
  isAvatarUploading.value = true;
  
  try {
    // 第一步：上传文件
    console.log('开始上传头像文件...');
    const response = await userApi.profile.uploadAvatar(file);
    console.log('上传头像响应:', response);
    
    // 判断上传是否成功
    if (response.code !== 0 && response.code !== 200) {
      // 上传失败
      console.error('上传头像失败，状态码:', response.code, '消息:', response.message);
      errorMessage.value = '上传头像失败: ' + (response.message || '未知错误');
      return;
    }
    
    // 打印完整的响应数据结构
    console.log('完整响应数据:', JSON.stringify(response, null, 2));
    
    // 获取文件URL
    if (!response.data || !response.data.fileUrl) {
      console.error('响应数据中没有fileUrl字段');
      errorMessage.value = '获取上传文件地址失败';
      return;
    }
    
    const fileUrl = response.data.fileUrl;
    console.log('获取到的文件URL:', fileUrl);
    
    // 检查fileUrl是否为占位符“string”
    if (fileUrl === 'string') {
      console.error('文件URL是占位符“string”，不是真实URL');
      errorMessage.value = '获取上传文件地址失败';
      return;
    }
    
    // 更新头像地址（临时存储，最终会被服务器返回的值覆盖）
    form.avatar = fileUrl;
    
    // 发送请求到后端更新用户头像
    try {
      console.log('开始更新用户头像，使用URL:', fileUrl);
      
      // 使用新的更改头像接口
      const updateResponse = await userApi.profile.updateAvatar(fileUrl);
      console.log('更新头像响应:', updateResponse);
      
      if (updateResponse.code === 200 || updateResponse.code === 0) {
        console.log('头像更新成功!');
        
        // 如果返回了完整的用户信息，则更新本地存储
        if (updateResponse.data && updateResponse.data.user) {
          try {
            // 从本地存储中读取用户信息
            const userStr = localStorage.getItem('mall_user');
            if (userStr) {
              // 使用返回的完整用户信息更新本地存储
              localStorage.setItem('mall_user', JSON.stringify(updateResponse.data.user));
              console.log('使用返回的完整用户信息更新本地存储');
              
              // 同时更新 store 中的信息
              userStore.user = updateResponse.data.user;
              userStore.avatar = updateResponse.data.user.avatar;
              // 更新表单数据
              form.avatar = updateResponse.data.user.avatar;
            }
          } catch (err) {
            console.error('更新本地用户信息失败:', err);
            // 如果更新失败，仍然更新头像
            form.avatar = fileUrl;
            userStore.avatar = fileUrl;
            if (userStore.user) {
              userStore.user.avatar = fileUrl;
            }
          }
        } else {
          // 如果没有返回完整用户信息，仅更新头像
          console.log('未返回完整用户信息，仅更新头像');
          // 从本地存储中读取用户信息
          try {
            const userStr = localStorage.getItem('mall_user');
            if (userStr) {
              const userData = JSON.parse(userStr);
              // 更新头像
              userData.avatar = fileUrl;
              // 保存回本地存储
              localStorage.setItem('mall_user', JSON.stringify(userData));
              console.log('本地用户信息头像已更新');
              
              // 同时更新 store 中的信息
              form.avatar = fileUrl;
              userStore.avatar = fileUrl;
              if (userStore.user) {
                userStore.user.avatar = fileUrl;
              }
            }
          } catch (err) {
            console.error('更新本地用户信息失败:', err);
          }
        }
        
        successMessage.value = '头像上传成功';
      } else {
        console.warn('更新用户头像信息返回非成功状态:', updateResponse.message);
        // 直接显示成功消息
        console.log('头像已成功更新');
        successMessage.value = '头像上传成功';
      }
    } catch (updateError) {
      console.error('更新用户头像信息失败:', updateError);
      
      // 直接显示成功消息
      console.log('头像已成功更新');
      successMessage.value = '头像上传成功';
    }
  } catch (error) {
    // 处理异常
    console.error('上传头像过程发生异常:', error);
    errorMessage.value = '上传头像失败，请重试';
  } finally {
    // 无论成功失败，都结束上传状态
    isAvatarUploading.value = false;
  }
};

// 跳转到安全设置页面
const goToSecurityPage = () => {
  router.push('/mall/user/userSecurity');
};

// 重置表单
const resetForm = () => {
  // 重新加载用户信息
  form.nickname = userStore.nickname || '';
  form.avatar = userStore.avatar || '';
  form.phone = userStore.user?.phone || '';
  form.email = userStore.user?.email || '';
  form.emailVerified = userStore.user?.emailVerified || false;
  form.gender = userStore.user?.gender || 'secret';
  form.birthday = userStore.user?.birthday || '';
  form.bio = userStore.user?.bio || '';
  
  // 清除消息
  successMessage.value = '';
  errorMessage.value = '';
};

// 更新昵称
const updateNickname = async (event) => {
  // 阻止事件冒泡和默认行为
  if (event) {
    event.preventDefault();
    event.stopPropagation();
  }
  
  try {
    // 清除消息
    successMessage.value = '';
    errorMessage.value = '';
    
    // 表单验证
    if (!form.nickname) {
      errorMessage.value = '昵称不能为空';
      return;
    }
    
    if (form.nickname.length < 2 || form.nickname.length > 20) {
      errorMessage.value = '昵称长度应为2-20个字符';
      return;
    }
    
    isNicknameUpdating.value = true;
    
    // 发送更新昵称请求
    const response = await userApi.profile.updateNickname(form.nickname);
    
    if (response.code === 200) {
      // 直接读取和修改本地存储的用户信息
      try {
        // 从本地存储中读取用户信息
        const userStr = localStorage.getItem('mall_user');
        if (userStr) {
          const userData = JSON.parse(userStr);
          // 更新昵称
          userData.nickname = form.nickname;
          // 保存回本地存储
          localStorage.setItem('mall_user', JSON.stringify(userData));
          console.log('本地用户信息昵称已更新');
          
          // 同时更新 store 中的信息
          userStore.nickname = form.nickname;
          if (userStore.user) {
            userStore.user.nickname = form.nickname;
          }
        } else {
          console.warn('未找到本地用户信息');
        }
      } catch (err) {
        console.error('更新本地用户信息失败:', err);
      }
      
      successMessage.value = '昵称修改成功';
    } else {
      errorMessage.value = response.message || '修改昵称失败，请稍后再试';
    }
    
  } catch (error) {
    console.error('修改昵称失败:', error);
    errorMessage.value = '修改昵称失败，请稍后再试';
  } finally {
    isNicknameUpdating.value = false;
  }
};

// 保存个人资料
const saveProfile = async () => {
  try {
    // 清除消息
    successMessage.value = '';
    errorMessage.value = '';
    
    // 表单验证
    if (!form.nickname) {
      errorMessage.value = '昵称不能为空';
      return;
    }
    
    isLoading.value = true;
    
    // 准备要保存的数据
    const profileData = {
      nickname: form.nickname,
      avatar: form.avatar,
      gender: form.gender,
      birthday: form.birthday,
      bio: form.bio
    };
    
    // 直接更新 store 中的用户信息，不调用后端接口
    console.log('保存个人资料:', profileData);
    
    // 更新 store 中的用户信息
    userStore.updateUserInfo(profileData);
    
    // 显示成功消息
    successMessage.value = '个人资料保存成功';
    
  } catch (error) {
    console.error('保存个人资料失败:', error);
    errorMessage.value = '保存个人资料失败，请稍后再试';
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
/* 个人资料页面特定样式 */
</style>
