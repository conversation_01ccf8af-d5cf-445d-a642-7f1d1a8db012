<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/user/center" class="hover:text-red-600">会员中心</NuxtLink> &gt; 
      <span>浏览历史</span>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载浏览历史...</p>
      </div>
      
      <!-- 未登录状态提示 -->
      <div v-else-if="!userStore.loggedIn" class="p-8 text-center">
        <div class="mb-6">
          <img src="https://placehold.co/120x120/ffffff/333333?text=未登录" alt="未登录" class="mx-auto mb-4 rounded-full border-4 border-gray-200">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">您尚未登录</h2>
          <p class="text-gray-600 mb-6">登录后可查看您的浏览历史</p>
          <div class="flex justify-center space-x-4">
            <NuxtLink to="/mall/login" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              立即登录
            </NuxtLink>
            <NuxtLink to="/mall/register" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition">
              注册账号
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- 已登录状态 - 浏览历史 -->
      <div v-else class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <div class="flex justify-between items-center mb-6 pb-2 border-b">
            <h2 class="text-xl font-bold">浏览历史</h2>
            <div class="flex items-center">
              <!-- 视图切换按钮 -->
              <div class="flex border rounded-lg overflow-hidden mr-2">
                <button 
                  class="px-3 py-1.5 flex items-center text-sm" 
                  :class="viewMode === 'grid' ? 'bg-blue-50 text-blue-600' : 'bg-white text-gray-600 hover:bg-gray-50'"
                  @click="viewMode = 'grid'"
                >
                  <i class="i-carbon-grid mr-1"></i>
                  网格视图
                </button>
                <button 
                  class="px-3 py-1.5 flex items-center text-sm border-l" 
                  :class="viewMode === 'list' ? 'bg-blue-50 text-blue-600' : 'bg-white text-gray-600 hover:bg-gray-50'"
                  @click="viewMode = 'list'"
                >
                  <i class="i-carbon-list mr-1"></i>
                  列表视图
                </button>
              </div>
              
              <!-- 批量操作下拉菜单 -->
              <a-dropdown trigger="click" @select="handleBatchAction">
                <a-button class="ml-2">
                  批量操作
                  <i class="i-carbon-chevron-down ml-1"></i>
                </a-button>
                <template #content>
                  <a-doption value="select-all">全选</a-doption>
                  <a-doption value="unselect-all">取消全选</a-doption>
                  <!-- <a-doption value="add-to-cart" v-if="selectedIds.length > 0">加入购物车</a-doption> -->
                  <a-doption value="add-to-favorites" v-if="selectedIds.length > 0">添加到收藏</a-doption>
                  <a-doption value="remove" v-if="selectedIds.length > 0">
                    <span class="text-red-500">删除选中</span>
                  </a-doption>
                </template>
              </a-dropdown>
            </div>
          </div>
          
          <!-- 历史记录为空时的提示 -->
          <div v-if="historyItems.length === 0" class="text-center py-16">
            <img src="https://placehold.co/120x120/f5f5f5/999999?text=暂无记录" alt="暂无浏览历史" class="mx-auto mb-4">
            <p class="text-gray-500 mb-4">您还没有浏览过任何商品</p>
            <NuxtLink to="/mall" class="inline-block px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              去逛逛
            </NuxtLink>
          </div>
          
          <!-- 历史记录列表 - 网格视图 -->
          <div v-else-if="historyItems.length > 0 && viewMode === 'grid'" class="grid grid-cols-4 gap-4">
            <div v-for="(item, index) in historyItems" :key="index" 
                 class="product-card bg-white rounded-lg border border-gray-200 hover:shadow-md transition overflow-hidden relative">
              <div class="relative">
                <!-- 选择框 -->
                <div class="absolute top-2 left-2 z-10">
                  <a-checkbox v-model="item.selected" @change="(checked) => updateItemSelection(item, checked)" />
                </div>
                
                <!-- 浏览时间 -->
                <div class="absolute top-2 right-2 z-10 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
                  {{ item.viewTime }}
                </div>
                
                <!-- 商品图片 -->
                <NuxtLink :to="`/mall/product/${item.id}`">
                  <img :src="item.image" :alt="item.name" class="w-full h-48 object-cover">
                </NuxtLink>
              </div>
              
              <!-- 商品信息 -->
              <div class="p-3">
                <NuxtLink :to="`/mall/product/${item.id}`" class="block">
                  <h3 class="text-lg font-bold text-gray-800 line-clamp-2 h-10 mb-2 text-ellipsis">{{ item.name || '未命名商品' }}</h3>
                </NuxtLink>
                
                <div class="flex justify-between items-center mb-2">
                  <div class="text-red-600 font-bold">¥{{ formatPrice(item.price) }}</div>
                </div>
                
                <div class="flex space-x-2">
                  <!-- <a-button size="small" @click="addToCart(item)">
                    <template #icon><i class="i-carbon-shopping-cart"></i></template>
                    加入购物车
                  </a-button> -->

                  <a-button size="small" @click="addToFavorites(item)"
                            :loading="item.favoriteLoading"
                            :type="item.isFavorited ? 'primary' : 'outline'">
                    <template #icon>
                      <i :class="item.isFavorited ? 'i-carbon-favorite-filled' : 'i-carbon-favorite'"></i>
                    </template>
                    {{ item.isFavorited ? '已收藏' : '收藏' }}
                  </a-button>
                  
                  <a-popconfirm
                    content="确定要删除此浏览记录吗？"
                    @ok="handleRemoveHistory(item.id)"
                  >
                    <a-button size="small" status="danger">
                      <template #icon><i class="i-carbon-close"></i></template>
                      删除
                    </a-button>
                  </a-popconfirm>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 历史记录列表 - 列表视图 -->
          <div v-else-if="historyItems.length > 0 && viewMode === 'list'" class="divide-y divide-gray-200">
            <div v-for="(item, index) in historyItems" :key="index" 
                 class="product-row py-4 flex items-center hover:bg-gray-50 transition">
              <!-- 选择框 -->
              <div class="mr-4">
                <a-checkbox v-model="item.selected" @change="(checked) => updateItemSelection(item, checked)" />
              </div>
              
              <!-- 商品图片 -->
              <div class="w-24 h-24 flex-shrink-0 mr-4">
                <NuxtLink :to="`/mall/product/${item.id}`">
                  <img :src="item.image" :alt="item.name" class="w-full h-full object-cover rounded">
                </NuxtLink>
              </div>
              
              <!-- 商品信息 -->
              <div class="flex-grow mr-4">
                <NuxtLink :to="`/mall/product/${item.id}`" class="block">
                  <h3 class="text-lg font-bold text-gray-800 line-clamp-2 text-ellipsis">{{ item.name || '未命名商品' }}</h3>
                </NuxtLink>
                <div class="text-sm text-gray-500 mt-1">浏览于 {{ item.viewTime }}</div>
              </div>
              
              <!-- 价格和浏览时间 -->
              <div class="flex flex-col mx-6">
                <div class="text-red-600 font-bold text-lg">¥{{ formatPrice(item.price) }}</div>
                <div class="text-xs text-gray-500">{{ item.viewTime }}</div>
              </div>
              
              <!-- 操作按钮 -->
              <div class="flex space-x-2">
                <!-- <a-button size="small" @click="addToCart(item)">
                  <template #icon><i class="i-carbon-shopping-cart"></i></template>
                  加入购物车
                </a-button> -->
                <a-button size="small" @click="addToFavorites(item)"
                          :loading="item.favoriteLoading"
                          :type="item.isFavorited ? 'primary' : 'outline'">
                  <template #icon>
                    <i :class="item.isFavorited ? 'i-carbon-favorite-filled' : 'i-carbon-favorite'"></i>
                  </template>
                  {{ item.isFavorited ? '已收藏' : '收藏' }}
                </a-button>
                
                <a-popconfirm
                  content="确定要删除此浏览记录吗？"
                  @ok="handleRemoveHistory(item.id)"
                >
                  <a-button size="small" status="danger">
                    <template #icon><i class="i-carbon-close"></i></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div v-if="historyItems.length > 0" class="mt-6 flex justify-center">
            <a-pagination
              v-model:current="currentPage"
              show-total
              show-jumper 
              show-page-size
              :total="totalItems"
              :page-size="pageSize"
              :page-size-options="pageSizeOptions"
              @change="handlePageChange"
              @page-size-change="handlePageSizeChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useMallUserStore } from '~/store/mall/user';
import { useMallHistoryStore } from '~/store/mall/history';
import { useMallCartStore } from '~/store/mall/cart';
import { Message } from '@arco-design/web-vue';
import UserSideNav from '~/components/mall/user/UserSideNav.vue';
import mallApi from '~/api/mall';
// ...

definePageMeta({
  layout: 'mall'
});
// 获取用户状态
const userStore = useMallUserStore();
// 历史记录状态
const historyStore = useMallHistoryStore();
// 购物车状态
const cartStore = useMallCartStore();

// 视图模式：网格(grid)或列表(list)
const viewMode = ref('grid');

// 分页相关
const currentPage = ref(1);
const pageSize = ref(8);
const totalItems = ref(0);
const pageSizeOptions = ref([8, 16, 24, 32]);
// 加载状态
const isLoading = ref(false);

// 历史记录数据
const historyItems = ref([]);

// 选中的项目 - 从store中获取
const selectedIds = computed(() => historyStore.getSelectedItems);

// 更新商品选中状态
const updateItemSelection = (item, isSelected) => {
  // 更新store中的选中状态
  historyStore.updateSelectedItem(item.id, isSelected);
  // 更新当前页面中的选中状态
  if (historyItems.value) {
    const foundItem = historyItems.value.find(i => i.id === item.id);
    if (foundItem) {
      foundItem.selected = isSelected;
    }
  }
};

// 批量操作处理
const handleBatchAction = async (value) => {
  switch (value) {
    case 'select-all':
      // 获取所有历史记录商品ID
      const allHistoryIds = historyStore.getHistoryItems.map(item => item.id);
      // 更新store中的选中状态
      historyStore.batchUpdateSelectedItems(allHistoryIds, true);
      // 更新当前页面中的选中状态
      historyItems.value.forEach(item => item.selected = true);
      break;
    case 'unselect-all':
      // 获取当前页面所有商品ID
      const allCurrentIds2 = historyItems.value.map(item => item.id);
      // 更新store中的选中状态
      historyStore.batchUpdateSelectedItems(allCurrentIds2, false);
      // 更新当前页面中的选中状态
      historyItems.value.forEach(item => item.selected = false);
      break;
    case 'add-to-cart':
      if (selectedIds.value.length === 0) {
        Message.warning('请先选择商品');
        return;
      }
      const selectedProducts = historyItems.value.filter(item => selectedIds.value.includes(item.id));
      if (selectedProducts.length === 0) {
        Message.warning('请先选择商品');
        return;
      }
      selectedProducts.forEach(item => addToCart(item,false));
      Message.success(`已将${selectedProducts.length}个商品加入购物车`);
      break;
    case 'add-to-favorites':
      if (selectedIds.value.length === 0) {
        Message.warning('请先选择商品');
        return;
      }
      
      try{
        // 获取所有历史记录数据
        const allHistoryItems = historyStore.getHistoryItems;
        // 过滤出所有已选择的商品
        const allSelectedProducts = allHistoryItems.filter(item => selectedIds.value.includes(item.id));
        
        if (allSelectedProducts.length === 0) {
          Message.warning('请先选择商品');
          return;
        }
        
        let successCount = 0;
        let failCount = 0;
        for(const item of allSelectedProducts){
          const result = await addToFavorites(item,false);
          if(result){
            successCount++;
          }else{
            failCount++;
          }
        }
        if(successCount>0){ 
          Message.success(`已将${successCount}个商品添加到收藏`);
        }
        if(failCount>0){
          Message.error(`${failCount}个商品添加到收藏失败，请稍后再试`);
        }
      }catch(error){
        console.error('添加到收藏出错:', error);
        Message.error('添加到收藏失败，请稍后再试');
      }
      break;
    case 'remove':
      if (selectedIds.value.length === 0) {
        Message.warning('请先选择商品');
        return;
      }
      removeSelectedItems();
      break;
  }
};

// 批量删除历史记录
const removeSelectedItems = async () => {
  const ids = selectedIds.value;
  console.log('选中的历史记录ID:', ids);

  if (ids.length === 0) {
    Message.warning('请选择要删除的记录');
    return;
  }

  try {
    // 使用store的批量删除方法
    await historyStore.batchRemoveFromHistory(ids);

    // 清空选中状态
    historyStore.clearSelectedItems();
    Message.success(`已删除${ids.length}条浏览记录`);

    // 重新加载数据
    await loadHistoryData();
  } catch (error) {
    console.error('批量删除历史记录失败:', error);
    Message.error('删除失败，请稍后再试');
  }
};

// 添加到购物车
const addToCart = async (item,showMessage=true) => {
  try {
    console.log('历史记录添加商品到购物车:', item);
    // 调用购物车store添加商品
    const result = await cartStore.addToCart(
      {
        id: item.id,
        skuId: item.skuId,
        skuName: item.skuName,
        skuCode: item.skuCode,
        stock: item.stock,
        name: item.name,
        price: item.price,
        originalPrice: item.originalPrice,
        images: [item.image]
      },
      1, // 默认数量为1
      {} // 无规格信息
    );
    
    if (result.success ) {
      if(showMessage){
        Message.success(`已将 ${item.name} 添加到购物车`);
      }
    } else {
      if(showMessage){
        Message.error(result.message || '添加到购物车失败');
      }
    }
  } catch (error) {
    console.error('添加到购物车出错:', error);
    if(showMessage){
      Message.error('添加到购物车失败，请稍后再试');
    }
  }
};

// 添加到收藏
const addToFavorites =  async (item, showMessage = true) => {
  // 检查用户是否登录
  if (!userStore.loggedIn) {
    if (showMessage) {
      Message.warning('请先登录后再收藏商品');
    }
    return false;
  }

  // 检查商品是否存在
  if (!item || !item.id) {
    if (showMessage) {
      Message.error('商品信息不完整，无法收藏');
    }
    return false;
  }

  // 设置加载状态
  item.favoriteLoading = true;

  try {
    const response = await mallApi.favorite.toggleFavorite(
        item.id,
        item.isFavorited || false,
        1,
        `${item.isFavorited ? '取消收藏' : '收藏'}商品: ${item.name}`
    );

    if (response.code === 200) {
      item.isFavorited = !item.isFavorited;
      if (showMessage) {
        Message.success(item.isFavorited ? '收藏成功' : '取消收藏成功');
      }
      return true;
    } else {
      if (showMessage) {
        Message.error(response.message || '操作失败，请稍后再试');
      }
      return false;
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    if (showMessage) {
      Message.error('操作失败，请稍后再试');
    }
    return false;
  } finally {
    item.favoriteLoading = false;
  }
};
// 删除历史记录
const handleRemoveHistory = async (id) => {
  try {
    // 删除单个历史记录
    removeHistoryItem(id);
  } catch (error) {
    console.error('删除历史记录出错:', error);
    Message.error('删除历史记录失败，请稍后再试');
  }
};

// 删除单个历史记录
const removeHistoryItem = async (itemId) => {
  try {
    // 从store中删除历史记录
    await historyStore.removeFromHistory(itemId);
    Message.success('已删除该条浏览记录');

    // 重新加载数据
    await loadHistoryData();
  } catch (error) {
    console.error('删除历史记录失败:', error);
    Message.error('删除失败，请稍后再试');
  }
};

// 处理分页变化
const handlePageChange = (page) => {
  console.log('页码变化:', page);
  currentPage.value = page;
  loadHistoryData();
};

const handlePageSizeChange = (size) => {
  console.log('页面大小变化:', size);
  pageSize.value = size;
  currentPage.value = 1; // 重置到第一页
  loadHistoryData();
};
// 检查单个商品的收藏状态
const checkItemFavoriteStatus = async (item) => {
  if (!userStore.loggedIn || !item.id) {
    return false;
  }

  try {
    const response = await mallApi.favorite.checkFavoriteStatus({
      targetId: item.id,
      targetType: 1
    });

    if (response.code === 200) {
      return response.data.isFavorited || false;
    }
  } catch (error) {
    console.error('检查收藏状态失败:', error);
  }

  return false;
};

// 加载历史记录数据
const loadHistoryData = async () => {
  isLoading.value = true;

  try {
    // 初始化历史记录store
    await historyStore.init();

    // 如果用户已登录，从API加载数据
    if (userStore.loggedIn) {
      try {
        await historyStore.loadHistoryFromApi({
          page: currentPage.value,
          pageSize: pageSize.value
        });
      } catch (apiError) {
        console.warn('从API加载浏览历史失败，使用本地数据:', apiError);
      }
    }

    // 从历史记录store获取数据
    const storeHistoryItems = historyStore.getHistoryItems;
    // 获取已选择的商品ID
    const selectedItemIds = historyStore.getSelectedItems;

    // 处理历史记录数据，根据store中的选中状态设置selected属性
    const processedItems = await Promise.all(storeHistoryItems.map(async (item) => {
      // 检查收藏状态
      const isFavorited = await checkItemFavoriteStatus(item);
      return {
        ...item,
        isFavorited: isFavorited,
        selected: selectedItemIds.includes(item.id),
        favoriteLoading: false,
        // 格式化查看时间
        viewTime: formatViewTime(parseViewTime(item.viewTime))
      };
    }));

    // 如果使用API模式且用户已登录，使用API返回的分页信息
    if (historyStore.useApiMode && userStore.loggedIn && historyStore.lastApiResponse) {
      const apiData = historyStore.lastApiResponse.data;
      const pagination = apiData?.pagination;

      if (pagination) {
        // 使用API返回的分页信息
        totalItems.value = pagination.total;
        currentPage.value = pagination.page;
        pageSize.value = pagination.pageSize;
        // 直接使用处理后的当前页数据
        historyItems.value = processedItems;

        console.log('使用API分页信息:', {
          total: totalItems.value,
          currentPage: currentPage.value,
          pageSize: pageSize.value,
          totalPages: pagination.totalPages,
          itemsCount: historyItems.value.length
        });
      } else {
        // 没有分页信息时，使用本地分页
        totalItems.value = processedItems.length;
        const start = (currentPage.value - 1) * pageSize.value;
        const end = start + pageSize.value;
        historyItems.value = processedItems.slice(start, end);

        console.log('使用本地分页（无API分页信息）:', {
          total: totalItems.value,
          currentPage: currentPage.value,
          pageSize: pageSize.value,
          itemsCount: historyItems.value.length
        });
      }
    } else {
      // 本地模式，使用本地分页
      totalItems.value = processedItems.length;
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      historyItems.value = processedItems.slice(start, end);

      console.log('使用本地分页模式:', {
        total: totalItems.value,
        currentPage: currentPage.value,
        pageSize: pageSize.value,
        itemsCount: historyItems.value.length
      });
    }
    console.log('处理后的历史记录:', historyItems.value);
  } catch (error) {
    console.error('加载历史记录失败:', error);
    Message.error('加载历史记录失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};

// 清空所有历史记录
const clearAllHistory = async () => {
  try {
    // 从store中清空所有历史记录
    await historyStore.clearHistory();

    // 更新视图数据
    historyItems.value = [];
    totalItems.value = 0;
    selectedIds.value = [];
    Message.success('已清空所有浏览记录');
  } catch (error) {
    console.error('清空历史记录失败:', error);
    Message.error('清空失败，请稍后再试');
  }
};

// 格式化价格显示
const formatPrice = (price) => {
  if (typeof price === 'number') {
    return price.toFixed(2);
  } else if (typeof price === 'string') {
    // 尝试将字符串转换为数字并格式化
    const num = parseFloat(price);
    return isNaN(num) ? price : num.toFixed(2);
  }
  return '0.00';
};

// 解析查看时间 - 处理不同的时间格式
const parseViewTime = (viewTime) => {
  if (!viewTime) {
    return new Date();
  }

  // 如果已经是Date对象，直接返回
  if (viewTime instanceof Date) {
    return viewTime;
  }

  // 如果是字符串格式的时间戳（毫秒）
  if (typeof viewTime === 'string' && /^\d+$/.test(viewTime)) {
    const timestamp = parseInt(viewTime, 10);
    return new Date(timestamp);
  }

  // 如果是数字格式的时间戳
  if (typeof viewTime === 'number') {
    return new Date(viewTime);
  }

  // 尝试直接解析字符串
  const parsed = new Date(viewTime);
  if (!isNaN(parsed.getTime())) {
    return parsed;
  }

  // 如果都失败了，返回当前时间
  console.warn('无法解析时间格式:', viewTime);
  return new Date();
};

// 格式化查看时间
const formatViewTime = (date) => {
  const now = new Date();
  const diffMs = now - date;
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffDays === 0) {
    // 今天
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `今天 ${hours}:${minutes}`;
  } else if (diffDays === 1) {
    // 昨天
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `昨天 ${hours}:${minutes}`;
  } else if (diffDays === 2) {
    // 前天
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `前天 ${hours}:${minutes}`;
  } else if (diffDays < 7) {
    // 一周内
    return `${diffDays}天前`;
  } else {
    // 超过一周
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
};

// 在组件挂载后加载数据
onMounted(async () => {
  try {
    // 尝试恢复用户会话
    if (!userStore.loggedIn) {
      await userStore.restoreSession();
    }
    
    // 加载历史记录数据
    await loadHistoryData();
  } catch (error) {
    console.error('初始化组件时出错:', error);
    // 即使出错也尝试加载数据
    loadHistoryData();
  }
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  border-color: #3b82f6;
}
.text-ellipsis {
  overflow: hidden;
  width: 95%;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
