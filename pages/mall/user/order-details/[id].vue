<template>
  <div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="loading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载订单信息...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="p-8 text-center">
        <div class="flex justify-center items-center h-40 text-red-500">
          <i class="i-carbon-warning text-5xl"></i>
        </div>
        <p class="text-red-600 mt-4">{{ errorMessage }}</p>
        <button @click="goBack" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          返回订单列表
        </button>
      </div>
      
      <!-- 内容区域 -->
      <div v-else-if="order" class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <!-- 返回按钮 -->
          <div class="mb-4">
            <a href="/mall/user/orders" class="inline-flex items-center text-blue-600 hover:text-blue-800">
              <i class="i-carbon-arrow-left mr-1"></i> 返回订单列表
            </a>
          </div>

          <div v-if="order" class="mb-6">
            <h2 class="text-xl font-bold text-gray-800 mb-1">订单详情</h2>
            <p class="text-gray-500 text-sm">订单号：{{ order.id }}</p>
          </div>

          <!-- 订单状态进度条 -->
          <div v-if="order" class="mb-8">
            <!-- 正常订单进度条 -->
            <div v-if="order.orderStatus !== 4 && order.orderStatus !== 5" class="relative">
              <!-- 进度条背景 -->
              <div class="absolute top-1/2 left-0 w-full h-1 bg-gray-200 -translate-y-1/2"></div>
              
              <!-- 进度条前景 -->
              <div class="absolute top-1/2 left-0 h-1 bg-blue-500 -translate-y-1/2" :style="{ width: progressWidth }"></div>
              
              <!-- 进度点 -->
              <div class="relative flex justify-between">
                <div class="flex flex-col items-center">
                  <div :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center z-10',
                    orderProgress >= 1 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'
                  ]">
                    <i class="i-carbon-document"></i>
                  </div>
                  <div class="mt-2 text-xs text-center" :class="orderProgress >= 1 ? 'text-blue-600' : 'text-gray-500'">
                    下单成功
                    <div class="text-gray-400">{{ formatDateTime(order.createdAt) }}</div>
                  </div>
                </div>
                
                <div class="flex flex-col items-center">
                  <div :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center z-10',
                    orderProgress >= 2 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'
                  ]">
                    <i class="i-carbon-wallet"></i>
                  </div>
                  <div class="mt-2 text-xs text-center" :class="orderProgress >= 2 ? 'text-blue-600' : 'text-gray-500'">
                    付款成功
                    <div class="text-gray-400">{{ order.paidAt ? formatDateTime(order.paidAt) : '待付款' }}</div>
                  </div>
                </div>
                
                <div class="flex flex-col items-center">
                  <div :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center z-10',
                    orderProgress >= 3 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'
                  ]">
                    <i class="i-carbon-delivery"></i>
                  </div>
                  <div class="mt-2 text-xs text-center" :class="orderProgress >= 3 ? 'text-blue-600' : 'text-gray-500'">
                    商品发货
                    <div class="text-gray-400">{{ order.logistics?.deliveryTime ? formatDateTime(order.logistics.deliveryTime) : '待发货' }}</div>
                  </div>
                </div>
                
                <div class="flex flex-col items-center">
                  <div :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center z-10',
                    orderProgress >= 4 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'
                  ]">
                    <i class="i-carbon-package"></i>
                  </div>
                  <div class="mt-2 text-xs text-center" :class="orderProgress >= 4 ? 'text-blue-600' : 'text-gray-500'">
                    确认收货
                    <div class="text-gray-400">{{ order.completedAt ? formatDateTime(order.completedAt) : '待收货' }}</div>
                  </div>
                </div>
                
                <div class="flex flex-col items-center">
                  <div :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center z-10',
                    orderProgress >= 5 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'
                  ]">
                    <i class="i-carbon-star"></i>
                  </div>
                  <div class="mt-2 text-xs text-center" :class="orderProgress >= 5 ? 'text-blue-600' : 'text-gray-500'">
                    评价完成
                    <div class="text-gray-400">{{ order.comment ? formatDateTime(order.comment.createTime) : '待评价' }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 已关闭/已退款订单进度条 -->
            <div v-else class="relative">
              <!-- 进度条背景 -->
              <div class="absolute top-1/2 left-0 w-full h-1 bg-gray-200 -translate-y-1/2"></div>
              
              <!-- 进度条前景 -->
              <div class="absolute top-1/2 left-0 h-1 bg-red-500 -translate-y-1/2" style="width: 100%"></div>
              
              <!-- 只显示三个状态：下单、订单关闭和已退款（如果是退款状态） -->
              <div class="relative flex justify-between">
                <!-- 下单成功 -->
                <div class="flex flex-col items-center">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center z-10 bg-red-500 text-white">
                    <i class="i-carbon-document"></i>
                  </div>
                  <div class="mt-2 text-xs text-center text-red-600">
                    下单成功
                    <div class="text-gray-400">{{ formatDateTime(order.createdAt) }}</div>
                  </div>
                </div>
                
                <!-- 订单关闭 -->
                <div class="flex flex-col items-center">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center z-10 bg-red-500 text-white">
                    <i class="i-carbon-close"></i>
                  </div>
                  <div class="mt-2 text-xs text-center text-red-600">
                    订单关闭
                    <div class="text-gray-400">{{ order.cancelledAt ? formatDateTime(order.cancelledAt) : formatDateTime(order.updatedAt) }}</div>
                  </div>
                </div>
                
                <!-- 已退款（仅在状态为5时显示） -->
                <div class="flex flex-col items-center">
                  <div class="w-8 h-8 rounded-full flex items-center justify-center z-10 bg-red-500 text-white">
                    <i class="i-carbon-wallet"></i>
                  </div>
                  <div class="mt-2 text-xs text-center text-red-600">
                    已退款
                    <div class="text-gray-400">{{ order.refundedAt ? formatDateTime(order.refundedAt) : (order.orderStatus === 5 ? formatDateTime(order.updatedAt) : '') }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 订单信息 -->
          <div v-if="order" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h3 class="text-gray-800 font-medium">订单信息</h3>
            </div>
            <div class="p-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="text-sm">
                  <span class="text-gray-500">订单编号：</span>
                  <span class="text-gray-800">{{ order.id }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">订单状态：</span>
                  <span :class="{
                    'text-yellow-600 font-medium': order.orderStatus === 0,
                    'text-blue-600 font-medium': order.orderStatus === 1 || order.orderStatus === 2,
                    'text-green-700 font-medium': order.orderStatus === 3,
                    'text-gray-600 font-medium': order.orderStatus === 4,
                    'text-red-600 font-medium': order.orderStatus === 5
                  }">{{ order.orderStatusText || getOrderStatusText(order.orderStatus) }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">下单时间：</span>
                  <span class="text-gray-800">{{ formatDateTime(order.createdAt) }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">支付方式：</span>
                  <span class="text-gray-800">{{ order.paymentMethodText || getPaymentMethodText(order.paymentMethodId) }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">支付时间：</span>
                  <span class="text-gray-800">{{ order.paidAt ? formatDateTime(order.paidAt) : '未支付' }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">订单金额：</span>
                  <span class="text-red-600 font-bold">¥{{ parseFloat(order.totalAmount || 0).toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 收货信息 -->
          <div v-if="order && order.shipping" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h3 class="text-gray-800 font-medium">收货信息</h3>
            </div>
            <div class="p-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="text-sm">
                  <span class="text-gray-500">收货人：</span>
                  <span class="text-gray-800">{{ order.shipping.recipientName || '暂无' }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">联系电话：</span>
                  <span class="text-gray-800">{{ order.shipping.recipientPhone || '暂无' }}</span>
                </div>
                <div class="text-sm col-span-2">
                  <span class="text-gray-500">收货地址：</span>
                  <span class="text-gray-800">{{ order.shipping.streetAddress || '' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 发票信息 -->
          <div v-if="order && order.invoiceApplication" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h3 class="text-gray-800 font-medium">发票信息</h3>
            </div>
            <div class="p-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="text-sm">
                  <span class="text-gray-500">发票类型：</span>
                  <span class="text-gray-800">{{ getInvoiceTypeText(order.invoiceApplication.applicationType) }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">发票金额：</span>
                  <span class="text-gray-800">¥{{ parseFloat(order.invoiceApplication.applicationAmount || 0).toFixed(2) }}</span>
                </div>
                <div v-if="getInvoiceSnapshotData('headerName')" class="text-sm">
                  <span class="text-gray-500">发票抬头：</span>
                  <span class="text-gray-800">{{ getInvoiceSnapshotData('headerName') }}</span>
                </div>
                <div v-if="getInvoiceSnapshotData('taxNumber')" class="text-sm">
                  <span class="text-gray-500">纳税人识别号：</span>
                  <span class="text-gray-800">{{ getInvoiceSnapshotData('taxNumber') }}</span>
                </div>
                <div v-if="getInvoiceSnapshotData('companyAddress')" class="text-sm col-span-2">
                  <span class="text-gray-500">公司地址：</span>
                  <span class="text-gray-800">{{ getInvoiceSnapshotData('companyAddress') }}</span>
                </div>
                <div v-if="getInvoiceSnapshotData('companyPhone')" class="text-sm">
                  <span class="text-gray-500">公司电话：</span>
                  <span class="text-gray-800">{{ getInvoiceSnapshotData('companyPhone') }}</span>
                </div>
                <div v-if="getInvoiceSnapshotData('bankName')" class="text-sm">
                  <span class="text-gray-500">开户银行：</span>
                  <span class="text-gray-800">{{ getInvoiceSnapshotData('bankName') }}</span>
                </div>
                <div v-if="getInvoiceSnapshotData('bankAccount')" class="text-sm">
                  <span class="text-gray-500">银行账户：</span>
                  <span class="text-gray-800">{{ getInvoiceSnapshotData('bankAccount') }}</span>
                </div>
                <div v-if="order.invoiceApplication.applicantRemark" class="text-sm col-span-2">
                  <span class="text-gray-500">备注：</span>
                  <span class="text-gray-800">{{ order.invoiceApplication.applicantRemark }}</span>
                </div>
              </div>

              <!-- 发票记录列表 -->
              <div v-if="order.invoices && order.invoices.length > 0" class="mt-4 pt-4 border-t border-gray-200">
                <h4 class="text-gray-700 font-medium mb-3">发票记录</h4>
                <div v-for="invoice in order.invoices" :key="invoice.id" class="mb-3 p-3 bg-gray-50 rounded">
                  <div class="grid grid-cols-2 gap-2 text-sm">
                    <div v-if="invoice.invoiceNumber">
                      <span class="text-gray-500">发票号码：</span>
                      <span class="text-gray-800">{{ invoice.invoiceNumber }}</span>
                    </div>
                    <div>
                      <span class="text-gray-500">发票状态：</span>
                      <span class="text-gray-800" :class="getInvoiceStatusClass(invoice.invoiceStatus)">
                        {{ getInvoiceStatusText(invoice.invoiceStatus) }}
                      </span>
                    </div>
                    <div v-if="invoice.invoicePdfUrl" class="col-span-2">
                      <a :href="invoice.invoicePdfUrl" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">
                        下载发票PDF
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 物流信息 -->
          <div v-if="order && order.shippingStatus > 0" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h3 class="text-gray-800 font-medium">物流信息</h3>
            </div>
            <div class="p-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="text-sm">
                  <span class="text-gray-500">物流公司：</span>
                  <span class="text-gray-800">{{ order.shipping?.shippingCompanyName || '暂无' }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">物流单号：</span>
                  <span class="text-gray-800">{{ order.shipping?.trackingNumber || '暂无' }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">发货时间：</span>
                  <span class="text-gray-800">{{ order.shippedAt ? formatDateTime(order.shippedAt) : '暂未发货' }}</span>
                </div>
                <div class="text-sm">
                  <span class="text-gray-500">收货时间：</span>
                  <span class="text-gray-800">{{ order.completedAt ? formatDateTime(order.completedAt) : '未收货' }}</span>
                </div>
              </div>
              <div class="mt-4" v-if="order.shipping?.trackingNumber">
                <button class="px-4 py-2 border border-blue-500 rounded-md text-blue-600 hover:bg-blue-50">
                  查看物流详情
                </button>
              </div>
            </div>
          </div>

          <!-- 商品信息 -->
          <div v-if="order && (order.items || order.orderItems) && (order.items?.length > 0 || order.orderItems?.length > 0)" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h3 class="text-gray-800 font-medium">商品信息</h3>
            </div>
            <div class="p-4">
              <table class="w-full">
                <thead class="bg-gray-50 text-gray-600 text-sm">
                  <tr>
                    <th class="py-2 px-4 text-left">商品信息</th>
                    <th class="py-2 px-4 text-center">单价</th>
                    <th class="py-2 px-4 text-center">数量</th>
                    <th class="py-2 px-4 text-right">小计</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, index) in (order.items || order.orderItems)" :key="item.id || index" class="border-b border-gray-100 last:border-b-0">
                    <td class="py-4 px-4">
                      <div class="flex items-center">
                        <div class="w-16 h-16 mr-4">
                          <a-image :src="item.productImage || '/assets/placeholder.png'" :alt="item.productName || '商品图片'" class="w-full h-full object-contain overflow-hidden" />
                        </div>
                        <div>
                          <div class="text-gray-800 hover:text-blue-600">
                            <a :href="`/mall/product/${item.goodsSpuId || 0}`">{{ item.productName || '商品名称' }}</a>
                          </div>
                          <div v-if="item.skuSpecifications" class="text-xs text-gray-500 mt-1">
                            {{ item.skuSpecifications }}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="py-4 px-4 text-center text-gray-800">
                      ¥{{ parseFloat(item.unitPrice || 0).toFixed(2) }}
                    </td>
                    <td class="py-4 px-4 text-center text-gray-800">
                      {{ item.quantity || 0 }}
                    </td>
                    <td class="py-4 px-4 text-right text-gray-800 font-medium">
                      ¥{{ parseFloat(item.totalPrice || 0).toFixed(2) }}
                    </td>
                  </tr>
                </tbody>
              </table>
              
              <div class="mt-4 flex justify-end">
                <div class="w-64">
                  <div class="flex justify-between py-1">
                    <span class="text-gray-500">商品总价：</span>
                    <span class="text-gray-800">¥{{ parseFloat(order.totalProductAmount || 0).toFixed(2) }}</span>
                  </div>
                  <div class="flex justify-between py-1">
                    <span class="text-gray-500">运费：</span>
                    <span class="text-gray-800">¥{{ parseFloat(order.shippingFee || 0).toFixed(2) }}</span>
                  </div>
                  <!-- 优惠金额暂时隐藏
                  <div class="flex justify-between py-1">
                    <span class="text-gray-500">优惠金额：</span>
                    <span class="text-gray-800">¥{{ parseFloat(order.discountAmount || 0).toFixed(2) }}</span>
                  </div>
                  -->
                  <div class="flex justify-between py-1 border-t border-gray-100 mt-2 pt-2">
                    <span class="text-gray-700 font-medium">实付款：</span>
                    <span class="text-red-600 font-bold">¥{{ parseFloat(order.totalAmount || 0).toFixed(2) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 订单操作 -->
          <div v-if="order" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h3 class="text-gray-800 font-medium">订单操作</h3>
            </div>
            <div class="p-4 flex space-x-3">
              <!-- 根据订单状态显示不同的操作按钮 -->
              <template v-if="order.orderStatus === 0"> <!-- 待付款 -->
                <button @click="goToPayment" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                  去支付
                </button>
                <button @click="cancelOrder" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                  取消订单
                </button>
              </template>
              
              <template v-else-if="order.orderStatus === 1"> <!-- 待发货 -->
                <button @click="()=>{Message.info('已提醒发货，请耐心等待')}" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                  提醒发货
                </button>
              </template>
              
              <template v-else-if="order.orderStatus === 2"> <!-- 待收货 -->
                <a-popconfirm content="确定要确认收货吗？" @ok="confirmReceipt(order.id)">
                        <a-button status="warning" class="px-3 py-1 border border-blue-500 rounded text-sm text-blue-600 hover:bg-blue-50 cursor-pointer">
                          确认收货
                        </a-button>
                      </a-popconfirm>
                <button v-if="order.shipping?.trackingNumber" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                  查看物流
                </button>
              </template>
              
              <template v-else-if="order.orderStatus === 3"> <!-- 交易成功 -->
                <button @click="goToReview" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                  {{ hasReviews ? '查看评价' : '去评价' }}
                </button>
<!--                <button @click="buyAgain" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">-->
<!--                  再次购买-->
<!--                </button>-->
              </template>
              
<!--              <template v-else-if="order.orderStatus === 4"> &lt;!&ndash; 已关闭 &ndash;&gt;-->
<!--                <button @click="buyAgain" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">-->
<!--                  再次购买-->
<!--                </button>-->
<!--              </template>-->

<!--              <template v-else-if="order.orderStatus === 5"> &lt;!&ndash; 已退款 &ndash;&gt;-->
<!--                <button @click="buyAgain" class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">-->
<!--                  再次购买-->
<!--                </button>-->
<!--              </template>-->

            </div>
          </div>

          <!-- 评价信息 -->
          <div v-if="order && order.comment" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h3 class="text-gray-800 font-medium">我的评价</h3>
            </div>
            <div class="p-4">
              <div class="mb-2">
                <div class="flex items-center">
                  <div class="mr-2">评分：</div>
                  <div class="flex text-yellow-400">
                    <i v-for="i in 5" :key="i" :class="[
                      i <= order.comment.rating ? 'i-carbon-star-filled' : 'i-carbon-star'
                    ]"></i>
                  </div>
                </div>
              </div>
              <div class="mb-4">
                <div class="text-gray-800">{{ order.comment.content }}</div>
              </div>
              <div v-if="order.comment.images && order.comment.images.length > 0" class="flex flex-wrap">
                <div v-for="(image, index) in order.comment.images" :key="index" class="w-20 h-20 mr-2 mb-2">
                  <img :src="image" alt="评价图片" class="w-full h-full object-cover rounded">
                </div>
              </div>
              <div class="mt-2 text-sm text-gray-500">
                {{ order.comment.createTime }}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 订单不存在 -->
      <div v-else class="p-8 text-center">
        <div class="mb-6">
          <i class="i-carbon-document-error text-6xl text-gray-300 mb-4"></i>
          <h2 class="text-xl font-bold text-gray-800 mb-2">订单不存在</h2>
          <p class="text-gray-600 mb-6">该订单不存在或已被删除</p>
          <div class="flex justify-center">
            <NuxtLink to="/mall/user/orders" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              返回订单列表
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';
import { OrderStatusText, PaymentMethodText } from '~/mock/mall/userCenter';
import orderApi from '~/api/mall/order';
import { Message } from '@arco-design/web-vue';
import UserSideNav from '~/components/mall/user/UserSideNav.vue';
import { InvoiceTypeEnum } from '~/constants/InvoiceTypeEnum';
definePageMeta({
  layout: 'mall'
});

// 处理订单状态
const orderStatusMap = {
  0: '待付款',
  1: '待发货',
  2: '待收货',
  3: '交易成功',
  4: '已关闭',
  5: '已退款'
};

// 处理支付状态
const paymentStatusMap = {
  0: '未支付',
  1: '部分支付',
  2: '已支付',
  3: '退款中',
  4: '已退款'
};

// 处理配送状态
const shippingStatusMap = {
  0: '未发货',
  1: '已发货',
  2: '部分发货',
  3: '已收货',
  4: '退货中',
  5: '已退货'
};

// 处理订单来源
const orderSourceMap = {
  1: '官方商城',
  2: '微信小程序',
  3: '手机应用'
};

// 处理订单类型
const orderTypeMap = {
  1: '普通订单',
  2: '团购订单',
  3: '秒杀订单'
};

// 处理发票类型 - 使用枚举
const invoiceTypeMap = {
  [InvoiceTypeEnum.NONE]: InvoiceTypeEnum.getTypeName(InvoiceTypeEnum.NONE),
  [InvoiceTypeEnum.PERSONAL]: InvoiceTypeEnum.getTypeName(InvoiceTypeEnum.PERSONAL),
  [InvoiceTypeEnum.COMPANY]: InvoiceTypeEnum.getTypeName(InvoiceTypeEnum.COMPANY)
};

const route = useRoute();
const router = useRouter();
const userStore = useMallUserStore();

// 状态变量
const order = ref(null);
const loading = ref(true);
const unreadMessages = ref(0);
const orderId = computed(() => route.params.id);
const error = ref(false);
const errorMessage = ref('');


// 确认收货
const confirmReceipt = async (orderId) => {
  if (!orderId) {
    Message.error('订单信息不完整，无法确认收货');
    return;
  }
  
  try {
    const result = await orderApi.receiptOrder(orderId);
    if (result.code === 200) {
      Message.success(result.message || '确认收货成功');
      console.log('确认收货响应数据:', result);
      //刷新订单详情
      loadOrderDetail(orderId);
    } else {
      Message.error(result.message || '确认收货失败');
    }
  } catch (error) {
    console.error('确认收货失败:', error);
    Message.error('确认收货失败，请稍后重试');
  }
};

// 订单进度
const orderProgress = computed(() => {
  if (!order.value) return 0;
  
  // 使用新的 API 返回的状态字段
  const status = order.value.orderStatus;
  
  // 根据订单状态计算进度
  switch (status) {
    case 0: // 待付款
      return 1;
    case 1: // 待发货
      return 2;
    case 2: // 待收货
      return 3;
    case 3: // 交易成功
      return 5;
    case 4: // 已关闭
      return 5; // 已关闭的订单进度显示全部步骤，但使用不同颜色
    case 5: // 已退款
      return 5; // 已退款的订单进度显示全部步骤，但使用不同颜色
    default:
      return 1;
  }
});

// 进度条宽度
const progressWidth = computed(() => {
  if (!order.value) return '0%';

  // 计算进度百分比
  const progress = orderProgress.value;
  const totalSteps = 5;

  // 对于已关闭或已退款的订单，进度条宽度为100%，但颜色不同
  if (order.value.orderStatus === 4 || order.value.orderStatus === 5) {
    return '100%';
  }

  return `${(progress - 1) / (totalSteps - 1) * 100}%`;
});

// 检查是否已有评价
const hasReviews = computed(() => {
  if (!order.value || !order.value.items) return false;

  // 检查订单中的商品是否已经有评价
  return order.value.items.some(item => item.hasReview || item.reviewId);
});

// 获取订单状态文本
const getOrderStatusText = (status) => {
  return orderStatusMap[status] || '未知状态';
};

// 获取支付方式文本
const getPaymentMethodText = (method) => {
  return method ? PaymentMethodText[method] : '未支付';
};

// 获取发票类型文本
const getInvoiceTypeText = (invoiceType) => {
  return InvoiceTypeEnum.getTypeName(invoiceType);
};

// 获取申请状态文本
const getApplicationStatusText = (status) => {
  const statusMap = {
    0: '待审核',
    1: '已通过',
    2: '已拒绝',
    3: '已撤销'
  };
  return statusMap[status] || '未知状态';
};

// 获取申请状态样式类
const getApplicationStatusClass = (status) => {
  const classMap = {
    0: 'text-orange-600', // 待审核
    1: 'text-green-600',  // 已通过
    2: 'text-red-600',    // 已拒绝
    3: 'text-gray-600'    // 已撤销
  };
  return classMap[status] || 'text-gray-600';
};

// 获取发票状态文本
const getInvoiceStatusText = (status) => {
  const statusMap = {
    0: '已开具',
    1: '已发送',
    2: '已作废'
  };
  return statusMap[status] || '未知状态';
};

// 获取发票状态样式类
const getInvoiceStatusClass = (status) => {
  const classMap = {
    0: 'text-blue-600',   // 已开具
    1: 'text-green-600',  // 已发送
    2: 'text-red-600'     // 已作废
  };
  return classMap[status] || 'text-gray-600';
};

// 从发票快照中获取数据
const getInvoiceSnapshotData = (field) => {
  if (!order.value || !order.value.invoiceApplication) {
    return null;
  }

  const application = order.value.invoiceApplication;

  // 字段映射关系
  const fieldMapping = {
    'headerType': 'snapshotHeaderType',
    'headerName': 'snapshotHeaderName',
    'taxNumber': 'snapshotTaxNumber',
    'companyAddress': 'snapshotCompanyAddress',
    'companyPhone': 'snapshotCompanyPhone',
    'bankName': 'snapshotBankName',
    'bankAccount': 'snapshotBankAccount'
  };

  // 使用快照字段
  const snapshotField = fieldMapping[field];
  if (snapshotField && application[snapshotField]) {
    return application[snapshotField];
  }

  // 兼容旧的JSON格式（如果存在）
  if (application.headerSnapshot) {
    try {
      const snapshot = JSON.parse(application.headerSnapshot);
      return snapshot[field] || null;
    } catch (error) {
      console.error('解析发票抬头快照失败:', error);
    }
  }

  return null;
};

// 格式化日期
const formatDate = (dateString) => {
  console.log('格式化日期:', dateString);
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return `${date.getMonth() + 1}月${date.getDate()}日`;
};

// 格式化完整日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  
  try {
    // 如果是数字字符串，先转换为数字
    const timestampNum = typeof timestamp === 'string' ? Number(timestamp) : timestamp;
    
    // 创建日期对象
    const date = new Date(timestampNum);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的时间戳:', timestamp);
      return '';
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('格式化日期时间错误:', error);
    return '';
  }
};

// 返回订单列表
const goBack = () => {
  router.push('/mall/user/orders');
};

// 取消订单
const cancelOrder = async () => {
  if (!order.value || !order.value.id) {
    Message.error('订单信息不完整，无法取消');
    return;
  }
  
  try {
    loading.value = true;
    const result = await orderApi.cancelOrder({
      orderId: order.value.id,
      cancelReason: '商城用户取消订单'
    });
    
    if (result.code === 200 || result.code === 0 || result.success) {
      Message.success('订单取消成功');
      console.log('取消订单响应数据:', result);
      
      // 如果返回了订单ID，使用该ID重新加载订单详情
      if (result.data && result.data.id) {
        await loadOrderDetail(result.data.id);
      } else {
        // 否则使用原来的订单ID
        await loadOrderDetail(order.value.id);
      }
    } else {
      Message.error(result.message || '取消订单失败');
    }
  } catch (err) {
    console.error('取消订单出错:', err);
    Message.error('取消订单失败，请稍后再试');
  } finally {
    loading.value = false;
  }
};

// 去支付
const goToPayment = () => {
  if (!order.value || !order.value.id) {
    Message.error('订单信息不完整，无法支付');
    return;
  }

  // 跳转到支付页面
  router.push({
    path: `/mall/payment/${order.value.id}`,
    query: {
      // 不再使用orderSn参数，因为路径中已包含id
      amount: order.value.totalAmount || 0
    }
  });
};

// 去评价
const goToReview = () => {
  if (!order.value || !order.value.id) {
    Message.error('订单信息不完整，无法评价');
    return;
  }

  if (hasReviews.value) {
    // 如果已有评价，跳转到评价查看页面或商品详情页的评价部分
    if (order.value.items && order.value.items.length > 0) {
      const firstItem = order.value.items[0];
      if (firstItem.goodsSpuId) {
        // 跳转到商品详情页的评价选项卡
        router.push(`/mall/product/${firstItem.goodsSpuId}#reviews`);
        return;
      }
    }
    Message.info('评价已完成');
  } else {
    // 如果未评价，跳转到评价页面
    router.push({
      path: `/mall/user/order-review/${order.value.id}`
    });
  }
};

// 再次购买
const buyAgain = async () => {
  if (!order.value || !order.value.items || order.value.items.length === 0) {
    Message.error('订单信息不完整，无法再次购买');
    return;
  }

  try {
    // 遍历订单商品，添加到购物车
    for (const item of order.value.items) {
      if (item.goodsSpuId) {
        // 跳转到商品详情页，用户可以重新选择规格和数量
        router.push(`/mall/product/${item.goodsSpuId}`);
        break; // 只跳转到第一个商品，如果有多个商品用户需要手动添加其他商品
      }
    }

    if (order.value.items.length > 1) {
      Message.info('订单包含多个商品，已跳转到第一个商品页面，请手动添加其他商品到购物车');
    }
  } catch (error) {
    console.error('再次购买失败:', error);
    Message.error('再次购买失败，请稍后重试');
  }
};

// 加载订单详情
const loadOrderDetail = async (id) => {
  if (!id) {
    error.value = true;
    errorMessage.value = '订单ID为空，无法加载订单详情';
    loading.value = false;
    return;
  }
  
  error.value = false;
  errorMessage.value = '';
  loading.value = true;
  
  try {
    console.log('加载订单详情:', id);
    // 确保用户已登录
    if (!userStore.loggedIn) {
      console.log('用户未登录，尝试恢复会话...');
      await userStore.restoreSession();
    }
    
    if (!userStore.loggedIn) {
      console.warn('用户未登录，无法加载订单详情');
      router.push('/mall/login');
      return;
    }
    
    // 调用 getOrderDetail 方法
    const result = await orderApi.getOrderDetail(id);
    
    if (result.code === 200 && result.data) {
      console.log('订单详情数据:', result.data);
      // 使用新的数据格式
      order.value = result.data;
      
      // 确保订单项数据一致性
      if (!order.value.items && order.value.orderItems && order.value.orderItems.length > 0) {
        order.value.items = order.value.orderItems.map(item => {
          // 添加待发货数量字段
          if (!item.pendingShipQuantity) {
            item.pendingShipQuantity = item.quantity - (item.shippedQuantity || 0);
          }
          return item;
        });
      }
    } else {
      console.warn('未找到订单:', id, result.message);
      error.value = true;
      errorMessage.value = result.message || `未找到订单 ${id} 的详细信息`;
      order.value = null;
    }
  } catch (error) {
    console.error('加载订单详情失败:', error);
    error.value = true;
    errorMessage.value = '加载订单详情失败，请稍后再试';
    order.value = null;
  } finally {
    loading.value = false;
  }
};

// 在组件挂载后加载数据
onMounted(async () => {
  try {
    console.log('订单详情页面挂载，路由参数:', route.params);
    
    // 获取未读消息数量
    unreadMessages.value = userStore.unreadMessageCount;

    // 获取订单ID
    const id = orderId.value;
    console.log('当前订单ID:', id);
    
    if (!id) {
      console.error('未找到订单ID参数');
      error.value = true;
      errorMessage.value = '未找到订单ID参数';
      loading.value = false;
      return;
    }
    
    // 加载订单详情
    await loadOrderDetail(id);
    
  } catch (err) {
    console.error('初始化订单详情页面失败:', err);
    error.value = true;
    errorMessage.value = '初始化订单详情页面失败';
    loading.value = false;
  }
});

// 监听路由参数变化，重新加载订单详情
watch(orderId, async (newId, oldId) => {
  console.log('路由参数变化:', oldId, '->', newId);
  if (newId && newId !== oldId) {
    await loadOrderDetail(newId);
  }
}, { immediate: true });
</script>

<style scoped>
/* 订单详情页面特定样式 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
