<template>
  <div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-800">商品评价</h2>
            <p class="text-gray-500 text-sm mt-1">请对您购买的商品进行评价</p>
          </div>

          <!-- 加载中 -->
          <div v-if="isLoading" class="py-16">
            <div class="text-center">
              <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              <p class="mt-2 text-gray-500">加载中...</p>
            </div>
          </div>

          <!-- 订单信息 -->
          <div v-else-if="orderInfo" class="mb-6">
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="flex justify-between items-center">
                <div>
                  <span class="text-gray-500">订单号：</span>
                  <span class="text-gray-800 font-medium">{{ orderInfo.id }}</span>
                </div>
                <div>
                  <span class="text-gray-500">订单金额：</span>
                  <span class="text-red-600 font-bold">¥{{ parseFloat(orderInfo.totalAmount || 0).toFixed(2) }}</span>
                </div>
              </div>
            </div>

            <!-- 商品评价列表 -->
            <div class="mt-6">
              <div v-for="(item, index) in reviewableItems" :key="item.id" class="border border-gray-200 rounded-lg mb-4 overflow-hidden">
                <!-- 商品信息 -->
                <div class="bg-gray-50 p-4 flex items-center">
                  <div class="w-16 h-16 mr-4">
                    <img :src="item.productImage || 'https://placehold.co/200x200'" :alt="item.productName" class="w-full h-full object-cover rounded">
                  </div>
                  <div class="flex-1">
                    <div class="text-gray-800 font-medium">{{ item.productName }}</div>
                    <div class="text-gray-500 text-sm mt-1">{{ formatSkuSpecifications(item.skuSpecifications) }}</div>
                    <div class="text-gray-500 text-sm">¥{{ parseFloat(item.unitPrice).toFixed(2) }} × {{ item.quantity }}</div>
                  </div>
                  <div v-if="item.isEvaluated" class="text-green-600 font-medium">
                    已评价
                  </div>
                </div>

                <!-- 评价表单 -->
                <div v-if="!item.isEvaluated" class="p-4">
                  <OrderReviewForm 
                    :item="item"
                    :order-id="orderId"
                    @submit="handleReviewSubmit"
                    @loading="handleFormLoading"
                  />
                </div>

                <!-- 已有评价显示 -->
                <div v-else-if="item.existingReview" class="p-4 bg-green-50">
                  <div class="mb-3">
                    <div class="flex items-center mb-2">
                      <span class="text-gray-600 mr-2">商品质量：</span>
                      <a-rate :model-value="item.existingReview.qualityRating" readonly allow-half />
                      <span class="ml-2 text-gray-500">{{ item.existingReview.qualityRating }}分</span>
                    </div>
                    <div class="flex items-center mb-2">
                      <span class="text-gray-600 mr-2">服务态度：</span>
                      <a-rate :model-value="item.existingReview.serviceRating" readonly allow-half />
                      <span class="ml-2 text-gray-500">{{ item.existingReview.serviceRating }}分</span>
                    </div>
                    <div class="flex items-center mb-2">
                      <span class="text-gray-600 mr-2">物流速度：</span>
                      <a-rate :model-value="item.existingReview.logisticsRating" readonly allow-half />
                      <span class="ml-2 text-gray-500">{{ item.existingReview.logisticsRating }}分</span>
                    </div>
                  </div>
                  <div v-if="item.existingReview.reviewContent" class="mb-3">
                    <div class="text-gray-600 mb-1">评价内容：</div>
                    <div class="text-gray-800 bg-white p-3 rounded border">{{ item.existingReview.reviewContent }}</div>
                  </div>
                  <div class="text-gray-500 text-sm">
                    评价时间：{{ formatTimestamp(item.existingReview.createdAt) }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 批量操作按钮 -->
            <div v-if="hasUnreviewedItems" class="mt-6 text-center">
              <a-button 
                type="primary" 
                size="large"
                :loading="isBatchSubmitting"
                @click="handleBatchSubmit"
              >
                一键好评
              </a-button>
              <p class="text-gray-500 text-sm mt-2">将对所有未评价商品给予5星好评</p>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="py-16 flex flex-col items-center justify-center">
            <i class="i-carbon-document text-6xl text-gray-300 mb-4"></i>
            <p class="text-gray-500">订单不存在或不可评价</p>
            <a @click.prevent="goToOrders" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              返回订单列表
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import UserSideNav from '~/components/mall/user/UserSideNav.vue';
import OrderReviewForm from '~/components/mall/OrderReviewForm.vue';
import { Message } from '@arco-design/web-vue';
import mallApi from '@/api/mall';

const route = useRoute();
const router = useRouter();

definePageMeta({
  layout: 'mall'
});

// 响应式数据
const orderId = ref(route.params.id);
const orderInfo = ref(null);
const reviewableItems = ref([]);
const isLoading = ref(false);
const isBatchSubmitting = ref(false);
const formLoadingStates = ref({});

// 计算属性
const hasUnreviewedItems = computed(() => {
  return reviewableItems.value.some(item => !item.isEvaluated);
});

// 时间格式化函数
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '';

  try {
    // 确保时间戳是数字类型
    let ts = Number(timestamp);

    // 如果时间戳无效，返回空字符串
    if (isNaN(ts) || ts <= 0) return '';

    // 如果时间戳是秒级（10位），转换为毫秒级
    if (ts.toString().length === 10) {
      ts = ts * 1000;
    }

    const date = new Date(ts);
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('格式化时间戳失败:', error, '原始时间戳:', timestamp);
    return '';
  }
};

// 格式化SKU规格
const formatSkuSpecifications = (specifications) => {
  if (!specifications) return '';
  
  try {
    if (typeof specifications === 'string') {
      return specifications;
    }
    
    if (typeof specifications === 'object') {
      return Object.entries(specifications)
        .map(([key, value]) => `${key}：${value}`)
        .join('，');
    }
    
    return '';
  } catch (error) {
    console.error('格式化SKU规格失败:', error);
    return '';
  }
};

// 加载可评价商品列表
const loadReviewableItems = async () => {
  if (!orderId.value) {
    Message.error('订单ID不能为空');
    return;
  }

  isLoading.value = true;
  
  try {
    const response = await mallApi.review.getReviewableItems(orderId.value);
    
    if (response.code === 200 && response.data) {
      orderInfo.value = response.data.order;
      reviewableItems.value = response.data.items;
    } else {
      Message.error(response.message || '获取可评价商品列表失败');
    }
  } catch (error) {
    console.error('获取可评价商品列表失败:', error);
    Message.error('获取可评价商品列表失败');
  } finally {
    isLoading.value = false;
  }
};

// 处理单个评价提交
const handleReviewSubmit = async (reviewData) => {
  try {
    const response = await mallApi.review.submitReview(reviewData);
    
    if (response.code === 200) {
      Message.success('评价提交成功');
      // 重新加载数据
      await loadReviewableItems();
    } else {
      Message.error(response.message || '评价提交失败');
    }
  } catch (error) {
    console.error('评价提交失败:', error);
    Message.error('评价提交失败');
  }
};

// 处理表单加载状态
const handleFormLoading = (itemId, loading) => {
  formLoadingStates.value[itemId] = loading;
};

// 批量好评
const handleBatchSubmit = async () => {
  const unreviewedItems = reviewableItems.value.filter(item => !item.isEvaluated);
  
  if (unreviewedItems.length === 0) {
    Message.warning('没有需要评价的商品');
    return;
  }

  isBatchSubmitting.value = true;

  try {
    const reviews = unreviewedItems.map(item => ({
      orderItemId: item.id.toString(),
      qualityRating: 5,
      serviceRating: 5,
      logisticsRating: 5,
      reviewContent: '商品不错，满意！',
      isAnonymous: false,
      images: []
    }));

    const response = await mallApi.review.submitBatchReviews({
      orderId: orderId.value,
      reviews
    });

    if (response.code === 200) {
      Message.success(`批量评价完成，成功${response.data.successCount}个`);
      // 重新加载数据
      await loadReviewableItems();
    } else {
      Message.error(response.message || '批量评价失败');
    }
  } catch (error) {
    console.error('批量评价失败:', error);
    Message.error('批量评价失败');
  } finally {
    isBatchSubmitting.value = false;
  }
};

// 返回订单列表
const goToOrders = () => {
  router.push('/mall/user/orders');
};

// 组件挂载时加载数据
onMounted(() => {
  loadReviewableItems();
});
</script>
