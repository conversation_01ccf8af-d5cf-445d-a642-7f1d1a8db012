<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <NuxtLink to="/mall/user/center" class="hover:text-red-600">会员中心</NuxtLink> &gt; 
      <span>消息中心</span>
    </div>

    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载消息...</p>
      </div>
      
      <!-- 未登录状态提示 -->
      <div v-else-if="!userStore.loggedIn" class="p-8 text-center">
        <div class="mb-6">
          <img src="https://placehold.co/120x120/f5f5f5/333333?text=未登录" alt="未登录" class="mx-auto mb-4 rounded-full border-4 border-gray-200">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">您尚未登录</h2>
          <p class="text-gray-600 mb-6">登录后可查看您的消息通知</p>
          <div class="flex justify-center space-x-4">
            <NuxtLink to="/mall/login" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              立即登录
            </NuxtLink>
            <NuxtLink to="/mall/register" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition">
              注册账号
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- 已登录状态 - 消息中心 -->
      <div v-else class="flex">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-4/5 p-6">
          <div class="flex justify-between items-center mb-6 pb-2 border-b">
            <h2 class="text-xl font-bold">消息中心</h2>
            <div class="flex items-center space-x-2">
              <a-button type="outline" status="danger" @click="showClearConfirm" v-if="messages.length > 0">
                <template #icon><i class="i-carbon-trash-can"></i></template>
                清空全部
              </a-button>
              <a-button type="outline" @click="handleReadAll" v-if="hasUnread">
                <template #icon><i class="i-carbon-checkmark"></i></template>
                全部已读
              </a-button>
            </div>
          </div>
          
          <!-- 消息分类标签 -->
          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <a-tabs v-model:active-key="activeTab" type="card-gutter">
              <a-tab-pane key="all" title="全部消息">
                <template #title>
                  <span class="flex items-center">
                    <i class="i-carbon-list mr-1"></i>
                    全部消息
                    <a-badge :count="unreadCount" v-if="unreadCount > 0" class="ml-2" />
                  </span>
                </template>
              </a-tab-pane>
              <a-tab-pane key="system" title="系统消息">
                <template #title>
                  <span class="flex items-center">
                    <i class="i-carbon-notification mr-1"></i>
                    系统消息
                    <a-badge :count="getUnreadCountByType('system')" v-if="getUnreadCountByType('system') > 0" class="ml-2" />
                  </span>
                </template>
              </a-tab-pane>
              <a-tab-pane key="order" title="订单消息">
                <template #title>
                  <span class="flex items-center">
                    <i class="i-carbon-shopping-cart mr-1"></i>
                    订单消息
                    <a-badge :count="getUnreadCountByType('order')" v-if="getUnreadCountByType('order') > 0" class="ml-2" />
                  </span>
                </template>
              </a-tab-pane>
<!--              <a-tab-pane key="promotion" title="活动消息">-->
<!--                <template #title>-->
<!--                  <span class="flex items-center">-->
<!--                    <i class="i-carbon-gift mr-1"></i>-->
<!--                    活动消息-->
<!--                    <a-badge :count="getUnreadCountByType('promotion')" v-if="getUnreadCountByType('promotion') > 0" class="ml-2" />-->
<!--                  </span>-->
<!--                </template>-->
<!--              </a-tab-pane>-->
<!--              <a-tab-pane key="service" title="客服消息">-->
<!--                <template #title>-->
<!--                  <span class="flex items-center">-->
<!--                    <i class="i-carbon-chat mr-1"></i>-->
<!--                    客服消息-->
<!--                    <a-badge :count="getUnreadCountByType('service')" v-if="getUnreadCountByType('service') > 0" class="ml-2" />-->
<!--                  </span>-->
<!--                </template>-->
<!--              </a-tab-pane>-->
            </a-tabs>
          </div>
          
          <!-- 消息列表 -->
          <div v-if="filteredMessages.length > 0" class="message-list">
            <div 
              v-for="(message, index) in filteredMessages" 
              :key="message.id" 
              class="message-item mb-4 rounded-lg border overflow-hidden shadow-sm transition-all duration-300"
              :class="{'border-blue-200 shadow-blue-100': !message.isRead, 'border-gray-200': message.isRead}"
            >
              <div class="flex items-start p-4">
                <!-- 消息图标 -->
                <div class="message-icon mr-4">
                  <div 
                    class="w-12 h-12 rounded-full flex items-center justify-center"
                    :class="{
                      'bg-blue-100 text-blue-600': message.type === 'system',
                      'bg-green-100 text-green-600': message.type === 'order',
                      'bg-red-100 text-red-600': message.type === 'promotion',
                      'bg-purple-100 text-purple-600': message.type === 'service'
                    }"
                  >
                    <i 
                      :class="{
                        'i-carbon-notification': message.type === 'system',
                        'i-carbon-shopping-cart': message.type === 'order',
                        'i-carbon-gift': message.type === 'promotion',
                        'i-carbon-chat': message.type === 'service'
                      }"
                      class="text-2xl"
                    ></i>
                  </div>
                </div>
                
                <!-- 消息内容 -->
                <div class="message-content flex-grow">
                  <div class="flex justify-between items-start">
                    <h3
                      class="text-lg font-medium transition-colors"
                      :class="{
                        'text-gray-900': !message.isRead,
                        'text-gray-700': message.isRead,
                        'cursor-pointer hover:text-blue-600': message.actionUrl || !message.isRead,
                        'cursor-default': !message.actionUrl && message.isRead
                      }"
                      @click="handleMessageAction(message)"
                      :title="message.actionUrl ? '点击查看详情' : (!message.isRead ? '点击标记为已读' : '')"
                    >
                      {{ message.title }}
                      <a-badge v-if="!message.isRead" status="processing" class="ml-2" />
                    </h3>
                    <div class="text-sm text-gray-500 bg-gray-50 px-2 py-1 rounded">{{ message.time }}</div>
                  </div>
                  <div
                    class="message-preview text-gray-600 mt-2 mb-4 leading-relaxed transition-colors"
                    :class="{
                      'cursor-pointer hover:text-gray-800': message.actionUrl || !message.isRead,
                      'cursor-default': !message.actionUrl && message.isRead
                    }"
                    @click="handleMessageAction(message)"
                    :title="message.actionUrl ? '点击查看详情' : (!message.isRead ? '点击标记为已读' : '')"
                  >
                    {{ message.content }}
                  </div>
                  <div class="message-actions flex justify-between items-center mt-2 pt-2 border-t border-gray-100">
                    <div>
                      <a-tag 
                        :color="{
                          'system': 'blue',
                          'order': 'green',
                          'promotion': 'red',
                          'service': 'purple'
                        }[message.type]"
                      >
                        {{ {
                          'system': '系统消息',
                          'order': '订单消息',
                          'promotion': '活动消息',
                          'service': '客服消息'
                        }[message.type] }}
                      </a-tag>
                    </div>
                    <div class="flex space-x-2">
                      <a-button 
                        size="small" 
                        type="outline" 
                        @click="handleReadMessage(index)"
                        v-if="!message.isRead"
                      >
                        <template #icon><i class="i-carbon-checkmark"></i></template>
                        标记已读
                      </a-button>
                      <a-button 
                        size="small" 
                        type="outline" 
                        status="danger" 
                        @click="handleDeleteMessage(index)"
                      >
                        <template #icon><i class="i-carbon-trash-can"></i></template>
                        删除
                      </a-button>
                      <a-button 
                        size="small" 
                        type="primary" 
                        v-if="message.actionUrl"
                        @click="handleMessageAction(message)"
                      >
                        <template #icon><i class="i-carbon-arrow-right"></i></template>
                        {{ message.actionText || '查看详情' }}
                      </a-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空状态 -->
          <div v-else class="empty-state p-8 text-center bg-gray-50 rounded-lg border border-dashed border-gray-300 mt-4">
            <img src="https://placehold.co/160x160/f5f5f5/333333?text=空" alt="暂无消息" class="mx-auto mb-4">
            <p class="text-gray-600 mb-2 text-lg">暂无{{
              {
                'all': '消息',
                'system': '系统消息',
                'order': '订单消息',
                'promotion': '活动消息',
                'service': '客服消息'
              }[activeTab]
            }}</p>
            <p class="text-gray-500 text-sm">有新消息时会在这里显示</p>
          </div>
          
          <!-- 分页 -->
          <div v-if="filteredMessages.length > 0" class="pagination flex justify-center mt-8">
            <a-pagination 
              v-model:current="currentPage" 
              :total="totalItems" 
              show-total 
              show-jumper
              show-page-size
              :page-size-options="[8, 16, 24, 32]"
              :page-size="pageSize"
              @change="handlePageChange"
              @page-size-change="handlePageSizeChange"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 清空消息确认对话框 -->
  <a-modal
    v-model:visible="showClearModal"
    @ok="handleClearAllMessages"
    @cancel="showClearModal = false"
    title="清空消息"
    simple
  >
    <div class="p-4 text-center">
      <i class="i-carbon-warning text-yellow-500 text-4xl mb-4"></i>
      <p class="text-gray-800 mb-2">确定要清空所有消息吗？</p>
      <p class="text-gray-500 text-sm">清空后将无法恢复，请谨慎操作</p>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { Message, Modal } from '@arco-design/web-vue';
import UserSideNav from '~/components/mall/user/UserSideNav.vue';
import { useMallUserStore } from '~/store/mall/user';
import { useRouter } from 'vue-router';
import messageApi from '~/api/mall/message';

definePageMeta({
  layout: 'mall'
});

// 路由
const router = useRouter();

// 用户状态管理
const userStore = useMallUserStore();

// 页面状态
const isLoading = ref(true);
const messages = ref([]);
const activeTab = ref('all');
const currentPage = ref(1);
const pageSize = ref(8);
const totalItems = ref(0);
const showClearModal = ref(false);

// 各类型未读消息数量
const unreadCounts = ref({
  all: 0,
  system: 0,
  order: 0,
  promotion: 0,
  service: 0
});

// 计算未读消息数量
const unreadCount = computed(() => {
  return unreadCounts.value.all;
});

// 是否有未读消息
const hasUnread = computed(() => unreadCount.value > 0);

// 根据类型获取未读消息数量
const getUnreadCountByType = (type) => {
  return unreadCounts.value[type] || 0;
};

// 获取未读消息数量
const fetchUnreadCounts = async () => {
  try {
    const response = await messageApi.getUnreadCount();
    if (response.code === 200) {
      unreadCounts.value.all = response.data.count;

      // 获取各类型未读数量
      const typeParams = ['system', 'order', 'promotion', 'service'];
      for (const type of typeParams) {
        const typeResponse = await messageApi.getMessageList({
          messageType: type,
          isRead: false,
          page: 1,
          pageSize: 1
        });
        if (typeResponse.code === 200) {
          unreadCounts.value[type] = typeResponse.data.pagination.total;
        }
      }

      // 更新用户存储中的未读消息数量
      userStore.updateUnreadMessageCount(unreadCounts.value.all);
    }
  } catch (error) {
    console.error('获取未读消息数量失败:', error);
  }
};

// 根据当前标签过滤消息（服务器端已经处理了分页和筛选，这里直接返回）
const filteredMessages = computed(() => {
  return messages.value;
});

// 处理分页变化
const handlePageChange = (page) => {
  currentPage.value = page;
};

// 标记消息为已读
const handleReadMessage = async (index) => {
  try {
    const messageId = filteredMessages.value[index].id;
    console.log('标记消息为已读，消息ID:', messageId);

    // 调用API标记消息为已读
    const response = await messageApi.markAsRead(messageId);
    console.log('API响应:', response);

    if (response.code === 200) {
      console.log('API调用成功，开始更新UI');

      // 更新消息状态
      const msgIndex = messages.value.findIndex(msg => msg.id === messageId);
      if (msgIndex !== -1) {
        messages.value[msgIndex].isRead = true;
        console.log('消息状态已更新');
      }

      // 重新获取未读消息数量
      await fetchUnreadCounts();
      console.log('未读消息数量已更新');

      Message.success('已标记为已读');
    } else {
      console.error('API返回错误:', response);
      throw new Error(response.message || '标记消息已读失败');
    }
  } catch (error) {
    console.error('标记已读失败:', error);
    Message.error('标记已读失败，请稍后再试');
  }
};

// 标记所有消息为已读
const handleReadAll = async () => {
  try {
    // 获取所有未读消息的ID
    const unreadMessageIds = messages.value
      .filter(msg => !msg.isRead)
      .map(msg => msg.id);

    if (unreadMessageIds.length === 0) {
      Message.info('没有未读消息');
      return;
    }

    // 调用API批量标记为已读
    const response = await messageApi.batchMarkAsRead(unreadMessageIds);

    if (response.code === 200) {
      // 更新所有消息状态
      messages.value.forEach(msg => {
        msg.isRead = true;
      });

      // 重新获取未读消息数量
      await fetchUnreadCounts();

      Message.success('已全部标记为已读');
    } else {
      throw new Error(response.message || '批量标记已读失败');
    }
  } catch (error) {
    console.error('标记全部已读失败:', error);
    Message.error('标记全部已读失败，请稍后再试');
  }
};

// 删除消息
const handleDeleteMessage = async (index) => {
  try {
    const messageId = filteredMessages.value[index].id;

    // 调用API删除消息
    const response = await messageApi.deleteMessage(messageId);

    if (response.code === 200) {
      // 删除消息
      const msgIndex = messages.value.findIndex(msg => msg.id === messageId);
      if (msgIndex !== -1) {
        // 删除消息
        messages.value.splice(msgIndex, 1);
        totalItems.value = Math.max(0, totalItems.value - 1);
      }

      // 重新获取未读消息数量
      await fetchUnreadCounts();

      Message.success('删除消息成功');
    } else {
      throw new Error(response.message || '删除消息失败');
    }
  } catch (error) {
    console.error('删除消息失败:', error);
    Message.error('删除消息失败，请稍后再试');
  }
};

const handlePageSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
};

// 显示清空确认对话框
const showClearConfirm = () => {
  showClearModal.value = true;
};

// 清空所有消息
const handleClearAllMessages = async () => {
  try {
    // 调用API清空所有消息
    const response = await messageApi.clearAllMessages();

    if (response.code === 200) {
      // 清空消息
      messages.value = [];
      totalItems.value = 0;

      // 重新获取未读消息数量
      await fetchUnreadCounts();

      // 关闭对话框
      showClearModal.value = false;

      Message.success('已清空所有消息');
    } else {
      throw new Error(response.message || '清空消息失败');
    }
  } catch (error) {
    console.error('清空消息失败:', error);
    Message.error('清空消息失败，请稍后再试');
  }
};

// 处理消息操作
const handleMessageAction = async (message) => {
  // 如果是未读消息，先调用API标记为已读
  if (!message.isRead) {
    try {
      console.log('点击消息，自动标记为已读，消息ID:', message.id);

      // 调用API标记消息为已读
      const response = await messageApi.markAsRead(message.id);
      console.log('自动标记已读API响应:', response);

      if (response.code === 200) {
        // 更新本地消息状态
        const msgIndex = messages.value.findIndex(msg => msg.id === message.id);
        if (msgIndex !== -1) {
          messages.value[msgIndex].isRead = true;
          console.log('消息状态已更新为已读');
        }

        // 重新获取未读消息数量
        await fetchUnreadCounts();
        console.log('未读消息数量已更新');

        // 显示成功提示（可选，不显示也可以）
        // Message.success('消息已标记为已读');
      } else {
        console.error('自动标记已读失败:', response);
        // 即使标记失败，也继续执行后续操作
      }
    } catch (error) {
      console.error('自动标记已读出错:', error);
      // 即使出错，也继续执行后续操作
    }
  }

  // 如果有操作链接，则跳转到目标页面
  if (message.actionUrl) {
    console.log('跳转到目标页面:', message.actionUrl);
    router.push(message.actionUrl);
  } else {
    console.log('消息没有操作链接，仅标记为已读');
  }
};

// 加载消息数据
const loadMessages = async () => {
  try {
    isLoading.value = true;
    
    // 构建查询参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value
    };

    // 根据当前标签筛选消息类型
    if (activeTab.value !== 'all') {
      if (activeTab.value === 'unread') {
        params.isRead = false;
      } else {
        params.messageType = activeTab.value;
      }
    }

    // 调用API获取消息列表
    const response = await messageApi.getMessageList(params);

    if (response.code === 200) {
      // 转换数据格式以适配前端组件
      const apiMessages = response.data.messages.map(msg => ({
        id: msg.id,
        type: msg.messageType,
        title: msg.title,
        content: msg.content,
        time: new Date(msg.createdAt).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }),
        isRead: msg.isRead,
        actionUrl: msg.actionUrl,
        actionText: msg.actionText,
        icon: msg.icon,
        priority: msg.priority
      }));

      messages.value = apiMessages;
      totalItems.value = response.data.pagination.total;

      // 获取未读消息数量
      await fetchUnreadCounts();
    } else {
      throw new Error(response.message || '获取消息列表失败');
    }
  } catch (error) {
    console.error('加载消息失败:', error);
    Message.error('加载消息失败，请稍后再试');

    // 清空消息列表，不显示模拟数据
    messages.value = [];
    totalItems.value = 0;
  } finally {
    isLoading.value = false;
  }
};

// 监听标签变化
watch(activeTab, () => {
  // 切换标签时重置页码并重新加载数据
  currentPage.value = 1;
  loadMessages();
});

// 监听分页变化
watch(currentPage, () => {
  loadMessages();
});

// 监听页面大小变化
watch(pageSize, () => {
  currentPage.value = 1;
  loadMessages();
});

// 在组件挂载后加载数据
onMounted(async () => {
  // 尝试恢复用户会话
  if (!userStore.loggedIn) {
    await userStore.restoreSession();
  }
  
  // 加载消息数据
  await loadMessages();
});
</script>

<style scoped>
.message-item {
  transition: all 0.3s ease;
}

.message-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.05);
}

:deep(.arco-tabs-nav) {
  margin-bottom: 0;
}

:deep(.arco-tabs-content) {
  padding: 0;
}
</style>
