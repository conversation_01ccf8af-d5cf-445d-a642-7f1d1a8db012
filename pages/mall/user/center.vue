<template>
  <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="p-4 sm:p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载用户信息...</p>
      </div>
      
      <!-- 未登录状态提示 -->
      <div v-else-if="!userStore.loggedIn" class="p-4 sm:p-8 text-center">
        <div class="mb-6">
          <img src="https://placehold.co/120x120/ffffff/333333?text=未登录" alt="未登录" class="mx-auto mb-4 rounded-full border-4 border-gray-200 w-24 h-24 object-cover">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">您尚未登录</h2>
          <p class="text-gray-600 mb-6">登录后可查看您的会员中心信息</p>
          <div class="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4">
            <NuxtLink to="/mall/login" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition">
              立即登录
            </NuxtLink>
            <NuxtLink to="/mall/register" class="px-6 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-lg transition">
              注册账号
            </NuxtLink>
            <!-- 测试登录按钮，实际生产环境中应移除 -->
            <button @click="testLogin" class="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition">
              测试登录
            </button>
          </div>
        </div>
        
        <!-- 未登录状态下的推荐内容 -->
        <ProductList
        title="热门推荐"
        :rightSwitch="true"
        :imageWidth="0"
        :imageHeight="0"
        :columnsPerRow="2"
        :columnsPerRowMd="3"
        :columnsPerRowLg="6"
        :count="6"
        :enableResponsiveCount="false"
        :autoLoad="true"
        />
      </div>
      
      <!-- 已登录状态 - 用户信息卡片 -->
      <div v-else class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 sm:p-6 text-white">
        <!-- 移动端布局 -->
        <div class="flex flex-col items-center md:hidden">
          <div class="mb-3">
            <a-image :src="userStore.user.avatar || 'https://placehold.co/100x100/ffffff/333333?text=头像'"
                 alt="用户头像" 
                 class="w-20 h-20 rounded-full border-4 border-white/30 shadow-lg"></a-image>
          </div>
          <div class="text-center mb-3">
            <h2 class="text-xl font-bold mb-1">{{ userStore.user.nickname || '尊敬的用户' }}</h2>
            <div class="flex items-center justify-center gap-2">
              <span class="bg-yellow-400 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-bold">
                {{ userStore.userLevel || '普通会员' }}
              </span>
              <span class="text-white/80 text-xs">
                {{ userInfo.phone || '未绑定手机' }}
              </span>
            </div>
          </div>
          
          <!-- 移动端显示用户数据 -->
          <div class="grid grid-cols-3 gap-2 w-full mb-3">
            <div class="bg-white/10 rounded-lg p-2 text-center">
              <div class="text-xs text-white/80">我的积分</div>
              <div class="text-lg font-bold">{{ userInfo.points || 0 }}</div>
            </div>
            <div class="bg-white/10 rounded-lg p-2 text-center">
              <div class="text-xs text-white/80">优惠券</div>
              <div class="text-lg font-bold">{{ userInfo.coupons || 0 }}</div>
            </div>
            <div class="bg-white/10 rounded-lg p-2 text-center">
              <div class="text-xs text-white/80">未读消息</div>
              <div class="text-lg font-bold">{{ unreadMessages || 0 }}</div>
            </div>
          </div>
          
          <NuxtLink to="/mall/user/profile" class="w-full text-center px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-white text-sm transition">
            编辑资料
          </NuxtLink>
        </div>
        
        <!-- 桌面端布局 -->
        <div class="hidden md:flex md:flex-row md:items-center">
          <div class="mr-4">
            <a-image :src="userStore.user.avatar || 'https://placehold.co/100x100/ffffff/333333?text=头像'"
                 alt="用户头像" 
                 class="w-20 h-20 rounded-full border-4 border-white/30"></a-image>
          </div>
          <div class="flex-1">
            <h2 class="text-2xl font-bold">{{ userStore.user.nickname || '尊敬的用户' }}</h2>
            <div class="flex items-center mt-1">
              <span class="bg-yellow-400 text-yellow-800 text-xs px-2 py-0.5 rounded-full font-bold">
                {{ userStore.userLevel || '普通会员' }}
              </span>
              <span class="text-white/80 text-sm ml-2">
                {{ userInfo.phone || '未绑定手机' }}
              </span>
            </div>
            <div class="flex mt-3">
              <div class="mr-6">
                <div class="text-white/80 text-sm">我的积分</div>
                <div class="text-xl font-bold">{{ userInfo.points || 0 }}</div>
              </div>
              <div>
                <div class="text-white/80 text-sm">我的优惠券</div>
                <div class="text-xl font-bold">{{ userInfo.coupons || 0 }}</div>
              </div>
            </div>
          </div>
          <div>
            <NuxtLink to="/mall/user/profile" class="inline-block px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg text-white text-sm transition">
              编辑资料
            </NuxtLink>
          </div>
        </div>
      </div>

      <div v-if="userStore.loggedIn" class="flex flex-col md:flex-row">
        <!-- 左侧导航菜单 -->
        <UserSideNav />

        <!-- 右侧内容区域 -->
        <div class="w-full md:w-4/5 p-3 sm:p-6">
          <!-- 订单概览 -->
          <div class="mb-8">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-bold text-gray-800">我的订单</h3>
              <NuxtLink to="/mall/user/orders" class="text-sm text-blue-600 hover:text-blue-800">查看全部 <i class="i-carbon-chevron-right"></i></NuxtLink>
            </div>
            <div class="grid grid-cols-3 sm:grid-cols-5 gap-4">
              <NuxtLink :to="`/mall/user/orders?orderStatus=0`" class="flex flex-col items-center justify-center p-2 sm:p-4 bg-gray-50 rounded-lg hover:bg-gray-100">
                <i class="i-carbon-document text-2xl sm:text-3xl text-blue-600 mb-1 sm:mb-2"></i>
                <span class="text-xs sm:text-sm text-gray-700">待付款</span>
                <span v-if="orderStats.pendingPayment > 0" class="mt-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[20px] text-center">
                  {{ orderStats.pendingPayment }}
                </span>
              </NuxtLink>
              <NuxtLink :to="`/mall/user/orders?orderStatus=1`" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100">
                <i class="i-carbon-delivery text-2xl sm:text-3xl text-blue-600 mb-1 sm:mb-2"></i>
                <span class="text-xs sm:text-sm text-gray-700">待发货</span>
                <span v-if="orderStats.pendingShipment > 0" class="mt-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[20px] text-center">
                  {{ orderStats.pendingShipment }}
                </span>
              </NuxtLink>
              <NuxtLink :to="`/mall/user/orders?orderStatus=2`" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100">
                <i class="i-carbon-package text-2xl sm:text-3xl text-blue-600 mb-1 sm:mb-2"></i>
                <span class="text-xs sm:text-sm text-gray-700">待收货</span>
                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 sm:h-5 sm:w-5 flex items-center justify-center">
                  {{ orderStats.pendingReceipt }}
                </span>
              </NuxtLink>
              <NuxtLink :to="`/mall/user/orders?orderStatus=3`" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100">
                <i class="i-carbon-wallet text-2xl sm:text-3xl text-blue-600 mb-1 sm:mb-2"></i>
                <span class="text-xs sm:text-sm text-gray-700">待评价</span>
                <span v-if="orderStats.pendingReview > 0" class="mt-1 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[20px] text-center">
                  {{ orderStats.pendingReview }}
                </span>
              </NuxtLink>
              <NuxtLink :to="`/mall/user/orders?orderStatus=5`" class="flex flex-col items-center justify-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100">
                <i class="i-carbon-undo text-2xl sm:text-3xl text-blue-600 mb-1 sm:mb-2"></i>
                <span class="text-xs sm:text-sm text-gray-700">退款/售后</span>
              </NuxtLink>
            </div>
          </div>

          <!-- 最近浏览 -->
          <div class="mb-6 sm:mb-8">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-bold text-gray-800">最近浏览</h3>
              <NuxtLink to="/mall/user/history" class="text-sm text-blue-600 hover:text-blue-800">查看更多 <i class="i-carbon-chevron-right"></i></NuxtLink>
            </div>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 sm:gap-4">
              <div v-for="item in browsingHistory" :key="item.id" class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition">
                <NuxtLink :to="`/mall/product/${item.productId}`">
                  <a-image :src="item.productImage" :alt="item.productName" class="w-full h-24 sm:h-32 object-contain p-2"></a-image>
                  <div class="p-3">
                    <h4 class="text-xs sm:text-sm text-gray-800 line-clamp-2 h-8 sm:h-10">{{ item.productName }}</h4>
                    <div class="text-red-600 font-bold text-xs sm:text-sm mt-1">¥{{ formatPrice(item.price) }}</div>
                  </div>
                </NuxtLink>
              </div>
            </div>
          </div>

          <!-- 收藏商品 -->
          <div class="mb-6 sm:mb-8">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-bold text-gray-800">我的收藏</h3>
              <NuxtLink to="/mall/user/favorites" class="text-sm text-blue-600 hover:text-blue-800">查看更多 <i class="i-carbon-chevron-right"></i></NuxtLink>
            </div>
            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3 sm:gap-4">
              <div v-for="item in favorites" :key="item.id" class="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition">
                <NuxtLink :to="`/mall/product/${item.productId}`">
                  <a-image :src="item.productImage" :alt="item.productName" class="w-full h-24 sm:h-32 object-contain p-2"></a-image>
                  <div class="p-3">
                    <h4 class="text-xs sm:text-sm text-gray-800 line-clamp-2 h-8 sm:h-10">{{ item.productName }}</h4>
                    <div class="text-red-600 font-bold text-xs sm:text-sm mt-1">¥{{ formatPrice(item.price) }}</div>
                  </div>
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';
import { 
  getUserOrderStats, 
  getUserFavorites, 
  getUserMessages,
  getUserCoupons,
  getUserPointsRecords,
  mockProducts
} from '~/mock/mall/userCenter';

import { useMallHistoryStore } from '~/store/mall/history';
import { ProductList } from '~/components/mall/user/product-list';
import UserSideNav from '~/components/mall/user/UserSideNav.vue'; // 导入UserSideNav组件
import mallApi from '~/api/mall'; // 导入商城API
definePageMeta({
  layout: 'mall'
});

const router = useRouter();
const userStore = useMallUserStore();
const historyStore = useMallHistoryStore();

// 用户信息
const userInfo = reactive({
  points: 0,
  coupons: 0,
  phone: ''
});

// 加载状态
const isLoading = ref(true);

// 订单统计
const orderStats = reactive({
  pendingPayment: 0,
  pendingShipment: 0,
  pendingReceipt: 0,
  pendingReview: 0
});

// 最近浏览
const browsingHistory = ref([]);

// 收藏商品
const favorites = ref([]);

// 未读消息数量
const unreadMessages = ref(0);

// 格式化价格显示
const formatPrice = (price) => {
  if (typeof price === 'number') {
    return price.toFixed(2);
  } else if (typeof price === 'string') {
    // 尝试将字符串转换为数字并格式化
    const num = parseFloat(price);
    return isNaN(num) ? price : num.toFixed(2);
  }
  return '0.00';
};

// 手动登录测试功能
const testLogin = async () => {
  try {
    console.log('开始手动登录测试...');
    const result = await userStore.login({
      username: 'test',
      password: '123456'
    });
    console.log('手动登录结果:', result);
    if (result.success) {
      // 登录成功后重新加载数据
      await loadUserData();
    }
  } catch (error) {
    console.error('手动登录失败:', error);
  }
};

// 加载用户数据的函数
const loadUserData = async () => {
  try {
    console.log('开始加载用户数据...');
    console.log('用户登录状态:', userStore.loggedIn);
    console.log('用户信息:', userStore.user);
    
    // 检查用户是否已登录
    if (!userStore.loggedIn) {
      console.warn('用户未登录，显示未登录状态页面');
      return false; // 不加载会员数据，直接返回
    }
    
    // 获取用户ID
    const userId = userStore.user?.id || 1001; // 使用模拟用户ID
    console.log('使用的用户ID:', userId);
    
    // 加载用户信息
    userInfo.phone = userStore.user?.phone || '未绑定手机';
    console.log('用户手机号:', userInfo.phone);
    
    try {
      // 加载积分信息
      console.log('开始加载积分信息...');
      const pointsRecords = getUserPointsRecords(userId);
      console.log('积分记录:', pointsRecords);
      if (pointsRecords && pointsRecords.length > 0) {
        // 计算积分总和
        userInfo.points = pointsRecords.reduce((total, record) => {
          if (record.type === 'increase') {
            return total + record.points;
          } else {
            return total - record.points;
          }
        }, 0);
        console.log('计算后的积分总和:', userInfo.points);
      } else {
        console.warn('未找到积分记录或记录为空');
      }
    } catch (pointsError) {
      console.error('加载积分信息失败:', pointsError);
    }
    
    try {
      // 加载优惠券信息
      console.log('开始加载优惠券信息...');
      const validCoupons = getUserCoupons(userId, 'valid');
      console.log('有效优惠券:', validCoupons);
      userInfo.coupons = validCoupons ? validCoupons.length : 0;
      console.log('优惠券数量:', userInfo.coupons);
    } catch (couponsError) {
      console.error('加载优惠券信息失败:', couponsError);
    }

    try {
      // 加载订单统计
      console.log('开始加载订单统计...');
      const stats = getUserOrderStats(userId);
      console.log('订单统计:', stats);
      if (stats) {
        orderStats.pendingPayment = stats.pendingPayment;
        orderStats.pendingShipment = stats.pendingShipment;
        orderStats.pendingReceipt = stats.pendingReceipt;
        orderStats.pendingReview = stats.pendingReview;
      } else {
        console.warn('未获取到订单统计数据');
      }
    } catch (orderStatsError) {
      console.error('加载订单统计失败:', orderStatsError);
    }

    try {
      // 从store中加载最近浏览
      console.log('开始加载浏览历史...');
      console.log('用户登录状态:', userStore.loggedIn, '用户信息:', userStore.user);

      // 强制启用API模式进行测试
      historyStore.useApiMode = true;

      // 初始化历史记录store
      await historyStore.init();

      // 强制尝试从API加载最近5条记录（无论登录状态）
      try {
        console.log('尝试从API加载浏览历史...');
        await historyStore.loadHistoryFromApi({ limit: 5 });
        console.log('API加载成功');
      } catch (apiError) {
        console.warn('从API加载浏览历史失败，使用本地数据:', apiError);
      }

      // 获取历史记录数据
      const storeHistoryItems = historyStore.historyItems;
      console.log('原始浏览历史:', storeHistoryItems);
      // 最多显示5条记录
      browsingHistory.value = storeHistoryItems.slice(0, 5).map(item => ({
        id: item.id,
        productId: item.id,
        productName: item.name,
        productImage: item.image,
        price: parseFloat(item.price) || 0,
        viewTime: item.viewTime
      }));
      console.log('处理后的浏览历史:', browsingHistory.value);
    } catch (historyError) {
      console.error('加载浏览历史失败:', historyError);
    }

    try {
      // 加载收藏商品 - 使用真实API
      console.log('开始加载收藏商品...');
      const response = await mallApi.favorite.getFavoriteList({
        page: 1,
        pageSize: 5,
        targetType: 1
      });

      if (response.code === 200 && response.data) {
        console.log('API收藏商品响应:', response.data);
        // 处理API响应数据格式
        const favoriteItems = response.data.records || response.data.list || response.data;
        favorites.value = Array.isArray(favoriteItems) ? favoriteItems.map(item => ({
          id: item.id,
          productId: item.targetId || item.productId,
          productName: item.goodsInfo?.name || item.productName,
          productImage: item.goodsInfo?.image || item.productImage,
          price: item.goodsInfo?.price || item.price,
          collectTime: item.createTime || item.collectTime
        })) : [];
        console.log('处理后的收藏商品:', favorites.value);
      } else {
        console.warn('获取收藏商品失败:', response.message);
        // 如果API失败，回退到mock数据
        const favs = getUserFavorites(userId);
        favorites.value = favs ? favs.slice(0, 5) : [];
      }
    } 
    catch (favoritesError) {
      console.error('加载收藏商品失败:', favoritesError);
      // 如果API调用失败，回退到mock数据
      try {
        const favs = getUserFavorites(userId);
        favorites.value = favs ? favs.slice(0, 5) : [];
        console.log('使用mock收藏数据:', favorites.value);
      } catch (mockError) {
        console.error('mock收藏数据也失败:', mockError);
        favorites.value = [];
      }
    }


    try {
      // 加载未读消息数量
      console.log('开始加载未读消息...');
      const unreadMsgs = getUserMessages(userId, false);
      console.log('未读消息:', unreadMsgs);
      unreadMessages.value = unreadMsgs ? unreadMsgs.length : 0;
      console.log('未读消息数量:', unreadMessages.value);
    } catch (messagesError) {
      console.error('加载未读消息失败:', messagesError);
    }

    // 如果数据不存在，尝试从 store 中加载
    if (orderStats.pendingPayment === 0 && orderStats.pendingShipment === 0 && 
        orderStats.pendingReceipt === 0 && orderStats.pendingReview === 0) {
      console.log('从 store 中加载订单统计...');
      console.log('store中的订单统计:', userStore.orderStats);
      if (userStore.orderStats) {
        orderStats.pendingPayment = userStore.orderStats.pendingPayment || 0;
        orderStats.pendingShipment = userStore.orderStats.pendingShipment || 0;
        orderStats.pendingReceipt = userStore.orderStats.pendingReceipt || 0;
        orderStats.pendingReview = userStore.orderStats.pendingReview || 0;
      } else {
        console.warn('store中没有订单统计数据');
      }
    }

    if (browsingHistory.value.length === 0) {
      console.log('从 store 中加载浏览历史...');
      console.log('store中的浏览历史:', userStore.browsingHistory);
      if (userStore.browsingHistory) {
        browsingHistory.value = userStore.browsingHistory.slice(0, 5);
      } else {
        console.warn('store中没有浏览历史数据');
      }
    }

    if (favorites.value.length === 0) {
      console.log('从 store 中加载收藏商品...');
      console.log('store中的收藏商品:', userStore.favorites);
      if (userStore.favorites) {
        favorites.value = userStore.favorites.slice(0, 5);
      } else {
        console.warn('store中没有收藏商品数据');
      }
    }

    console.log('会员中心数据加载完成');
    return true;
  } catch (error) {
    console.error('加载会员中心数据失败:', error);
    console.error('错误堆栈:', error.stack);
    return false;
  } finally {
    // 无论成功失败，都将加载状态设为false
    isLoading.value = false;
  }
};

// 在组件挂载后加载数据
onMounted(async () => {
  console.log('用户中心页面挂载，检查登录状态...');
  
  // 设置加载状态
  isLoading.value = true;
  
  // 尝试恢复用户会话
  if (!userStore.loggedIn) {
    console.log('用户未登录，尝试恢复会话...');
    await userStore.restoreSession();
    console.log('恢复会话后的登录状态:', userStore.loggedIn);
  }
  
  // 加载用户数据
  await loadUserData();
});
</script>

<style scoped>
/* 会员中心特定样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
:deep(.arco-image-img){
 height: 100%;
}
</style>
