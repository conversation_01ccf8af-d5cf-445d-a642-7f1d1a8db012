<template>
  <div class="container mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="loading" class="p-8 text-center">
        <div class="flex justify-center items-center h-40">
          <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent"></div>
        </div>
        <p class="text-gray-600 mt-4">正在加载订单信息...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="p-8 text-center">
        <div class="flex justify-center items-center h-40 text-red-500">
          <i class="i-carbon-warning text-5xl"></i>
        </div>
        <p class="text-red-600 mt-4">{{ errorMessage }}</p>
        <button @click="goBack" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          返回订单列表
        </button>
      </div>
      
      <!-- 内容区域 -->
      <div v-else-if="order" class="p-6">
        <!-- 返回按钮 -->
        <div class="mb-4">
          <a @click="goBack" class="inline-flex items-center text-blue-600 hover:text-blue-800 cursor-pointer">
            <i class="i-carbon-arrow-left mr-1"></i> 返回订单详情
          </a>
        </div>

        <div class="mb-6">
          <h2 class="text-xl font-bold text-gray-800 mb-1">订单支付</h2>
          <p class="text-gray-500 text-sm">订单号：{{ order.id }}</p>
        </div>

        <!-- 订单信息 -->
        <div class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
          <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 class="text-gray-800 font-medium">订单信息</h3>
          </div>
          <div class="p-4">
            <div class="grid grid-cols-2 gap-4">
              <div class="text-sm">
                <span class="text-gray-500">订单编号：</span>
                <span class="text-gray-800">{{ order.id }}</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-500">创建时间：</span>
                <span class="text-gray-800">{{ order.createdAt ? formatDateTime(order.createdAt) : '-' }}</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-500">订单状态：</span>
                <span class="text-gray-800">{{ order.orderStatusText || getOrderStatusText(order.orderStatus) }}</span>
              </div>
              <div class="text-sm">
                <span class="text-gray-500">订单金额：</span>
                <span class="text-red-600 font-bold">¥{{ parseFloat(order.totalAmount || 0).toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 商品信息 -->
        <div v-if="order && (order.items || order.orderItems) && (order.items?.length > 0 || order.orderItems?.length > 0)" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
          <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 class="text-gray-800 font-medium">商品信息</h3>
          </div>
          <div class="p-4">
            <table class="w-full">
              <thead class="bg-gray-50 text-gray-600 text-sm">
                <tr>
                  <th class="py-2 px-4 text-left">商品信息</th>
                  <th class="py-2 px-4 text-center">单价</th>
                  <th class="py-2 px-4 text-center">数量</th>
                  <th class="py-2 px-4 text-right">小计</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in order.items || order.orderItems" :key="item.id || index" class="border-b border-gray-100 last:border-b-0">
                  <td class="py-4 px-4">
                    <div class="flex items-center">
                      <div class="w-16 h-16 mr-4">
                        <a-image :src="item.productImage || '/assets/placeholder.png'" :alt="item.productName || item.spuNameSnapshot || '商品图片'" class="w-full h-full object-contain" />
                      </div>
                      <div>
                        <div class="text-gray-800 hover:text-blue-600">
                          <a :href="`/mall/product/${item.goodsSpuId || 0}`">{{ item.productName || item.spuNameSnapshot || '商品名称' }}</a>
                        </div>
                        <div v-if="item.skuSpecifications" class="text-xs text-gray-500 mt-1">
                          {{ item.skuSpecifications }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="py-4 px-4 text-center text-gray-800">
                    ¥{{ parseFloat(item.unitPrice || 0).toFixed(2) }}
                  </td>
                  <td class="py-4 px-4 text-center text-gray-800">
                    {{ item.quantity || 0 }}
                  </td>
                  <td class="py-4 px-4 text-right text-gray-800 font-medium">
                    ¥{{ parseFloat(item.totalPrice || 0).toFixed(2) }}
                  </td>
                </tr>
              </tbody>
            </table>
            
            <div class="mt-4 flex justify-end">
              <div class="w-64">
                <div class="flex justify-between py-1">
                  <span class="text-gray-500">商品总价：</span>
                  <span class="text-gray-600">¥{{ parseFloat(order.totalProductAmount || 0).toFixed(2) }}</span>
                </div>
                <div class="flex justify-between py-1">
                  <span class="text-gray-500">运费：</span>
                  <span class="text-gray-600">¥{{ parseFloat(order.shippingFee || 0).toFixed(2) }}</span>
                </div>
                <div class="flex justify-between py-1 border-t border-gray-100 mt-2 pt-2">
                  <span class="text-gray-700 font-medium">实付款：</span>
                  <span class="text-red-600 font-bold">¥{{ (parseFloat(order.totalProductAmount || 0) + parseFloat(order.shippingFee || 0)).toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 余额支付区域 - 只有当用户有余额时才显示 -->
        <div v-if="userBalance > 0" class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
          <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 class="text-gray-800 font-medium">账户余额支付</h3>
          </div>
          <div class="p-4">
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <div class="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                  <i class="iconfont icon-balance " style="font-size: 22px;"></i>
                </div>
                <div>
                  <div class="text-gray-800 font-medium">账户余额</div>
                  <div class="text-red-500 font-bold">¥{{ userBalance.toFixed(2) }}</div>
                </div>
              </div>
              <div class="flex items-center">
                <a-switch v-model="useBalance" @change="handleBalanceChange" />
                <span class="ml-2 text-gray-600">{{ useBalance ? '已启用' : '未启用' }}</span>
              </div>
            </div>
            <div v-if="useBalance" class="bg-gray-50 p-3 rounded-md flex flex-row justify-between">
              <div class="flex items-center mb-2">
                <span class="text-gray-600">可用余额：</span>
                <span class="text-gray-800">¥{{ userBalance.toFixed(2) }}</span>
              </div>
              <div class="flex items-center mb-2">
                <span class="text-gray-600">订单金额：</span>
                <span class="text-gray-800">¥{{ parseFloat(order?.totalAmount || 0).toFixed(2) }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-gray-600 whitespace-nowrap">使用余额：</span>
                <a-input-number
                  v-model="customBalanceAmount"
                  :min="0"
                  :max="maxAvailableBalance"
                  :step="10"
                  :precision="2"
                  style="width: 100%"
                  @change="handleCustomBalanceChange"
                  placeholder="请输入使用的余额金额"
                >
                  <template #prefix>
                    <span class="text-gray-500">¥</span>
                  </template>
                </a-input-number>
              </div>
              <div class="flex items-center">
                <span class="text-gray-600 whitespace-nowrap">余额抵扣：</span>
                <span class="text-red-600 font-bold">¥{{ balanceDeduction.toFixed(2) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 支付方式选择 -->
        <div class="mb-6 border border-gray-200 rounded-lg overflow-hidden">
          <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 class="text-gray-800 font-medium">选择支付方式</h3>
          </div>
          <div class="p-4">
            <div class="grid grid-cols-1 gap-4">
              <!-- 支付方式选项 -->
              <div 
                v-for="(method, index) in paymentMethods" 
                :key="index" 
                @click="selectPaymentMethod(method.id)"
                class="flex items-center p-4 border rounded-lg cursor-pointer transition-colors"
                :class="selectedPaymentMethodId === method.id ? 'border-red-500 bg-red-50' : 'border-gray-200 hover:border-red-300'">
                <div class="w-8 h-8 mr-4 flex items-center justify-center">
                  <i class="iconfont" :class="method.icon" ></i>
                </div>
                <div class="flex-1">
                  <div class="text-gray-800 font-medium">{{ method.label }}</div>
                  <div class="text-xs text-gray-500">{{ method.description }}</div>
                </div>
                <div class="ml-4">
                  <div class="w-6 h-6 rounded-full border-2 flex items-center justify-center"
                    :class="selectedPaymentMethodId === method.id ? 'border-red-500' : 'border-gray-300'">
                    <div v-if="selectedPaymentMethodId === method.id" class="w-3 h-3 bg-red-500 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 支付按钮 -->
        <div class="flex justify-between items-center">
          <div class="text-gray-600">
            <span v-if="useBalance && balanceDeduction > 0" class="text-sm">
              已使用余额抵扣 <span class="text-red-500 font-medium">¥{{ balanceDeduction.toFixed(2) }}</span>
              <span v-if="remainingAmount > 0" class="text-gray-600">，还需支付 <span class="text-red-500 font-medium">¥{{ remainingAmount.toFixed(2) }}</span></span>
              <span v-else class="text-green-600">，无需额外支付</span>
            </span>
          </div>
          <button 
            @click="confirmPayment" 
            :disabled="processing || (needOtherPayment && !selectedPaymentMethodId)" 
            class="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            <span v-if="processing" class="inline-flex items-center">
              <i class="i-carbon-circle-dash animate-spin mr-2"></i> 处理中...
            </span>
            <span v-else>
              {{ needOtherPayment ? `支付 ¥${remainingAmount.toFixed(2)}` : '确认支付' }}
            </span>
          </button>
        </div>
      </div>
    </div>

    <!-- 微信支付二维码模态框 -->
    <a-modal
      v-model:visible="showQRCode"
      title="微信支付"
      :footer="false"
      :width="450"
      :closable="false"
      :mask-closable="false"
    >
      <div class="text-center p-6">
        <!-- 支付金额显示 -->
        <div class="mb-4">
          <h3 class="text-lg font-semibold text-gray-800 mb-2">支付金额</h3>
          <div class="text-2xl font-bold text-red-500">
            ¥{{ remainingAmount.toFixed(2) }}
          </div>
          <div v-if="useBalance && balanceDeduction > 0" class="text-sm text-gray-600 mt-1">
            (已使用余额 ¥{{ balanceDeduction.toFixed(2) }})
          </div>
        </div>

        <!-- 二维码显示区域 -->
        <div class="mb-6">
          <div v-if="!qrCodeUrl" class="text-red-500 text-sm mb-2">
            正在生成二维码...
          </div>

          <!-- 二维码容器 -->
          <div class="flex justify-center mb-4">
            <div class="w-48 h-48 border-2 border-gray-200 rounded-lg flex items-center justify-center bg-white">
              <canvas
                id="qr-canvas"
                v-show="qrCodeGenerated"
                class="max-w-full max-h-full"
              ></canvas>
              <div v-if="!qrCodeGenerated && qrCodeUrl" class="text-gray-500">
                <div class="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                <div class="text-sm">生成中...</div>
              </div>
            </div>
          </div>

          <!-- 备选链接 -->
          <div v-if="qrCodeUrl && !qrCodeGenerated" class="mt-4 p-3 bg-gray-50 rounded-lg">
            <p class="text-gray-600 text-sm mb-2">二维码生成失败，请点击下方链接：</p>
            <a :href="qrCodeUrl" class="text-blue-500 underline text-sm break-all">{{ qrCodeUrl }}</a>
          </div>
        </div>

        <!-- 操作说明 -->
        <div class="mb-6">
          <p class="text-gray-600 mb-2">请使用微信扫描上方二维码完成支付</p>
          <p class="text-xs text-gray-500">支付完成后请点击下方按钮确认</p>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-center space-x-4">
          <a-button @click="cancelPayment" size="large">
            取消支付
          </a-button>
          <a-button type="primary" @click="checkPaymentStatus" size="large">
            我已支付
          </a-button>
        </div>

        <!-- 自动检测提示 -->
        <div class="mt-4 text-xs text-gray-400">
          系统将自动检测支付状态，支付成功后会自动跳转
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch, onBeforeUnmount, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';
import orderApi from '~/api/mall/order';
import orderPaymentApi from '@/api/mall/orderPayment';
import userApi from '@/api/mall/user';
import { Message } from '@arco-design/web-vue';
import QRCode from 'qrcode';

definePageMeta({
  layout: 'empty'
});



// 处理订单状态
const orderStatusMap = {
  0: '待付款',
  1: '已付款待发货',
  2: '已发货待收货',
  3: '交易成功',
  4: '已关闭',
  5: '已退款'
};

const route = useRoute();
const router = useRouter();
const userStore = useMallUserStore();
const orderId = computed(() => route.params.id);

// 用户余额数据（从API获取）
const userBalance = ref(0); // 初始余额0元

  // 状态变量
  const order = ref(null);
  const loading = ref(true);
  const error = ref(false);
  const errorMessage = ref('');
  const processing = ref(false);

  // 支付相关状态
  const showQRCode = ref(false);
  const paymentResult = ref(null);
  const paymentSn = ref('');
  const qrCodeUrl = ref('');
  const qrCodeGenerated = ref(false);

  // 余额支付相关
  const useBalance = ref(false); // 默认不启用余额支付
  const customBalanceAmount = ref(0); // 用户自定义使用的余额金额
  
  // 最大可用余额（不超过订单金额和用户余额）
  const maxAvailableBalance = computed(() => {
    if (!order.value) return 0;
    return Math.min(userBalance.value, parseFloat(order.value.totalAmount || 0));
  });
  
  // 实际抵扣的余额金额
  const balanceDeduction = computed(() => {
    if (!useBalance.value || !order.value) return 0;
    // 如果用户设置了自定义金额，则使用自定义金额（不超过最大可用余额）
    if (customBalanceAmount.value > 0) {
      return Math.min(customBalanceAmount.value, maxAvailableBalance.value);
    }
    // 否则默认使用最大可用余额
    return maxAvailableBalance.value;
  });

  // 计算剩余需要支付的金额
  const remainingAmount = computed(() => {
    if (!order.value) return 0;
    const orderAmount = parseFloat(order.value.totalAmount || 0);
    return Math.max(0, orderAmount - balanceDeduction.value);
  });

  // 是否需要选择其他支付方式
  const needOtherPayment = computed(() => remainingAmount.value > 0);

  // ...
  // 支付方式
const paymentMethods = ref([]);

const selectedPaymentMethodId = ref('');

// 支付倒计时
// const paymentTimeRemaining = ref('00:30:00');
let countdownTimer = null;
let paymentStatusTimer = null;

// 加载支付方式
const loadPaymentMethods = async () => {
  try {
    // 从API获取支付方式
    const result = await orderPaymentApi.getPaymentMethods();
    if (result.code === 200 && result.data && result.data.length > 0) {
      // 将API返回的支付方式转换为前端需要的格式
      paymentMethods.value = result.data.map(method => ({
        id: method.id,
        label: method.name,
        value: method.id,
        icon: getPaymentIcon(method.id),
        description: getPaymentDescription(method.id)
      }));

      // 默认选择第一个支付方式（不是余额支付）
      if (paymentMethods.value.length > 0) {
        selectedPaymentMethodId.value = paymentMethods.value[0].id;
      }
    } else {
      // API返回为空时使用默认支付方式
      throw new Error('API返回的支付方式为空');
    }
  } catch (error) {
    console.error('加载支付方式失败:', error);
    // 加载失败时使用默认支付方式
    paymentMethods.value = [
      {
        id: 1,
        label: '微信支付',
        value: 1,
        icon: 'icon-wechat',
        description: '使用微信扫码支付'
      },
      {
        id: 2,
        label: '支付宝',
        value: 2,
        icon: 'icon-alipay',
        description: '使用支付宝扫码支付'
      },
      {
        id: 3,
        label: '银联支付',
        value: 3,
        icon: 'icon-unionpay',
        description: '使用银联卡支付，支持各大银行借记卡及信用卡'
      },
      {
        id: 4,
        label: '货到付款',
        value: 4,
        icon: 'icon-daofu',
        description: '货到付款'
      },
      {
        id: 5,
        label: '其他',
        value: 5,
        icon: 'icon-other-pay',
        description: '其他支付方式'
      }
    ];
    // 默认选择第一个支付方式
    selectedPaymentMethodId.value = paymentMethods.value[0].id;
  }
};

// 根据支付方式ID获取图标
const getPaymentIcon = (paymentMethodId) => {
  const iconMap = {
    1: 'icon-wechat',
    2: 'icon-alipay',
    3: 'icon-unionpay',
    4: 'icon-daofu',
    5: 'icon-other-pay'
  };
  return iconMap[paymentMethodId] || 'icon-other-pay';
};

// 根据支付方式ID获取描述
const getPaymentDescription = (paymentMethodId) => {
  const descMap = {
    1: '使用微信扫码支付',
    2: '使用支付宝扫码支付',
    3: '使用银联卡支付，支持各大银行借记卡及信用卡',
    4: '货到付款',
    5: '其他支付方式'
  };
  return descMap[paymentMethodId] || '其他支付方式';
};

// 获取用户余额
const loadUserBalance = async () => {
  try {
    const result = await userApi.getBalance();
    if (result.code === 200 && result.data) {
      userBalance.value = parseFloat(result.data.balance) || 0;
      console.log('用户余额:', userBalance.value);
    } else {
      console.warn('获取用户余额失败:', result.message);
      userBalance.value = 0;
    }
  } catch (error) {
    console.error('获取用户余额失败:', error);
    userBalance.value = 0;
  }
};

// 选择支付方式
const selectPaymentMethod = (method) => {
  selectedPaymentMethodId.value = method;
};

// 处理余额支付开关变化
const handleBalanceChange = (value) => {
  useBalance.value = value;
  // 如果启用余额支付，初始化自定义余额为最大可用余额
  if (value) {
    customBalanceAmount.value = maxAvailableBalance.value;
  } else {
    customBalanceAmount.value = 0;
  }
  
  // 如果关闭余额支付，确保已选择其他支付方式
  if (!value && !selectedPaymentMethodId.value && needOtherPayment.value) {
    Message.warning('请选择一种支付方式');
  }
};

// 处理自定义余额金额变化
const handleCustomBalanceChange = (value) => {
  // 确保金额不超过最大可用余额且不小于0
  if (value > maxAvailableBalance.value) {
    customBalanceAmount.value = maxAvailableBalance.value;
    Message.warning(`最多可使用余额：¥${maxAvailableBalance.value.toFixed(2)}`);
  } else if (value < 0) {
    customBalanceAmount.value = 0;
  } else {
    customBalanceAmount.value = value;
  }
};



// 确认支付
const confirmPayment = async () => {
  // 检查支付方式
  if (needOtherPayment.value && !selectedPaymentMethodId.value) {
    Message.warning('请选择支付方式');
    return;
  }

  if (!order.value || !order.value.id) {
    Message.error('订单信息不完整，无法支付');
    return;
  }

  processing.value = true;

  try {
    // 调试：支付前检查用户登录状态和令牌
    console.log('支付前 - 用户登录状态:', userStore.loggedIn);
    console.log('支付前 - 用户令牌:', userStore.token ? '存在' : '不存在');
    
    if (process.client) {
      console.log('支付前 - localStorage中的mall_user_token:', localStorage.getItem('mall_user_token'));
      console.log('支付前 - sessionStorage中的mall_user_token:', sessionStorage.getItem('mall_user_token'));
    }
    
    // 如果用户未登录，尝试恢复会话
    if (!userStore.loggedIn || !userStore.token) {
      console.log('支付前检测到用户未登录或令牌不存在，尝试恢复会话...');
      await userStore.restoreSession();
    }
    
    // 构建支付数据
    const paymentData = {
      orderId: order.value.id.toString(),
      paymentMethodId: parseInt(selectedPaymentMethodId.value),
      // 添加余额支付信息
      useBalance: useBalance.value,
      balanceAmount: useBalance.value ? balanceDeduction.value : 0
    };

    console.log('创建支付:', paymentData);

    // 调用订单支付接口
    const result = await orderPaymentApi.createPayment(paymentData);

    // 判断后端响应是否成功（code为200表示成功）
    if (result.code === 200) {
      console.log('支付创建成功:', result.data);
      paymentResult.value = result.data;
      paymentSn.value = result.data.paymentRecord.payment_sn;

      // 显示支付创建成功消息
      Message.success('支付订单创建成功');

      // 根据支付方式处理
      if (parseInt(selectedPaymentMethodId.value) === 1) { // 微信支付
        // 检查二维码URL的路径
        const codeUrl = result.data.paymentResult?.data?.codeUrl || result.data.paymentResult?.codeUrl;
        console.log('支付结果数据:', result.data.paymentResult);
        console.log('二维码URL:', codeUrl);

        if (codeUrl) {
          qrCodeUrl.value = codeUrl;
          showQRCode.value = true;

          // 等待模态框显示后再生成二维码
          await nextTick();
          setTimeout(async () => {
            await generateQRCode(codeUrl);
          }, 100);

          // 开始轮询支付状态
          startPaymentStatusPolling();
          Message.info('请使用微信扫描二维码完成支付');
        } else {
          console.error('未找到二维码URL，支付结果:', result.data.paymentResult);
          Message.error('获取微信支付二维码失败');
        }
      } else if (parseInt(selectedPaymentMethodId.value) === 4) { // 货到付款
        Message.success('货到付款订单创建成功');

        // 跳转到订单详情页面
        router.push({
          path: `/mall/user/order-details/${order.value.id}`,
          query: { paymentSuccess: 'true' }
        });
      } else {
        Message.info('支付方式暂未完全实现，请联系客服');
      }
    } else {
      console.error('支付创建失败:', result.message);
      Message.error(result.message || '创建支付失败，请重试');
    }
  } catch (err) {
    console.error('支付处理出错:', err);
    Message.error('支付处理出错，请稍后再试');
  } finally {
    processing.value = false;
  }
};

// 取消支付
const cancelPayment = () => {
  // 停止支付状态轮询
  stopPaymentStatusPolling();

  // 关闭二维码模态框
  showQRCode.value = false;
  qrCodeUrl.value = '';
  qrCodeGenerated.value = false;

  Message.info('已取消支付');
};

// 检查支付状态（用户点击"我已支付"）
const checkPaymentStatus = async () => {
  if (!paymentSn.value) {
    Message.warning('支付流水号不存在，无法查询支付状态');
    return;
  }

  try {
    Message.loading('正在查询支付状态...', 2);

    const result = await orderPaymentApi.queryPaymentStatus(paymentSn.value);

    if (result.code === 200) {
      const { paymentStatus } = result.data;

      if (paymentStatus === 2) {
        // 支付成功
        stopPaymentStatusPolling();
        showQRCode.value = false;

        if (useBalance.value && balanceDeduction.value > 0) {
          Message.success(`支付成功！已使用余额¥${balanceDeduction.value.toFixed(2)}，在线支付¥${remainingAmount.value.toFixed(2)}`);
        } else {
          Message.success('支付成功！');
        }

        // 跳转到订单详情页面
        router.push({
          path: `/mall/user/order-details/${order.value.id}`,
          query: { paymentSuccess: 'true' }
        });
      } else if (paymentStatus === 3) {
        // 支付失败
        Message.error('支付失败，请重新支付');
      } else if (paymentStatus === 4) {
        // 支付超时
        Message.error('支付已超时，请重新创建支付');
        cancelPayment();
      } else {
        // 支付中
        Message.info('支付尚未完成，请继续等待或重新扫码');
      }
    } else {
      Message.error(result.message || '查询支付状态失败');
    }
  } catch (error) {
    console.error('查询支付状态失败:', error);
    Message.error('查询支付状态失败，请稍后再试');
  }
};

// 返回订单详情
const goBack = () => {
  router.push(`/mall/user/order-details/${orderId.value}`);
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
  return orderStatusMap[status] || '未知状态';
};

// 格式化完整日期时间
const formatDateTime = (timestamp) => {
  if (!timestamp) return '';
  
  try {
    // 如果是数字字符串，先转换为数字
    const timestampNum = typeof timestamp === 'string' ? Number(timestamp) : timestamp;
    
    // 创建日期对象
    const date = new Date(timestampNum);
    
    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      console.error('无效的时间戳:', timestamp);
      return '';
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  } catch (error) {
    console.error('格式化日期时间错误:', error);
    return '';
  }
};

// 加载订单详情
const loadOrderDetail = async (id) => {
  if (!id) {
    error.value = true;
    errorMessage.value = '订单ID为空，无法加载订单详情';
    loading.value = false;
    return;
  }
  
  error.value = false;
  errorMessage.value = '';
  loading.value = true;
  
  try {
    console.log('加载订单详情:', id);
    
    // 调试：检查本地存储中的令牌
    if (process.client) {
      console.log('支付页面 - localStorage中的mall_user_token:', localStorage.getItem('mall_user_token'));
      console.log('支付页面 - sessionStorage中的mall_user_token:', sessionStorage.getItem('mall_user_token'));
      console.log('支付页面 - localStorage中的mall_token:', localStorage.getItem('mall_token'));
    }
    
    // 确保用户已登录
    if (!userStore.loggedIn) {
      console.log('用户未登录，尝试恢复会话...');
      await userStore.restoreSession();
      
      // 调试：检查恢复会话后的状态
      console.log('恢复会话后 - 用户登录状态:', userStore.loggedIn);
      console.log('恢复会话后 - 用户令牌:', userStore.token ? '存在' : '不存在');
    }
    
    if (!userStore.loggedIn) {
      console.warn('用户未登录，无法加载订单详情');
      router.push('/mall/login');
      return;
    }
    
    // 调用获取订单详情方法
    const result = await userStore.getOrderDetail(id);
    console.log('Store返回结果:', result);
    console.log('result.success:', result.success);
    console.log('result.data:', result.data);

    if (result.success && result.data) {
      console.log('订单详情数据:', result.data);
      order.value = result.data;

      // 注意：新的订单数据结构中没有提供支付超时时间，暂不设置倒计时

      // 如果订单有支付方式标识，优先选中对应的支付方式
      if (order.value.paymentMethodId && paymentMethods.value.length > 0) {
        // 尝试匹配对应的支付方式
        const matchedPayment = paymentMethods.value.find(method => method.id == order.value.paymentMethodId);
        if (matchedPayment) {
          selectedPaymentMethodId.value = matchedPayment.id;
        }
      }
    } else {
      console.warn('未找到订单:', id, result.message);
      error.value = true;
      errorMessage.value = result.message || `未找到订单 ${id} 的详细信息`;
      order.value = null;
    }
  } catch (error) {
    console.error('加载订单详情失败:', error);
    error.value = true;
    errorMessage.value = '加载订单详情失败，请稍后再试';
    order.value = null;
  } finally {
    loading.value = false;
  }
};

// 在组件挂载后加载数据
onMounted(async () => {
  try {
    console.log('支付页面挂载，路由参数:', route.params);
    
    // 获取订单ID
    const id = orderId.value;
    console.log('当前订单ID:', id);
    
    if (!id) {
      console.error('未找到订单ID参数');
      error.value = true;
      errorMessage.value = '未找到订单ID参数';
      loading.value = false;
      return;
    }
    
    // 加载用户余额
    await loadUserBalance();

    // 加载支付方式
    await loadPaymentMethods();

    // 加载订单详情
    await loadOrderDetail(id);
    
    
    // 初始化自定义余额金额为最大可用余额
    if (useBalance.value) {
      customBalanceAmount.value = maxAvailableBalance.value;
    }
    
  } catch (err) {
    console.error('初始化支付页面失败:', err);
    error.value = true;
    errorMessage.value = '初始化支付页面失败';
    loading.value = false;
  }
});

// 监听路由参数变化，重新加载订单详情
watch(orderId, async (newId, oldId) => {
  console.log('路由参数变化:', oldId, '->', newId);
  if (newId && newId !== oldId) {
    await loadOrderDetail(newId);
  }
}, { immediate: true });

// 开始支付状态轮询
const startPaymentStatusPolling = () => {
  if (paymentStatusTimer) {
    clearInterval(paymentStatusTimer);
  }

  paymentStatusTimer = setInterval(async () => {
    try {
      if (!paymentSn.value) {
        return;
      }

      const result = await orderPaymentApi.queryPaymentStatus(paymentSn.value);

      if (result.code === 200) {
        const paymentStatus = result.data.paymentStatus;

        // 支付成功
        if (paymentStatus === 2) {
          clearInterval(paymentStatusTimer);
          showQRCode.value = false;

          // 如果是组合支付，显示特殊消息
          if (useBalance.value && balanceDeduction.value > 0) {
            Message.success(`支付成功！已使用余额¥${balanceDeduction.value.toFixed(2)}，在线支付¥${remainingAmount.value.toFixed(2)}`);
          } else {
            Message.success('支付成功！');
          }

          // 跳转到订单详情页面
          router.push({
            path: `/mall/user/order-details/${order.value.id}`,
            query: { paymentSuccess: 'true' }
          });
        }
        // 支付失败
        else if (paymentStatus === 3) {
          clearInterval(paymentStatusTimer);
          showQRCode.value = false;
          Message.error('支付失败，请重试');
        }
        // 支付过期
        else if (result.data.isExpired) {
          clearInterval(paymentStatusTimer);
          showQRCode.value = false;
          Message.warning('支付已过期，请重新发起支付');
        }
      }
    } catch (error) {
      console.error('查询支付状态失败:', error);
    }
  }, 3000); // 每3秒查询一次
};

// 停止支付状态轮询
const stopPaymentStatusPolling = () => {
  if (paymentStatusTimer) {
    clearInterval(paymentStatusTimer);
    paymentStatusTimer = null;
  }
};

// 生成二维码
const generateQRCode = async (url) => {
  try {
    console.log('开始生成二维码:', url);
    qrCodeGenerated.value = false;

    // 等待DOM更新
    await nextTick();

    // 多次尝试获取canvas元素
    let canvas = null;
    let attempts = 0;
    const maxAttempts = 10;

    while (!canvas && attempts < maxAttempts) {
      canvas = document.getElementById('qr-canvas');
      if (!canvas) {
        console.log(`第${attempts + 1}次尝试获取canvas元素失败，等待100ms后重试...`);
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }
    }

    if (!canvas) {
      console.error('多次尝试后仍未找到canvas元素');
      return;
    }

    console.log('找到canvas元素，开始生成二维码');

    // 清除canvas内容
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    await QRCode.toCanvas(canvas, url, {
      width: 192, // 48 * 4 = 192px (w-48 = 12rem = 192px)
      height: 192,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    });

    qrCodeGenerated.value = true;
    console.log('二维码生成成功');
  } catch (error) {
    console.error('生成二维码失败:', error);
    qrCodeGenerated.value = false;
  }
};

// 监听二维码显示状态变化
watch(showQRCode, async (show) => {
  if (!show) {
    stopPaymentStatusPolling();
    qrCodeGenerated.value = false;
  }
});

// 组件销毁时清除定时器
onBeforeUnmount(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
  stopPaymentStatusPolling();
});
</script>

<style scoped>
/* 支付页面特定样式 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
