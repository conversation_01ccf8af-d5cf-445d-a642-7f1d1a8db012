<template>
  <div class="container mx-auto px-4 py-6">
    <!-- 面包屑导航 -->
    <div class="breadcrumb text-sm text-gray-500 mb-4">
      <NuxtLink to="/mall" class="hover:text-red-600">首页</NuxtLink> &gt; 
      <span>购物车</span>
    </div>

    <!-- 购物车主体 -->
    <div class="cart-container bg-white rounded-lg p-6 mb-4">
      <div v-if="cartStore.cartItems.length > 0">
        <!-- 购物车头部 -->
        <div class="cart-header border-b pb-4 mb-4">
          <div class="grid grid-cols-12 gap-4 text-gray-500 text-sm">
            <div class="col-span-5 flex items-center">
              <label class="inline-flex items-center cursor-pointer">
                <input type="checkbox" 
                       :checked="cartStore.isAllSelected" 
                       @change="cartStore.toggleSelectAll"
                       class="form-checkbox h-4 w-4 text-red-600 rounded">
                <span class="ml-2">全选</span>
              </label>
              <span class="ml-4">商品信息</span>
            </div>
            <div class="col-span-2 text-center">单价</div>
            <div class="col-span-1 text-center">运费</div>
            <div class="col-span-2 text-center">数量</div>
            <div class="col-span-1 text-center">金额</div>
            <div class="col-span-1 text-center">操作</div>
          </div>
        </div>

        <!-- 购物车商品列表 -->
        <div class="cart-items">
          <div v-for="item in cartStore.cartItems" :key="item.itemKey" class="cart-item border-b py-4">
            <div class="grid grid-cols-12 gap-4 items-center">
              <!-- 商品信息 -->
              <div class="col-span-5 flex items-center">
                <label class="inline-flex items-center cursor-pointer mr-4">
                  <input type="checkbox" 
                         :checked="item.selected" 
                         @change="cartStore.toggleItemSelection(item.itemKey)"
                         class="form-checkbox h-4 w-4 text-red-600 rounded">
                </label>
                <div class="product-image flex-shrink-0 w-20 h-20 bg-gray-50 rounded flex items-center justify-center mr-4">
                  <img :src="item.image" :alt="item.name" class="max-h-full max-w-full object-contain">
                </div>
                <div class="product-info">
                  <div class="product-name text-base mb-1 line-clamp-2">
                    <NuxtLink :to="`/mall/product/${item.id}`" class="hover:text-red-600">{{ item.name }}</NuxtLink>
                  </div>
                  <div class="product-specs text-xs text-gray-500" v-if="item.specText">
                    {{ item.specText }}
                  </div>
                </div>
              </div>

              <!-- 单价 -->
              <div class="col-span-2 text-center">
                <div class="price text-red-600">¥{{ item.price }}</div>
                <div class="original-price text-gray-400 line-through text-xs" v-if="item.originalPrice">¥{{ item.originalPrice }}</div>
              </div>
              
              <!-- 运费 -->
              <div class="col-span-1 text-center">
                <div class="freight text-gray-600 text-sm" v-if="item.freightInfo">
                  <template v-if="item.freightInfo.isFreeShipping">
                    <span class="text-green-600">包邮</span>
                  </template>
                  <template v-else>
                    <span class="text-red-600 cursor-pointer">¥{{ item.freightInfo.freight }}</span>
                  </template>
                </div>
                <div class="freight text-gray-400 text-sm" v-else>暂无信息</div>
              </div>

              <!-- 数量 -->
              <div class="col-span-2 text-center">
                <div class="quantity-selector flex items-center justify-center">
                  <button class="quantity-btn w-8 h-8 border rounded-l flex items-center justify-center hover:bg-gray-50"
                          @click="updateQuantity(item.itemKey, item.quantity - 1)"
                          :disabled="item.quantity <= 1">
                    -
                  </button>
                  <input type="number" 
                         v-model.number="item.quantity" 
                         @change="updateQuantity(item.itemKey, item.quantity)"
                         class="w-12 h-8 border-t border-b text-center focus:outline-none" 
                         min="1" 
                         :max="item.stock">
                  <button class="quantity-btn w-8 h-8 border rounded-r flex items-center justify-center hover:bg-gray-50"
                          @click="updateQuantity(item.itemKey, item.quantity + 1)"
                          :disabled="item.quantity >= item.stock">
                    +
                  </button>
                </div>
                <div class="stock text-gray-500">库存：{{ item.stock }}</div>
              </div>

              <!-- 金额 -->
              <div class="col-span-1 text-center">
                <div class="subtotal text-red-600 font-medium">¥{{ (item.price * item.quantity) }}</div>
              </div>

              <!-- 操作 -->
              <div class="col-span-1 text-center">
                <a-popconfirm
                  content="确定要从购物车中删除该商品吗？"
                  @ok="removeItem(item.itemKey)"
                >
                  <a-button class="text-gray-500 hover:text-red-600">删除</a-button>
                </a-popconfirm>
              </div>
            </div>
          </div>
        </div>

        <!-- 购物车底部 -->
        <div class="cart-footer mt-4 flex justify-between items-center">
          <div class="left-actions flex items-center">
            <label class="inline-flex items-center cursor-pointer">
              <input type="checkbox" 
                     :checked="cartStore.isAllSelected" 
                     @change="cartStore.toggleSelectAll"
                     class="form-checkbox h-4 w-4 text-red-600 rounded">
              <span class="ml-2">全选</span>
            </label>
            <a-popconfirm
              content="确定要删除选中的商品吗？"
              :disabled="cartStore.selectedCount === 0"
              @ok="removeSelectedItems"
            >
              <a-button class="ml-4 text-gray-500 hover:text-red-600" :class="{'opacity-50 cursor-not-allowed': cartStore.selectedCount === 0}">删除选中商品</a-button>
            </a-popconfirm>
            <a-popconfirm
              content="确定要清空购物车吗？"
              :disabled="cartStore.cartItems.length === 0"
              @ok="clearCart"
            >
              <a-button class="ml-4 text-gray-500 hover:text-red-600" :class="{'opacity-50 cursor-not-allowed': cartStore.cartItems.length === 0}">清空购物车</a-button>
            </a-popconfirm>
          </div>
          <div class="right-actions flex items-center">
            <div class="summary-info mr-6">
              <div class="flex items-center">
                <span class="text-gray-500">已选商品 <span class="text-red-600">{{ cartStore.selectedCount }}</span> 件</span>
                <span class="mx-2 text-gray-300">|</span>
                <span class="text-gray-500">商品金额: <span class="text-red-600 text-xl font-medium">¥{{ cartStore.selectedTotalPrice }}</span></span>
                <span class="mx-2 text-gray-300">|</span>
                <span class="text-gray-500">运费: <span class="text-red-600">¥{{ cartStore.selectedFreight || '0.00' }}</span></span>
                <span class="mx-2 text-gray-300">|</span>
                <span class="text-gray-500">合计: <span class="text-red-600 text-xl font-medium">¥{{ (Number.parseFloat(cartStore.selectedTotalPrice) + Number.parseFloat(cartStore.selectedFreight || 0)) }}</span></span>
              </div>
            </div>
            <button class="checkout-btn bg-red-600 text-white px-8 py-3 rounded-full hover:bg-red-700 transition"
                    :disabled="cartStore.selectedCount === 0"
                    :class="{'opacity-50 cursor-not-allowed': cartStore.selectedCount === 0}"
                    @click="checkout">
              去结算
            </button>
          </div>
        </div>
      </div>

      <!-- 空购物车 -->
      <div v-else class="empty-cart py-12 text-center">
        <div class="empty-cart-icon mb-4 flex justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="text-gray-300"><circle cx="9" cy="21" r="1"></circle><circle cx="20" cy="21" r="1"></circle><path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path></svg>
        </div>
        <h2 class="text-xl text-gray-500 mb-4">购物车还是空的哦~</h2>
        <p class="text-gray-400 mb-6">快去选购心仪的商品吧</p>
        <NuxtLink to="/mall" class="inline-block bg-red-600 text-white px-6 py-2 rounded-full hover:bg-red-700 transition">
          去购物
        </NuxtLink>
      </div>
    </div>

    <!-- 猜你喜欢 -->
    <ProductList
      title="猜你喜欢"
      :rightSwitch="true"
      :imageWidth="0"
      :imageHeight="0"
      :columnsPerRow="2"
      :columnsPerRowMd="3"
      :columnsPerRowLg="6"
      :count="6"
      :autoLoad="true"
      :enableResponsiveCount="false"
      
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useMallCartStore } from '~/store/mall/cart';
import { useMallLocalStore } from '~/store/mall/local';
import { useMallUserStore } from '~/store/mall/user';
import { useRouter } from 'vue-router';
import mallGoodsApi from '~/api/mall/goods';
import { useDebounceFn } from '@vueuse/core';
import { ProductList } from '~/components/mall/user/product-list';
definePageMeta({
  layout: 'mall'
});

const cartStore = useMallCartStore()
const localStore = useMallLocalStore()
const userStore = useMallUserStore()
const router = useRouter()

// 推荐商品
const recommendedProducts = ref([]);

// 更新商品数量
const updateQuantity = async (itemKey, quantity) => {
  const result = await cartStore.updateQuantity(itemKey, quantity);
  if (result.success) {
    // 更新商品数量后，重新计算所有商品的运费
    await calculateFreight();
    cartStore.saveCartToLocalStorage();
    // Message.success('商品数量已更新');
  } else {
    Message.error(result.message || '更新数量失败');
  }
};

// 移除商品
const removeItem = async (itemKey) => {
  const result = await cartStore.removeFromCart(itemKey);
  if (result.success) {
    Message.success('商品已从购物车中移除');
    // 重新计算运费
    if (cartStore.cartItems.length > 0) {
      await calculateFreight();
    }
  } else {
    Message.error(result.message || '移除商品失败');
  }
};

// 移除选中商品
const removeSelectedItems = async () => {
  if (cartStore.selectedCount === 0) {
    Message.warning('请先选择要删除的商品');
    return;
  }
  
  const selectedItems = cartStore.cartItems.filter(item => item.selected);
  let success = true;
  let selectedCount = 0;
  for (const item of selectedItems) {
    selectedCount += item.quantity;
    const result = await cartStore.removeFromCart(item.itemKey);
    if (!result.success) {
      success = false;
    }
  }
  
  if (success) {
    Message.success(`已成功删除${selectedCount}件商品`);
    // 重新计算运费
    if (cartStore.cartItems.length > 0) {
      await calculateFreight();
    }
  } else {
    Message.warning('部分商品删除失败');
    // 即使部分失败，也重新计算运费
    if (cartStore.cartItems.length > 0) {
      await calculateFreight();
    }
  }
};

// 清空购物车
const clearCart = async () => {
  if (cartStore.cartItems.length === 0) {
    Message.warning('购物车已经是空的了');
    return;
  }
  
  const result = await cartStore.clearCart();
  if (result.success) {
    Message.success('购物车已清空');
  } else {
    Message.error(result.message || '清空购物车失败');
  }
};

// 去结算
const checkout = () => {
  if (cartStore.selectedCount === 0) {
    Message.warning('请先选择要结算的商品');
    return;
  }
  
  // 如果用户未登录，先跳转到登录页
  if (!userStore.loggedIn) {
    Message.warning('请先登录再进行结算');
    router.push('/mall/login?redirect=/mall/cart');
    return;
  }
  // 校验已选择商品数量是否少于库存
  const selectedItems = cartStore.cartItems.filter(item => item.selected);
  for (const item of selectedItems) {
    if (item.quantity > item.stock) {
      Message.warning(`商品${item.name}库存不足，无法结算`);
      return;
    }
  }
  // 跳转到结算页面
  router.push('/mall/checkout');
};


// 批量计算已选择商品运费的实现函数
const calculateFreightImpl = async () => {
  if (!cartStore.cartItems || cartStore.cartItems.length === 0) return;
  
  // 获取已选择的商品
  const selectedItems = cartStore.cartItems.filter(item => item.selected);
  
  // 如果没有选择任何商品，则不需要计算运费
  if (selectedItems.length === 0) {
    cartStore.selectedFreight = 0;
    return;
  }
  
  try {
    // 获取当前位置信息中的城市代码
    const provinceCode = localStore.locationInfo?.provinceCode || '440000'; // 默认使用广东省
    const cityCode = localStore.locationInfo?.cityCode || '440100'; // 默认使用广州市
    
    // 构建批量运费计算参数，只包含已选择的商品
    const items = selectedItems.map(item => ({
      spuId: item.spuId,
      skuId: item.skuId,
      quantity: item.quantity
    }));
    
    const batchFreightParams = {
      items,
      provinceCode,
      cityCode
    };
    
    console.log('批量计算已选择商品运费参数:', batchFreightParams);
    
    // 调用批量运费计算API
    const response = await mallGoodsApi.calculateBatchProductsFreight(batchFreightParams);
    console.log('批量运费计算结果:', response);
    
    if (response.code === 200 && response.data) {
      // 获取总运费和商品运费详情
      const { totalFreight, items: freightItems } = response.data;
      
      // 更新已选择商品的运费信息
      freightItems.forEach(freightItem => {
        const cartItem = selectedItems.find(
          item => item.spuId === freightItem.spuId && item.skuId === freightItem.skuId
        );
        
        if (cartItem) {
          cartItem.freightInfo = {
            freight: freightItem.freight,
            isFreeShipping: freightItem.isFreeShipping,
            chargeType: freightItem.chargeType,
            matchRegion: freightItem.matchRegion
          };
          console.log(`商品 ${cartItem.name} 运费信息已更新:`, cartItem.freightInfo);
        }
      });
      
      // 计算选中商品的运费合计
      let selectedFreight = 0;
      selectedItems.forEach(item => {
        if (item.freightInfo && !item.freightInfo.isFreeShipping) {
          selectedFreight += item.freightInfo.freight || 0;
        }
      });
      cartStore.selectedFreight = selectedFreight;
      
      console.log('已选择商品运费信息更新完成，运费合计:', selectedFreight);
    } else {
      console.warn('批量计算运费失败:', response.message);
      Message.error(response.message);
      // 设置已选择商品默认运费信息
      selectedItems.forEach(item => {
        item.freightInfo = {
          freight: 0,
          isFreeShipping: false,
          noFreightTemplate: true
        };
      });
      cartStore.selectedFreight = 0;
    }
  } catch (error) {
    console.error('批量计算运费出错:', error);
    // 设置已选择商品默认运费信息
    selectedItems.forEach(item => {
      item.freightInfo = {
        freight: 0,
        isFreeShipping: false,
        noFreightTemplate: true
      };
    });
    cartStore.selectedFreight = 0;
  }
};

// 监听购物车选择状态变化，更新运费计算
watch(
  () => cartStore.cartItems.map(item => item.selected),
  async () => {
    console.log('商品选择状态变化，重新计算商品运费');
    // 重新计算所有商品的运费
    await calculateFreight();
  },
  { deep: true }
);

// 使用防抖包装calculateFreight函数，延迟500ms执行，避免频繁调用
const calculateFreight = useDebounceFn(calculateFreightImpl, 500);

// 页面加载时初始化数据
onMounted(async () => {
  // 从本地存储加载购物车数据
  cartStore.loadCartFromLocalStorage();
  
  // 计算商品运费
  await calculateFreight();
  
  // 使用ProductList组件自动加载推荐商品
});
</script>

<style scoped>
.section-title {
  font-size: 18px;
  font-weight: bold;
  position: relative;
  padding-left: 12px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 25%;
  height: 50%;
  width: 4px;
  background-color: #e4393c;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-card {
  background-color: #fff;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.product-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  transform: translateY(-2px);
  border-color: #e4393c;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}
</style>
