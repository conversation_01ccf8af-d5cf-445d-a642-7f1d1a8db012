<template>
  <div class="forgot-password-container bg-white">
    <!-- 头部区域 -->
    <div class="header-container w-full border-b border-gray-200">
      <div class="container mx-auto px-4 py-4 flex items-center">
        <div class="logo-container cursor-pointer" @click="navigateToHome">
          <a-image :preview="false" src="https://jlc-4.oss-cn-guangzhou.aliyuncs.com/avatar/cf49eb9e51b3c935200b32595fc5bf56.png" width="120" alt="八灵logo" />
        </div>
        <div class="ml-4 text-lg text-gray-500">找回密码</div>
      </div>
    </div>
    <div class="flex-grow flex items-center justify-center py-10">
      <div class="forgot-password-box p-0 max-w-md w-full">
        <div class="shadow-md rounded-lg overflow-hidden bg-white">
          <!-- 找回密码区域 -->
          <div class="py-10 px-8 bg-white">
            <h1 class="text-2xl font-bold mb-6 text-center">
              {{ currentStep === 1 ? '找回密码' : '设置新密码' }}
            </h1>
            
            <!-- 操作提示信息已改为使用 Message 组件 -->
            
            <!-- 找回密码方式选择 -->
            <div class="mb-6" v-if="currentStep === 1">
              <a-radio-group v-model="resetMethod" type="button" size="large" class="w-full flex">
                <a-radio value="phone" class="flex-1 text-center">手机号找回</a-radio>
                <a-radio value="username" class="flex-1 text-center">用户名找回</a-radio>
              </a-radio-group>
            </div>
            
            <!-- 找回密码表单 - 第一步：验证手机号 -->
            <a-form :model="formState" class="forgot-password-form" @submit="handleResetPassword" v-if="currentStep === 1 && resetMethod === 'phone'">
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-input v-model="formState.phone" placeholder="请输入手机号" size="large" class="rounded" />

              </a-form-item>
              
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <div class="flex relative w-full">
                  <a-input v-model="formState.verificationCode" placeholder="请输入验证码" size="large" class="rounded-l" />

                  <div v-if="verificationCodeSent" class="mt-1 text-xs text-green-600">{{ verificationCodeSent }}</div>
                  <a-button @click="sendVerificationCode" size="large" class="code-btn" :disabled="cooldown > 0">
                    {{ cooldown > 0 ? `${cooldown}秒后重发` : '获取验证码' }}
                  </a-button>
                </div>
              </a-form-item>
              
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-button type="primary" html-type="submit" size="large" long :loading="isLoading">
                  {{ isLoading ? '验证中...' : '下一步' }}
                </a-button>
              </a-form-item>
            </a-form>
            
            <!-- 找回密码表单 - 第一步：输入用户名 -->
            <a-form :model="formState" class="forgot-password-form" @submit="handleVerifyUsername" v-if="currentStep === 1 && resetMethod === 'username' && !usernameVerified">
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-input v-model="formState.username" placeholder="请输入用户名" size="large" class="rounded" />

              </a-form-item>
              
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-button type="primary" html-type="submit" size="large" long :loading="isLoading">
                  {{ isLoading ? '验证中...' : '下一步' }}
                </a-button>
              </a-form-item>
            </a-form>
            
            <!-- 选择找回密码方式 -->
            <div class="recovery-options" v-if="currentStep === 1 && resetMethod === 'username' && usernameVerified && !selectedRecoveryMethod">
              <h3 class="text-lg font-medium text-gray-700 mb-4">请选择找回密码的方式：</h3>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- 邮箱验证卡片 -->
                <div 
                  class="recovery-card p-4 border-2 rounded-lg cursor-pointer transition-all duration-300 hover:shadow-md"
                  :class="{'border-red-500 shadow-lg': tempRecoveryMethod === 'email', 'border-transparent': tempRecoveryMethod !== 'email'}"
                  @click="tempRecoveryMethod = 'email'"
                >
                  <div class="flex items-center mb-2">
                    <i class="icon-mail text-2xl mr-2 text-primary-500"></i>
                    <h4 class="text-lg font-medium">邮箱验证</h4>
                  </div>
                  <p class="text-gray-600 text-sm mb-2">我们将发送重置密码链接到您的邮箱</p>
                  <p class="text-gray-500 text-xs">邮箱: {{ maskedEmail }}</p>
                </div>
                
                <!-- 安全问题卡片 -->
                <div 
                  class="recovery-card p-4 border-2 rounded-lg cursor-pointer transition-all duration-300 hover:shadow-md"
                  :class="{'border-red-500 shadow-lg': tempRecoveryMethod === 'security', 'border-transparent': tempRecoveryMethod !== 'security'}"
                  @click="tempRecoveryMethod = 'security'"
                >
                  <div class="flex items-center mb-2">
                    <i class="icon-shield text-2xl mr-2 text-primary-500"></i>
                    <h4 class="text-lg font-medium">安全问题</h4>
                  </div>
                  <p class="text-gray-600 text-sm mb-2">回答您设置的安全问题</p>
                  <p class="text-gray-500 text-xs">共{{ securityQuestions.length }}个安全问题</p>
                </div>
              </div>
              
              <div class="mt-4">
                <a-button type="primary" size="large" long :disabled="!tempRecoveryMethod" @click="confirmRecoveryMethod">
                  确认选择
                </a-button>
              </div>
            </div>
            
            <!-- 邮箱验证方式 -->
            <div class="email-recovery" v-if="currentStep === 1 && resetMethod === 'username' && selectedRecoveryMethod === 'email'">
              <div class="text-center py-4">
                <i class="icon-mail text-5xl text-primary-500 mb-4"></i>
                <h3 class="text-lg font-medium mb-2">重置密码链接已发送</h3>
                <p class="text-gray-600 mb-4">我们已将重置密码链接发送到您的邮箱：{{ maskedEmail }}</p>
                <p class="text-gray-500 text-sm mb-6">请登录您的邮箱查收并点击链接重置密码</p>
                
                <div class="flex justify-center space-x-4">
                  <a-button @click="resendEmailLink" :loading="isLoading">重新发送</a-button>
                  <a-button type="primary" @click="navigateToLogin">返回登录</a-button>
                </div>
              </div>
            </div>
            
            <!-- 安全问题验证方式 -->
            <a-form :model="formState" class="forgot-password-form" @submit="handleUsernameReset" v-if="currentStep === 1 && resetMethod === 'username' && selectedRecoveryMethod === 'security'">

              <a-form-item  v-for="question in securityQuestions" :key="question.id" :style="{marginBottom: '16px'}" :hide-label="true">
                <div class="question-item">
                  <div>{{ question.question_text }}</div>
                  <a-input v-model="question.answer" placeholder="请输入安全问题答案" size="large" class="rounded" />
                </div>
              </a-form-item>
              
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-button type="primary" html-type="submit" size="large" long :loading="isLoading">
                  {{ isLoading ? '验证中...' : '验证答案' }}
                </a-button>
              </a-form-item>
              
              <div class="text-center mt-2">
                <a href="#" @click.prevent="backToRecoveryOptions" class="text-primary-500 hover:text-primary-700">返回选择其他方式</a>
              </div>
            </a-form>
            
            <!-- 找回密码表单 - 第二步：设置新密码 -->
            <a-form :model="formState" class="forgot-password-form" @submit="handleSetNewPassword" v-if="currentStep === 2">
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-input-password v-model="formState.newPassword" placeholder="请设置新密码" size="large" class="rounded" />

              </a-form-item>
              
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-input-password v-model="formState.confirmPassword" placeholder="请再次输入新密码" size="large" class="rounded" />

              </a-form-item>
              
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-button type="primary" html-type="submit" size="large" long :loading="isLoading">
                  {{ isLoading ? '提交中...' : '重置密码' }}
                </a-button>
              </a-form-item>
            </a-form>
            
            <div class="text-center mt-4">
              <div class="text-sm text-gray-600">
                记起密码了？
                <NuxtLink to="/mall/login" class="text-red-600 hover:text-red-800 ml-1">立即登录</NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部区域 -->
    <div class="footer-container w-full border-t border-gray-200 bg-gray-50">
      <div class="container mx-auto px-4 py-6">
        <div class="text-center text-gray-500 text-xs">
          <div class="links mb-2">
            <a href="#" class="hover:text-red-600">关于我们</a>
            <span class="mx-2">|</span>
            <a href="#" class="hover:text-red-600">联系我们</a>
            <span class="mx-2">|</span>
            <a href="#" class="hover:text-red-600">帮助中心</a>
            <span class="mx-2">|</span>
            <a href="#" class="hover:text-red-600">法律声明</a>
            <span class="mx-2">|</span>
            <a href="#" class="hover:text-red-600">隐私政策</a>
            <span class="mx-2">|</span>
            <a href="#" class="hover:text-red-600">销售联盟</a>
          </div>
          <div class="copyright">
            Copyright 2023-2025 八灵云 版权所有
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';
import mallApi from '@/api/mall';
import { Message } from '@arco-design/web-vue';

definePageMeta({
  layout: 'empty',
  title: '找回密码'
});

const router = useRouter();
const userStore = useMallUserStore();
const resetToken = ref('');
// 当前步骤：1-验证手机号/用户名，2-设置新密码
const currentStep = ref(1);

// 找回密码方式：'phone'-手机号找回，'username'-用户名找回
const resetMethod = ref('phone');

// 表单数据
const formState = reactive({
  phone: '',
  username: '',
  securityAnswer: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
});

// 错误信息
const errors = reactive({
  phone: '',
  username: '',
  securityAnswer: '',
  verificationCode: '',
  newPassword: '',
  confirmPassword: ''
});

// 状态变量
const cooldown = ref(0);
const verificationCodeSent = ref('');
const isLoading = ref(false);
// 保存后端返回的验证码
const serverCaptcha = ref('');
// 保存用户名方式的用户名
const resetUsername = ref('');
// 安全问题列表
const securityQuestions = ref([]);
// 选中的安全问题
const selectedQuestion = ref('');
// 用户名验证状态
const usernameVerified = ref(false);
// 选择的找回方式
const selectedRecoveryMethod = ref('');
// 临时选择的找回方式
const tempRecoveryMethod = ref('');
// 用户邮箱（隐藏部分字符）
const maskedEmail = ref('');

// 点击logo返回首页
const navigateToHome = () => {
  navigateTo('/mall');
};

// 验证手机号
const validatePhone = () => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(formState.phone)) {
    Message.error('请输入正确的手机号码');
    return false;
  }
  return true;
};

// 发送验证码
const sendVerificationCode = async () => {
  // 验证手机号
  if (!validatePhone()) {
    return;
  }

  try {
    // 调用发送忘记密码短信验证码接口
    const result = await userStore.sendForgotPasswordSmsCode(formState.phone);

    if (result.success) {
      // 开始倒计时
      cooldown.value = 60;
      const timer = setInterval(() => {
        cooldown.value--;
        if (cooldown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);

      // 显示发送成功提示
      verificationCodeSent.value = result.message || '验证码已发送到您的手机';
      Message.success(result.message || '验证码已发送到您的手机');
    } else {
      Message.error(result.message || '发送验证码失败，请稍后再试');
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    Message.error('发送验证码失败，请稍后再试');
  }
};

// 验证手机号方式表单
const validatePhoneMethod = () => {
  let isValid = true;
  
  // 验证手机号
  if (!validatePhone()) {
    isValid = false;
  }
  
  // 验证验证码
  if (!formState.verificationCode) {
    Message.error('请输入验证码');
    isValid = false;
  }
  
  return isValid;
};

// 重置方式变化时清空安全问题
watch(resetMethod, (newValue) => {
  // 切换方式时重置相关状态
  securityQuestions.value = [];
  selectedQuestion.value = '';
  formState.securityAnswer = '';
  errors.securityAnswer = '';
  
  if (newValue === 'phone') {
    formState.username = '';
    errors.username = '';
  } else {
    formState.phone = '';
    formState.verificationCode = '';
    errors.phone = '';
    errors.verificationCode = '';
    verificationCodeSent.value = '';
  }
});

// 验证第二步表单
const validateStepTwo = () => {
  let isValid = true;
  
  // 验证密码
  if (formState.newPassword.length < 6) {
    Message.error('密码长度不能少于6个字符');
    isValid = false;
  }
  
  // 验证确认密码
  if (formState.newPassword !== formState.confirmPassword) {
    Message.error('两次输入的密码不一致');
    isValid = false;
  }
  
  return isValid;
};

// 处理第一步 - 手机号方式
const handleResetPassword = async (e) => {
  // 阻止表单默认提交行为
  if (e && typeof e.preventDefault === 'function') {
    e.preventDefault();
  }
  // 验证表单
  if (!validatePhoneMethod()) {
    return;
  }
  
  try {
    // 不需要立即验证，直接进入第二步
    currentStep.value = 2;
    Message.success('请设置新密码');
  } catch (error) {
    console.error('处理失败:', error);
    Message.error('处理失败，请稍后再试');
  }
};

// 验证用户名并获取找回密码选项
const handleVerifyUsername = async (e) => {
  // 阻止表单默认提交行为
  if (e && typeof e.preventDefault === 'function') {
    e.preventDefault();
  }
  
  // 验证用户名
  if (!formState.username || formState.username.trim() === '') {
    Message.error('请输入用户名');
    return;
  }
  
  try {
    isLoading.value = true;
    
    // 直接调用 API 接口获取安全问题
    const response = await mallApi.user.auth.getSecurityQuestionsByUsername(formState.username);
    console.log('获取安全问题响应:', response);
    
    if (response.code === 200) {
      // 验证成功，保存用户名和安全问题
      resetUsername.value = formState.username;
      usernameVerified.value = true;
      const questions = response.data.questions.map((question) => {
        question.answer = '';
        return question;
      });
      // 保存安全问题和邮箱信息
      securityQuestions.value = questions || [];
      maskedEmail.value = maskEmail(response.data.email || '');
      
      // 显示成功消息
      Message.success('用户名验证成功，请选择找回密码的方式');
    } else {
      // 验证失败，显示错误消息
      Message.error(response.message || '用户名验证失败，该用户可能不存在');
    }
  } catch (error) {
    console.error('验证用户名失败:', error);
    Message.error('验证用户名失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};

// 确认选择的找回方式
const confirmRecoveryMethod = async () => {
  if (!tempRecoveryMethod.value) {
    Message.warning('请选择找回密码的方式');
    return;
  }
  
  try {
    isLoading.value = true;
    
    // 在这里进行用户名验证
    const result = await userStore.verifyUsername(resetUsername.value);
    
    if (result.success) {
      // 验证成功，设置找回方式
      selectedRecoveryMethod.value = tempRecoveryMethod.value;
      
      // 获取并保存安全问题
      if (result.data.securityQuestions && result.data.securityQuestions.length > 0) {
        securityQuestions.value = result.data.securityQuestions;
        selectedQuestion.value = result.data.securityQuestions[0].id; // 默认选中第一个问题
      }
      
      // 处理邮箱显示（隐藏部分字符）
      if (result.data.email) {
        maskedEmail.value = maskEmail(result.data.email);
      } else {
        maskedEmail.value = '未设置邮箱';
        // 如果选择了邮箱方式但没有设置邮箱，提示用户
        if (tempRecoveryMethod.value === 'email') {
          Message.warning('您未设置邮箱，请选择安全问题方式');
          tempRecoveryMethod.value = 'security';
          return;
        }
      }
      
      // 如果选择邮箱方式，自动发送重置密码链接
      if (tempRecoveryMethod.value === 'email') {
        await sendResetPasswordEmail();
      }
    } else {
      Message.error(result.message || '用户名验证失败，该用户可能不存在');
      // 验证失败，重置状态
      usernameVerified.value = false;
      tempRecoveryMethod.value = '';
    }
  } catch (error) {
    console.error('验证用户名失败:', error);
    Message.error('验证用户名失败，请稍后再试');
    // 验证失败，重置状态
    usernameVerified.value = false;
    tempRecoveryMethod.value = '';
  } finally {
    isLoading.value = false;
  }
};

// 发送重置密码邮件
const sendResetPasswordEmail = async () => {
  try {
    isLoading.value = true;
    
    // 调用发送重置密码邮件接口
    const result = await userStore.sendResetPasswordEmail({
      username: resetUsername.value
    });
    
    if (result.success) {
      Message.success('重置密码链接已发送到您的邮箱');
    } else {
      Message.error(result.message || '发送重置密码邮件失败');
    }
  } catch (error) {
    console.error('发送重置密码邮件失败:', error);
    Message.error('发送重置密码邮件失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};

// 重新发送重置密码邮件
const resendEmailLink = async () => {
  await sendResetPasswordEmail();
};

// 返回选择找回方式
const backToRecoveryOptions = () => {
  selectedRecoveryMethod.value = '';
};

// 跳转到登录页面
const navigateToLogin = () => {
  router.push('/mall/login');
};

// 隐藏邮箱部分字符
const maskEmail = (email) => {
  if (!email || typeof email !== 'string') return '';
  
  const parts = email.split('@');
  if (parts.length !== 2) return email;
  
  const [username, domain] = parts;
  
  // 如果用户名小于等于3个字符，只显示第一个字符
  // 否则显示第一个和最后一个字符，中间用*代替
  let maskedUsername = '';
  if (username.length <= 3) {
    maskedUsername = username.charAt(0) + '*'.repeat(username.length - 1);
  } else {
    maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);
  }
  
  return `${maskedUsername}@${domain}`;
};

// 处理用户名方式的找回密码
const handleUsernameReset = async (e) => {
  // 阻止表单默认提交行为
  if (e && typeof e.preventDefault === 'function') {
    e.preventDefault();
  }
  
  try {
    // 验证安全问题答案不为空
    const hasEmptyAnswers = securityQuestions.value.some(question => !question.answer || question.answer.trim() === '');
    if (hasEmptyAnswers) {
      Message.error('请回答所有安全问题');
      return;
    }
    
    isLoading.value = true;
    
    // 构建验证安全问题请求体
    const requestData = {
      username: resetUsername.value,
      answers: securityQuestions.value.map(question => ({
        question_id: question.id,
        answer: question.answer
      }))
    };
    
    // 调用验证安全问题答案接口
    const response = await mallApi.user.auth.verifySecurityQuestions(requestData);
    console.log(response,'调用验证安全问题答案接口response')
    if (response.code === 200) {
      // 验证成功，进入第二步
      currentStep.value = 2;
      resetToken.value = response.data.token;
      // 保存token到localStorage
      localStorage.setItem('mall_resetToken', response.data.token);
      Message.success('安全验证成功，请设置新密码');
    } else {
      Message.error(response.message || '安全问题答案错误');
    }
  } catch (error) {
    console.error('验证失败:', error);
    Message.error('验证失败，请稍后再试');
  } finally {
    isLoading.value = false;
  }
};

// 处理设置新密码
const handleSetNewPassword = async (e) => {
  // 阻止表单默认提交行为
  if (e && typeof e.preventDefault === 'function') {
    e.preventDefault();
  }
  // 验证表单
  if (!validateStepTwo()) {
    return;
  }
  
  try {
    isLoading.value = true;
    
    // 准备重置密码数据
    let resetData = {};
    
    // 根据不同的找回方式准备不同的参数
    if (resetMethod.value === 'phone') {
      // 手机号方式，将手机号、验证码和新密码一起发送
      resetData = {
        username: formState.phone,
        captcha: formState.verificationCode,
        newPassword: formState.newPassword
      };
      
      // 调用手机号重置密码接口
      const response = await mallApi.user.auth.resetPasswordByPhone(resetData);
      
      if (response.code === 200) {
        Message.success('密码重置成功！即将跳转到登录页面...');
        
        // 重置成功后跳转到登录页面
        setTimeout(() => {
          router.push({
            path: '/mall/login',
            query: { reset: 'success' }
          });
        }, 1500);
      } else {
        Message.error(response.message || '密码重置失败，请稍后再试');
      }
    } else {
      // 用户名方式，需要使用之前验证安全问题获取的token
      const token = resetToken.value || localStorage.getItem('mall_resetToken');
      if (!token) {
        Message.error('重置令牌已失效，请重新验证');
        currentStep.value = 1;
        return;
      }
      
      resetData = {
        token: token,
        newPassword: formState.newPassword,
        resetType: 'username'
      };
      
      // 调用用户名重置密码接口
      const response = await mallApi.user.auth.resetPasswordByToken(resetData);
      
      if (response.code === 200) {
        Message.success('密码重置成功！即将跳转到登录页面...');
        
        // 重置成功后跳转到登录页面
        setTimeout(() => {
          router.push({
            path: '/mall/login',
            query: { reset: 'success' }
          });
        }, 1500);
      } else {
        Message.error(response.message || '密码重置失败，请稍后再试');
      }
    }
  } catch (error) {
    console.error('密码重置失败:', error);
    Message.error('密码重置失败，请稍后再试');
  } finally {
    isLoading.value = false;
    // 清除token
    localStorage.removeItem('mall_resetToken');
  }
};
</script>

<style scoped>
.question-item{
  display: flex;
  width: 100%;
  align-items: flex-start;
  flex-direction: column;
}
.forgot-password-container {
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.forgot-password-box {
  width: 100%;
  max-width: 660px;
}

.code-btn {
  position: absolute;
  right: 0;
  color: red;
}

/* 覆盖Arco Design的主题色 */
:deep(.arco-btn-primary) {
  background-color: #dc2626;
  border-color: #dc2626;
}

:deep(.arco-btn-primary:hover) {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

:deep(.arco-checkbox-checked .arco-checkbox-icon) {
  background-color: #dc2626;
  border-color: #dc2626;
}

/* 修改单选按钮组的颜色 */
:deep(.arco-radio-button.arco-radio-checked) {
  color: #dc2626;
  border-color: #dc2626;
}

:deep(.arco-radio-button.arco-radio-checked::before) {
  background-color: #dc2626;
}
</style>
