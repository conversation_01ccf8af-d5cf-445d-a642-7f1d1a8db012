<template>
  <div class="login-container bg-gray-100">
    <!-- 左上角logo -->
    <div class="logo-container absolute top-6 left-6 cursor-pointer" @click="navigateToHome">
      <a-image :preview="false" src="https://jlc-4.oss-cn-guangzhou.aliyuncs.com/avatar/cf49eb9e51b3c935200b32595fc5bf56.png" width="120" alt="八灵logo" />
    </div>
    <div class="login-box p-0 max-w-3xl w-full">
      <div class="flex rounded-lg overflow-hidden bg-white">
        <!-- 左侧扫码登录 -->
        <div class="w-5/12 py-10 px-6 bg-white flex flex-col items-center justify-center border-r border-gray-100">
          <h2 class="text-lg font-medium mb-6">微信扫码登录</h2>
          <div class="qrcode-container mb-6 relative">
            <!-- 加载中状态 -->
            <div v-if="qrcodeLoading" class="flex justify-center items-center" style="width: 180px; height: 180px;">
              <a-spin />
            </div>
            <!-- 二维码显示 -->
            <a-image v-else-if="qrcodeUrl" :src="qrcodeUrl" width="180px" height="180px" alt="扫码登录" class="qrcode border border-gray-200 rounded" />
            <!-- 二维码过期或错误 -->
            <div v-else-if="qrcodeError" class="flex flex-col justify-center items-center border border-gray-200 rounded" style="width: 180px; height: 180px;">
              <a-icon type="close-circle" class="text-red-500 text-2xl mb-2" />
              <p class="text-sm text-gray-500">{{ qrcodeError }}</p>
              <a-button type="primary" size="small" class="mt-2" @click="refreshQrCode">刷新二维码</a-button>
            </div>
            <!-- 扫码成功等待确认 -->
            <div v-if="qrcodeScanSuccess" class="absolute inset-0 bg-white bg-opacity-90 flex flex-col items-center justify-center rounded">
              <a-icon type="check-circle" class="text-green-500 text-2xl mb-2" />
              <p class="text-sm text-gray-700">扫码成功</p>
              <p class="text-xs text-gray-500">请在微信中确认登录</p>
            </div>
          </div>
          <p class="text-sm text-gray-500 text-center">打开<span style="color: red">微信</span>扫一扫上方二维码</p>
          <p v-if="qrcodeExpiresIn > 0" class="text-xs text-gray-400 mt-2">二维码有效期: {{ Math.floor(qrcodeExpiresIn / 60) }}:{{ (qrcodeExpiresIn % 60).toString().padStart(2, '0') }}</p>
        </div>
        
        <!-- 右侧登录区域 -->
        <div class="w-7/12 py-10 px-6 bg-white">
          <!-- 登录选项卡 -->
          <div class="login-tabs flex border-b border-gray-200">
            <div 
              class="tab-item px-6 py-2 cursor-pointer" 
              :class="{'text-red-500 border-b-2 border-red-500 font-medium': activeTab === 'password'}"
              @click="activeTab = 'password'"
            >
              密码登录
            </div>
            <div 
              class="tab-item px-6 py-2 cursor-pointer" 
              :class="{'text-red-500 border-b-2 border-red-500 font-medium': activeTab === 'sms'}"
              @click="activeTab = 'sms'"
            >
              短信登录
            </div>
          </div>
          
          <!-- 错误提示 -->
          <div v-if="loginError" class="mb-4 p-3 bg-red-50 text-red-600 rounded-md text-sm">
            {{ loginError }}
          </div>
          
          <!-- 密码登录表单 -->
          <div v-if="activeTab === 'password'" class="mt-6">
            <a-form :model="formState" class="login-form">
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-input v-model="formState.username" placeholder="八灵账号/手机号" size="large" class="rounded" />
              </a-form-item>
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-input-password v-model="formState.password" placeholder="请输入密码" size="large" class="rounded" />
              </a-form-item>
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <a-checkbox v-model="formState.remember">记住我</a-checkbox>
                  </div>
                </div>
              </a-form-item>
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-button type="primary" @click="handleLogin" :loading="isLoading" class="w-full rounded" size="large" :style="{backgroundColor: 'red', borderColor: 'red'}">
                  {{ isLoading ? '登录中...' : '登录' }}
                </a-button>
              </a-form-item>
            </a-form>
          </div>
          
          <!-- 短信登录表单 -->
          <div v-else class="mt-6">
            <a-form :model="formState" class="login-form">
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-input v-model="formState.phone" placeholder="请输入手机号" size="large" class="rounded" />
              </a-form-item>
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <div class="relative w-full">
                  <a-input v-model="formState.smsCode" placeholder="请输入验证码" size="large" class="rounded" />
                  <a-button  :disabled="smsSending" @click="sendSmsCode" size="large" class="code-btn">
                    {{ smsSending ? `${countdown}秒后重发` : '获取验证码' }}
                  </a-button>
                </div>
              </a-form-item>
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <div class="flex items-center justify-start">
                  <div class="flex items-center">
                    <a-checkbox v-model="formState.remember">记住我</a-checkbox>
                  </div>
                </div>
              </a-form-item>
              <a-form-item :style="{marginBottom: '16px'}" :hide-label="true">
                <a-button type="primary" @click="handleLogin" :loading="isLoading" class="w-full rounded" size="large" :style="{backgroundColor: 'red', borderColor: 'red'}">
                  {{ isLoading ? '登录中...' : '登录' }}
                </a-button>
              </a-form-item>
            </a-form>
          </div>
          
          <!-- 底部其他登录方式 -->
          <div class="flex justify-between items-center mt-2">
            <div class="flex items-center">
                <NuxtLink to="/mall/forgot-password" class="text-red-600 hover:text-red-800">忘记密码?</NuxtLink>
              <span class="mx-1 text-gray-300">|</span>
              <NuxtLink to="/mall/register" class="text-red-600 hover:text-red-800">免费注册</NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRouter, useRoute } from 'vue-router';
import { useMallUserStore } from '~/store/mall/user';
import { navigateTo } from '#app';
import mallApi from '~/api/mall';

definePageMeta({
  layout: 'empty'
});

const router = useRouter();
const route = useRoute();
const userStore = useMallUserStore();

// 在组件挂载后检查是否已经登录
onMounted(async () => {
  try {
    // 如果用户已登录，跳转到首页
    if (userStore.loggedIn) {
      router.push('/mall');
    }
  } catch (error) {
    console.error('检查登录状态失败:', error);
  }
});

const formState = reactive({
  phone: '',
  password: '',
  smsCode: '',
  remember: true
});

// 登录相关状态
const activeTab = ref('password'); // 当前激活的登录选项卡
const isLoading = ref(false); // 登录加载状态
const loginError = ref(''); // 登录错误信息
const countdown = ref(0); // 验证码倒计时
let timer = null; // 倒计时定时器
const smsSending = ref(false); // 是否正在发送短信验证码
// 微信扫码登录相关状态
const qrcodeLoading = ref(true); // 二维码加载状态
const qrcodeUrl = ref(''); // 二维码图片URL
const qrcodeError = ref(''); // 二维码错误信息
const qrcodeScanSuccess = ref(false); // 是否已扫码
const qrcodeExpiresIn = ref(0); // 二维码剩余有效时间（秒）
const qrcodeTicket = ref(''); // 二维码票据，用于查询扫码状态
let qrcodeTimer = null; // 二维码状态轮询定时器
let qrcodeCountdownTimer = null; // 二维码倒计时定时器

// 发送短信验证码
const sendSmsCode = async () => {
  if (!formState.phone) {
    Message.error('请输入手机号');
    return;
  }

  // 验证手机号格式
  const isValidPhone = /^1[3-9]\d{9}$/.test(formState.phone);
  if (!isValidPhone) {
    Message.error('请输入正确的手机号');
    return;
  }

  try {
    smsSending.value = true;
    countdown.value = 60;

    // 调用发送短信验证码的API
    const result = await userStore.sendSmsCode(formState.phone);

    if (result.success) {
      Message.success(result.message || '验证码发送成功');

      timer = setInterval(() => {
        if (countdown.value > 0) {
          countdown.value--;
        } else {
          clearInterval(timer);
          smsSending.value = false;
        }
      }, 1000);
    } else {
      Message.error(result.message || '验证码发送失败');
      smsSending.value = false;
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    Message.error('发送验证码失败: ' + (error.message || '未知错误'));
    smsSending.value = false;
  }
};

// 登录处理
const handleLogin = async () => {
  try {
    console.log('开始登录处理...');
    
    if (activeTab.value === 'password') {
      if (!formState.username || !formState.password) {
        Message.error('请输入账号和密码');
        return;
      }
      
      // 检查是否是手机号
      const isPhone = /^1[3-9]\d{9}$/.test(formState.username);
      console.log('用户名是否为手机号:', isPhone);
      
      // 登录请求
      console.log('发送登录请求:', {
        username: formState.username,
        password: formState.password ? '******' : null,
        isPhone
      });
      
      isLoading.value = true;
      loginError.value = '';
      
      // 调用登录API
      const result = await userStore.login({
        username: formState.username,
        password: formState.password,
        remember: formState.remember
      });
      
      console.log('登录结果:', result);
      
      if (!result.success) {
        // 如果是手机号登录失败，提供更具体的错误信息
        if (isPhone) {
          loginError.value = result.message || '手机号或密码错误，请重试';
          Message.error(loginError.value);
        } else {
          loginError.value = result.message || '用户名或密码错误，请重试';
          Message.error(loginError.value);
        }
        return;
      }
      
      // 如果选择了"记住我"，可以在localStorage中保存token
      if (formState.remember && process.client) {
        localStorage.setItem('mall_user_token', userStore.token);
      } else if (process.client) {
        sessionStorage.setItem('mall_user_token', userStore.token);
      }
      
      Message.success('登录成功');
      
      // 登录成功后跳转到会员中心首页
      await navigateTo('/mall/user/center');
      // 如果navigateTo不起作用，尝试使用window.location
      if (process.client) {
        window.location.href = '/mall/user/center';
      }
      
    } else if (activeTab.value === 'sms') {
      if (!formState.phone || !formState.smsCode) {
        Message.error('请输入手机号和验证码');
        return;
      }
      
      isLoading.value = true;
      loginError.value = '';
      
      // 调用短信登录API
      const result = await userStore.smsLogin({
        phone: formState.phone,
        smsCode: formState.smsCode,
        remember: formState.remember
      });
      
      if (!result.success) {
        loginError.value = result.message || '验证码错误或已过期，请重试';
        Message.error(loginError.value);
        return;
      }
      
      // 如果选择了"记住我"，可以在localStorage中保存token
      if (formState.remember && process.client) {
        localStorage.setItem('mall_user_token', userStore.token);
      } else if (process.client) {
        sessionStorage.setItem('mall_user_token', userStore.token);
      }
      
      Message.success('登录成功');
      
      // 登录成功后跳转到会员中心首页
      await navigateTo('/mall/user/center');
      // 如果navigateTo不起作用，尝试使用window.location
      if (process.client) {
        window.location.href = '/mall/user/center';
      }
    }
  } catch (error) {
    console.error('登录过程中出错:', error);
    loginError.value = '登录失败: ' + (error.message || '未知错误');
    Message.error(loginError.value);
  } finally {
    isLoading.value = false;
  }
};

// 点击logo返回首页
const navigateToHome = () => {
  navigateTo('/mall');
};

// 获取微信扫码登录二维码
const getQrCode = async () => {
  try {
    qrcodeLoading.value = true;
    qrcodeError.value = '';
    qrcodeScanSuccess.value = false;
    
    // 调用获取二维码接口
    const result = await mallApi.wechat.getLoginQrcode();
    
    if (result.code === 200 && result.data) {
      qrcodeUrl.value = result.data.qrcodeUrl;
      qrcodeTicket.value = result.data.ticket;
      qrcodeExpiresIn.value = result.data.expiresIn || 300; // 默认五分钟有效期
      
      // 启动二维码状态轮询
      startQrCodePolling();
      
      // 启动二维码倒计时
      startQrCodeCountdown();
    } else {
      qrcodeError.value = result.message || '获取二维码失败';
    }
  } catch (error) {
    console.error('获取微信二维码失败:', error);
    qrcodeError.value = '获取二维码失败，请刷新重试';
  } finally {
    qrcodeLoading.value = false;
  }
};

// 刷新二维码
const refreshQrCode = () => {
  // 清除定时器
  clearQrCodeTimers();
  // 重新获取二维码
  getQrCode();
};

// 启动二维码状态轮询
const startQrCodePolling = () => {
  // 清除现有定时器
  if (qrcodeTimer) {
    clearInterval(qrcodeTimer);
  }
  
  // 每3秒查询一次扫码状态
  qrcodeTimer = setInterval(async () => {
    try {
      if (!qrcodeTicket.value) return;
      
      // 调用查询扫码状态接口
      const result = await mallApi.wechat.checkLoginStatus(qrcodeTicket.value);
      
      if (result.code === 200) {
        const status = result.data.status;
        
        // 处理不同的扫码状态
        if (status === 'SCANNED') {
          // 已扫码，等待确认
          qrcodeScanSuccess.value = true;
        } else if (status === 'CONFIRMED' || status === 'SUCCESS') {
          // 已确认登录，处理登录成功
          clearQrCodeTimers();
          console.log('登录成功，准备跳转', result.data);
          
          // 如果返回了token，则保存并跳转
          if (result.data.token) {
            const token = result.data.token;
            const userInfo = result.data.userInfo;
            
            console.log('微信扫码登录成功，开始处理登录状态', { token, userInfo });
            
            try {
              // 使用 store 的微信登录方法处理登录
              const loginResult = await userStore.handleWechatLogin({
                token: token,
                userInfo: userInfo
              });
              
              console.log('微信登录处理结果:', loginResult);
              
              if (!loginResult.success) {
                console.warn('微信登录状态更新失败:', loginResult.message);
                
                // 即使状态更新失败，仍然保存token到localStorage作为备用
                if (process.client) {
                  localStorage.setItem('mall_user_token', token);
                  console.log('备用方式：Token已保存到localStorage:', token);
                }
              }
            } catch (storeError) {
              console.error('处理微信登录失败:', storeError);
              
              // 如果使用store方法失败，则直接保存token到localStorage
              if (process.client) {
                localStorage.setItem('mall_user_token', token);
                console.log('备用方式：Token已保存到localStorage:', token);
              }
            }
            
            Message.success('登录成功');
            
            // 登录成功后跳转到会员中心首页
            console.log('准备跳转到会员中心');
            try {
              await navigateTo('/mall/user/center');
              // 如果navigateTo不起作用，尝试使用window.location
              setTimeout(() => {
                if (process.client) {
                  console.log('使用window.location跳转');
                  window.location.href = '/mall/user/center';
                }
              }, 500);
            } catch (error) {
              console.error('跳转失败:', error);
              if (process.client) {
                window.location.href = '/mall/user/center';
              }
            }
          }
        } else if (status === 'EXPIRED') {
          // 二维码过期
          clearQrCodeTimers();
          qrcodeError.value = '二维码已过期，请刷新';
          qrcodeUrl.value = '';
        } else if (status === 'CANCELED') {
          // 用户取消登录
          qrcodeScanSuccess.value = false;
          qrcodeError.value = '用户取消了登录，请重新扫码';
        }
      }
    } catch (error) {
      console.error('查询扫码状态失败:', error);
    }
  }, 3000);
};

// 启动二维码倒计时
const startQrCodeCountdown = () => {
  // 清除现有定时器
  if (qrcodeCountdownTimer) {
    clearInterval(qrcodeCountdownTimer);
  }
  
  // 每秒更新倒计时
  qrcodeCountdownTimer = setInterval(() => {
    if (qrcodeExpiresIn.value > 0) {
      qrcodeExpiresIn.value--;
    } else {
      // 二维码过期
      clearQrCodeTimers();
      qrcodeError.value = '二维码已过期，请刷新';
      qrcodeUrl.value = '';
    }
  }, 1000);
};

// 清除二维码相关定时器
const clearQrCodeTimers = () => {
  if (qrcodeTimer) {
    clearInterval(qrcodeTimer);
    qrcodeTimer = null;
  }
  
  if (qrcodeCountdownTimer) {
    clearInterval(qrcodeCountdownTimer);
    qrcodeCountdownTimer = null;
  }
};

// 页面加载时获取二维码
onMounted(() => {
  getQrCode();
  
  // 添加消息监听器，用于处理微信授权成功的消息
  if (process.client) {
    console.log('添加微信登录消息监听器');
    window.addEventListener('message', handleWindowMessage);
  }
});

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
  
  clearQrCodeTimers();
  
  // 移除消息监听器
  if (process.client) {
    window.removeEventListener('message', handleWindowMessage);
  }
});

// 处理窗口消息
const handleWindowMessage = (event) => {
  console.log('收到窗口消息:', event.data);
  
  // 处理微信登录成功的消息
  if (event.data && event.data.type === 'wechat_login_success') {
    const { token, userInfo } = event.data;
    console.log('收到微信登录成功消息:', { token, userInfo });
    
    // 保存token到localStorage
    if (process.client && token) {
      localStorage.setItem('mall_user_token', token);
      console.log('从消息中保存Token到localStorage:', token);
      
      // 安全地更新用户状态
      try {
        if (userStore && typeof userStore.setToken === 'function') {
          userStore.setToken(token);
          console.log('消息处理中使用 userStore.setToken 更新令牌');
          
          if (userInfo && typeof userStore.setUserInfo === 'function') {
            userStore.setUserInfo(userInfo);
            console.log('消息处理中更新用户信息到store:', userInfo);
          }
        } else {
          console.warn('消息处理中 userStore.setToken 不是一个函数');
        }
      } catch (storeError) {
        console.error('消息处理中更新用户store失败:', storeError);
      }
    }
    
    // 显示成功消息
    Message.success('微信登录成功');
    
    // 跳转到会员中心
    navigateTo('/mall/user/center');
  }
};
</script>

<style scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.login-box {
  width: 100%;
  max-width: 760px;
  border-radius: 20px;
  overflow: hidden;
}

.tab-item {
  transition: all 0.3s;
}

.tab-item:hover {
  color: red;
}
.code-btn{
    position: absolute;
    right: 0;
    color: red;
}
</style>