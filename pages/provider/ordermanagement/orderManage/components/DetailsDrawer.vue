<template>
  <a-drawer
    v-model:visible="visible"
    :title="drawerTitle"
    :width="1300"
    :mask-closable="false"
    >
    <div class="ma-content-block lg:p-4 p-2">
   

   <!-- 订单基本信息卡片 -->
   <a-card class="mb-4 order-card" title="订单信息">
     <a-row :gutter="[16, 16]">
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">订单编号：</span>
           <span class="info-value">{{ orderDetail.orderNumber }}</span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">订单来源：</span>
           <span class="info-value">{{ orderDetail.channel?.name || orderDetail.channelName || '未知来源' }}</span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">下单时间：</span>
           <span class="info-value">{{ orderDetail.createTime }}</span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">支付时间：</span>
           <span class="info-value">{{ orderDetail.payTime }}</span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">订单状态：</span>
           <span class="info-value">
             {{ orderDetail.orderStatus }}
           </span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">支付状态：</span>
           <span class="info-value">
             {{ orderDetail.paymentStatusText }}
           </span>
         </div>
       </a-col>
       <a-col :span="24">
         <div class="info-item">
           <span class="info-label">订单备注：</span>
           <span class="info-value">{{ orderDetail.orderNote || '无' }}</span>
         </div>
       </a-col>
     </a-row>
   </a-card>

   <!-- 商品信息卡片 -->
   <a-card class="mb-4 order-card" title="商品信息">
     <a-table 
       :columns="goodsColumns" 
       :data="goodsData" 
       :pagination="false" 
       :bordered="{ cell: true }"
       class="goods-table"
     >
       <template #name="{ record }">
         <div class="flex items-start">
           <a-image :src="record.imageUrl" width="60" height="60" class="mr-3 product-image" />
           <div class="product-info">
             <div class="product-name">{{ record.name }}</div>
             <div class="product-specs">{{ record.specs }}</div>
           </div>
         </div>
       </template>
       <template #vendor="{ record }">
         <span>{{ record.vendor }}</span>
       </template>
       <template #price="{ record }">
         <span class="text-price">￥{{ record.price }}</span>
       </template>
       <template #quantity="{ record }">
         <span>{{ record.quantity }}</span>
       </template>
     </a-table>

     <!-- 价格汇总 -->
     <div class="price-summary">
       <div class="price-row">
         <span>订单金额：</span>
         <span class="price-value">￥ {{ orderDetail.totalProductAmount }}</span>
       </div>
       <div class="price-row">
         <span>运费金额：</span>
         <span class="price-value">￥ {{ orderDetail.shippingAmount }}</span>
       </div>
       <div class="price-row">
         <span>优惠金额：</span>
         <span class="price-value">￥ {{ orderDetail.discountAmount }}</span>
       </div>
       <div class="price-row">
         <span>支付金额：</span>
         <span class="price-value">￥ {{ orderDetail.payAmount }}</span>
       </div>
       <div class="price-row payment-method-row">
         <span>支付方式：</span>
         <span class="payment-method">{{ orderDetail.paymentMethodText }}</span>
       </div>
     </div>
   </a-card>

   <!-- 收货信息卡片 -->
   <a-card class="mb-4 order-card" title="收货信息">
     <a-row :gutter="[16, 16]">
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">收货人：</span>
           <span class="info-value">{{ orderDetail.receiver }}</span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">联系电话：</span>
           <span class="info-value">{{ orderDetail.contactPhone }}</span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">收货地址：</span>
           <span class="info-value">{{ orderDetail.shippingAddress }}</span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">物流公司：</span>
           <span class="info-value">{{ orderDetail.logisticsCompany || '暂无' }}</span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">快递单号：</span>
           <span class="info-value">{{ orderDetail.trackingNumber || '暂无' }}</span>
         </div>
       </a-col>
       <a-col :span="8" :xs="24" :sm="12" :md="8">
         <div class="info-item">
           <span class="info-label">物流详情：</span>
           <span class="info-value">
             <a-button v-if="orderDetail.trackingNumber" type="text" status="primary" size="small" @click="showLogisticsTracking">查看物流</a-button>
             <span v-else class="text-gray-400">暂无物流信息</span>
           </span>
         </div>
       </a-col>
     </a-row>

     <!-- 物流轨迹区域 -->
     <div v-if="showLogistics" class="logistics-tracking-section mt-4 border-t pt-4">
       <div class="logistics-tracking-header flex justify-between items-center mb-3">
         <div class="tracking-number">
           <span>物流单号：{{ orderDetail.trackingNumber }}</span>
           <span class="ml-4">物流公司：{{ orderDetail.logisticsCompany }}</span>
         </div>
         <a-button type="text" status="primary" size="small" @click="showLogistics = false">收起</a-button>
       </div>
       <div class="logistics-tracking-content">
         <LogisticsTrackingTimeline 
           :tracking-number="orderDetail.trackingNumber" 
           :company-code="logisticsCompanyCode" 
           :visible="showLogistics"
           :logistics-tracks="orderDetail.logisticsTracks || []"
           ref="logisticsTrackingRef"
         />
       </div>
     </div>
   </a-card>

   <!-- 订单日志卡片 -->
   <a-card class="order-card" title="订单日志">
     <a-timeline class="order-timeline">
       <a-timeline-item v-for="(log, index) in orderDetail.logs" :key="index" :dot-color="getLogColor(log.type)">
         <div class="timeline-content">
           <div class="timeline-title">{{ log.title }}</div>
           <div class="timeline-info">
             <span class="timeline-operator">操作人：{{ log.operator }}</span>
             <span class="timeline-time">（{{ log.time }}）</span>
           </div>
         </div>
       </a-timeline-item>
     </a-timeline>
   </a-card>
 </div>

  <template #footer>
    <a-space>
      <a-button @click="showRefundConfirm" status="danger" v-if="orderDetail.paymentStatus==3">确认退款</a-button>
      <a-button @click="visible = false">取消</a-button>
      <a-button type="primary" @click="visible = false">确定</a-button>
    </a-space>
  </template>
    </a-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted, nextTick } from 'vue';
import LogisticsTrackingTimeline from './LogisticsTrackingTimeline.vue';
import { useRoute, useRouter } from 'vue-router';
import { Message, Modal } from '@arco-design/web-vue';
import { IconArrowLeft } from '@arco-design/web-vue/es/icon';
import orderApi from '@/api/master/order';

const route = useRoute();
const router = useRouter();
const orderId = ref('');
const loading = ref(false);
const visible = ref(false);
const showLogistics = ref(false);
const logisticsTrackingRef = ref(null);
const drawerTitle = ref('订单详情');

// 订单详情数据
const orderDetail = reactive({
  id: '',
  orderNumber: '',
  orderSource: '',
  orderChannel: '',
  orderStatus: '',
  payStatus: '',
  createTime: '',
  payTime: '',
  orderNote: '',
  totalProductAmount: '0.00',
  shippingAmount: '0.00',
  discountAmount: '0.00',
  payAmount: '0.00',
  paymentMethodText: '',
  receiver: '',
  contactPhone: '',
  shippingAddress: '',
  logisticsCompany: '',
  trackingNumber: '',
  logs: [],
  channel:{name:''},
  paymentStatus:0,
});

// 商品数据
const goodsData = ref([]);

// 商品表格列定义
const goodsColumns = [
  {
    title: '商品名称',
    slotName: 'name',
    dataIndex: 'name',
  },
  {
    title: '商品规格',
    slotName: 'specs',
    dataIndex: 'specs',
  },
  {
    title: '商家',
    slotName: 'vendor',
    dataIndex: 'vendor',
  },
 
  {
    title: '单价',
    slotName: 'price',
    dataIndex: 'price',
    align: 'center'
  },
  {
    title: '购买数量',
    slotName: 'quantity',
    dataIndex: 'quantity',
    align: 'center'
  }
];

// 获取日志颜色
const getLogColor = (type) => {
  const colorMap = {
    'create': 'blue',
    'pay': 'green',
    'ship': 'orange',
    'complete': 'green',
    'cancel': 'red',
    'refund': 'red'
  };
  return colorMap[type] || 'gray';
};

// 获取订单状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    '待付款': 'orange',
    '待发货': 'blue',
    '已发货': 'purple',
    '已完成': 'green',
    '已取消': 'red',
    '已关闭': 'gray',
    '退款中': 'magenta',
    '已退款': 'red'
  };
  return statusMap[status] || '';
};

// 返回列表
const goBack = () => {
  router.push('/master/order/orderManage');
};

// 获取订单详情
const getOrderDetail = async (id) => {
  loading.value = true;
  try {
    // 调用API获取订单详情
    const res = await orderApi.getOrderDetail(id);
    if (res && res.code === 200) {
      const data = res.data;
      
      // 处理订单状态
      const orderStatusMap = {
        0: '待付款',
        1: '待发货',
        2: '已发货',
        3: '已完成',
        4: '已取消',
        5: '已关闭'
      };
      
      // 处理支付状态
      const paymentStatusMap = {
        0: '未支付',
        1: '已支付'
      };
      
      // 处理配送状态
      const shippingStatusMap = {
        0: '未发货',
        1: '已发货',
        2: '已收货'
      };
      
      // 处理订单来源
      const orderSourceMap = {
        1: '官方商城',
        2: '微信小程序',
        3: '手机应用'
      };
      
      // 处理订单类型
      const orderTypeMap = {
        1: '普通订单',
        2: '团购订单',
        3: '秒杀订单'
      };
      
      // 格式化时间
      const formatTimestamp = (timestamp) => {
        if (!timestamp) return '';
        const date = new Date(parseInt(timestamp));
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        }).replace(/\//g, '-');
      };
      
      // 更新订单详情数据
      Object.assign(orderDetail, {
        id: data.id,
        orderNumber: data.id, // 使用id替代orderSn作为订单编号
        orderSource: orderSourceMap[data.orderSource] || '未知',
        orderChannel: orderTypeMap[data.orderType] || '普通订单',
        orderStatus: data.orderStatusText || '未知状态',
        payStatus: paymentStatusMap[data.paymentStatus] || '未支付',
        createTime: formatTimestamp(data.createdAt),
        payTime: formatTimestamp(data.paidAt),
        orderNote: data.remark || '',
        totalProductAmount: data.totalProductAmount,
        shippingAmount: data.shippingFee,
        discountAmount: data.discountAmount,
        payAmount: data.totalAmount,
        paymentMethodText: data.paymentMethodText || '未知',
        receiver: data.shipping?.recipientName || '',
        contactPhone: data.shipping?.recipientPhone || '',
        shippingAddress: data.shipping ? `${data.shipping.regionPathName} ${data.shipping.streetAddress}` : '',
        logisticsCompany: data.shipping?.shippingCompanyName || '',
        trackingNumber: data.shipping?.trackingNumber || '',
        logs: data.logs || [],
        channel: data.channel || {},
        paymentStatus:data.paymentStatus,
        paymentStatusText:data.paymentStatusText
      });
      
      // 处理商品数据
      if (data.items && Array.isArray(data.items)) {
        goodsData.value = data.items.map(item => ({
          id: item.id,
          imageUrl: item.productImage || '/placeholder-image.jpg',
          name: item.productName,
          specs: item.skuSpecifications,
          price: item.unitPrice,
          quantity: item.quantity,
          subtotal: item.totalPrice,
          vendor: item.thirdPartyProductCode ? `第三方商品编码: ${item.thirdPartyProductCode}` : '自营商品'
        }));
      }
    }
    // 注意：不再显示错误消息，因为request.js中的响应拦截器已经处理了错误
  } catch (error) {
    // 仅在控制台记录错误，不显示给用户
    console.error('获取订单详情失败', error);
  } finally {
    loading.value = false;
  }
};

// 打开抽屉的方法，供外部调用
const open = async (id) => {
  if (!id) {
    Message.error('订单ID不能为空');
    return;
  }
  
  orderId.value = id;
  visible.value = true;
  await getOrderDetail(id);
};

// 显示物流轨迹信息
const showLogisticsTracking = () => {
  if (!orderDetail.trackingNumber) {
    Message.warning('暂无物流单号');
    return;
  }
  showLogistics.value = true;
  
  // 如果有物流轨迹组件引用，则调用处理方法
  nextTick(() => {
    if (logisticsTrackingRef.value) {
      // 如果订单详情中已包含物流轨迹数据，则使用processLogisticsTracks方法处理
      if (orderDetail.logisticsTracks && orderDetail.logisticsTracks.length > 0) {
        logisticsTrackingRef.value.processLogisticsTracks();
      } else {
        // 否则使用fetchTrackingInfo方法从API获取
        logisticsTrackingRef.value.fetchTrackingInfo();
      }
    }
  });
};

// 根据物流公司名称获取对应的编码
const logisticsCompanyCode = computed(() => {
  // 这里可以根据物流公司名称返回对应的编码
  // 示例：如果有物流公司映射表，可以在这里查找并返回
  const companyMap = {
    '顺丰速运': 'SF',
    '中通快递': 'ZTO',
    '圆通速递': 'YTO',
    '韵达速递': 'YD',
    '申通快递': 'STO',
    '百世快递': 'HTKY',
    '邮政快递': 'YZPY',
    '京东物流': 'JD'
  };
  
  return companyMap[orderDetail.logisticsCompany] || '';
});

const showRefundConfirm = () => {
  Modal.warning({
    title: '是否确认退款',
    content: `当前订单支付金额为：￥${orderDetail.payAmount}，确认要退款吗？`,
    okText: '确认退款',
    cancelText: '取消',
    onOk: async () => {
      // 这里添加退款逻辑
      const res  = await orderApi.confirmRefund(orderId.value);
      if (res.code === 200) {
        Message.success('退款操作成功');
        visible.value = false;
      }else{
        Message.error(res.message);
      }
    }
  });
};

// 暴露方法给父组件
defineExpose({
  open
});

</script>

<style scoped>
/* 卡片样式 */
.order-card {
  margin-bottom: 16px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.order-card :deep(.arco-card-header) {
  border-bottom: 1px solid #f2f3f5;
  padding: 12px 20px;
}

.order-card :deep(.arco-card-body) {
  padding: 16px 20px;
}

/* 信息项样式 */
.info-item {
  display: flex;
  align-items: flex-start;
  line-height: 1.6;
}

.info-label {
  color: #86909c;
  min-width: 80px;
  margin-right: 8px;
  font-size: 14px;
}

.info-value {
  color: #1d2129;
  font-size: 14px;
  word-break: break-all;
}

/* 商品表格样式 */
.goods-table {
  margin-bottom: 16px;
}

.goods-table :deep(.arco-table-th) {
  background-color: #f7f8fa;
  font-weight: 500;
  color: #1d2129;
  padding: 12px 16px;
  border-color: #e5e6eb;
}

.goods-table :deep(.arco-table-td) {
  padding: 16px;
  border-color: #e5e6eb;
}

.goods-table :deep(.arco-table-tr:hover) {
  background-color: #f7f8fa;
}

.product-image {
  border-radius: 4px;
  border: 1px solid #f2f3f5;
  background-color: #f7f8fa;
}

.product-info {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  color: #1d2129;
  line-height: 1.5;
  margin-bottom: 4px;
}

.product-specs {
  font-size: 13px;
  color: #86909c;
}

.text-price {
  color: #1d2129;
  font-size: 14px;
}

/* 价格汇总样式 */
.price-summary {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 16px;
}

.price-row {
  display: flex;
  justify-content: space-between;
  /* width: 200px; */
  margin-bottom: 8px;
  font-size: 14px;
  color: #1d2129;
}

.price-value {
  font-weight: 500;
}

.payment-method-row {
  margin-top: 8px;
}

.payment-method {
  color: #ff7d00;
}

/* 物流轨迹样式 */
.logistics-tracking-section {
  border-top: 1px dashed #e5e6eb;
  margin-top: 16px;
  padding-top: 16px;
}

.logistics-tracking-header {
  margin-bottom: 12px;
}

.tracking-number {
  font-size: 14px;
  color: #1d2129;
  font-weight: 500;
}

.logistics-tracking-content {
  padding: 0 12px;
}
</style>


