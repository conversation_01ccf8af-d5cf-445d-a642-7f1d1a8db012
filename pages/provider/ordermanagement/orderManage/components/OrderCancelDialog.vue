<template>
  <a-modal
    v-model:visible="visible"
    :title="'取消订单'"
    :mask-closable="false"
    @cancel="handleCancel"
    @before-ok="handleBeforeOk"
    width="500px"
  >
    <a-form :model="formData" ref="formRef" :label-col-props="{ span: 5 }" :wrapper-col-props="{ span: 18 }">
      <a-form-item field="cancelReason" label="取消原因">
        <a-textarea
          v-model="formData.cancelReason"
          placeholder="请输入取消原因（可选）"
          :max-length="200"
          show-word-limit
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Message } from '@arco-design/web-vue';
import orderApi from '@/api/master/order';

const visible = ref(false);
const formRef = ref(null);

// 表单数据
const formData = reactive({
  orderId: '',
  cancelReason: ''
});

// 回调函数
let refreshCallback = null;

// 打开弹窗
const open = (orderId, callback) => {
  formData.orderId = orderId;
  formData.cancelReason = '';
  refreshCallback = callback || (() => {});
  visible.value = true;
};

// 取消操作
const handleCancel = () => {
  visible.value = false;
  formRef.value?.resetFields();
};

// 确认前验证
const handleBeforeOk = (done) => {
  // 直接构建取消原因数据
  const cancelData = {
    cancelReason: formData.cancelReason || '管理员取消'
  };
  
  // 调用取消订单接口
  orderApi.cancelOrder(formData.orderId, cancelData)
    .then(res => {
      if (res && res.code === 200) {
        Message.success('订单取消成功');
        // 刷新订单列表
        if (refreshCallback) {
          refreshCallback();
        }
        done(); // 关闭弹窗
      } else {
        Message.error((res && res.message) || '订单取消失败');
        done(false); // 阻止关闭弹窗
      }
    })
    .catch(err => {
      console.error('取消订单失败', err);
      Message.error('取消订单失败: ' + (err.message || String(err)));
      done(false); // 阻止关闭弹窗
    });
};

// 暴露方法给父组件
defineExpose({
  open
});
</script>

<style scoped>
.arco-form-item {
  margin-bottom: 20px;
}
</style>
