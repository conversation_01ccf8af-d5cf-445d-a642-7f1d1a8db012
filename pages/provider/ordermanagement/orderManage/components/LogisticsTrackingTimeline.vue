<template>
  <div class="logistics-tracking-container">
    <div v-if="loading" class="loading-container">
      <a-spin />
      <span class="ml-2">加载物流信息中...</span>
    </div>
    <div v-else-if="trackingData.length === 0" class="empty-container">
      <a-empty description="暂无物流信息" />
    </div>
    <div v-else class="tracking-timeline">
      <a-timeline>
        <a-timeline-item 
          v-for="(item, index) in trackingData" 
          :key="index"
          :dot-color="index === 0 ? '#165DFF' : '#86909C'"
        >
          <div class="tracking-item">
            <div class="tracking-content">{{ item.content }}</div>
            <div class="tracking-time">{{ item.time }}</div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  // 物流单号
  trackingNumber: {
    type: String,
    default: ''
  },
  // 物流公司编码
  companyCode: {
    type: String,
    default: ''
  },
  // 是否显示物流轨迹
  visible: {
    type: Boolean,
    default: false
  },
  // 物流轨迹数据（从订单详情接口获取）
  logisticsTracks: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:visible']);

const loading = ref(false);
const trackingData = ref([]);

// 监听visible变化，当显示时获取物流信息
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 如果有传入物流轨迹数据，直接使用
    if (props.logisticsTracks && props.logisticsTracks.length > 0) {
      processLogisticsTracks();
    } else if (props.trackingNumber) {
      // 否则通过API获取
      fetchTrackingInfo();
    }
  }
});

// 处理从订单详情接口获取的物流轨迹数据
const processLogisticsTracks = () => {
  loading.value = true;
  trackingData.value = [];
  
  try {
    // 查找与当前物流单号匹配的轨迹数据
    const matchedTrack = props.logisticsTracks.find(track => 
      track.expressNo === props.trackingNumber
    );
    
    if (matchedTrack && matchedTrack.trackData && matchedTrack.trackData.length > 0) {
      // 转换为组件需要的格式
      trackingData.value = matchedTrack.trackData.map(item => ({
        content: item.context || item.desc || '',
        time: item.time || item.ftime || '',
        location: item.location || ''
      }));
      console.log('使用订单详情接口返回的物流轨迹数据:', trackingData.value);
    } else {
      console.log('未找到匹配的物流轨迹数据，尝试通过API获取');
      // 如果没有找到匹配的轨迹数据，尝试通过API获取
      if (props.trackingNumber) {
        fetchTrackingInfo();
        return;
      }
    }
  } catch (error) {
    console.error('处理物流轨迹数据失败', error);
  } finally {
    loading.value = false;
  }
};

// 通过API获取物流信息
const fetchTrackingInfo = async () => {
  if (!props.trackingNumber) {
    Message.error('物流单号不能为空');
    return;
  }

  loading.value = true;
  trackingData.value = [];

  try {
    // 导入订单API
    const orderApi = await import('@/api/master/order');

    // 调用物流查询API
    const res = await orderApi.default.queryLogisticsTrack({
      trackingNumber: props.trackingNumber,
      companyCode: props.companyCode || ''
    });

    if (res && res.code === 200 && res.data) {
      trackingData.value = res.data.map(item => ({
        content: item.context || '',
        time: item.time || '',
        location: item.location || ''
      }));
    } else {
      Message.error(res?.message || '获取物流信息失败');
    }
  } catch (error) {
    console.error('获取物流信息失败', error);
    Message.error('获取物流信息失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  fetchTrackingInfo,
  processLogisticsTracks
});
</script>

<style scoped>
.logistics-tracking-container {
  width: 100%;
  padding: 16px 0;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
}

.empty-container {
  padding: 24px 0;
}

.tracking-timeline {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 16px;
}

.tracking-item {
  padding: 4px 0;
}

.tracking-content {
  font-size: 14px;
  color: #1d2129;
  line-height: 1.5;
  margin-bottom: 4px;
}

.tracking-time {
  font-size: 12px;
  color: #86909c;
}
</style>
