<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
-->
<template>
  <a-modal
    :visible="visible"
    title="上游订单录入"
    @cancel="handleCancel"
    @before-ok="handleSubmit"
    @close="visible = false"
    :mask-closable="false"
    :esc-to-close="false"
  >
    <div class="upstream-order-form">
      <a-form :model="form" ref="formRef" layout="vertical">
        <a-form-item field="store_id" label="平台ID" :rules="[{required:true,message:'请选择平台ID'}]">
          <a-cascader :field-names="fieldNames" v-model="form.store_id" :options="options" placeholder="请选择平台ID" allow-clear />
        </a-form-item>
        <a-form-item field="order_no" label="上游订单编号" :rules="[{required:true,message:'请输入上游订单编号'}]">
          <a-input v-model="form.order_no" placeholder="请输入上游订单编号" />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>
  
<script setup>
import { ref, reactive, onMounted, watch } from "vue";
import { Message } from "@arco-design/web-vue";
import orderApi from "@/api/master/order";
// 控制抽屉显示
const visible = ref(false);
const formRef = ref(null);

// 表单数据
const form = reactive({
  spider_type: 'order',
  platform_id: '',
});
const fieldNames = {value: 'id', label: 'name'}

const emit = defineEmits(["success", "cancel"]);

// 打开抽屉
const open = () => {
  getAllPlatform()
  visible.value = true;
};

// 获取平台数据
const options = ref([])
const getAllPlatform =()=>{
  orderApi.getAllPlatformManagement().then(res=>{
    options.value = res.data
  })
}

// 取消
const handleCancel = () => {
  visible.value = false;
  resetForm();
  emit("cancel");
};

// 根据store_id查找对应的商店对象及其parentId
const findStoreAndPlatform = (storeId) => {
  // 递归查找商店对象
  const findStore = (items) => {
    for (const item of items) {
      // 如果当前项是商店且ID匹配
      if (item.type === 'store' && item.id === storeId) {
        return item;
      }
      // 如果有子项，递归查找
      if (item.children && item.children.length > 0) {
        const found = findStore(item.children);
        if (found) return found;
      }
    }
    return null;
  };
  
  return findStore(options.value);
};

// 提交
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate();
    if (!valid) {
      // 根据选择的store_id找到对应的商店对象
      const storeObj = findStoreAndPlatform(form.store_id);
      
      if (storeObj && storeObj.parentId) {
        // 设置platform_id为商店对象的parentId
        form.platform_id = storeObj.parentId;
      } else {
        Message.warning("未找到对应的平台信息");
        return false;
      }
      
      let data = { ...form };
      console.log("🚀 ~ handleSubmit ~ data:", data);
      
      orderApi.callSpider(data).then(res => {
        if (res.code === 200) {
          Message.success("提交成功");
          emit("success");
          visible.value = false; // 提交成功后关闭抽屉
          return true;
        }else{
          Message.error(res.message);
          return false;
        }
      });
      return true;
    }
  } catch (err) {
    console.error("表单验证失败", err);
    return false;
  }
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(form, {
    order_no: '',
    store_id: '',
    spider_type: 'order',
    platform_id: '',
  });
};

// 暴露方法
defineExpose({
  open
});
</script>
  
<style scoped>
.upstream-order-form {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-footer {
  margin-top: auto;
  text-align: right;
  padding-top: 16px;
}
</style>