<template>
  <a-modal
    v-model:visible="visible"
    title="订单发货"
    :width="900"
    :footer="false"
    @cancel="handleCancel"
    class="order-delivery-dialog"
  >
    <!-- 收货信息 -->
    <a-card class="mb-4" title="收货信息" size="small">
      <a-row>
        <a-col :span="6">收货人：{{ deliveryInfoRef.receiver }}</a-col>
        <a-col :span="6">联系电话：{{ deliveryInfoRef.phone }}</a-col>
        <a-col :span="12">收货地址：{{ deliveryInfoRef.address }}</a-col>
      </a-row>
    </a-card>
    <!-- 商品信息 -->
    <a-card class="mb-4" title="商品信息" size="small">
      <a-table :columns="goodsColumns" :data="goodsDataRef" :pagination="false" size="small" bordered >
        <template #image="{ record }">
          <img :src="record.imageUrl || '/placeholder.png'" alt="商品图片" style="width:48px;height:48px;object-fit:cover;border-radius:6px;" />
        </template>
      </a-table>
    </a-card>
    <!-- 发货表单 -->
    <a-card title="发货物流" size="small">
      <ma-form
        ref="maFormRef"
        v-model="formData"
        :columns="formColumns"
        :options="{ 
          showButtons: false, 
          labelWidth: 120,
          rules: formRules
        }"
        layout="horizontal"
      />
    </a-card>
    <!-- 底部按钮 -->
    <div class="flex justify-end mt-6 gap-2">
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk">确定</a-button>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, nextTick, computed, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import commonApi from '@/api/common';

// 弹窗显示控制
const visible = ref(false);
const maFormRef = ref();

// 供外部调用，打开弹窗并传递数据
function open({ orderId = '', deliveryInfo = {}, goodsData = [], onConfirm = null } = {}) {
  // 设置订单ID和回调函数
  orderIdRef.value = orderId;
  onConfirmRef.value = onConfirm;
  
  // 处理商品数据
  // 兼容单商品与多商品
  let goodsList = Array.isArray(goodsData) ? goodsData : [goodsData];
  // 适配字段，保证每个商品有图片
  goodsList = goodsList.map(item => ({
    name: item.name || item.productName || '',
    vendor: item.vendor || item.seller || '',
    price: item.price || item.unitPrice || '',
    quantity: item.quantity || item.count || 1,
    imageUrl: item.imageUrl || item.img || '/placeholder.png'
  }));
  
  // 设置收货信息和商品数据
  deliveryInfoRef.value = { ...deliveryInfo };
  goodsDataRef.value = goodsList;
  
  // 重置表单数据
  formData.shippingMethod = ''; // 默认不选中任何配送方式
  formData.shippingCompanyCode = '';
  formData.shippingCompanyName = '';
  formData.trackingNumber = '';
  formData.imagesUrl = [];
  
  // 使用静态数据，不需要加载
  // fetchLogisticsCompanies();
  
  // 显示弹窗
  visible.value = true;
  
  // 等待DOM更新后重置表单
  nextTick(() => {
    if (maFormRef.value) {
      maFormRef.value.resetForm();
    }
  });
}

function close() {
  visible.value = false;
}

// 对外暴露方法
defineExpose({ open, close });

// 订单ID
const orderIdRef = ref('');

// 回调函数
const onConfirmRef = ref(null);

// 收货信息（只读）
const deliveryInfoRef = ref({
  receiver: '',
  phone: '',
  address: ''
});
const deliveryInfo = deliveryInfoRef.value;

// 商品信息（只读）
const goodsDataRef = ref([]);
const goodsData = goodsDataRef.value;
const goodsColumns = [
  {
    title: '图片',
    dataIndex: 'imageUrl',
    slotName: 'image'
  },
  { title: '商品名称', dataIndex: 'name' },
  { title: '商家', dataIndex: 'vendor' },
  { title: '单价', dataIndex: 'price' },
  { title: '购买数量', dataIndex: 'quantity' }
];

// 发货表单数据
const formData = reactive({
  // 配送方式类型映射
  shippingMethod: '', // 1-快递物流，2-自定义物流，3-商家自送，4-线下自取，5-无需物流
  shippingCompanyCode: '', // 物流公司编码
  shippingCompanyName: '', // 物流公司名称
  trackingNumber: '', // 物流单号
  imagesUrl: [] // 附件图片URL地址
});

// 自定义表单验证规则
const formRules = {
  shippingMethod: [{ required: true, message: '请选择配送方式' }],
  shippingCompanyCode: [{ required: true, message: '请选择物流公司' }],
  shippingCompanyName: [{ required: true, message: '请输入公司名称' }],
  trackingNumber: [{ required: true, message: '请输入物流单号' }]
};

// 配送方式数据
const shippingMethodData = [
  { id: '1', name: '快递物流' },
  { id: '2', name: '自定义物流' },
  { id: '3', name: '商家自送' },
  { id: '4', name: '线下自取' },
  { id: '5', name: '无需物流' }
];

// 物流公司数据
const logisticsCompanies = [
  { id: 'SF', name: '顺丰快递' },
  { id: 'ZTO', name: '中通快递' },
  { id: 'YTO', name: '圆通快递' },
  { id: 'STO', name: '申通快递' },
  { id: 'YUNDA', name: '韵达快递' },
  { id: 'JD', name: '京东物流' },
  { id: 'EMS', name: 'EMS' },
  { id: 'ZJS', name: '宅急送' },
  { id: 'HTKY', name: '汇通快递' },
  { id: 'BEST', name: '百世快递' },
  { id: 'OTHER', name: '其他快递' }
];

// 获取物流公司数据
// 暂时使用静态数据，后续可以开启API调用
const fetchLogisticsCompanies = async () => {
  // 当需要从后端获取数据时，可以开启下面的代码
  try {
    const res = await commonApi.getExpressCompanyCode();
    if (res && res.data) {
      // 将API返回的数据转换为组件需要的格式
      const companyList = res.data.items.map(item => ({
        id: item.companyCode,
        name: item.companyName
      }));
      logisticsCompanies.length = 0;
      logisticsCompanies.push(...companyList);
    }
  } catch (error) {
    console.error('获取物流公司数据失败:', error);
  }
  
};

// 组件挂载时获取物流公司数据
// 暂时注释掉
onMounted(() => {
   fetchLogisticsCompanies();
});

// 表单字段定义
const formColumns = computed(() => {
  const baseColumns = [
    {
      title: '配送方式',
      dataIndex: 'shippingMethod',
      formType: 'select',
      dict: { data: shippingMethodData, props: { label: 'name', value: 'id' } },
      placeholder: '请选择配送方式',
      required: true
    }
  ];

  // 快递物流时显示物流公司选择
  if (formData.shippingMethod === 1 || formData.shippingMethod === '1') {
    baseColumns.push(
      {
        title: '物流公司',
        dataIndex: 'shippingCompanyCode',
        formType: 'select',
        dict: { data: logisticsCompanies, props: { label: 'name', value: 'id' } },
        placeholder: '请选择物流公司',
        required: true,
        onChange: (value) => {
          const company = logisticsCompanies.find(item => item.id === value);
          if (company) {
            formData.shippingCompanyName = company.name;
            console.log('选择物流公司:', company.name, company.code);
          }
        }
      }
    );
  }

  // 自定义物流时显示公司名称输入
  if (formData.shippingMethod === 2 || formData.shippingMethod === '2') {
    baseColumns.push(
      {
        title: '公司名称',
        dataIndex: 'shippingCompanyName',
        formType: 'input',
        placeholder: '请输入物流公司名称',
        required: true
      }
    );
  }

  // 快递物流或自定义物流时显示物流单号输入
  if (formData.shippingMethod === 1 || formData.shippingMethod === 2 || 
      formData.shippingMethod === '1' || formData.shippingMethod === '2') {
    baseColumns.push(
      {
        title: '物流单号',
        dataIndex: 'trackingNumber',
        formType: 'input',
        placeholder: '请输入物流单号',
        required: true
      }
    );
  }

  // 所有配送方式都可以上传附件
  baseColumns.push({
    title: '附件',
    dataIndex: 'imagesUrl',
    formType: 'upload',
    // 限制文件类型
    accept: '.jpg,.jpeg,.png,.pdf,.doc,.docx',
    // 文件类型描述
    fileTypeDesc: '图片、PDF和文档',
    // 限制文件大小为5MB
    size: 5 * 1024 * 1024,
    // 允许多选
    multiple: true,
    // 限制上传数量为5个
    limit: 5,
    // 返回类型为URL
    returnType: 'url',
    // 显示已上传文件列表
    showList: true,
    // 提示信息
    tip: '支持图片、PDF和文档格式，最多上传5个文件，单个文件不超过5MB',
    required: false
  });

  return baseColumns;
});



// 确认提交
async function handleOk() {
  try {  
    const valid = await maFormRef.value.validateForm();
    
    // 如果有验证错误，则返回
    if (valid) {
      return;
    }
    
    // 构建发货数据 - 符合API参数要求
    const logistics = {
      shippingMethod: formData.shippingMethod,
      imagesUrl: formData.imagesUrl ? formData.imagesUrl.join(',') : ''
    };
    
    // 根据配送方式添加不同的字段
    if (formData.shippingMethod === 1 || formData.shippingMethod === 2 || 
        formData.shippingMethod === '1' || formData.shippingMethod === '2') { // 快递物流或自定义物流
      logistics.shippingCompanyCode = formData.shippingCompanyCode || '';
      logistics.shippingCompanyName = formData.shippingCompanyName || '';
      logistics.trackingNumber = formData.trackingNumber || '';
    }
    
    // 关闭弹窗
    visible.value = false;
    
    // 如果有回调函数，调用回调函数
    if (typeof onConfirmRef.value === 'function') {
      onConfirmRef.value({
        orderId: orderIdRef.value,
        logistics
      });
    }
  } catch (error) {
    console.error('表单提交错误:', error);
    Message.error('请填写必填信息');
  }
}

function handleCancel() {
  visible.value = false;
}
</script>

<style scoped>
.order-delivery-dialog .arco-modal-body {
  padding-bottom: 0;
}
.mb-4 { margin-bottom: 16px; }
.flex { display: flex; }
.justify-end { justify-content: flex-end; }
.mt-6 { margin-top: 24px; }
.gap-2 { gap: 8px; }
</style>
