<template>
  <a-drawer
    v-model:visible="visible"
    :title="drawerTitle"
    :width="1300"
    :mask-closable="false"
  >
    <a-form :model="formData" ref="formRef" layout="vertical">
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="客户姓名"
            field="customerName"
            :rules="[{ required: true, message: '请输入客户姓名' }]"
          >
            <a-input
              v-model="formData.customerName"
              placeholder="请输入客户姓名"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="客户电话"
            field="customerPhone"
            :rules="[
              { required: true, message: '请输入客户电话' },
              { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式' },
            ]"
          >
            <a-input
              v-model="formData.customerPhone"
              placeholder="请输入客户电话"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="渠道ID"
            field="channelId"
            :rules="[{ required: true, message: '请输入渠道ID' }]"
          >
            <a-select
              v-model="formData.channelId"
              :options="options"
              :style="{ width: '100%' }"
              :loading="loading"
              placeholder="请选择渠道"
              @change="handleChannelChange"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="支付方式"
            field="paymentMethod"
            :rules="[{ required: true, message: '请输入支付方式' }]"
          >
            <a-select
              v-model="formData.paymentMethod"
              :options="paymentMethodOptions"
              :style="{ width: '100%' }"
              placeholder="请选择支付方式"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16" >
        <a-col :span="24" v-if="showThirdPartyOrderSn">
          <a-form-item
            label="第三方订单号"
            field="thirdPartyOrderSn"
            :rules="[{ required: showThirdPartyOrderSn, message: '请输入第三方订单号' }]"
          >
            <a-input
              v-model="formData.thirdPartyOrderSn"
              placeholder="请输入第三方订单号"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" field="remark">
            <a-textarea
              v-model="formData.remark"
              placeholder="请输入备注信息"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="省市区"
            field="area"
            :rules="[{ required: true, message: '请选择省市区' }]"
          >
            <a-cascader
              path-mode
              v-model="formData.area"
              :options="areaOptions"
              :style="{ width: '100%' }"
              placeholder="请选择省市区"
              allow-search
              :field-names="{ value: 'code', label: 'name' }"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="详细地址"
            field="streetAddress"
            :rules="[{ required: true, message: '请输入详细地址' }]"
          >
            <a-input
              v-model="formData.streetAddress"
              placeholder="请输入详细地址"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item
            label="订单总金额"
            field="totalAmount"
            :rules="[{ required: true, message: '请输入订单总金额' }, {type:'number', min: 0.01, message: '订单总金额不能小于0.01' }]"
          >
            <a-input-number
              v-model="formData.totalAmount"
              placeholder="请输入订单总金额"
              :style="{ width: '100%' }"
              disabled
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item
            label="优惠总金额"
          >
            <a-input-number
              v-model="formData.discountAmount"
              placeholder="请输入优惠总金额"
              :style="{ width: '100%' }"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-divider orientation="left">商品信息</a-divider>
      <!-- 商品信息表格，强制为每行指定唯一key，确保响应式刷新 -->
      <a-table
        :key="tableKey"
        :data="formData.items"
        :columns="columns"
        :pagination="false"
        row-key="id"
      >
        <!-- 商品名称插槽 -->
        <template #productName="{ record, rowIndex }">
          <a-form-item
            :field="`items[${rowIndex}].productName`"
            :rules="[{ required: true, message: '请输入商品名称' }]"
            hide-label
            validate-trigger="blur"
          >
            <a-input
              v-model="record.productName"
              placeholder="商品名称"
              allow-clear
              disabled
            />
          </a-form-item>
        </template>
        <!-- 数量插槽 -->
        <template #quantity="{ record, rowIndex }">
          <a-form-item
            :field="`items[${rowIndex}].quantity`"
            :rules="[{ required: true, message: '请输入数量' }]"
            hide-label
            validate-trigger="blur"
          >
            <a-input-number
              v-model="record.quantity"
              placeholder="数量"
              :min="1"
              :max="9999"
              :precision="0"
              :step="1"
              @change="calculateTotalAmount()"
            />
          </a-form-item>
        </template>
        <!-- SKU编码插槽 -->
        <template #skuId="{ record, rowIndex }">
          <a-form-item
            :field="`items[${rowIndex}].skuId`"
            :rules="[{ required: true, message: '请输入skuId' }]"
            hide-label
            validate-trigger="blur"
          >
            <a-input
              v-model="record.skuId"
              placeholder="请输入skuId"
              allow-clear
              @change="get_goods_sku_batch(rowIndex)"
            />
          </a-form-item>
        </template>

        <template #price="{ record, rowIndex}">
          <a-form-item
            hide-label
            validate-trigger="blur"
            :field="`items[${rowIndex}].price`"
            :rules="[{ required: true, message: '请输入价格' }, {type:'number', min: 0.01, message: '价格不能小于0.01' }]"
          >
            <a-input-number
              v-model="record.price"
              placeholder="价格"
              @change="calculateTotalAmount()"
              :min="0.01"
            />
          </a-form-item>
        </template>
        <!-- 操作插槽 -->
        <template #operation="{ rowIndex }">
          <a-button type="text" status="danger" @click="removeItem(rowIndex)">
            删除
          </a-button>
        </template>
      </a-table>
      <a-button type="outline" long @click="addItem" style="margin-top: 16px">
        <template #icon><icon-plus /></template>
        添加商品
      </a-button>
    </a-form>
    <div style="margin-top: 16px;text-align: right">
      <div>总价：{{ formData.totalAmount }}</div>
      <div>优惠金额：{{ formData.discountAmount }}</div>
      <div>实付价格：{{ calculateActualAmount() }}</div>
    </div>

    <template #footer>
      <a-button type="primary" @click="handleSubmit">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, reactive } from "vue";
import { Message } from "@arco-design/web-vue";
import Decimal from "decimal.js";
import orderApi from "@/api/master/order";
import goodsApi from "@/api/master/goods";
import common from "@/api/common";
const emit = defineEmits(["submit"]);
const formRef = ref();
const visible = ref(false);
const drawerTitle = ref("新增订单");
// 表格相关参数
const tableKey = ref(0); // 表格强制刷新的key

// 表格列定义
const columns = [
  {
    title: "商品名称",
    dataIndex: "productName",
    slotName: "productName",
  },
  {
    title: "skuId",
    dataIndex: "skuId",
    slotName: "skuId",
  },
  {
    title: "数量",
    dataIndex: "quantity",
    slotName: "quantity",
    width: 160,
  },
  {
    title: "价格",
    dataIndex: "price",
    slotName: "price",
    width: 200,
  },
  {
    title: "操作",
    slotName: "operation",
    width: 60,
  },
];

// 渠道数据
const options = ref([]);
const loading = ref(false);
// 控制第三方订单号表单项的显示
const showThirdPartyOrderSn = ref(false);

// 获取渠道数据
const getChannelList = async () => {
  loading.value = true;
  try {
    const res = await orderApi.getChannelList();
    options.value = res.data.items.map((item) => ({
      label: item.name,
      value: item.id,
      isBuiltIn: item.isBuiltIn, // 保存isBuiltIn属性
    }));
  } catch (error) {
    Message.error(error.message);
  } finally {
    loading.value = false;
  }
};



// 创建新商品项的函数
const createNewItem = () => {
  return {
    id: Date.now(), // 唯一标识符，用于表格的row-key
    productName: "", // 商品名称
    quantity: 1, // 数量，默认1
    skuId: "", // skuId
    price: 0, // 价格
  };
};

// 单独维护商品数据数组以确保响应式
const items = ref([createNewItem()]);

// 订单表单数据，使用reactive保证响应式
const formData = reactive({
  customerName: "",
  customerPhone: "",
  channelId: "",
  thirdPartyOrderSn: "",
  remark: "",
  totalAmount: 0,
  discountAmount: 0,

  area: [],
  streetAddress: "",
  items: items.value,
});

const open = (data) => {
  if (data) {
    Object.assign(formData, data);
    drawerTitle.value = "编辑订单";
  } else {
    resetForm();
    drawerTitle.value = "新增订单";
  }
  getChannelList()
  visible.value = true;
};

const resetForm = () => {
  // 创建包含一个新商品项的数组
  const initialItem = createNewItem();

  // 重置表单数据
  Object.assign(formData, {
    customerName: "",
    customerPhone: "",
    channelId: "",
    thirdPartyOrderSn: "",
    remark: "",
    totalAmount: 0,
    discountAmount: 0,
    area: [],
    streetAddress: "",
    items: [initialItem],
  });

  // 强制表格刷新
  tableKey.value += 1;
};

// 新增商品空行，点击按钮后表格自动刷新
const addItem = () => {
  // 添加一个新的空商品行
  const newItem = createNewItem();
  formData.items.push(newItem);

  // 强制刷新表格
  tableKey.value += 1;
};

const removeItem = (index) => {
  // 删除指定索引的商品
  formData.items.splice(index, 1);
  calculateTotalAmount(); 
  // 强制表格刷新
  tableKey.value += 1;
};

const handleCancel = () => {
  formRef.value.resetFields();
  visible.value = false;
};

const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate();
    if (valid) {
      let message = "";
      for (let name in valid) message += valid[name].message + "、";
      Message.error(message.substring(0, message.length - 1));
      return true;
    }
    let data = { ...formData };
    console.log("🚀 ~ file: OrderEntryDrawer.vue:442 ~ data:", data)
    data.items = data.items.map((item) => {
      delete item.id;
      // delete item.price;
      return item;
    });
    data.shippingInfo = {
      recipientName: data.recipientName,
      recipientPhone: data.recipientPhone,
      regionProvinceId: data.area[0],
      regionCityId: data.area[1],
      regionDistrictId: data.area[2],
      streetAddress: data.streetAddress,
    };
    delete data.recipientName;
    delete data.recipientPhone;
    delete data.area;
    delete data.streetAddress;


    const res = await orderApi.addOrderManual(data);
    if (res.code === 200) {
      Message.success("提交成功");
      emit("success");
      visible.value = false; // 提交成功后关闭抽屉
      return true;
    } else {
      Message.error(res.message);
      return false;
    }
  } catch (err) {
    console.error("表单提交出错:", err);
    Message.error("提交失败，请重试");
    return false;
  }
};

let areaOptions = ref([]);
let paymentMethodOptions = ref([]);
const getAreaOptions = async () => {
  try {
    const res = await common.getTreeRegion();
    if (res && res.code === 200 && res.data) {
      areaOptions.value = [];
      res.data.forEach((item) => {
        const provinceItem = {
          name: item.name,
          code: item.code.toString(),
        };

        // 处理市级数据
        if (item.children && item.children.length > 0) {
          provinceItem.children = [];
          item.children.forEach((cityItem) => {
            const city = {
              name: cityItem.name,
              code: cityItem.code.toString(),
            };

            // 处理区县级数据
            if (cityItem.children && cityItem.children.length > 0) {
              city.children = [];
              cityItem.children.forEach((areaItem) => {
                city.children.push({
                  name: areaItem.name,
                  code: areaItem.code.toString(),
                });
              });
            }

            provinceItem.children.push(city);
          });
        }

        areaOptions.value.push(provinceItem);
      });
    }
  } catch (error) {
    console.error("获取区域数据失败:", error);
  }
};
const handleChannelChange = (value) => {
  // 查找选中的渠道对象
  const selectedChannel = options.value.find(option => option.value === value);
  
  // 如果找到渠道，并且isBuiltIn为0，则显示第三方订单号表单项，否则隐藏
  if (selectedChannel && selectedChannel.isBuiltIn === 0) {
    showThirdPartyOrderSn.value = true;
  } else {
    showThirdPartyOrderSn.value = false;
    // 清空第三方订单号的值
    formData.thirdPartyOrderSn = "";
  }
}
// 原始的获取SKU信息方法
const _get_goods_sku_batch = async () => {
  try {
    // 过滤掉空的skuId值（空字符串、null、undefined）
    const validSkuIds = formData.items
      .map(item => item.skuId)
      .filter(skuId => skuId && skuId.trim() !== '');
    
    // 如果没有有效的skuId，则直接返回
    if (validSkuIds.length === 0) {
      return;
    }
    
    const res = await goodsApi.goods_sku_batch({ skuIds: validSkuIds });
    if (res.code === 200) {
      formData.items.forEach(item => {
        const sku = res.data.find(sku => sku.skuId === item.skuId);
        if (sku) {
          // item.price = sku.salesPrice;
          item.productName = sku.spuName + " " + sku.skuName;
        }
      });
      calculateTotalAmount();
    }
  } catch (error) {
    console.error("获取SKU信息失败:", error);
  }
}

// 防抖处理变量
let debounceTimer = null;

// 添加防抖功能的方法，频繁触发时只调用最后一次
const get_goods_sku_batch = (currentIndex) => {
  // 如果已经有定时器，则清除之前的定时器
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  
  // 设置新的定时器，300毫秒后执行实际的方法
  debounceTimer = setTimeout(() => {
    // 验证是否存在相同的 skuId
    const currentItem = formData.items[currentIndex];
    if (!currentItem || !currentItem.skuId) {
      debounceTimer = null;
      return;
    }
    
    // 检查是否有其他行使用了相同的 skuId
    const duplicateIndex = formData.items.findIndex(
      (item, index) => index !== currentIndex && 
                      item.skuId && 
                      item.skuId === currentItem.skuId
    );
    
    if (duplicateIndex !== -1) {
      // 存在重复的 skuId，清空当前输入
      currentItem.skuId = '';
      Message.warning('列表中已存在相同的商品 SKU ID，请输入其他 SKU ID');
    } else {
      // 不存在重复，调用获取 SKU 信息的方法
      _get_goods_sku_batch();
    }
    
    debounceTimer = null;
  }, 300);
}

// 计算总金额
const calculateTotalAmount = () => {
  // 使用 Decimal 处理精度丢失问题
  formData.totalAmount = formData.items.reduce((total, item) => {
    const itemPrice = new Decimal(item.price || 0);
    const itemQuantity = new Decimal(item.quantity || 0);
    const itemTotal = itemPrice.times(itemQuantity);
    return new Decimal(total).plus(itemTotal);
  }, new Decimal(0)).toNumber();
}

// 计算实付价格，使用 Decimal 处理精度问题
const calculateActualAmount = () => {
  const totalAmount = new Decimal(formData.totalAmount || 0);
  const discountAmount = new Decimal(formData.discountAmount || 0);
  return totalAmount.minus(discountAmount).toNumber();
}
// 获取支付方式列表
const getPaymentMethodOptions = async () => {
  try {
    const res = await orderApi.getPaymentMethod();
    if (res && res.data) {
      paymentMethodOptions.value = res.data.map(item => ({
        label: item.name,
        value: item.id,
      }));
    }
  } catch (error) {
    console.error('获取支付方式列表失败:', error);
    Message.error('获取支付方式列表失败');
  }
};

onMounted(() => {
  getAreaOptions();
  getPaymentMethodOptions();
});
defineExpose({
  open,
});
</script>
