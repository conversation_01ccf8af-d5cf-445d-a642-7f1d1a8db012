<template>
  <div>
    <!-- 订单取消 -->
    <OrderCancelDialog ref="cancelDialogRef" />
    <!-- 订单关闭 -->
    <OrderCloseDialog ref="closeDialogRef" @success="getOrders" />
    <!-- 订单退款 -->
    <OrderRefundDialog ref="refundDialogRef" @success="getOrders" />
    <!-- 订单标注 -->
    <OrderBadgeDialog ref="badgeDialogRef" />
    <!-- 分配跟进人员 -->
    <AssignFollowDialog ref="assignFollowDialogRef" @success="getOrders" />
    <!-- 订单录入 -->
    <OrderEntryDrawer ref="orderEntryDrawerRef" @success="getOrders" />
    <!-- 上游订单录入 -->
    <UpstreamOrderEntryDrawer ref="upstreamOrderEntryDrawerRef" @success="getOrders" />
    <!-- 订单详情 -->
    <DetailsDrawer ref="detailsDrawerRef" />
    <div class="order-list-container">
      <!-- 顶部标签页 -->
      <div class="order-tabs">
        <div 
          class="tab-item" 
          :class="{ active: timeRange === 'recent6m' }" 
          @click="switchTimeRange('recent6m')"
        >近6个月订单</div>
        <div 
          class="tab-item" 
          :class="{ active: timeRange === 'before6m' }" 
          @click="switchTimeRange('before6m')"
        >6个月前订单</div>
      </div>
      <div class="search-area">
        <!-- 顶部统计卡片 -->
        <div class="stats-cards-container">
          <div class="stats-scroll-btn stats-scroll-left" @click="scrollStatsCards('left')">
            <icon-left />
          </div>
          <div class="stats-cards" ref="statsCardsRef">
            <div
              v-for="card in statsCards"
              :key="card.id"
              class="stats-card"
              :class="{ active: activeStatsCard === card.id }"
              @click="setActiveStatsCard(card.id)"
            >
              <div class="stats-corner-mark" v-if="activeStatsCard === card.id">
                <icon-check class="check-icon" />
              </div>
              <div class="stats-title">{{ card.title }}</div>
              <div class="stats-value">{{ card.value }}</div>
            </div>
          </div>
          <div class="stats-scroll-btn stats-scroll-right" @click="scrollStatsCards('right')">
            <icon-right />
          </div>
        </div>
        <!-- 搜索表单 -->
        <SearchForm
          :formData="searchForm"
          :formItems="formItems"
          :showAdvancedSearch="showAdvancedSearch"
          @update:formData="updateSearchForm"
          @search="handleSearch"
          @reset="resetSearch"
        >
          <!-- 自定义左侧按钮 -->
          <template #left-actions>
            <!-- <a-button type="primary" status="success">导出订单</a-button> -->
            <!-- <a-dropdown>
            <a-button v-auth="['system:user:edit']">
              更多筛选
              <icon-down />
            </a-button>
            <template #content>
              <a-doption>选项1</a-doption>
              <a-doption>选项2</a-doption>
            </template>
            </a-dropdown>-->
            <a-button type="primary" status="success" @click="showOrderEntryDrawer">订单录入</a-button>

            <a-button type="primary" status="success" @click="showUpstreamOrderEntryDrawer">上游订单录入</a-button>
          </template>

          <!-- 自定义右侧内容 -->
          <!-- <template #right-actions>
          <a-checkbox v-model="searchForm.onlyShowSelected">仅显示已选订单</a-checkbox>
          </template>-->
        </SearchForm>
      </div>

      <a-table
        :columns="columns"
        :data="tableData"
        :pagination="false"
        :loading="tableLoading"
        :span-method="spanMethod"
        row-key="rowId"
        class="order-table"
        :hoverable="false"
      >
        <template #productInfo-cell="{ record }">
          <div v-if="record.type === 'header'" class="order-header-cell">
            <div class="order-header-left">
              <!-- Header Checkbox for Select/Deselect All on Page -->
              <a-checkbox
                :model-value="isAllSelectedOnPage"
                :indeterminate="isIndeterminate"
                @change="handleSelectAllOnPage"
                class="header-checkbox order-checkbox"
              />
              
              <span>系统订单编号： {{ record.id }}</span>
              <span v-if="record.thirdPartyOrderSn">三方订单编号： {{ record.thirdPartyOrderSn }}</span>
              <!-- <a-button
                shape="circle"
                size="mini"
                class="copy-btn-inline"
                @click="copyOrderId(record.originalId)"
              >
                <icon-copy />
              </a-button> -->
              <span class="order-time-inline">下单时间： {{ record.createdAt }}</span>

              <span v-if="record.channelName || record.channelIconUrl">
                订单来源：{{ record.channelName }}
              <i  class="iconfont" :class="record.channelIconUrl"></i>
              </span>
              
              <span v-if="record.platforName">
                平台名称：{{ record.platforName }}
              </span>
              
              <span v-if="record.storeName">
                店铺名称：{{ record.storeName }}
              </span>

              <span v-if="record.remark" class="order-remark">备注：{{ record.remark }}</span>
            </div>
            <div class="order-header-right">
              <a-button type="text" size="small" class="add-btn" @click="showBadgeDialog(record.originalId)">
                <icon-plus />添加标注
              </a-button>
            </div>
          </div>
          <div v-else class="product-info-cell-content">
            <!-- <img :src="record.imageUrl" class="product-image" /> -->
            <a-image width="80" height="60" :src="record.imageUrl" class="product-image" />
            <div class="product-details">
              <span class="product-name">{{ record.productName }}</span>
              <span style="color: #999;">
                {{ record.color
                }}
              </span>
              <div class="product-meta">
                <div>系统SPU：{{ record.goodsSpuId }}</div>
                <div>系统SKU：{{ record.goodsSkuId }}</div>
                <div v-if="record.thirdPartySpuId">三方SPU：{{ record.thirdPartySpuId }}</div>
                <div v-if="record.thirdPartySkuId">三方SKU：{{ record.thirdPartySkuId }}</div>
                <!-- <div class="product-tags">
                <span class="risk-tag">除名加赠</span>
                <span class="days-tag">7天</span>
                <span class="speed-tag">极速退</span>
              </div>
              <div class="store-tags">
                <span class="store-tag">小店自营</span>
                <span class="goods-tag">商品卡</span>
                </div>-->
              </div>
            </div>
          </div>
        </template>

        <template #priceQuantity-cell="{ record }">
          <div v-if="record.type === 'details'" class="price-cell-content">
            <div class="price">¥{{ record.price }}</div>
            <div class="quantity">x{{ record.quantity }}</div>
          </div>
        </template>

        <template #payment-method-cell="{ record }">
          <div v-if="record.type === 'details'" class="status-tag">{{ record.paymentMethodText }}</div>
        </template>

        <template #delivery-method-cell="{ record }">
          <div
            v-if="record.type === 'details'"
            class="status-tag"
            style="display: block; text-align: center; color: #1D2129; font-weight: 500;"
          >{{ record.shippingMethodText || '无配送信息' }}</div>
        </template>

        <template #shipping-status-cell="{ record }">
          <div
            v-if="record.type === 'details'"
            class="status-tag"
            style="display: block; text-align: center; color: #1D2129; font-weight: 500;"
          >{{ record.shippingStatusText || '未知状态' }}</div>
        </template>

        <template #receiver-cell="{ record }">
          <div
            v-if="record.type === 'details'"
            class="payment-cell-content"
            style="text-align: center;"
          >
            <div class="address" :title="record.recipientName">{{ record.recipientName }}</div>
            <div class="address" :title="record.recipientPhone">{{ record.recipientPhone }}</div>
            <div class="address" :title="record.address">{{ record.address }}</div>
          </div>
        </template>

        <template #payment-cell="{ record }">
          <div v-if="record.type === 'details'" class="receiver-info"></div>
          <div class="price highlight" v-if="record.type === 'details'">¥{{ record.totalAmount }}</div>
        </template>

        <template #consumer-cell="{ record }">
          <div v-if="record.type === 'details'" class="consumer-info">{{ record.userId }}</div>
        </template>

        <template #status-cell="{ record }">
          <div v-if="record.type === 'details'" class="status-cell-content">
            <div class="status-text">{{ record.orderStatusText }}</div>
          </div>
        </template>
        <template #payment-status-cell="{ record }">
          <div v-if="record.type === 'details'" class="status-cell-content">
            <div class="status-text">{{ record.paymentStatusText }}</div>
          </div>
        </template>
        <!-- 操作列 -->
        <template #operations-cell="{ record }">
          <div class="operations-cell-content">
            <div class="operation-buttons">
              <!-- 所有状态都显示查看详情按钮 -->
              <a-button type="text" size="small" @click="viewOrderDetail(record.originalId)">查看详情</a-button>
              <!-- 分配跟进 -->
              <a-button type="text" size="small" :status="record.followers.length > 0?'warning':'primary'" @click="showAssignFollowDialog(record.originalId, record.followers)">分配跟进</a-button>
              <!-- 待付款状态：取消订单 -->
              <template v-if="record.orderStatusText === '待付款'">
                <a-button type="text" size="small" status="warning" @click="showCancelDialog(record.originalId)">取消订单</a-button>
              </template>

              <!-- 待发货状态：发货、关闭订单 -->
              <template v-if="record.orderStatusText === '待发货'">
                <a-button
                  type="text"
                  size="small"
                  status="primary"
                  @click="handleDelivery(record)"
                >发货</a-button>
                <a-button type="text" size="small" status="warning" @click="showCloseOrderDialog(record.originalId)">关闭订单</a-button>
              </template>

              <!-- 已发货状态：确认收货、关闭订单 -->
              <template v-if="record.orderStatusText === '已发货'">
                <a-popconfirm content="是否确认收货？" @ok="handleConfirmReceipt(record.originalId)">
                  <a-button type="text" size="small" status="primary">确认收货</a-button>
                </a-popconfirm>
                <a-button type="text" size="small" status="warning" @click="showCloseOrderDialog(record.originalId)">关闭订单</a-button>
              </template>

              <!-- 交易成功状态：确认收货 -->
              <template v-if="record.orderStatusText == '交易成功' || record.orderStatusText == '已完成'">
                <a-popconfirm content="是否确认收货？" @ok="handleConfirmReceipt(record.originalId)">
                  <a-button type="text" size="small" status="primary">确认收货</a-button>
                </a-popconfirm>
              </template>

              <!-- 已关闭状态：退款 -->
              <template v-if="record.orderStatusText === '已关闭'">
                <a-button type="text" size="small" status="danger" @click="showRefundDialog(record.originalId, record.paymentStatusText)">申请退款</a-button>
              </template>
            </div>
          </div>
        </template>
      </a-table>
      <!-- 分页组件 -->
      <div class="pagination-container fixed-pagination">
        <a-pagination
          :total="totalItems"
          show-total
          show-jumper
          size="small"
          :page-size="pageSize"
          show-page-size
          :current="currentPage"
          @change="handlePageChange"
          @pageSizeChange="handlePageSizeChange"
        />
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import {
  IconCopy,
  IconMessage,
  IconPlus,
  IconDown,
  IconRight,
  IconLeft,
  IconCheck
} from "@arco-design/web-vue/es/icon";
import UpstreamOrderEntryDrawer from "./components/UpstreamOrderEntryDrawer.vue";
import { Message, Modal } from "@arco-design/web-vue";
import SearchForm from "./components/SearchForm.vue";
import OrderCancelDialog from "./components/OrderCancelDialog.vue";
import OrderCloseDialog from "./components/OrderCloseDialog.vue";
import OrderRefundDialog from "./components/OrderRefundDialog.vue";
import OrderBadgeDialog from "./components/OrderBadgeDialog.vue";
import AssignFollowDialog from "./components/AssignFollowDialog.vue";
import OrderEntryDrawer from "./components/OrderEntryDrawer.vue"; // 引入订单录入组件
import DetailsDrawer from "./components/DetailsDrawer.vue"; // 引入订单详情组件
import { useRouter } from "vue-router";
import orderApi from "@/api/master/order";

// 定义页面路由元信息
definePageMeta({
  name: "provider-ordermanagement-orderManage",
  path: "/provider/ordermanagement/orderManage"
});

onMounted(() => {
  getOrders();
});

// 表格加载状态
const tableLoading = ref(false);

const getOrders = () => {
  // 显示加载状态
  tableLoading.value = true;
  
  // 构建请求参数
  const params = {
    timeRange: timeRange.value,
    orderNumber: searchForm.value.orderNumber,
    productName: searchForm.value.productName,
    buyerPhone: searchForm.value.buyerPhone,
    address: searchForm.value.address,
    orderSource: searchForm.value.orderSource === '全部' ? null : searchForm.value.orderSource,
    // 当统计卡片不是"all"时，优先使用统计卡片ID映射到的订单状态值；否则使用搜索表单中的订单状态
    orderStatus: activeStatsCard.value !== 'all' ? statsCardToOrderStatus[activeStatsCard.value] : (searchForm.value.orderStatus === '全部' ? null : searchForm.value.orderStatus),
    afterSaleStatus: searchForm.value.afterSaleStatus === '全部' ? null : searchForm.value.afterSaleStatus,
    paymentMethod: searchForm.value.paymentMethod === '全部' ? null : searchForm.value.paymentMethod,
    paymentStatus: searchForm.value.paymentStatus === '全部' ? null : searchForm.value.paymentStatus,
    shippingStatus: searchForm.value.shippingStatus === '全部' ? null : searchForm.value.shippingStatus,
    // 将日期转换为时间戳格式
    startTime: searchForm.value.startTime ? new Date(searchForm.value.startTime).getTime(): null,
    endTime: searchForm.value.endTime ? new Date(searchForm.value.endTime).getTime(): null,
    // 添加分页参数
    page: currentPage.value,
    pageSize: pageSize.value
  };
  orderApi
    .getOrders(params)
    .then(res => {
      if (res.code === 200 && res.data && res.data.items) {
        originalOrders.value = res.data.items.map(order =>
          transformOrderData(order)
        );
        // 更新分页信息
        if (res.data.pageInfo) {
          totalItems.value = res.data.pageInfo.total || 0;
          totalPages.value = res.data.pageInfo.totalPage || 1;
          currentPage.value = res.data.pageInfo.currentPage || 1;
        }
        // 获取订单数据后再获取统计数据
        getOrderStats();
      } else {
        console.error("获取订单数据失败", res);
      }
      tableLoading.value = false;
    })
    .catch(err => {
      console.error("获取订单数据失败", err);
      tableLoading.value = false;
    });
};

// 将API返回的订单数据转换为表格需要的格式
const transformOrderData = order => {
  // 确保渠道信息有效
  const hasChannelInfo = Boolean(order.channelName || order.channelIconUrl);
  // 构建商品数据
  const products =
    order.items && order.items.length > 0
      ? order.items.map(item => ({
          productId: item.id,
          productName: item.productName,
          imageUrl: item.productImage || "/placeholder.png",
          price: item.unitPrice,
          quantity: item.quantity,
          goodsSpuId: item.goodsSpuId || "",
          goodsSkuId: item.goodsSkuId || "",
          thirdPartySpuId: item.thirdPartySpuId || "",
          thirdPartySkuId: item.thirdPartySkuId || "",
          returnLink: null, // API中没有对应字段
          afterSaleStatus: "", // API中没有对应字段
          merchantCode: item.thirdPartyProductCode || "",
          color: item.skuSpecifications || ""
        }))
      : [];

  // 获取收货地址信息
  const shipping = order.shipping || {};
  const address = shipping.id
    ? shipping.streetAddress || "未设置收货地址"
    : "未设置收货地址";
  const recipientName = shipping.recipientName || "";
  const recipientPhone = shipping.recipientPhone || "";
  // 判断订单是否可发货
  const isShippable =
    order.orderStatus === 1 &&
    order.paymentStatus === 1 &&
    order.shippingStatus === 0;
  // 判断是否可修改地址
  const hasAddressEdit = order.orderStatus < 2;
  // 判断是否可查看物流
  const hasLogisticsCheck = order.shippingStatus > 0;
  return {
    id: order.id,
    // 不再需要orderSn字段，直接使用id作为系统订单编号
    thirdPartyOrderSn: order.thirdPartyOrderSn || "", // 添加三方订单编号字段
    channelName: order.channelName || "", // 添加渠道名称字段
    platforName: order.platforName || "", // 添加平台名称字段
    storeName: order.storeName || "", // 添加店铺名称字段
    followers: order.followers || "", // 添加跟进人员字段
    channelId: order.channelId || "", // 添加渠道ID字段
    channelIconUrl: order.channelIconUrl || "", // 添加渠道图标URL字段
    hasChannelInfo: hasChannelInfo, // 添加是否有渠道信息的标志
    paymentMethodText: order.paymentMethodText || "", // 使用API返回的paymentMethodText字段
    totalAmount: order.totalAmount,
    shippingMethodText: order.shipping?.shippingMethodText || "", // 配送方式使用shipping.shippingMethodText字段
    paymentSn: order.paymentSn || "-",
    address: address,
    recipientName: recipientName,
    recipientPhone: recipientPhone,
    userId: order.userId || "",
    remark: order.remark || "", // 添加备注字段
    orderStatusText: order.orderStatusText || "未知状态",
    paymentStatusText: order.paymentStatusText || "未知状态",
    shippingStatusText: order.shippingStatusText || "未知状态",
    trackingNumber: shipping.trackingNumber
      ? `物流单号: ${shipping.trackingNumber}`
      : null,
    isShippable: isShippable,
    hasAddressEdit: hasAddressEdit,
    hasLogisticsCheck: hasLogisticsCheck,
    createdAt: formatTime(order.createdAt),
    products:
      products.length > 0
        ? products
        : [
            {
              productId: "unknown",
              productName: "无商品信息",
              imageUrl: "/placeholder.png",
              price: "0.00",
              quantity: 0,
              returnLink: null,
              afterSaleStatus: "",
              merchantCode: "",
              color: ""
            }
          ]
  };
};

// 格式化时间戳
const formatTime = timestamp => {
  if (!timestamp) return "-";
  const date = new Date(Number(timestamp));
  return date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false
    })
    .replace(/\//g, "-");
};

// 获取路由实例
const router = useRouter();

// 弹窗refs
const deliveryDialogRef = ref();
const cancelDialogRef = ref();
const closeDialogRef = ref();
const refundDialogRef = ref();
const badgeDialogRef = ref();
const assignFollowDialogRef = ref(null);
const orderEntryDrawerRef = ref(null);
const upstreamOrderEntryDrawerRef = ref(null);
const detailsDrawerRef = ref(null); // 订单详情组件ref

// 显示取消订单弹窗
const showCancelDialog = (orderId) => {
  if (cancelDialogRef.value) {
    cancelDialogRef.value.open(orderId, getOrders);
  }
};

// 显示关闭订单弹窗
const showCloseOrderDialog = (orderId) => {
  if (closeDialogRef.value) {
    closeDialogRef.value.open(orderId, getOrders);
  }
};

// 显示退款对话框
const showRefundDialog = (orderId, paymentStatus) => {
  if (refundDialogRef.value) {
    refundDialogRef.value.open(orderId, paymentStatus, getOrders);
  }
};

// 分配跟进人员对话框
const showAssignFollowDialog = (orderId, followers) => {
  if (assignFollowDialogRef.value) {
    assignFollowDialogRef.value.open(orderId, followers);
  }
};

// 显示添加标注弹窗
const showBadgeDialog = orderId => {
  badgeDialogRef.value.open(orderId, getOrders);
};

// 确认收货函数
const handleConfirmReceipt = orderId => {
  orderApi.confirmReceipt(orderId).then(res => {
    if (res && res.code === 200) {
      Message.success("确认收货成功");
      getOrders();
    } else {
      Message.error((res && res.message) || "确认收货失败");
    }
  });
};

// 显示订单详情抽屉
const viewOrderDetail = id => {
  detailsDrawerRef.value.open(id);
};

// 发货处理函数
const handleDelivery = (record) => {
  import("@/utils/common").then(module => {
    // 跳转到订单发货页面，传递订单ID
    module.navigateWithTag(router, `/master/order/orderManage/orderShipping/${record.originalId}`);
  });
};

const showAdvancedSearch = ref(false);
// 搜索表单项定义
const formItems = reactive([
  {
    field: "orderSource",
    label: "订单来源",
    type: "select",
    // placeholder: "全部",
    // options: [
    //   { label: "自采", value: "自采" },
    //   { label: "京东", value: "京东" }
    // ],
    // defaultValue: "",
    span: 1
  },
  {
    field: "orderNumber",
    label: "订单编号",
    type: "input",
    placeholder: "请输入",
    span: 1
  },
  {
    field: "productName",
    label: "商品名称",
    type: "input",
    placeholder: "请输入",
    span: 1
  },
  {
    field: "buyerPhone",
    label: "买家手机",
    type: "input",
    placeholder: "请输入",
    span: 1
  },
  {
    field: "address",
    label: "收货地址",
    type: "input",
    placeholder: "请输入",
    span: 1
  },
  {
    field: "orderStatus",
    label: "订单状态",
    type: "select",
    placeholder: "全部",
    options: [
      { label: "全部", value: "全部" },
      { label: "待付款", value: "0" },
      { label: "待发货", value: "1" },
      { label: "待收货", value: "2" },
      { label: "交易成功", value: "3" },
      { label: "已关闭", value: "4" },
      // { label: "已退款", value: "5" }
    ],
    defaultValue: "全部",
    span: 1
  },
  // {
  //   field: "afterSaleStatus",
  //   label: "售后状态",
  //   type: "select",
  //   placeholder: "全部",
  //   options: [
  //     { label: "全部", value: "全部" },
  //     { label: "无售后", value: "无售后" },
  //     { label: "售后中", value: "售后中" },
  //     { label: "售后完成", value: "售后完成" }
  //   ],
  //   defaultValue: "全部",
  //   span: 1
  // },
  {
    field: "paymentMethod",
    label: "支付方式",
    type: "select",
    placeholder: "请选择",
    options: [
      { label: "全部", value: "全部" },
      { label: "抖音支付", value: "抖音支付" },
      { label: "微信支付", value: "微信支付" },
      { label: "支付宝", value: "支付宝" }
    ],
    defaultValue: "全部",
    span: 1
  },
  {
    field: "paymentStatus",
    label: "支付状态",
    type: "select",
    placeholder: "全部",
    options: [
      { label: "全部", value: "全部" },
      { label: "未支付", value: "0" },
      { label: "部分支付", value: "1" },
      { label: "已支付", value: "2" },
      { label: "退款中", value: "3" },
      { label: "已退款", value: "4" }
    ],
    defaultValue: "全部",
    span: 1
  },
  {
    field: "shippingStatus",
    label: "配送状态",
    type: "select",
    placeholder: "全部",
    options: [
      { label: "全部", value: "全部" },
      { label: "未发货", value: "0" },
      { label: "已发货", value: "1" },
      { label: "部分发货", value: "2" },
      { label: "已收货", value: "3" },
      { label: "退货中", value: "4" },
      { label: "已退货", value: "5" }
    ],
    defaultValue: "全部",
    span: 1
  },
  {
    field: "createdAt",
    label: "下单时间",
    isTimeRange: true,
    timePresetField: "timeRange",
    startDateField: "startTime",
    endDateField: "endTime",
    span: 2
  }
]);

// 搜索表单字段
const searchForm = ref({
  orderNumber: "",
  productName: "",
  buyerPhone: "",
  address: "",
  orderStatus: "全部",
  afterSaleStatus: "全部",
  paymentMethod: "全部",
  paymentStatus: "全部",
  shippingStatus: "全部",
  timeRange: "recent6m",
  startTime: null,
  endTime: null,
  onlyShowSelected: false
});

// 更新搜索表单数据的方法
const updateSearchForm = (newVal) => {
  console.log('更新搜索表单:', newVal);
  searchForm.value = {...newVal};
};

const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value;
};

const handleSearch = (formData) => {
  // 更新搜索表单数据
  searchForm.value = { ...searchForm.value, ...formData };
  currentPage.value = 1;
  getOrders();
};

const resetSearch = () => {
  // 重置页码
  currentPage.value = 1;
  // 确保所有搜索参数都被清空
  Object.keys(searchForm.value).forEach(key => {
    if (key === 'timeRange') {
      searchForm.value[key] = 'recent6m';
    } else if (key === 'startTime' || key === 'endTime') {
      searchForm.value[key] = null;
    } else if (key.includes('status') || key === 'orderSource' || key === 'paymentMethod' || key === 'afterSaleStatus') {
      searchForm.value[key] = '全部';
    } else {
      searchForm.value[key] = '';
    }
  });
  getOrders();
};

// 存储API获取的订单数据
const originalOrders = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const totalItems = ref(0);
const totalPages = ref(1);

const tableData = computed(() => {
  const data = [];
  originalOrders.value.forEach(order => {
    const productCount = order.products.length;
    data.push({
      // 表头行数据
      rowId: `${order.id}-header`, // 表头唯一ID
      type: "header",
      originalId: order.id,
      id: order.id, // 添加id字段，用于显示系统订单编号
      followers: order.followers,
      channelId: order.channelId,
      channelIconUrl: order.channelIconUrl,
      // 使用id作为系统订单编号，不再需要单独的orderSn字段
      thirdPartyOrderSn: order.thirdPartyOrderSn,
      channelName: order.channelName || "", // 添加渠道名称字段
      platforName: order.platforName || "", // 添加平台名称字段
      orderSourceText: order.orderSourceText,
      createdAt: order.createdAt,
      remark: order.remark, // 添加备注字段
      productCount: productCount // 用于合并单元格计算
    });
    // 详情行数据 (每个商品一行)
    order.products.forEach((product, productIndex) => {
      data.push({
        // 订单级信息 (用于合并单元格或在详情行显示)
        originalId: order.id,
        // 使用id作为系统订单编号
        followers: order.followers,
        thirdPartyOrderSn: order.thirdPartyOrderSn,
        channelName: order.channelName || "", // 添加渠道名称字段
        platforName: order.platforName || "", // 添加平台名称字段
        orderSourceText: order.orderSourceText,
        paymentMethodText: order.paymentMethodText,
        shippingMethodText: order.shippingMethodText, // 配送方式字段
        totalAmount: order.totalAmount,
        paymentSn: order.paymentSn,
        address: order.address,
        recipientName: order.recipientName,
        recipientPhone: order.recipientPhone,
        userId: order.userId,
        orderStatusText: order.orderStatusText,
        paymentStatusText: order.paymentStatusText,
        shippingStatusText: order.shippingStatusText, // 添加配送状态字段
        deliveryTime: order.deliveryTime,
        trackingNumber: order.trackingNumber,
        isShippable: order.isShippable, // 订单级别的可发货状态
        hasAddressEdit: order.hasAddressEdit,
        hasLogisticsCheck: order.hasLogisticsCheck,
        // 商品级信息
        ...product,
        // 表格行属性
        rowId: `${order.id}-${product.productId}-details`, // 详情行唯一ID (订单ID + 商品ID)
        type: "details",
        productIndex: productIndex, // 商品在订单中的索引 (0-based)
        productCount: productCount // 订单总商品数
      });
    });
  });
  return data;
});

// --- 表格配置 ---
const selectedRowKeys = ref([]);

const rowSelection = reactive({
  type: "checkbox",
  showCheckedAll: false,
  selectedRowKeys: selectedRowKeys,
  onlyCurrent: false
});

const selectableRowKeysOnPage = computed(() => {
  return tableData.value
    .filter(row => row.type === "details")
    .map(row => row.rowId);
});

const isAllSelectedOnPage = computed(() => {
  const pageKeys = selectableRowKeysOnPage.value;
  if (pageKeys.length === 0) return false;
  return pageKeys.every(key => selectedRowKeys.value.includes(key));
});

const isIndeterminate = computed(() => {
  const pageKeys = selectableRowKeysOnPage.value;
  const selectedOnPageCount = pageKeys.filter(key =>
    selectedRowKeys.value.includes(key)
  ).length;
  return selectedOnPageCount > 0 && selectedOnPageCount < pageKeys.length;
});

const handleSelectAllOnPage = checked => {
  const pageKeys = selectableRowKeysOnPage.value;
  if (checked) {
    const newKeys = [...new Set([...selectedRowKeys.value, ...pageKeys])];
    selectedRowKeys.value = newKeys;
  } else {
    selectedRowKeys.value = selectedRowKeys.value.filter(
      key => !pageKeys.includes(key)
    );
  }
};

const columns = reactive([
  {
    title: "商品信息",
    slotName: "productInfo-cell",
    width: 380
  },
  {
    title: "单价/数量",
    slotName: "priceQuantity-cell",
    width: 120,
    align: "center"
  },
  { title: "收件信息", slotName: "receiver-cell", width: 150, align: "center" },
  { title: "实付金额 ", slotName: "payment-cell", width: 130 },
  {
    title: "支付方式",
    slotName: "payment-method-cell",
    width: 100,
    align: "center"
  },
  {
    title: "配送方式",
    slotName: "delivery-method-cell",
    width: 100,
    align: "center"
  },
  {
    title: "配送状态",
    slotName: "shipping-status-cell",
    width: 100,
    align: "center"
  },
  { title: "订单状态", slotName: "status-cell", width: 150 },
  { title: "支付状态", slotName: "payment-status-cell", width: 150 },
  {
    title: "操作",
    slotName: "operations-cell",
    width: 150,
    align: "center",
    fixed: "right"
  }
]);

// 用于合并单元格的 Span 方法
const spanMethod = ({ record, column, rowIndex }) => {
  if (record.type === "header") {
    // 表头行: 第一列合并所有列
    if (column.slotName === "productInfo-cell") {
      return { rowspan: 1, colspan: columns.length };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
  if (record.type === "details") {
    // 详情行: 对特定列进行垂直合并
    const isFirstProduct = record.productIndex === 0;
    const rowspan = isFirstProduct ? record.productCount : 0;
    // 需要垂直合并的列 (订单级别信息)
    const colsToSpan = [
      "payment-method-cell",
      "delivery-method-cell",
      "shipping-status-cell",
      "payment-cell",
      "receiver-cell",
      "status-cell",
      "operations-cell"
    ];

    if (colsToSpan.includes(column.slotName)) {
      return { rowspan: rowspan, colspan: 1 };
    }
  }
  // 其他单元格不合并
  return { rowspan: 1, colspan: 1 };
};

// 复制订单 ID 函数
const copyOrderId = async id => {
  try {
    await navigator.clipboard.writeText(id);
    Message.success(`订单号 ${id} 已复制`);
  } catch (err) {
    Message.error("复制失败");
    console.error("Failed to copy: ", err);
  }
};

// --- 分页处理程序 ---
const handlePageChange = page => {
  currentPage.value = page;
  getOrders();
};

const handlePageSizeChange = size => {
  pageSize.value = size;
  currentPage.value = 1;
  getOrders();
};

// 获取指定字段的选项
const getOptionsForField = fieldName => {
  const item = formItems.find(item => item.field === fieldName);
  return item ? item.options : [];
};

// 获取时间预设选项
const getTimePresetOptions = () => {
  const item = formItems.find(item => item.isTimeRange);
  return item ? item.presetOptions : [];
};

// 统计卡片ID到订单状态值的映射
const statsCardToOrderStatus = {
  "all": "全部",
  "unpaid": "0",
  "toBeShipped": "1",
  "shipped": "2",
  "success": "3",
  "closed": "4"
};

// 统计卡片数据
const statsCards = reactive([
  { id: "all", title: "全部订单", value: "0" },
  { id: "unpaid", title: "待付款", value: "0" },
  { id: "toBeShipped", title: "待发货", value: "0" },
  { id: "shipped", title: "已发货", value: "0" },
  { id: "closed", title: "已关闭", value: "0" },
  { id: "success", title: "交易成功", value: "0" }
]);

// 获取订单统计数据
const getOrderStats = () => {
  orderApi.getOrderBadgeStats({ timeRange: timeRange.value })
    .then(res => {
      if (res.code === 200 && res.data) {
        // 更新统计卡片数据
        statsCards.forEach(card => {
          switch (card.id) {
            case "all":
              card.value = (res.data.all || 0).toString();
              break;
            case "unpaid":
              card.value = (res.data.unpaid || 0).toString();
              break;
            case "toBeShipped":
              card.value = (res.data.toBeShipped || 0).toString();
              break;
            case "shipped":
              card.value = (res.data.shipped || 0).toString();
              break;
            case "closed":
              card.value = (res.data.closed || 0).toString();
              break;
            case "success":
              card.value = (res.data.success || 0).toString();
              break;
          }
        });
      } else {
        console.error("获取订单统计数据失败", res);
      }
    })
    .catch(err => {
      console.error("获取订单统计数据失败", err);
    });
};

// 当前选中的统计卡片
const activeStatsCard = ref("all");
// 当前时间范围
const timeRange = ref("recent6m"); // 默认近6个月

// 设置当前选中的统计卡片
const setActiveStatsCard = id => {
  activeStatsCard.value = id;
  currentPage.value = 1;
  if (searchForm.value.orderStatus !== '全部') {
    searchForm.value.orderStatus = '全部';
  }
  getOrders();
};

// 切换时间范围
const switchTimeRange = range => {
  timeRange.value = range;
  activeStatsCard.value = "all";
  getOrders();
};

// 统计卡片滚动相关
const statsCardsRef = ref(null);

// 滚动统计卡片
const scrollStatsCards = direction => {
  if (!statsCardsRef.value) return;
  const scrollAmount = 200; // 每次滚动的像素数
  const currentScroll = statsCardsRef.value.scrollLeft;
  if (direction === "left") {
    statsCardsRef.value.scrollTo({
      left: Math.max(0, currentScroll - scrollAmount),
      behavior: "smooth"
    });
  } else {
    statsCardsRef.value.scrollTo({
      left: currentScroll + scrollAmount,
      behavior: "smooth"
    });
  }
};

// 显示订单录入抽屉
const showOrderEntryDrawer = () => {
  orderEntryDrawerRef.value.open();
};

// 显示上游订单录入抽屉
const showUpstreamOrderEntryDrawer = () => {
  upstreamOrderEntryDrawerRef.value.open();
};

</script>

<style scoped lang="less">
@import "./orderManage.css";
</style>