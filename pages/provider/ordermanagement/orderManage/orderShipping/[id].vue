<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
-->
<template>
  <div class="ma-content-block p-4">

      <!-- 订单信息 -->
      <div class="flex items-center mb-4">
        <h3 class="text-lg font-medium">订单信息</h3>
      </div>

        <div class="order-info bg-gray-100 p-4 rounded-md mb-4" v-if="Object.keys(orderInfo).length > 0">
          <div style="display: flex;justify-content: space-between;flex-wrap: wrap">
            <div class="info-item">
              <span class="label">订单编号：</span>
              <span class="value">{{ orderInfo.orderNo || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">收货人：</span>
              <span class="value">{{ orderInfo.receiver || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">收货地址：</span>
              <span class="value">{{ orderInfo.address || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">联系电话：</span>
              <span class="value">{{ orderInfo.phone || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">邮箱：</span>
              <span class="value">{{ orderInfo.email || '-' }}</span>
            </div>
          </div>
        </div>
   

      <!-- 商品信息 -->
      <div class="product-info mb-6">
        <div class="flex items-center mb-4">
          <h3 class="text-lg font-medium">商品信息</h3>
        </div>
        
        <a-table :data="productList" :bordered="false" :pagination="false">
          <template #columns>
            <a-table-column title="序号" width="80" align="center">
              <template #cell="{ rowIndex }">
                {{ rowIndex + 1 }}
              </template>
            </a-table-column>
            <a-table-column title="商品图片" data-index="image" width="120" align="center">
              <template #cell="{ record }">
                <a-image width="80" height="80" :src="record.image" :preview="false" />
              </template>
            </a-table-column>
            <a-table-column title="商品信息" data-index="name" align="left">
              <template #cell="{ record }">
                <div class="product-name">{{ record.name }}</div>
                <div class="product-sku">商品编号：{{ record.sku }}</div>
                <div class="product-specs" v-if="record.specs">规格：{{ record.specs }}</div>
              </template>
            </a-table-column>
            <a-table-column title="采购数量" data-index="quantity" width="150" align="center" />
            <a-table-column title="商品单价" data-index="price" width="150" align="center">
              <template #cell="{ record }">
                {{ record.price }}
              </template>
            </a-table-column>
            <a-table-column title="小计" width="150" align="center">
              <template #cell="{ record }">
                {{ record.totalPrice }}
              </template>
            </a-table-column>
          </template>
        </a-table>
      </div>

      <!-- 发货信息 -->
      <div class="shipping-info mb-6">
        <div class="flex items-center mb-4">
          <h3 class="text-lg font-medium">发货信息 ({{ packages.length }})</h3>
          <a-button type="primary" class="ml-auto" @click="handleAddPackage">添加包裹</a-button>
        </div>

        <!-- 包裹列表 -->
        <div v-for="(pkg, index) in packages" :key="index" class="package-item mb-6 border rounded-md p-6 relative shadow-sm">
          <div class="package-header flex items-center mb-5">
            <h4 class="text-md font-medium">包裹{{ index + 1 }}</h4>
            <a-button v-if="packages.length > 1" class="ml-auto" type="text" @click="handleRemovePackage(index)">
              <template #icon><icon-close /></template>
            </a-button>
          </div>

          <!-- 包裹商品列表 -->
          <div class="package-products mb-5 border rounded overflow-hidden">
            <div class="grid grid-cols-2 bg-gray-100 p-4 font-medium">
              <div class="pl-3">商品信息</div>
              <div class="text-right pr-4">发货数量</div>
            </div>
            <div v-for="(item, itemIndex) in pkg.items" :key="itemIndex" class="grid grid-cols-2 border-b py-4 hover:bg-gray-50">
              <div class="flex items-center pl-4">
                <a-image width="60" height="60" :src="item.image" :preview="false" class="mr-4 border rounded p-1" />
                <div>
                  <div class="product-name font-medium mb-1">{{ item.name }}</div>
                  <div class="product-sku text-gray-500">商品编号：{{ item.sku }}</div>
                </div>
              </div>
              <div class="text-right pr-6 font-medium text-lg flex items-center justify-end">×{{ item.quantity }}</div>
            </div>
          </div>

          <!-- 配送方式和快递信息 -->
          <a-form :model="pkg" class="shipping-form mb-5 mt-4" :auto-label-width="false">
            <!-- 配送方式 -->
            <a-form-item field="shippingMethod" label="配送方式" :rules="[{ required: true, message: '请选择配送方式' }]" label-col-flex="80px">
              <a-radio-group v-model="pkg.shippingMethod" type="button">
                <a-radio value="1">快递物流</a-radio>
                <a-radio value="2">自定义物流</a-radio>
                <a-radio value="3">商家自送</a-radio>
                <a-radio value="4">线下自取</a-radio>
                <a-radio value="5">无需物流</a-radio>
              </a-radio-group>
            </a-form-item>
            
            <!-- 快递物流相关表单 -->
            <div v-if="pkg.shippingMethod == '1'">
              <a-row :gutter="16">
                <!-- 快递公司 -->
                <a-col :span="7">
                  <a-form-item field="expressCompany" label="物流公司" label-col-flex="80px" :rules="[{ required: true, message: '请选择物流公司' }]">
                    <a-select 
                      v-model="pkg.expressCompany" 
                      placeholder="请选择" 
                      :loading="loadingExpressCompanies"
                      :filter-option="false"
                      :allow-search="true"
                      @search="handleExpressCompanySearch"
                      @change="(value) => handleExpressCompanyChange(value, pkg)"
                    >
                      <a-option v-for="company in expressCompanies" :key="company.id" :value="company.companyCode">
                        {{ company.companyName }}
                      </a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                
                <!-- 快递单号 -->
                <a-col :span="9">
                  <a-form-item field="trackingNumber" label="物流单号" label-col-flex="80px" :rules="[{ required: true, message: '请输入物流单号' }]">
                    <a-input v-model="pkg.trackingNumber" placeholder="请输入物流单号" />
                  </a-form-item>
                </a-col>
                
                <!-- 发货地 -->
                <a-col :span="8">
                  <a-form-item field="shipping_origin" label="发货地" label-col-flex="80px">
                    <a-input v-model="pkg.shipping_origin" placeholder="选填" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            
            <!-- 自定义物流相关表单 -->
            <div v-if="pkg.shippingMethod == '2'">
              <a-row :gutter="16">
                <!-- 快递公司名称 -->
                <a-col :span="7">
                  <a-form-item field="expressCompanyName" label="物流公司" label-col-flex="80px" :rules="[{ required: true, message: '请输入物流公司名称' }]">
                    <a-input v-model="pkg.expressCompanyName" placeholder="请输入物流公司名称" />
                  </a-form-item>
                </a-col>
                
                <!-- 快递单号 -->
                <a-col :span="9">
                  <a-form-item field="trackingNumber" label="物流单号" label-col-flex="80px" :rules="[{ required: true, message: '请输入物流单号' }]">
                    <a-input v-model="pkg.trackingNumber" placeholder="请输入物流单号" />
                  </a-form-item>
                </a-col>
                
                <!-- 备注 -->
                <a-col :span="8">
                  <a-form-item field="mark" label="备注" label-col-flex="80px">
                    <a-input v-model="pkg.mark" placeholder="选填" />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            
            <!-- 商家自送相关表单 -->
            <div v-if="pkg.shippingMethod == '3'">
              <a-row :gutter="16">
                <!-- 备注 -->
                <a-col :span="24">
                  <a-form-item field="mark" label="备注" label-col-flex="80px">
                    <a-input v-model="pkg.mark" placeholder="请输入备注信息" style="width: 350px"/>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            
            <!-- 线下自取相关表单 -->
            <div v-if="pkg.shippingMethod == '4'">
              <a-row :gutter="16">  
                <!-- 备注 -->
                <a-col :span="24">
                  <a-form-item field="mark" label="备注" label-col-flex="80px">
                    <a-input v-model="pkg.mark" placeholder="请输入备注信息" style="width: 350px"/>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
            
            <!-- 无需物流相关表单 -->
            <div v-if="pkg.shippingMethod == '5'">
              <a-row :gutter="16">
                <!-- 备注 -->
                <a-col :span="24">
                  <a-form-item field="mark" label="备注" label-col-flex="80px">
                    <a-input v-model="pkg.mark" placeholder="请输入备注信息" style="width: 350px"/>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </a-form>
        </div>
      </div>

      <!-- 添加发货包裹弹窗 -->
      <a-modal
        :visible="showAddPackage"
        @update:visible="(val) => showAddPackage = val"
        title="添加发货包裹"
        :width="900"
        @cancel="handleCancelAddPackage"
        unmountOnClose
      >
        <div class="add-package">
          <a-table :data="productList" :bordered="false" :pagination="false" row-key="id">
            <template #columns>
              <a-table-column title="" width="50" align="center">
                <template #cell="{ record }">
                  <a-checkbox v-model="record.selected" :disabled="record.availableQuantity <= 0" />
                </template>
              </a-table-column>
              <a-table-column title="商品图片" data-index="image" width="120" align="center">
                <template #cell="{ record }">
                  <a-image width="60" height="60" :src="record.image" :preview="false" />
                </template>
              </a-table-column>
              <a-table-column title="商品信息" data-index="name" align="left">
                <template #cell="{ record }">
                  <div class="product-name">{{ record.name }}</div>
                  <div class="product-sku">商品编号：{{ record.sku }}</div>
                </template>
              </a-table-column>
              <a-table-column title="可选分包数量" data-index="availableQuantity" width="120" align="center" />
              <a-table-column title="分包件数" data-index="packageQuantity" width="120" align="center">
                <template #cell="{ record }">
                  <a-input-number 
                    v-model="record.packageQuantity" 
                    :min="0" 
                    :max="record.availableQuantity" 
                    :disabled="record.availableQuantity <= 0"
                  />
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
        <template #footer>
          <div class="flex justify-end gap-2">
            <a-button @click="handleCancelAddPackage">取消</a-button>
            <a-button type="primary" @click="handleConfirmAddPackage">确定</a-button>
          </div>
        </template>
      </a-modal>

    <!-- 验证和上传附件 -->
      <a-form :model="formData" class="mb-6" >
        <a-row :gutter="16">
          <a-form-item field="isExpressValidated" label="是否验证快递" label-col-flex="120px">
              <a-radio-group v-model="formData.isExpressValidated">
                <a-radio value = '1'>是</a-radio>
                <a-radio value = '0'>否</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item field="imagesUrl" label="上传附件" label-col-flex="90px">
              <ma-upload
                v-model="formData.imagesUrl"
                type="file"
                :multiple="true"
                :limit="5"
                :accept="'.jpg,.jpeg,.png,.pdf'"
                :size="5 * 1024 * 1024"
                :tip="'支持格式：jpg, jpeg, png, pdf，单个文件不超过5MB'"
                :requestData="{method: 'uploadImage'}"
              />
            </a-form-item>
        </a-row>
        
        <!-- 提交按钮 -->
        <div class="flex justify-center mt-6">
          <a-button type="primary" @click="handleSubmit" :loading="submitting" style="margin-right: 20px;">提交发货</a-button>
          <a-button @click="handleBack">返回</a-button>
        </div>
      </a-form>
    
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRouter, useRoute } from 'vue-router';
import orderApi from '@/api/master/order';
import commonApi from '@/api/common';
import { closeTag } from "~/utils/common.js";
definePageMeta({
  name: 'master-orderShipping',
  path: '/master/order/orderManage/orderShipping/:id'
});

const router = useRouter();
const route = useRoute();
let orderId = route.params.id;
const submitting = ref(false);
const showAddPackage = ref(false);


// 表单数据
const formData = reactive({
  isExpressValidated: '1',
  imagesUrl: []
});

// 订单信息
const orderInfo = reactive({});

// 加载状态
const loading = ref(true);

// 快递公司列表
const expressCompanies = ref([]);
const loadingExpressCompanies = ref(false);
const expressCompanySearchKeyword = ref('');

// 商品列表
const productList = ref([]);

// 包裹列表
const packages = ref([]);

// 计算商品可选分包数量
const calculateAvailableQuantities = () => {
  // 首先重置所有商品的可用数量为初始数量
  productList.value.forEach(product => {
    product.availableQuantity = product.pendingShipQuantity || product.quantity;
  });
  
  // 然后减去已经分配到包裹中的数量
  packages.value.forEach(pkg => {
    pkg.items.forEach(item => {
      const product = productList.value.find(p => p.id === item.id);
      if (product) {
        product.availableQuantity -= item.quantity;
      }
    });
  });
  
  console.log('计算后的可用数量:', productList.value.map(p => ({ id: p.id, name: p.name, availableQuantity: p.availableQuantity })));
};

// 添加包裹
const handleAddPackage = () => {
  // 重新计算每个商品的可用数量
  calculateAvailableQuantities();
  
  // 重置商品选择状态
  productList.value.forEach(item => {
    item.selected = false;
    item.packageQuantity = 0;
  });
  
  // 重新获取快递公司列表
  fetchExpressCompanies();
  
  showAddPackage.value = true;
};

// 确认添加包裹
const handleConfirmAddPackage = () => {
  const selectedProducts = productList.value.filter(item => item.selected && item.packageQuantity > 0);
  
  if (selectedProducts.length === 0) {
    Message.warning('请选择至少一个商品并设置数量');
    return;
  }
  
  // 验证分包数量不超过可用数量
  for (const product of selectedProducts) {
    if (product.packageQuantity > product.availableQuantity) {
      Message.error(`商品 ${product.name} 的分包数量不能超过可用数量 ${product.availableQuantity}`);
      return;
    }
  }
  
  // 创建新包裹
  const newPackage = {
    id: Date.now(), // 使用时间戳作为唯一ID
    items: selectedProducts.map(item => ({
      id: item.id,
      image: item.image,
      name: item.name,
      sku: item.sku,
      quantity: item.packageQuantity
    })),
    shippingMethod: '1',
    expressCompany: '', // 初始化为空字符串
    expressCompanyName: '',
    trackingNumber: '',
    shipping_origin: '',
    mark: '',
    pickup_address: ''
  };
  
  packages.value.push(newPackage);
  showAddPackage.value = false;
  
  // 重新计算可用数量
  calculateAvailableQuantities();
  
  Message.success('包裹添加成功');
};

// 取消添加包裹
const handleCancelAddPackage = () => {
  showAddPackage.value = false;
};

// 移除包裹
const handleRemovePackage = (index) => {
  packages.value.splice(index, 1);
  
  // 重新计算所有商品的可用数量
  calculateAvailableQuantities();
  
  Message.success('包裹已移除');
};

// ma-upload组件会直接更新formData.imagesUrl
// 不需要额外的处理函数

// 返回按钮处理函数
const handleBack = () => {
  const currentTag = {
        path: route.fullPath,
        name: route.name,
      };
      closeTag(currentTag, router);
};

// 提交发货
const handleSubmit = async () => {
  // 验证是否有包裹
  if (packages.value.length === 0) {
    Message.error('请添加至少一个发货包裹');
    return;
  }
  
  // 验证包裹信息
  for (let i = 0; i < packages.value.length; i++) {
    const pkg = packages.value[i];
    
    // 快递物流
    if (pkg.shippingMethod == '1') {
      if (!pkg.expressCompany) {
        Message.error(`包裹${i + 1}：请选择快递公司`);
        return;
      }
      if (!pkg.trackingNumber) {
        Message.error(`包裹${i + 1}：请输入快递单号`);
        return;
      }
    }
    
    // 自定义物流
    else if (pkg.shippingMethod == '2') {
      if (!pkg.expressCompanyName) {
        Message.error(`包裹${i + 1}：请输入物流公司名称`);
        return;
      }
      if (!pkg.trackingNumber) {
        Message.error(`包裹${i + 1}：请输入物流单号`);
        return;
      }
    }
    
    // 线下自取
    else if (pkg.shippingMethod == '4') {
      if (!pkg.pickup_address) {
        Message.error(`包裹${i + 1}：请输入自取地址`);
        return;
      }
    }
    
    if (pkg.items.length === 0) {
      Message.error(`包裹${i + 1}：请选择商品`);
      return;
    }
  }
  
  try {
    loading.value = true;
    
    // 整理提交数据格式
    const submitData = {
      orderId: orderId,
      packages: packages.value.map(pkg => {
        // 将包裹数据转换为接口要求的格式
        const packageData = {
          shippingMethod: parseInt(pkg.shippingMethod),
          items: pkg.items.map(item => ({
            orderItemId: item.id,
            quantity: item.quantity
          }))
        };
        
        // 根据配送方式添加不同字段
        if (pkg.shippingMethod == '1') { // 快递物流
          packageData.shipping_origin = pkg.shipping_origin || '';
        } else if (pkg.shippingMethod == '2') { // 自定义物流
          packageData.mark = pkg.mark || '';
        } else if (pkg.shippingMethod == '3') { // 商家自送
          packageData.mark = pkg.mark || '';
        } else if (pkg.shippingMethod == '4') { // 线下自取
          packageData.pickup_address = pkg.pickup_address || '';
          packageData.mark = pkg.mark || '';
        } else if (pkg.shippingMethod == '5') { // 无需物流
          packageData.mark = pkg.mark || '';
        }
        
        // 根据配送方式添加物流相关信息
        if (pkg.shippingMethod == '1') { // 快递物流
          // 确保使用正确的字段名
          packageData.shippingCompanyCode = pkg.expressCompany; // 使用companyCode字段
          packageData.shippingCompanyName = pkg.expressCompanyName; // 使用companyName字段
          packageData.trackingNumber = pkg.trackingNumber;
        } else if (pkg.shippingMethod == '2') { // 自定义物流
          packageData.shippingCompanyName = pkg.expressCompanyName;
          packageData.trackingNumber = pkg.trackingNumber;
        }
        
        return packageData;
      }),
      isExpressValidated: parseInt(formData.isExpressValidated),
      imagesUrl: formData.imagesUrl.map(file => file.response?.data?.url || file.url || '')
    };
    
    console.log('提交数据:', submitData);
    
    // 调用发货接口
    const res = await orderApi.shipOrderPackage(submitData);
    
    if (res.code === 200) {
      Message.success('发货成功');
      const currentTag = {
        path: route.fullPath,
        name: route.name,
      };
      closeTag(currentTag, router);
    } else {
      Message.error(res.message || '发货失败');
    }
  } catch (error) {
    console.error('提交发货错误:', error);
    Message.error('发货失败：' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 获取订单详情
const fetchOrderDetail = async () => {
  loading.value = true;
  try {
    const res = await orderApi.getOrderDetail(orderId);
    if (res.code === 200) {
      // 更新订单基本信息
      orderInfo.orderNo = res.data.id; // 使用id替代orderSn作为订单编号
      if (res.data.shipping) {
        orderInfo.receiver = res.data.shipping.recipientName;
        orderInfo.address = res.data.shipping.streetAddress || '';
        orderInfo.phone = res.data.shipping.recipientPhone;
      }
      
      // 更新商品列表
      if (res.data.items && res.data.items.length > 0) {
        productList.value = res.data.items.map(item => ({
          id: item.id,
          name: item.productName,
          sku: item.skuCode,
          specs: item.skuSpecifications,
          image: item.productImage,
          price: item.unitPrice,
          quantity: item.quantity,
          totalPrice: item.totalPrice,
          pendingShipQuantity: item.pendingShipQuantity || item.quantity,
          availableQuantity: item.pendingShipQuantity || item.quantity, // 初始化可用数量
          selected: false,
          packageQuantity: 0
        }));
      }
      
      // 初始化包裹
      if (res.data.packages && res.data.packages.length > 0) {
        packages.value = res.data.packages;
        // 获取包裹数据后重新计算可用数量
        calculateAvailableQuantities();
      }
      
      console.log('订单详情获取成功:', res.data);
    } else {
      Message.error(res.message || '获取订单详情失败');
    }
  } catch (error) {
    console.error('获取订单详情错误:', error);
    Message.error('获取订单详情失败：' + (error.message || '未知错误'));
  } finally {
    loading.value = false;
  }
};

// 获取快递公司列表
const fetchExpressCompanies = async (keyword = '') => {
  loadingExpressCompanies.value = true;
  try {
    const params = {
      page: 1,
      pageSize: 100
    };
    
    if (keyword) {
      params.company_name = keyword;
    }
    
    const res = await commonApi.getExpressCompanyCode(params);
    if (res.code === 200) {
      expressCompanies.value = res.data.items || [];
      console.log('快递公司列表获取成功:', expressCompanies.value);
    } else {
      Message.error(res.message || '获取快递公司列表失败');
    }
  } catch (error) {
    console.error('获取快递公司列表错误:', error);
  } finally {
    loadingExpressCompanies.value = false;
  }
};

// 搜索快递公司
const handleExpressCompanySearch = (keyword) => {
  expressCompanySearchKeyword.value = keyword;
  fetchExpressCompanies(keyword);
};

// 快递公司选择变化
const handleExpressCompanyChange = (value, pkg) => {
  // 找到对应的快递公司对象
  const selectedCompany = expressCompanies.value.find(company => company.companyCode === value);
  if (selectedCompany) {
    // 设置快递公司名称
    pkg.expressCompanyName = selectedCompany.companyName;
    console.log('快递公司选择变化:', value, pkg.expressCompanyName);
  }
};

// 初始化
onMounted(() => {
  // 获取快递公司列表
  fetchExpressCompanies();
  
  // 获取订单详情
  if (orderId) {
    fetchOrderDetail(orderId);
  }
});
</script>

<style scoped>
.ma-content-block {
  background-color: var(--color-bg-2);
  border-radius: 4px;
  overflow: hidden;
}

.general-card {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.info-item .label {
  color: #666;
  margin-right: 8px;
}

.info-item .value {

}

.product-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.product-sku {
  color: #666;
  font-size: 12px;
}
</style>