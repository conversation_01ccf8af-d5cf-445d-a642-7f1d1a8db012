
<template>
  <div class="big_content">
    <!-- 加载中状态 -->
    <a-spin :loading="loading" tip="加载中..." class="loading-container">
      <div class="page-wrapper">
        <!-- 顶部背景区域 -->
        <div class="header-section">
          <img src="/assets/images/h5/bgc.png" class="bgc-image">
        </div>
        
        <!-- 搜索输入区域 -->
        <div class="search-wrapper">
          <div class="search-container">
            <div class="search-input-wrapper">
              <a-input
                ref="inputRef"
                placeholder="请输入溯源码"
                v-model="userInput"
                @input="formatInput"
                @press-enter="customThrottleClick"
                allow-clear
                class="search-input no-focus-style"
              />
            </div>
            
            <a-button type="primary" class="search-button" @click="customThrottleClick">
              <icon-search />
              <span>立即查询</span>
            </a-button>
          </div>
        </div>
        
        <div class="log">
          <div>
            <p style="text-align: left;color: #47566D;font-weight: 550">验证提示</p>
            <p class="three">
              请对照可刮涂层上的溯源码，依次输入验证，结果分为以下三种情况:
            </p>
          </div>
          <div class="log_item" v-for="(item, index) in log" :key="index">
            <div class="log_left">
              <div class="log_circle" :class="item.bgc">
                <component :is="item.icon" />
              </div>
              <div class="log_line" v-if="index != log.length - 1" :class="`line-${index}`"></div>
            </div>
            <div class="log_right">
              <div class="title">
                {{ item.title }}
              </div>
              <div class="content">
                {{ item.content }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>
<script setup>
definePageMeta({
  layout: 'false',
  name: 'h5-SecurityCode-InspectionCenter',
  path: '/h5/SecurityCode/InspectionCenter'
})

// 添加meta标签，禁止页面缩放
useHead({
  meta: [
    { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no' }
  ]
})
import { ref, onMounted, h } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconCheck, IconRefresh, IconExclamation, IconSearch } from '@arco-design/web-vue/es/icon'
import { useRouter } from 'vue-router'
import SecurityCodeApi from '@/api/h5/SecurityCode.js'

// 路由实例
const router = useRouter()

// 响应式数据
const log = ref([
  {
    icon: IconCheck,
    bgc: 'green',
    title: '正确查询',
    content: '即您所购买的商品为正牌产品'
  },
  {
    icon: IconRefresh,
    bgc: 'orange',
    title: '重复查询',
    content: '即同一验证码被多次查验，请您确认首次查询是否由您本人所为，若非本人所为，则可能为假冒商品'
  },
  {
    icon: IconExclamation,
    bgc: 'red',
    title: '错误查询',
    content: '即验证码不存在，您购买的产品未经官方验证'
  }
])

const source_code = ref('')
const throttleTimeout = ref(null)
const loading = ref(false)
const userInput = ref('')
const inputRef = ref(null)

// 判断输入的溯源码是否满足16位数 只包含数字或者字母
const isValid16DigitString = (inputStr) => {
  // 定义一个正则表达式，用于匹配只包含16位数字和字母的字符串
  const pattern = /^[0-9a-zA-Z]{16}$/

  // 使用 test 方法检查输入字符串是否符合模式
  return pattern.test(inputStr)
}

// 显示弹窗
const showDog = (data) => {
  Modal.info({
    content: h('div', [
      h('div', { class: 'modal-title' }, '温馨提示'),
      h('div', { class: 'modal-content' }, data)
    ]),
    okText: '我知道了',
    okButtonProps: {
      type: 'primary',
      status: 'normal'
    },
    modalClass: 'custom-modal-width'
  })
}

// 格式化输入内容
const formatInput = () => {
  if (userInput.value) {
    let input = userInput.value.replace(/\s/g, '').replace(/[^a-zA-Z0-9]/g, '')

    // 截取前16位
    input = input.substring(0, 16)

    let formatted = ''

    for (let i = 0; i < input.length; i++) {
      if (i === 5 || i === 9 || i === 13) {
        formatted += ' ' + input[i]
      } else {
        formatted += input[i]
      }
    }

    userInput.value = formatted
  }
}

// 获取格式化后的溯源码
const formattedCode = () => {
  return userInput.value.replace(/\s/g, '')
}

// 手动实现节流
const customThrottleClick = () => {
  if (!throttleTimeout.value) {
    throttleTimeout.value = setTimeout(() => {
      throttleTimeout.value = null
    }, 2000)

    search()
  }
}

// 搜索功能
const search = async () => {
  source_code.value = formattedCode()
  console.log(source_code.value)
  let data = ''
  
  // if (!isValid16DigitString(source_code.value)) {
  //   data = '您输入的内容不正确，请核实再重新尝试'
  //   showDog(data)
  //   return
  // }
  
  loading.value = true
  
  try {
    // 带着溯源码跳转到 @[id].vue 这个页面
    // router.push({
    //   path: `/h5/SecurityCode/${source_code.value}`
    // })
    // return
     const res = await SecurityCodeApi.codeQuery({ sourceCode: source_code.value })
    console.log(res, 'res')
    
    // 先将loading设置为false，表示请求已完成
    loading.value = false
    
    // 检查响应状态码
    if (res.code == 200 || res.code == '200') {
      // 根据您提供的接口返回数据格式进行处理
      
      // 状态码为2：无法识别
      if (res.data.status == 2) {
        data = `非常抱歉，无法识别您所输入的内容，请再次核对后重新尝试，如有疑问\n请致电：4006-728-268`
        showDog(data)
        return
      }
      
      // 状态码为3：正品，跳转到详情页
      if (res.data.status == 3) {
        // 直接跳转到溯源码详情页，带上溯源码参数
        router.push({
          path: `/h5/SecurityCode/${source_code.value}`,
          query: {
            brandName: res.data.brandName,
            queryCount: res.data.queryCount,
            brandLocation: res.data.brandLocation,
            lastQueryTime: res.data.lastQueryTime,
            firstQueryTime: res.data.firstQueryTime
          }
        })
        return
      }
      
      // 状态码为4：已查询多次
      if (res.data.status == 4) {
        router.push({
          path: `/h5/SecurityCode/${source_code.value}`,
          query: {
            brandName: res.data.brandName,
            queryCount: res.data.queryCount,
            brandLocation: res.data.brandLocation,
            lastQueryTime: res.data.lastQueryTime,
            firstQueryTime: res.data.firstQueryTime
          }
        })
        return
      }
      
      // 状态码为5：查询次数超过五次
      if (res.data.status == 5) {
        data = `溯源码查询次数超过五次，无法继续查询。如有疑问，请致电：4006-728-268`
        showDog(data)
        return
      }
      
      // 状态码为6：操作过于频繁
      if (res.data.status == 6) {
        data = `您操作太频繁了，请稍后再试`
        showDog(data)
        return
      }
    }
  } catch (error) {
    console.error('查询失败:', error)
    loading.value = false
    Message.error('查询失败，请稍后再试')
  }
}
</script>
<style lang="less" scoped>
/* 去除输入框焦点效果，保留背景色 */
:deep(.no-focus-style),
:deep(.arco-input-wrapper) {
  &:focus-within, &:focus, &:hover, &.arco-input-focus {
    border-color: transparent !important;
    box-shadow: none !important;
    outline: none !important;
    background-color: #f5f7fa !important;
  }
}

:deep(.arco-input) {
  &:focus, &:active, &:hover {
    box-shadow: none !important;
    outline: none !important;
    background-color: #f5f7fa !important;
  }
}

/* 全局样式覆盖 */
:global(.arco-input-wrapper-focus) {
  border-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
  background-color: #f5f7fa !important;
}

/* 自定义弹窗宽度为容器宽度的70% */
:global(.custom-modal-width) {
  width: 85% !important;
  border-radius: 20px !important;
  max-width: 400px !important;
}

:global(.custom-modal-width .arco-modal) {
  width: 100% !important;
  max-width: 100% !important;
}

:global(.custom-modal-width .arco-modal-body) {
  word-break: break-word;
}

/* 自定义标题和内容样式 */
:global(.modal-title) {
  font-weight: 600;
  font-size: 20px;
  text-align: center;
  margin-bottom: 15px;
  color: #333;
}

:global(.modal-content) {
  font-size: 14px;
  line-height: 1.6;
}

/* 自定义确认按钮样式 */
:global(.custom-modal-width .arco-modal-footer) {
  padding: 0 10px 0px !important;
  text-align: center !important;
}

:global(.custom-modal-width .arco-modal-footer .arco-btn) {
  width: 100% !important;
  height: 44px !important;
  border-radius: 22px !important;
  font-size: 16px !important;
  background-color: #1278FF !important;
}

.loading-container {
  width: 100%;
  height: 100%;
}

.big_content {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  position: relative;
  overflow-x: hidden;
  touch-action: manipulation; /* 优化触摸操作 */
  -webkit-text-size-adjust: 100%; /* 禁止文本大小自动调整 */
  -webkit-tap-highlight-color: transparent; /* 去除点击高亮 */
}

.loading-container {
  width: 100%;
  height: 100%;
}

.page-wrapper {
  width: 100%;
  min-height: 100vh;
  background-color: white;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  
  /* 顶部背景区域样式 */
  .header-section {
    width: 100%;
    position: relative;
    overflow: visible;
    
    .bgc-image {
      width: 100%;
      display: block;
    }
  }
  
  /* 搜索区域样式 */
  .search-container {
    position: relative; /* 添加定位属性使z-index生效 */
    background-color: white !important;
    z-index: 999;
    border-radius: 20px;
    width: 100%;
    margin: -40px auto 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 18px;
    .search-input-wrapper {
      width: 100%;
      margin-top: 5px;
      margin-bottom: 20px;
      background: #f5f7fa;
      border-radius: 20px;
      overflow: hidden;
      
      .search-input {
        width: 100%;
        
        :deep(.arco-input) {
          height: 44px;
          font-size: 16px; /* 增加字体大小到16px，避免iOS自动缩放 */
          padding: 0 15px;
          -webkit-text-size-adjust: 100%; /* 禁止文本大小自动调整 */
          touch-action: manipulation; /* 优化触摸操作 */
        }
        
        :deep(.arco-input-wrapper) {
          background: #f5f7fa;
          border: none;
          box-shadow: none;
        }
        
        /* 去掉输入框获取焦点后的高亮效果和缩放 */
        :deep(.arco-input:focus),
        :deep(.arco-input:active),
        :deep(.arco-input:hover) {
          box-shadow: none !important;
          outline: none !important;
          font-size: 16px !important; /* 保持字体大小一致，避免iOS缩放 */
          transform: scale(1) !important; /* 禁止缩放 */
        }
        
        :deep(.arco-input-wrapper:focus),
        :deep(.arco-input-wrapper:focus-within),
        :deep(.arco-input-wrapper:active),
        :deep(.arco-input-wrapper:hover),
        :deep(.arco-input-wrapper.arco-input-focus) {
          border-color: transparent !important;
          box-shadow: none !important;
          outline: none !important;
        }
      }
    }
    
    .search-button {
      width: 100%;
      height: 44px;
      border-radius: 22px;
      font-size: 16px;
      background-color: #1890ff;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      
      :deep(.arco-icon) {
        margin-right: 5px;
      }
    }
  }
  
  .log {
    width: 90%;
    margin: 0 auto;
    padding-bottom: 20px;
    .three {
      margin-top: 10px;
      font-size: 13px;
      color: #9A9A9A;
      line-height: 20px;
      margin-bottom: 15px;
    }
    
    .log_item {
      width: 100%;
      display: flex;
 
      
      .log_left {
        width: 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .log_circle {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          svg {
            font-size: 16px;
            color: #fff;
          }
        }
        
        .green {
          background: #00B42A; // Arco绿色
        }
        
        .orange {
          background: #FF7D00; // Arco橙色
        }
        
        .red {
          background: #F53F3F; // Arco红色
        }
        
        .log_line {
          width: 1px;
          height: 50px;
          background: #DCDCDC;
        }
        
        /* 使用具体的类名来定位第一个连接线 */
        .line-0 {
          height: 32px !important;
        }
      }
      
      .log_right {
        flex: 1;
        margin-left: 10px;
        
        .title {
          font-size: 15px;
          font-weight: 550;
          color: #47566D;
        }
        
        .content {
          margin-top: 5px;
          font-size: 13px;
          color: #9A9A9A;
          line-height: 20px;
        }
      }
    }
  }
}

@media (min-width: 500px) {
  .big_content {
    margin: 0 auto;
    max-width: 500px;
  }
}
</style>
