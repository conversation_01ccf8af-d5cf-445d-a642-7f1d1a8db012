<template>
  <div class="security-code-details">
    <!-- 顶部背景和标题 -->
    <div class="header-section">
      <div class="header-bg">
        <img src="~/assets/images/h5/bgc.png" alt="背景图" />
      </div>
    </div>

    <!-- 查询结果内容 -->
    <div class="result-section">
      <div class="code-display">
        <p class="code-label">您所查询的溯源码为</p>
        <div class="code-value">{{ $route.params.id }}</div>
      </div>

      <!-- 查询结果状态 -->
      <div class="result-status">
        <div class="status-icon">
          <img src="~/assets/images/h5/success.png" alt="查询成功" />
        </div>
        <p class="status-text">
          您输入的溯源码({{ $route.params.id }})<br />
          查询结果为<span class="highlight">正品商品</span>
        </p>
      </div>

      <!-- 查询详情 -->
      <div class="query-details">
        <div class="details-section">
          <div class="details-left">
            <div class="details-title">查询时间</div>
            <div class="details-content">首次: {{ firstQueryTime }}</div>
            <div class="details-content" v-if="lastQueryTime">上次: {{ lastQueryTime }}</div>
          </div>
          <div class="details-right">
            <div class="query-count">第 <span class="count-num">{{ queryCount }}</span> 次</div>
          </div>
        </div>
        
        <div class="details-section">
          <div class="details-left">
            <div class="details-title">溯源信息</div>
            <div class="details-content">品牌名称：{{ brandName }}</div>
            <div class="details-content">品牌所属地：{{ brandLocation }}</div>
          </div>
          <div class="details-right">
            <div class="details-content text-right">商品已查询{{ queryCount }}次</div>
          </div>
        </div>

        <div class="disclaimer">
          *温馨提示: 每个溯源码只能查询5次, 请谨慎查看查询结果
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { ref, computed, onMounted } from 'vue';
import dayjs from 'dayjs';

definePageMeta({
  layout: 'false',
  name: 'h5-SecurityCode-Details',
  path: "/h5/SecurityCode/:id"
});

const route = useRoute();

// 从 URL 查询参数中获取数据
const brandName = computed(() => route.query.brandName || '-');
const queryCount = computed(() => route.query.queryCount || '0');
const brandLocation = computed(() => route.query.brandLocation || '-');

// 格式化时间戳
const formatTime = (timestamp) => {
  if (!timestamp) return '1970-1-1 08:00:00';
  return dayjs(parseInt(timestamp)).format('YYYY-MM-DD HH:mm:ss');
};

// 计算首次和上次查询时间
const firstQueryTime = computed(() => formatTime(route.query.firstQueryTime));
const lastQueryTime = computed(() => formatTime(route.query.lastQueryTime));
</script>

<style scoped lang="less">
.security-code-details {
  min-height: 100vh;
  background-color: white;
  font-family: PingFang SC, Helvetica Neue, Arial, sans-serif;
}

/* 顶部背景和标题 */
.header-section {
  width: 100%;
  position: relative;
  overflow: visible;
}

.header-bg {
  width: 100%;
  position: relative;
  z-index: 1;
  
  img {
    width: 100%;
    display: block;
  }
}

.header-content {
  position: absolute;
  z-index: 2;
  padding: 40px 20px 0;
  color: #fff;
  text-align: left;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

.title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px;
}

.subtitle {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

/* 查询结果内容 */
.result-section {
  position: relative;
  z-index: 999;
  background-color: white !important;
  border-radius: 20px;
  width: 100%;
  margin: -40px auto 15px;
  padding: 18px;
}

.code-display {
  background-color: white;
  border-radius: 16px;
  padding:10px 0px;
  text-align: center;
}

.code-label {
  font-weight: 500;
  font-size: 16px;
  color: black;
  margin: 0 0 10px;
}

.code-value {
  width: 100%;
  margin: auto;
  font-size: 24px;
  font-weight: normal;
  color: #1278FF;
  padding: 10px 40px;
  background-color: #f5f7fa;
  border-radius: 100px;
  display: inline-block;
  border: 1px solid #E2E3E3;
}

/* 查询结果状态 */
.result-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.status-icon {
  width: 150px;

  margin-bottom: 16px;

  img {
    width: 100%;
    height: 100%;
  }
}

.status-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333333;
}

.highlight {
  color: #1278FF;
  font-weight: 500;
}

/* 查询详情 */
.query-details {
  margin-top: 30px;
  background-color: #f5f7fa;
  border-radius: 16px;
  padding:16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.03);
}

.details-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.details-left {
  flex: 1;
}

.details-right {
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.details-title {
  font-weight: 500;
  font-size: 14px;
  color: black;
  margin-bottom: 5px;
}

.details-content {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.text-right {
  font-weight: 500;
  font-size: 14px;
  color: black;
  text-align: right;
  
  margin-right: -10px;
}

.query-count {
  background-color: #1D94FF;
  color: #fff;
  font-size: 12px;
  padding: 8px 15px;
  border-radius: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70px;
  height: 70px;
  box-shadow: 0 2px 8px rgba(18, 120, 255, 0.3);
}

.count-num {
  font-weight: 600;
  font-size: 24px;
  margin:0px 2px;
  margin-top: -6px;
}

.disclaimer {
  font-size: 12px;
  color: #999;
  margin-top: 15px;
  line-height: 1.5;
}

@media (min-width: 500px) {
  .security-code-details {
    margin: 0 auto;
    max-width: 500px;
  }
}
</style>
