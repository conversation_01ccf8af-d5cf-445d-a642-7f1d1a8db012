<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">分类品牌接口测试</h1>
    
    <!-- 分类选择 -->
    <div class="mb-6">
      <label class="block text-sm font-medium mb-2">选择分类ID:</label>
      <select v-model="selectedCategoryId" @change="loadBrands" class="border rounded px-3 py-2">
        <option value="">请选择分类</option>
        <option value="717736550871339171">分类 717736550871339171 (16个品牌)</option>
        <option value="717736550875533374">分类 717736550875533374 (15个品牌)</option>
        <option value="717736550875533482">分类 717736550875533482 (14个品牌)</option>
        <option value="717736550875533435">分类 717736550875533435 (13个品牌)</option>
        <option value="717736550871339149">分类 717736550871339149 (13个品牌)</option>
      </select>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="text-center py-4">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      <p class="mt-2">加载中...</p>
    </div>

    <!-- 品牌统计信息 -->
    <div v-if="stats && !loading" class="bg-blue-50 rounded-lg p-4 mb-6">
      <h2 class="text-lg font-semibold mb-3">统计信息</h2>
      <div class="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
        <div>
          <span class="text-gray-600">品牌总数:</span>
          <span class="font-medium ml-1">{{ stats.brandCount }}</span>
        </div>
        <div>
          <span class="text-gray-600">商品总数:</span>
          <span class="font-medium ml-1">{{ stats.totalProducts }}</span>
        </div>
        <div>
          <span class="text-gray-600">总销量:</span>
          <span class="font-medium ml-1">{{ stats.totalSales }}</span>
        </div>
        <div>
          <span class="text-gray-600">总库存:</span>
          <span class="font-medium ml-1">{{ stats.totalStock }}</span>
        </div>
        <div>
          <span class="text-gray-600">平均商品数:</span>
          <span class="font-medium ml-1">{{ stats.avgProductsPerBrand.toFixed(1) }}</span>
        </div>
      </div>
    </div>

    <!-- 品牌列表 -->
    <div v-if="brands.length > 0 && !loading" class="bg-white rounded-lg border">
      <div class="px-4 py-3 border-b">
        <h2 class="text-lg font-semibold">品牌列表 ({{ pagination.total }} 个品牌)</h2>
      </div>
      
      <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="brand in brands" :key="brand.id" 
               class="border rounded-lg p-4 hover:shadow-md transition-shadow">
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <h3 class="font-medium text-gray-900">{{ brand.name }}</h3>
                <p v-if="brand.description" class="text-sm text-gray-600 mt-1">{{ brand.description }}</p>
                <div class="mt-2 text-sm text-gray-500">
                  商品数量: <span class="font-medium text-red-600">{{ brand.productCount }}</span>
                </div>
                <div class="text-xs text-gray-400 mt-1">
                  销量: {{ brand.totalSales }} | 库存: {{ brand.totalStock }}
                </div>
              </div>
              <div v-if="brand.logoUrl" class="ml-3">
                <img :src="brand.logoUrl" :alt="brand.name" class="w-12 h-12 object-contain">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="pagination.totalPages > 1" class="px-4 py-3 border-t">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-600">
            第 {{ pagination.page }} 页，共 {{ pagination.totalPages }} 页
          </div>
          <div class="flex space-x-2">
            <button @click="loadPage(pagination.page - 1)" 
                    :disabled="!pagination.hasPrev"
                    class="px-3 py-1 border rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed">
              上一页
            </button>
            <button @click="loadPage(pagination.page + 1)" 
                    :disabled="!pagination.hasNext"
                    class="px-3 py-1 border rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed">
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && selectedCategoryId && brands.length === 0" 
         class="text-center py-8 text-gray-500">
      <p>该分类下暂无品牌数据</p>
    </div>

    <!-- API 响应信息 -->
    <div v-if="lastResponse" class="mt-8 bg-gray-50 rounded-lg p-4">
      <h3 class="text-sm font-medium mb-2">最后一次API响应:</h3>
      <pre class="text-xs text-gray-600 overflow-auto">{{ JSON.stringify(lastResponse, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

definePageMeta({
  layout: 'mall'
});

const selectedCategoryId = ref('');
const brands = ref([]);
const stats = ref(null);
const pagination = ref({});
const loading = ref(false);
const lastResponse = ref(null);

// 加载品牌数据
const loadBrands = async (page = 1, pageSize = 10) => {
  if (!selectedCategoryId.value) {
    brands.value = [];
    stats.value = null;
    return;
  }

  loading.value = true;
  
  try {
    // 并行请求品牌列表和统计信息
    const [brandsResponse, statsResponse] = await Promise.all([
      $fetch(`/api/v1/mall/categorybrands`, {
        method: 'GET',
        params: {
          categoryid: selectedCategoryId.value,
          page,
          pageSize
        }
      }),
      $fetch(`/api/v1/mall/categorybrands/stats`, {
        method: 'GET',
        params: {
          categoryid: selectedCategoryId.value
        }
      })
    ]);

    lastResponse.value = { brands: brandsResponse, stats: statsResponse };

    if (brandsResponse.code === 200) {
      brands.value = brandsResponse.data.list;
      pagination.value = brandsResponse.data.pagination;
    } else {
      brands.value = [];
      pagination.value = {};
    }

    if (statsResponse.code === 200) {
      stats.value = statsResponse.data;
    } else {
      stats.value = null;
    }

  } catch (error) {
    console.error('加载品牌数据失败:', error);
    brands.value = [];
    stats.value = null;
    lastResponse.value = { error: error.message };
  } finally {
    loading.value = false;
  }
};

// 加载指定页
const loadPage = (page) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    loadBrands(page);
  }
};
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>
