// ~/middleware/auth.global.ts (全局中间件)
import { useUserStore } from '@/store'
import tool from '@/utils/tool'
import NProgress from 'nprogress';

// 白名单路由：允许未登录用户访问的路由
const whiteRoute = [
  '/',
  '/master/login',
  'login', 
  'mineDoc', 
  'interfaceList', 
  'interfaceCode', 
  'signature', 
  '/error',
  '/mall',
  '/mall/user/center',
  '/mall/user/login',
  '/mall/user/register',
  '/master/dashboard', // 添加这一行
  '/merchant/dashboard', // 添加这一行
  '/merchant/join', // 商家入驻页面
  '/merchant/join/apply', // 商家入驻申请页面
  // '/provider/login', //服务商端登录
  // '/provider/register', //服务商注册页面
  '/provider/index', //服务商端首页
  '/provider/information', //服务商端信息
  '/provider/failed', //服务商端信息
  '/provider/review', //服务商端审核中页面
 
]

export default defineNuxtRouteMiddleware(async (to,from) => {
  const userStore = useUserStore()
  const config = useRuntimeConfig()
  
  // 提取路径前缀（master、mall、merchant等）
  const pathPrefix = to.path.split('/')[1] || 'master'

  // 根据路径前缀获取对应的 token
  const token = userStore.getToken(pathPrefix)
  // 进度条处理
  if (process.client) {
    NProgress.start()
  }
  // 登录状态处理
  if (token) {
    // 提取路径前缀（master或merchant）
    const pathPrefix = to.path.split('/')[1] || 'master'
    console.log('路径前缀:', pathPrefix)
    
    // 检查是否在登录页面
    if (to.name === 'masterlogin' || to.name === 'merchantlogin'|| to.path === '/master/login' || to.path === '/merchant/login' ) {
      const dashboardPath = `/${pathPrefix}/dashboard`
      console.log('重定向到仪表盘:', dashboardPath)
      return navigateTo(dashboardPath)
    }
    
    // 如果是根路径，允许访问导航页
    if (to.path === '/') {
      console.log('访问导航页')
      return
    }

    let allRouters = userStore.routers;
    if (!userStore.user) {
      const data = await userStore.requestUserInfo()
      allRouters = userStore.routers
      if (data) {
        return navigateTo(to.fullPath)
      }
    }
    
    // 收集所有路由路径，包括嵌套路由
    const collectPaths = (routers: any[]): string[] => {
      let paths: string[] = [];
      if (!routers) return paths;
      
      routers.forEach((router: any) => {
        if (router.path) {
          paths.push(router.path);
        }
        
        if (router.children && router.children.length > 0) {
          // 递归收集子路由路径
          paths = paths.concat(collectPaths(router.children));
          
          // 处理多级嵌套路由
          router.children.forEach((child: any) => {
            if (child.children && child.children.length > 0) {
              paths = paths.concat(collectPaths(child.children));
            }
          });
        }
      });
      
      return paths;
    };
    
    const all: string[] = collectPaths(allRouters || []);
    
    // 添加更多白名单路由
    const extendedWhiteRoute: string[] = [...whiteRoute];

    // 检查路由权限
    if(!all.includes(to.path) && !extendedWhiteRoute.includes(to.path)) {
      console.log('Route not found in paths or whitelist, checking for hidden routes...');
      
      // 尝试在路由定义中查找hidden:true的路由
      const findHiddenRoute = (routers: any[], targetPath: string): boolean => {
        if (!routers) return false;
        
        for (const router of routers) {
          if (router.path === targetPath && router.meta?.hidden === true) {
            console.log('Found hidden route:', router.path);
            return true;
          }
          
          // 检查动态路由匹配
          // 例如：路由定义为 /master/order/orderManage/detail/:id
          // 实际路径为 /master/order/orderManage/detail/ORD001
          if (router.path && router.path.includes(':') && targetPath.startsWith(router.path.split(':')[0])) {
            console.log('Found dynamic route match:', router.path, 'for path:', targetPath);
            return true;
          }
          
          if (router.children && router.children.length > 0) {
            const found = findHiddenRoute(router.children, targetPath);
            if (found) return true;
          }
        }
        
        return false;
      };
      
      // 如果是hidden:true的路由，允许访问
      if (findHiddenRoute(allRouters || [], to.path)) {
        console.log('Allowing access to hidden route:', to.path);
        return;
      }
      
      console.log('Route not found, redirecting to error page');
      return navigateTo({
        path: '/error',
        query: { redirect: from.fullPath }
      })
    }

  } else {
    // 检查路由是否在白名单中
// 先检查路由名称，再检查完整路径
if (!whiteRoute.includes(to.name as string) && !whiteRoute.some(route => to.path.startsWith(route))) {
  return navigateTo({
        name: '/',
        query: { redirect: to.fullPath }
      })
    }
  }
})
