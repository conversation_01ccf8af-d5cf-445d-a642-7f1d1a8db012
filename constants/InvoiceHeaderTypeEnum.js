/**
 * 发票抬头类型枚举 - 前端版本
 * 与后端 server/apps/master/constants/InvoiceHeaderTypeEnum.js 保持一致
 */

export const InvoiceHeaderTypeEnum = {
  /**
   * 个人抬头 - 个人消费者的发票抬头
   */
  PERSONAL: 1,
  
  /**
   * 企业抬头 - 企业/公司的发票抬头，包含完整的企业信息
   */
  COMPANY: 2,
  
  /**
   * 根据类型码获取类型名称
   * @param {number} typeCode - 类型码
   * @returns {string} - 类型名称
   */
  getTypeName(typeCode) {
    switch (parseInt(typeCode)) {
      case this.PERSONAL:
        return '个人抬头';
      case this.COMPANY:
        return '企业抬头';
      default:
        return '未知类型';
    }
  },
  
  /**
   * 获取所有抬头类型选项
   * @returns {Array} - 抬头类型选项数组
   */
  getAllTypes() {
    return [
      { value: this.PERSONAL, label: this.getTypeName(this.PERSONAL) },
      { value: this.COMPANY, label: this.getTypeName(this.COMPANY) }
    ];
  },
  
  /**
   * 验证抬头类型是否有效
   * @param {number} typeCode - 类型码
   * @returns {boolean} - 是否有效
   */
  isValidType(typeCode) {
    const validTypes = [this.PERSONAL, this.COMPANY];
    return validTypes.includes(parseInt(typeCode));
  }
};

export default InvoiceHeaderTypeEnum;
