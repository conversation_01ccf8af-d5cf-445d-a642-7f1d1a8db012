#!/usr/bin/env node

// 开发环境优化启动脚本
import { spawn } from 'child_process';
import path from 'path';

// 设置环境变量
process.env.NODE_ENV = 'development';
process.env.NUXT_TELEMETRY_DISABLED = '1';

// 内存优化和性能选项
const nodeOptions = [
  '--max-old-space-size=4096'
];

console.log('🚀 启动优化的开发服务器...');
console.log('📊 内存限制: 4GB');
console.log('⚡ 启用性能优化选项');

// 启动 Nuxt 开发服务器
const nuxtProcess = spawn('node', [
  ...nodeOptions,
  './node_modules/.bin/nuxt',
  'dev'
], {
  stdio: 'inherit',
  cwd: process.cwd()
});

// 处理进程退出
nuxtProcess.on('close', (code) => {
  console.log(`开发服务器退出，代码: ${code}`);
  process.exit(code);
});

// 处理中断信号
process.on('SIGINT', () => {
  console.log('\n正在关闭开发服务器...');
  nuxtProcess.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n正在关闭开发服务器...');
  nuxtProcess.kill('SIGTERM');
});
