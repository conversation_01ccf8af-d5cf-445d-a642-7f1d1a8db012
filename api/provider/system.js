import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 系统管理模块API
 */
export default {  
  /**
   * OCR相关接口
   */
  ocr: {
    /**
     * 营业执照OCR识别
     * @param {Object} data - 包含imageUrl的对象
     * @returns {Promise} - 返回OCR识别结果
     */
    businessLicense(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/integration/aliyun/ocr/business-license`,
        method: 'post',
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        data
      })
    }
  },
  /**
   * 个人中心相关接口
   */
  userInfo: {
    /**
     * 获取服务商详情信息
     * @param {string} userId - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    getDetail(userId) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/detail/${userId}`,
        method: 'get'
      })
    },
    
    /**
     * 添加联系人信息
     * @param {Object} data - 联系人信息数据
     * @returns {Promise} - 返回请求结果
     */
    addContacts(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/add-contacts`,
        method: 'post',
        data
      })
    },
    
    /**
     * 更新联系人信息
     * @param {Object} data - 联系人信息数据
     * @returns {Promise} - 返回请求结果
     */
    updateContacts(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/update-contacts`,
        method: 'post',
        data
      })
    },
    
    /**
     * 更新财务信息
     * @param {Object} data - 财务信息数据
     * @returns {Promise} - 返回请求结果
     */
    updateFinance(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/update-finance`,
        method: 'post',
        data
      })
    }
  },
  
  /**
   * 框架协议相关接口
   */
  agreement: {
    /**
     * 获取框架协议列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/provider/framework/list`,
        method: 'get',
        params
      })
    },

    /**
     * 上传框架协议
     * @param {Object} data - 协议数据
     * @returns {Promise} - 返回请求结果
     */
    create(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/framework/create`,
        method: 'post',
        data,

      })
    },

  
  },

  /**
   * 用户管理相关接口
   */
  user: {
    /**
     * 获取账号信息
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    getAccountInfo(id) {
      return request({
        url: `${apiBaseUrl}/v1/provider/user/account-info/${id}`,
        method: 'get'
      })
    },
    
    /**
     * 更新账号信息
     * @param {string|number} id - 用户ID
     * @param {Object} data - 用户数据
     * @param {string} data.username - 用户名
     * @param {string} data.phone - 手机号
     * @returns {Promise} - 返回请求结果
     */
    updateAccountInfo(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/provider/user/account-info/${id}`,
        method: 'put',
        data
      })
    },
    
    /**
     * 重置密码
     * @param {Object} data - 密码数据
     * @param {string} data.oldPassword - 原密码
     * @param {string} data.newPassword - 新密码
     * @param {string} data.confirmPassword - 确认密码
     * @returns {Promise} - 返回请求结果
     */
    resetPassword(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/provider/user/reset-password`,
        method: 'post',
        data
      })
    },
    /**
     * 获取用户列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user`,
        method: 'get',
        params
      })
    },

    /**
     * 获取用户详情
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建用户
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user`,
        method: 'post',
        data
      })
    },

    /**
     * 更新用户
     * @param {string|number} id - 用户ID
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除用户
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user/${id}`,
        method: 'delete'
      })
    },


  },

  /**
   * 部门管理相关接口
   */
  dept: {
    /**
     * 获取部门列表
     * @param {Object} params - 查询参数
     * @returns {Promise<any>} 返回部门列表的Promise
     */
    getDeptList(params) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept`,
        method: 'get',
        params
      })
    },

    /**
     * 获取部门详情
     * @param {number|string} id - 部门ID
     * @returns {Promise<any>} 返回部门详情的Promise
     */
    getDeptDetail(id) {
      return request({
        url: `${apiBaseUrl}/master/system/dept/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建部门
     * @param {Object} data - 部门数据
     * @returns {Promise<any>} 返回创建结果的Promise
     */
    createDept(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept`,
        method: 'post',
        data
      })
    },

    /**
     * 更新部门
     * @param {number|string} id - 部门ID
     * @param {Object} data - 部门数据
     * @returns {Promise<any>} 返回更新结果的Promise
     */
    updateDept(id, data) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除部门
     * @param {number|string} id - 部门ID
     * @returns {Promise<any>} 返回删除结果的Promise
     */
    deleteDept(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept/${id}`,
        method: 'delete'
      })
    },
  },

  /**
   * 角色管理相关接口
   */
  role: {
    /**
     * 获取角色列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role`,
        method: 'get',
        params
      })
    },

    /**
     * 获取角色详情
     * @param {string|number} id - 角色ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建角色
     * @param {Object} data - 角色数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role`,
        method: 'post',
        data
      })
    },

    /**
     * 更新角色
     * @param {string|number} id - 角色ID
     * @param {Object} data - 角色数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除角色
     * @param {string|number} id - 角色ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 菜单管理相关接口
   */
  menu: {
    /**
     * 获取菜单列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu`,
        method: 'get',
        params
      })
    },

    /**
     * 获取菜单详情
     * @param {string|number} id - 菜单ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建菜单
     * @param {Object} data - 菜单数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu`,
        method: 'post',
        data
      })
    },

    /**
     * 更新菜单
     * @param {string|number} id - 菜单ID
     * @param {Object} data - 菜单数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除菜单
     * @param {string|number} id - 菜单ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/${id}`,
        method: 'delete'
      })
    },
    /**
     * 获取菜单树形结构
     * @param {string|number} 
     * @returns {Promise} - 返回请求结果
     */
    tree(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/tree`,
        method: 'get',
        params
      })
    },
  
  },

  /**
 * 系统配置相关接口
 */
  configuration: {  
    /**
     * 获取所有配置类型
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getTypes(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/types`,
        method: 'get',
        params
      })
    },
    /**
     * 获取指定类型的配置对象
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    objectTypes(type,params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/object/${type}`,
        method: 'get',
        params
      })
    },
     /**
     * 获取指定类型的所有配置
     * @param {Object} params - 查询参数
    * @returns {Promise} - 返回请求结果
    */
    configure(type,params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/${type}`,
        method: 'get',
        params
      })
    },
   /**
     * 更新指定配置
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    update(type,key,params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/${type}/${key}`,
        method: 'put',
        data: params
      })
    },
  },
  /**
 * 操作日志管理相关接口
 */
  operLog: {
    /**
     * 获取操作日志列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs`,
        method: 'get',
        params
      })
    },
    /**
     * 获取操作日志详情
     * @param {string|number} id - 操作日志ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs/${id}`,
        method: 'get'
      })
    },
    /**
     * 删除操作日志
     * @param {string|number} id - 操作日志ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs/${id}`,
        method: 'delete'
      })
    },
    /**
     * 清空操作日志
     * @returns {Promise} - 返回请求结果
     */
    clear() {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs/clear`,
        method: 'delete'
      })
    },
  },
}
