import { apiBaseUrl } from '@/api/config'
import { request } from '@/utils/request.js'

/**
 * 服务商订单管理模块API
 */
export default {
  /**
   * 获取服务商订单列表
   * @param {Object} params - 查询参数，包含分页、筛选和排序信息
   * @returns {Promise} - 返回请求结果
   */
  getOrders(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/orders`,
      method: 'get',
      params
    })
  },

  /**
   * 获取订单详情
   * @param {string|number} id - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  getOrderDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/provider/orders/${id}`,
      method: 'get'
    })
  },

  /**
   * 派单操作
   * @param {Object} data - 派单数据
   * @param {string|number} data.orderId - 订单ID
   * @param {string|number} data.providerId - 服务商ID
   * @param {string|number} data.salesmanId - 业务员ID
   * @param {number} data.rate - 订单费率
   * @param {string} data.remark - 备注
   * @returns {Promise} - 返回请求结果
   */
  assignOrder(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/orders/assign`,
      method: 'post',
      data
    })
  },

  /**
   * 获取订单状态选项
   * @returns {Promise} - 返回订单状态选项列表
   */
  getOrderStatusOptions() {
    return Promise.resolve({
      success: true,
      data: [
        { label: '全部', value: '' },
        { label: '待付款', value: 0 },
        { label: '已付款待发货', value: 1 },
        { label: '已发货待收货', value: 2 },
        { label: '交易成功', value: 3 },
        { label: '已关闭', value: 4 },
        { label: '已退款', value: 5 }
      ]
    })
  },

  /**
   * 获取支付状态选项
   * @returns {Promise} - 返回支付状态选项列表
   */
  getPaymentStatusOptions() {
    return Promise.resolve({
      success: true,
      data: [
        { label: '全部', value: '' },
        { label: '未支付', value: 0 },
        { label: '部分支付', value: 1 },
        { label: '已支付', value: 2 },
        { label: '退款中', value: 3 },
        { label: '已退款', value: 4 }
      ]
    })
  },

  /**
   * 获取订单来源选项
   * @returns {Promise} - 返回订单来源选项列表
   */
  getOrderSourceOptions() {
    return Promise.resolve({
      success: true,
      data: [
        { label: '全部', value: '' },
        { label: '系统创建', value: 0 },
        { label: '后台创建', value: 1 },
        { label: '商城下单', value: 2 },
        { label: 'APP下单', value: 3 },
        { label: '小程序下单', value: 4 }
      ]
    })
  },

  /**
   * 获取订单来源文本映射
   * @param {number} source - 订单来源值
   * @returns {string} - 返回对应的文本
   */
  getOrderSourceText(source) {
    const sourceMap = {
      0: '系统创建',
      1: '后台创建',
      2: '商城下单',
      3: 'APP下单',
      4: '小程序下单'
    }
    return sourceMap[source] || '未知来源'
  },

  /**
   * 获取订单状态文本映射
   * @param {number} status - 订单状态值
   * @returns {string} - 返回对应的文本
   */
  getOrderStatusText(status) {
    const statusMap = {
      0: '待付款',
      1: '已付款待发货',
      2: '已发货待收货',
      3: '交易成功',
      4: '已关闭',
      5: '已退款'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取支付状态文本映射
   * @param {number} status - 支付状态值
   * @returns {string} - 返回对应的文本
   */
  getPaymentStatusText(status) {
    const statusMap = {
      0: '未支付',
      1: '部分支付',
      2: '已支付',
      3: '退款中',
      4: '已退款'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取支付方式文本映射
   * @param {number} methodId - 支付方式ID
   * @returns {string} - 返回对应的文本
   */
  getPaymentMethodText(methodId) {
    const methodMap = {
      1: '微信支付',
      2: '支付宝',
      3: '银联',
      4: '货到付款',
      5: '其他'
    }
    return methodMap[methodId] || '未知支付方式'
  },

  /**
   * 格式化订单数据
   * @param {Object} order - 原始订单数据
   * @returns {Object} - 格式化后的订单数据
   */
  formatOrderData(order) {
    return {
      ...order,
      // 添加文本字段
      orderStatusText: this.getOrderStatusText(order.order_status),
      paymentStatusText: this.getPaymentStatusText(order.payment_status),
      orderSourceText: this.getOrderSourceText(order.order_source),
      paymentMethodText: this.getPaymentMethodText(order.payment_method_id),

      // 格式化时间 - 确保时间戳正确处理
      createdAt: order.created_at,
      updatedAt: order.updated_at,
      paidAt: order.paid_at,
      shippedAt: order.shipped_at,
      completedAt: order.completed_at,
      cancelledAt: order.cancelled_at,

      // 格式化金额 - 确保数值正确
      totalAmount: parseFloat(order.total_amount || 0),
      paidAmount: parseFloat(order.paid_amount || 0),
      paid_amount: parseFloat(order.paid_amount || 0), // 保持原字段名
      total_amount: parseFloat(order.total_amount || 0), // 保持原字段名

      // 支付方式 - 优先使用订单表中的支付方式名称
      payment_method: order.payment_method || this.getPaymentMethodText(order.payment_method_id),

      // 渠道信息
      channelId: order.channel_id,
      channelName: order.channel_name,
      channelIconUrl: order.channel_icon_url,

      // 第三方订单号
      thirdPartyOrderSn: order.third_party_order_sn || '',

      // 收货信息
      receiverName: order.receiver_name,
      receiverPhone: order.receiver_phone,
      receiverAddress: order.receiver_address,

      // 保持原字段名以兼容前端
      receiver_name: order.receiver_name,
      receiver_phone: order.receiver_phone,
      receiver_address: order.receiver_address,

      // 时间字段保持原名
      created_at: order.created_at,
      updated_at: order.updated_at,
      paid_at: order.paid_at,

      // 商品信息 - 确保SKU字段正确
      products: (order.products || []).map(product => ({
        ...product,
        // 确保SKU字段映射正确
        sku: product.sku_code || product.product_sku || '',
        product_sku: product.sku_code || product.product_sku || '',
        third_sku: product.third_party_sku_id || product.third_sku || '',
        // 确保价格字段正确 - 处理可能的Decimal类型
        price: parseFloat((product.unit_price || product.price || 0).toString()),
        subtotal: parseFloat((product.total_price || product.subtotal || 0).toString())
      }))
    }
  },
  /**
  * 订单报备添加
  * @param {Object} data - 报备数据
  * @returns {Promise} - 返回请求结果
  */
  addOrderReport(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/order/order_report_add`,
      method: 'post',
      data
    })
  },

  /**
   * 获取订单报备列表
   * @param {Object} params - 查询参数
   * @param {string} params.id - id (可选)
   * @param {string} params.report_sn - 报备单号 (可选)
   * @param {string} params.customer_name - 客户名称 (可选)
   * @param {string} params.customer_region - 客户区域 (可选)
   * @param {string} params.platform_id - 平台id (可选)
   * @param {string} params.platform_name - 平台名称 (必需)
   * @param {number} params.report_type - 报备类型 (可选) 1=新客户报备 2=老客户续单 3=竞争客户转单
   * @param {number} params.report_amount - 报备金额 (可选)
   * @param {string} params.created_at - 报备日期 (可选)
   * @param {number} params.audit_status - 审核状态 (可选) 1=待审核 2=审核通过 3=驳回
   * @param {string} params.related_order_number - 关联订单号 (可选)
   * @param {string} params.reporter_id - 报备人id (可选)
   * @param {string} params.reporter_name - 报备人 (可选)
   * @returns {Promise} - 返回请求结果
   */
  getOrderReportList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/order/order_report_list`,
      method: 'get',
      params
    })
  },

  /**
   * 获取订单报备列表 (POST请求，JSON格式)
   * @param {Object} data - JSON格式的查询参数
   * @param {number} data.page - 页码
   * @param {number} data.pageSize - 每页数量
   * @param {number} data.audit_status - 审核状态 2=审核通过
   * @param {string} data.related_order_number - 关联订单号
   * @param {string} data.platform_id - 平台id
   * @returns {Promise} - 返回请求结果
   */
  getOrderReportListByPost(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-report/list`,
      method: 'get',
      params:data,
      // headers: {
      //   'Content-Type': 'application/json'
      // }
    })
  },

  /**
   * 获取订单报备详情
   * @param {string|number} id - 报备ID
   * @returns {Promise} - 返回请求结果
   */
  getOrderReportDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/provider/order/order_report_detail/${id}`,
      method: 'get'
    })
  },

  /**
   * 订单报备审核
   * @param {Object} data - 审核数据
   * @param {string|number} data.id - 报备ID
   * @param {number} data.audit_status - 审核状态 1=通过 3=驳回
   * @param {string} data.audit_remark - 审核意见
   * @returns {Promise} - 返回请求结果
   */
  auditOrderReport(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/order/order_report_audit`,
      method: 'post',
      data
    })
  },

  /**
   * 订单号报备
   * @param {Object} data - 报备数据
   * @param {string} data.third_party_order_sn - 第三方订单号
   * @returns {Promise} - 返回请求结果
   */
  addOrderByOrderSn(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/order/order_add`,
      method: 'post',
      data
    })
  },

  /**
   * 获取渠道列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码，默认为1
   * @param {number} params.pageSize - 每页数量，默认为10
   * @returns {Promise} - 返回请求结果
   */
  getChannelList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/platformManagement/channel`,
      method: 'get',
      params
    })
  },

  /**
   * 获取报备类型文本映射
   * @param {number} type - 报备类型值
   * @returns {string} - 返回对应的文本
   */
  getReportTypeText(type) {
    const typeMap = {
      1: '新客户报备',
      2: '老客户续单',
      3: '竞争客户转单'
    }
    return typeMap[type] || '未知类型'
  },

  /**
   * 获取审核状态文本映射
   * @param {number} status - 审核状态值
   * @returns {string} - 返回对应的文本
   */
  getAuditStatusText(status) {
    const statusMap = {
      1: '待审核',
      2: '审核通过',
      3: '已驳回'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 格式化报备数据
   * @param {Object} report - 原始报备数据
   * @returns {Object} - 格式化后的报备数据
   */
  formatReportData(report) {
    return {
      ...report,
      // 添加文本字段
      reportTypeText: this.getReportTypeText(report.report_type),
      auditStatusText: this.getAuditStatusText(report.audit_status),

      // 格式化时间戳
      expectedOrderTime: report.expected_order_time,
      expectedShippingTime: report.expected_shipping_time,
      createdAt: report.created_at,

      // 格式化金额
      reportAmount: parseFloat(report.report_amount || 0),

      // 商品信息格式化
      items: (report.items || []).map(item => ({
        ...item,
        unitPrice: parseFloat(item.unit_price || 0),
        reportPrice: parseFloat(item.report_price || 0),
        subtotal: parseFloat(item.subtotal || 0),
        quantity: parseInt(item.quantity || 0)
      }))
    }
  },

  /**
   * 获取服务商订单列表（用于订单号报备）
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码，默认为1
   * @param {number} params.pageSize - 每页数量，默认为10
   * @returns {Promise} - 返回请求结果
   */
  getProviderOrders(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/provider_orders`,
      method: 'get',
      params: {
        page: params.page || 1,
        pageSize: params.pageSize || 10,
        ...params
      }
    })
  },

  /**
   * 获取订单详情
   * @param {string} orderId - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  getOrderDetail(orderId) {
    return request({
      url: `${apiBaseUrl}/v1/master/cooperative/orders/${orderId}`,
      method: 'get'
    })
  }
}
