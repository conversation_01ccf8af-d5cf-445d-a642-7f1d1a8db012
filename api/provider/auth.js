import { apiBaseUrl } from '@/api/config'
import { request } from '@/utils/request.js'

export default {
  /**
   * 获取RSA公钥
   * @returns {Promise} 返回包含公钥的Promise
   */
  getPublicKey() {
    return request({
      url: `${apiBaseUrl}/v1/common/public-key`,
      method: 'get'
    })
  },

  /**
   * 用户登录
   * @param {object} params - 登录参数
   * @param {string} params.username - 用户名
   * @param {string} params.password - 已加密的密码
   * @param {string} params.captcha - 验证码
   * @param {string} params.captchaId - 验证码ID
   * @param {boolean} [rawResponse=false] - 是否返回原始响应
   * @returns {Promise} 返回登录结果的Promise
   */
  login(params = {}, rawResponse = false) {
    return request({
      url: `${apiBaseUrl}/v1/provider/user/login`,
      method: 'post',
      data: params,
      rawResponse // 添加原始响应选项
    })
  },

  /**
   * 用户注册
   * @param {object} params - 注册参数
   * @param {string} params.username - 用户名
   * @param {string} params.password - 密码
   * @param {string} params.phone - 手机号
   * @param {string} params.captcha - 验证码
   * @returns {Promise} 返回注册结果的Promise
   */
  register(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/user/register`,
      method: 'post',
      data: params
    })
  },

  /**
   * 获取注册验证码
   * @param {object} params - 获取验证码参数
   * @param {string} params.phone - 手机号
   * @returns {Promise} 返回验证码的Promise
   */
  getCaptcha(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/system/integration/aliyun/sms/verification-code`,
      method: 'post',
      data: params
    })
  },

  /**
   * 用户注销
   * @returns {Promise} 返回注销结果的Promise
   */
  logout() {
    return request({
      url: `${apiBaseUrl}/v1/provider/user/logout`,
      method: 'post'
    })
  },

  /**
   * 手机号登录
   * @param {object} params - 登录参数
   * @param {string} params.phone - 手机号
   * @param {string} params.captcha - 短信验证码
   * @param {boolean} [rawResponse=false] - 是否返回原始响应
   * @returns {Promise} 返回登录结果的Promise
   */
  phoneLogin(params = {}, rawResponse = false) {
    return request({
      url: `${apiBaseUrl}/v1/provider/user/phone-login`,
      method: 'post',
      data: params,
      rawResponse // 添加原始响应选项
    })
  },

  /**
   * 获取手机号登录验证码
   * @param {object} params - 获取验证码参数
   * @param {string} params.phone - 手机号
   * @returns {Promise} 返回验证码的Promise
   */
  getLoginCaptcha(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/system/integration/aliyun/sms/verification-code`,
      method: 'post',
      data: params
    })
  },

  /**
   * 发送手机验证码
   * @param {object} params - 参数
   * @param {string} params.phone - 手机号
   * @param {string} params.captcha - 图形验证码
   * @param {string} params.captchaId - 验证码ID
   * @param {string} params.type - 验证码类型，默认为login
   * @returns {Promise} 返回发送结果的Promise
   */
  sendSmsCode(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/system/integration/aliyun/sms/verification-code`,
      method: 'post',
      data: params
    })
  },

  /**
   * 获取省市区级联数据
   * @param {object} params - 参数
   * @param {number} params.parentId - 父区域ID，默认0表示全国
   * @param {boolean} params.excludeStreet - 是否排除街道级别数据
   * @returns {Promise} 返回区域树数据的Promise
   */
  getRegionTree(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/region/tree`,
      method: 'get',
      params
    })
  },

  /**
   * 提交服务商入驻信息
   * @param {Object} data - 包含服务商入驻信息的数据
   * @returns {Promise} - 返回请求结果
   */
  submitInfo(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/info/create`,
      method: 'post',
      data
    })
  },

  /**
   * 获取服务商详情信息
   * @param {string} userId - 用户ID
   * @returns {Promise} - 返回请求结果
   */
  getInfoDetail(userId = '587926350434674688') {
    return request({
      url: `${apiBaseUrl}/v1/provider/info/detail/${userId}`,
      method: 'get'
    })
  },

  /**
   * 更新服务商入驻信息
   * @param {Object} data - 包含服务商入驻信息的数据
   * @returns {Promise} - 返回请求结果
   */
  updateInfo(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/info/update`,
      method: 'post',
      data
    })
  }
}
