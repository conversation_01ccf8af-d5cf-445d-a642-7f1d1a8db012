import { request } from '@/utils/request'
import { apiBaseUrl } from '@/api/config'

export default {
  /**
   * 获取爬虫任务列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/spider/tasks`,
      method: 'get',
      params
    })
  },

  /**
   * 获取爬虫任务详情
   * @param {string} id - 任务ID
   * @returns {Promise} - 返回请求结果
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/spider/tasks/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建爬虫任务
   * @param {Object} data - 任务数据
   * @returns {Promise} - 返回请求结果
   */
  create(data) {
    return request({
      url: `${apiBaseUrl}/v1/spider/tasks`,
      method: 'post',
      data
    })
  },

  /**
   * 更新爬虫任务
   * @param {string} id - 任务ID
   * @param {Object} data - 更新数据
   * @returns {Promise} - 返回请求结果
   */
  update(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/spider/tasks/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除爬虫任务
   * @param {string} id - 任务ID
   * @returns {Promise} - 返回请求结果
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/spider/tasks/${id}`,
      method: 'delete'
    })
  },

  /**
   * 运行爬虫任务
   * @param {string} id - 任务ID
   * @param {Object} data - 运行参数
   * @returns {Promise} - 返回请求结果
   */
  run(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/spider/tasks/${id}/run`,
      method: 'post',
      data
    })
  },

  /**
   * 停止爬虫任务
   * @param {string} id - 任务ID
   * @returns {Promise} - 返回请求结果
   */
  stop(id) {
    return request({
      url: `${apiBaseUrl}/v1/spider/tasks/${id}/stop`,
      method: 'post'
    })
  }
}
