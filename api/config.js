/**
 * API 配置文件
 * 集中管理所有 API 的基础配置
 */

// API 基础URL，优先从环境变量中读取，如果不存在则使用硬编码的值
export const baseUrl = process.env.VITE_API_BASE_URL || 'https://v4api.ioa.8080bl.com'

// 服务器URL，与baseUrl相同，保持兼容性
export const serverUrl = baseUrl

// API 路径前缀
export const apiPrefix = '/api'

// 完整的API基础路径，不包含http://前缀
// 注意：为了使用service的错误处理逻辑，这里不应该包含协议和域名
export const apiBaseUrl = apiPrefix

// 超时时间（毫秒）
export const timeout = 10000

// 默认请求头
export const defaultHeaders = {
  'Content-Type': 'application/json;charset=UTF-8'
}
