import { request } from '@/utils/request.js'

export default {
  /**
   * 获取手机登录配置
   * @returns
   */
  getMobileConfig(params = {}) {
    return request({
      url: 'system/config/thirdparty/mobile',
      method: 'get',
      params
    })
  },

  /**
   * 更新手机登录配置
   * @returns
   */
  updateMobileConfig(data = {}) {
    return request({
      url: 'system/config/thirdparty/mobile/update',
      method: 'put',
      data
    })
  },

  /**
   * 获取微信登录配置
   * @returns
   */
  getWechatConfig(params = {}) {
    return request({
      url: 'system/config/thirdparty/wechat',
      method: 'get',
      params
    })
  },

  /**
   * 更新微信登录配置
   * @returns
   */
  updateWechatConfig(data = {}) {
    return request({
      url: 'system/config/thirdparty/wechat/update',
      method: 'put',
      data
    })
  },

  /**
   * 测试第三方登录配置
   * @returns
   */
  testConfig(data = {}) {
    return request({
      url: 'system/config/thirdparty/test',
      method: 'post',
      data
    })
  }
}
