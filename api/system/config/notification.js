import { request } from '@/utils/request.js'

export default {
  /**
   * 获取短信配置
   * @returns
   */
  getSmsConfig(params = {}) {
    return request({
      url: 'system/config/notification/sms',
      method: 'get',
      params
    })
  },

  /**
   * 更新短信配置
   * @returns
   */
  updateSmsConfig(data = {}) {
    return request({
      url: 'system/config/notification/sms/update',
      method: 'put',
      data
    })
  },

  /**
   * 获取邮箱配置
   * @returns
   */
  getEmailConfig(params = {}) {
    return request({
      url: 'system/config/notification/email',
      method: 'get',
      params
    })
  },

  /**
   * 更新邮箱配置
   * @returns
   */
  updateEmailConfig(data = {}) {
    return request({
      url: 'system/config/notification/email/update',
      method: 'put',
      data
    })
  },

  /**
   * 发送测试短信
   * @returns
   */
  testSms(data = {}) {
    return request({
      url: 'system/config/notification/sms/test',
      method: 'post',
      data
    })
  },

  /**
   * 发送测试邮件
   * @returns
   */
  testEmail(data = {}) {
    return request({
      url: 'system/config/notification/email/test',
      method: 'post',
      data
    })
  }
}
