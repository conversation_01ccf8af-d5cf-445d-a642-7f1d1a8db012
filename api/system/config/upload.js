import { request } from '@/utils/request.js'

export default {
  /**
   * 获取上传设置
   * @returns
   */
  getConfig(params = {}) {
    return request({
      url: 'system/config/upload',
      method: 'get',
      params
    })
  },

  /**
   * 更新上传设置
   * @returns
   */
  updateConfig(data = {}) {
    return request({
      url: 'system/config/upload/update',
      method: 'put',
      data
    })
  },

  /**
   * 获取上传驱动列表
   * @returns
   */
  getDrivers() {
    return request({
      url: 'system/config/upload/drivers',
      method: 'get'
    })
  },

  /**
   * 测试上传配置
   * @returns
   */
  testConfig(data = {}) {
    return request({
      url: 'system/config/upload/test',
      method: 'post',
      data
    })
  }
}
