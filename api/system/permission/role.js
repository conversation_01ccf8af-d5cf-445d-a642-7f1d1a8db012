import { request } from '@/utils/request.js'

export default {
  /**
   * 获取角色列表
   * @returns
   */
  getPageList(params = {}) {
    return request({
      url: 'system/role/index',
      method: 'get',
      params
    })
  },

  /**
   * 从回收站获取角色
   * @returns
   */
  getRecyclePageList(params = {}) {
    return request({
      url: 'system/role/recycle',
      method: 'get',
      params
    })
  },

  /**
   * 读取一个角色
   * @returns
   */
  read(id) {
    return request({
      url: 'system/role/read/' + id,
      method: 'get'
    })
  },

  /**
   * 添加角色
   * @returns
   */
  save(params = {}) {
    return request({
      url: 'system/role/save',
      method: 'post',
      data: params
    })
  },

  /**
   * 移到回收站
   * @returns
   */
  deletes(data) {
    return request({
      url: 'system/role/delete',
      method: 'delete',
      data
    })
  },

  /**
   * 恢复数据
   * @returns
   */
  recoverys(data) {
    return request({
      url: 'system/role/recovery',
      method: 'put',
      data
    })
  },

  /**
   * 真实删除
   * @returns
   */
  realDeletes(data) {
    return request({
      url: 'system/role/realDelete',
      method: 'delete',
      data
    })
  },

  /**
   * 更新数据
   * @returns
   */
  update(id, data = {}) {
    return request({
      url: 'system/role/update/' + id,
      method: 'put',
      data
    })
  },

  /**
   * 更改角色状态
   * @returns
   */
  changeStatus(data = {}) {
    return request({
      url: 'system/role/changeStatus',
      method: 'put',
      data
    })
  },

  /**
   * 获取角色列表
   * @returns
   */
  getList(params = {}) {
    return request({
      url: 'system/role/list',
      method: 'get',
      params
    })
  }
}
