import { request } from "@/utils/request.js";
import { apiBaseUrl } from "@/api/config";

/**
 * 商品管理模块API
 */
export default {
  /**
   * 官方模块相关接口
   */
  caseManagement: {
    /**
     * 获取案例列表
     * @param {Object} params - 查询参数，包含分页、筛选和排序信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/case-management/cases`,
        method: "get",
        params,
      });
    },

    create(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/case-management/addCases`,
        method: "post",
        data: params,
      });
    },

    update(id, params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/case-management/cases/${id}`,
        method: "put",
        data: params,
      });
    },

    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/official/case-management/cases/${id}`,
        method: "delete",
      });
    },

    faceList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/case-management/face/cases`,
        method: "get",
        params,
      });
    },
    // 案例详情
    detail(id){
      return request({
        url: `${apiBaseUrl}/v1/official/case-management/face/cases/${id}`,
        method: "get",
      });
    }
  },
  /**
   * 新闻管理模块API
   */
  newsManagement: {
    // 新闻分类
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/news-categories`,
        method: "get",
        params,
      });
    },
    create(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/news-categories`,
        method: "post",
        data: params,
      });
    },
    update(id, params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/news-categories/${id}`,
        method: "put",
        data: params,
      });
    },
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/official/news-categories/${id}`,
        method: "delete",
      });
    },
    // 新闻列表
    list:{
      getList(params = {},id = '') {
        return request({
          url: `${apiBaseUrl}/v1/official/news-articles/${id}`,
          method: "get",
          params,
        });
      },
      create(params = {}) {
        return request({
          url: `${apiBaseUrl}/v1/official/news-articles`,
          method: "post",
          data: params,
        });
      },
      update(id, params = {}) {
        return request({
          url: `${apiBaseUrl}/v1/official/news-articles/${id}`,
          method: "put",
          data: params,
        });
      },
      delete(id) {
        return request({
          url: `${apiBaseUrl}/v1/official/news-articles/${id}`,
          method: "delete",
        });
      },
      detail(id){
        return request({
          url: `${apiBaseUrl}/v1/official/news-articles/${id}`,
          method: "get",
        });
      }
    }
  },
  /**
   * 产品管理模块API
   */
  productManagement: {
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/product-categories`,
        method: "get",
        params,
      });
    },
    create(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/product-categories`,
        method: "post",
        data: params,
      });
    },
    update(id, params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/product-categories/${id}`,
        method: "put",
        data: params,
      });
    },
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/official/product-categories/${id}`,
        method: "delete",
      });
    },
    list:{
      getList(params = {}) {
        return request({
          url: `${apiBaseUrl}/v1/official/products`,
          method: "get",
          params,
        });
      },
      create(params = {}) {
        return request({
          url: `${apiBaseUrl}/v1/official/products`,
          method: "post",
          data: params,
        });
      },
      update(id, params = {}) {
        return request({
          url: `${apiBaseUrl}/v1/official/products/${id}`,
          method: "put",
          data: params,
        });
      },
      delete(id) {
        return request({
          url: `${apiBaseUrl}/v1/official/products/${id}`,
          method: "delete",
        });
      },
      detail(id){
        return request({
          url: `${apiBaseUrl}/v1/official/products/${id}`,
          method: "get",
        });
      }
    }
  },
  /**
   * 招聘管理模块API
   */
  recruitManagement:{
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/recruitment/list`,
        method: "get",
        params,
      });
    },
    create(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/recruitment/create`,
        method: "post",
        data: params,
      });
    },
    update(id, params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/recruitment/${id}`,
        method: "put",
        data: params,
      });
    },
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/official/recruitment/${id}`,
        method: "delete",
      });
    },
    // 门户获取招聘详情
    faceDetail(id){
      return request({
        url: `${apiBaseUrl}/v1/official/recruitment/face/${id}`,
        method: "get",
      });
    },
    //门户获取招聘列表
    faceList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/recruitment/face/list`,
        method: "get",
        params,
      });
    }
  },
  /**
   * 消息管理模块API
   */
  messageManagement:{
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/message-management/list`,
        method: "get",
        params,
      });
    },
    create(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/message-management/messages`,
        method: "post",
        data: params,
      });
    },
    update(id, params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/message-management/messages/${id}/status`,
        method: "patch",
        data: params,
      });
    },
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/official/message-management/messages/${id}`,
        method: "delete",
      });
    },
  },
  /**
   * 基础管理模块API
   */
  basisManagement:{
    getData(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/company-info/2`,
        method: "get",
        params,
      });
    },
    update(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/company-info/2`,
        method: "put",
        data: params,
      });
    }
  },

  enterpriseInfo: {
    /**
     * 获取案例列表
     * @param {Object} params - 查询参数，包含分页、筛选和排序信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/enterprise-information/enterprise-informations`,
        method: "get",
        params,
      });
    },

    create(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/enterprise-information/addEnterpriseInformations`,
        method: "post",
        data: params,
      });
    },

    update(id, params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/enterprise-information/enterprise-informations/${id}`,
        method: "put",
        data: params,
      });
    },

    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/official/enterprise-information/enterprise-informations/${id}`,
        method: "delete",
      });
    },

    faceList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/official/case-management/face/cases`,
        method: "get",
        params,
      });
    },
    // 案例详情
    detail(id){
      return request({
        url: `${apiBaseUrl}/v1/official/case-management/face/cases/${id}`,
        method: "get",
      });
    }
  },

  // 获取企业信息列表
  getEnterpriseInformations(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/official/enterprise-information/face/enterprise-informations`,
      method: "get",
      params
    });
  },

};
