import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 服务商团队管理模块API
 */
export default {
  /**
   * 获取团队列表
   * @param {Object} params - 查询参数，包含分页信息
   * @returns {Promise} - 返回请求结果
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/team`,
      method: 'get',
      params
    })
  },

  /**
   * 获取团队详情
   * @param {string|number} id - 团队ID
   * @returns {Promise} - 返回请求结果
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/provider/team/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建团队
   * @param {Object} data - 团队数据
   * @returns {Promise} - 返回请求结果
   */
  create(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/team`,
      method: 'post',
      data
    })
  },

  /**
   * 更新团队
   * @param {string|number} id - 团队ID
   * @param {Object} data - 团队数据
   * @returns {Promise} - 返回请求结果
   */
  update(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/provider/team/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除团队
   * @param {string|number} id - 团队ID
   * @returns {Promise} - 返回请求结果
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/provider/team/${id}`,
      method: 'delete'
    })
  },

  /**
   * 获取用户列表（用于团队成员选择）
   * @returns {Promise} - 返回请求结果
   */
  getUserList() {
    return request({
      url: `${apiBaseUrl}/v1/provider/team/users/list`,
      method: 'get'
    })
  }
}
