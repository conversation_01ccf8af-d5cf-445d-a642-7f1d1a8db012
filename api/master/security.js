import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 溯源码管理模块API
 */
export default {
  
    /**
     * 获取自主品牌列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getBrandList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/self-owned-brand`,
        method: 'get',
        params
      })
    },

    /**
     * 创建自主品牌
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    createBrand(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/self-owned-brand`,
        method: 'post',
        data
      })
    },

    /**
     * 更新自主品牌
     * @param {string|number} id - 用户ID
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    updateBrand(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/self-owned-brand/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除自主品牌
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    deleteBrand(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/self-owned-brand/${id}`,
        method: 'delete'
      })
    },

    /**
     * 获取自主品牌详情
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    getBrandDetail(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/self-owned-brand/${id}`,
        method: 'get'
      })
    },

     /**
     * 生成溯源码
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    generateSourceCode(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/self-source-code/generate`,
        method: 'post',
        data
      })
    },
    /**
     * 获取溯源码查询情况列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getSourceCodeQueryList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/self-source-code-query/list`,
        method: 'get',
        params
      })
    }
}
