import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 上游管理模块API
 */
export default {
  /**
   * 上游授权管理相关接口
   */
  authorization: {
    /**
     * 获取授权列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果11
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization`,
        method: 'get',
        params
      })
    },

    /**
     * 获取授权详情
     * @param {string|number} id - 授权ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建授权
     * @param {Object} data - 授权数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization`,
        method: 'post',
        data
      })
    },

    /**
     * 更新授权
     * @param {Object} data - 授权数据
     * @returns {Promise} - 返回请求结果
     */
    update(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization/${data.id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除授权
     * @param {string|number} id - 授权ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization/${id}`,
        method: 'delete'
      })
    },
    
    /**
     * 添加授权
     * @param {Object} data - 授权数据
     * @returns {Promise} - 返回请求结果
     */
    addAuthorization(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization/add`,
        method: 'post',
        data
      })
    }
  },

  /**
   * 平台管理相关接口
   */
  /**
   * 商铺管理相关接口
   */
  store: {
    /**
     * 获取商铺列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store`,
        method: 'get',
        params
      })
    },

    /**
     * 获取商铺详情
     * @param {string|number} id - 商铺ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建商铺
     * @param {Object} data - 商铺数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store`,
        method: 'post',
        data
      })
    },

    /**
     * 更新商铺
     * @param {string|number} id - 商铺ID
     * @param {Object} data - 商铺数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      console.log('更新商铺数据 ID:', id);
      console.log('更新商铺数据内容:', data);
      delete data.id;
      if (!id) {
        console.error('更新商铺数据错误: 缺少 id 参数');
        return Promise.reject(new Error('缺少必要的 ID 参数'));
      }
      
      if (!data || typeof data !== 'object') {
        console.error('更新商铺数据错误: data 不是一个有效对象', data);
        return Promise.reject(new Error('数据格式错误'));
      }
      
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除商铺
     * @param {string|number} id - 商铺ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store/${id}`,
        method: 'delete'
      })
    }
  },

  platform: {
    /**
     * 获取渠道列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getChannelList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/channel`,
        method: 'get',
        params
      })
    },
    
    /**
     * 获取平台列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform`,
        method: 'get',
        params
      })
    },

    /**
     * 获取平台详情
     * @param {string|number} id - 平台ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建平台
     * @param {Object} data - 平台数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform`,
        method: 'post',
        data
      })
    },

    /**
     * 更新平台
     * @param {string|number} id - 平台ID
     * @param {Object} data - 平台数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      console.log('更新平台数据 ID:', id);
      console.log('更新平台数据内容:', data);
      
      if (!id) {
        console.error('更新平台数据错误: 缺少 id 参数');
        return Promise.reject(new Error('缺少必要的 ID 参数'));
      }
      
      if (!data || typeof data !== 'object') {
        console.error('更新平台数据错误: data 不是一个有效对象', data);
        return Promise.reject(new Error('数据格式错误'));
      }
      
      // 删除数据中的id字段，避免重复
      delete data.id;
      
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除平台
     * @param {string|number} id - 平台ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform/${id}`,
        method: 'delete'
      })
    }
  },
  
  /**
   * 爬虫相关接口
   */
  spider: {
    /**
     * 获取爬虫日志列表
     * @param {Object} params - 查询参数，包含分页信息和店铺ID
     * @returns {Promise} - 返回请求结果
     */
    getLogs(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/spider/logs`,
        method: 'get',
        params
      })
    },

    /**
     * 调用爬虫接口
     * @param {Object} data - 爬虫调用参数
     * @param {string} data.platform_id - 平台ID
     * @param {string} data.store_id - 店铺ID
     * @param {string} data.spider_type - 爬虫类型
     * @param {string} data.mode - 执行模式 (incremental|single)
     * @param {number} [data.last_sync_time] - 上次同步时间 (增量模式必填)
     * @param {string} [data.order_no] - 订单号 (单订单模式必填)
     * @returns {Promise} - 返回请求结果
     */
    callSpider(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/spider/spiders/call`,
        method: 'post',
        data
      })
    }
  }
}
