import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 服务商合作模块API
 */
export default {
  /**
   * 服务商订单管理相关接口
   */
  orders: {
    /**
     * 获取服务商订单列表
     * 只返回当前登录用户有权限查看的订单（基于 provider.order_assignment 表的 salesman_id）
     * @param {Object} params - 查询参数
     * @param {number} params.page - 页码，默认1
     * @param {number} params.pageSize - 每页数量，默认10
     * @param {string} params.sortField - 排序字段，默认created_at
     * @param {string} params.sortOrder - 排序方向，asc/desc，默认desc
     * @param {string} params.orderNumber - 订单编号（模糊搜索）
     * @param {string} params.thirdPartyOrderSn - 第三方订单编号（模糊搜索）
     * @param {string} params.productName - 商品名称（模糊搜索）
     * @param {string} params.receiverName - 收货人姓名（模糊搜索）
     * @param {string} params.orderSource - 订单来源
     * @param {number} params.orderStatus - 订单状态
     * @param {string} params.timeRange - 时间范围：recent6m/recent3m/recent1m/recent1w
     * @param {string} params.orderTimeStart - 下单开始时间
     * @param {string} params.orderTimeEnd - 下单结束时间
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/orders`,
        method: 'get',
        params
      })
    },

    /**
     * 获取服务商订单详情
     * 需要验证当前用户是否有权限查看该订单
     * @param {string|number} id - 订单ID
     * @returns {Promise} - 返回请求结果
     */
    getDetail(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/orders/${id}`,
        method: 'get'
      })
    },

    /**
     * 获取订单统计数据
     * @param {Object} params - 查询参数
     * @param {string} params.timeRange - 时间范围
     * @returns {Promise} - 返回请求结果
     */
    getStats(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/orders/stats`,
        method: 'get',
        params
      })
    }
  },

  /**
   * 服务商商品管理相关接口
   */
  products: {
    /**
     * 获取服务商商品列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/products`,
        method: 'get',
        params
      })
    },

    /**
     * 获取服务商商品详情
     * @param {string|number} id - 商品ID
     * @returns {Promise} - 返回请求结果
     */
    getDetail(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/products/${id}`,
        method: 'get'
      })
    }
  },

  /**
   * 服务商报备管理相关接口
   */
  reports: {
    /**
     * 获取报备列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/reports`,
        method: 'get',
        params
      })
    },

    /**
     * 创建报备
     * @param {Object} data - 报备数据
     * @returns {Promise} - 返回请求结果
     */
    create(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/reports`,
        method: 'post',
        data
      })
    },

    /**
     * 更新报备
     * @param {string|number} id - 报备ID
     * @param {Object} data - 报备数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/reports/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除报备
     * @param {string|number} id - 报备ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/reports/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 服务商发票管理相关接口
   */
  invoices: {
    /**
     * 获取发票列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/invoices`,
        method: 'get',
        params
      })
    },

    /**
     * 申请发票
     * @param {Object} data - 发票申请数据
     * @returns {Promise} - 返回请求结果
     */
    apply(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/invoices`,
        method: 'post',
        data
      })
    },

    /**
     * 获取发票详情
     * @param {string|number} id - 发票ID
     * @returns {Promise} - 返回请求结果
     */
    getDetail(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/invoices/${id}`,
        method: 'get'
      })
    }
  },

  /**
   * 服务商订单分配相关接口
   */
  allocation: {
    /**
     * 获取订单分配列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/allocation`,
        method: 'get',
        params
      })
    },

    /**
     * 分配订单
     * @param {Object} data - 分配数据
     * @returns {Promise} - 返回请求结果
     */
    assign(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/allocation`,
        method: 'post',
        data
      })
    },

    /**
     * 获取可分配的服务商列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getProviders(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/allocation/providers`,
        method: 'get',
        params
      })
    },

    /**
     * 获取服务商的业务员列表
     * @param {string|number} providerId - 服务商ID
     * @returns {Promise} - 返回请求结果
     */
    getSalesmen(providerId) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/allocation/providers/${providerId}/salesmen`,
        method: 'get'
      })
    }
  },

  /**
   * 服务商付款申请相关接口
   */
  payments: {
    /**
     * 获取付款申请列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/payments`,
        method: 'get',
        params
      })
    },

    /**
     * 创建付款申请
     * @param {Object} data - 付款申请数据
     * @returns {Promise} - 返回请求结果
     */
    create(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/payments`,
        method: 'post',
        data
      })
    },

    /**
     * 审核付款申请
     * @param {string|number} id - 申请ID
     * @param {Object} data - 审核数据
     * @returns {Promise} - 返回请求结果
     */
    audit(id, data) {
      return request({
        url: `${apiBaseUrl}/v1/master/cooperative/payments/${id}/audit`,
        method: 'post',
        data
      })
    }
  }
}
