import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 上游管理模块API
 */
export default {
  /**
   * 服务商管理相关接口
   */
  provider: {
    /**
     * 获取服务商列表
     * @param {Object} data - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/provider/user/list`,
        method: 'get',
        params:data
      })
    },
    
    /**
     * 获取服务商详情
     * @param {string|number} id - 服务商ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/provider/user/detail/${id}`,
        method: 'get'
      })
    },
    
    /**
     * 获取服务商业务详情信息
     * @param {string|number} id - 服务商ID
     * @returns {Promise} - 返回请求结果
     */
    getBusinessInfo(id) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/detail/${id}`,
        method: 'get'
      })
    },
    
    /**
     * 审核服务商
     * @param {Object} data - 审核数据
     * @returns {Promise} - 返回请求结果
     */
    audit(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/audit`,
        method: 'post',
        data
      })
    },
    
    /**
     * 审核服务商信息
     * @param {Object} data - 审核数据，包含id、status和remark
     * @returns {Promise} - 返回请求结果
     */
    auditInfo(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/audit`,
        method: 'post',
        data
      })
    },
    
    /**
     * 修改服务商状态（启用/禁用）
     * @param {Object} data - 状态数据
     * @returns {Promise} - 返回请求结果
     */
    changeStatus(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/user/status`,
        method: 'post',
        data
      })
    },
    
    /**
     * 更新服务商详情信息
     * @param {Object} data - 服务商详情数据，包含基本信息、联系信息、财务信息、资质审核等
     * @returns {Promise} - 返回请求结果
     */
    updateInfo(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/update`,
        method: 'post',
        data
      })
    },
    
    /**
     * 更新服务商业务员
     * @param {Object} data - 包含服务商ID和业务员ID列表
     * @returns {Promise} - 返回请求结果
     */
    updateSalesman(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/update-salesman`,
        method: 'post',
        data
      })
    },
    
    /**
     * 更新服务商平台费率
     * @param {Object} data - 包含服务商ID和平台费率数组
     * @returns {Promise} - 返回请求结果
     */
    updatePlatformRate(data) {
      return request({
        url: `${apiBaseUrl}/v1/provider/info/platform_rate`,
        method: 'post',
        data
      })
    }
  },

  /**
   * 获取服务商对指定渠道的费率
   * @param {string|number} providerId - 服务商ID
   * @param {string|number} channelId - 渠道ID
   * @returns {Promise} - 返回请求结果
   */
  getProviderChannelRate(providerId, channelId) {
    return request({
      url: `${apiBaseUrl}/v1/provider/info/rate/${providerId}/${channelId}`,
      method: 'get'
    })
  },
  
  /**
   * 上游授权管理相关接口
   */
  authorization: {
    /**
     * 获取授权列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization`,
        method: 'get',
        params
      })
    },

    /**
     * 获取授权详情
     * @param {string|number} id - 授权ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建授权
     * @param {Object} data - 授权数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization`,
        method: 'post',
        data
      })
    },

    /**
     * 更新平台费率
     * @param {Object} data - 包含provider_id和platform_rate数组
     * @returns {Promise} - 返回请求结果
     */
    updatePlatformRate(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/provider/platform-rate`,
        method: 'post',
        data
      })
    },

    /**
     * 更新授权
     * @param {Object} data - 授权数据
     * @returns {Promise} - 返回请求结果
     */
    update(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization/${data.id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除授权
     * @param {string|number} id - 授权ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization/${id}`,
        method: 'delete'
      })
    },
    
    /**
     * 添加授权
     * @param {Object} data - 授权数据
     * @returns {Promise} - 返回请求结果
     */
    addAuthorization(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/upstream/authorization/add`,
        method: 'post',
        data
      })
    }
  },

  /**
   * 平台管理相关接口
   */
  /**
   * 商铺管理相关接口
   */
  store: {
    /**
     * 获取商铺列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store`,
        method: 'get',
        params
      })
    },

    /**
     * 获取商铺详情
     * @param {string|number} id - 商铺ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建商铺
     * @param {Object} data - 商铺数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store`,
        method: 'post',
        data
      })
    },

    /**
     * 更新商铺
     * @param {string|number} id - 商铺ID
     * @param {Object} data - 商铺数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      console.log('更新商铺数据 ID:', id);
      console.log('更新商铺数据内容:', data);
      delete data.id;
      if (!id) {
        console.error('更新商铺数据错误: 缺少 id 参数');
        return Promise.reject(new Error('缺少必要的 ID 参数'));
      }
      
      if (!data || typeof data !== 'object') {
        console.error('更新商铺数据错误: data 不是一个有效对象', data);
        return Promise.reject(new Error('数据格式错误'));
      }
      
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除商铺
     * @param {string|number} id - 商铺ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/store/${id}`,
        method: 'delete'
      })
    }
  },

  platform: {
    /**
     * 获取渠道列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getChannelList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/channel`,
        method: 'get',
        params
      })
    },
    
    /**
     * 获取平台列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform`,
        method: 'get',
        params
      })
    },

    /**
     * 获取平台详情
     * @param {string|number} id - 平台ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建平台
     * @param {Object} data - 平台数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform`,
        method: 'post',
        data
      })
    },

    /**
     * 更新平台
     * @param {string|number} id - 平台ID
     * @param {Object} data - 平台数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      console.log('更新平台数据 ID:', id);
      console.log('更新平台数据内容:', data);
      
      if (!id) {
        console.error('更新平台数据错误: 缺少 id 参数');
        return Promise.reject(new Error('缺少必要的 ID 参数'));
      }
      
      if (!data || typeof data !== 'object') {
        console.error('更新平台数据错误: data 不是一个有效对象', data);
        return Promise.reject(new Error('数据格式错误'));
      }
      
      // 删除数据中的id字段，避免重复
      delete data.id;
      
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除平台
     * @param {string|number} id - 平台ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/platformManagement/platform/${id}`,
        method: 'delete'
      })
    }
  }
}
