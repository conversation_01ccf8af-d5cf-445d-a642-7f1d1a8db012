/**
 * 防火墙规则API
 */
import { request } from '~/utils/request'

const API_PREFIX = '/api/v1/master/system/config/securityCenter/firewall'

export default {
  /**
   * 获取防火墙规则列表
   * @param {Object} params 查询参数
   * @returns {Promise} 请求结果
   */
  getList(params = {}) {
    return request({
      url: API_PREFIX,
      method: 'GET',
      params
    })
  },

  /**
   * 根据ID获取防火墙规则
   * @param {string} id 规则ID
   * @returns {Promise} 请求结果
   */
  getById(id) {
    return request({
      url: `${API_PREFIX}/${id}`,
      method: 'GET'
    })
  },

  /**
   * 创建防火墙规则
   * @param {Object} data 规则数据
   * @returns {Promise} 请求结果
   */
  create(data) {
    return request({
      url: API_PREFIX,
      method: 'POST',
      data
    })
  },

  /**
   * 更新防火墙规则
   * @param {string} id 规则ID
   * @param {Object} data 更新数据
   * @returns {Promise} 请求结果
   */
  update(id, data) {
    return request({
      url: `${API_PREFIX}/${id}`,
      method: 'PUT',
      data
    })
  },

  /**
   * 删除防火墙规则
   * @param {string} id 规则ID
   * @returns {Promise} 请求结果
   */
  delete(id) {
    return request({
      url: `${API_PREFIX}/${id}`,
      method: 'DELETE'
    })
  },

  /**
   * 批量删除防火墙规则
   * @param {Object} data 包含ids数组的对象
   * @returns {Promise} 请求结果
   */
  batchDelete(data) {
    return request({
      url: `${API_PREFIX}/batch/delete`,
      method: 'POST',
      data
    })
  },

  /**
   * 更新规则状态
   * @param {string} id 规则ID
   * @param {Object} data 状态数据
   * @returns {Promise} 请求结果
   */
  updateStatus(id, data) {
    return request({
      url: `${API_PREFIX}/${id}/status`,
      method: 'PATCH',
      data
    })
  },

  /**
   * 获取规则统计信息
   * @returns {Promise} 请求结果
   */
  getStats() {
    return request({
      url: `${API_PREFIX}/stats`,
      method: 'GET'
    })
  },

  /**
   * 测试IP匹配
   * @param {Object} data 测试数据
   * @returns {Promise} 请求结果
   */
  testMatch(data) {
    return request({
      url: `${API_PREFIX}/test/match`,
      method: 'POST',
      data
    })
  }
}
