/**
 * Master端报备管理API
 */
import { request } from '~/utils/request'

// API基础URL
const apiBaseUrl = '/api'

export default {
  /**
   * 获取报备列表
   * @param {Object} params - 查询参数，包含分页、筛选和排序信息
   * @returns {Promise} - 返回请求结果
   */
  getReportList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-report/list`,
      method: 'get',
      params
    })
  },

  /**
   * 获取报备详情
   * @param {string|number} id - 报备ID
   * @returns {Promise} - 返回请求结果
   */
  getReportDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-report/detail/${id}`,
      method: 'get'
    })
  },

  /**
   * 审核报备
   * @param {string|number} id - 报备ID
   * @param {Object} data - 审核数据
   * @param {number} data.audit_status - 审核状态（2-审核通过，3-审核驳回）
   * @param {string} data.audit_remark - 审核备注
   * @returns {Promise} - 返回请求结果
   */
  auditReport(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-report/audit/${id}`,
      method: 'post',
      data
    })
  },

  /**
   * 获取报备类型文本
   * @param {number} type - 报备类型
   * @returns {string} - 报备类型文本
   */
  getReportTypeText(type) {
    const typeMap = {
      1: '新客户报备',
      2: '老客户续单',
      3: '竞争客户转单'
    }
    return typeMap[type] || '未知类型'
  },

  /**
   * 获取审核状态文本
   * @param {number} status - 审核状态
   * @returns {string} - 审核状态文本
   */
  getAuditStatusText(status) {
    const statusMap = {
      1: '待审核',
      2: '审核通过',
      3: '审核驳回'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取审核状态颜色
   * @param {number} status - 审核状态
   * @returns {string} - 状态颜色
   */
  getAuditStatusColor(status) {
    const colorMap = {
      1: 'orange',
      2: 'green',
      3: 'red'
    }
    return colorMap[status] || 'gray'
  },

  /**
   * 格式化报备数据
   * @param {Object} report - 原始报备数据
   * @returns {Object} - 格式化后的报备数据
   */
  formatReportData(report) {
    return {
      ...report,
      // 添加文本字段
      reportTypeText: this.getReportTypeText(report.report_type),
      auditStatusText: this.getAuditStatusText(report.audit_status),

      // 格式化时间戳
      expectedOrderTime: report.expected_order_time,
      expectedShippingTime: report.expected_shipping_time,
      createdAt: report.created_at,

      // 格式化金额
      reportAmount: parseFloat(report.report_amount || 0),

      // 商品信息格式化
      items: (report.items || []).map(item => ({
        ...item,
        unitPrice: parseFloat(item.unit_price || 0),
        reportPrice: parseFloat(item.report_price || 0),
        subtotal: parseFloat(item.subtotal || 0),
        quantity: parseInt(item.quantity || 0)
      }))
    }
  },

  /**
   * 格式化金额显示
   * @param {number} amount - 金额
   * @returns {string} - 格式化后的金额字符串
   */
  formatAmount(amount) {
    if (!amount && amount !== 0) return '0.00'
    return parseFloat(amount).toFixed(2)
  },

  /**
   * 格式化日期显示
   * @param {string|number} timestamp - 时间戳
   * @returns {string} - 格式化后的日期字符串
   */
  formatDate(timestamp) {
    if (!timestamp) return '-'
    const date = new Date(parseInt(timestamp))
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  },

  /**
   * 格式化日期时间显示
   * @param {string|number} timestamp - 时间戳
   * @returns {string} - 格式化后的日期时间字符串
   */
  formatDateTime(timestamp) {
    if (!timestamp) return '-'

    // 处理不同格式的时间戳
    let time = timestamp

    // 如果是字符串，转换为数字
    if (typeof timestamp === 'string') {
      time = parseInt(timestamp)
    }

    // 如果是秒级时间戳（小于10位数），转换为毫秒级
    if (time < 10000000000) {
      time = time * 1000
    }

    const date = new Date(time)

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '-'
    }

    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}
