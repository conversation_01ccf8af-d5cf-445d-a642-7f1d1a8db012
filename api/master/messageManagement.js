/**
 * 管理员消息管理API
 */
import { request } from '~/utils/request'

// API基础URL
const apiBaseUrl = '/api'

export default {
  /**
   * 获取所有用户消息列表
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getAllUserMessages(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/message-management/messages`,
      method: 'GET',
      params
    })
  },

  /**
   * 获取消息统计信息
   * @returns {Promise} API响应
   */
  getMessageStatistics() {
    return request({
      url: `${apiBaseUrl}/v1/master/message-management/statistics`,
      method: 'GET'
    })
  },

  /**
   * 发送消息给特定用户
   * @param {Object} messageData - 消息数据
   * @returns {Promise} API响应
   */
  sendMessageToUser(messageData) {
    return request({
      url: `${apiBaseUrl}/v1/master/message-management/send-to-user`,
      method: 'POST',
      data: messageData
    })
  },

  /**
   * 批量发送消息给匹配条件的用户
   * @param {Object} messageData - 消息数据
   * @param {Object} userConditions - 用户筛选条件
   * @returns {Promise} API响应
   */
  sendBulkMessage(messageData, userConditions = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/message-management/send-bulk`,
      method: 'POST',
      data: {
        ...messageData,
        userConditions
      }
    })
  },

  /**
   * 删除消息
   * @param {string} messageId - 消息ID
   * @returns {Promise} API响应
   */
  deleteMessage(messageId) {
    return request({
      url: `${apiBaseUrl}/v1/master/message-management/messages/${messageId}`,
      method: 'DELETE'
    })
  },

  /**
   * 获取用户列表（用于发送消息时选择用户）
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getUserList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/message-management/users`,
      method: 'GET',
      params
    })
  },

  /**
   * 根据用户ID获取用户信息
   * @param {string} userId - 用户ID
   * @returns {Promise} API响应
   */
  getUserById(userId) {
    return request({
      url: `${apiBaseUrl}/v1/master/message-management/users/${userId}`,
      method: 'GET'
    })
  }
}
