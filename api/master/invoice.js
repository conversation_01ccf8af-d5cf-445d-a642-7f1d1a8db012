import { request } from '@/utils/request.js'

const apiBaseUrl = '/api/v1/master'

export default {
  /**
   * 为订单记录发票文件
   * @param {string} orderId - 订单ID
   * @param {Array} fileUrls - 文件URL数组
   * @returns {Promise} API 响应
   */
  async uploadInvoiceFiles(orderId, fileUrls) {
    // 将文件URL发送到发票服务进行记录
    return request({
      url: `${apiBaseUrl}/invoices/upload/${orderId}`,
      method: 'post',
      data: {
        fileUrls: fileUrls
      }
    })
  },

  /**
   * 查看订单的所有发票
   * @param {string} orderId - 订单ID
   * @returns {Promise} API 响应
   */
  async getOrderInvoices(orderId) {
    return request({
      url: `${apiBaseUrl}/invoices/view/${orderId}`,
      method: 'get'
    })
  }
}
