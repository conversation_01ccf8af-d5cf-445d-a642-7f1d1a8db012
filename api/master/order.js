import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 订单管理模块API
 */
export default {
  /**
   * 获取所有支付方式
   * @returns {Promise} - 返回所有可用的支付方式列表
   */
  getPaymentMethods() {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-methods`,
      method: 'get'
    })
  },

  /**
   * 获取订单列表
   * @param {Object} params - 查询参数，包含分页、筛选和排序信息
   * @returns {Promise} - 返回请求结果
   */
  getOrders(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders`,
      method: 'get',
      params
    })
  },

  /**
   * 获取订单详情
   * @param {string|number} id - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  getOrderDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建订单
   * @param {Object} data - 订单数据
   * @returns {Promise} - 返回请求结果
   */
  createOrder(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders`,
      method: 'post',
      data
    })
  },

  /**
   * 订单发货
   * @param {string|number} id - 订单ID
   * @param {Object} data - 发货信息数据
   * @returns {Promise} - 返回请求结果
   */
  shipOrder(id, data = {}) {
    console.log('调用发货API:', id, data);
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${id}/ship`,
      method: 'post',
      data
    })
  },

  /**
   * 取消订单
   * @param {string|number} id - 订单ID
   * @param {Object} data - 取消原因数据
   * @returns {Promise} - 返回请求结果
   */
  cancelOrder(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${id}/cancel`,
      method: 'post',
      data
    });
  },

  /**
   * 订单退款
   * @param {string|number} id - 订单ID
   * @param {Object} data - 退款数据
   * @returns {Promise} - 返回请求结果
   */
  refundOrder(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${id}/refund`,
      method: 'post',
      data
    });
  },

  /**
   * 确认收货
   * @param {string|number} id - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  confirmReceipt(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${id}/receipt`,
      method: 'post'
    })
  },

  /**
   * 获取订单跟单员列表
   * @param {string|number} id - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  getFollowers(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${id}/followers`,
      method: 'get'
    })
  },
 /**
   * 更新订单跟单员
   * @param {string|number} id - 订单ID
   * @param {Object} data - 操作相关数据
   * @returns {Promise} - 返回请求结果
   */
  setFollowers(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${id}/followers`,
      method: 'put',
      data
    })
  },
  /**
   * 订单统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getOrderStats(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/stats`,
      method: 'get',
      params
    })
  },

  /**
   * 导出订单数据
   * @param {Object} params - 导出参数，包含筛选条件
   * @returns {Promise} - 返回请求结果
   */
  exportOrders(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/export`,
      method: 'get',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 批量操作订单
   * @param {string} action - 操作类型，如'ship', 'cancel'
   * @param {Array} ids - 订单ID数组
   * @param {Object} data - 操作相关数据
   * @returns {Promise} - 返回请求结果
   */
  batchOrderAction(action, ids, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/batch/${action}`,
      method: 'post',
      data: {
        ids,
        ...data
      }
    })
  },
  /**
   * 获取订单角标统计
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getOrderBadgeStats(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/badge-stats`,
      method: 'get',
      params
    })
  },
  /**
   * 添加订单标注
   * @param {Object} data - 操作相关数据
   * @returns {Promise} - 返回请求结果
   */
  addOrderBadge(id,data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${id}/remark`,
      method: 'post',
      data
    })
  },
  /**
   * 获取订单包裹列表
   * @param {string|number} id - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  getOrderPackages(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-package/order/${orderId}`,
      method: 'get'
    })
  },
  /**
   * 订单发货（单、多包裹）
   * @param {Object} data - 操作相关数据
   * @returns {Promise} - 返回请求结果
   */
  shipOrderPackage(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-package/ship`,
      method: 'post',
      data
    })
  },

  /**
   * 获取渠道列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getChannelList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/platformManagement/channel`,
      method: 'get',
      params
    })
  },
  /**
 * 创建渠道
 * @param {Object} data - 操作相关数据
 * @returns {Promise} - 返回请求结果
 */
  createChannel(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/platformManagement/channel`,
      method: 'post',
      data
    })
  },
  /**
   * 获取渠道详情
   * @param {string|number} id - 渠道ID
   * @returns {Promise} - 返回请求结果
   */
  getChannelDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/platformManagement/channel/${id}`,
      method: 'get'
    })
  },
  /**
   * 更新渠道
   * @param {string|number} id - 渠道ID
   * @param {Object} data - 操作相关数据
   * @returns {Promise} - 返回请求结果
   */
  updateChannel(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/platformManagement/channel/${id}`,
      method: 'put',
      data
    })
  },
  /**
 * 删除渠道
 * @param {string|number} id - 渠道ID
 * @returns {Promise} - 返回请求结果
 */
  deleteChannel(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/platformManagement/channel/${id}`,
      method: 'delete'
    })
  },


  /**
   * 获取订单标注列表
   * @param {string|number} id - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  getOrderRemarkList(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-remark/list/${id}`,
      method: 'get'
    })
  },
  /**
   * 添加订单标注
   * @param {string|number} id - 订单ID
   * @param {Object} data - 操作相关数据
   * @returns {Promise} - 返回请求结果
   */
  addOrderRemark(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${id}/remark`,
      method: 'post',
      data
    })
  },

  addOrderManual(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/manual`,
      method: 'post',
      data
    })
  },

  getPaymentMethod() {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-methods`,
      method: 'get',
    })
  },

  confirmRefund(orderId) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-refund/${orderId}/confirm`,
      method: 'post',
    })
  },

  /**
   * 查询物流轨迹
   * @param {Object} data - 查询参数
   * @param {string} data.trackingNumber - 快递单号
   * @param {string} data.companyCode - 快递公司编码（可选）
   * @returns {Promise} - 返回请求结果
   */
  queryLogisticsTrack(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/orders/logistics/track`,
      method: 'post',
      data
    })
  },
  getAllPlatformManagement(){
    return request({
      url: `${apiBaseUrl}/v1/master/platformManagement/channel/tree/all`,
      method: 'get'
    })
  },
  /**
   * 根据订单号抓订单

   * @param {Object} data - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  callSpider(data = {}){
    return request({
      url: `${apiBaseUrl}/v1/spider/spiders/call`,
      method: 'post',
      data
    })
  },

 
}