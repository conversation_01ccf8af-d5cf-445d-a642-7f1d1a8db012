/**
 * Master端订单指派API
 */
import { request } from '~/utils/request'

// API基础URL
const apiBaseUrl = '/api'

export default {
  /**
   * 创建订单指派
   * @param {Object} data - 指派数据
   * @param {string} data.order_id - 订单ID
   * @param {string} data.order_report_id - 报备信息ID
   * @param {string} data.provider_id - 服务商ID
   * @param {string} data.salesman_id - 业务员ID
   * @param {number} data.rate - 费率
   * @param {number} data.assignment_amount - 指派金额
   * @param {string} data.remark - 备注
   * @returns {Promise} - 返回请求结果
   */
  createAssignment(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-assignment/create`,
      method: 'post',
      data
    })
  },

  /**
   * 获取订单指派列表
   * @param {Object} params - 查询参数，包含分页、筛选和排序信息
   * @returns {Promise} - 返回请求结果
   */
  getAssignmentList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-assignment/list`,
      method: 'get',
      params
    })
  },

  /**
   * 获取订单指派详情
   * @param {string|number} id - 指派记录ID
   * @returns {Promise} - 返回请求结果
   */
  getAssignmentDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/order-assignment/detail/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取指派状态文本
   * @param {number} status - 指派状态
   * @returns {string} - 指派状态文本
   */
  getAssignmentStatusText(status) {
    const statusMap = {
      1: '已指派',
      2: '已接受',
      3: '已拒绝',
      4: '已完成'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取指派状态颜色
   * @param {number} status - 指派状态
   * @returns {string} - 指派状态颜色
   */
  getAssignmentStatusColor(status) {
    const colorMap = {
      1: 'blue',      // 已指派
      2: 'green',     // 已接受
      3: 'red',       // 已拒绝
      4: 'gray'       // 已完成
    }
    return colorMap[status] || 'gray'
  },

  /**
   * 格式化指派数据
   * @param {Object} assignment - 原始指派数据
   * @returns {Object} - 格式化后的指派数据
   */
  formatAssignmentData(assignment) {
    if (!assignment) return null

    return {
      ...assignment,
      assignment_status_text: this.getAssignmentStatusText(assignment.assignment_status),
      assignment_status_color: this.getAssignmentStatusColor(assignment.assignment_status),
      rate_percent: assignment.rate ? (assignment.rate * 100).toFixed(2) + '%' : '0.00%',
      assignment_amount_formatted: assignment.assignment_amount ? 
        parseFloat(assignment.assignment_amount).toFixed(2) : '0.00',
      order_amount_formatted: assignment.order_amount ? 
        parseFloat(assignment.order_amount).toFixed(2) : '0.00',
      assigned_at_formatted: assignment.assigned_at ? 
        new Date(parseInt(assignment.assigned_at)).toLocaleString('zh-CN') : '',
      accepted_at_formatted: assignment.accepted_at ? 
        new Date(parseInt(assignment.accepted_at)).toLocaleString('zh-CN') : '',
      completed_at_formatted: assignment.completed_at ? 
        new Date(parseInt(assignment.completed_at)).toLocaleString('zh-CN') : ''
    }
  }
}
