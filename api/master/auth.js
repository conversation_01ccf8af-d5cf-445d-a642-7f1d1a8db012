import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

export default {
  /**
   * 获取RSA公钥
   * @returns {Promise} 返回包含公钥的Promise
   */
  getPublicKey() {
    return request({
      url: `${apiBaseUrl}/v1/common/public-key`,
      method: 'get'
    })
  },

  /**
   * 用户登录
   * @param {object} params - 登录参数
   * @param {string} params.username - 用户名
   * @param {string} params.password - 已加密的密码
   * @param {string} params.captcha - 验证码
   * @param {string} params.captchaId - 验证码ID
   * @param {boolean} [rawResponse=false] - 是否返回原始响应
   * @returns {Promise} 返回登录结果的Promise
   */
  login(params = {}, rawResponse = false) {
    return request({
      url: `${apiBaseUrl}/v1/master/auth/login`,
      method: 'post',
      data: params,
      rawResponse // 添加原始响应选项
    })
  },

  /**
   * 用户注销
   * @returns {Promise} 返回注销结果的Promise
   */
  logout() {
    return request({
      url: `${apiBaseUrl}/v1/master/auth/logout`,
      method: 'post'
    })
  },
  
  /**
   * 手机号登录
   * @param {object} params - 登录参数
   * @param {string} params.phone - 手机号
   * @param {string} params.code - 短信验证码
   * @param {string} params.type - 验证码类型，默认为login
   * @param {boolean} [rawResponse=false] - 是否返回原始响应
   * @returns {Promise} 返回登录结果的Promise
   */
  phoneLogin(params = {}, rawResponse = false) {
    return request({
      url: `${apiBaseUrl}/v1/master/auth/phone-login`,
      method: 'post',
      data: params,
      rawResponse // 添加原始响应选项
    })
  },
  
  /**
   * 获取短信模版
   * @returns {Promise} 返回发送结果的Promise
   */
  getSmsTemplates() {
    return request({
      url: `${apiBaseUrl}/v1/master/system/configure/open/sms/templates`,
      method: 'get'
    })
  },

  /**
   * 发送手机验证码
   * @param {object} params - 参数
   * @param {string} params.phone - 手机号
   * @param {string} params.captcha - 图形验证码
   * @param {string} params.captchaId - 验证码ID
   * @param {string} params.type - 验证码类型，默认为login
   * @returns {Promise} 返回发送结果的Promise
   */
  sendSmsCode(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/system/integration/aliyun/sms/verification-code`,
      method: 'post',
      data: params
    })
  }
}
