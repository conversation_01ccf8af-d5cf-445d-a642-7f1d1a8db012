import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 支付配置管理API
 */
export default {
  /**
   * 获取所有支付配置
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config`,
      method: 'get',
      params
    })
  },

  /**
   * 根据支付类型获取配置
   * @param {string} type - 支付类型 (wechat, alipay, unionpay)
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getByType(type, params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/type/${type}`,
      method: 'get',
      params
    })
  },

  /**
   * 获取默认配置
   * @param {string} type - 支付类型 (wechat, alipay, unionpay)
   * @returns {Promise} - 返回请求结果
   */
  getDefault(type) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/default/${type}`,
      method: 'get'
    })
  },

  /**
   * 根据ID获取配置详情
   * @param {string|number} id - 配置ID
   * @returns {Promise} - 返回请求结果
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/${id}`,
      method: 'get'
    })
  },

  /**
   * 保存微信支付配置
   * @param {Object} data - 配置数据
   * @returns {Promise} - 返回请求结果
   */
  saveWechatConfig(data) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/wechat`,
      method: 'post',
      data
    })
  },

  /**
   * 保存支付宝配置
   * @param {Object} data - 配置数据
   * @returns {Promise} - 返回请求结果
   */
  saveAlipayConfig(data) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/alipay`,
      method: 'post',
      data
    })
  },

  /**
   * 更新配置
   * @param {string|number} id - 配置ID
   * @param {Object} data - 配置数据
   * @returns {Promise} - 返回请求结果
   */
  update(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 设置默认配置
   * @param {string|number} id - 配置ID
   * @returns {Promise} - 返回请求结果
   */
  setDefault(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/${id}/default`,
      method: 'put'
    })
  },

  /**
   * 删除配置
   * @param {string|number} id - 配置ID
   * @returns {Promise} - 返回请求结果
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/${id}`,
      method: 'delete'
    })
  },

  /**
   * 获取支付类型统计
   * @returns {Promise} - 返回请求结果
   */
  getStats() {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/stats`,
      method: 'get'
    })
  },

  /**
   * 测试配置连通性
   * @param {string|number} id - 配置ID
   * @returns {Promise} - 返回请求结果
   */
  testConfig(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/payment-config/${id}/test`,
      method: 'post'
    })
  }
}
