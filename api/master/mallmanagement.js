import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 系统管理模块API
 */
export default {
  /**
   * 用户管理相关接口
   */
  user: {
    /**
     * 获取用户列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user`,
        method: 'get',
        params
      })
    },

    /**
     * 获取用户详情
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建用户
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user`,
        method: 'post',
        data
      })
    },

    /**
     * 更新用户
     * @param {string|number} id - 用户ID
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除用户
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user/${id}`,
        method: 'delete'
      })
    },


  },

   /**
   * 用户管理相关接口
   */
   level: {
    /**
     * 获取用户列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-level`,
        method: 'get',
        params
      })
    },

    /**
     * 获取用户详情
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-level/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建用户
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-level`,
        method: 'post',
        data
      })
    },

    /**
     * 更新用户
     * @param {string|number} id - 用户ID
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-level/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除用户
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-level/${id}`,
        method: 'delete'
      })
    },


  },

  /**
   * 部门管理相关接口
   */
  dept: {
    /**
     * 获取部门列表
     * @param {Object} params - 查询参数
     * @returns {Promise<any>} 返回部门列表的Promise
     */
    getDeptList(params) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept`,
        method: 'get',
        params
      })
    },

    /**
     * 获取部门详情
     * @param {number|string} id - 部门ID
     * @returns {Promise<any>} 返回部门详情的Promise
     */
    getDeptDetail(id) {
      return request({
        url: `${apiBaseUrl}/master/system/dept/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建部门
     * @param {Object} data - 部门数据
     * @returns {Promise<any>} 返回创建结果的Promise
     */
    createDept(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept`,
        method: 'post',
        data
      })
    },

    /**
     * 更新部门
     * @param {number|string} id - 部门ID
     * @param {Object} data - 部门数据
     * @returns {Promise<any>} 返回更新结果的Promise
     */
    updateDept(id, data) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除部门
     * @param {number|string} id - 部门ID
     * @returns {Promise<any>} 返回删除结果的Promise
     */
    deleteDept(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept/${id}`,
        method: 'delete'
      })
    },
  },

  /**
   * 角色管理相关接口
   */
  role: {
    /**
     * 获取角色列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role`,
        method: 'get',
        params
      })
    },

    /**
     * 获取角色详情
     * @param {string|number} id - 角色ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建角色
     * @param {Object} data - 角色数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role`,
        method: 'post',
        data
      })
    },

    /**
     * 更新角色
     * @param {string|number} id - 角色ID
     * @param {Object} data - 角色数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除角色
     * @param {string|number} id - 角色ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 轮播图管理相关接口
   */
  banner: {
    /**
     * 获取轮播图列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/banner`,
        method: 'get',
        params
      })
    },

    /**
     * 获取轮播图详情
     * @param {string|number} id - 轮播图ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/banner/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建轮播图
     * @param {Object} data - 轮播图数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/banner`,
        method: 'post',
        data
      })
    },

    /**
     * 更新轮播图
     * @param {string|number} id - 轮播图ID
     * @param {Object} data - 轮播图数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/banner/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除轮播图
     * @param {string|number} id - 轮播图ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/banner/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 菜单管理相关接口
   */
  menu: {
    /**
     * 获取菜单列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu`,
        method: 'get',
        params
      })
    },

    /**
     * 获取菜单详情
     * @param {string|number} id - 菜单ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建菜单
     * @param {Object} data - 菜单数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu`,
        method: 'post',
        data
      })
    },

    /**
     * 更新菜单
     * @param {string|number} id - 菜单ID
     * @param {Object} data - 菜单数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除菜单
     * @param {string|number} id - 菜单ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/${id}`,
        method: 'delete'
      })
    },
    /**
     * 获取菜单树形结构
     * @param {string|number} 
     * @returns {Promise} - 返回请求结果
     */
    tree(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/tree`,
        method: 'get',
        params
      })
    },
  
  },

  /**
   * 用户实名认证相关接口
   */
  realnameAuth: {
    /**
     * 获取用户实名认证信息
     * @param {string} userId 用户ID
     * @returns {Promise} 请求结果
     */
    getInfo: (userId) => {
      return request({
        url: `/api/v1/master/mall/user/realname-auth/${userId}`,
        method: 'get'
      });
    },



    /**
     * 审核通过用户实名认证
     * @param {string} userId 用户ID
     * @returns {Promise} 请求结果
     */
    approve: (userId) => {
      return request({
        url: `/api/v1/master/mall/user/realname-auth/approve/${userId}`,
        method: 'post'
      });
    },

    /**
     * 拒绝用户实名认证
     * @param {string} userId 用户ID
     * @param {string} reason 拒绝原因
     * @returns {Promise} 请求结果
     */
    reject: (userId, reason) => {
      return request({
        url: `/api/v1/master/mall/user/realname-auth/reject/${userId}`,
        method: 'post',
        data: { reason }
      });
    }
  },

  /**
 * 系统配置相关接口
 */
  configuration: {  
    /**
     * 获取所有配置类型
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getTypes(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/types`,
        method: 'get',
        params
      })
    },
    /**
     * 获取指定类型的配置对象
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    objectTypes(type,params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/object/${type}`,
        method: 'get',
        params
      })
    },
   /**
     * 更新指定配置
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    update(type,key,params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/${type}/${key}`,
        method: 'put',
        data: params
      })
    },
  },
  /**
 * 操作日志管理相关接口
 */
  operLog: {
    /**
     * 获取操作日志列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs`,
        method: 'get',
        params
      })
    },
    /**
     * 获取操作日志详情
     * @param {string|number} id - 操作日志ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs/${id}`,
        method: 'get'
      })
    },
    /**
     * 删除操作日志
     * @param {string|number} id - 操作日志ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs/${id}`,
        method: 'delete'
      })
    },
    /**
     * 清空操作日志
     * @returns {Promise} - 返回请求结果
     */
    clear() {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs/clear`,
        method: 'delete'
      })
    },
  },

  /**
   * 会员标签组管理相关接口
   */
  tagGroup: {
    /**
     * 获取标签组列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag-group`,
        method: 'get',
        params
      })
    },

    /**
     * 获取标签组详情
     * @param {string|number} id - 标签组ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag-group/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建标签组
     * @param {Object} data - 标签组数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag-group`,
        method: 'post',
        data
      })
    },

    /**
     * 更新标签组
     * @param {string|number} id - 标签组ID
     * @param {Object} data - 标签组数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag-group/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除标签组
     * @param {string|number} id - 标签组ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag-group/${id}`,
        method: 'delete'
      })
    },

    /**
     * 获取所有启用的标签组（用于下拉选择）
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getAllEnabled(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag-group/options/enabled`,
        method: 'get',
        params
      })
    }
  },

  /**
   * 会员标签管理相关接口
   */
  tag: {
    /**
     * 获取标签列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag`,
        method: 'get',
        params
      })
    },

    /**
     * 获取标签详情
     * @param {string|number} id - 标签ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建标签
     * @param {Object} data - 标签数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag`,
        method: 'post',
        data
      })
    },

    /**
     * 更新标签
     * @param {string|number} id - 标签ID
     * @param {Object} data - 标签数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除标签
     * @param {string|number} id - 标签ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag/${id}`,
        method: 'delete'
      })
    },

    /**
     * 获取特定标签组下的所有标签
     * @param {string|number} groupId - 标签组ID
     * @returns {Promise} - 返回请求结果
     */
    getTagsByGroupId(groupId) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag/group/${groupId}`,
        method: 'get'
      })
    },

    /**
     * 获取所有启用的标签（用于下拉选择）
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getAllEnabled(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag/options/enabled`,
        method: 'get',
        params
      })
    },

    /**
     * 批量为用户添加标签
     * @param {Object} data - 包含userId和tagIds的对象
     * @returns {Promise} - 返回请求结果
     */
    addTagsToUser(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag/user/add`,
        method: 'post',
        data
      })
    },

    /**
     * 批量为用户移除标签
     * @param {Object} data - 包含userId和tagIds的对象
     * @returns {Promise} - 返回请求结果
     */
    removeTagsFromUser(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag/user/remove`,
        method: 'post',
        data
      })
    },

    /**
     * 获取用户的所有标签
     * @param {string|number} userId - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    getUserTags(userId) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/user-tag/user/${userId}`,
        method: 'get'
      })
    }
  },
}
