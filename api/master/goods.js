import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 商品管理模块API
 */
export default {
  /**
   * 商品SPU相关接口
   */
  spu: {
    /**
     * 获取商品列表
     * @param {Object} params - 查询参数，包含分页、筛选和排序信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu`,
        method: 'get',
        params:{isDeleted:0,...params}
      })
    },
    getRetrieveList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu`,
        method: 'get',
        params:{isDeleted:1,...params}
      })
    },
    /**
     * 获取商品详情
     * @param {string|number} id - 商品ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建商品
     * @param {Object} data - 商品数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu`,
        method: 'post',
        data
      })
    },

    /**
     * 更新商品
     * @param {string|number} id - 商品ID
     * @param {Object} data - 商品数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除商品
     * @param {string|number} id - 商品ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu/${id}/soft`,
        method: 'delete'
      })
    },
    deleteReal(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu/${id}/permanent`,
        method: 'delete'
      })
    },
    recover(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu/${id}/restore`,
        method: 'post'
      })
    },
    /**
     * 商品上架
     * @param {string|number} id - 商品ID
     * @returns {Promise} - 返回请求结果
     */
    putOnShelf(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu/${id}/on-shelf`,
        method: 'patch'
      })
    },

    /**
     * 商品下架
     * @param {string|number} id - 商品ID
     * @returns {Promise} - 返回请求结果
     */
    putOffShelf(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-spu/${id}/off-shelf`,
        method: 'patch'
      })
    },

    /**
     * 获取商品分类属性模板
     * @param {string|number} id - 商品分类ID
     * @returns {Promise} - 返回请求结果
     */
    getCategoryTemplate(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-category/${id}/attribute-template`,
        method: "get",
      });
    },

    /**
     * 保存草稿箱
     * @param {string|number} id - 商品分类ID
     * @returns {Promise} - 返回请求结果
     */
    goodsDraft(data={}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-draft`,
        method: 'post',
        data
      });
    },

    /**
     * 保存草稿箱
     * @param {string|number} id - 商品分类ID
     * @returns {Promise} - 返回请求结果
     */
    goodsDraftLatest() {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-draft/latest`,
        method: "get",
      });
    },

    /**
     * 删除草稿箱
     * @param {string|number} id - 商品分类ID
     * @returns {Promise} - 返回请求结果
     */
    deleteDraft(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-draft/${id}`,
        method: "delete",
      });
    },
  },

  /**
   * 商品分类相关接口
   */
  category: {
    /**
     * 获取商品分类列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-category`,
        method: 'get',
        params
      })
    },

    /**
     * 获取商品分类详情
     * @param {string|number} id - 分类ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-category/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建商品分类
     * @param {Object} data - 分类数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-category`,
        method: 'post',
        data
      })
    },

    /**
     * 更新商品分类
     * @param {string|number} id - 分类ID
     * @param {Object} data - 分类数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-category/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除商品分类
     * @param {string|number} id - 分类ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-category/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 商品品牌相关接口
   */
  brand: {
    /**
     * 获取商品品牌列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-brand`,
        method: 'get',
        params
      })
    },

    /**
     * 获取商品品牌详情
     * @param {string|number} id - 品牌ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-brand/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建商品品牌
     * @param {Object} data - 品牌数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-brand`,
        method: 'post',
        data
      })
    },

    /**
     * 更新商品品牌
     * @param {string|number} id - 品牌ID
     * @param {Object} data - 品牌数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-brand/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除商品品牌
     * @param {string|number} id - 品牌ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-brand/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 商品标签相关接口
   */
  tag: {
    /**
     * 获取商品标签列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-tag`,
        method: 'get',
        params
      })
    },

    /**
     * 获取商品标签详情
     * @param {string|number} id - 标签ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-tag/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建商品标签
     * @param {Object} data - 标签数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-tag`,
        method: 'post',
        data
      })
    },

    /**
     * 更新商品标签
     * @param {string|number} id - 标签ID
     * @param {Object} data - 标签数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-tag/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除商品标签
     * @param {string|number} id - 标签ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-tag/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 商品服务相关接口
   */
  service: {
    /**
     * 获取商品服务列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-service`,
        method: 'get',
        params
      })
    },

    /**
     * 获取商品服务详情
     * @param {string|number} id - 服务ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-service/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建商品服务
     * @param {Object} data - 服务数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-service`,
        method: 'post',
        data
      })
    },

    /**
     * 更新商品服务
     * @param {string|number} id - 服务ID
     * @param {Object} data - 服务数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-service/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除商品服务
     * @param {string|number} id - 服务ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-service/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 商品属性模板相关接口
   */
  attrTemplate: {
    /**
     * 获取属性模板列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-attr-template`,
        method: 'get',
        params
      })
    },

    /**
     * 获取属性模板详情
     * @param {string|number} id - 模板ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-attr-template/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建属性模板
     * @param {Object} data - 模板数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-attr-template`,
        method: 'post',
        data
      })
    },

    /**
     * 更新属性模板
     * @param {string|number} id - 模板ID
     * @param {Object} data - 模板数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-attr-template/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除属性模板
     * @param {string|number} id - 模板ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-attr-template/${id}`,
        method: 'delete'
      })
    },

    /**
     * 获取属性模板的所有参数
     * @param {string|number} id - 模板ID
     * @returns {Promise} - 返回请求结果
     */
    getParamList(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-attr-template/${id}/param`,
        method: 'get'
      })
    },

    /**
     * 获取属性模板的所有参数
     * @param {string|number} id - 模板ID
     * @returns {Promise} - 返回请求结果
     */
    addParam(id,data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-attr-template/${id}/param`,
        method: 'post',
        data
      })
    },

    /**
     * 更新属性模板参数
     * @param {string|number} id - 模板ID
     * @returns {Promise} - 返回请求结果
     */
    updateParam(id,data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-attr-template/param/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除属性模板参数
     * @param {string|number} id - 模板ID
     * @returns {Promise} - 返回请求结果
     */
    deleteParam(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-attr-template/param/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 运费模板相关接口
   */
  freightTemplate: {
    /**
     * 获取运费模板列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-freight-template`,
        method: 'get',
        params
      })
    },

    /**
     * 获取运费模板详情
     * @param {string|number} id - 模板ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-freight-template/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建运费模板
     * @param {Object} data - 模板数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-freight-template`,
        method: 'post',
        data
      })
    },

    /**
     * 更新运费模板
     * @param {string|number} id - 模板ID
     * @param {Object} data - 模板数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-freight-template/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除运费模板
     * @param {string|number} id - 模板ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/goods-freight-template/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 批量查询SKU信息
   * @param {Object} data - 商品skuIds数组
   * @returns {Promise} - 返回请求结果
   */
  goods_sku_batch(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/goods-sku/batch`,
      method: 'post',
      data
    })
  },
}
