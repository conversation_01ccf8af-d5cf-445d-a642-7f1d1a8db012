import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 系统管理模块API
 */
export default {
  /**
   * 用户管理相关接口
   */
  user: {
    /**
     * 获取用户列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user`,
        method: 'get',
        params
      })
    },

    /**
     * 获取用户详情
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建用户
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user`,
        method: 'post',
        data
      })
    },

    /**
     * 更新用户
     * @param {string|number} id - 用户ID
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除用户
     * @param {string|number} id - 用户ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/user/${id}`,
        method: 'delete'
      })
    },


  },

  /**
   * 部门管理相关接口
   */
  dept: {
    /**
     * 获取部门列表
     * @param {Object} params - 查询参数
     * @returns {Promise<any>} 返回部门列表的Promise
     */
    getDeptList(params) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept`,
        method: 'get',
        params
      })
    },

    /**
     * 获取部门详情
     * @param {number|string} id - 部门ID
     * @returns {Promise<any>} 返回部门详情的Promise
     */
    getDeptDetail(id) {
      return request({
        url: `${apiBaseUrl}/master/system/dept/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建部门
     * @param {Object} data - 部门数据
     * @returns {Promise<any>} 返回创建结果的Promise
     */
    createDept(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept`,
        method: 'post',
        data
      })
    },

    /**
     * 更新部门
     * @param {number|string} id - 部门ID
     * @param {Object} data - 部门数据
     * @returns {Promise<any>} 返回更新结果的Promise
     */
    updateDept(id, data) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除部门
     * @param {number|string} id - 部门ID
     * @returns {Promise<any>} 返回删除结果的Promise
     */
    deleteDept(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/dept/${id}`,
        method: 'delete'
      })
    },
  },

  /**
   * 角色管理相关接口
   */
  role: {
    /**
     * 获取角色列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role`,
        method: 'get',
        params
      })
    },

    /**
     * 获取角色详情
     * @param {string|number} id - 角色ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建角色
     * @param {Object} data - 角色数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role`,
        method: 'post',
        data
      })
    },

    /**
     * 更新角色
     * @param {string|number} id - 角色ID
     * @param {Object} data - 角色数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除角色
     * @param {string|number} id - 角色ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/role/${id}`,
        method: 'delete'
      })
    }
  },

  /**
   * 菜单管理相关接口
   */
  menu: {
    /**
     * 获取菜单列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu`,
        method: 'get',
        params
      })
    },

    /**
     * 获取菜单详情
     * @param {string|number} id - 菜单ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建菜单
     * @param {Object} data - 菜单数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu`,
        method: 'post',
        data
      })
    },

    /**
     * 更新菜单
     * @param {string|number} id - 菜单ID
     * @param {Object} data - 菜单数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除菜单
     * @param {string|number} id - 菜单ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/${id}`,
        method: 'delete'
      })
    },
    /**
     * 获取菜单树形结构
     * @param {string|number} 
     * @returns {Promise} - 返回请求结果
     */
    tree(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/menu/tree`,
        method: 'get',
        params
      })
    },
  
  },

  /**
 * 系统配置相关接口
 */
  configuration: {  
    /**
     * 获取所有配置类型
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getTypes(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/types`,
        method: 'get',
        params
      })
    },
    /**
     * 获取指定类型的配置对象
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    objectTypes(type,params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/object/${type}`,
        method: 'get',
        params
      })
    },
     /**
     * 获取指定类型的所有配置
     * @param {Object} params - 查询参数
    * @returns {Promise} - 返回请求结果
    */
    configure(type,params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/${type}`,
        method: 'get',
        params
      })
    },
   /**
     * 更新指定配置
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    update(type,key,params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/configure/${type}/${key}`,
        method: 'put',
        data: params
      })
    },
  },
  /**
 * 操作日志管理相关接口
 */
  operLog: {
    /**
     * 获取操作日志列表
     * @param {Object} params - 查询参数，包含分页信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs`,
        method: 'get',
        params
      })
    },
    /**
     * 获取操作日志详情
     * @param {string|number} id - 操作日志ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs/${id}`,
        method: 'get'
      })
    },
    /**
     * 删除操作日志
     * @param {string|number} id - 操作日志ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs/${id}`,
        method: 'delete'
      })
    },
    /**
     * 清空操作日志
     * @returns {Promise} - 返回请求结果
     */
    clear() {
      return request({
        url: `${apiBaseUrl}/v1/master/system/logs/clear`,
        method: 'delete'
      })
    },
  },

  /**
   * 测试物流查询
   * @param {Object} data - 测试数据
   * @returns {Promise} - 返回请求结果
   */
  testLogisticsQuery(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/system/integration/express100/test`,
      method: 'post',
      data
    })
  },

  /**
   * 获取快递公司列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getExpressCompanies(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/express-company-code`,
      method: 'get',
      params
    })
  },
  
  /**
   * 获取爬虫任务列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getSpiderTasks(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/spider/spiders`,
      method: 'get',
      params
    })
  },
  /**
   * 创建爬虫任务
   * @param {Object} data - 爬虫任务数据
   * @returns {Promise} - 返回请求结果
   */
  createSpiderTask(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/spider/spiders`,
      method: 'post',
      data
    })
  },
  /**
   * 获取爬虫任务详情
   * @param {string|number} id - 爬虫任务ID
   * @returns {Promise} - 返回请求结果
   */
  getSpiderTaskById(id) {
    return request({
      url: `${apiBaseUrl}/v1/spider/spiders/${id}`,
      method: 'get'
    })
  },
  /**
   * 更新爬虫任务
   * @param {string|number} id - 爬虫任务ID
   * @param {Object} data - 爬虫任务数据
   * @returns {Promise} - 返回请求结果
   */
  updateSpiderTask(id, data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/spider/spiders/${id}`,
      method: 'put',
      data
    })
  },
  /**
   * 删除爬虫任务
   * @param {string|number} id - 爬虫任务ID
   * @returns {Promise} - 返回请求结果
   */
  deleteSpiderTask(id) {
    return request({
      url: `${apiBaseUrl}/v1/spider/spiders/${id}`,
      method: 'delete'
    })
  },
}
