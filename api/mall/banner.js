import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 商城轮播图模块API
 */
export default {
  /**
   * 获取轮播图列表
   * @param {Object} params - 查询参数
   * @param {number} params.limit - 获取轮播图数量，默认5个
   * @returns {Promise} - 返回请求结果
   */
  getBanners(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/banners`,
      method: 'get',
      params
    })
  }
}
