import userApi from './user'
import cartApi from './cart'
import addressApi from './address'
import orderApi from './order'
import goodsApi from './goods'
import categoryApi from './category'
import wechatApi from './wechat'
import reviewApi from './review'
import browseHistoryApi from './browseHistory'
import favoriteApi from './favorite'
import newsApi from './news'
import invoiceHeaderApi from './invoiceHeader'
/**
 * 商城模块API集合
 */
export default {
  user: userApi,
  cart: cartApi,
  address: addressApi,
  order: orderApi,
  goods: goodsApi,
  category: categoryApi,
  wechat: wechatApi,
  review: reviewApi,
  browseHistory: browseHistoryApi,
  favorite: favoriteApi,
  news: newsApi,
  invoiceHeader: invoiceHeaderApi
}
