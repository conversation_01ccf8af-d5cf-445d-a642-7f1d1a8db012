import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

// 获取商城用户token
const getMallUserToken = () => {
  // 优先从localStorage获取
  const token = localStorage.getItem('mall_user_token') || sessionStorage.getItem('mall_user_token')
  return token
}

/**
 * 商城用户浏览记录模块API
 */
export default {
  /**
   * 添加浏览记录
   * @param {Object} data - 浏览记录数据
   * @param {String} data.goodsSpuId - 商品SPU ID
   * @param {String} data.goodsSkuId - 商品SKU ID（可选）
   * @returns {Promise} - 返回请求结果
   */
  addBrowseHistory(data = {}) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/browse-history`,
      method: 'post',
      data,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 获取浏览记录列表
   * @param {Object} params - 查询参数
   * @param {Number} params.page - 页码，默认1
   * @param {Number} params.pageSize - 每页数量，默认20
   * @param {Number} params.limit - 限制数量（用于获取最近几条记录）
   * @returns {Promise} - 返回请求结果
   */
  getBrowseHistory(params = {}) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/browse-history`,
      method: 'get',
      params,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 删除单个浏览记录
   * @param {String} goodsSpuId - 商品SPU ID
   * @returns {Promise} - 返回请求结果
   */
  removeBrowseHistory(goodsSpuId) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/browse-history/${goodsSpuId}`,
      method: 'delete',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 批量删除浏览记录
   * @param {Array<String>} goodsSpuIds - 商品SPU ID数组
   * @returns {Promise} - 返回请求结果
   */
  removeBrowseHistoryBatch(goodsSpuIds) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/browse-history/batch`,
      method: 'delete',
      data: { goodsSpuIds },
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 清空所有浏览记录
   * @returns {Promise} - 返回请求结果
   */
  clearBrowseHistory() {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/browse-history/clear`,
      method: 'delete',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 获取浏览记录统计
   * @returns {Promise} - 返回请求结果
   */
  getBrowseHistoryStats() {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/browse-history/stats`,
      method: 'get',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  }
}
