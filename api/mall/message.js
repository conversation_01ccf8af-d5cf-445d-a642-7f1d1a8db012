import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

// 获取商城用户token
const getMallUserToken = () => {
  // 优先从localStorage获取
  const token = localStorage.getItem('mall_user_token') || sessionStorage.getItem('mall_user_token')
  return token
}

/**
 * 商城用户消息模块API
 */
export default {
  /**
   * 获取用户消息列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getMessageList(params = {}) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/user/messages`,
      method: 'get',
      params,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 获取未读消息数量
   * @returns {Promise} - 返回请求结果
   */
  getUnreadCount() {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/user/messages/unread-count`,
      method: 'get',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 标记消息为已读
   * @param {string} messageId - 消息ID
   * @returns {Promise} - 返回请求结果
   */
  markAsRead(messageId) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/user/messages/${messageId}/read`,
      method: 'put',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 批量标记消息为已读
   * @param {Array} messageIds - 消息ID数组
   * @returns {Promise} - 返回请求结果
   */
  batchMarkAsRead(messageIds) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/user/messages/batch-read`,
      method: 'put',
      data: { messageIds },
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 删除消息
   * @param {string} messageId - 消息ID
   * @returns {Promise} - 返回请求结果
   */
  deleteMessage(messageId) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/user/messages/${messageId}`,
      method: 'delete',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 清空所有消息
   * @returns {Promise} - 返回请求结果
   */
  clearAllMessages() {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/user/messages/clear-all`,
      method: 'delete',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 创建消息（管理员功能）
   * @param {Object} data - 消息数据
   * @returns {Promise} - 返回请求结果
   */
  createMessage(data = {}) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/user/messages`,
      method: 'post',
      data,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  }
}
