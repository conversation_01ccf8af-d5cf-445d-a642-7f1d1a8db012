import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 商城商品分类模块API
 */
export default {
  /**
   * 获取商品分类树形结构
   * @returns {Promise} - 返回请求结果
   */
  getCategoryTree() {
    return request({
      url: `${apiBaseUrl}/v1/mall/goods/category/tree`,
      method: 'get'
    })
  },
  /**
   * 获取商品分类详情
   * @Param {id} -分类id
   * @returns {Promise} - 返回请求结果
   */
  getCategoryInfoById(id) {
    return request({
      url: `${apiBaseUrl}/v1/mall/goods/category/${id}`,
      method: 'get'
    })
  },


  /**
   * 获取商品分类标签
   * @param {Object} params - 请求参数
   * @param {Number} params.page - 页码
   * @param {Number} params.pageSize - 每页数量
   * @returns {Promise} - 返回请求结果
   */
  getCategoryTags(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/goods/tags`,
      method: 'get',
      params
    })
  },

  /**
   * 获取商品品牌列表
   * @param {Object} params - 请求参数
   * @param {Number} params.page - 页码
   * @param {Number} params.pageSize - 每页数量
   * @returns {Promise} - 返回请求结果
   */
  getBrands(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/goods/brands`,
      method: 'get',
      params
    })
  },
  
  /**
   * 获取商品服务列表
   * @param {Object} params - 请求参数
   * @param {Number} params.page - 页码，默认为1
   * @param {Number} params.pageSize - 每页数量，默认为10，最大为100
   * @returns {Promise} - 返回请求结果
   */
  getServices(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/goods/services`,
      method: 'get',
      params
    })
  }
}
