import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

// 获取商城用户token
const getMallUserToken = () => {
  // 优先从localStorage获取
  const token = localStorage.getItem('mall_user_token') || sessionStorage.getItem('mall_user_token')
  return token
}

/**
 * 商城商品模块API
 */
export default {
  /**
   * 商品操作相关接口
   */
    /**
     * 查询商品列表
     * @param {Object} params - 查询参数
     * @param {string} params.keyword - 搜索关键词
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @param {string} params.categoryId - 分类ID
     * @param {string} params.sortField - 排序字段
     * @param {string} params.sortOrder - 排序方式
     * @returns {Promise} - 返回请求结果
     */
    queryGoods(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/goods/products`,
        method: 'get',
        params
      })
    },
    
    /**
     * 获取商品详情
     * @param {string} id - 商品ID
     * @returns {Promise} - 返回请求结果
     */
    getGoodsDetail(id) {
      return request({
        url: `${apiBaseUrl}/v1/mall/goods/product/${id}`,
        method: 'get',
      })
    },

    /**
     * 获取精选商品列表
     * @param {Object} params - 查询参数
     * @param {number} params.count - 获取商品数量
     * @param {boolean} params.random - 是否随机获取
     * @returns {Promise} - 返回请求结果
     */
    getFeaturedProducts(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/goods/featured-products`,
        method: 'get',
        params
      })
    },

    /**
     * 获取热销品牌列表
     * @param {Object} params - 查询参数
     * @param {number} params.count - 获取品牌数量
     * @param {boolean} params.random - 是否随机获取
     * @returns {Promise} - 返回请求结果
     */
    getFeaturedBrands(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/goods/featured-brands`,
        method: 'get',
        params
      })
    },

    /**
     * 计算单个产品的运费
     * @param {Object} data - 运费计算所需数据
     * @param {string} data.productId - 产品ID
     * @param {number} data.quantity - 产品数量
     * @param {string} data.addressId - 收货地址ID
     * @param {Array} data.attributes - 产品属性（可选）
     * @returns {Promise} - 返回运费计算结果
     */
    calculateSingleProductFreight(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/freight-calculation/single-product`,
        method: 'post',
        data
      })
    },

    /**
     * 计算批量商品的运费合计
     * @param {Object} data - 运费计算所需数据
     * @param {Array} data.items - 商品项数组
     * @param {string} data.items[].productId - 产品ID
     * @param {number} data.items[].quantity - 产品数量
     * @param {Array} data.items[].attributes - 产品属性（可选）
     * @param {string} data.addressId - 收货地址ID
     * @returns {Promise} - 返回批量运费计算结果
     */
    calculateBatchProductsFreight(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/freight-calculation/batch`,
        method: 'post',
        data
      })
    }
}
