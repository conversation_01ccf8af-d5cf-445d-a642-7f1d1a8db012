import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

// 获取商城用户token
const getMallUserToken = () => {
  // 优先从localStorage获取
  const token = localStorage.getItem('mall_user_token') || sessionStorage.getItem('mall_user_token')
  return token
}

/**
 * 商城用户地址模块API
 */
export default {
  /**
   * 地址操作相关接口
   */
  address: {
    /**
     * 获取用户地址列表
     * @returns {Promise} - 返回请求结果
     */
    getList() {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/addresses`,
        method: 'get',
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },

    /**
     * 创建新地址
     * @param {Object} data - 地址数据
     * @returns {Promise} - 返回请求结果
     */
    create(data = {}) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/addresses`,
        method: 'post',
        data,
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },

    /**
     * 更新地址
     * @param {String|Number} id - 地址ID
     * @param {Object} data - 地址数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data = {}) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/addresses/${id}`,
        method: 'put',
        data,
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },

    /**
     * 删除地址
     * @param {String|Number} id - 地址ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/addresses/${id}`,
        method: 'delete',
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },

    /**
     * 设置默认地址
     * @param {String|Number} id - 地址ID
     * @returns {Promise} - 返回请求结果
     */
    setDefault(id) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/addresses/${id}/default`,
        method: 'patch',
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },

    /**
     * 获取省市区数据
     * @returns {Promise} - 返回请求结果
     */
    getRegionData() {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/master/region/tree`,
        method: 'get',
        params: {
          excludeStreet: true
        },
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    }
  },
  
  /**
   * IP定位相关接口
   */
  location: {
    /**
     * 获取IP定位信息
     * @returns {Promise} - 返回请求结果，包含IP定位信息
     */
    getIpLocation() {
      return request({
        url: `${apiBaseUrl}/v1/mall/ip/location`,
        method: 'get'
      })
    }
  }
}
