import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'


// 获取商城用户token
const getMallUserToken = () => {
  // 优先从localStorage获取
  const token = localStorage.getItem('mall_user_token') || sessionStorage.getItem('mall_user_token')
  return token
}

/**
 * 商城用户模块API
 */
export default {
  /**
   * 用户认证相关接口
   */
  auth: {
    /**
     * 用户登录
     * @param {Object} data - 登录数据，包含用户名和密码
     * @returns {Promise} - 返回请求结果
     */
    login(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/login`,
        method: 'post',
        data
      })
    },

    /**
     * 用户注册
     * @param {Object} data - 注册数据
     * @returns {Promise} - 返回请求结果
     */
    register(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/register`,
        method: 'post',
        data
      })
    },

    /**
     * 获取验证码
     * @param {String} phone - 手机号
     * @returns {Promise} - 返回请求结果
     */
    getCaptcha(phone) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/captcha`,
        method: 'get',
        params: { phone }
      })
    },



    /**
     * 发送短信验证码（通用方法）
     * @param {String} phone - 手机号
     * @param {String} type - 验证码类型，默认为'login'
     * @returns {Promise} - 返回请求结果
     */
    sendSmsVerificationCode(phone, type = 'login') {
      return request({
        url: `${apiBaseUrl}/v1/master/system/integration/aliyun/sms/verification-code`,
        method: 'post',
        data: {
          phoneNumber: phone,
          type: type,
          templateCode: 'SMS_276465873'
        }
      })
    },

    /**
     * 短信验证码登录
     * @param {Object} data - 登录数据，包含手机号和验证码
     * @returns {Promise} - 返回请求结果
     */
    smsLogin(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/sms-login`,
        method: 'post',
        data
      })
    },

    /**
     * 获取忘记密码验证码
     * @param {String} username - 手机号
     * @returns {Promise} - 返回请求结果
     */
    getForgotPasswordCaptcha(username) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/reset-password-captcha`,
        method: 'get',
        params: { username }
      })
    },

    /**
     * 验证忘记密码验证码
     * @param {Object} data - 验证数据，包含手机号和验证码
     * @returns {Promise} - 返回请求结果
     */
    verifyForgotPasswordCaptcha(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/forgot-password/verify-captcha`,
        method: 'post',
        data
      })
    },

    /**
     * 重置密码
     * @param {Object} data - 重置密码数据，包含手机号、验证码和新密码
     * @returns {Promise} - 返回请求结果
     */
    resetPassword(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/reset-password`,
        method: 'post',
        data
      })
    },

    /**
     * 通过用户名获取安全问题列表
     * @param {String} username - 用户名
     * @returns {Promise} - 返回请求结果，包含安全问题列表和邮箱信息
     */
    getSecurityQuestionsByUsername(username) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/security-questions/${username}`,
        method: 'get'
      })
    },
    
    /**
     * 验证安全问题答案
     * @param {Object} data - 验证数据，包含用户名和安全问题答案
     * @returns {Promise} - 返回请求结果
     */
    verifySecurityQuestions(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/verify-security-questions`,
        method: 'post',
        data
      })
    },
    
    /**
     * 通过手机号重置密码
     * @param {Object} data - 重置密码数据，包含手机号、验证码和新密码
     * @returns {Promise} - 返回请求结果
     */
    resetPasswordByPhone(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/reset-password`,
        method: 'post',
        data
      })
    },
    
    /**
     * 通过令牌重置密码
     * @param {Object} data - 重置密码数据，包含令牌、新密码和重置类型
     * @returns {Promise} - 返回请求结果
     */
    resetPasswordByToken(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/reset-password-by-token`,
        method: 'post',
        data
      })
    }
  },

  /**
   * 用户信息相关接口
   */
  profile: {
    /**
     * 获取用户信息
     * @returns {Promise} - 返回请求结果
     */
    getUserInfo() {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/profile`,
        method: 'get',
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    /**
     * 更新用户信息
     * @param {Object} data - 用户数据
     * @returns {Promise} - 返回请求结果
     */
    update(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/user/profile`,
        method: 'put',
        data
      })
    },
    
    /**
     * 更新用户昵称
     * @param {String} nickname - 新昵称
     * @returns {Promise} - 返回请求结果
     */
    updateNickname(nickname) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/update-nickname`,
        method: 'post',
        data: { nickname },
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 更新用户头像
     * @param {String} avatarUrl - 头像地址
     * @returns {Promise} - 返回请求结果
     */
    updateAvatar(avatarUrl) {
      // 获取token，先从 sessionStorage 获取，如果没有再从 localStorage 获取
      let token = null;
      if (typeof window !== 'undefined') {
        token = sessionStorage.getItem('mall_user_token') || localStorage.getItem('mall_user_token') || localStorage.getItem('token_mall');
      }
      
      // 打印token信息，便于调试
      console.log('更新头像使用的token:', token ? '已获取' : '未获取');
      
      return request({
        url: `${apiBaseUrl}/v1/mall/user/update-avatar`,
        method: 'post',
        data: { avatarUrl },
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 上传用户头像
     * @param {File} file - 头像文件（支持JPG、PNG、GIF、WEBP格式，大小不超过2MB）
     * @returns {Promise} - 返回请求结果
     */
    uploadAvatar(file) {
      // 获取token，先从 sessionStorage 获取，如果没有再从 localStorage 获取
      let token = null;
      if (typeof window !== 'undefined') {
        token = sessionStorage.getItem('mall_user_token') || localStorage.getItem('mall_user_token') || localStorage.getItem('token_mall');
      }
      
      // 打印token信息，便于调试
      console.log('上传头像使用的token:', token ? '已获取' : '未获取');
      
      const formData = new FormData()
      formData.append('file', file)
      formData.append('dir', 'avatar')
      formData.append('module', 'mall')
      formData.append('bizType', 'user_avatar')
      formData.append('isPublic', 'true')
      
      // 使用 master 的token
      const masterToken = localStorage.getItem('token_master');
      
      return request({
        url: `${apiBaseUrl}/v1/master/system/integration/upload/file`,
        method: 'post',
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': masterToken ? `Bearer ${masterToken}` : (token ? `Bearer ${token}` : '')
        }
      })
    },
    
    /**
     * 获取手机验证码
     * @param {String} newPhone - 新手机号
     * @returns {Promise} - 返回请求结果
     */
    getPhoneCaptcha(newPhone) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/phone-captcha`,
        method: 'get',
        params: { newPhone },
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 获取邮箱验证码
     * @param {String} email - 邮箱地址
     * @returns {Promise} - 返回请求结果
     */
    getEmailCaptcha(email) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/email-captcha`,
        method: 'post',
        data: { email },
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 绑定邮箱
     * @param {String} email - 邮箱地址
     * @param {String} captcha - 验证码
     * @returns {Promise} - 返回请求结果
     */
    bindEmail(email, captcha) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/bind-email`,
        method: 'post',
        data: { email, captcha },
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 修改手机号码
     * @param {String} newPhone - 新手机号
     * @param {String} captcha - 验证码
     * @returns {Promise} - 返回请求结果
     */
    updatePhone(newPhone, captcha) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/update-phone`,
        method: 'post',
        data: { newPhone, captcha },
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 修改密码
     * @param {Object} data - 包含原密码和新密码的对象
     * @returns {Promise} - 返回请求结果
     */
    changePassword(data = {}) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/change-password`,
        method: 'post',
        data,
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 保存用户实名认证信息
     * @param {Object} data - 实名认证数据，包含真实姓名、身份证号、身份证正反面照片
     * @returns {Promise} - 返回请求结果
     */
    saveRealnameAuth(data = {}) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/realname-auth`,
        method: 'post',
        data,
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 获取用户实名认证信息
     * @returns {Promise} - 返回请求结果
     */
    getRealnameAuth() {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/realname-auth`,
        method: 'get',
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 保存用户安全问题
     * @param {Array} questions - 安全问题数组，包含三个问题及答案
     * @returns {Promise} - 返回请求结果
     */
    saveSecurityQuestions(questions = []) {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/security-questions`,
        method: 'post',
        data: { questions },
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },
    
    /**
     * 获取用户安全问题列表
     * @returns {Promise} - 返回请求结果
     */
    getSecurityQuestions() {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/security-questions`,
        method: 'get',
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    },

    /**
     * 获取用户余额
     * @returns {Promise} - 返回请求结果
     */
    getBalance() {
      const token = getMallUserToken()
      return request({
        url: `${apiBaseUrl}/v1/mall/user/balance`,
        method: 'get',
        headers: {
          'Authorization': token ? `Bearer ${token}` : ''
        }
      })
    }
  }
}
