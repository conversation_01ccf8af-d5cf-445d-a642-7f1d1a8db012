import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 商城新闻分类管理API
 */
export default {
  /**
   * 获取新闻分类列表
   * @param {Object} params - 查询参数，包含分页、筛选和排序信息
   * @returns {Promise} - 返回请求结果
   */
  getList(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/master/mall/news-categories`,
      method: 'get',
      params
    })
  },

  /**
   * 获取新闻分类树形结构
   * @returns {Promise} - 返回请求结果
   */
  getTree() {
    return request({
      url: `${apiBaseUrl}/v1/master/mall/news-categories/tree`,
      method: 'get'
    })
  },

  /**
   * 获取新闻分类详情
   * @param {number} id - 分类ID
   * @returns {Promise} - 返回请求结果
   */
  getById(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/mall/news-categories/${id}`,
      method: 'get'
    })
  },

  /**
   * 创建新闻分类
   * @param {Object} data - 分类数据
   * @returns {Promise} - 返回请求结果
   */
  create(data) {
    return request({
      url: `${apiBaseUrl}/v1/master/mall/news-categories`,
      method: 'post',
      data
    })
  },

  /**
   * 更新新闻分类
   * @param {number} id - 分类ID
   * @param {Object} data - 更新数据
   * @returns {Promise} - 返回请求结果
   */
  update(id, data) {
    return request({
      url: `${apiBaseUrl}/v1/master/mall/news-categories/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除新闻分类
   * @param {number} id - 分类ID
   * @returns {Promise} - 返回请求结果
   */
  delete(id) {
    return request({
      url: `${apiBaseUrl}/v1/master/mall/news-categories/${id}`,
      method: 'delete'
    })
  },

  /**
   * 切换新闻分类状态
   * @param {number} id - 分类ID
   * @param {number} isEnabled - 启用状态（1-启用，0-禁用）
   * @returns {Promise} - 返回请求结果
   */
  toggleStatus(id, isEnabled) {
    return request({
      url: `${apiBaseUrl}/v1/master/mall/news-categories/${id}/status`,
      method: 'put',
      data: { is_enabled: isEnabled }
    })
  },

  // ==================== 新闻文章相关接口 ====================

  /**
   * 新闻文章管理API
   */
  list: {
    /**
     * 获取新闻文章列表
     * @param {Object} params - 查询参数，包含分页、筛选和排序信息
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/news-articles`,
        method: 'get',
        params
      })
    },

    /**
     * 获取新闻文章详情
     * @param {number} id - 文章ID
     * @returns {Promise} - 返回请求结果
     */
    getById(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/news-articles/${id}`,
        method: 'get'
      })
    },

    /**
     * 创建新闻文章
     * @param {Object} data - 文章数据
     * @returns {Promise} - 返回请求结果
     */
    create(data) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/news-articles`,
        method: 'post',
        data
      })
    },

    /**
     * 更新新闻文章
     * @param {number} id - 文章ID
     * @param {Object} data - 更新数据
     * @returns {Promise} - 返回请求结果
     */
    update(id, data) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/news-articles/${id}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除新闻文章
     * @param {number} id - 文章ID
     * @returns {Promise} - 返回请求结果
     */
    delete(id) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/news-articles/${id}`,
        method: 'delete'
      })
    },

    /**
     * 切换新闻文章状态
     * @param {number} id - 文章ID
     * @param {number} isEnabled - 启用状态（1-启用，0-禁用）
     * @returns {Promise} - 返回请求结果
     */
    toggleStatus(id, isEnabled) {
      return request({
        url: `${apiBaseUrl}/v1/master/mall/news-articles/${id}/status`,
        method: 'put',
        data: { is_enabled: isEnabled }
      })
    }
  },

  // ==================== 商城前端公共新闻接口 ====================

  /**
   * 获取启用的新闻分类列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getCategories(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/news/categories`,
      method: 'get',
      params
    })
  },

  /**
   * 获取启用的新闻文章列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getArticles(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/news/articles`,
      method: 'get',
      params
    })
  },

  /**
   * 获取最新新闻文章
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getLatestArticles(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/news/articles/latest`,
      method: 'get',
      params
    })
  },

  /**
   * 获取热门新闻文章
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getHotArticles(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/news/articles/hot`,
      method: 'get',
      params
    })
  },

  /**
   * 获取新闻文章详情
   * @param {number} id - 文章ID
   * @returns {Promise} - 返回请求结果
   */
  getArticleDetail(id) {
    return request({
      url: `${apiBaseUrl}/v1/mall/news/articles/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取相关文章推荐
   * @param {number} id - 文章ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getRelatedArticles(id, params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/news/articles/${id}/related`,
      method: 'get',
      params
    })
  }
}
