/**
 * 商城订单支付API
 */
import { request } from '~/utils/request';

const orderPaymentApi = {
  /**
   * 创建订单支付
   * @param {Object} data - 支付数据
   * @param {string} data.orderId - 订单ID
   * @param {number} data.paymentMethodId - 支付方式ID
   * @returns {Promise}
   */
  createPayment(data) {
    return request({
      url: '/api/v1/mall/order-payment/create',
      method: 'POST',
      data
    });
  },

  /**
   * 查询支付状态
   * @param {string} paymentSn - 支付流水号
   * @returns {Promise}
   */
  queryPaymentStatus(paymentSn) {
    return request({
      url: `/api/v1/mall/order-payment/status/${paymentSn}`,
      method: 'GET'
    });
  },

  /**
   * 取消支付
   * @param {string} paymentSn - 支付流水号
   * @returns {Promise}
   */
  cancelPayment(paymentSn) {
    return request({
      url: `/api/v1/mall/order-payment/cancel/${paymentSn}`,
      method: 'POST'
    });
  },

  /**
   * 获取订单支付记录
   * @param {string} orderId - 订单ID
   * @returns {Promise}
   */
  getOrderPaymentRecords(orderId) {
    return request({
      url: `/api/v1/mall/order-payment/records/${orderId}`,
      method: 'GET'
    });
  },

  /**
   * 获取支付统计信息
   * @param {Object} params - 查询参数
   * @param {number} params.startTime - 开始时间戳
   * @param {number} params.endTime - 结束时间戳
   * @returns {Promise}
   */
  getPaymentStatistics(params) {
    return request({
      url: '/api/v1/mall/order-payment/statistics',
      method: 'GET',
      params
    });
  },

  /**
   * 获取支付方式列表
   * @returns {Promise}
   */
  getPaymentMethods() {
    return request({
      url: '/api/v1/master/payment-methods',
      method: 'GET'
    });
  }
};

export default orderPaymentApi;
