import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 获取商城用户token
 * @returns {string|null} token
 */
const getMallUserToken = () => {
  if (process.client) {
    return localStorage.getItem('mall_user_token')
  }
  return null
}

/**
 * 商城订单评价模块API
 */
export default {
  /**
   * 获取订单可评价的商品列表
   * @param {String} orderId - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  getReviewableItems(orderId) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/review/reviewable-items/${orderId}`,
      method: 'get',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 提交商品评价
   * @param {Object} data - 评价数据
   * @returns {Promise} - 返回请求结果
   */
  submitReview(data = {}) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/review/submit`,
      method: 'post',
      data,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 批量提交商品评价
   * @param {Object} data - 批量评价数据
   * @returns {Promise} - 返回请求结果
   */
  submitBatchReviews(data = {}) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/review/submit-batch`,
      method: 'post',
      data,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 获取商品评价列表（用于商品详情页）
   * @param {String} goodsSpuId - 商品SPU ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getProductReviews(goodsSpuId, params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/review/product/${goodsSpuId}`,
      method: 'get',
      params
    })
  },

  /**
   * 获取商品评价统计
   * @param {String} goodsSpuId - 商品SPU ID
   * @returns {Promise} - 返回请求结果
   */
  getProductReviewStats(goodsSpuId) {
    return request({
      url: `${apiBaseUrl}/v1/mall/review/product/${goodsSpuId}/stats`,
      method: 'get'
    })
  },

  /**
   * 获取用户的评价历史
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回请求结果
   */
  getUserReviews(params = {}) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/review/user/reviews`,
      method: 'get',
      params,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  }
}
