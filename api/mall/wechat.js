import { request } from '@/utils/request.js';
import { apiBaseUrl } from '@/api/config';

// 导出命名函数，供组件直接导入使用
export function getBindQrcode(token) {
  return request({
    url: `${apiBaseUrl}/v1/mall/wechat/user/bind/qrcode`,
    method: 'get',
    headers: {
      'Authorization': token ? `Bearer ${token}` : ''
    }
  });
}

export function checkBindStatus(sceneStr, token) {
  return request({
    url: `${apiBaseUrl}/v1/mall/wechat/user/bind/status`,
    method: 'get',
    params: { sceneStr },
    headers: {
      'Authorization': token ? `Bearer ${token}` : ''
    }
  });
}

export default {
  /**
   * 获取微信扫码登录二维码
   * @returns {Promise} - 返回请求结果
   */
  getLoginQrcode() {
    return request({
      url: `${apiBaseUrl}/v1/mall/wechat/login/qrcode`,
      method: 'get'
    });
  },
  
  /**
   * 获取微信绑定二维码
   * @param {String} token - 用户令牌
   * @returns {Promise} - 返回请求结果
   */
  getBindQrcode(token) {
    return request({
      url: `${apiBaseUrl}/v1/mall/user/wechat/bind/qrcode`,
      method: 'get',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });
  },

  /**
   * 查询扫码状态
   * @param {String} ticket - 二维码票据
   * @returns {Promise} - 返回请求结果
   */
  checkLoginStatus(ticket) {
    return request({
      url: `${apiBaseUrl}/v1/mall/wechat/login/check`,
      method: 'get',
      params: { sceneStr: ticket }
    });
  },

  /**
   * 处理微信授权回调
   * @param {String} code - 授权码
   * @param {String} state - 状态参数
   * @returns {Promise} - 返回请求结果
   */
  handleLoginCallback(code, state) {
    console.log('处理微信授权回调:', code, state);
    return request({
      url: `${apiBaseUrl}/v1/mall/wechat/login/callback`,
      method: 'get',
      params: { code, state }
    });
  },
  
  /**
   * 查询微信绑定状态
   * @param {String} sceneStr - 场景值
   * @param {String} token - 用户令牌
   * @returns {Promise} - 返回请求结果
   */
  checkBindStatus(sceneStr, token) {
    return request({
      url: `${apiBaseUrl}/v1/mall/user/wechat/bind/status`,
      method: 'get',
      params: { sceneStr },
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });
  }
};
