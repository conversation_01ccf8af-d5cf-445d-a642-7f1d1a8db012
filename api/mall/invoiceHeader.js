/**
 * 发票抬头 API
 */
import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

// 获取商城用户token
const getMallUserToken = () => {
  // 优先从localStorage获取
  const token = localStorage.getItem('mall_user_token') || sessionStorage.getItem('mall_user_token')
  return token
}

export default {
  /**
   * 获取用户的发票抬头列表
   * @returns {Promise} API 响应
   */
  async getUserHeaders() {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/invoice-headers`,
      method: 'get',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 创建发票抬头
   * @param {Object} headerData - 抬头数据
   * @returns {Promise} API 响应
   */
  async createHeader(headerData) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/invoice-headers`,
      method: 'post',
      data: headerData,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 更新发票抬头
   * @param {string|number} id - 抬头ID
   * @param {Object} headerData - 更新数据
   * @returns {Promise} API 响应
   */
  async updateHeader(id, headerData) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/invoice-headers/${id}`,
      method: 'put',
      data: headerData,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 删除发票抬头
   * @param {string|number} id - 抬头ID
   * @returns {Promise} API 响应
   */
  async deleteHeader(id) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/invoice-headers/${id}`,
      method: 'delete',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 设置默认抬头
   * @param {string|number} id - 抬头ID
   * @returns {Promise} API 响应
   */
  async setDefaultHeader(id) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/invoice-headers/${id}/set-default`,
      method: 'put',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 根据ID获取发票抬头
   * @param {string|number} id - 抬头ID
   * @returns {Promise} API 响应
   */
  async getHeaderById(id) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/invoice-headers/${id}`,
      method: 'get',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  }
};
