import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

// 获取商城用户token
const getMallUserToken = () => {
  // 优先从localStorage获取
  const token = localStorage.getItem('mall_user_token') || sessionStorage.getItem('mall_user_token')
  return token
}

/**
 * 商城订单模块API
 */
export default {
  /**
   * 查询用户自己的订单
   * @param {Object} data - 查询参数，包含页码、每页数量、订单状态等
   * @returns {Promise} - 返回请求结果
   */
  queryOwnOrders(data = {}) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/order/query-own-orders`,
      method: 'post',
      data,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 获取订单详情
   * @param {String|Number} orderId - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  getOrderDetail(orderId) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${orderId}`,
      method: 'get',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },
  
  /**
   * 创建订单
   * @param {Object} orderData - 订单数据，包含客户信息、商品项、配送信息等
   * @returns {Promise} - 返回请求结果，包含订单ID和订单编号
   */
  createOrder(orderData = {}) {
    const token = getMallUserToken()
    // 确保订单类型和来源设置正确
    const orderDataWithCorrectParams = {
      ...orderData,
      orderType: 1,  // 商城订单
      orderSource: 2 // 商城下单
    }
    
    // 确保商品项的 ID 不为空
    if (orderDataWithCorrectParams.items && Array.isArray(orderDataWithCorrectParams.items)) {
      orderDataWithCorrectParams.items = orderDataWithCorrectParams.items.map(item => ({
        ...item,
        goodsSpuId: item.goodsSpuId || '0',
        goodsSkuId: item.goodsSkuId || '0'
      }));
    }
    
    console.log('发送订单数据:', orderDataWithCorrectParams);
    
    return request({
      url: `${apiBaseUrl}/v1/master/orders`,
      method: 'post',
      data: orderDataWithCorrectParams,
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },
  
  /**
   * 取消订单
   * @param {String|Number} orderId - 订单ID
   * @param {Object} data - 取消原因数据
   * @returns {Promise} - 返回请求结果
   */
  cancelOrder(data) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${data.orderId}/cancel`,
      method: 'post',
      data:{
        "cancelReason":data.cancelReason
      },
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },



  /**
   * 获取订单状态数量统计
   * @returns {Promise} - 返回各订单状态的数量
   */
  getOrderStatusCounts() {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/mall/order/status-counts`,
      method: 'get',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  },

  /**
   * 订单收货
   * @param {String|Number} orderId - 订单ID
   * @returns {Promise} - 返回请求结果
   */
  receiptOrder(orderId) {
    const token = getMallUserToken()
    return request({
      url: `${apiBaseUrl}/v1/master/orders/${orderId}/receipt`,
      method: 'post',
      headers: {
        'Authorization': token ? `Bearer ${token}` : ''
      }
    })
  }
}
