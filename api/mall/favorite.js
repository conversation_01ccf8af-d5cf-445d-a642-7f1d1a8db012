import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

// 获取商城用户token
const getMallUserToken = () => {
  // 优先从localStorage获取
  const token = localStorage.getItem('mall_user_token') || sessionStorage.getItem('mall_user_token')
  return token
}

/**
 * 商城收藏模块API
 */
export default {
  /**
   * 添加收藏
   * @param {Object} data - 收藏数据
   * @param {Array} data.targetId - 目标ID数组
   * @param {number} data.targetType - 目标类型 (1: 商品)
   * @param {string} data.remark - 备注
   * @returns {Promise} - 返回请求结果
   */
  addFavorite(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/favorite/add`,
      method: 'post',
      data,
      headers: {
        'Authorization': `Bearer ${getMallUserToken()}`,
        'Content-Type': 'application/json'
      }
    })
  },

  /**
   * 取消收藏
   * @param {Object} data - 取消收藏数据
   * @param {Array} data.targetId - 目标ID数组
   * @param {number} data.targetType - 目标类型 (1: 商品)
   * @param {string} data.remark - 备注
   * @returns {Promise} - 返回请求结果
   */
  cancelFavorite(data = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/favorite/cancel`,
      method: 'post',
      data,
      headers: {
        'Authorization': `Bearer ${getMallUserToken()}`,
        'Content-Type': 'application/json'
      }
    })
  },

  /**
   * 获取收藏列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {number} params.targetType - 目标类型，商城默认为1
   * @returns {Promise} - 返回请求结果
   */
  getFavoriteList(params = {}) {
    // 设置默认的targetType为1（商品类型）
    const queryParams = {
      targetType: 1,
      ...params
    }

    return request({
      url: `${apiBaseUrl}/v1/mall/favorite/list`,
      method: 'get',
      params: queryParams,
      headers: {
        'Authorization': `Bearer ${getMallUserToken()}`
      }
    })
  },

  /**
   * 检查收藏状态
   * @param {Object} params - 查询参数
   * @param {string|number} params.targetId - 目标ID
   * @param {number} params.targetType - 目标类型 (1: 商品)
   * @returns {Promise} - 返回请求结果
   */
  checkFavoriteStatus(params = {}) {
    return request({
      url: `${apiBaseUrl}/v1/mall/favorite/check`,
      method: 'get',
      params,
      headers: {
        'Authorization': `Bearer ${getMallUserToken()}`
      }
    })
  },

  /**
   * 批量添加收藏
   * @param {Array} targetIds - 目标ID数组
   * @param {number} targetType - 目标类型 (1: 商品)
   * @param {string} remark - 备注
   * @returns {Promise} - 返回请求结果
   */
  batchAddFavorite(targetIds, targetType = 1, remark = '') {
    return this.addFavorite({
      targetId: targetIds,
      targetType,
      remark
    })
  },

  /**
   * 批量取消收藏
   * @param {Array} targetIds - 目标ID数组
   * @param {number} targetType - 目标类型 (1: 商品)
   * @param {string} remark - 备注
   * @returns {Promise} - 返回请求结果
   */
  batchCancelFavorite(targetIds, targetType = 1, remark = '') {
    return this.cancelFavorite({
      targetId: targetIds,
      targetType,
      remark
    })
  },

  /**
   * 单个商品收藏切换
   * @param {string|number} targetId - 目标ID
   * @param {boolean} isFavorited - 当前收藏状态
   * @param {number} targetType - 目标类型 (1: 商品)
   * @param {string} remark - 备注
   * @returns {Promise} - 返回请求结果
   */
  toggleFavorite(targetId, isFavorited, targetType = 1, remark = '') {
    if (isFavorited) {
      // 如果已收藏，则取消收藏
      return this.cancelFavorite({
        targetId: [targetId],
        targetType,
        remark
      })
    } else {
      // 如果未收藏，则添加收藏
      return this.addFavorite({
        targetId: [targetId],
        targetType,
        remark
      })
    }
  }
}
