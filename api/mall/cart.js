import { request } from '@/utils/request.js'
import { apiBaseUrl } from '@/api/config'

/**
 * 商城购物车模块API
 */
export default {
  /**
   * 购物车操作相关接口
   */
  cart: {
    /**
     * 获取购物车列表
     * @param {Object} params - 查询参数
     * @returns {Promise} - 返回请求结果
     */
    getList(params = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/cart`,
        method: 'get',
        params
      })
    },

    /**
     * 添加商品到购物车
     * @param {Object} data - 商品数据
     * @returns {Promise} - 返回请求结果
     */
    add(data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/cart`,
        method: 'post',
        data
      })
    },

    /**
     * 更新购物车商品数量
     * @param {String} itemId - 购物车项ID
     * @param {Object} data - 更新数据
     * @returns {Promise} - 返回请求结果
     */
    update(itemId, data = {}) {
      return request({
        url: `${apiBaseUrl}/v1/mall/cart/${itemId}`,
        method: 'put',
        data
      })
    },

    /**
     * 删除购物车商品
     * @param {String} itemId - 购物车项ID
     * @returns {Promise} - 返回请求结果
     */
    remove(itemId) {
      return request({
        url: `${apiBaseUrl}/v1/mall/cart/${itemId}`,
        method: 'delete'
      })
    },

    /**
     * 清空购物车
     * @returns {Promise} - 返回请求结果
     */
    clear() {
      return request({
        url: `${apiBaseUrl}/v1/mall/cart/clear`,
        method: 'delete'
      })
    },

    /**
     * 选择/取消选择购物车商品
     * @param {String} itemId - 购物车项ID
     * @param {Boolean} selected - 是否选中
     * @returns {Promise} - 返回请求结果
     */
    toggleSelect(itemId, selected) {
      return request({
        url: `${apiBaseUrl}/v1/mall/cart/${itemId}/select`,
        method: 'put',
        data: { selected }
      })
    },

    /**
     * 全选/取消全选购物车商品
     * @param {Boolean} selected - 是否全选
     * @returns {Promise} - 返回请求结果
     */
    selectAll(selected) {
      return request({
        url: `${apiBaseUrl}/v1/mall/cart/select-all`,
        method: 'put',
        data: { selected }
      })
    }
  }
}
