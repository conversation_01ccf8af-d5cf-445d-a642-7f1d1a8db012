-- 第三方商品品牌功能数据库迁移脚本
-- 执行时间：2025-01-25

-- =====================================================
-- 1. 为 goods_brands 表添加第三方相关字段
-- =====================================================

-- 添加来源类型字段
ALTER TABLE "base"."goods_brands" 
ADD COLUMN IF NOT EXISTS "source_type" INTEGER DEFAULT 0;

-- 添加第三方创建相关字段
ALTER TABLE "base"."goods_brands" 
ADD COLUMN IF NOT EXISTS "created_channel_id" BIGINT;

ALTER TABLE "base"."goods_brands" 
ADD COLUMN IF NOT EXISTS "created_platform_id" BIGINT;

ALTER TABLE "base"."goods_brands" 
ADD COLUMN IF NOT EXISTS "created_store_id" BIGINT;

-- 添加字段注释
COMMENT ON COLUMN "base"."goods_brands"."source_type" IS '来源类型：0-系统创建，1-第三方创建';
COMMENT ON COLUMN "base"."goods_brands"."created_channel_id" IS '创建渠道ID（第三方品牌）';
COMMENT ON COLUMN "base"."goods_brands"."created_platform_id" IS '创建平台ID（第三方品牌）';
COMMENT ON COLUMN "base"."goods_brands"."created_store_id" IS '创建店铺ID（第三方品牌）';

-- 添加索引
CREATE INDEX IF NOT EXISTS "idx_goods_brands_source_type" 
ON "base"."goods_brands" ("source_type");

CREATE INDEX IF NOT EXISTS "idx_goods_brands_created_channel_id" 
ON "base"."goods_brands" ("created_channel_id");

CREATE INDEX IF NOT EXISTS "idx_goods_brands_created_platform_id" 
ON "base"."goods_brands" ("created_platform_id");

CREATE INDEX IF NOT EXISTS "idx_goods_brands_created_store_id" 
ON "base"."goods_brands" ("created_store_id");

-- =====================================================
-- 2. 创建商品品牌平台关联表
-- =====================================================

CREATE TABLE IF NOT EXISTS "base"."goods_brand_platform_relations" (
    "id" BIGINT PRIMARY KEY,
    "goods_brand_id" BIGINT NOT NULL,
    "channel_id" BIGINT NOT NULL,
    "platform_id" BIGINT NOT NULL,
    "store_id" BIGINT NOT NULL,
    "platform_brand_id" VARCHAR(100),
    "platform_brand_name" VARCHAR(255),
    "push_status" INTEGER NOT NULL DEFAULT 0,
    "sync_status" INTEGER NOT NULL DEFAULT 0,
    "push_time" BIGINT,
    "sync_time" BIGINT,
    "push_error_message" TEXT,
    "sync_error_message" TEXT,
    "created_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "updated_at" BIGINT NOT NULL DEFAULT extract(epoch from now()) * 1000,
    "deleted_at" BIGINT
);

-- 添加表注释
COMMENT ON TABLE "base"."goods_brand_platform_relations" IS '商品品牌平台关联表';

-- 添加字段注释
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."id" IS '主键ID，雪花算法生成';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."goods_brand_id" IS '品牌ID，关联goods_brands表';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."channel_id" IS '渠道ID，关联channel表';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."platform_id" IS '平台ID，关联platform表';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."store_id" IS '店铺ID，关联store表';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."platform_brand_id" IS '第三方平台品牌ID';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."platform_brand_name" IS '第三方平台品牌名称';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."push_status" IS '推送状态：0-未推送，1-推送成功，2-推送失败';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."sync_status" IS '同步状态：0-未同步，1-同步成功，2-同步失败';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."push_time" IS '推送时间戳（毫秒）';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."sync_time" IS '同步时间戳（毫秒）';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."push_error_message" IS '推送错误信息';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."sync_error_message" IS '同步错误信息';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."created_at" IS '创建时间戳（毫秒）';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."updated_at" IS '更新时间戳（毫秒）';
COMMENT ON COLUMN "base"."goods_brand_platform_relations"."deleted_at" IS '删除时间戳（毫秒，软删除）';

-- 创建索引
CREATE INDEX IF NOT EXISTS "idx_goods_brand_platform_relations_brand_id" 
ON "base"."goods_brand_platform_relations" ("goods_brand_id");

CREATE INDEX IF NOT EXISTS "idx_goods_brand_platform_relations_channel_id" 
ON "base"."goods_brand_platform_relations" ("channel_id");

CREATE INDEX IF NOT EXISTS "idx_goods_brand_platform_relations_platform_id" 
ON "base"."goods_brand_platform_relations" ("platform_id");

CREATE INDEX IF NOT EXISTS "idx_goods_brand_platform_relations_store_id" 
ON "base"."goods_brand_platform_relations" ("store_id");

CREATE INDEX IF NOT EXISTS "idx_goods_brand_platform_relations_push_status" 
ON "base"."goods_brand_platform_relations" ("push_status");

CREATE INDEX IF NOT EXISTS "idx_goods_brand_platform_relations_sync_status" 
ON "base"."goods_brand_platform_relations" ("sync_status");

CREATE INDEX IF NOT EXISTS "idx_goods_brand_platform_relations_deleted_at" 
ON "base"."goods_brand_platform_relations" ("deleted_at");

-- 创建复合唯一索引（确保同一品牌在同一渠道-平台-店铺组合下只有一条记录）
CREATE INDEX IF NOT EXISTS "idx_goods_brand_platform_relations_unique" 
ON "base"."goods_brand_platform_relations" ("goods_brand_id", "channel_id", "platform_id", "store_id");

-- =====================================================
-- 3. 数据完整性约束（可选，根据需要启用）
-- =====================================================

-- 注意：以下外键约束可能会影响性能，根据实际需求决定是否启用

-- 添加外键约束（可选）
-- ALTER TABLE "base"."goods_brand_platform_relations" 
-- ADD CONSTRAINT "fk_goods_brand_platform_relations_brand" 
-- FOREIGN KEY ("goods_brand_id") REFERENCES "base"."goods_brands" ("id") ON DELETE CASCADE;

-- ALTER TABLE "base"."goods_brand_platform_relations" 
-- ADD CONSTRAINT "fk_goods_brand_platform_relations_channel" 
-- FOREIGN KEY ("channel_id") REFERENCES "base"."channel" ("id") ON DELETE CASCADE;

-- ALTER TABLE "base"."goods_brand_platform_relations" 
-- ADD CONSTRAINT "fk_goods_brand_platform_relations_platform" 
-- FOREIGN KEY ("platform_id") REFERENCES "base"."platform" ("id") ON DELETE CASCADE;

-- ALTER TABLE "base"."goods_brand_platform_relations" 
-- ADD CONSTRAINT "fk_goods_brand_platform_relations_store" 
-- FOREIGN KEY ("store_id") REFERENCES "base"."store" ("id") ON DELETE CASCADE;

-- =====================================================
-- 4. 验证脚本
-- =====================================================

-- 验证 goods_brands 表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'base' 
  AND table_name = 'goods_brands' 
  AND column_name IN ('source_type', 'created_channel_id', 'created_platform_id', 'created_store_id')
ORDER BY ordinal_position;

-- 验证 goods_brand_platform_relations 表是否创建成功
SELECT table_name, table_comment 
FROM information_schema.tables 
WHERE table_schema = 'base' 
  AND table_name = 'goods_brand_platform_relations';

-- 验证索引是否创建成功
SELECT indexname, tablename 
FROM pg_indexes 
WHERE schemaname = 'base' 
  AND tablename IN ('goods_brands', 'goods_brand_platform_relations')
  AND indexname LIKE '%brand%'
ORDER BY tablename, indexname;

-- =====================================================
-- 执行完成
-- =====================================================

-- 执行完成后，请验证：
-- 1. goods_brands 表已添加 source_type, created_channel_id, created_platform_id, created_store_id 字段
-- 2. goods_brand_platform_relations 表已创建
-- 3. 所有索引已创建
-- 4. 第三方商品品牌API可以正常使用
