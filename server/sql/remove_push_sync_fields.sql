-- 移除第三方商品相关表中的推送、同步业务字段
-- 执行前请备份数据库！

-- 1. 移除 goods_sku_platform_relation 表中的推送、同步字段
ALTER TABLE "base"."goods_sku_platform_relation" DROP COLUMN IF EXISTS "push_status";
ALTER TABLE "base"."goods_sku_platform_relation" DROP COLUMN IF EXISTS "push_time";
ALTER TABLE "base"."goods_sku_platform_relation" DROP COLUMN IF EXISTS "push_error_msg";
ALTER TABLE "base"."goods_sku_platform_relation" DROP COLUMN IF EXISTS "push_retry_count";
ALTER TABLE "base"."goods_sku_platform_relation" DROP COLUMN IF EXISTS "sync_status";
ALTER TABLE "base"."goods_sku_platform_relation" DROP COLUMN IF EXISTS "sync_time";
ALTER TABLE "base"."goods_sku_platform_relation" DROP COLUMN IF EXISTS "sync_error_msg";
ALTER TABLE "base"."goods_sku_platform_relation" DROP COLUMN IF EXISTS "sync_retry_count";
ALTER TABLE "base"."goods_sku_platform_relation" DROP COLUMN IF EXISTS "last_sync_data";

-- 2. 移除 goods_category_platform_relations 表中的推送、同步字段
ALTER TABLE "base"."goods_category_platform_relations" DROP COLUMN IF EXISTS "push_status";
ALTER TABLE "base"."goods_category_platform_relations" DROP COLUMN IF EXISTS "push_time";
ALTER TABLE "base"."goods_category_platform_relations" DROP COLUMN IF EXISTS "push_error_msg";
ALTER TABLE "base"."goods_category_platform_relations" DROP COLUMN IF EXISTS "sync_status";
ALTER TABLE "base"."goods_category_platform_relations" DROP COLUMN IF EXISTS "sync_time";
ALTER TABLE "base"."goods_category_platform_relations" DROP COLUMN IF EXISTS "sync_error_msg";

-- 3. 移除 goods_brand_platform_relations 表中的推送、同步字段
ALTER TABLE "base"."goods_brand_platform_relations" DROP COLUMN IF EXISTS "push_status";
ALTER TABLE "base"."goods_brand_platform_relations" DROP COLUMN IF EXISTS "sync_status";
ALTER TABLE "base"."goods_brand_platform_relations" DROP COLUMN IF EXISTS "push_time";
ALTER TABLE "base"."goods_brand_platform_relations" DROP COLUMN IF EXISTS "sync_time";
ALTER TABLE "base"."goods_brand_platform_relations" DROP COLUMN IF EXISTS "push_error_message";
ALTER TABLE "base"."goods_brand_platform_relations" DROP COLUMN IF EXISTS "sync_error_message";

-- 4. 删除相关索引
-- goods_sku_platform_relation 表的推送、同步相关索引
DROP INDEX IF EXISTS "base"."idx_goods_sku_platform_relation_push_retry";
DROP INDEX IF EXISTS "base"."idx_goods_sku_platform_relation_push_status";
DROP INDEX IF EXISTS "base"."idx_goods_sku_platform_relation_sync_retry";
DROP INDEX IF EXISTS "base"."idx_goods_sku_platform_relation_sync_status";

-- goods_category_platform_relations 表的推送、同步相关索引
DROP INDEX IF EXISTS "base"."idx_goods_category_platform_relations_push_status";
DROP INDEX IF EXISTS "base"."idx_goods_category_platform_relations_sync_status";

-- goods_brand_platform_relations 表的推送、同步相关索引
DROP INDEX IF EXISTS "base"."idx_goods_brand_platform_relations_push_status";
DROP INDEX IF EXISTS "base"."idx_goods_brand_platform_relations_sync_status";

-- 执行完成后的验证查询
-- 验证字段是否已删除
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_schema = 'base' 
    AND table_name IN ('goods_sku_platform_relation', 'goods_category_platform_relations', 'goods_brand_platform_relations')
    AND column_name LIKE '%push%' OR column_name LIKE '%sync%'
ORDER BY table_name, column_name;

-- 如果上述查询返回空结果，说明所有推送、同步相关字段已成功删除
