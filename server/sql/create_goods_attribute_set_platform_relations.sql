-- 创建商品属性集平台关联表
-- 用于管理商品属性集与渠道、平台、店铺的多对多关联关系

CREATE TABLE IF NOT EXISTS base.goods_attribute_set_platform_relations (
    id BIGSERIAL PRIMARY KEY,
    attribute_set_id BIGINT NOT NULL COMMENT '属性集ID',
    channel_id BIGINT NOT NULL COMMENT '渠道ID',
    platform_id BIGINT NOT NULL COMMENT '平台ID', 
    store_id BIGINT NOT NULL COMMENT '店铺ID',
    platform_attribute_set_id VARCHAR(100) NULL COMMENT '平台属性集ID',
    platform_attribute_set_name VARCHAR(255) NULL COMMENT '平台属性集名称',
    source_type SMALLINT NOT NULL DEFAULT 0 COMMENT '来源类型：0-系统，1-第三方',
    created_at BIGINT NOT NULL COMMENT '创建时间戳（毫秒）',
    updated_at BIGINT NOT NULL COMMENT '更新时间戳（毫秒）'
);

-- 创建唯一索引，确保同一属性集在同一渠道-平台-店铺组合下只能有一条记录
CREATE UNIQUE INDEX IF NOT EXISTS goods_attribute_set_platform_relation_unique 
ON base.goods_attribute_set_platform_relations (attribute_set_id, channel_id, platform_id, store_id);

-- 创建普通索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_goods_attribute_set_platform_relation_channel_id 
ON base.goods_attribute_set_platform_relations (channel_id);

CREATE INDEX IF NOT EXISTS idx_goods_attribute_set_platform_relation_platform_id 
ON base.goods_attribute_set_platform_relations (platform_id);

CREATE INDEX IF NOT EXISTS idx_goods_attribute_set_platform_relation_store_id 
ON base.goods_attribute_set_platform_relations (store_id);

CREATE INDEX IF NOT EXISTS idx_goods_attribute_set_platform_relation_attribute_set_id 
ON base.goods_attribute_set_platform_relations (attribute_set_id);

CREATE INDEX IF NOT EXISTS idx_goods_attribute_set_platform_relation_source_type 
ON base.goods_attribute_set_platform_relations (source_type);

-- 添加表注释
COMMENT ON TABLE base.goods_attribute_set_platform_relations IS '商品属性集平台关联表';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.id IS '主键ID';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.attribute_set_id IS '属性集ID';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.channel_id IS '渠道ID';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.platform_id IS '平台ID';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.store_id IS '店铺ID';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.platform_attribute_set_id IS '平台属性集ID';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.platform_attribute_set_name IS '平台属性集名称';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.source_type IS '来源类型：0-系统，1-第三方';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.created_at IS '创建时间戳（毫秒）';
COMMENT ON COLUMN base.goods_attribute_set_platform_relations.updated_at IS '更新时间戳（毫秒）';
