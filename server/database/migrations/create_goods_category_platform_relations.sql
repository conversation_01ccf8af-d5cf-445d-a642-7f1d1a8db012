-- 创建商品分类平台关联表
CREATE TABLE IF NOT EXISTS goods_category_platform_relations (
    id BIGINT PRIMARY KEY,
    goods_category_id BIGINT NOT NULL,
    channel_id BIGINT NOT NULL,
    platform_id BIGINT NOT NULL,
    store_id BIGINT NOT NULL,
    platform_category_id VARCHAR(100),
    platform_category_name VARCHAR(255),
    
    -- 同步状态字段
    push_status INTEGER DEFAULT 0,
    push_time BIGINT,
    push_error_msg TEXT,
    sync_status INTEGER DEFAULT 0,
    sync_time BIGINT,
    sync_error_msg TEXT,
    
    -- 业务状态字段
    is_enabled INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    
    -- 审计字段
    created_at BIGINT DEFAULT (extract(epoch from now()) * 1000),
    updated_at BIGINT DEFAULT (extract(epoch from now()) * 1000),
    deleted_at BIGINT,
    created_by BIGIN<PERSON>,
    updated_by BIGINT,
    
    -- 外键约束
    CONSTRAINT fk_goods_category_platform_relations_category 
        FOREIGN KEY (goods_category_id) REFERENCES goods_categories(id) 
        ON DELETE NO ACTION ON UPDATE NO ACTION,
    CONSTRAINT fk_goods_category_platform_relations_channel 
        FOREIGN KEY (channel_id) REFERENCES channels(id) 
        ON DELETE NO ACTION ON UPDATE NO ACTION,
    CONSTRAINT fk_goods_category_platform_relations_platform 
        FOREIGN KEY (platform_id) REFERENCES platforms(id) 
        ON DELETE NO ACTION ON UPDATE NO ACTION,
    CONSTRAINT fk_goods_category_platform_relations_store 
        FOREIGN KEY (store_id) REFERENCES stores(id) 
        ON DELETE NO ACTION ON UPDATE NO ACTION
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_category_id ON goods_category_platform_relations(goods_category_id);
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_channel_id ON goods_category_platform_relations(channel_id);
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_platform_id ON goods_category_platform_relations(platform_id);
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_store_id ON goods_category_platform_relations(store_id);
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_unique ON goods_category_platform_relations(goods_category_id, channel_id, platform_id, store_id);
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_push_status ON goods_category_platform_relations(push_status);
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_sync_status ON goods_category_platform_relations(sync_status);
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_enabled ON goods_category_platform_relations(is_enabled);
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_created_at ON goods_category_platform_relations(created_at);
CREATE INDEX IF NOT EXISTS idx_goods_category_platform_relations_deleted_at ON goods_category_platform_relations(deleted_at);

-- 添加注释
COMMENT ON TABLE goods_category_platform_relations IS '商品分类平台关联表';
COMMENT ON COLUMN goods_category_platform_relations.id IS '主键ID';
COMMENT ON COLUMN goods_category_platform_relations.goods_category_id IS '商品分类ID，关联goods_categories表';
COMMENT ON COLUMN goods_category_platform_relations.channel_id IS '渠道ID，关联channels表';
COMMENT ON COLUMN goods_category_platform_relations.platform_id IS '平台ID，关联platforms表';
COMMENT ON COLUMN goods_category_platform_relations.store_id IS '店铺ID，关联stores表';
COMMENT ON COLUMN goods_category_platform_relations.platform_category_id IS '第三方平台的分类ID（用于同步）';
COMMENT ON COLUMN goods_category_platform_relations.platform_category_name IS '第三方平台的分类名称';
COMMENT ON COLUMN goods_category_platform_relations.push_status IS '推送到平台状态：0-未推送，1-推送成功，2-推送失败';
COMMENT ON COLUMN goods_category_platform_relations.push_time IS '推送时间戳';
COMMENT ON COLUMN goods_category_platform_relations.push_error_msg IS '推送失败错误信息';
COMMENT ON COLUMN goods_category_platform_relations.sync_status IS '从平台同步状态：0-未同步，1-同步成功，2-同步失败';
COMMENT ON COLUMN goods_category_platform_relations.sync_time IS '同步时间戳';
COMMENT ON COLUMN goods_category_platform_relations.sync_error_msg IS '同步失败错误信息';
COMMENT ON COLUMN goods_category_platform_relations.is_enabled IS '是否启用：1-启用，0-禁用';
COMMENT ON COLUMN goods_category_platform_relations.sort_order IS '在该平台的排序';
COMMENT ON COLUMN goods_category_platform_relations.created_at IS '创建时间戳（毫秒）';
COMMENT ON COLUMN goods_category_platform_relations.updated_at IS '更新时间戳（毫秒）';
COMMENT ON COLUMN goods_category_platform_relations.deleted_at IS '删除时间戳（毫秒，软删除）';
COMMENT ON COLUMN goods_category_platform_relations.created_by IS '创建人ID';
COMMENT ON COLUMN goods_category_platform_relations.updated_by IS '最后更新人ID';
