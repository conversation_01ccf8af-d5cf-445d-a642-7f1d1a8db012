-- 创建第三方商品分类表
CREATE TABLE IF NOT EXISTS third_party_goods_categories (
    id BIGINT PRIMARY KEY,
    parent_id BIGINT,
    name VARCHAR(255) NOT NULL,
    image_url VARCHAR(500),
    description TEXT,
    meta_title VARCHAR(255),
    meta_keywords VARCHAR(500),
    meta_description TEXT,
    sort_order INTEGER DEFAULT 0,
    is_enabled INTEGER DEFAULT 1,
    level INTEGER DEFAULT 1,
    source_type INTEGER DEFAULT 1,
    created_channel_id BIGINT NOT NULL,
    created_platform_id BIGINT NOT NULL,
    created_store_id BIGINT NOT NULL,
    created_at BIGINT DEFAULT (extract(epoch from now()) * 1000),
    updated_at BIGINT DEFAULT (extract(epoch from now()) * 1000),
    deleted_at BIGINT,
    created_by BIGINT,
    updated_by BIGINT,
    
    -- 外键约束
    CONSTRAINT fk_third_party_goods_categories_parent 
        FOREIGN KEY (parent_id) REFERENCES third_party_goods_categories(id) 
        ON DELETE NO ACTION ON UPDATE NO ACTION
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_deleted_at ON third_party_goods_categories(deleted_at);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_parent_id ON third_party_goods_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_is_enabled ON third_party_goods_categories(is_enabled);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_created_by ON third_party_goods_categories(created_by);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_updated_by ON third_party_goods_categories(updated_by);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_source_type ON third_party_goods_categories(source_type);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_created_channel_id ON third_party_goods_categories(created_channel_id);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_created_platform_id ON third_party_goods_categories(created_platform_id);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_created_store_id ON third_party_goods_categories(created_store_id);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_channel_platform_store ON third_party_goods_categories(created_channel_id, created_platform_id, created_store_id);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_sort_order ON third_party_goods_categories(sort_order);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_level ON third_party_goods_categories(level);
CREATE INDEX IF NOT EXISTS idx_third_party_goods_categories_created_at ON third_party_goods_categories(created_at);

-- 添加注释
COMMENT ON TABLE third_party_goods_categories IS '第三方商品分类表';
COMMENT ON COLUMN third_party_goods_categories.id IS '主键ID';
COMMENT ON COLUMN third_party_goods_categories.parent_id IS '父分类ID，用于构建分类树结构';
COMMENT ON COLUMN third_party_goods_categories.name IS '分类名称';
COMMENT ON COLUMN third_party_goods_categories.image_url IS '分类图片URL';
COMMENT ON COLUMN third_party_goods_categories.description IS '分类描述';
COMMENT ON COLUMN third_party_goods_categories.meta_title IS 'SEO标题';
COMMENT ON COLUMN third_party_goods_categories.meta_keywords IS 'SEO关键词';
COMMENT ON COLUMN third_party_goods_categories.meta_description IS 'SEO描述';
COMMENT ON COLUMN third_party_goods_categories.sort_order IS '排序顺序';
COMMENT ON COLUMN third_party_goods_categories.is_enabled IS '是否启用：1-启用，0-禁用';
COMMENT ON COLUMN third_party_goods_categories.level IS '分类层级：1-一级分类，2-二级分类，3-三级分类';
COMMENT ON COLUMN third_party_goods_categories.source_type IS '来源类型：固定为1-第三方分类';
COMMENT ON COLUMN third_party_goods_categories.created_channel_id IS '创建时的渠道ID';
COMMENT ON COLUMN third_party_goods_categories.created_platform_id IS '创建时的平台ID';
COMMENT ON COLUMN third_party_goods_categories.created_store_id IS '创建时的店铺ID';
COMMENT ON COLUMN third_party_goods_categories.created_at IS '创建时间戳（毫秒）';
COMMENT ON COLUMN third_party_goods_categories.updated_at IS '更新时间戳（毫秒）';
COMMENT ON COLUMN third_party_goods_categories.deleted_at IS '删除时间戳（毫秒，软删除）';
COMMENT ON COLUMN third_party_goods_categories.created_by IS '创建人ID';
COMMENT ON COLUMN third_party_goods_categories.updated_by IS '最后更新人ID';
