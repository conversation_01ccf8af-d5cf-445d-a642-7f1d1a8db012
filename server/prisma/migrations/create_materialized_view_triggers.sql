-- 创建物化视图刷新触发器
-- 当商品、品牌、分类关联发生变化时，触发物化视图刷新

-- 创建触发器函数
CREATE OR REPLACE FUNCTION "base"."trigger_materialized_view_refresh"()
RETURNS TRIGGER AS $$
DECLARE
    notification_payload TEXT;
BEGIN
    -- 构建通知载荷
    notification_payload := json_build_object(
        'table', TG_TABLE_NAME,
        'operation', TG_OP,
        'timestamp', EXTRACT(EPOCH FROM NOW()) * 1000
    )::TEXT;
    
    -- 发送异步通知
    -- 这里使用 PostgreSQL 的 NOTIFY 机制
    -- Node.js 应用可以监听这些通知并触发物化视图刷新
    PERFORM pg_notify('materialized_view_refresh', notification_payload);
    
    -- 记录触发日志（可选）
    INSERT INTO "base"."materialized_view_refresh_log" (
        table_name,
        operation,
        triggered_at,
        payload
    ) VALUES (
        TG_TABLE_NAME,
        TG_OP,
        EXTRACT(EPOCH FROM NOW()) * 1000,
        notification_payload
    );
    
    -- 返回适当的记录
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 创建刷新日志表（如果不存在）
CREATE TABLE IF NOT EXISTS "base"."materialized_view_refresh_log" (
    id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    operation VARCHAR(20) NOT NULL,
    triggered_at BIGINT NOT NULL,
    payload TEXT,
    created_at BIGINT NOT NULL DEFAULT EXTRACT(EPOCH FROM NOW()) * 1000
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_mv_refresh_log_table_name 
ON "base"."materialized_view_refresh_log" (table_name);

CREATE INDEX IF NOT EXISTS idx_mv_refresh_log_triggered_at 
ON "base"."materialized_view_refresh_log" (triggered_at);

-- 删除已存在的触发器（如果存在）
DROP TRIGGER IF EXISTS trigger_goods_spus_mv_refresh ON "base"."goods_spus";
DROP TRIGGER IF EXISTS trigger_goods_brands_mv_refresh ON "base"."goods_brands";
DROP TRIGGER IF EXISTS trigger_goods_category_associations_mv_refresh ON "base"."goods_category_associations";

-- 为 goods_spus 表创建触发器
-- 当商品信息发生变化时触发
CREATE TRIGGER trigger_goods_spus_mv_refresh
    AFTER INSERT OR UPDATE OR DELETE
    ON "base"."goods_spus"
    FOR EACH ROW
    EXECUTE FUNCTION "base"."trigger_materialized_view_refresh"();

-- 为 goods_brands 表创建触发器
-- 当品牌信息发生变化时触发
CREATE TRIGGER trigger_goods_brands_mv_refresh
    AFTER INSERT OR UPDATE OR DELETE
    ON "base"."goods_brands"
    FOR EACH ROW
    EXECUTE FUNCTION "base"."trigger_materialized_view_refresh"();

-- 为 goods_category_associations 表创建触发器
-- 当商品分类关联发生变化时触发
CREATE TRIGGER trigger_goods_category_associations_mv_refresh
    AFTER INSERT OR UPDATE OR DELETE
    ON "base"."goods_category_associations"
    FOR EACH ROW
    EXECUTE FUNCTION "base"."trigger_materialized_view_refresh"();

-- 创建清理旧日志的函数
CREATE OR REPLACE FUNCTION "base"."cleanup_materialized_view_refresh_log"()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
    cutoff_time BIGINT;
BEGIN
    -- 保留最近30天的日志
    cutoff_time := EXTRACT(EPOCH FROM NOW() - INTERVAL '30 days') * 1000;
    
    DELETE FROM "base"."materialized_view_refresh_log"
    WHERE triggered_at < cutoff_time;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建定时清理任务（需要 pg_cron 扩展，如果没有可以手动执行）
-- SELECT cron.schedule('cleanup-mv-refresh-log', '0 2 * * *', 'SELECT base.cleanup_materialized_view_refresh_log();');

COMMENT ON FUNCTION "base"."trigger_materialized_view_refresh"() IS '物化视图刷新触发器函数';
COMMENT ON FUNCTION "base"."cleanup_materialized_view_refresh_log"() IS '清理物化视图刷新日志函数';
COMMENT ON TABLE "base"."materialized_view_refresh_log" IS '物化视图刷新日志表';
