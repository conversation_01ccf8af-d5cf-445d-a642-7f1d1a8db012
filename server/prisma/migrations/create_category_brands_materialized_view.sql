-- 创建分类品牌物化视图
-- 统计各分类下的品牌及商品数量，按商品数量排序

-- 删除已存在的物化视图
DROP MATERIALIZED VIEW IF EXISTS "base"."category_brands_materialized_view";

-- 创建物化视图
CREATE MATERIALIZED VIEW "base"."category_brands_materialized_view" AS
SELECT 
    gca.goods_category_id,
    gs.goods_brand_id,
    gb.name as brand_name,
    gb.logo_url as brand_logo_url,
    gb.description as brand_description,
    COUNT(DISTINCT gs.id) as product_count,
    -- 添加一些统计信息
    SUM(gs.total_sales) as total_sales,
    SUM(gs.total_stock) as total_stock,
    -- 添加时间戳用于跟踪刷新时间
    EXTRACT(EPOCH FROM NOW()) * 1000 as last_updated
FROM "base"."goods_category_associations" gca
INNER JOIN "base"."goods_spus" gs ON gca.goods_spu_id = gs.id
INNER JOIN "base"."goods_brands" gb ON gs.goods_brand_id = gb.id
WHERE 
    gs.deleted_at IS NULL 
    AND gb.deleted_at IS NULL
    AND gs.status = 1  -- 只统计上架的商品
GROUP BY 
    gca.goods_category_id, 
    gs.goods_brand_id,
    gb.name,
    gb.logo_url,
    gb.description
ORDER BY 
    gca.goods_category_id,
    product_count DESC,
    total_sales DESC;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_category_brands_mv_category_id 
ON "base"."category_brands_materialized_view" (goods_category_id);

CREATE INDEX IF NOT EXISTS idx_category_brands_mv_brand_id 
ON "base"."category_brands_materialized_view" (goods_brand_id);

CREATE INDEX IF NOT EXISTS idx_category_brands_mv_product_count 
ON "base"."category_brands_materialized_view" (product_count DESC);

-- 创建复合索引
CREATE INDEX IF NOT EXISTS idx_category_brands_mv_category_product_count 
ON "base"."category_brands_materialized_view" (goods_category_id, product_count DESC);

-- 刷新物化视图
REFRESH MATERIALIZED VIEW "base"."category_brands_materialized_view";
