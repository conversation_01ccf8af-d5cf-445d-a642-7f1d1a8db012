/// 渠道信息表，存储系统渠道数据
model channel {
  id             BigInt  @id /// 渠道ID，雪花ID
  name           String  @db.VarChar(100) /// 渠道名称
  icon_url       String? @db.VarChar(255) /// 渠道图标URL
  is_built_in    Int     @default(0) @db.SmallInt /// 是否内置：1-是，0-否
  created_at     BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 创建时间（时间戳，毫秒）
  updated_at     BigInt  @default(dbgenerated("((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint")) /// 更新时间（时间戳，毫秒）
  deleted_at     BigInt? /// 删除时间（时间戳，毫秒），软删除标记
  created_by     BigInt? /// 创建者ID
  updated_by     BigInt? /// 更新者ID
  orders        orders[] /// 关联的订单
  goods_sku_channels goods_sku_channel[] /// 关联的商品SKU渠道

  @@map("channel")
  @@schema("base")
}
