-- 创建服务商平台费率表
CREATE TABLE IF NOT EXISTS provider.provider_platform_rate (
    id BIGINT PRIMARY KEY,
    provider_id BIGINT NOT NULL,
    channel_id BIGINT NOT NULL,
    rate DECIMAL(8,4) NOT NULL DEFAULT 0.0000,
    status INTEGER DEFAULT 1,
    created_by BIGIN<PERSON>,
    updated_by BIGINT,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    deleted_at BIGINT,

    -- 唯一约束：同一服务商同一渠道只能有一条费率记录
    CONSTRAINT unique_provider_channel_rate UNIQUE (provider_id, channel_id)
);

-- 添加表注释
COMMENT ON TABLE provider.provider_platform_rate IS '服务商平台费率表，存储服务商对各个平台渠道的费率设置';
COMMENT ON COLUMN provider.provider_platform_rate.id IS '费率记录ID，16位雪花算法，系统自动生成';
COMMENT ON COLUMN provider.provider_platform_rate.provider_id IS '服务商ID，关联provider_user表';
COMMENT ON COLUMN provider.provider_platform_rate.channel_id IS '渠道ID，关联base.channel表';

COMMENT ON COLUMN provider.provider_platform_rate.rate IS '费率，小数格式，如0.0500表示5%';
COMMENT ON COLUMN provider.provider_platform_rate.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN provider.provider_platform_rate.created_by IS '创建者ID';
COMMENT ON COLUMN provider.provider_platform_rate.updated_by IS '更新者ID';
COMMENT ON COLUMN provider.provider_platform_rate.created_at IS '创建时间戳（毫秒）';
COMMENT ON COLUMN provider.provider_platform_rate.updated_at IS '更新时间戳（毫秒）';
COMMENT ON COLUMN provider.provider_platform_rate.deleted_at IS '删除时间戳（毫秒），软删除标记';

-- 添加索引
CREATE INDEX idx_provider_platform_rate_provider_id ON provider.provider_platform_rate (provider_id);
CREATE INDEX idx_provider_platform_rate_channel_id ON provider.provider_platform_rate (channel_id);
CREATE INDEX idx_provider_platform_rate_status ON provider.provider_platform_rate (status);
CREATE INDEX idx_provider_platform_rate_created_at ON provider.provider_platform_rate (created_at);
