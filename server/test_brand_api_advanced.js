/**
 * 第三方商品品牌API高级测试脚本
 * 测试更复杂的场景和边界情况
 */

const { PrismaClient } = require('@prisma/client');
const ThirdPartyGoodsBrandService = require('./apps/master/services/ThirdPartyGoodsBrandService');

async function testAdvancedBrandAPI() {
  const prisma = new PrismaClient();
  const brandService = new ThirdPartyGoodsBrandService(prisma);

  try {
    console.log('🚀 开始高级测试第三方商品品牌API...\n');

    // 测试1: 创建只有必填字段的品牌
    console.log('🔥 测试1: 创建只有必填字段的品牌');
    const minimalBrandData = {
      name: '简单品牌',
      channelId: '194267216928182272',
      platformId: '194267326521151488', 
      storeId: '194270091980967936'
    };
    
    const minimalBrand = await brandService.createThirdPartyBrand(minimalBrandData);
    console.log('✅ 最小字段创建成功:', {
      id: minimalBrand.id,
      name: minimalBrand.name,
      logoUrl: minimalBrand.logoUrl,
      description: minimalBrand.description
    });

    // 测试2: 创建包含所有可选字段的品牌
    console.log('\n🔥 测试2: 创建包含所有可选字段的品牌');
    const fullBrandData = {
      name: '完整品牌Adidas',
      channelId: '194267216928182272',
      platformId: '194267565734891520', // 不同的平台
      storeId: '194267674442862592',    // 对应的店铺
      logoUrl: 'https://example.com/adidas-logo.png',
      description: '全球知名运动品牌Adidas，三条纹标志'
    };
    
    const fullBrand = await brandService.createThirdPartyBrand(fullBrandData);
    console.log('✅ 完整字段创建成功:', {
      id: fullBrand.id,
      name: fullBrand.name,
      logoUrl: fullBrand.logoUrl,
      description: fullBrand.description
    });

    // 测试3: 测试数据验证 - 无效的渠道ID
    console.log('\n🔥 测试3: 测试数据验证 - 无效的渠道ID');
    try {
      await brandService.createThirdPartyBrand({
        name: '无效渠道品牌',
        channelId: '999999999999999999',
        platformId: '194267326521151488',
        storeId: '194270091980967936'
      });
      console.log('❌ 应该抛出错误但没有');
    } catch (error) {
      console.log('✅ 正确捕获无效渠道错误:', error.message);
    }

    // 测试4: 测试数据验证 - 渠道平台不匹配
    console.log('\n🔥 测试4: 测试数据验证 - 渠道平台不匹配');
    try {
      await brandService.createThirdPartyBrand({
        name: '不匹配品牌',
        channelId: '185653478876647424', // 商城渠道
        platformId: '194267326521151488', // 自采平台
        storeId: '194270091980967936'
      });
      console.log('❌ 应该抛出错误但没有');
    } catch (error) {
      console.log('✅ 正确捕获渠道平台不匹配错误:', error.message);
    }

    // 测试5: 更新品牌 - 部分字段更新
    console.log('\n🔥 测试5: 更新品牌 - 部分字段更新');
    const partialUpdateData = {
      name: '部分更新的简单品牌'
    };
    
    const partialUpdatedBrand = await brandService.updateThirdPartyBrand(minimalBrand.id, partialUpdateData);
    console.log('✅ 部分更新成功:', {
      id: partialUpdatedBrand.id,
      name: partialUpdatedBrand.name,
      logoUrl: partialUpdatedBrand.logoUrl,
      description: partialUpdatedBrand.description
    });

    // 测试6: 更新品牌 - 清空可选字段
    console.log('\n🔥 测试6: 更新品牌 - 清空可选字段');
    const clearFieldsData = {
      logoUrl: '',
      description: ''
    };
    
    const clearedBrand = await brandService.updateThirdPartyBrand(fullBrand.id, clearFieldsData);
    console.log('✅ 清空字段成功:', {
      id: clearedBrand.id,
      name: clearedBrand.name,
      logoUrl: clearedBrand.logoUrl,
      description: clearedBrand.description
    });

    // 测试7: 验证多平台关联能力
    console.log('\n🔥 测试7: 验证多平台关联能力');
    const multiPlatformBrand = await brandService.createThirdPartyBrand({
      name: '多平台品牌Puma',
      channelId: '194267216928182272',
      platformId: '195370222180503552', // 第三个平台
      storeId: '195370375490703360',    // 对应店铺
      logoUrl: 'https://example.com/puma-logo.png',
      description: 'Puma运动品牌'
    });
    
    console.log('✅ 多平台品牌创建成功:', {
      id: multiPlatformBrand.id,
      name: multiPlatformBrand.name,
      channelId: multiPlatformBrand.createdChannelId,
      platformId: multiPlatformBrand.createdPlatformId,
      storeId: multiPlatformBrand.createdStoreId
    });

    // 测试8: 验证数据库中的关联记录
    console.log('\n🔥 测试8: 验证数据库中的关联记录');
    const relationCount = await prisma.goodsBrandPlatformRelation.count({
      where: {
        channel_id: BigInt('194267216928182272')
      }
    });
    console.log(`✅ 该渠道下共有 ${relationCount} 个品牌关联记录`);

    console.log('\n🎉 所有高级测试通过！第三方商品品牌API功能完全正常！');
    console.log('\n📊 测试总结:');
    console.log('  ✅ 必填字段创建');
    console.log('  ✅ 完整字段创建');
    console.log('  ✅ 数据验证（无效渠道）');
    console.log('  ✅ 数据验证（渠道平台不匹配）');
    console.log('  ✅ 部分字段更新');
    console.log('  ✅ 清空可选字段');
    console.log('  ✅ 多平台关联');
    console.log('  ✅ 数据库关联记录验证');

  } catch (error) {
    console.error('❌ 高级测试失败:', error.message);
    console.error('详细错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行高级测试
testAdvancedBrandAPI();
