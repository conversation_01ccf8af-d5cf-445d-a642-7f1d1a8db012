/**
 * 物化视图定时任务服务
 * 负责定时刷新物化视图
 */
const cron = require('node-cron');
const MaterializedViewRefreshService = require('./MaterializedViewRefreshService');

class MaterializedViewScheduleService {
  constructor() {
    this.cronJob = null;
    this.isRunning = false;
    this.refreshService = MaterializedViewRefreshService;
  }

  /**
   * 启动定时任务
   * 每天凌晨3点执行全局刷新
   */
  start() {
    if (this.cronJob) {
      console.log('[物化视图定时任务] 定时任务已在运行中');
      return;
    }

    console.log('[物化视图定时任务] 启动定时任务，每天凌晨3点执行');
    
    // 每天凌晨3点执行
    this.cronJob = cron.schedule('0 3 * * *', async () => {
      if (this.isRunning) {
        console.log('[物化视图定时任务] 上一次任务还在执行中，跳过本次执行');
        return;
      }

      this.isRunning = true;
      try {
        console.log('[物化视图定时任务] 开始执行定时刷新任务');
        await this.executeScheduledRefresh();
        console.log('[物化视图定时任务] 定时刷新任务执行完成');
      } catch (error) {
        console.error('[物化视图定时任务] 定时刷新任务执行失败:', error);
      } finally {
        this.isRunning = false;
      }
    }, {
      scheduled: true,
      timezone: 'Asia/Shanghai'
    });

    console.log('[物化视图定时任务] 定时任务启动成功');
  }

  /**
   * 停止定时任务
   */
  stop() {
    if (this.cronJob) {
      this.cronJob.destroy();
      this.cronJob = null;
      console.log('[物化视图定时任务] 定时任务已停止');
    }
  }

  /**
   * 执行定时刷新任务
   */
  async executeScheduledRefresh() {
    const startTime = Date.now();
    
    try {
      console.log('[物化视图定时任务] 开始全局刷新物化视图');
      
      // 执行全局刷新
      const success = await this.refreshService.globalRefresh();
      
      const duration = Date.now() - startTime;
      
      if (success) {
        console.log(`[物化视图定时任务] 全局刷新成功，耗时: ${duration}ms`);
      } else {
        console.log(`[物化视图定时任务] 全局刷新失败，耗时: ${duration}ms`);
      }
      
      return success;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[物化视图定时任务] 全局刷新异常，耗时: ${duration}ms`, error);
      throw error;
    }
  }

  /**
   * 手动执行一次刷新任务
   */
  async runOnce() {
    if (this.isRunning) {
      return {
        success: false,
        message: '定时刷新任务正在执行中，请稍后再试'
      };
    }

    this.isRunning = true;
    try {
      console.log('[物化视图定时任务] 手动执行刷新任务');
      const success = await this.executeScheduledRefresh();
      
      return {
        success,
        message: success ? '手动刷新任务执行成功' : '手动刷新任务执行失败'
      };
    } catch (error) {
      console.error('[物化视图定时任务] 手动执行刷新任务失败:', error);
      return {
        success: false,
        message: `执行失败: ${error.message}`
      };
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * 获取任务状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      hasSchedule: !!this.cronJob,
      nextRun: this.cronJob ? '每天凌晨3点' : null,
      refreshServiceStatus: this.refreshService.getRefreshStatus()
    };
  }

  /**
   * 重启定时任务
   */
  restart() {
    console.log('[物化视图定时任务] 重启定时任务');
    this.stop();
    this.start();
  }
}

// 创建单例
const materializedViewScheduleService = new MaterializedViewScheduleService();

module.exports = materializedViewScheduleService;
