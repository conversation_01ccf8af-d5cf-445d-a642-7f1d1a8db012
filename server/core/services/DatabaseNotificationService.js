/**
 * 数据库通知服务
 * 监听 PostgreSQL 的 NOTIFY 事件并触发相应的处理
 */
const { Client } = require('pg');
const MaterializedViewRefreshService = require('./MaterializedViewRefreshService');

class DatabaseNotificationService {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000; // 5秒
  }

  /**
   * 初始化并连接到数据库
   */
  async initialize() {
    try {
      console.log('[数据库通知] 初始化数据库通知服务...');
      
      // 创建专用的数据库连接用于监听通知
      this.client = new Client({
        host: process.env.DATABASE_HOST || '************',
        port: process.env.DATABASE_PORT || 5432,
        database: process.env.DATABASE_NAME || 'julingcloud5',
        user: process.env.DATABASE_USER || 'postgres',
        password: process.env.DATABASE_PASSWORD || '12342234',
      });

      // 连接到数据库
      await this.client.connect();
      this.isConnected = true;
      this.reconnectAttempts = 0;

      console.log('[数据库通知] 数据库连接成功');

      // 监听通知事件
      this.client.on('notification', this.handleNotification.bind(this));
      
      // 监听连接错误
      this.client.on('error', this.handleConnectionError.bind(this));
      
      // 监听连接结束
      this.client.on('end', this.handleConnectionEnd.bind(this));

      // 订阅物化视图刷新通知
      await this.client.query('LISTEN materialized_view_refresh');
      
      console.log('[数据库通知] 开始监听 materialized_view_refresh 通知');

    } catch (error) {
      console.error('[数据库通知] 初始化失败:', error);
      this.isConnected = false;
      
      // 尝试重连
      this.scheduleReconnect();
    }
  }

  /**
   * 处理数据库通知
   * @param {Object} notification 通知对象
   */
  async handleNotification(notification) {
    try {
      console.log('[数据库通知] 收到通知:', notification.channel);
      
      if (notification.channel === 'materialized_view_refresh') {
        let payload = {};
        
        try {
          payload = JSON.parse(notification.payload || '{}');
        } catch (parseError) {
          console.warn('[数据库通知] 解析通知载荷失败:', parseError);
        }

        console.log('[数据库通知] 通知详情:', {
          table: payload.table,
          operation: payload.operation,
          timestamp: payload.timestamp
        });

        // 触发物化视图刷新
        await MaterializedViewRefreshService.requestRefresh(
          `database_trigger_${payload.table}_${payload.operation}`
        );

      }
    } catch (error) {
      console.error('[数据库通知] 处理通知失败:', error);
    }
  }

  /**
   * 处理连接错误
   * @param {Error} error 错误对象
   */
  handleConnectionError(error) {
    console.error('[数据库通知] 连接错误:', error);
    this.isConnected = false;
    
    // 尝试重连
    this.scheduleReconnect();
  }

  /**
   * 处理连接结束
   */
  handleConnectionEnd() {
    console.log('[数据库通知] 连接已结束');
    this.isConnected = false;
    
    // 尝试重连
    this.scheduleReconnect();
  }

  /**
   * 调度重连
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('[数据库通知] 达到最大重连次数，停止重连');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * this.reconnectAttempts;

    console.log(`[数据库通知] 将在 ${delay}ms 后尝试第 ${this.reconnectAttempts} 次重连`);

    setTimeout(async () => {
      try {
        await this.reconnect();
      } catch (error) {
        console.error('[数据库通知] 重连失败:', error);
        this.scheduleReconnect();
      }
    }, delay);
  }

  /**
   * 重新连接
   */
  async reconnect() {
    console.log('[数据库通知] 尝试重新连接...');
    
    // 清理旧连接
    if (this.client) {
      try {
        await this.client.end();
      } catch (error) {
        // 忽略清理错误
      }
    }

    // 重新初始化
    await this.initialize();
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts
    };
  }

  /**
   * 手动发送测试通知
   */
  async sendTestNotification() {
    if (!this.isConnected || !this.client) {
      throw new Error('数据库连接未建立');
    }

    const payload = JSON.stringify({
      table: 'test',
      operation: 'TEST',
      timestamp: Date.now()
    });

    await this.client.query('SELECT pg_notify($1, $2)', ['materialized_view_refresh', payload]);
    console.log('[数据库通知] 发送测试通知成功');
  }

  /**
   * 停止服务
   */
  async stop() {
    console.log('[数据库通知] 停止数据库通知服务...');
    
    if (this.client) {
      try {
        await this.client.query('UNLISTEN materialized_view_refresh');
        await this.client.end();
      } catch (error) {
        console.error('[数据库通知] 停止服务时出错:', error);
      }
    }

    this.isConnected = false;
    this.client = null;
    
    console.log('[数据库通知] 数据库通知服务已停止');
  }
}

// 创建单例
const databaseNotificationService = new DatabaseNotificationService();

module.exports = databaseNotificationService;
