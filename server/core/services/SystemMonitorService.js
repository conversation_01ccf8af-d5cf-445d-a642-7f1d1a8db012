/**
 * 系统监控服务
 * 用于监控 CPU、内存、数据库负载等系统资源
 */
const os = require('os');
const { prisma } = require('../database/prisma');

class SystemMonitorService {
  constructor() {
    this.thresholds = {
      cpu: 80,      // CPU使用率阈值 80%
      memory: 85,   // 内存使用率阈值 85%
      database: 70  // 数据库负载阈值 70%
    };
  }

  /**
   * 获取CPU使用率
   * @returns {Promise<number>} CPU使用率百分比
   */
  async getCpuUsage() {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();
      
      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const cpuPercentage = 100 - ~~(100 * idleDifference / totalDifference);
        resolve(cpuPercentage);
      }, 1000);
    });
  }

  /**
   * 计算CPU平均值
   * @returns {Object} CPU统计信息
   */
  cpuAverage() {
    const cpus = os.cpus();
    let user = 0, nice = 0, sys = 0, idle = 0, irq = 0;
    
    for (let cpu of cpus) {
      user += cpu.times.user;
      nice += cpu.times.nice;
      sys += cpu.times.sys;
      idle += cpu.times.idle;
      irq += cpu.times.irq;
    }
    
    const total = user + nice + sys + idle + irq;
    return { idle, total };
  }

  /**
   * 获取内存使用率
   * @returns {number} 内存使用率百分比
   */
  getMemoryUsage() {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    return Math.round((usedMemory / totalMemory) * 100);
  }

  /**
   * 获取数据库负载
   * @returns {Promise<number>} 数据库负载百分比
   */
  async getDatabaseLoad() {
    try {
      // 查询数据库连接数和活跃查询数
      const result = await prisma.$queryRaw`
        SELECT 
          (SELECT count(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
          (SELECT setting::int FROM pg_settings WHERE name = 'max_connections') as max_connections,
          (SELECT count(*) FROM pg_stat_activity WHERE state != 'idle') as busy_connections
      `;
      
      if (result && result.length > 0) {
        const { active_connections, max_connections, busy_connections } = result[0];
        // 基于活跃连接数计算负载百分比
        const loadPercentage = Math.round((Number(busy_connections) / Number(max_connections)) * 100);
        return Math.min(loadPercentage, 100);
      }
      
      return 0;
    } catch (error) {
      console.error('获取数据库负载失败:', error);
      return 0;
    }
  }

  /**
   * 获取系统负载信息
   * @returns {Promise<Object>} 系统负载信息
   */
  async getSystemLoad() {
    const [cpuUsage, memoryUsage, databaseLoad] = await Promise.all([
      this.getCpuUsage(),
      Promise.resolve(this.getMemoryUsage()),
      this.getDatabaseLoad()
    ]);

    return {
      cpu: cpuUsage,
      memory: memoryUsage,
      database: databaseLoad,
      timestamp: Date.now()
    };
  }

  /**
   * 检查系统是否超出负载阈值
   * @returns {Promise<Object>} 检查结果
   */
  async checkSystemLoad() {
    const load = await this.getSystemLoad();
    
    const isOverloaded = 
      load.cpu > this.thresholds.cpu ||
      load.memory > this.thresholds.memory ||
      load.database > this.thresholds.database;

    return {
      ...load,
      isOverloaded,
      thresholds: this.thresholds,
      overloadedResources: {
        cpu: load.cpu > this.thresholds.cpu,
        memory: load.memory > this.thresholds.memory,
        database: load.database > this.thresholds.database
      }
    };
  }

  /**
   * 等待系统负载降低
   * @param {number} maxWaitTime 最大等待时间（毫秒）
   * @param {number} checkInterval 检查间隔（毫秒）
   * @returns {Promise<boolean>} 是否成功等待到负载降低
   */
  async waitForLowLoad(maxWaitTime = 30000, checkInterval = 5000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      const loadCheck = await this.checkSystemLoad();
      
      if (!loadCheck.isOverloaded) {
        console.log('系统负载已降低，可以执行操作');
        return true;
      }
      
      console.log(`系统负载过高，等待中... CPU: ${loadCheck.cpu}%, 内存: ${loadCheck.memory}%, 数据库: ${loadCheck.database}%`);
      
      // 等待指定间隔
      await new Promise(resolve => setTimeout(resolve, checkInterval));
    }
    
    console.log('等待超时，系统负载仍然过高');
    return false;
  }

  /**
   * 设置监控阈值
   * @param {Object} newThresholds 新的阈值配置
   */
  setThresholds(newThresholds) {
    this.thresholds = { ...this.thresholds, ...newThresholds };
  }

  /**
   * 获取系统信息
   * @returns {Object} 系统基本信息
   */
  getSystemInfo() {
    return {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      uptime: os.uptime(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpuCount: os.cpus().length,
      loadAverage: os.loadavg()
    };
  }
}

module.exports = SystemMonitorService;
