/**
 * 物化视图刷新服务
 * 实现事件驱动的物化视图刷新机制，支持系统负载监控
 */
const SystemMonitorService = require('./SystemMonitorService');
const { prisma } = require('../database/prisma');

class MaterializedViewRefreshService {
  constructor() {
    this.systemMonitor = new SystemMonitorService();
    this.refreshQueue = new Set(); // 使用Set避免重复刷新请求
    this.isRefreshing = false;
    this.refreshDelay = 5000; // 延迟5秒再执行检查
  }

  /**
   * 请求刷新分类品牌物化视图
   * @param {string} reason 刷新原因
   * @returns {Promise<boolean>} 是否成功添加到刷新队列
   */
  async requestRefresh(reason = 'manual') {
    console.log(`[物化视图刷新] 收到刷新请求，原因: ${reason}`);
    
    // 添加到刷新队列
    this.refreshQueue.add('category_brands_materialized_view');
    
    // 如果当前没有在刷新，启动刷新流程
    if (!this.isRefreshing) {
      this.scheduleRefresh();
    }
    
    return true;
  }

  /**
   * 调度刷新任务
   */
  scheduleRefresh() {
    console.log(`[物化视图刷新] 调度刷新任务，延迟 ${this.refreshDelay}ms`);
    
    setTimeout(async () => {
      await this.executeRefresh();
    }, this.refreshDelay);
  }

  /**
   * 执行刷新任务
   */
  async executeRefresh() {
    if (this.isRefreshing) {
      console.log('[物化视图刷新] 刷新任务正在执行中，跳过');
      return;
    }

    if (this.refreshQueue.size === 0) {
      console.log('[物化视图刷新] 刷新队列为空，跳过');
      return;
    }

    this.isRefreshing = true;
    
    try {
      console.log('[物化视图刷新] 开始执行刷新任务');
      
      // 检查系统负载
      const loadCheck = await this.systemMonitor.checkSystemLoad();
      
      if (loadCheck.isOverloaded) {
        console.log('[物化视图刷新] 系统负载过高，等待负载降低...');
        console.log(`当前负载: CPU ${loadCheck.cpu}%, 内存 ${loadCheck.memory}%, 数据库 ${loadCheck.database}%`);
        
        // 等待系统负载降低，最多等待30秒
        const canProceed = await this.systemMonitor.waitForLowLoad(30000, 5000);
        
        if (!canProceed) {
          console.log('[物化视图刷新] 等待超时，延迟执行刷新任务');
          // 重新调度，延迟更长时间
          setTimeout(() => {
            this.isRefreshing = false;
            this.scheduleRefresh();
          }, 30000); // 延迟30秒重试
          return;
        }
      }

      // 执行物化视图刷新
      await this.refreshMaterializedView();
      
      // 清空刷新队列
      this.refreshQueue.clear();
      
      console.log('[物化视图刷新] 刷新任务执行完成');
      
    } catch (error) {
      console.error('[物化视图刷新] 刷新任务执行失败:', error);
      
      // 失败后重新调度，延迟更长时间
      setTimeout(() => {
        this.isRefreshing = false;
        this.scheduleRefresh();
      }, 60000); // 延迟1分钟重试
      return;
    } finally {
      this.isRefreshing = false;
    }
  }

  /**
   * 刷新物化视图
   */
  async refreshMaterializedView() {
    const startTime = Date.now();
    console.log('[物化视图刷新] 开始刷新 category_brands_materialized_view');
    
    try {
      // 刷新物化视图
      await prisma.$executeRaw`REFRESH MATERIALIZED VIEW "base"."category_brands_materialized_view"`;
      
      const duration = Date.now() - startTime;
      console.log(`[物化视图刷新] 物化视图刷新完成，耗时: ${duration}ms`);
      
      // 记录刷新日志
      await this.logRefresh('category_brands_materialized_view', 'success', duration);
      
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`[物化视图刷新] 物化视图刷新失败，耗时: ${duration}ms`, error);
      
      // 记录失败日志
      await this.logRefresh('category_brands_materialized_view', 'failed', duration, error.message);
      
      throw error;
    }
  }

  /**
   * 记录刷新日志
   * @param {string} viewName 视图名称
   * @param {string} status 状态
   * @param {number} duration 耗时
   * @param {string} errorMessage 错误信息
   */
  async logRefresh(viewName, status, duration, errorMessage = null) {
    try {
      // 这里可以记录到数据库或日志文件
      const logEntry = {
        view_name: viewName,
        status,
        duration,
        error_message: errorMessage,
        timestamp: Date.now()
      };
      
      console.log('[物化视图刷新] 刷新日志:', logEntry);
      
      // 如果需要持久化日志，可以在这里添加数据库操作
      // await prisma.materializedViewRefreshLog.create({ data: logEntry });
      
    } catch (error) {
      console.error('[物化视图刷新] 记录刷新日志失败:', error);
    }
  }

  /**
   * 全局刷新所有物化视图
   * 用于定时任务
   */
  async globalRefresh() {
    console.log('[物化视图刷新] 开始全局刷新');
    
    try {
      // 获取系统负载信息
      const loadInfo = await this.systemMonitor.getSystemLoad();
      console.log(`[物化视图刷新] 当前系统负载: CPU ${loadInfo.cpu}%, 内存 ${loadInfo.memory}%, 数据库 ${loadInfo.database}%`);
      
      // 全局刷新不受负载限制，但会记录负载信息
      await this.refreshMaterializedView();
      
      console.log('[物化视图刷新] 全局刷新完成');
      return true;
      
    } catch (error) {
      console.error('[物化视图刷新] 全局刷新失败:', error);
      return false;
    }
  }

  /**
   * 获取刷新状态
   */
  getRefreshStatus() {
    return {
      isRefreshing: this.isRefreshing,
      queueSize: this.refreshQueue.size,
      queueItems: Array.from(this.refreshQueue)
    };
  }

  /**
   * 设置刷新延迟时间
   * @param {number} delay 延迟时间（毫秒）
   */
  setRefreshDelay(delay) {
    this.refreshDelay = delay;
  }
}

// 创建单例
const materializedViewRefreshService = new MaterializedViewRefreshService();

module.exports = materializedViewRefreshService;
