/**
 * 分类品牌服务
 * 负责查询分类下的品牌信息
 */
const DecimalUtils = require('../../../core/utils/DecimalUtils');

class CategoryBrandService {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取指定分类下的品牌列表
   * @param {Object} options 查询选项
   * @param {string} options.categoryId 分类ID
   * @param {number} options.page 页码，默认1
   * @param {number} options.pageSize 每页数量，默认10
   * @returns {Promise<Object>} 品牌列表和分页信息
   */
  async getCategoryBrands(options) {
    try {
      const { categoryId, page = 1, pageSize = 10 } = options;
      
      // 参数验证
      if (!categoryId) {
        return {
          code: 400,
          message: '分类ID不能为空',
          data: null
        };
      }

      // 转换分页参数
      const currentPage = Math.max(1, parseInt(page, 10) || 1);
      const limit = Math.min(Math.max(1, parseInt(pageSize, 10) || 10), 100); // 限制最大100条
      const offset = (currentPage - 1) * limit;

      console.log(`[分类品牌查询] 查询分类 ${categoryId} 的品牌，页码: ${currentPage}, 每页: ${limit}`);

      // 查询物化视图中的数据
      const [brands, totalCount] = await Promise.all([
        // 查询品牌列表
        this.prisma.$queryRaw`
          SELECT 
            goods_brand_id,
            brand_name,
            brand_logo_url,
            brand_description,
            product_count,
            total_sales,
            total_stock
          FROM "base"."category_brands_materialized_view"
          WHERE goods_category_id = ${BigInt(categoryId)}
          ORDER BY product_count DESC, total_sales DESC
          LIMIT ${limit} OFFSET ${offset}
        `,
        // 查询总数
        this.prisma.$queryRaw`
          SELECT COUNT(*) as total
          FROM "base"."category_brands_materialized_view"
          WHERE goods_category_id = ${BigInt(categoryId)}
        `
      ]);

      const total = totalCount[0] ? Number(totalCount[0].total) : 0;
      
      console.log(`[分类品牌查询] 查询结果: 总数 ${total}, 当前页 ${brands.length} 条`);

      // 处理数据格式
      const processedBrands = brands.map(brand => ({
        id: DecimalUtils.handleBigInt(brand.goods_brand_id),
        name: brand.brand_name,
        logoUrl: brand.brand_logo_url,
        description: brand.brand_description,
        productCount: Number(brand.product_count),
        totalSales: Number(brand.total_sales || 0),
        totalStock: Number(brand.total_stock || 0)
      }));

      // 计算分页信息
      const totalPages = Math.ceil(total / limit);

      return {
        code: 200,
        message: '获取分类品牌列表成功',
        data: {
          list: processedBrands,
          pagination: {
            page: currentPage,
            pageSize: limit,
            total,
            totalPages,
            hasNext: currentPage < totalPages,
            hasPrev: currentPage > 1
          }
        }
      };

    } catch (error) {
      console.error('[分类品牌查询] 查询失败:', error);
      return {
        code: 500,
        message: '获取分类品牌列表失败',
        data: null
      };
    }
  }

  /**
   * 获取分类品牌统计信息
   * @param {string} categoryId 分类ID
   * @returns {Promise<Object>} 统计信息
   */
  async getCategoryBrandStats(categoryId) {
    try {
      if (!categoryId) {
        return {
          code: 400,
          message: '分类ID不能为空',
          data: null
        };
      }

      console.log(`[分类品牌统计] 查询分类 ${categoryId} 的品牌统计`);

      // 查询统计信息
      const stats = await this.prisma.$queryRaw`
        SELECT 
          COUNT(*) as brand_count,
          SUM(product_count) as total_products,
          SUM(total_sales) as total_sales,
          SUM(total_stock) as total_stock,
          AVG(product_count) as avg_products_per_brand,
          MAX(last_updated) as last_updated
        FROM "base"."category_brands_materialized_view"
        WHERE goods_category_id = ${BigInt(categoryId)}
      `;

      if (!stats || stats.length === 0) {
        return {
          code: 404,
          message: '该分类下暂无品牌数据',
          data: null
        };
      }

      const stat = stats[0];
      
      return {
        code: 200,
        message: '获取分类品牌统计成功',
        data: {
          brandCount: Number(stat.brand_count || 0),
          totalProducts: Number(stat.total_products || 0),
          totalSales: Number(stat.total_sales || 0),
          totalStock: Number(stat.total_stock || 0),
          avgProductsPerBrand: Number(stat.avg_products_per_brand || 0),
          lastUpdated: Number(stat.last_updated || 0)
        }
      };

    } catch (error) {
      console.error('[分类品牌统计] 查询失败:', error);
      return {
        code: 500,
        message: '获取分类品牌统计失败',
        data: null
      };
    }
  }

  /**
   * 检查物化视图是否存在
   * @returns {Promise<boolean>} 是否存在
   */
  async checkMaterializedViewExists() {
    try {
      const result = await this.prisma.$queryRaw`
        SELECT EXISTS (
          SELECT 1 
          FROM information_schema.tables 
          WHERE table_schema = 'base' 
          AND table_name = 'category_brands_materialized_view'
        ) as exists
      `;
      
      return result[0]?.exists || false;
    } catch (error) {
      console.error('[分类品牌服务] 检查物化视图失败:', error);
      return false;
    }
  }

  /**
   * 获取物化视图最后更新时间
   * @returns {Promise<number|null>} 最后更新时间戳
   */
  async getLastUpdateTime() {
    try {
      const result = await this.prisma.$queryRaw`
        SELECT MAX(last_updated) as last_updated
        FROM "base"."category_brands_materialized_view"
      `;
      
      return result[0]?.last_updated ? Number(result[0].last_updated) : null;
    } catch (error) {
      console.error('[分类品牌服务] 获取最后更新时间失败:', error);
      return null;
    }
  }
}

module.exports = CategoryBrandService;
