/**
 * 商城轮播图服务类 - 面向前端用户的公共接口
 */
class BannerService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取启用状态的轮播图列表
   * @param {Object} params 查询参数
   * @returns {Array} 轮播图列表
   */
  async getEnabledBanners(params = {}) {
    const { limit = 5 } = params;
    
    // 查询启用状态的轮播图
    const banners = await this.prisma.MallBanner.findMany({
      where: { 
        status: 1,  // 启用状态
        deleted_at: null  // 未删除
      },
      orderBy: [
        { sort_order: 'asc' },  // 首先按排序字段升序
        { created_at: 'desc' }  // 然后按创建时间降序
      ],
      take: parseInt(limit)
    });

    // 转换为驼峰命名
    return this.convertToCamelCase(banners);
  }

  /**
   * 将下划线命名转换为驼峰命名
   * @param {Object} obj 要转换的对象
   * @returns {Object} 转换后的对象
   */
  convertToCamelCase(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.convertToCamelCase(item));
    }

    const camelCaseObj = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        camelCaseObj[camelKey] = this.convertToCamelCase(obj[key]);
      }
    }
    return camelCaseObj;
  }
}

module.exports = BannerService;
