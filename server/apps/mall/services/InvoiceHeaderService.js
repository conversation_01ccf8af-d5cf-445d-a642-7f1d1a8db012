/**
 * 商城发票抬头服务
 */
const InvoiceHeaderModel = require('../models/InvoiceHeaderModel');
const InvoiceHeaderTypeEnum = require('../../master/constants/InvoiceHeaderTypeEnum');

class InvoiceHeaderService {
  constructor() {
    this.invoiceHeaderModel = new InvoiceHeaderModel();
  }

  /**
   * 获取用户的发票抬头列表
   * @param {string|number|BigInt} userId - 用户ID
   * @returns {Promise<Object>} 服务响应
   */
  async getUserHeaders(userId) {
    try {
      if (!userId) {
        return {
          success: false,
          message: '用户ID不能为空',
          data: null
        };
      }

      const headers = await this.invoiceHeaderModel.getUserHeaders(userId);
      
      return {
        success: true,
        message: '获取发票抬头列表成功',
        data: headers
      };
    } catch (error) {
      console.error('获取用户发票抬头列表失败:', error);
      return {
        success: false,
        message: '获取发票抬头列表失败',
        data: null
      };
    }
  }

  /**
   * 创建发票抬头
   * @param {Object} headerData - 抬头数据
   * @returns {Promise<Object>} 服务响应
   */
  async createHeader(headerData) {
    try {
      const { userId, headerType, headerName, taxNumber, companyAddress, companyPhone, bankName, bankAccount, isDefault } = headerData;

      // 验证必填字段
      if (!userId || !headerType || !headerName) {
        return {
          success: false,
          message: '用户ID、抬头类型和抬头名称不能为空',
          data: null
        };
      }

      // 验证抬头类型
      if (!InvoiceHeaderTypeEnum.isValidType(headerType)) {
        return {
          success: false,
          message: '无效的发票抬头类型',
          data: null
        };
      }

      // 企业发票必须填写抬头名称和纳税人识别号
      if (headerType === InvoiceHeaderTypeEnum.COMPANY) {
        if (!headerName) {
          return {
            success: false,
            message: '企业发票抬头名称不能为空',
            data: null
          };
        }
        if (!taxNumber) {
          return {
            success: false,
            message: '企业发票必须填写纳税人识别号',
            data: null
          };
        }
      }

      const header = await this.invoiceHeaderModel.createHeader({
        userId,
        headerType,
        headerName,
        taxNumber: headerType === 2 ? taxNumber : null,
        companyAddress: headerType === 2 ? companyAddress : null,
        companyPhone: headerType === 2 ? companyPhone : null,
        bankName: headerType === 2 ? bankName : null,
        bankAccount: headerType === 2 ? bankAccount : null,
        isDefault: isDefault || false,
        createdBy: userId // 创建人就是当前用户
      });

      return {
        success: true,
        message: '创建发票抬头成功',
        data: header
      };
    } catch (error) {
      console.error('创建发票抬头失败:', error);
      return {
        success: false,
        message: '创建发票抬头失败',
        data: null
      };
    }
  }

  /**
   * 更新发票抬头
   * @param {string|number|BigInt} id - 抬头ID
   * @param {Object} headerData - 更新数据
   * @param {string|number|BigInt} updatedBy - 更新人ID
   * @returns {Promise<Object>} 服务响应
   */
  async updateHeader(id, headerData, updatedBy = null) {
    try {
      if (!id) {
        return {
          success: false,
          message: '抬头ID不能为空',
          data: null
        };
      }

      const { headerType, headerName, taxNumber, companyAddress, companyPhone, bankName, bankAccount, isDefault } = headerData;

      // 验证必填字段
      if (!headerType || !headerName) {
        return {
          success: false,
          message: '抬头类型和抬头名称不能为空',
          data: null
        };
      }

      // 企业发票必须填写抬头名称和纳税人识别号
      if (headerType === 2) {
        if (!headerName) {
          return {
            success: false,
            message: '企业发票抬头名称不能为空',
            data: null
          };
        }
        if (!taxNumber) {
          return {
            success: false,
            message: '企业发票必须填写纳税人识别号',
            data: null
          };
        }
      }

      const header = await this.invoiceHeaderModel.updateHeader(id, {
        headerType,
        headerName,
        taxNumber: headerType === 2 ? taxNumber : null,
        companyAddress: headerType === 2 ? companyAddress : null,
        companyPhone: headerType === 2 ? companyPhone : null,
        bankName: headerType === 2 ? bankName : null,
        bankAccount: headerType === 2 ? bankAccount : null,
        isDefault: isDefault || false,
        updatedBy: updatedBy
      });

      if (!header) {
        return {
          success: false,
          message: '发票抬头不存在',
          data: null
        };
      }

      return {
        success: true,
        message: '更新发票抬头成功',
        data: header
      };
    } catch (error) {
      console.error('更新发票抬头失败:', error);
      return {
        success: false,
        message: '更新发票抬头失败',
        data: null
      };
    }
  }

  /**
   * 删除发票抬头
   * @param {string|number|BigInt} id - 抬头ID
   * @returns {Promise<Object>} 服务响应
   */
  async deleteHeader(id) {
    try {
      if (!id) {
        return {
          success: false,
          message: '抬头ID不能为空',
          data: null
        };
      }

      const success = await this.invoiceHeaderModel.deleteHeader(id);

      if (!success) {
        return {
          success: false,
          message: '发票抬头不存在',
          data: null
        };
      }

      return {
        success: true,
        message: '删除发票抬头成功',
        data: null
      };
    } catch (error) {
      console.error('删除发票抬头失败:', error);
      return {
        success: false,
        message: '删除发票抬头失败',
        data: null
      };
    }
  }

  /**
   * 设置默认抬头
   * @param {string|number|BigInt} id - 抬头ID
   * @param {string|number|BigInt} userId - 用户ID
   * @returns {Promise<Object>} 服务响应
   */
  async setDefaultHeader(id, userId) {
    try {
      if (!id || !userId) {
        return {
          success: false,
          message: '抬头ID和用户ID不能为空',
          data: null
        };
      }

      const success = await this.invoiceHeaderModel.setDefaultHeader(id, userId);

      if (!success) {
        return {
          success: false,
          message: '设置默认抬头失败',
          data: null
        };
      }

      return {
        success: true,
        message: '设置默认抬头成功',
        data: null
      };
    } catch (error) {
      console.error('设置默认抬头失败:', error);
      return {
        success: false,
        message: '设置默认抬头失败',
        data: null
      };
    }
  }

  /**
   * 根据ID获取发票抬头
   * @param {string|number|BigInt} id - 抬头ID
   * @returns {Promise<Object>} 服务响应
   */
  async getHeaderById(id) {
    try {
      if (!id) {
        return {
          success: false,
          message: '抬头ID不能为空',
          data: null
        };
      }

      const header = await this.invoiceHeaderModel.getHeaderById(id);

      if (!header) {
        return {
          success: false,
          message: '发票抬头不存在',
          data: null
        };
      }

      return {
        success: true,
        message: '获取发票抬头成功',
        data: header
      };
    } catch (error) {
      console.error('获取发票抬头失败:', error);
      return {
        success: false,
        message: '获取发票抬头失败',
        data: null
      };
    }
  }
}

module.exports = InvoiceHeaderService;
