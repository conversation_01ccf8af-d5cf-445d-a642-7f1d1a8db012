/**
 * 商城发票抬头模型
 */
const { prisma } = require('../../../core/database/prisma');
const { recursiveSnakeToCamel } = require('../../../shared/utils/format');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

class InvoiceHeaderModel {
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 确保 BigInt 类型
   * @param {string|number|BigInt} id 
   * @returns {BigInt}
   */
  ensureBigInt(id) {
    if (typeof id === 'bigint') return id;
    if (typeof id === 'string' || typeof id === 'number') {
      return BigInt(id);
    }
    throw new Error('Invalid ID type');
  }

  /**
   * 获取用户的发票抬头列表
   * @param {string|number|BigInt} userId - 用户ID
   * @returns {Promise<Array>} 发票抬头列表
   */
  async getUserHeaders(userId) {
    try {
      const userIdBigInt = this.ensureBigInt(userId);
      
      const headers = await this.prisma.$queryRaw`
        SELECT * FROM base.mall_invoice_headers 
        WHERE user_id = ${userIdBigInt} AND deleted_at IS NULL
        ORDER BY is_default DESC, created_at DESC
      `;
      
      return recursiveSnakeToCamel(handleBigInt(headers));
    } catch (error) {
      console.error('获取用户发票抬头失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取发票抬头
   * @param {string|number|BigInt} id - 抬头ID
   * @returns {Promise<Object|null>} 发票抬头信息
   */
  async getHeaderById(id) {
    try {
      const idBigInt = this.ensureBigInt(id);
      
      const headers = await this.prisma.$queryRaw`
        SELECT * FROM base.mall_invoice_headers 
        WHERE id = ${idBigInt} AND deleted_at IS NULL
      `;
      
      const result = recursiveSnakeToCamel(handleBigInt(headers));
      return result.length > 0 ? result[0] : null;
    } catch (error) {
      console.error('获取发票抬头失败:', error);
      throw error;
    }
  }

  /**
   * 创建发票抬头
   * @param {Object} headerData - 抬头数据
   * @returns {Promise<Object>} 创建的抬头信息
   */
  async createHeader(headerData) {
    try {
      console.log('=== createHeader 调试信息 ===');
      console.log('原始 headerData:', JSON.stringify(headerData, null, 2));

      const {
        userId,
        headerType,
        headerName,
        taxNumber,
        companyAddress,
        companyPhone,
        bankName,
        bankAccount,
        isDefault = false,
        createdBy = null
      } = headerData;

      console.log('解构后的字段:');
      console.log('companyAddress:', companyAddress, 'type:', typeof companyAddress);
      console.log('companyPhone:', companyPhone, 'type:', typeof companyPhone);
      console.log('bankName:', bankName, 'type:', typeof bankName);
      console.log('bankAccount:', bankAccount, 'type:', typeof bankAccount);

      const userIdBigInt = this.ensureBigInt(userId);
      const createdByBigInt = createdBy ? this.ensureBigInt(createdBy) : userIdBigInt;
      const now = BigInt(Date.now());

      // 处理可选字段，确保空字符串转为null
      const companyAddressValue = companyAddress && companyAddress.trim() ? companyAddress : null;
      const companyPhoneValue = companyPhone && companyPhone.trim() ? companyPhone : null;
      const bankNameValue = bankName && bankName.trim() ? bankName : null;
      const bankAccountValue = bankAccount && bankAccount.trim() ? bankAccount : null;

      console.log('处理后的字段值:');
      console.log('companyAddressValue:', companyAddressValue);
      console.log('companyPhoneValue:', companyPhoneValue);
      console.log('bankNameValue:', bankNameValue);
      console.log('bankAccountValue:', bankAccountValue);

      // 如果设置为默认，先取消其他默认抬头
      if (isDefault) {
        await this.prisma.$executeRaw`
          UPDATE base.mall_invoice_headers
          SET is_default = false, updated_at = ${now}, updated_by = ${createdByBigInt}
          WHERE user_id = ${userIdBigInt} AND deleted_at IS NULL
        `;
      }

      // 先插入数据
      await this.prisma.$executeRaw`
        INSERT INTO base.mall_invoice_headers
        (user_id, header_type, header_name, tax_number, company_address, company_phone, bank_name, bank_account, is_default, created_at, updated_at, created_by, updated_by)
        VALUES (${userIdBigInt}, ${headerType}, ${headerName}, ${taxNumber}, ${companyAddressValue}, ${companyPhoneValue}, ${bankNameValue}, ${bankAccountValue}, ${isDefault}, ${now}, ${now}, ${createdByBigInt}, ${createdByBigInt})
      `;

      // 然后查询插入的数据
      const result = await this.prisma.$queryRaw`
        SELECT * FROM base.mall_invoice_headers
        WHERE user_id = ${userIdBigInt} AND created_at = ${now}
        ORDER BY created_at DESC LIMIT 1
      `;

      return recursiveSnakeToCamel(handleBigInt(result))[0];
    } catch (error) {
      console.error('创建发票抬头失败:', error);
      throw error;
    }
  }

  /**
   * 更新发票抬头
   * @param {string|number|BigInt} id - 抬头ID
   * @param {Object} headerData - 更新数据
   * @returns {Promise<Object>} 更新后的抬头信息
   */
  async updateHeader(id, headerData) {
    try {
      const idBigInt = this.ensureBigInt(id);
      const {
        headerType,
        headerName,
        taxNumber,
        companyAddress,
        companyPhone,
        bankName,
        bankAccount,
        isDefault,
        updatedBy = null
      } = headerData;

      const now = BigInt(Date.now());
      const updatedByBigInt = updatedBy ? this.ensureBigInt(updatedBy) : null;

      // 处理可选字段，确保空字符串转为null
      const companyAddressValue = companyAddress && companyAddress.trim() ? companyAddress : null;
      const companyPhoneValue = companyPhone && companyPhone.trim() ? companyPhone : null;
      const bankNameValue = bankName && bankName.trim() ? bankName : null;
      const bankAccountValue = bankAccount && bankAccount.trim() ? bankAccount : null;

      // 如果设置为默认，先取消其他默认抬头
      if (isDefault) {
        const header = await this.getHeaderById(id);
        if (header) {
          await this.prisma.$executeRaw`
            UPDATE base.mall_invoice_headers
            SET is_default = false, updated_at = ${now}, updated_by = ${updatedByBigInt}
            WHERE user_id = ${this.ensureBigInt(header.userId)} AND id != ${idBigInt} AND deleted_at IS NULL
          `;
        }
      }

      const result = await this.prisma.$queryRaw`
        UPDATE base.mall_invoice_headers
        SET header_type = ${headerType},
            header_name = ${headerName},
            tax_number = ${taxNumber},
            company_address = ${companyAddressValue},
            company_phone = ${companyPhoneValue},
            bank_name = ${bankNameValue},
            bank_account = ${bankAccountValue},
            is_default = ${isDefault},
            updated_at = ${now},
            updated_by = ${updatedByBigInt}
        WHERE id = ${idBigInt} AND deleted_at IS NULL
        RETURNING *
      `;

      const updatedHeaders = recursiveSnakeToCamel(handleBigInt(result));
      return updatedHeaders.length > 0 ? updatedHeaders[0] : null;
    } catch (error) {
      console.error('更新发票抬头失败:', error);
      throw error;
    }
  }

  /**
   * 删除发票抬头（软删除）
   * @param {string|number|BigInt} id - 抬头ID
   * @param {string|number|BigInt} deletedBy - 删除人ID（可选）
   * @returns {Promise<boolean>} 删除是否成功
   */
  async deleteHeader(id, deletedBy = null) {
    try {
      const idBigInt = this.ensureBigInt(id);
      const deletedByBigInt = deletedBy ? this.ensureBigInt(deletedBy) : null;
      const now = BigInt(Date.now());

      const result = await this.prisma.$executeRaw`
        UPDATE base.mall_invoice_headers
        SET deleted_at = ${now}, updated_at = ${now}, updated_by = ${deletedByBigInt}
        WHERE id = ${idBigInt} AND deleted_at IS NULL
      `;

      return result > 0;
    } catch (error) {
      console.error('删除发票抬头失败:', error);
      throw error;
    }
  }

  /**
   * 设置默认抬头
   * @param {string|number|BigInt} id - 抬头ID
   * @param {string|number|BigInt} userId - 用户ID
   * @param {string|number|BigInt} updatedBy - 更新人ID（可选）
   * @returns {Promise<boolean>} 设置是否成功
   */
  async setDefaultHeader(id, userId, updatedBy = null) {
    try {
      const idBigInt = this.ensureBigInt(id);
      const userIdBigInt = this.ensureBigInt(userId);
      const updatedByBigInt = updatedBy ? this.ensureBigInt(updatedBy) : userIdBigInt;
      const now = BigInt(Date.now());

      // 先取消所有默认抬头
      await this.prisma.$executeRaw`
        UPDATE base.mall_invoice_headers
        SET is_default = false, updated_at = ${now}, updated_by = ${updatedByBigInt}
        WHERE user_id = ${userIdBigInt} AND deleted_at IS NULL
      `;

      // 设置指定抬头为默认
      const result = await this.prisma.$executeRaw`
        UPDATE base.mall_invoice_headers
        SET is_default = true, updated_at = ${now}, updated_by = ${updatedByBigInt}
        WHERE id = ${idBigInt} AND user_id = ${userIdBigInt} AND deleted_at IS NULL
      `;

      return result > 0;
    } catch (error) {
      console.error('设置默认抬头失败:', error);
      throw error;
    }
  }
}

module.exports = InvoiceHeaderModel;
