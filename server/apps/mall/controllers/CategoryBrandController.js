/**
 * 分类品牌控制器
 * 负责处理分类品牌相关的HTTP请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const CategoryBrandService = require('../services/CategoryBrandService');

/**
 * 分类品牌控制器类
 */
class CategoryBrandController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.categoryBrandService = new CategoryBrandService(prisma);
  }

  /**
   * 获取指定分类下的品牌列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getCategoryBrands(req, res) {
    try {
      const { categoryid: categoryId, page = 1, pageSize = 10 } = req.query;

      // 参数验证
      if (!categoryId) {
        return this.fail(res, '分类ID不能为空', null, 400);
      }

      // 验证分类ID格式
      if (!/^\d+$/.test(categoryId)) {
        return this.fail(res, '分类ID格式不正确', null, 400);
      }

      // 验证分页参数
      const pageNum = parseInt(page, 10);
      const pageSizeNum = parseInt(pageSize, 10);

      if (isNaN(pageNum) || pageNum < 1) {
        return this.fail(res, '页码必须是大于0的整数', null, 400);
      }

      if (isNaN(pageSizeNum) || pageSizeNum < 1 || pageSizeNum > 100) {
        return this.fail(res, '每页数量必须是1-100之间的整数', null, 400);
      }

      console.log(`[分类品牌接口] 查询分类 ${categoryId} 的品牌列表，页码: ${pageNum}, 每页: ${pageSizeNum}`);

      // 调用服务层方法
      const result = await this.categoryBrandService.getCategoryBrands({
        categoryId,
        page: pageNum,
        pageSize: pageSizeNum
      });

      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, null, result.code);
      }

    } catch (error) {
      console.error('[分类品牌接口] 获取分类品牌列表失败:', error);
      return this.fail(res, '获取分类品牌列表失败', null, 500);
    }
  }

  /**
   * 获取分类品牌统计信息
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getCategoryBrandStats(req, res) {
    try {
      const { categoryid: categoryId } = req.query;

      // 参数验证
      if (!categoryId) {
        return this.fail(res, '分类ID不能为空', null, 400);
      }

      // 验证分类ID格式
      if (!/^\d+$/.test(categoryId)) {
        return this.fail(res, '分类ID格式不正确', null, 400);
      }

      console.log(`[分类品牌统计] 查询分类 ${categoryId} 的品牌统计`);

      // 调用服务层方法
      const result = await this.categoryBrandService.getCategoryBrandStats(categoryId);

      if (result.code === 200) {
        return this.success(res, result.data, result.message);
      } else {
        return this.fail(res, result.message, null, result.code);
      }

    } catch (error) {
      console.error('[分类品牌统计] 获取分类品牌统计失败:', error);
      return this.fail(res, '获取分类品牌统计失败', null, 500);
    }
  }

  /**
   * 检查物化视图状态
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async checkViewStatus(req, res) {
    try {
      console.log('[分类品牌接口] 检查物化视图状态');

      const [exists, lastUpdateTime] = await Promise.all([
        this.categoryBrandService.checkMaterializedViewExists(),
        this.categoryBrandService.getLastUpdateTime()
      ]);

      const statusData = {
        exists,
        lastUpdateTime,
        lastUpdateDate: lastUpdateTime ? new Date(lastUpdateTime).toLocaleString('zh-CN', {
          timeZone: 'Asia/Shanghai'
        }) : null
      };

      return this.success(res, statusData, '获取物化视图状态成功');

    } catch (error) {
      console.error('[分类品牌接口] 检查物化视图状态失败:', error);
      return this.fail(res, '检查物化视图状态失败', null, 500);
    }
  }

  /**
   * 手动触发物化视图刷新
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async refreshView(req, res) {
    try {
      console.log('[分类品牌接口] 手动触发物化视图刷新');

      // 导入刷新服务
      const MaterializedViewRefreshService = require('../../../core/services/MaterializedViewRefreshService');
      
      // 请求刷新
      const success = await MaterializedViewRefreshService.requestRefresh('manual_api');

      if (success) {
        return this.success(res, null, '物化视图刷新请求已提交');
      } else {
        return this.fail(res, '物化视图刷新请求失败', null, 500);
      }

    } catch (error) {
      console.error('[分类品牌接口] 手动刷新物化视图失败:', error);
      return this.fail(res, '手动刷新物化视图失败', null, 500);
    }
  }
}

module.exports = CategoryBrandController;
