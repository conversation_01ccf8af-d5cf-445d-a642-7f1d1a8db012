/**
 * 商城发票抬头控制器
 */
const BaseController = require('../../../core/controllers/BaseController');
const InvoiceHeaderService = require('../services/InvoiceHeaderService');

class InvoiceHeaderController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.invoiceHeaderService = new InvoiceHeaderService();
  }

  /**
   * 获取用户的发票抬头列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getUserHeaders(req, res) {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      const result = await this.invoiceHeaderService.getUserHeaders(userId);
      
      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('获取发票抬头列表失败:', error);
      this.fail(res, '获取发票抬头列表失败', 500);
    }
  }

  /**
   * 创建发票抬头
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createHeader(req, res) {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      const { headerType, headerName, taxNumber, companyAddress, companyPhone, bankName, bankAccount, isDefault } = req.body;

      const headerData = {
        userId,
        headerType,
        headerName,
        taxNumber,
        companyAddress,
        companyPhone,
        bankName,
        bankAccount,
        isDefault
      };

      const result = await this.invoiceHeaderService.createHeader(headerData);

      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('创建发票抬头失败:', error);
      this.fail(res, '创建发票抬头失败', 500);
    }
  }

  /**
   * 更新发票抬头
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateHeader(req, res) {
    try {
      const { id } = req.params;
      const { headerType, headerName, taxNumber, companyAddress, companyPhone, bankName, bankAccount, isDefault } = req.body;

      if (!id) {
        return this.fail(res, '抬头ID不能为空', 400);
      }

      const headerData = {
        headerType,
        headerName,
        taxNumber,
        companyAddress,
        companyPhone,
        bankName,
        bankAccount,
        isDefault
      };

      const result = await this.invoiceHeaderService.updateHeader(id, headerData, req.user?.id);
      
      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('更新发票抬头失败:', error);
      this.fail(res, '更新发票抬头失败', 500);
    }
  }

  /**
   * 删除发票抬头
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteHeader(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, '抬头ID不能为空', 400);
      }

      const result = await this.invoiceHeaderService.deleteHeader(id);
      
      if (result.success) {
        this.success(res, null, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('删除发票抬头失败:', error);
      this.fail(res, '删除发票抬头失败', 500);
    }
  }

  /**
   * 设置默认抬头
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async setDefaultHeader(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user?.id;
      
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      if (!id) {
        return this.fail(res, '抬头ID不能为空', 400);
      }

      const result = await this.invoiceHeaderService.setDefaultHeader(id, userId);
      
      if (result.success) {
        this.success(res, null, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('设置默认抬头失败:', error);
      this.fail(res, '设置默认抬头失败', 500);
    }
  }

  /**
   * 根据ID获取发票抬头
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getHeaderById(req, res) {
    try {
      const { id } = req.params;

      if (!id) {
        return this.fail(res, '抬头ID不能为空', 400);
      }

      const result = await this.invoiceHeaderService.getHeaderById(id);
      
      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 404);
      }
    } catch (error) {
      console.error('获取发票抬头失败:', error);
      this.fail(res, '获取发票抬头失败', 500);
    }
  }
}

module.exports = InvoiceHeaderController;
