/**
 * 商城轮播图控制器 - 面向前端用户的公共接口
 */
const BaseController = require('../../../core/controllers/BaseController');
const BannerService = require('../services/BannerService');

class BannerController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
    this.bannerService = new BannerService(prisma);
  }

  /**
   * 获取轮播图列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   * @returns {Promise<void>}
   */
  async getBanners(req, res) {
    try {
      const { limit = 5 } = req.query;
      
      const banners = await this.bannerService.getEnabledBanners({
        limit: parseInt(limit)
      });
      
      this.success(res, banners,'获取轮播图列表成功');
    } catch (error) {
      this.fail(res, error.message);
    }
  }
}

module.exports = BannerController;
