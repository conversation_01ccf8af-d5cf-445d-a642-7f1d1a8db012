/**
 * 分类品牌路由
 * 定义分类品牌相关的API路由
 */
const express = require('express');
const router = express.Router();

// 导入分类品牌控制器
const CategoryBrandController = require('../controllers/CategoryBrandController');

/**
 * 分类品牌路由模块
 * @param {Object} prisma Prisma客户端实例
 * @returns {Object} Express路由对象
 */
module.exports = (prisma) => {
  // 实例化控制器
  const categoryBrandController = new CategoryBrandController(prisma);

  /**
   * @swagger
   * tags:
   *   name: 商城分类品牌
   *   description: 商城分类品牌相关接口
   */

  /**
   * @swagger
   * /api/v1/mall/categorybrands:
   *   get:
   *     summary: 获取指定分类下的品牌列表
   *     tags: [商城分类品牌]
   *     parameters:
   *       - in: query
   *         name: categoryid
   *         required: true
   *         schema:
   *           type: string
   *         description: 分类ID
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: pageSize
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 10
   *         description: 每页数量
   *     responses:
   *       200:
   *         description: 获取品牌列表成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取分类品牌列表成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     list:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             description: 品牌ID
   *                           name:
   *                             type: string
   *                             description: 品牌名称
   *                           logoUrl:
   *                             type: string
   *                             description: 品牌Logo URL
   *                           description:
   *                             type: string
   *                             description: 品牌描述
   *                           productCount:
   *                             type: integer
   *                             description: 该分类下该品牌的商品数量
   *                           totalSales:
   *                             type: integer
   *                             description: 总销量
   *                           totalStock:
   *                             type: integer
   *                             description: 总库存
   *                     pagination:
   *                       type: object
   *                       properties:
   *                         page:
   *                           type: integer
   *                           description: 当前页码
   *                         pageSize:
   *                           type: integer
   *                           description: 每页数量
   *                         total:
   *                           type: integer
   *                           description: 总记录数
   *                         totalPages:
   *                           type: integer
   *                           description: 总页数
   *                         hasNext:
   *                           type: boolean
   *                           description: 是否有下一页
   *                         hasPrev:
   *                           type: boolean
   *                           description: 是否有上一页
   *       400:
   *         description: 请求参数错误
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/', categoryBrandController.getCategoryBrands.bind(categoryBrandController));

  /**
   * @swagger
   * /api/v1/mall/categorybrands/stats:
   *   get:
   *     summary: 获取分类品牌统计信息
   *     tags: [商城分类品牌]
   *     parameters:
   *       - in: query
   *         name: categoryid
   *         required: true
   *         schema:
   *           type: string
   *         description: 分类ID
   *     responses:
   *       200:
   *         description: 获取统计信息成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取分类品牌统计成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     brandCount:
   *                       type: integer
   *                       description: 品牌总数
   *                     totalProducts:
   *                       type: integer
   *                       description: 商品总数
   *                     totalSales:
   *                       type: integer
   *                       description: 总销量
   *                     totalStock:
   *                       type: integer
   *                       description: 总库存
   *                     avgProductsPerBrand:
   *                       type: number
   *                       description: 平均每个品牌的商品数
   *                     lastUpdated:
   *                       type: integer
   *                       description: 最后更新时间戳
   *       400:
   *         description: 请求参数错误
   *       404:
   *         description: 该分类下暂无品牌数据
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/stats', categoryBrandController.getCategoryBrandStats.bind(categoryBrandController));

  /**
   * @swagger
   * /api/v1/mall/categorybrands/view-status:
   *   get:
   *     summary: 检查物化视图状态
   *     tags: [商城分类品牌]
   *     responses:
   *       200:
   *         description: 获取状态成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取物化视图状态成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     exists:
   *                       type: boolean
   *                       description: 物化视图是否存在
   *                     lastUpdateTime:
   *                       type: integer
   *                       description: 最后更新时间戳
   *                     lastUpdateDate:
   *                       type: string
   *                       description: 最后更新时间（格式化）
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/view-status', categoryBrandController.checkViewStatus.bind(categoryBrandController));

  /**
   * @swagger
   * /api/v1/mall/categorybrands/refresh:
   *   post:
   *     summary: 手动触发物化视图刷新
   *     tags: [商城分类品牌]
   *     responses:
   *       200:
   *         description: 刷新请求提交成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "物化视图刷新请求已提交"
   *                 data:
   *                   type: null
   *       500:
   *         description: 服务器内部错误
   */
  router.post('/refresh', categoryBrandController.refreshView.bind(categoryBrandController));

  return router;
};
