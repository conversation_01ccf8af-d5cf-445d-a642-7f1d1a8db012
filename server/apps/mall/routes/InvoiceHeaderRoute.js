/**
 * 商城发票抬头路由
 */
const express = require('express');
const router = express.Router();
const InvoiceHeaderController = require('../controllers/InvoiceHeaderController');
const { prisma } = require('../../../core/database/prisma');

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 创建控制器实例
const invoiceHeaderController = new InvoiceHeaderController(prisma);

/**
 * @route GET /api/v1/mall/invoice-headers
 * @desc 获取用户的发票抬头列表
 * @access Private
 */
router.get('/', authMiddleware, (req, res) => {
  invoiceHeaderController.getUserHeaders(req, res);
});

/**
 * @route POST /api/v1/mall/invoice-headers
 * @desc 创建发票抬头
 * @access Private
 */
router.post('/', authMiddleware, (req, res) => {
  invoiceHeaderController.createHeader(req, res);
});

/**
 * @route GET /api/v1/mall/invoice-headers/:id
 * @desc 根据ID获取发票抬头
 * @access Private
 */
router.get('/:id', authMiddleware, (req, res) => {
  invoiceHeaderController.getHeaderById(req, res);
});

/**
 * @route PUT /api/v1/mall/invoice-headers/:id
 * @desc 更新发票抬头
 * @access Private
 */
router.put('/:id', authMiddleware, (req, res) => {
  invoiceHeaderController.updateHeader(req, res);
});

/**
 * @route DELETE /api/v1/mall/invoice-headers/:id
 * @desc 删除发票抬头
 * @access Private
 */
router.delete('/:id', authMiddleware, (req, res) => {
  invoiceHeaderController.deleteHeader(req, res);
});

/**
 * @route PUT /api/v1/mall/invoice-headers/:id/set-default
 * @desc 设置默认抬头
 * @access Private
 */
router.put('/:id/set-default', authMiddleware, (req, res) => {
  invoiceHeaderController.setDefaultHeader(req, res);
});

module.exports = router;
