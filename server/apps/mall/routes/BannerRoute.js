/**
 * 商城轮播图路由 - 面向前端用户的公共接口
 */
const express = require('express');
const router = express.Router();
const BannerController = require('../controllers/BannerController');

/**
 * 商城轮播图路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} Express路由实例
 */
module.exports = (prisma) => {
  const bannerController = new BannerController(prisma);

  /**
   * @swagger
   * tags:
   *   name: 商城轮播图
   *   description: 商城轮播图公共接口
   */

  /**
   * @swagger
   * /api/v1/mall/banners:
   *   get:
   *     tags:
   *       - 商城轮播图
   *     summary: 获取轮播图列表
   *     description: 获取启用状态的轮播图列表
   *     parameters:
   *       - name: limit
   *         in: query
   *         description: 返回数量限制
   *         required: false
   *         schema:
   *           type: integer
   *           default: 5
   *     responses:
   *       200:
   *         description: 成功获取轮播图列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "获取轮播图列表成功"
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         example: "195347405783306240"
   *                       title:
   *                         type: string
   *                         example: "促销活动"
   *                       imageUrl:
   *                         type: string
   *                         example: "https://jlc-4.oss-cn-guangzhou.aliyuncs.com/uploads/df7e2132bc44eaf6ef4f497a757aaa7a.png"
   *                       sortOrder:
   *                         type: integer
   *                         example: 0
   *                       linkUrl:
   *                         type: string
   *                         example: "https://example.com/promotion"
   *                       linkType:
   *                         type: integer
   *                         example: 2
   *                         description: "1-内部链接，2-外部链接，3-无链接"
   *                       status:
   *                         type: integer
   *                         example: 1
   *                         description: "1-启用，0-禁用"
   *                       createdAt:
   *                         type: string
   *                         example: "1750641650918"
   */
  router.get('/', bannerController.getBanners.bind(bannerController));

  return router;
};
