/**
 * 测试 mode 字段的验证
 */

const SpiderDto = require('../dto/SpiderDto');

console.log('=== 测试 mode 字段验证 ===\n');

// 测试用例
const testCases = [
  {
    name: '✅ 上游订单录入 - single 模式',
    data: {
      platform_id: '1',
      store_id: '1001',
      spider_type: 'order',
      mode: 'single',
      order_no: 'ORDER123456789'
    },
    shouldPass: true,
    description: '上游订单录入默认使用 single 模式'
  },
  {
    name: '✅ 订单同步按钮 - incremental 模式',
    data: {
      platform_id: '1',
      store_id: '1001',
      spider_type: 'order',
      mode: 'incremental',
      last_sync_time: Date.now() - (24 * 60 * 60 * 1000)
    },
    shouldPass: true,
    description: '订单同步按钮使用 incremental 模式'
  },
  {
    name: '❌ 缺少 mode 字段',
    data: {
      platform_id: '1',
      store_id: '1001',
      spider_type: 'order',
      order_no: 'ORDER123456789'
    },
    shouldPass: false,
    description: '缺少必填的 mode 字段'
  },
  {
    name: '❌ 无效的 mode 值',
    data: {
      platform_id: '1',
      store_id: '1001',
      spider_type: 'order',
      mode: 'invalid_mode',
      order_no: 'ORDER123456789'
    },
    shouldPass: false,
    description: 'mode 字段值无效'
  },
  {
    name: '❌ single 模式缺少 order_no',
    data: {
      platform_id: '1',
      store_id: '1001',
      spider_type: 'order',
      mode: 'single'
    },
    shouldPass: false,
    description: 'single 模式必须提供 order_no'
  },
  {
    name: '❌ incremental 模式缺少 last_sync_time',
    data: {
      platform_id: '1',
      store_id: '1001',
      spider_type: 'order',
      mode: 'incremental'
    },
    shouldPass: false,
    description: 'incremental 模式必须提供 last_sync_time'
  }
];

// 执行测试
testCases.forEach((testCase, index) => {
  console.log(`${index + 1}. ${testCase.name}`);
  console.log(`   描述: ${testCase.description}`);
  
  try {
    const { error, value } = SpiderDto.validateCallSpider(testCase.data);
    
    if (testCase.shouldPass) {
      if (error) {
        console.log(`   ❌ 应该通过但失败了: ${error.details[0].message}`);
      } else {
        console.log(`   ✅ 验证通过`);
        console.log(`   参数: mode=${value.mode}, order_no=${value.order_no || 'null'}, last_sync_time=${value.last_sync_time || 'null'}`);
      }
    } else {
      if (error) {
        console.log(`   ✅ 正确拒绝: ${error.details[0].message}`);
      } else {
        console.log(`   ❌ 应该失败但通过了`);
      }
    }
  } catch (err) {
    console.log(`   ❌ 测试异常: ${err.message}`);
  }
  
  console.log('');
});

console.log('=== 测试总结 ===');
console.log('1. 上游订单录入应该使用 mode: "single" 并提供 order_no');
console.log('2. 订单同步按钮应该使用 mode: "incremental" 并提供 last_sync_time');
console.log('3. mode 字段现在是必填项，只能是 "incremental" 或 "single"');
console.log('4. 根据不同模式，对应的参数也是必填的');
