const express = require('express');
const { PrismaClient } = require('@prisma/client');

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 导入控制器
const OrderReportController = require('../controllers/OrderReportController');

/**
 * 订单报备路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
    console.log('OrderReportRoute initialized with prisma:', !!prisma);

    if (!prisma) {
        throw new Error('Prisma client is required');
    }

    const router = express.Router();
    const orderReportController = new OrderReportController(prisma);

    /**
     * @swagger
     * /api/v1/provider/order/order_report_add:
     *   post:
     *     tags: [订单报备管理]
     *     summary: 添加订单报备
     *     description: 创建新的订单报备记录及关联的商品信息
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             required:
     *               - platform_id
     *               - report_type
     *               - report_amount
     *               - recipient_name
     *               - contact_phone
     *               - items
     *             properties:
     *               customer_name:
     *                 type: string
     *                 description: 客户名称
     *               customer_region:
     *                 type: string
     *                 description: 客户区域
     *               platform_id:
     *                 type: string
     *                 description: 平台ID
     *               report_type:
     *                 type: integer
     *                 description: 报备类型(1-新单, 2-复购, 3-追加等)
     *               report_amount:
     *                 type: number
     *                 format: decimal
     *                 description: 报备金额
     *               expected_order_time:
     *                 type: integer
     *                 format: int64
     *                 description: 预计下单时间(时间戳，毫秒)
     *               related_order_number:
     *                 type: string
     *                 description: 关联订单号
     *               expected_shipping_time:
     *                 type: integer
     *                 format: int64
     *                 description: 预计发货时间(时间戳，毫秒)
     *               recipient_name:
     *                 type: string
     *                 description: 收货人
     *               contact_phone:
     *                 type: string
     *                 description: 联系电话
     *               shipping_address:
     *                 type: string
     *                 description: 收货地址
     *               remark:
     *                 type: string
     *                 description: 备注
     *               items:
     *                 type: array
     *                 description: 商品列表
     *                 items:
     *                   type: object
     *                   required:
     *                     - product_name
     *                     - quantity
     *                     - unit_price
     *                     - report_price
     *                     - subtotal
     *                   properties:
     *                     product_name:
     *                       type: string
     *                       description: 商品名称
     *                     specification:
     *                       type: string
     *                       description: 规格
     *                     quantity:
     *                       type: integer
     *                       description: 数量
     *                     unit_price:
     *                       type: number
     *                       format: decimal
     *                       description: 单价
     *                     report_price:
     *                       type: number
     *                       format: decimal
     *                       description: 报备价
     *                     subtotal:
     *                       type: number
     *                       format: decimal
     *                       description: 小计(数量*报备价)
     *                     product_image:
     *                       type: string
     *                       description: 商品图片
     *                     product_id:
     *                       type: string
     *                       description: 商品ID
     *                     product_sku:
     *                       type: string
     *                       description: 商品SKU
     *     responses:
     *       200:
     *         description: 创建成功
     *       400:
     *         description: 请求参数错误
     */
    router.post('/order_report_add', authMiddleware, orderReportController.addOrderReport.bind(orderReportController));

    /**
     * @swagger
     * /api/v1/provider/order/order_report_list:
     *   get:
     *     tags: [订单报备管理]
     *     summary: 获取订单报备列表
     *     description: 根据条件筛选并分页获取订单报备列表
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - name: page
     *         in: query
     *         description: 页码，默认1
     *         schema:
     *           type: integer
     *           default: 1
     *       - name: pageSize
     *         in: query
     *         description: 每页数量，默认10
     *         schema:
     *           type: integer
     *           default: 10
     *       - name: id
     *         in: query
     *         description: 报备单ID
     *         schema:
     *           type: string
     *       - name: reporter_id
     *         in: query
     *         description: 报备人ID
     *         schema:
     *           type: string
     *       - name: start_date
     *         in: query
     *         description: 开始日期(时间戳，毫秒)
     *         schema:
     *           type: integer
     *           format: int64
     *       - name: end_date
     *         in: query
     *         description: 结束日期(时间戳，毫秒)
     *         schema:
     *           type: integer
     *           format: int64
     *       - name: platform_id
     *         in: query
     *         description: 平台ID
     *         schema:
     *           type: string
     *       - name: audit_status
     *         in: query
     *         description: 审核状态(1-待审核, 2-已通过, 3-已驳回)
     *         schema:
     *           type: integer
     *       - name: related_order_number
     *         in: query
     *         description: 关联订单号
     *         schema:
     *           type: string
     *       - name: customer_name
     *         in: query
     *         description: 客户名称
     *         schema:
     *           type: string
     *       - name: customer_region
     *         in: query
     *         description: 客户区域
     *         schema:
     *           type: string
     *     responses:
     *       200:
     *         description: 查询成功
     *       400:
     *         description: 请求参数错误
     */
    router.get('/order_report_list', authMiddleware, orderReportController.getOrderReportList.bind(orderReportController));

    /**
     * @swagger
     * /api/v1/provider/order/order_report_list:
     *   post:
     *     tags: [订单报备管理]
     *     summary: 获取订单报备列表(POST方式)
     *     description: 根据条件筛选并分页获取订单报备列表，支持POST请求
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: false
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               page:
     *                 type: integer
     *                 description: 页码，默认1
     *                 default: 1
     *               pageSize:
     *                 type: integer
     *                 description: 每页数量，默认10
     *                 default: 10
     *               id:
     *                 type: string
     *                 description: 报备单ID
     *               reporter_id:
     *                 type: string
     *                 description: 报备人ID
     *               start_date:
     *                 type: integer
     *                 format: int64
     *                 description: 开始日期(时间戳，毫秒)
     *               end_date:
     *                 type: integer
     *                 format: int64
     *                 description: 结束日期(时间戳，毫秒)
     *               platform_id:
     *                 type: string
     *                 description: 平台ID
     *               audit_status:
     *                 type: integer
     *                 description: 审核状态(1-待审核, 2-已通过, 3-已驳回)
     *               related_order_number:
     *                 type: string
     *                 description: 关联订单号
     *               customer_name:
     *                 type: string
     *                 description: 客户名称
     *               customer_region:
     *                 type: string
     *                 description: 客户区域
     *     responses:
     *       200:
     *         description: 查询成功
     *       400:
     *         description: 请求参数错误
     */
    router.post('/order_report_list', authMiddleware, orderReportController.getOrderReportList.bind(orderReportController));

    /**
     * @swagger
     * /api/v1/provider/order/order_report_detail/{id}:
     *   get:
     *     tags: [订单报备管理]
     *     summary: 获取订单报备详情
     *     description: 根据ID获取订单报备的详细信息，包括基本信息和商品明细
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - name: id
     *         in: path
     *         required: true
     *         description: 报备单ID
     *         schema:
     *           type: string
     *     responses:
     *       200:
     *         description: 查询成功
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 message:
     *                   type: string
     *                   example: 操作成功
     *                 data:
     *                   type: object
     *                   properties:
     *                     id:
     *                       type: string
     *                       description: 报备单ID
     *                     report_sn:
     *                       type: string
     *                       description: 报备单号
     *                     platform_id:
     *                       type: string
     *                       description: 平台ID
     *                     platform_name:
     *                       type: string
     *                       description: 平台名称
     *                     report_type:
     *                       type: integer
     *                       description: 报备类型(1-新单, 2-复购, 3-追加等)
     *                     report_type_text:
     *                       type: string
     *                       description: 报备类型文本
     *                     expected_order_time:
     *                       type: string
     *                       description: 预计下单时间(时间戳)
     *                     customer_name:
     *                       type: string
     *                       description: 客户名称
     *                     customer_region:
     *                       type: string
     *                       description: 客户区域
     *                     remark:
     *                       type: string
     *                       description: 备注
     *                     report_amount:
     *                       type: number
     *                       description: 报备金额
     *                     recipient_name:
     *                       type: string
     *                       description: 收货人
     *                     contact_phone:
     *                       type: string
     *                       description: 联系电话
     *                     expected_shipping_time:
     *                       type: string
     *                       description: 预计交付时间(时间戳)
     *                     shipping_address:
     *                       type: string
     *                       description: 收货地址
     *                     audit_status:
     *                       type: integer
     *                       description: 审核状态(0-待审核, 1-已通过, 2-已驳回)
     *                     audit_status_text:
     *                       type: string
     *                       description: 审核状态文本
     *                     audit_remark:
     *                       type: string
     *                       description: 审核备注
     *                     audit_time:
     *                       type: string
     *                       description: 审核时间(时间戳)
     *                     audit_user_id:
     *                       type: string
     *                       description: 审核人ID
     *                     audit_username:
     *                       type: string
     *                       description: 审核人用户名
     *                     created_at:
     *                       type: string
     *                       description: 创建时间(时间戳)
     *                     items:
     *                       type: array
     *                       description: 商品列表
     *                       items:
     *                         type: object
     *                         properties:
     *                           product_name:
     *                             type: string
     *                             description: 商品名称
     *                           specification:
     *                             type: string
     *                             description: 规格
     *                           quantity:
     *                             type: integer
     *                             description: 数量
     *                           unit_price:
     *                             type: number
     *                             description: 单价
     *                           report_price:
     *                             type: number
     *                             description: 报备价
     *                           subtotal:
     *                             type: number
    
     *                             description: 小计(数量*报备价)
     *                           product_image:
     *                             type: string
     *                             description: 商品图片
     *                           product_id:
     *                             type: string
     *                             description: 商品ID
     *                           product_sku:
     *                             type: string
     *                             description: 商品SKU
     *       400:
     *         description: 请求参数错误
     *       404:
     *         description: 报备单不存在
     */
    router.get('/order_report_detail/:id', authMiddleware, orderReportController.getOrderReportDetail.bind(orderReportController));

    /**
     * @swagger
     * /api/v1/provider/order/order_report_audit:
     *   post:
     *     tags: [订单报备管理]
     *     summary: 审核订单报备
     *     description: 对订单报备进行审核通过或驳回操作
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             required:
     *               - id
     *               - audit_status
     *             properties:
     *               id:
     *                 type: string
     *                 description: 报备单ID
     *               audit_status:
     *                 type: integer
     *                 description: 审核状态(2-审核通过, 3-已驳回)
     *                 enum: [2, 3]
     *               audit_remark:
     *                 type: string
     *                 description: 审核备注，特别是驳回时的原因说明
     *     responses:
     *       200:
     *         description: 审核成功
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 message:
     *                   type: string
     *                   example: 审核成功
     *       400:
     *         description: 请求参数错误
     *       401:
     *         description: 未授权
     *       404:
     *         description: 报备单不存在
     */
    router.post('/order_report_audit', authMiddleware, orderReportController.auditOrderReport.bind(orderReportController));

    /**
     * @swagger
     * /api/v1/provider/order/order_add:
     *   post:
     *     tags: [订单报备管理]
     *     summary: 通过订单号添加订单报备
     *     description: 通过第三方订单号查询订单信息并创建报备记录
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             required:
     *               - third_party_order_sn
     *               - platform_id
     *             properties:
     *               third_party_order_sn:
     *                 type: string
     *                 description: 第三方订单号
     *               platform_id:
     *                 type: string
     *                 description: 平台ID
     *     responses:
     *       200:
     *         description: 创建成功
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 message:
     *                   type: string
     *                   example: 通过订单号创建报备成功
     *                 data:
     *                   type: object
     *                   properties:
     *                     report_id:
     *                       type: string
     *                       description: 报备单ID
     *                     report_sn:
     *                       type: string
     *                       description: 报备单号
     *                     order_id:
     *                       type: string
     *                       description: 订单ID
     *                     order_sn:
     *                       type: string
     *                       description: 订单号
     *       400:
     *         description: 请求参数错误
     *       401:
     *         description: 未授权
     *       404:
     *         description: 未找到对应的订单信息
     *       500:
     *         description: 服务器错误
     */
    router.post('/order_add', authMiddleware, orderReportController.addOrderByThirdPartySn.bind(orderReportController));

    return router;
}; 