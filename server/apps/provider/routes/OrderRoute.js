/**
 * 服务商订单路由
 * 处理服务商订单相关的路由配置
 */
const express = require('express');
const { PrismaClient } = require('@prisma/client');

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 导入控制器
const OrderController = require('../controllers/OrderController');

/**
 * 服务商订单路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
  console.log('OrderRoute initialized with prisma:', !!prisma);

  if (!prisma) {
    throw new Error('Prisma client is required');
  }

  const router = express.Router();
  const orderController = new OrderController(prisma);

  /**
   * @swagger
   * /api/v1/provider/orders:
   *   get:
   *     tags: [服务商订单管理]
   *     summary: 获取服务商订单列表
   *     description: 获取当前登录服务商用户跟进的订单列表，支持分页和筛选
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - name: page
   *         in: query
   *         description: 页码，默认1
   *         schema:
   *           type: integer
   *           default: 1
   *       - name: pageSize
   *         in: query
   *         description: 每页数量，默认10
   *         schema:
   *           type: integer
   *           default: 10
   *       - name: sortField
   *         in: query
   *         description: 排序字段，默认created_at
   *         schema:
   *           type: string
   *           default: created_at
   *       - name: sortOrder
   *         in: query
   *         description: 排序方向，默认desc
   *         schema:
   *           type: string
   *           enum: [asc, desc]
   *           default: desc
   *       - name: orderNumber
   *         in: query
   *         description: 订单编号
   *         schema:
   *           type: string
   *       - name: thirdPartyOrderSn
   *         in: query
   *         description: 第三方订单编号
   *         schema:
   *           type: string
   *       - name: orderStatus
   *         in: query
   *         description: 订单状态：0-待付款 1-已付款待发货 2-已发货待收货 3-交易成功 4-已关闭 5-已退款
   *         schema:
   *           type: integer
   *           enum: [0, 1, 2, 3, 4, 5]
   *       - name: paymentStatus
   *         in: query
   *         description: 支付状态：0-未支付 1-部分支付 2-已支付 3-退款中 4-已退款
   *         schema:
   *           type: integer
   *           enum: [0, 1, 2, 3, 4]
   *       - name: orderSource
   *         in: query
   *         description: 订单来源：0-系统创建 1-后台创建 2-商城下单 3-APP下单 4-小程序下单
   *         schema:
   *           type: integer
   *           enum: [0, 1, 2, 3, 4]
   *       - name: receiverName
   *         in: query
   *         description: 收货人姓名
   *         schema:
   *           type: string
   *       - name: startTime
   *         in: query
   *         description: 开始时间
   *         schema:
   *           type: string
   *           format: date-time
   *       - name: endTime
   *         in: query
   *         description: 结束时间
   *         schema:
   *           type: string
   *           format: date-time
   *     responses:
   *       200:
   *         description: 成功获取订单列表
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 0
   *                 message:
   *                   type: string
   *                   example: 获取订单列表成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     items:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             description: 订单ID
   *                           order_id:
   *                             type: string
   *                             description: 系统订单编号
   *                           third_party_order_sn:
   *                             type: string
   *                             description: 第三方订单编号
   *                           order_status:
   *                             type: integer
   *                             description: 订单状态
   *                           payment_status:
   *                             type: integer
   *                             description: 支付状态
   *                           total_amount:
   *                             type: number
   *                             description: 订单总金额
   *                           paid_amount:
   *                             type: number
   *                             description: 已支付金额
   *                           receiver_name:
   *                             type: string
   *                             description: 收货人姓名
   *                           receiver_phone:
   *                             type: string
   *                             description: 收货人电话
   *                           receiver_address:
   *                             type: string
   *                             description: 收货地址
   *                           products:
   *                             type: array
   *                             description: 商品列表
   *                             items:
   *                               type: object
   *                               properties:
   *                                 product_name:
   *                                   type: string
   *                                   description: 商品名称
   *                                 price:
   *                                   type: number
   *                                   description: 单价
   *                                 quantity:
   *                                   type: integer
   *                                   description: 数量
   *                           created_at:
   *                             type: integer
   *                             description: 创建时间戳
   *                     pageInfo:
   *                       type: object
   *                       properties:
   *                         total:
   *                           type: integer
   *                           description: 总记录数
   *                         currentPage:
   *                           type: integer
   *                           description: 当前页码
   *                         totalPage:
   *                           type: integer
   *                           description: 总页数
   *       401:
   *         description: 未授权
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/', authMiddleware, orderController.getOrders.bind(orderController));

  /**
   * @swagger
   * /api/v1/provider/orders/{id}:
   *   get:
   *     tags: [服务商订单管理]
   *     summary: 获取订单详情
   *     description: 根据订单ID获取订单详情，只能查看自己跟进的订单
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: 订单ID
   *     responses:
   *       200:
   *         description: 成功获取订单详情
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 0
   *                 message:
   *                   type: string
   *                   example: 获取订单详情成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     id:
   *                       type: string
   *                       description: 订单ID
   *                     order_id:
   *                       type: string
   *                       description: 系统订单编号
   *                     third_party_order_sn:
   *                       type: string
   *                       description: 第三方订单编号
   *                     order_status:
   *                       type: integer
   *                       description: 订单状态
   *                     payment_status:
   *                       type: integer
   *                       description: 支付状态
   *                     total_amount:
   *                       type: number
   *                       description: 订单总金额
   *                     paid_amount:
   *                       type: number
   *                       description: 已支付金额
   *                     receiver_name:
   *                       type: string
   *                       description: 收货人姓名
   *                     receiver_phone:
   *                       type: string
   *                       description: 收货人电话
   *                     receiver_address:
   *                       type: string
   *                       description: 收货地址
   *                     products:
   *                       type: array
   *                       description: 商品列表
   *                     logs:
   *                       type: array
   *                       description: 订单日志
   *                     created_at:
   *                       type: integer
   *                       description: 创建时间戳
   *       401:
   *         description: 未授权
   *       404:
   *         description: 订单不存在或无权限查看
   *       500:
   *         description: 服务器内部错误
   */
  router.get('/:id', authMiddleware, orderController.getOrderDetail.bind(orderController));

  return router;
};
