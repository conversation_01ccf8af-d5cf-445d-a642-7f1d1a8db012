/**
 * 订单指派路由
 * 处理订单指派相关的路由配置
 */
const express = require('express');

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 导入控制器
const OrderAssignmentController = require('../controllers/OrderAssignmentController');

/**
 * 订单指派路由
 * @param {Object} prisma - Prisma客户端实例
 * @returns {Object} - Express路由对象
 */
module.exports = (prisma) => {
    console.log('OrderAssignmentRoute initialized with prisma:', !!prisma);

    if (!prisma) {
        throw new Error('Prisma client is required');
    }

    const router = express.Router();
    const orderAssignmentController = new OrderAssignmentController(prisma);

    /**
     * @swagger
     * /api/v1/provider/order/order_assign:
     *   post:
     *     tags: [订单管理]
     *     summary: 订单指派
     *     description: 将订单指派给服务商和业务员
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             required:
     *               - order_id
     *               - order_report_id
     *               - provider_id
     *               - salesman_id
     *               - rate
     *               - assigned_by
     *             properties:
     *               order_id:
     *                 type: string
     *                 description: 订单ID
     *               order_report_id:
     *                 type: string
     *                 description: 报备信息ID
     *               provider_id:
     *                 type: string
     *                 description: 服务商ID
     *               salesman_id:
     *                 type: string
     *                 description: 业务员ID
     *               rate:
     *                 type: string
     *                 description: 费率
     *               assignment_amount:
     *                 type: number
     *                 format: decimal
     *                 description: 指派金额
     *               remark:
     *                 type: string
     *                 description: 备注
     *               assigned_by:
     *                 type: string
     *                 description: 指派人ID
     *     responses:
     *       200:
     *         description: 指派成功
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 message:
     *                   type: string
     *                   example: 订单指派成功
     *                 data:
     *                   type: object
     *                   properties:
     *                     id:
     *                       type: string
     *                       description: 指派记录ID
     *                     order_id:
     *                       type: string
     *                       description: 订单ID
     *                     provider_id:
     *                       type: string
     *                       description: 服务商ID
     *                     assigned_at:
     *                       type: string
     *                       description: 指派时间
     *       400:
     *         description: 请求参数错误
     *       401:
     *         description: 未授权
     *       500:
     *         description: 服务器错误
     */
    router.post('/order_assign', authMiddleware, orderAssignmentController.assignOrder.bind(orderAssignmentController));

    /**
     * @swagger
     * /api/v1/provider/order/order_assignment/{id}:
     *   get:
     *     tags: [订单管理]
     *     summary: 获取订单指派详情
     *     description: 根据指派ID获取订单指派的详细信息
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - name: id
     *         in: path
     *         required: true
     *         description: 指派ID
     *         schema:
     *           type: string
     *     responses:
     *       200:
     *         description: 查询成功
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 code:
     *                   type: integer
     *                   example: 200
     *                 message:
     *                   type: string
     *                   example: 操作成功
     *                 data:
     *                   type: object
     *                   properties:
     *                     id:
     *                       type: string
     *                       description: 指派记录ID
     *                     order_id:
     *                       type: string
     *                       description: 订单ID
     *                     order_report_id:
     *                       type: string
     *                       description: 报备信息ID
     *                     provider_id:
     *                       type: string
     *                       description: 服务商ID
     *                     salesman_id:
     *                       type: string
     *                       description: 业务员ID
     *                     salesman_name:
     *                       type: string
     *                       description: 业务员名称
     *                     rate:
     *                       type: number
     *                       description: 费率
     *                     assignment_amount:
     *                       type: number
     *                       description: 指派金额
     *                     assignment_status:
     *                       type: integer
     *                       description: 指派状态
     *                     assignment_status_text:
     *                       type: string
     *                       description: 指派状态文本
     *                     remark:
     *                       type: string
     *                       description: 备注
     *                     assigned_by:
     *                       type: string
     *                       description: 指派人ID
     *                     assigned_at:
     *                       type: string
     *                       description: 指派时间
     *                     accepted_at:
     *                       type: string
     *                       description: 接受时间
     *                     completed_at:
     *                       type: string
     *                       description: 完成时间
     *                     created_at:
     *                       type: string
     *                       description: 创建时间
     *                     order:
     *                       type: object
     *                       description: 订单信息
     *                       properties:
     *                         id:
     *                           type: string
     *                           description: 订单ID
     *                         third_party_order_sn:
     *                           type: string
     *                           description: 第三方订单号
     *                         total_amount:
     *                           type: number
     *                           description: 订单总金额
     *                         channel_name:
     *                           type: string
     *                           description: 渠道名称
     *                         created_at:
     *                           type: string
     *                           description: 订单创建时间
     *       400:
     *         description: 请求参数错误
     *       401:
     *         description: 未授权
     *       404:
     *         description: 指派记录不存在
     */
    router.get('/order_assignment/:id', authMiddleware, orderAssignmentController.getAssignmentDetail.bind(orderAssignmentController));

    return router;
}; 