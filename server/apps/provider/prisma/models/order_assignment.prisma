// 订单指派表
model orderAssignment {
  id                  BigInt   @id @default(autoincrement())
  order_id            BigInt   // 订单ID
  order_report_id     BigInt   // 报备信息ID
  provider_id         BigInt   // 服务商ID
  salesman_id         BigInt   // 业务员ID
  rate                Decimal  @db.Decimal(10, 4) // 费率
  assignment_amount   Decimal? @db.Decimal(18, 2) // 指派金额
  assignment_status   Int      @default(1) // 指派状态：1-已指派，2-已接受，3-已拒绝，4-已完成
  remark              String?  @db.Text // 备注
  assigned_by         BigInt   // 指派人ID
  assigned_at         BigInt   // 指派时间
  accepted_at         BigInt?  // 接受时间
  completed_at        BigInt?  // 完成时间
  created_at          BigInt   @default(autoincrement())
  updated_at          BigInt   @default(autoincrement())
  deleted_at          BigInt?

  @@map("order_assignment")
  @@schema("provider")
  @@index([order_id], map: "idx_order_assignment_order_id")
  @@index([order_report_id], map: "idx_order_assignment_order_report_id")
  @@index([provider_id], map: "idx_order_assignment_provider_id")
  @@index([salesman_id], map: "idx_order_assignment_salesman_id")
  @@index([assignment_status], map: "idx_order_assignment_status")
  @@index([assigned_at], map: "idx_order_assignment_assigned_at")
  @@index([deleted_at], map: "idx_order_assignment_deleted_at")
}
