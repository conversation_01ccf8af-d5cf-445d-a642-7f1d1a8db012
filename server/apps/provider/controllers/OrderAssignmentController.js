const ProviderBaseController = require('./base/ProviderBaseController');

/**
 * 订单指派控制器
 */
class OrderAssignmentController extends ProviderBaseController {
    /**
     * 构造函数
     * @param {Object} prisma - Prisma客户端实例
     */
    constructor(prisma) {
        if (!prisma) {
            throw new Error('Prisma client is required');
        }
        super(prisma);
        console.log('OrderAssignmentController initialized with prisma:', !!this.prisma);
    }

    /**
     * 生成唯一ID
     * @returns {bigint} 生成的ID
     */
    generateId() {
        const timestamp = BigInt(Date.now());
        const workerId = BigInt(1); // 工作机器ID
        const sequence = BigInt(Math.floor(Math.random() * 4096)); // 序列号
        const snowflakeId = (timestamp << BigInt(22)) | (workerId << BigInt(12)) | sequence;
        return snowflakeId;
    }

    /**
     * 订单指派
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async assignOrder(req, res) {
        try {
            const {
                order_id,
                order_report_id,
                provider_id,
                salesman_id,
                rate,
                assignment_amount,
                remark,
                assigned_by
            } = req.body;

            // 验证必填参数
            if (!order_id || !order_report_id || !provider_id || !salesman_id || !rate || !assigned_by) {
                return this.fail(res, '缺少必要参数', 400);
            }

            // 获取当前用户ID
            const userId = req.user?.id ? BigInt(req.user.id) : null;
            if (!userId) {
                return this.fail(res, '用户未登录', 401);
            }

            try {
                // 检查订单是否已经被指派
                const existingAssignment = await this.prisma.$queryRaw`
                    SELECT id 
                    FROM "provider"."order_assignment" 
                    WHERE "order_id" = ${BigInt(order_id)}
                    AND "deleted_at" IS NULL
                `;

                if (existingAssignment && existingAssignment.length > 0) {
                    return this.fail(res, '该订单已经被指派', 400);
                }

                // 生成指派记录ID
                const assignmentId = this.generateId();
                const timestamp = BigInt(Math.floor(Date.now() / 1000));
                const assignedAt = BigInt(Date.now());

                // 插入订单指派记录
                await this.prisma.$executeRaw`
                    INSERT INTO "provider"."order_assignment" (
                        id, order_id, order_report_id, provider_id, salesman_id,
                        rate, assignment_amount, assignment_status, remark,
                        assigned_by, assigned_at
                    ) VALUES (
                        ${assignmentId}, ${BigInt(order_id)}, ${BigInt(order_report_id)}, 
                        ${BigInt(provider_id)}, ${BigInt(salesman_id)}, ${parseFloat(rate)}, 
                        ${parseFloat(assignment_amount) || null}, 1, ${remark || null},
                        ${BigInt(assigned_by)}, ${assignedAt}
                    )
                `;

                return this.success(res, {
                    id: assignmentId.toString(),
                    order_id: order_id,
                    provider_id: provider_id,
                    assigned_at: assignedAt.toString()
                }, '订单指派成功');
            } catch (error) {
                console.error('订单指派失败:', error);
                return this.fail(res, `处理失败: ${error.message}`, 500);
            }
        } catch (error) {
            console.error('订单指派失败:', error);
            return this.fail(res, error.message, 500);
        }
    }

    /**
     * 获取订单指派详情
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async getAssignmentDetail(req, res) {
        try {
            const { id } = req.params;

            if (!id) {
                return this.fail(res, '指派ID不能为空', 400);
            }

            // 获取当前用户ID
            const userId = req.user?.id ? BigInt(req.user.id) : null;
            if (!userId) {
                return this.fail(res, '用户未登录', 401);
            }

            try {
                // 查询指派详情
                const assignmentResult = await this.prisma.$queryRaw`
                    SELECT 
                        a.id, a.order_id, a.order_report_id, a.provider_id, 
                        a.salesman_id, a.rate, a.assignment_amount, 
                        a.assignment_status, a.remark, a.assigned_by, 
                        a.assigned_at, a.accepted_at, a.completed_at, 
                        a.created_at, a.updated_at
                    FROM "provider"."order_assignment" a
                    WHERE a.id = ${BigInt(id)}
                    AND a.deleted_at IS NULL
                    AND a.provider_id = ${userId}
                `;

                if (!assignmentResult || assignmentResult.length === 0) {
                    return this.fail(res, '指派记录不存在或无权限查看', 404);
                }

                const assignment = assignmentResult[0];

                // 查询订单信息
                const orderResult = await this.prisma.$queryRaw`
                    SELECT 
                        o.id, o.third_party_order_sn, o.total_amount, 
                        o.created_at, c.name as channel_name
                    FROM "base"."orders" o
                    LEFT JOIN "base"."channel" c ON o.channel_id = c.id
                    WHERE o.id = ${assignment.order_id}
                    AND o.deleted_at IS NULL
                `;

                const order = orderResult && orderResult.length > 0 ? orderResult[0] : {};

                // 查询业务员信息
                const salesmanResult = await this.prisma.$queryRaw`
                    SELECT id, username, nickname
                    FROM "base"."system_user"
                    WHERE id = ${assignment.salesman_id}
                `;

                const salesman = salesmanResult && salesmanResult.length > 0 ? salesmanResult[0] : {};

                // 构造返回数据
                const result = {
                    id: assignment.id.toString(),
                    order_id: assignment.order_id.toString(),
                    order_report_id: assignment.order_report_id.toString(),
                    provider_id: assignment.provider_id.toString(),
                    salesman_id: assignment.salesman_id.toString(),
                    salesman_name: salesman.username || salesman.nickname || '未知业务员',
                    rate: parseFloat(assignment.rate),
                    assignment_amount: parseFloat(assignment.assignment_amount) || 0,
                    assignment_status: assignment.assignment_status,
                    assignment_status_text: this.getAssignmentStatusText(assignment.assignment_status),
                    remark: assignment.remark || '',
                    assigned_by: assignment.assigned_by.toString(),
                    assigned_at: assignment.assigned_at.toString(),
                    accepted_at: assignment.accepted_at ? assignment.accepted_at.toString() : null,
                    completed_at: assignment.completed_at ? assignment.completed_at.toString() : null,
                    created_at: assignment.created_at.toString(),
                    order: {
                        id: order.id ? order.id.toString() : '',
                        third_party_order_sn: order.third_party_order_sn || '',
                        total_amount: order.total_amount ? parseFloat(order.total_amount) : 0,
                        channel_name: order.channel_name || '未知渠道',
                        created_at: order.created_at ? order.created_at.toString() : ''
                    }
                };

                return this.success(res, result);
            } catch (error) {
                console.error('获取指派详情失败:', error);
                return this.fail(res, `处理失败: ${error.message}`, 500);
            }
        } catch (error) {
            console.error('获取指派详情失败:', error);
            return this.fail(res, error.message, 500);
        }
    }

    /**
     * 获取指派状态文本
     * @param {number} status - 指派状态
     * @returns {string} 指派状态文本
     */
    getAssignmentStatusText(status) {
        const statusMap = {
            1: '已指派',
            2: '已接受',
            3: '已拒绝',
            4: '已完成'
        };
        return statusMap[status] || '未知状态';
    }
}

module.exports = OrderAssignmentController; 