const { Prisma } = require('@prisma/client');
const ProviderBaseController = require('./base/ProviderBaseController');

/**
 * 订单报备控制器
 */
class OrderReportController extends ProviderBaseController {
    /**
     * 构造函数
     * @param {Object} prisma - Prisma客户端实例
     */
    constructor(prisma) {
        if (!prisma) {
            throw new Error('Prisma client is required');
        }
        super(prisma);
        console.log('OrderReportController initialized with prisma:', !!this.prisma);
    }



    /**
     * 生成唯一ID
     * @returns {bigint} 生成的ID
     */
    generateId() {
        const timestamp = BigInt(Date.now());
        const workerId = BigInt(1); // 工作机器ID
        const sequence = BigInt(Math.floor(Math.random() * 4096)); // 序列号
        const snowflakeId = (timestamp << BigInt(22)) | (workerId << BigInt(12)) | sequence;
        return snowflakeId;
    }

    /**
     * 添加订单报备
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async addOrderReport(req, res) {
        try {
            const {
                customer_name,
                customer_region,
                platform_id,
                report_type,
                report_amount,
                expected_order_time,
                related_order_number,
                expected_shipping_time,
                recipient_name,
                contact_phone,
                shipping_address,
                remark,
                items // 商品列表
            } = req.body;

            // 验证必填参数
            if (!platform_id || !report_type || !report_amount ||
                !recipient_name || !contact_phone || !items) {
                return this.fail(res, '缺少必要参数', 400);
            }

            // 验证商品列表
            if (!Array.isArray(items) || items.length === 0) {
                return this.fail(res, '商品列表不能为空', 400);
            }

            // 验证每个商品项
            for (const item of items) {
                if (!item.product_name || !item.quantity || !item.unit_price || !item.report_price || !item.subtotal) {
                    return this.fail(res, '商品信息不完整', 400);
                }
            }

            // 获取当前用户ID
            const userId = req.user?.id ? BigInt(req.user.id) : null;
            if (!userId) {
                return this.fail(res, '用户未登录', 401);
            }

            // 如果提供了关联订单号，检查是否已经被报备过
            if (related_order_number) {
                const existingReportResult = await this.prisma.$queryRaw`
                    SELECT id 
                    FROM "provider"."order_report" 
                    WHERE "related_order_number" = ${related_order_number}
                    AND "deleted_at" IS NULL
                    AND "provider_id" = ${userId}
                `;

                if (existingReportResult && existingReportResult.length > 0) {
                    return this.fail(res, '该订单已经被报备过', 400);
                }
            }

            // 生成报备单ID
            const reportId = this.generateId();
            const timestamp = BigInt(Math.floor(Date.now() / 1000));

            // 开始事务
            const result = await this.prisma.$transaction(async (prisma) => {
                // 1. 插入订单报备主表
                await prisma.$executeRaw`
          INSERT INTO "provider"."order_report" (
            id, customer_name, customer_region, platform_id, report_type,
            report_amount, expected_order_time, related_order_number, expected_shipping_time,
            recipient_name, contact_phone, shipping_address, remark, audit_status,
            reporter_id, provider_id, created_at, updated_at, created_by, updated_by
          ) VALUES (
            ${reportId}, ${customer_name || null}, ${customer_region || null}, ${platform_id}, ${report_type},
            ${report_amount}, ${expected_order_time || null}, ${related_order_number || null}, ${expected_shipping_time || null},
            ${recipient_name}, ${contact_phone}, ${shipping_address || null}, ${remark || null}, 1,
            ${userId}, ${userId}, ${timestamp}, ${timestamp}, ${userId}, ${userId}
          )
        `;

                // 2. 插入订单报备商品详情
                for (const item of items) {
                    const itemId = this.generateId();
                    await prisma.$executeRaw`
            INSERT INTO "provider"."order_report_item" (
              id, order_report_id, product_name, specification, quantity,
              unit_price, report_price, subtotal, created_at, updated_at, created_by, updated_by,
              product_image, product_id, product_sku
            ) VALUES (
              ${itemId}, ${reportId}, ${item.product_name}, ${item.specification || null}, ${item.quantity},
              ${item.unit_price}, ${item.report_price}, ${item.subtotal}, ${timestamp}, ${timestamp}, ${userId}, ${userId},
              ${item.product_image || null}, ${item.product_id ? BigInt(item.product_id) : BigInt(0)}, ${item.product_sku || null}
            )
          `;
                }

                return { reportId };
            });

            return this.success(res, {
                report_id: result.reportId.toString()
            }, '创建报备成功');
        } catch (error) {
            console.error('创建订单报备失败:', error);
            return this.fail(res, error.message, 500);
        }
    }

    /**
     * 获取订单报备列表
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async getOrderReportList(req, res) {
        try {
            // 同时支持GET和POST请求的参数获取
            const params = req.method === 'GET' ? req.query : req.body;

            console.log('订单报备列表请求参数:', JSON.stringify(params));
            console.log('请求方法:', req.method);

            const {
                page = 1,
                pageSize = 10,
                id,
                reporter_id,
                start_date,
                end_date,
                platform_id,
                audit_status,
                related_order_number,
                customer_name,
                customer_region,
                provider_id // 添加provider_id参数
            } = params;

            console.log('解析后的参数:', {
                page,
                pageSize,
                id,
                platform_id,
                audit_status,
                related_order_number,
                provider_id // 添加provider_id日志
            });

            const offset = BigInt((page - 1) * pageSize);
            const limit = BigInt(pageSize);

            // 获取当前用户ID
            const userId = req.user?.id ? BigInt(req.user.id) : null;
            if (!userId) {
                return this.fail(res, '用户未登录', 401);
            }

            console.log('当前用户ID:', userId.toString());

            // 使用原始SQL查询，避免Prisma.sql可能的问题
            // 初始化只包含deleted_at条件
            let whereConditions = [`"deleted_at" IS NULL`];

            // 判断是否已经提供了provider_id参数
            if (provider_id) {
                // 如果请求中已经包含provider_id，则使用请求中的provider_id
                whereConditions.push(`"provider_id" = ${BigInt(provider_id)}`);
                console.log('使用请求提供的provider_id:', provider_id);
            } else {
                // 如果没有提供provider_id，则使用当前用户ID
                whereConditions.push(`"provider_id" = ${userId}`);
                console.log('使用当前用户ID作为provider_id:', userId.toString());
            }

            if (id) {
                whereConditions.push(`"id" = ${BigInt(id)}`);
            }

            if (reporter_id) {
                whereConditions.push(`"reporter_id" = ${BigInt(reporter_id)}`);
            }

            if (start_date) {
                whereConditions.push(`"created_at" >= ${BigInt(start_date)}`);
            }

            if (end_date) {
                whereConditions.push(`"created_at" <= ${BigInt(end_date)}`);
            }

            if (platform_id) {
                whereConditions.push(`"platform_id" = '${platform_id}'`);
                console.log('添加platform_id条件:', `"platform_id" = '${platform_id}'`);
            }

            if (audit_status !== undefined && audit_status !== null) {
                whereConditions.push(`"audit_status" = ${parseInt(audit_status)}`);
                console.log('添加audit_status条件:', `"audit_status" = ${parseInt(audit_status)}`);
            }

            if (related_order_number) {
                whereConditions.push(`"related_order_number" LIKE '%${related_order_number}%'`);
                console.log('添加related_order_number条件:', `"related_order_number" LIKE '%${related_order_number}%'`);
            }

            if (customer_name) {
                whereConditions.push(`"customer_name" LIKE '%${customer_name}%'`);
            }

            if (customer_region) {
                whereConditions.push(`"customer_region" LIKE '%${customer_region}%'`);
            }

            // 构建WHERE子句
            const whereClause = whereConditions.join(' AND ');
            console.log('最终WHERE子句:', whereClause);

            // 构建完整的SQL查询
            const countQuery = `
                SELECT COUNT(*) as total
                FROM "provider"."order_report"
                WHERE ${whereClause}
            `;
            console.log('计数查询SQL:', countQuery);

            // 获取报备单总数
            const totalResult = await this.prisma.$queryRawUnsafe(countQuery);
            const total = parseInt(totalResult[0].total);
            console.log('查询到的总记录数:', total);

            // 构建列表查询SQL
            const listQuery = `
                SELECT
                    id, customer_name, customer_region, platform_id,
                    report_type, report_amount, created_at, audit_status,
                    related_order_number, reporter_id
                FROM "provider"."order_report"
                WHERE ${whereClause}
                ORDER BY created_at DESC
                LIMIT ${limit}
                OFFSET ${offset}
            `;
            console.log('列表查询SQL:', listQuery);

            // 获取报备单列表
            const reports = await this.prisma.$queryRawUnsafe(listQuery);
            console.log('查询到的记录数:', reports.length);

            // 获取报备人信息
            const reporterIds = [...new Set(reports.map(report => report.reporter_id))];
            const reporterInfo = {};

            if (reporterIds.length > 0) {
                // 使用单独的查询获取每个报备人信息
                for (const reporterId of reporterIds) {
                    const reporterResult = await this.prisma.$queryRaw`
                        SELECT id, username
                        FROM "provider"."provider_user"
                        WHERE id = ${reporterId}
                        AND deleted_at IS NULL
                    `;

                    if (reporterResult && reporterResult.length > 0) {
                        reporterInfo[reporterId.toString()] = reporterResult[0].username;
                    }
                }
            }

            // 获取每个报备单的商品信息
            const items = await Promise.all(reports.map(async (report) => {
                const reportItems = await this.prisma.$queryRaw`
          SELECT product_name, specification, quantity, unit_price, report_price, subtotal, product_image, product_id, product_sku
          FROM "provider"."order_report_item"
          WHERE order_report_id = ${report.id}
          AND deleted_at IS NULL
        `;

                // 获取平台名称
                let platformName = '未知平台';
                if (report.platform_id) {
                    try {
                        // 记录查询的platform_id值和类型
                        console.log('查询平台信息，platform_id:', report.platform_id, '类型:', typeof report.platform_id);

                        // 确保platform_id是字符串类型
                        const platformIdStr = report.platform_id.toString();

                        // 使用$queryRawUnsafe方法构建查询
                        const query = `
                            SELECT id, name
                            FROM "base"."channel"
                            WHERE id = '${platformIdStr}'
                            AND deleted_at IS NULL
                        `;
                        console.log('执行查询:', query);

                        const platformResult = await this.prisma.$queryRawUnsafe(query);

                        console.log('平台查询结果:', JSON.stringify(platformResult));

                        if (platformResult && platformResult.length > 0) {
                            platformName = platformResult[0].name;
                            console.log('获取到平台名称:', platformName);
                        } else {
                            console.log('未找到对应的平台记录');
                        }
                    } catch (error) {
                        console.error('获取平台信息失败:', error);
                    }
                } else {
                    console.log('报备单没有platform_id值');
                }

                return {
                    id: report.id.toString(),
                    customer_name: report.customer_name,
                    customer_region: report.customer_region,
                    platform_id: report.platform_id,
                    platform_name: platformName,
                    report_type: report.report_type,
                    report_amount: parseFloat(report.report_amount),
                    created_at: report.created_at.toString(),
                    audit_status: report.audit_status,
                    related_order_number: report.related_order_number || '',
                    reporter_id: report.reporter_id.toString(),
                    reporter_name: reporterInfo[report.reporter_id.toString()] || '未知',
                    items: reportItems.map(item => ({
                        product_name: item.product_name,
                        specification: item.specification || '',
                        quantity: item.quantity,
                        unit_price: parseFloat(item.unit_price),
                        report_price: parseFloat(item.report_price),
                        subtotal: parseFloat(item.subtotal),
                        product_image: item.product_image || '',
                        product_id: item.product_id ? item.product_id.toString() : '0',
                        product_sku: item.product_sku || ''
                    }))
                };
            }));

            return this.success(res, {
                items,
                pageInfo: {
                    current: parseInt(page),
                    pageSize: parseInt(pageSize),
                    total
                }
            });
        } catch (error) {
            console.error('获取订单报备列表失败:', error);
            return this.fail(res, error.message, 500);
        }
    }

    /**
     * 获取订单报备详情
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async getOrderReportDetail(req, res) {
        try {
            const { id } = req.params;

            if (!id) {
                return this.fail(res, '报备ID不能为空', 400);
            }

            try {
                // 将ID转换为BigInt类型
                const reportIdBigInt = BigInt(id);

                // 获取当前用户ID
                const userId = req.user?.id ? BigInt(req.user.id) : null;
                if (!userId) {
                    return this.fail(res, '用户未登录', 401);
                }

                // 查询报备单信息，移除provider_id限制，添加audit_time、audit_user_id和audit_remark字段
                const reportResult = await this.prisma.$queryRaw`
                    SELECT
                        r.id, r.customer_name, r.customer_region, r.platform_id, r.report_type,
                        r.report_amount, r.expected_order_time, r.related_order_number, r.expected_shipping_time,
                        r.recipient_name, r.contact_phone, r.shipping_address, r.remark, r.audit_status,
                        r.reporter_id, r.created_at, r.audit_time, r.audit_user_id, r.audit_remark,
                        su.username as audit_username
                    FROM "provider"."order_report" r
                    LEFT JOIN "base"."system_user" su ON r.audit_user_id = su.id
                    WHERE r.id = ${reportIdBigInt}
                    AND r.deleted_at IS NULL
                `;

                if (!reportResult || reportResult.length === 0) {
                    return this.fail(res, '报备单不存在或无权限查看', 404);
                }

                const report = reportResult[0];

                // 查询报备单商品信息
                const itemsResult = await this.prisma.$queryRaw`
                    SELECT 
                        product_name, specification, quantity, unit_price, report_price, subtotal, product_image, product_id, product_sku
                    FROM "provider"."order_report_item"
                    WHERE order_report_id = ${reportIdBigInt}
                    AND deleted_at IS NULL
                    ORDER BY id ASC
                `;

                // 查询平台信息
                let platformName = '未知平台';
                try {
                    // 记录查询的platform_id值和类型
                    console.log('详情接口查询平台信息，platform_id:', report.platform_id, '类型:', typeof report.platform_id);

                    // 确保platform_id是字符串类型
                    const platformIdStr = report.platform_id.toString();

                    // 使用$queryRawUnsafe方法构建查询
                    const query = `
                        SELECT id, name
                        FROM "base"."channel"
                        WHERE id = '${platformIdStr}'
                        AND deleted_at IS NULL
                    `;
                    console.log('详情接口执行查询:', query);

                    const platformResult = await this.prisma.$queryRawUnsafe(query);

                    console.log('详情接口平台查询结果:', JSON.stringify(platformResult));

                    if (platformResult && platformResult.length > 0) {
                        platformName = platformResult[0].name;
                        console.log('详情接口获取到平台名称:', platformName);
                    } else {
                        console.log('详情接口未找到对应的平台记录');
                    }
                } catch (error) {
                    console.error('详情接口获取平台信息失败:', error);
                }

                // 构造返回数据
                const result = {
                    id: report.id.toString(),
                    platform_id: report.platform_id,
                    platform_name: platformName,
                    report_type: report.report_type,
                    report_type_text: this.getReportTypeText(report.report_type),
                    expected_order_time: report.expected_order_time ? report.expected_order_time.toString() : null,
                    customer_name: report.customer_name,
                    customer_region: report.customer_region,
                    remark: report.remark || '',
                    report_amount: parseFloat(report.report_amount),
                    recipient_name: report.recipient_name,
                    contact_phone: report.contact_phone,
                    expected_shipping_time: report.expected_shipping_time ? report.expected_shipping_time.toString() : null,
                    shipping_address: report.shipping_address,
                    audit_status: report.audit_status,
                    audit_status_text: this.getAuditStatusText(report.audit_status),
                    audit_remark: report.audit_remark || '',
                    created_at: report.created_at.toString(),
                    audit_time: report.audit_time ? report.audit_time.toString() : null,
                    audit_user_id: report.audit_user_id ? report.audit_user_id.toString() : null,
                    audit_username: report.audit_username || null,
                    items: itemsResult.map(item => ({
                        product_name: item.product_name,
                        specification: item.specification || '',
                        quantity: item.quantity,
                        unit_price: parseFloat(item.unit_price),
                        report_price: parseFloat(item.report_price),
                        subtotal: parseFloat(item.subtotal),
                        product_image: item.product_image || '',
                        product_id: item.product_id ? item.product_id.toString() : '0',
                        product_sku: item.product_sku || ''
                    }))
                };

                return this.success(res, result);
            } catch (error) {
                console.error('解析报备ID失败:', error);
                return this.fail(res, '报备ID无效', 400);
            }
        } catch (error) {
            console.error('获取订单报备详情失败:', error);
            return this.fail(res, error.message, 500);
        }
    }

    /**
     * 获取报备类型文本
     * @param {number} type - 报备类型
     * @returns {string} 报备类型文本
     */
    getReportTypeText(type) {
        const typeMap = {
            1: '新单',
            2: '复购',
            3: '追加'
        };
        return typeMap[type] || '未知类型';
    }

    /**
     * 获取审核状态文本
     * @param {number} status - 审核状态
     * @returns {string} 审核状态文本
     */
    getAuditStatusText(status) {
        const statusMap = {
            1: '待审核',
            2: '审核通过',
            3: '已驳回'
        };
        return statusMap[status] || '未知状态';
    }

    /**
     * 审核订单报备
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async auditOrderReport(req, res) {
        try {
            const { id, audit_status, audit_remark } = req.body;

            // 验证必填参数
            if (!id || audit_status === undefined) {
                return this.fail(res, '报备ID和审核状态不能为空', 400);
            }

            // 验证审核状态值是否有效
            if (![2, 3].includes(parseInt(audit_status))) {
                return this.fail(res, '审核状态无效，只能为2(审核通过)或3(已驳回)', 400);
            }

            try {
                // 将报备ID转换为BigInt类型
                const reportIdBigInt = BigInt(id);

                // 获取当前用户ID
                const userId = req.user?.id ? BigInt(req.user.id) : null;
                if (!userId) {
                    return this.fail(res, '用户未登录', 401);
                }

                // 检查报备单是否存在
                const reportResult = await this.prisma.$queryRaw`
                    SELECT id, audit_status
                    FROM "provider"."order_report"
                    WHERE id = ${reportIdBigInt}
                    AND deleted_at IS NULL
                `;

                if (!reportResult || reportResult.length === 0) {
                    return this.fail(res, '报备单不存在', 404);
                }

                // 检查报备单当前状态
                const currentStatus = reportResult[0].audit_status;
                if (currentStatus !== 1) {
                    return this.fail(res, '只能审核待审核状态的报备单', 400);
                }

                // 更新报备单审核状态
                const timestamp = BigInt(Math.floor(Date.now() / 1000));
                await this.prisma.$executeRaw`
                    UPDATE "provider"."order_report"
                    SET audit_status = ${parseInt(audit_status)},
                        audit_remark = ${audit_remark || null},
                        updated_at = ${timestamp},
                        updated_by = ${userId}
                    WHERE id = ${reportIdBigInt}
                    AND deleted_at IS NULL
                `;

                return this.success(res, null, '审核成功');
            } catch (error) {
                console.error('解析报备ID失败:', error);
                return this.fail(res, '报备ID无效', 400);
            }
        } catch (error) {
            console.error('审核订单报备失败:', error);
            return this.fail(res, error.message, 500);
        }
    }

    /**
     * 通过第三方订单号添加订单报备
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async addOrderByThirdPartySn(req, res) {
        try {
            const { third_party_order_sn, platform_id } = req.body;

            // 验证必填参数
            if (!third_party_order_sn) {
                return this.fail(res, '第三方订单号不能为空', 400);
            }

            if (!platform_id) {
                return this.fail(res, '平台ID不能为空', 400);
            }

            // 获取当前用户ID
            const userId = req.user?.id ? BigInt(req.user.id) : null;
            if (!userId) {
                return this.fail(res, '用户未登录', 401);
            }

            try {
                // 1. 检查订单是否已经被报备过
                console.log('检查订单是否已被报备，第三方订单号:', third_party_order_sn);
                const existingReportResult = await this.prisma.$queryRaw`
                    SELECT id 
                    FROM "provider"."order_report" 
                    WHERE "related_order_number" = ${third_party_order_sn}
                    AND "deleted_at" IS NULL
                    AND "provider_id" = ${userId}
                `;

                if (existingReportResult && existingReportResult.length > 0) {
                    console.log('订单已被报备，报备ID:', existingReportResult[0].id.toString());
                    return this.fail(res, '该订单已经被报备过', 400);
                }

                // 2. 通过第三方订单号查询订单基本信息
                console.log('查询订单信息，第三方订单号:', third_party_order_sn);
                const orderResult = await this.prisma.$queryRaw`
                    SELECT 
                        o.id, 
                        o.third_party_order_sn, 
                        o.channel_id, 
                        o.platform_id,
                        o.store_id, 
                        o.total_amount, 
                        o.remark, 
                        o.created_at,
                        c.name as channel_name,
                        s.name as store_name
                    FROM "base"."orders" o
                    LEFT JOIN "base"."channel" c ON o.channel_id = c.id
                    LEFT JOIN "base"."store" s ON o.store_id = s.id
                    WHERE o.third_party_order_sn = ${third_party_order_sn}
                    AND o.deleted_at IS NULL
                `;

                if (!orderResult || orderResult.length === 0) {
                    console.log('未找到对应的订单信息，第三方订单号:', third_party_order_sn);
                    return this.fail(res, '未找到对应的订单信息', 404);
                }

                const order = orderResult[0];
                const orderId = order.id.toString();
                console.log('找到订单ID:', orderId);

                // 2. 查询订单收货信息
                const shippingResult = await this.prisma.$queryRaw`
                    SELECT 
                        recipient_name, 
                        recipient_phone, 
                        region_path_name,
                        street_address,
                        created_at
                    FROM "base"."order_shipping_info"
                    WHERE order_id = ${order.id}
                `;

                const shipping = shippingResult && shippingResult.length > 0 ? shippingResult[0] : {};

                // 3. 查询订单商品信息
                const orderItemsResult = await this.prisma.$queryRaw`
                    SELECT 
                        product_name, 
                        sku_specifications,
                        quantity, 
                        unit_price, 
                        market_price_snapshot,
                        total_price,
                        product_image,
                        goods_sku_id,
                        third_party_product_code
                    FROM "base"."order_items"
                    WHERE order_id = ${order.id}
                `;

                // 4. 组装报备参数
                const reportData = {
                    customer_name: order.store_name || '未知客户',
                    customer_region: shipping.region_path_name || '未知区域',
                    platform_id: platform_id, // 使用请求中提供的platform_id
                    report_type: 1, // 默认为新单
                    report_amount: parseFloat(order.total_amount) || 0,
                    expected_order_time: order.created_at ? parseInt(order.created_at) : null,
                    related_order_number: order.third_party_order_sn || '',
                    expected_shipping_time: shipping.created_at ? parseInt(shipping.created_at) : null,
                    recipient_name: shipping.recipient_name || '',
                    contact_phone: shipping.recipient_phone || '',
                    shipping_address: shipping.street_address || '',
                    remark: order.remark || '',
                    items: []
                };

                console.log('组装的报备基本信息:', {
                    customer_name: reportData.customer_name,
                    platform_id: reportData.platform_id,
                    report_amount: reportData.report_amount,
                    related_order_number: reportData.related_order_number
                });

                // 处理商品列表
                if (orderItemsResult && orderItemsResult.length > 0) {
                    reportData.items = orderItemsResult.map(item => {
                        // 处理sku_specifications，它可能是JSONB类型
                        let specification = '';
                        if (item.sku_specifications) {
                            try {
                                // 如果已经是对象，直接使用
                                if (typeof item.sku_specifications === 'object') {
                                    specification = Object.entries(item.sku_specifications)
                                        .map(([key, value]) => `${key}: ${value}`)
                                        .join(', ');
                                } else {
                                    // 尝试解析JSON字符串
                                    const specObj = JSON.parse(item.sku_specifications);
                                    specification = Object.entries(specObj)
                                        .map(([key, value]) => `${key}: ${value}`)
                                        .join(', ');
                                }
                            } catch (e) {
                                console.error('解析规格信息失败:', e);
                                specification = item.product_name || '';
                            }
                        } else {
                            specification = item.product_name || '';
                        }

                        const mappedItem = {
                            product_name: item.product_name || '',
                            product_id: item.goods_sku_id ? item.goods_sku_id.toString() : '',
                            product_sku: item.third_party_product_code || '',
                            product_image: item.product_image || '',
                            specification: specification,
                            quantity: parseInt(item.quantity) || 0,
                            unit_price: parseFloat(item.unit_price) || 0,
                            report_price: parseFloat(item.market_price_snapshot || item.unit_price) || 0,
                            subtotal: parseFloat(item.total_price) || 0
                        };

                        console.log('处理商品项:', {
                            product_name: mappedItem.product_name,
                            product_sku: mappedItem.product_sku,
                            quantity: mappedItem.quantity,
                            unit_price: mappedItem.unit_price
                        });

                        return mappedItem;
                    });
                } else {
                    console.log('订单中没有商品项');
                }

                // 5. 生成报备单ID
                const reportId = this.generateId();
                const timestamp = BigInt(Math.floor(Date.now() / 1000));

                console.log('生成的报备信息:', {
                    reportId: reportId.toString(),
                    timestamp: timestamp.toString()
                });

                // 6. 开始事务，插入报备数据
                const result = await this.prisma.$transaction(async (prisma) => {
                    // 插入订单报备主表
                    await prisma.$executeRaw`
                        INSERT INTO "provider"."order_report" (
                            id, customer_name, customer_region, platform_id, report_type,
                            report_amount, expected_order_time, related_order_number, expected_shipping_time,
                            recipient_name, contact_phone, shipping_address, remark, audit_status,
                            reporter_id, provider_id, created_at, updated_at, created_by, updated_by
                        ) VALUES (
                            ${reportId}, ${reportData.customer_name || null}, ${reportData.customer_region || null}, 
                            ${reportData.platform_id}, ${reportData.report_type}, ${reportData.report_amount}, 
                            ${reportData.expected_order_time}, ${reportData.related_order_number}, 
                            ${reportData.expected_shipping_time}, ${reportData.recipient_name}, 
                            ${reportData.contact_phone}, ${reportData.shipping_address || null}, 
                            ${reportData.remark}, 1, ${userId}, ${userId}, ${timestamp}, 
                            ${timestamp}, ${userId}, ${userId}
                        )
                    `;

                    console.log('插入报备主表成功');

                    // 插入订单报备商品详情
                    for (const item of reportData.items) {
                        const itemId = this.generateId();
                        await prisma.$executeRaw`
                            INSERT INTO "provider"."order_report_item" (
                                id, order_report_id, product_name, specification, quantity,
                                unit_price, report_price, subtotal, created_at, updated_at, 
                                created_by, updated_by, product_image, product_id, product_sku
                            ) VALUES (
                                ${itemId}, ${reportId}, ${item.product_name}, ${item.specification}, 
                                ${item.quantity}, ${item.unit_price}, ${item.report_price}, 
                                ${item.subtotal}, ${timestamp}, ${timestamp}, ${userId}, 
                                ${userId}, ${item.product_image}, ${item.product_id ? BigInt(item.product_id) : BigInt(0)}, 
                                ${item.product_sku}
                            )
                        `;
                    }

                    console.log('插入报备商品详情成功，共', reportData.items.length, '项');

                    return { reportId };
                });

                console.log('事务执行完成，报备创建成功');

                return this.success(res, {
                    report_id: result.reportId.toString(),
                    order_id: orderId,
                    order_sn: order.third_party_order_sn
                }, '通过订单号创建报备成功');
            } catch (error) {
                console.error('通过订单号创建报备失败:', error);
                return this.fail(res, `通过订单号创建报备失败: ${error.message}`, 500);
            }
        } catch (error) {
            console.error('通过第三方订单号添加订单报备失败:', error);
            return this.fail(res, `通过第三方订单号添加订单报备失败: ${error.message}`, 500);
        }
    }
}

module.exports = OrderReportController;