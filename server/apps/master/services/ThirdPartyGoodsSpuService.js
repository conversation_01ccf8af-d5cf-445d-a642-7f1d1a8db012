/**
 * 第三方商品SPU业务服务
 * 处理第三方平台商品创建的业务逻辑
 */
const ThirdPartyGoodsSpuModel = require('../models/ThirdPartyGoodsSpuModel');

class ThirdPartyGoodsSpuService {
  constructor(prisma) {
    this.prisma = prisma;
    this.thirdPartyGoodsSpuModel = new ThirdPartyGoodsSpuModel(prisma);
  }

  /**
   * 创建第三方商品
   * @param {Object} productData - 商品数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建结果
   */
  async createThirdPartyProduct(productData, userId) {
    try {
      console.log('开始创建第三方商品:', {
        name: productData.name,
        channelId: productData.channelId,
        platformId: productData.platformId,
        storeId: productData.storeId,
        skuCount: productData.skus?.length || 0
      });

      // 1. 数据预处理
      const processedData = this.preprocessProductData(productData);

      // 2. 业务验证
      await this.validateBusinessRules(processedData);

      // 3. 调用模型层创建商品
      const result = await this.thirdPartyGoodsSpuModel.createThirdPartyProduct(
        processedData, 
        userId
      );

      console.log('第三方商品创建成功:', {
        spuId: result.spu.id,
        spuCode: result.spu.spuCode,
        skuCount: result.skuCount,
        totalStock: result.totalStock
      });

      return {
        success: true,
        message: '第三方商品创建成功',
        data: {
          id: result.spu.id,
          spuCode: result.spu.spuCode,
          name: result.spu.name,
          skuCount: result.skuCount,
          totalStock: result.totalStock,
          createdAt: result.spu.createdAt
        }
      };
    } catch (error) {
      console.error('创建第三方商品失败:', error);
      
      return {
        success: false,
        message: error.message || '创建第三方商品失败',
        data: null
      };
    }
  }

  /**
   * 获取第三方商品列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 商品列表结果
   */
  async getThirdPartyProductList(options) {
    try {
      console.log('获取第三方商品列表:', options);

      const result = await this.thirdPartyGoodsSpuModel.getThirdPartyProductList(options);

      // 处理返回数据，添加业务字段
      const processedItems = result.items.map(item => this.formatProductListItem(item));

      return {
        success: true,
        message: '获取第三方商品列表成功',
        data: {
          items: processedItems,
          total: result.total
        }
      };
    } catch (error) {
      console.error('获取第三方商品列表失败:', error);
      
      return {
        success: false,
        message: '获取第三方商品列表失败',
        data: { items: [], total: 0 }
      };
    }
  }

  /**
   * 获取第三方商品详情
   * @param {string} id - 商品ID
   * @returns {Promise<Object>} - 商品详情结果
   */
  async getThirdPartyProductById(id) {
    try {
      console.log('获取第三方商品详情:', { id });

      const product = await this.thirdPartyGoodsSpuModel.getThirdPartyProductById(id);

      if (!product) {
        return {
          success: false,
          message: '商品不存在',
          data: null
        };
      }

      // 格式化商品详情数据
      const formattedProduct = this.formatProductDetail(product);

      return {
        success: true,
        message: '获取第三方商品详情成功',
        data: formattedProduct
      };
    } catch (error) {
      console.error('获取第三方商品详情失败:', error);
      
      return {
        success: false,
        message: '获取第三方商品详情失败',
        data: null
      };
    }
  }

  /**
   * 数据预处理
   * @param {Object} productData - 原始商品数据
   * @returns {Object} - 处理后的商品数据
   */
  preprocessProductData(productData) {
    return {
      name: productData.name.trim(),
      subtitle: productData.subtitle?.trim() || null,
      description: productData.description?.trim() || null,
      channelId: productData.channelId.toString(),
      platformId: productData.platformId.toString(),
      storeId: productData.storeId.toString(),
      skus: productData.skus.map(sku => ({
        skuCode: sku.skuCode.trim(),
        salesPrice: parseFloat(sku.salesPrice),
        stock: parseInt(sku.stock)
      }))
    };
  }

  /**
   * 业务规则验证
   * @param {Object} productData - 商品数据
   * @returns {Promise<void>}
   */
  async validateBusinessRules(productData) {
    // 1. 验证SKU数量限制
    if (productData.skus.length > 100) {
      throw new Error('单个商品最多支持100个SKU');
    }

    // 2. 验证价格范围
    const invalidPrices = productData.skus.filter(sku => 
      sku.salesPrice <= 0 || sku.salesPrice > 999999.99
    );
    if (invalidPrices.length > 0) {
      throw new Error('SKU销售价格必须在0.01-999999.99之间');
    }

    // 3. 验证库存范围
    const invalidStocks = productData.skus.filter(sku => 
      sku.stock < 0 || sku.stock > 999999
    );
    if (invalidStocks.length > 0) {
      throw new Error('SKU库存数量必须在0-999999之间');
    }

    // 4. 验证商品名称长度
    if (productData.name.length > 255) {
      throw new Error('商品名称不能超过255个字符');
    }

    // 5. 验证SKU编码格式
    const invalidSkuCodes = productData.skus.filter(sku => 
      !/^[A-Za-z0-9_-]+$/.test(sku.skuCode)
    );
    if (invalidSkuCodes.length > 0) {
      throw new Error('SKU编码只能包含字母、数字、下划线和横线');
    }
  }

  /**
   * 格式化商品列表项
   * @param {Object} item - 原始商品数据
   * @returns {Object} - 格式化后的商品数据
   */
  formatProductListItem(item) {
    // 计算价格区间
    const prices = item.goodsSkus?.map(sku => sku.salesPrice) || [];
    const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
    const maxPrice = prices.length > 0 ? Math.max(...prices) : 0;

    // 收集所有销售渠道信息
    const channels = new Map();
    const platforms = new Map();
    const stores = new Map();

    item.goodsSkus?.forEach(sku => {
      sku.goodsSkuChannels?.forEach(skuChannel => {
        if (skuChannel.channel) {
          channels.set(skuChannel.channel.id, skuChannel.channel);
        }
        if (skuChannel.platform) {
          platforms.set(skuChannel.platform.id, skuChannel.platform);
        }
        if (skuChannel.store) {
          stores.set(skuChannel.store.id, skuChannel.store);
        }
      });
    });

    return {
      id: item.id,
      spuCode: item.spuCode,
      name: item.name,
      subtitle: item.subtitle,
      status: item.status,
      statusText: item.status === 1 ? '已上架' : '已下架',
      skuCount: item.goodsSkus?.length || 0,
      totalStock: item.totalStock,
      priceRange: minPrice === maxPrice ?
        `¥${minPrice.toFixed(2)}` :
        `¥${minPrice.toFixed(2)} - ¥${maxPrice.toFixed(2)}`,
      // 商品在多个渠道、平台、店铺销售
      channels: Array.from(channels.values()),
      platforms: Array.from(platforms.values()),
      stores: Array.from(stores.values()),
      createdAt: item.createdAt,
      updatedAt: item.updatedAt
    };
  }

  /**
   * 格式化商品详情
   * @param {Object} product - 原始商品数据
   * @returns {Object} - 格式化后的商品详情
   */
  formatProductDetail(product) {
    // 处理SKU数据，包含销售渠道信息
    const skus = product.goodsSkus?.map(sku => {
      const salesChannels = sku.goodsSkuChannels?.map(skuChannel => ({
        channel: skuChannel.channel ? {
          id: skuChannel.channel.id,
          name: skuChannel.channel.name
        } : null,
        platform: skuChannel.platform ? {
          id: skuChannel.platform.id,
          name: skuChannel.platform.name
        } : null,
        store: skuChannel.store ? {
          id: skuChannel.store.id,
          name: skuChannel.store.name
        } : null,
        isEnabled: skuChannel.isEnabled === 1
      })) || [];

      return {
        id: sku.id,
        skuCode: sku.skuCode,
        salesPrice: sku.salesPrice,
        marketPrice: sku.marketPrice,
        costPrice: sku.costPrice,
        stock: sku.stock,
        weight: sku.weight,
        volume: sku.volume,
        unit: sku.unit,
        isEnabled: sku.isEnabled === 1,
        salesChannels: salesChannels // 显示该SKU在哪些渠道销售
      };
    }) || [];

    return {
      id: product.id,
      spuCode: product.spuCode,
      name: product.name,
      subtitle: product.subtitle,
      slug: product.slug,
      description: product.description,
      status: product.status,
      statusText: product.status === 1 ? '已上架' : '已下架',
      isVirtual: product.isVirtual === 1,
      sortOrder: product.sortOrder,
      totalSales: product.totalSales,
      totalStock: product.totalStock,
      skus: skus,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    };
  }
}

module.exports = ThirdPartyGoodsSpuService;
