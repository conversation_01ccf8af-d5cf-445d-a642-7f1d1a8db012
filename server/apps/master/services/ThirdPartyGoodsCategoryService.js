/**
 * 第三方商品分类服务层
 * 处理第三方平台分类管理的业务逻辑
 */
const ThirdPartyGoodsCategoryModel = require('../models/ThirdPartyGoodsCategoryModel');

class ThirdPartyGoodsCategoryService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
    this.thirdPartyGoodsCategoryModel = new ThirdPartyGoodsCategoryModel();
  }

  /**
   * 创建第三方商品分类
   * @param {Object} categoryData - 分类数据
   * @param {BigInt} userId - 用户ID
   * @returns {Promise<Object>} 创建结果
   */
  async createThirdPartyCategory(categoryData, userId) {
    try {
      console.log('第三方商品分类服务 - 开始创建分类:', {
        name: categoryData.name,
        channelId: categoryData.channelId,
        platformId: categoryData.platformId,
        storeId: categoryData.storeId,
        parentId: categoryData.parentId,
        userId: userId.toString()
      });

      // 调用模型层创建分类
      const result = await this.thirdPartyGoodsCategoryModel.createThirdPartyCategory(categoryData, userId);

      console.log('第三方商品分类服务 - 分类创建成功:', {
        id: result.id,
        name: result.name,
        level: result.level
      });

      return {
        success: true,
        message: '第三方商品分类创建成功',
        data: result
      };
    } catch (error) {
      console.error('第三方商品分类服务 - 创建分类失败:', error);

      // 根据错误类型返回不同的错误信息
      let errorMessage = '创建第三方商品分类失败';
      let errorCode = 500;

      if (error.message.includes('不存在')) {
        errorMessage = error.message;
        errorCode = 404;
      } else if (error.message.includes('已存在') || error.message.includes('重复')) {
        errorMessage = error.message;
        errorCode = 400;
      } else if (error.message.includes('验证失败') || error.message.includes('无效')) {
        errorMessage = error.message;
        errorCode = 400;
      }

      return {
        success: false,
        message: errorMessage,
        data: errorCode
      };
    }
  }

  /**
   * 更新第三方商品分类
   * @param {string} categoryId - 分类ID
   * @param {Object} categoryData - 更新数据
   * @param {BigInt} userId - 用户ID
   * @returns {Promise<Object>} 更新结果
   */
  async updateThirdPartyCategory(categoryId, categoryData, userId) {
    try {
      console.log('第三方商品分类服务 - 开始更新分类:', {
        categoryId,
        updateFields: Object.keys(categoryData),
        userId: userId.toString()
      });

      // 调用模型层更新分类
      const result = await this.thirdPartyGoodsCategoryModel.updateThirdPartyCategory(categoryId, categoryData, userId);

      console.log('第三方商品分类服务 - 分类更新成功:', {
        id: result.id,
        name: result.name
      });

      return {
        success: true,
        message: '第三方商品分类更新成功',
        data: result
      };
    } catch (error) {
      console.error('第三方商品分类服务 - 更新分类失败:', error);

      // 根据错误类型返回不同的错误信息
      let errorMessage = '更新第三方商品分类失败';
      let errorCode = 500;

      if (error.message.includes('不存在')) {
        errorMessage = error.message;
        errorCode = 404;
      } else if (error.message.includes('已存在') || error.message.includes('重复')) {
        errorMessage = error.message;
        errorCode = 400;
      } else if (error.message.includes('验证失败') || error.message.includes('无效')) {
        errorMessage = error.message;
        errorCode = 400;
      }

      return {
        success: false,
        message: errorMessage,
        data: errorCode
      };
    }
  }
}

module.exports = ThirdPartyGoodsCategoryService;
