/**
 * 发票管理服务
 */
const InvoiceModel = require('../models/InvoiceModel');
const InvoiceApplicationModel = require('../models/InvoiceApplicationModel');
const OrderModel = require('../models/OrderModel/OrderModel');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const {
  ORDER_INVOICE_STATUS,
  ORDER_INVOICE_STATUS_TEXT,
  INVOICE_STATUS,
  INVOICE_STATUS_TEXT
} = require('../constants/InvoiceConstants');

class InvoiceService {
  constructor() {
    this.invoiceModel = new InvoiceModel();
    this.invoiceApplicationModel = new InvoiceApplicationModel();
    this.orderModel = new OrderModel();
  }

  /**
   * 根据订单ID获取发票信息
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Object>} 服务响应
   */
  async getInvoiceByOrderId(orderId) {
    try {
      if (!orderId) {
        return {
          success: false,
          message: '订单ID不能为空',
          data: null
        };
      }

      const invoice = await this.invoiceModel.getInvoiceByOrderId(orderId);
      
      return {
        success: true,
        message: '获取发票信息成功',
        data: invoice
      };
    } catch (error) {
      console.error('获取发票信息失败:', error);
      return {
        success: false,
        message: '获取发票信息失败',
        data: null
      };
    }
  }

  /**
   * 创建发票记录
   * @param {Object} invoiceData - 发票数据
   * @param {string|number|BigInt} operatorId - 操作人ID
   * @returns {Promise<Object>} 服务响应
   */
  async createInvoice(invoiceData, operatorId) {
    try {
      const { applicationId, orderId, invoiceAmount, invoiceFiles } = invoiceData;

      if (!applicationId || !orderId) {
        return {
          success: false,
          message: '申请ID和订单ID不能为空',
          data: null
        };
      }

      // 检查申请是否存在
      const application = await this.invoiceApplicationModel.getApplicationById(applicationId);
      if (!application) {
        return {
          success: false,
          message: '发票申请不存在',
          data: null
        };
      }

      // 创建发票记录
      const invoice = await this.invoiceModel.createInvoice({
        application_id: applicationId,
        order_id: orderId,
        invoice_amount: invoiceAmount || application.applicationAmount,
        invoice_files: invoiceFiles ? JSON.stringify(invoiceFiles) : null,
        invoice_status: 0, // 已开具
        created_by: operatorId
      });

      // 更新申请状态为已通过
      await this.invoiceApplicationModel.updateApplicationStatus(applicationId, 1, {
        reviewed_by: operatorId,
        reviewed_at: BigInt(Date.now()),
        review_remark: '发票已开具'
      });

      return {
        success: true,
        message: '创建发票记录成功',
        data: invoice
      };
    } catch (error) {
      console.error('创建发票记录失败:', error);
      return {
        success: false,
        message: '创建发票记录失败',
        data: null
      };
    }
  }

  /**
   * 更新发票信息
   * @param {string|number|BigInt} id - 发票ID
   * @param {Object} updateData - 更新数据
   * @param {string|number|BigInt} operatorId - 操作人ID
   * @returns {Promise<Object>} 服务响应
   */
  async updateInvoice(id, updateData, operatorId) {
    try {
      if (!id) {
        return {
          success: false,
          message: '发票ID不能为空',
          data: null
        };
      }

      const invoice = await this.invoiceModel.updateInvoice(id, {
        ...updateData,
        updated_by: operatorId
      });

      return {
        success: true,
        message: '更新发票信息成功',
        data: invoice
      };
    } catch (error) {
      console.error('更新发票信息失败:', error);
      return {
        success: false,
        message: '更新发票信息失败',
        data: null
      };
    }
  }

  /**
   * 作废发票
   * @param {string|number|BigInt} id - 发票ID
   * @param {string} reason - 作废原因
   * @param {string|number|BigInt} operatorId - 操作人ID
   * @returns {Promise<Object>} 服务响应
   */
  async voidInvoice(id, reason, operatorId) {
    try {
      if (!id) {
        return {
          success: false,
          message: '发票ID不能为空',
          data: null
        };
      }

      const invoice = await this.invoiceModel.voidInvoice(id, reason, operatorId);

      return {
        success: true,
        message: '作废发票成功',
        data: invoice
      };
    } catch (error) {
      console.error('作废发票失败:', error);
      return {
        success: false,
        message: '作废发票失败',
        data: null
      };
    }
  }

  /**
   * 记录订单发票文件信息
   * @param {string|number|BigInt} orderId - 订单ID
   * @param {Array} fileUrls - 文件URL数组
   * @param {string|number|BigInt} operatorId - 操作人ID
   * @returns {Promise<Object>} 服务响应
   */
  async recordInvoiceFiles(orderId, fileUrls, operatorId) {
    try {
      console.log(`[InvoiceService] 开始记录发票文件 - 订单ID: ${orderId}, 文件数量: ${fileUrls?.length}, 操作人: ${operatorId}`);

      if (!orderId) {
        return {
          success: false,
          message: '订单ID不能为空',
          data: null
        };
      }

      if (!fileUrls || fileUrls.length === 0) {
        return {
          success: false,
          message: '没有上传文件信息',
          data: null
        };
      }

      console.log(`[InvoiceService] 文件URLs:`, fileUrls);

      // 检查订单是否存在
      const order = await this.orderModel.getOrderById(orderId);
      if (!order) {
        console.log(`[InvoiceService] 订单不存在: ${orderId}`);
        return {
          success: false,
          message: '订单不存在',
          data: null
        };
      }

      console.log(`[InvoiceService] 找到订单:`, {
        id: order.id,
        totalAmount: order.totalAmount,
        userId: order.userId
      });

      // 检查是否有发票申请（内置渠道订单可以直接上传发票，无需申请）
      let application = await this.invoiceApplicationModel.getApplicationByOrderId(orderId);
      console.log(`[InvoiceService] 发票申请查询结果:`, application ? { id: application.id, status: application.applicationStatus } : '无申请');

      // 如果没有发票申请，为内置渠道订单自动创建一个默认申请
      if (!application) {
        try {
          console.log(`[InvoiceService] 创建默认发票申请`);
          // 确保金额是数字类型
          const applicationAmount = order.totalAmount ? parseFloat(order.totalAmount.toString()) : 0;

          // 生成申请ID
          const applicationId = generateSnowflakeId();

          application = await this.invoiceApplicationModel.createApplication({
            id: BigInt(applicationId),
            order_id: BigInt(orderId),
            user_id: BigInt(order.userId || operatorId), // 添加用户ID字段
            application_amount: applicationAmount,
            application_type: 1, // 个人发票
            application_status: 0, // 待审核
            snapshot_header_type: 1, // 个人抬头
            snapshot_header_name: '个人',
            created_by: BigInt(operatorId)
          });
          console.log(`[InvoiceService] 默认发票申请创建成功:`, { id: application.id });
        } catch (createError) {
          console.error('创建默认发票申请失败:', createError);
          return {
            success: false,
            message: '创建发票申请失败',
            data: null
          };
        }
      }

      // 处理已上传的文件信息（简化为只存储URL）
      const uploadedFiles = fileUrls.map((url, index) => {
        // 从URL中提取文件名
        const fileName = url.split('/').pop() || `invoice_${Date.now()}_${index + 1}`;

        return {
          fileName: fileName,
          fileUrl: url,
          uploadTime: new Date().toISOString()
        };
      });

      // 检查是否已有发票记录
      let existingInvoices = await this.invoiceModel.getInvoicesByApplicationId(application.id);
      console.log(`[InvoiceService] 现有发票记录数量:`, existingInvoices.length);

      // 为每个文件创建或更新发票记录
      const invoiceResults = [];

      for (let i = 0; i < uploadedFiles.length; i++) {
        const fileInfo = uploadedFiles[i];
        let invoice;

        if (existingInvoices[i]) {
          // 更新现有发票记录
          console.log(`[InvoiceService] 更新现有发票记录 ${i + 1}, 新文件URL: ${fileInfo.fileUrl}`);

          invoice = await this.invoiceModel.updateInvoice(existingInvoices[i].id, {
            file_url: fileInfo.fileUrl,
            invoice_status: INVOICE_STATUS.ISSUED, // 已开具
            updated_by: BigInt(operatorId)
          });
          console.log(`[InvoiceService] 发票记录更新成功:`, { id: invoice.id });
        } else {
          // 创建新的发票记录
          const invoiceId = generateSnowflakeId();
          console.log(`[InvoiceService] 创建新发票记录 ${i + 1}, ID: ${invoiceId}`);

          invoice = await this.invoiceModel.createInvoice({
            id: BigInt(invoiceId),
            application_id: BigInt(application.id),
            order_id: BigInt(orderId),
            invoice_amount: parseFloat(application.applicationAmount.toString()),
            file_url: fileInfo.fileUrl,
            invoice_status: INVOICE_STATUS.ISSUED, // 已开具
            created_by: BigInt(operatorId)
          });
          console.log(`[InvoiceService] 新发票记录创建成功:`, { id: invoice.id });
        }

        invoiceResults.push(invoice);
      }

      // 更新申请状态为已通过（开票审核通过）
      await this.invoiceApplicationModel.updateApplicationStatus(application.id, 1, {
        reviewed_by: BigInt(operatorId),
        reviewed_at: BigInt(Date.now()),
        review_remark: '发票文件已上传，审核通过'
      });

      // 更新订单状态：标记为已开票
      try {
        console.log(`[InvoiceService] 准备更新订单发票状态: ${orderId} -> ${ORDER_INVOICE_STATUS.INVOICED}`);

        const updateResult = await this.orderModel.updateOrder(orderId, {
          invoice_status: ORDER_INVOICE_STATUS.INVOICED // 已开票
        });

        console.log(`[InvoiceService] 订单发票状态更新成功:`, {
          orderId: orderId,
          newInvoiceStatus: updateResult.invoiceStatus,
          updateTime: updateResult.updatedAt
        });
      } catch (updateError) {
        console.error('更新订单发票状态失败:', updateError);
        // 不影响主流程，只记录错误
      }

      return {
        success: true,
        message: `成功上传 ${uploadedFiles.length} 个发票文件，创建了 ${invoiceResults.length} 条发票记录`,
        data: {
          invoices: invoiceResults,
          uploadedFiles: uploadedFiles,
          totalCount: invoiceResults.length
        }
      };
    } catch (error) {
      console.error('上传发票文件失败:', error);
      return {
        success: false,
        message: '上传发票文件失败',
        data: null
      };
    }
  }

  /**
   * 检查订单是否有发票申请
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Object>} 服务响应
   */
  async checkInvoiceApplication(orderId) {
    try {
      if (!orderId) {
        return {
          success: false,
          message: '订单ID不能为空',
          data: null
        };
      }

      // 获取订单信息
      const order = await this.orderModel.getOrderById(orderId);
      if (!order) {
        return {
          success: false,
          message: '订单不存在',
          data: null
        };
      }

      // 获取发票申请
      const application = await this.invoiceApplicationModel.getApplicationByOrderId(orderId);

      // 获取发票记录（获取所有相关的发票记录）
      let invoices = [];
      if (application) {
        invoices = await this.invoiceModel.getInvoicesByApplicationId(application.id);
      }

      return {
        success: true,
        message: '检查发票申请成功',
        data: {
          hasApplication: !!application,
          application: application,
          hasInvoice: invoices.length > 0,
          invoices: invoices,
          invoiceCount: invoices.length,
          order: {
            id: order.id,
            orderStatus: order.orderStatus,
            paymentStatus: order.paymentStatus,
            shippingStatus: order.shippingStatus,
            orderSourceText: order.orderSourceText || '内置'
          },
          // 新增：判断是否可以上传发票（内置渠道且已发货）
          canUploadInvoice: (!order.orderSourceText || order.orderSourceText === '内置' || order.orderSourceText === '') &&
                           (order.shippingStatus === 1 || order.shippingStatusText === '已发货')
        }
      };
    } catch (error) {
      console.error('检查发票申请失败:', error);
      return {
        success: false,
        message: '检查发票申请失败',
        data: null
      };
    }
  }

  /**
   * 查看订单的所有发票
   * @param {string|number|BigInt} orderId - 订单ID
   * @returns {Promise<Object>} 服务响应
   */
  async getOrderInvoices(orderId) {
    try {
      if (!orderId) {
        return {
          success: false,
          message: '订单ID不能为空',
          data: null
        };
      }

      console.log(`[InvoiceService] 查看订单发票 - 订单ID: ${orderId}`);

      // 获取订单信息
      const order = await this.orderModel.getOrderById(orderId);
      if (!order) {
        return {
          success: false,
          message: '订单不存在',
          data: null
        };
      }

      // 获取发票申请
      const application = await this.invoiceApplicationModel.getApplicationByOrderId(orderId);

      // 获取所有发票记录
      let invoices = [];
      if (application) {
        invoices = await this.invoiceModel.getInvoicesByApplicationId(application.id);
        console.log(`[InvoiceService] 找到 ${invoices.length} 条发票记录`);
      }

      // 处理发票文件信息
      const invoiceFiles = invoices.map(invoice => ({
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        invoiceAmount: invoice.invoiceAmount,
        invoiceStatus: invoice.invoiceStatus,
        invoiceStatusText: this.getInvoiceStatusText(invoice.invoiceStatus),
        fileUrl: invoice.fileUrl,
        fileName: invoice.fileUrl ? invoice.fileUrl.split('/').pop() : null,
        fileType: invoice.fileUrl ? this.getFileType(invoice.fileUrl) : null,
        createdAt: invoice.createdAt,
        updatedAt: invoice.updatedAt
      }));

      return {
        success: true,
        message: `查询成功，找到 ${invoices.length} 条发票记录`,
        data: {
          order: {
            id: order.id,
            orderNumber: order.orderNumber,
            totalAmount: order.totalAmount,
            orderStatus: order.orderStatus,
            orderStatusText: order.orderStatusText,
            invoiceStatus: order.invoiceStatus || 0,
            invoiceStatusText: order.invoiceStatus === 1 ? '已开票' : '未开票'
          },
          application: application,
          invoices: invoiceFiles,
          totalCount: invoices.length,
          hasInvoice: invoices.length > 0
        }
      };
    } catch (error) {
      console.error('查看订单发票失败:', error);
      return {
        success: false,
        message: '查看订单发票失败',
        data: null
      };
    }
  }

  /**
   * 获取发票状态文本
   * @param {number} status - 发票状态
   * @returns {string} 状态文本
   */
  getInvoiceStatusText(status) {
    return INVOICE_STATUS_TEXT[status] || '未知状态';
  }

  /**
   * 获取文件类型
   * @param {string} fileUrl - 文件URL
   * @returns {string} 文件类型
   */
  getFileType(fileUrl) {
    if (!fileUrl) return 'unknown';

    const extension = fileUrl.split('.').pop().toLowerCase();
    const typeMap = {
      'pdf': 'PDF文档',
      'jpg': '图片',
      'jpeg': '图片',
      'png': '图片',
      'gif': '图片',
      'bmp': '图片'
    };
    return typeMap[extension] || '其他文件';
  }
}

module.exports = InvoiceService;
