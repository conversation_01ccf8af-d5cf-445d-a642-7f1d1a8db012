const ThirdPartyGoodsBrandDto = require('../dto/ThirdPartyGoodsBrandDto');
const ThirdPartyGoodsBrandModel = require('../models/ThirdPartyGoodsBrandModel');

/**
 * 第三方商品品牌服务
 * 处理第三方平台的品牌业务逻辑
 */
class ThirdPartyGoodsBrandService {
  constructor(prisma) {
    this.prisma = prisma;
    this.thirdPartyGoodsBrandModel = new ThirdPartyGoodsBrandModel(prisma);
  }

  /**
   * 创建第三方商品品牌
   * @param {Object} brandData 品牌数据
   * @returns {Promise<Object>} 创建的品牌信息
   */
  async createThirdPartyBrand(brandData) {
    try {
      // 数据验证
      const validatedData = ThirdPartyGoodsBrandDto.validateCreate(brandData);
      
      // 创建品牌
      const brand = await this.thirdPartyGoodsBrandModel.createThirdPartyBrand(validatedData);
      
      // 格式化响应数据
      return this.thirdPartyGoodsBrandModel.formatBrandResponse(brand);
    } catch (error) {
      console.error('创建第三方商品品牌失败:', error);
      throw error;
    }
  }

  /**
   * 更新第三方商品品牌
   * @param {string} brandId 品牌ID
   * @param {Object} brandData 更新的品牌数据
   * @returns {Promise<Object>} 更新后的品牌信息
   */
  async updateThirdPartyBrand(brandId, brandData) {
    try {
      // 数据验证
      const validatedData = ThirdPartyGoodsBrandDto.validateUpdate(brandData);
      
      // 更新品牌
      const brand = await this.thirdPartyGoodsBrandModel.updateThirdPartyBrand(brandId, validatedData);
      
      // 格式化响应数据
      return this.thirdPartyGoodsBrandModel.formatBrandResponse(brand);
    } catch (error) {
      console.error(`更新第三方商品品牌失败 (ID: ${brandId}):`, error);
      throw error;
    }
  }
}

module.exports = ThirdPartyGoodsBrandService;
