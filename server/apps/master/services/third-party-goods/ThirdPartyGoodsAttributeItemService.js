/**
 * 第三方商品属性参数项服务
 * 处理第三方平台商品属性参数项的业务逻辑
 */
const ThirdPartyGoodsAttributeItemModel = require('../../models/ThirdPartyGoodsAttributeItemModel');
const { 
  createThirdPartyGoodsAttributeItemDto, 
  updateThirdPartyGoodsAttributeItemDto 
} = require('../../dto/third-party-goods/ThirdPartyGoodsAttributeItemDto');

class ThirdPartyGoodsAttributeItemService {
  constructor() {
    this.model = new ThirdPartyGoodsAttributeItemModel();
  }

  /**
   * 创建第三方商品属性参数项
   * @param {Object} attributeItemData - 属性参数项数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建结果
   */
  async createThirdPartyAttributeItem(attributeItemData, userId = BigInt(1)) {
    try {
      // 1. 数据验证
      const { error, value } = createThirdPartyGoodsAttributeItemDto.validate(attributeItemData);
      if (error) {
        return {
          success: false,
          code: 400,
          message: '数据验证失败',
          errors: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }))
        };
      }

      // 2. 调用模型创建属性参数项
      const result = await this.model.createThirdPartyAttributeItem(value, userId);

      return {
        success: true,
        code: 200,
        message: '第三方商品属性参数项创建成功',
        data: result.data
      };

    } catch (error) {
      console.error('创建第三方商品属性参数项失败:', error);
      
      // 处理特定的业务错误
      if (error.message.includes('不存在') || error.message.includes('已存在')) {
        return {
          success: false,
          code: 400,
          message: error.message
        };
      }

      return {
        success: false,
        code: 500,
        message: '创建第三方商品属性参数项时发生内部错误'
      };
    }
  }

  /**
   * 更新第三方商品属性参数项
   * @param {string} id - 属性参数项ID
   * @param {Object} attributeItemData - 更新数据
   * @param {BigInt} userId - 更新者ID
   * @returns {Promise<Object>} - 更新结果
   */
  async updateThirdPartyAttributeItem(id, attributeItemData, userId = BigInt(1)) {
    try {
      // 1. 验证ID格式
      if (!id || !/^\d+$/.test(id)) {
        return {
          success: false,
          code: 400,
          message: '属性参数项ID格式无效'
        };
      }

      // 2. 数据验证
      const { error, value } = updateThirdPartyGoodsAttributeItemDto.validate(attributeItemData);
      if (error) {
        return {
          success: false,
          code: 400,
          message: '数据验证失败',
          errors: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }))
        };
      }

      // 3. 检查是否有更新内容
      if (Object.keys(value).length === 0) {
        return {
          success: false,
          code: 400,
          message: '没有提供需要更新的数据'
        };
      }

      // 4. 调用模型更新属性参数项
      const result = await this.model.updateThirdPartyAttributeItem(id, value, userId);

      return {
        success: true,
        code: 200,
        message: '第三方商品属性参数项更新成功',
        data: result.data
      };

    } catch (error) {
      console.error('更新第三方商品属性参数项失败:', error);
      
      // 处理特定的业务错误
      if (error.message.includes('不存在') || error.message.includes('已存在')) {
        return {
          success: false,
          code: 400,
          message: error.message
        };
      }

      return {
        success: false,
        code: 500,
        message: '更新第三方商品属性参数项时发生内部错误'
      };
    }
  }

  /**
   * 格式化响应数据
   * @param {Object} data - 原始数据
   * @returns {Object} - 格式化后的数据
   */
  formatResponseData(data) {
    return {
      attributeItemId: data.attributeItemId,
      name: data.name,
      goodsAttributeSetId: data.goodsAttributeSetId,
      type: data.type,
      value: data.value,
      isRequired: data.isRequired,
      isFilterable: data.isFilterable,
      sortOrder: data.sortOrder,
      isEnabled: data.isEnabled,
      channelId: data.channelId,
      platformId: data.platformId,
      storeId: data.storeId,
      platformAttributeItemId: data.platformAttributeItemId,
      platformAttributeItemName: data.platformAttributeItemName,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    };
  }
}

module.exports = ThirdPartyGoodsAttributeItemService;
