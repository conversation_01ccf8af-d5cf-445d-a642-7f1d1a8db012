/**
 * 第三方商品属性集服务
 * 处理第三方平台商品属性集的业务逻辑
 */
const ThirdPartyGoodsAttributeSetModel = require('../../models/ThirdPartyGoodsAttributeSetModel');
const { 
  createThirdPartyGoodsAttributeSetDto, 
  updateThirdPartyGoodsAttributeSetDto 
} = require('../../dto/third-party-goods/ThirdPartyGoodsAttributeSetDto');

class ThirdPartyGoodsAttributeSetService {
  constructor() {
    this.model = new ThirdPartyGoodsAttributeSetModel();
  }

  /**
   * 创建第三方商品属性集
   * @param {Object} attributeSetData - 属性集数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建结果
   */
  async createThirdPartyAttributeSet(attributeSetData, userId = BigInt(1)) {
    try {
      // 1. 数据验证
      const { error, value } = createThirdPartyGoodsAttributeSetDto.validate(attributeSetData);
      if (error) {
        return {
          success: false,
          code: 400,
          message: '数据验证失败',
          errors: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }))
        };
      }

      // 2. 调用模型创建属性集
      const result = await this.model.createThirdPartyAttributeSet(value, userId);

      return {
        success: true,
        code: 200,
        message: '第三方商品属性集创建成功',
        data: result.data
      };

    } catch (error) {
      console.error('创建第三方商品属性集失败:', error);
      
      // 处理特定的业务错误
      if (error.message.includes('不存在') || error.message.includes('已存在')) {
        return {
          success: false,
          code: 400,
          message: error.message
        };
      }

      return {
        success: false,
        code: 500,
        message: '创建第三方商品属性集时发生内部错误'
      };
    }
  }

  /**
   * 更新第三方商品属性集
   * @param {string} id - 属性集ID
   * @param {Object} attributeSetData - 更新数据
   * @param {BigInt} userId - 更新者ID
   * @returns {Promise<Object>} - 更新结果
   */
  async updateThirdPartyAttributeSet(id, attributeSetData, userId = BigInt(1)) {
    try {
      // 1. 验证ID格式
      if (!id || !/^\d+$/.test(id)) {
        return {
          success: false,
          code: 400,
          message: '属性集ID格式无效'
        };
      }

      // 2. 数据验证
      const { error, value } = updateThirdPartyGoodsAttributeSetDto.validate(attributeSetData);
      if (error) {
        return {
          success: false,
          code: 400,
          message: '数据验证失败',
          errors: error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message
          }))
        };
      }

      // 3. 检查是否有更新内容
      if (Object.keys(value).length === 0) {
        return {
          success: false,
          code: 400,
          message: '没有提供需要更新的数据'
        };
      }

      // 4. 调用模型更新属性集
      const result = await this.model.updateThirdPartyAttributeSet(id, value, userId);

      return {
        success: true,
        code: 200,
        message: '第三方商品属性集更新成功',
        data: result.data
      };

    } catch (error) {
      console.error('更新第三方商品属性集失败:', error);
      
      // 处理特定的业务错误
      if (error.message.includes('不存在') || error.message.includes('已存在')) {
        return {
          success: false,
          code: 400,
          message: error.message
        };
      }

      return {
        success: false,
        code: 500,
        message: '更新第三方商品属性集时发生内部错误'
      };
    }
  }

  /**
   * 格式化响应数据
   * @param {Object} data - 原始数据
   * @returns {Object} - 格式化后的数据
   */
  formatResponseData(data) {
    return {
      attributeSetId: data.attributeSetId,
      name: data.name,
      sortOrder: data.sortOrder,
      channelId: data.channelId,
      platformId: data.platformId,
      storeId: data.storeId,
      platformAttributeSetId: data.platformAttributeSetId,
      platformAttributeSetName: data.platformAttributeSetName,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    };
  }
}

module.exports = ThirdPartyGoodsAttributeSetService;
