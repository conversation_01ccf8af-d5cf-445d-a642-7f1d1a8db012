// 第三方商品分类模型
model ThirdPartyGoodsCategory {
  id                        BigInt    @id @default(autoincrement())    // 主键ID
  parent_id                 BigInt?                                     // 父分类ID，用于构建分类树结构
  name                      String                                      // 分类名称
  image_url                 String?                                     // 分类图片URL
  description               String?                                     // 分类描述
  meta_title                String?                                     // SEO标题
  meta_keywords             String?                                     // SEO关键词
  meta_description          String?                                     // SEO描述
  sort_order                Int       @default(0)                       // 排序顺序
  is_enabled                Int       @default(1)                       // 是否启用：1-启用，0-禁用
  level                     Int?      @default(1)                       // 分类层级：1-一级分类，2-二级分类，3-三级分类
  source_type               Int       @default(1)                       // 来源类型：固定为1-第三方分类
  created_channel_id        BigInt                                      // 创建时的渠道ID
  created_platform_id       BigInt                                      // 创建时的平台ID
  created_store_id          BigInt                                      // 创建时的店铺ID
  created_at                BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at                BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at                BigInt?                                     // 删除时间戳（毫秒，软删除）
  created_by                BigInt?                                     // 创建人ID
  updated_by                BigInt?                                     // 最后更新人ID

  // 关联关系
  parent                    ThirdPartyGoodsCategory?                    @relation("ThirdPartyCategoryToSubcategory", fields: [parent_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subcategories             ThirdPartyGoodsCategory[]                   @relation("ThirdPartyCategoryToSubcategory")

  @@map("third_party_goods_categories")
  @@index([deleted_at], map: "idx_third_party_goods_categories_deleted_at")
  @@index([parent_id], map: "idx_third_party_goods_categories_parent_id")
  @@index([is_enabled], map: "idx_third_party_goods_categories_is_enabled")
  @@index([created_by], map: "idx_third_party_goods_categories_created_by")
  @@index([updated_by], map: "idx_third_party_goods_categories_updated_by")
  @@index([source_type], map: "idx_third_party_goods_categories_source_type")
  @@index([created_channel_id], map: "idx_third_party_goods_categories_created_channel_id")
  @@index([created_platform_id], map: "idx_third_party_goods_categories_created_platform_id")
  @@index([created_store_id], map: "idx_third_party_goods_categories_created_store_id")
  @@index([created_channel_id, created_platform_id, created_store_id], map: "idx_third_party_goods_categories_channel_platform_store")
  @@index([sort_order], map: "idx_third_party_goods_categories_sort_order")
  @@index([level], map: "idx_third_party_goods_categories_level")
  @@index([created_at], map: "idx_third_party_goods_categories_created_at")
}
