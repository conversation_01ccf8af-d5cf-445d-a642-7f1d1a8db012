// 商品品牌平台关联模型
model GoodsBrandPlatformRelation {
  id                    BigInt    @id                                                                     // 主键ID，雪花算法生成
  goods_brand_id        BigInt                                                                           // 品牌ID，关联goods_brands表
  channel_id            BigInt                                                                           // 渠道ID，关联channel表
  platform_id           BigInt                                                                           // 平台ID，关联platform表
  store_id              BigInt                                                                           // 店铺ID，关联store表
  platform_brand_id     String?   @db.VarChar(100)                                                      // 第三方平台品牌ID
  platform_brand_name   String?   @db.VarChar(255)                                                      // 第三方平台品牌名称
  push_status           Int       @default(0)                                                            // 预留字段 推送状态：0-未推送，1-推送成功，2-推送失败
  sync_status           Int       @default(0)                                                            // 预留字段 同步状态：0-未同步，1-同步成功，2-同步失败
  push_time             BigInt?                                                                          // 预留字段 推送时间戳（毫秒）
  sync_time             BigInt?                                                                          // 预留字段 同步时间戳（毫秒）
  push_error_message    String?                                                                          // 预留字段 推送错误信息
  sync_error_message    String?                                                                          // 预留字段 同步错误信息
  created_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))             // 创建时间戳（毫秒）
  updated_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))             // 更新时间戳（毫秒）
  deleted_at            BigInt?                                                                          // 删除时间戳（毫秒，软删除）

  // 索引
  @@index([goods_brand_id], map: "idx_goods_brand_platform_relations_brand_id")
  @@index([channel_id], map: "idx_goods_brand_platform_relations_channel_id")
  @@index([platform_id], map: "idx_goods_brand_platform_relations_platform_id")
  @@index([store_id], map: "idx_goods_brand_platform_relations_store_id")
  @@index([push_status], map: "idx_goods_brand_platform_relations_push_status")
  @@index([sync_status], map: "idx_goods_brand_platform_relations_sync_status")
  @@index([deleted_at], map: "idx_goods_brand_platform_relations_deleted_at")
  @@index([goods_brand_id, channel_id, platform_id, store_id], map: "idx_goods_brand_platform_relations_unique")
  @@map("goods_brand_platform_relations")
  @@schema("base")
}
