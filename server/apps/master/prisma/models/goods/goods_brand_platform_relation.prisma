// 商品品牌平台关联模型
model GoodsBrandPlatformRelation {
  id                    BigInt    @id                                                                     // 主键ID，雪花算法生成
  goods_brand_id        BigInt                                                                           // 品牌ID，关联goods_brands表
  channel_id            BigInt                                                                           // 渠道ID，关联channel表
  platform_id           BigInt                                                                           // 平台ID，关联platform表
  store_id              BigInt                                                                           // 店铺ID，关联store表
  platform_brand_id     String?   @db.VarChar(100)                                                      // 第三方平台品牌ID
  platform_brand_name   String?   @db.VarChar(255)                                                      // 第三方平台品牌名称
  created_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))             // 创建时间戳（毫秒）
  updated_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))             // 更新时间戳（毫秒）
  deleted_at            BigInt?                                                                          // 删除时间戳（毫秒，软删除）

  // 索引
  @@index([goods_brand_id], map: "idx_goods_brand_platform_relations_brand_id")
  @@index([channel_id], map: "idx_goods_brand_platform_relations_channel_id")
  @@index([platform_id], map: "idx_goods_brand_platform_relations_platform_id")
  @@index([store_id], map: "idx_goods_brand_platform_relations_store_id")
  @@index([deleted_at], map: "idx_goods_brand_platform_relations_deleted_at")
  @@index([goods_brand_id, channel_id, platform_id, store_id], map: "idx_goods_brand_platform_relations_unique")
  @@map("goods_brand_platform_relations")
  @@schema("base")
}
