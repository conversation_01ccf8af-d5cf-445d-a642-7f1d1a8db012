// 商品品牌模型
model GoodsBrand {
  id                  BigInt    @id @default(autoincrement())
  name                String    @db.VarChar(100)               // 品牌名称
  logo_url            String?   @db.VarChar(255)               // 品牌logo URL
  description         String?                                   // 品牌描述
  source_type         Int?      @default(0)                    // 来源类型：0-系统创建，1-第三方创建
  created_channel_id  BigInt?                                   // 创建渠道ID（第三方品牌）
  created_platform_id BigInt?                                   // 创建平台ID（第三方品牌）
  created_store_id    BigInt?                                   // 创建店铺ID（第三方品牌）
  created_at          BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at          BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at          BigInt?                                   // 删除时间戳（毫秒，软删除）
  created_by          BigInt?                                   // 创建人ID
  updated_by          BigInt?                                   // 最后更新人ID

  // 关联商品
  goods_spus  GoodsSpu[]

  @@index([deleted_at], map: "idx_goods_brands_deleted_at")
  @@index([created_by], map: "idx_goods_brands_created_by")
  @@index([updated_by], map: "idx_goods_brands_updated_by")
  @@index([source_type], map: "idx_goods_brands_source_type")
  @@index([created_channel_id], map: "idx_goods_brands_created_channel_id")
  @@index([created_platform_id], map: "idx_goods_brands_created_platform_id")
  @@index([created_store_id], map: "idx_goods_brands_created_store_id")
  @@map("goods_brands")
  @@schema("base")
}
