// 商品分类平台关系模型（管理分类在不同渠道、平台、店铺的关系和同步状态）
model GoodsCategoryPlatformRelation {
  id                    BigInt    @id                                                               // 主键ID
  goods_category_id     BigInt                                                                      // 商品分类ID，关联goods_categories表
  channel_id            BigInt                                                                      // 渠道ID，关联channel表
  platform_id           BigInt                                                                      // 平台ID，支持分类在多平台使用
  store_id              BigInt                                                                      // 店铺ID，支持分类在多店铺使用
  platform_category_id  String?   @db.VarChar(100)                                                // 第三方平台的分类ID
  platform_category_name String?  @db.VarChar(255)                                                // 第三方平台的分类名称

  // 业务状态字段
  is_enabled            Int       @default(1)                                                       // 是否启用：1-启用，0-禁用
  sort_order            Int       @default(0)                                                       // 在该平台的排序
  
  // 审计字段
  created_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))        // 创建时间戳（毫秒）
  updated_at            BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))        // 更新时间戳（毫秒）
  deleted_at            BigInt?                                                                     // 删除时间戳（毫秒，软删除）
  created_by            BigInt?                                                                     // 创建人ID
  updated_by            BigInt?                                                                     // 最后更新人ID

  // 关联关系（暂时注释掉，避免循环引用问题）
  // goods_category        GoodsCategory @relation(fields: [goods_category_id], references: [id])     // 关联到商品分类
  // channel               channel       @relation(fields: [channel_id], references: [id])            // 关联到渠道
  // platform              platform      @relation(fields: [platform_id], references: [id])          // 关联到平台
  // store                 store         @relation(fields: [store_id], references: [id])              // 关联到店铺

  @@map("goods_category_platform_relations")
  @@schema("base")
  @@index([goods_category_id], map: "idx_goods_category_platform_relations_category_id")
  @@index([channel_id], map: "idx_goods_category_platform_relations_channel_id")
  @@index([platform_id], map: "idx_goods_category_platform_relations_platform_id")
  @@index([store_id], map: "idx_goods_category_platform_relations_store_id")
  @@index([goods_category_id, channel_id, platform_id, store_id], map: "idx_goods_category_platform_relations_unique")
  @@index([is_enabled], map: "idx_goods_category_platform_relations_enabled")
  @@index([created_at], map: "idx_goods_category_platform_relations_created_at")
  @@index([deleted_at], map: "idx_goods_category_platform_relations_deleted_at")
}
