// SKU平台关系模型（管理SKU在不同渠道、平台、店铺的关系和同步状态）
model GoodsSkuPlatformRelation {
  id                    BigInt    @id                                                               // 主键ID
  goods_sku_id          BigInt                                                                      // 商品SKU ID，关联goods_skus表
  channel_id            BigInt                                                                      // 渠道ID，关联channel表
  platform_id           BigInt                                                                      // 平台ID，支持SKU在多平台销售
  store_id              BigInt                                                                      // 店铺ID，支持SKU在多店铺销售
  third_party_sku_code  String?   @db.VarChar(100)                                                 // 第三方SKU编码
  is_enabled            Int       @default(1) @db.SmallInt                                          // 是否启用：1-启用，0-禁用
  created_at            BigInt                                                                      // 创建时间戳（毫秒）
  updated_at            BigInt                                                                      // 更新时间戳（毫秒）
  deleted_at            BigInt?                                                                     // 删除时间戳（毫秒，软删除）
  
  // 推送相关字段
  push_status           Int       @default(0) @db.SmallInt                                          // 推送状态：0=未推送，1=推送中，2=推送成功，3=推送失败
  push_time             BigInt?                                                                     // 推送时间
  push_error_msg        String?   @db.Text                                                          // 推送错误信息
  push_retry_count      Int       @default(0) @db.SmallInt                                          // 推送重试次数
  
  // 同步相关字段
  sync_status           Int       @default(0) @db.SmallInt                                          // 同步状态：0=未同步，1=同步中，2=同步成功，3=同步失败
  sync_time             BigInt?                                                                     // 同步时间
  sync_error_msg        String?   @db.Text                                                          // 同步错误信息
  sync_retry_count      Int       @default(0) @db.SmallInt                                          // 同步重试次数
  
  // 平台相关字段
  platform_sku_id       String?   @db.VarChar(100)                                                 // 平台返回的SKU ID
  platform_status       Int?      @db.SmallInt                                                     // 平台状态
  platform_price        Decimal?  @db.Decimal(10, 2)                                               // 平台价格
  platform_stock        Int?                                                                       // 平台库存
  last_sync_data        Json?                                                                      // 最后同步的完整数据（JSON格式）

  // 关联
  goods_sku             GoodsSku  @relation(fields: [goods_sku_id], references: [id], onDelete: Cascade, onUpdate: Cascade)  // 关联到商品SKU
  channel               channel   @relation(fields: [channel_id], references: [id], onDelete: Cascade, onUpdate: Cascade)    // 关联到渠道
  platform              platform  @relation(fields: [platform_id], references: [id], onDelete: Cascade, onUpdate: Cascade)  // 关联到平台
  store                 store     @relation(fields: [store_id], references: [id], onDelete: Cascade, onUpdate: Cascade)      // 关联到店铺

  // 索引
  @@index([channel_id, platform_id, store_id, is_enabled], name: "idx_goods_sku_platform_relation_composite")
  @@index([platform_id], name: "idx_goods_sku_platform_relation_platform")
  @@index([platform_sku_id], name: "idx_goods_sku_platform_relation_platform_sku_id")
  @@index([push_status, push_retry_count, push_time], name: "idx_goods_sku_platform_relation_push_retry")
  @@index([push_status], name: "idx_goods_sku_platform_relation_push_status")
  @@index([store_id], name: "idx_goods_sku_platform_relation_store")
  @@index([sync_status, sync_retry_count, sync_time], name: "idx_goods_sku_platform_relation_sync_retry")
  @@index([sync_status], name: "idx_goods_sku_platform_relation_sync_status")

  // 唯一约束
  @@unique([goods_sku_id, channel_id, platform_id, store_id], name: "idx_goods_sku_platform_relation_unique_combination")
  
  @@map("goods_sku_platform_relation")                                                             // 映射到数据库表名
  @@schema("base")                                                                                  // 所属schema
}
