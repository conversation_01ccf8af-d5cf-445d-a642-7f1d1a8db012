model GoodsAttributeItem {
  id                     BigInt                   @id // 主键ID
  goods_attribute_set_id BigInt                    // 属性集ID
  name                   String                   @db.VarChar(100) // 属性名称
  type                   String                   @db.VarChar(50) // 属性类型（如文本、数字、选项等）
  value                  Json?                    // 属性值（JSON格式）
  is_required            Int                      @default(0) // 是否必填：0-否，1-是
  is_filterable          Int                      @default(0) // 是否可筛选：0-否，1-是
  sort_order             Int                      @default(0) // 排序顺序
  is_enabled             Int                      @default(1) // 是否启用：0-禁用，1-启用
  created_at             BigInt                   @default(0) // 创建时间戳（毫秒）
  updated_at             BigInt                   @default(0) // 更新时间戳（毫秒）
  deleted_at             BigInt?                  // 删除时间戳（毫秒，软删除）
  created_by             BigInt?                  // 创建人ID
  updated_by             BigInt?                  // 最后更新人ID
  goods_attribute_sets   GoodsAttributeSet        @relation(fields: [goods_attribute_set_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  goods_attribute_values GoodsAttributeValue[]

  @@index([deleted_at], map: "idx_goods_attribute_items_deleted_at")
  @@index([is_filterable], map: "idx_goods_attribute_items_filterable")
  @@index([is_enabled], map: "idx_goods_attribute_items_is_enabled")
  @@index([goods_attribute_set_id], map: "idx_goods_attribute_items_set_id")
  @@index([created_by], map: "idx_goods_attribute_items_created_by")
  @@index([updated_by], map: "idx_goods_attribute_items_updated_by")
  @@map("goods_attribute_items")
  @@schema("base")
}

model GoodsAttributeSetCategoryAssociation {
  goods_attribute_set_id BigInt                  // 属性集ID
  goods_category_id      BigInt                  // 商品分类ID
  goods_attribute_sets   GoodsAttributeSet @relation(fields: [goods_attribute_set_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  // goods_categories       GoodsCategory     @relation(fields: [goods_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@id([goods_attribute_set_id, goods_category_id])
  @@index([goods_category_id], map: "idx_goods_attribute_set_category_associations_category_id")
  @@map("goods_attribute_set_category_associations")
  @@schema("base")
}

model GoodsAttributeSet {
  id                                        BigInt                                      @id // 主键ID
  name                                      String                                      @unique @db.VarChar(100) // 属性集名称
  sort_order                                Int                                         @default(0) // 排序顺序
  created_at                                BigInt                                      @default(0) // 创建时间戳（毫秒）
  updated_at                                BigInt                                      @default(0) // 更新时间戳（毫秒）
  deleted_at                                BigInt?                                     // 删除时间戳（毫秒，软删除）
  created_by                                BigInt?                                     // 创建人ID
  updated_by                                BigInt?                                     // 最后更新人ID
  goods_attribute_items                     GoodsAttributeItem[]
  goods_attribute_set_category_associations GoodsAttributeSetCategoryAssociation[]

  @@index([deleted_at], map: "idx_goods_attribute_sets_deleted_at")
  @@map("goods_attribute_sets")
  @@schema("base")
}

model GoodsAttributeValue {
  id                      BigInt                @id // 主键ID
  goods_spu_id            BigInt                // 商品SPU ID
  goods_attribute_item_id BigInt                // 商品属性项ID
  value                   String?               // 属性值
  created_at              BigInt                @default(0) // 创建时间戳（毫秒）
  updated_at              BigInt                @default(0) // 更新时间戳（毫秒）
  goods_attribute_items   GoodsAttributeItem    @relation(fields: [goods_attribute_item_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  goods_spus              GoodsSpu              @relation(fields: [goods_spu_id], references: [id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([goods_spu_id, goods_attribute_item_id], map: "goods_attribute_values_goods_spu_id_goods_attribute_it_key")
  @@index([goods_attribute_item_id], map: "idx_goods_attribute_values_item_id")
  @@index([goods_spu_id], map: "idx_goods_attribute_values_spu_id")
  @@map("goods_attribute_values")
  @@schema("base")
}

model GoodsAttributeSetPlatformRelation {
  id                          BigInt    @id @default(autoincrement())
  attribute_set_id            BigInt
  channel_id                  BigInt
  platform_id                 BigInt
  store_id                    BigInt
  platform_attribute_set_id   String?   @db.VarChar(100)
  platform_attribute_set_name String?   @db.VarChar(255)
  source_type                 Int       @default(0) @db.SmallInt
  created_at                  BigInt
  updated_at                  BigInt

  @@unique([attribute_set_id, channel_id, platform_id, store_id], map: "goods_attribute_set_platform_relation_unique")
  @@index([channel_id], map: "idx_goods_attribute_set_platform_relation_channel_id")
  @@index([platform_id], map: "idx_goods_attribute_set_platform_relation_platform_id")
  @@index([store_id], map: "idx_goods_attribute_set_platform_relation_store_id")
  @@index([attribute_set_id], map: "idx_goods_attribute_set_platform_relation_attribute_set_id")
  @@index([source_type], map: "idx_goods_attribute_set_platform_relation_source_type")
  @@map("goods_attribute_set_platform_relations")
  @@schema("base")
}

model GoodsAttributeItemPlatformRelation {
  id                          BigInt    @id @default(autoincrement())
  attribute_item_id           BigInt
  channel_id                  BigInt
  platform_id                 BigInt
  store_id                    BigInt
  platform_attribute_item_id  String?   @db.VarChar(100)
  platform_attribute_item_name String?  @db.VarChar(255)
  source_type                 Int       @default(0) @db.SmallInt
  created_at                  BigInt
  updated_at                  BigInt

  @@unique([attribute_item_id, channel_id, platform_id, store_id], map: "goods_attribute_item_platform_relation_unique")
  @@index([channel_id], map: "idx_goods_attribute_item_platform_relation_channel_id")
  @@index([platform_id], map: "idx_goods_attribute_item_platform_relation_platform_id")
  @@index([store_id], map: "idx_goods_attribute_item_platform_relation_store_id")
  @@index([attribute_item_id], map: "idx_goods_attribute_item_platform_relation_attribute_item_id")
  @@index([source_type], map: "idx_goods_attribute_item_platform_relation_source_type")
  @@map("goods_attribute_item_platform_relations")
  @@schema("base")
}
