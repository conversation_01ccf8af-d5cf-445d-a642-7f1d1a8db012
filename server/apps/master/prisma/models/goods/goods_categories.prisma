// 商品分类模型
model GoodsCategory {
  id                        BigInt    @id @default(autoincrement())    // 主键ID
  goods_parent_category_id  BigInt?                                     // 父分类ID，用于构建分类树结构
  name                      String                                      // 分类名称
  // 移除 slug 字段定义
  image_url                 String?                                     // 分类图片URL
  description               String?                                     // 分类描述
  meta_title                String?                                     // SEO标题
  meta_keywords             String?                                     // SEO关键词
  meta_description          String?                                     // SEO描述
  sort_order                Int       @default(0)                       // 排序顺序
  is_enabled                Int       @default(1)                       // 是否启用：1-启用，0-禁用
  level                     Int?                                        // 分类层级：1-一级分类，2-二级分类，3-三级分类
  created_at                BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 创建时间戳（毫秒）
  updated_at                BigInt    @default(dbgenerated("extract(epoch from now()) * 1000"))  // 更新时间戳（毫秒）
  deleted_at                BigInt?                                     // 删除时间戳（毫秒，软删除）
  created_by                BigInt?                                     // 创建人ID
  updated_by                BigInt?                                     // 最后更新人ID
  // 第三方分类相关字段
  source_type               Int?      @default(0)                       // 来源类型：0-系统分类，1-第三方分类
  created_channel_id        BigInt?                                     // 创建时的渠道ID（第三方分类专用）
  created_platform_id       BigInt?                                     // 创建时的平台ID（第三方分类专用）
  created_store_id          BigInt?                                     // 创建时的店铺ID（第三方分类专用）
  // 移除 goods_spu_count 字段定义
  parent                                    GoodsCategory?                              @relation("CategoryToSubcategory", fields: [goods_parent_category_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  subcategories                             GoodsCategory[]                             @relation("CategoryToSubcategory")
  // goods_category_platform_relations        GoodsCategoryPlatformRelation[]             // 分类平台关联关系（暂时注释避免循环引用）
  // goods_category_associations               GoodsCategoryAssociation[]
  // goods_attribute_set_category_associations GoodsAttributeSetCategoryAssociation[]

  @@index([deleted_at], map: "idx_goods_categories_deleted_at")
  @@index([goods_parent_category_id], map: "idx_goods_categories_goods_parent_id")
  @@index([is_enabled], map: "idx_goods_categories_is_enabled")
  @@index([created_by], map: "idx_goods_categories_created_by")
  @@index([updated_by], map: "idx_goods_categories_updated_by")
  @@index([source_type], map: "idx_goods_categories_source_type")
  @@index([created_channel_id], map: "idx_goods_categories_created_channel_id")
  @@index([created_platform_id], map: "idx_goods_categories_created_platform_id")
  @@index([created_store_id], map: "idx_goods_categories_created_store_id")
  @@map("goods_categories")
  @@schema("base")
}
