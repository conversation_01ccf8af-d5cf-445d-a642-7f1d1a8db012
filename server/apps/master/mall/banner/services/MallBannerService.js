const { generateSnowflakeId } = require('../../../../../shared/utils/snowflake');

/**
 * 将下划线命名转换为驼峰命名
 * @param {Object} obj 要转换的对象
 * @returns {Object} 转换后的对象
 */
function convertToCamelCase(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => convertToCamelCase(item));
  }

  const camelCaseObj = {};
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
      camelCaseObj[camelKey] = convertToCamelCase(obj[key]);
    }
  }
  return camelCaseObj;
}

/**
 * 商城轮播图服务类
 * 处理商城轮播图相关的业务逻辑
 */
class MallBannerService {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 获取商城轮播图列表
   * @param {Object} params 查询参数，包含分页和筛选条件
   * @returns {Object} 包含轮播图列表、总数和分页信息的对象
   */
  async list(params = {}) {
    const { skip, take, page, pageSize, ...filters } = params;
    const skipValue = skip || 0;
    const takeValue = take || parseInt(pageSize) || 10;

    // 构建查询条件
    const where = { deleted_at: null };
    const allowKeys = ['id', 'status', 'link_type'];
    
    // 精确匹配字段
    for (const key of allowKeys) {
      if (filters[key] !== undefined && filters[key] !== '') {
        where[key] = filters[key];
      }
    }
    
    // 模糊查询字段
    if (filters.title) {
      where.title = { contains: filters.title };
    }
    
    // 关键字模糊搜索
    if (filters.keyword && filters.keyword.trim() !== '') {
      const keyword = filters.keyword.trim();
      where.OR = [
        { title: { contains: keyword } },
        { remark: { contains: keyword } }
      ];
    }

    // 执行查询
    const [total, banners] = await Promise.all([
      this.prisma.MallBanner.count({ where }),
      this.prisma.MallBanner.findMany({
        where,
        skip: skipValue,
        take: takeValue,
        orderBy: [
          { sort_order: 'asc' },  // 首先按排序字段升序
          { created_at: 'desc' }  // 然后按创建时间降序
        ]
      })
    ]);

    return convertToCamelCase({
      list: banners,
      total,
      page: parseInt(page) || 1,
      pageSize: takeValue
    });
  }

  /**
   * 创建商城轮播图
   * @param {Object} bannerData 轮播图数据
   * @returns {Object} 创建的轮播图对象
   */
  async create(bannerData) {
    // 检查标题是否已存在（包括已删除的记录）
    const existingBanner = await this.prisma.MallBanner.findFirst({
      where: { 
        title: bannerData.title
      }
    });

    // 如果找到同名记录，且该记录未被删除，则报错
    if (existingBanner && existingBanner.deleted_at === null) {
      throw new Error('轮播图标题已存在');
    }
    
    // 如果找到同名记录，但该记录已被删除，则可以使用同名创建新记录
    if (existingBanner && existingBanner.deleted_at !== null) {
      // 彻底删除已软删除的同名记录
      await this.prisma.MallBanner.delete({
        where: { id: existingBanner.id }
      });
      console.log('已删除旧的同名记录:', existingBanner.id);
    }

    // 生成雪花ID
    const id = generateSnowflakeId();
    
    // 获取当前时间戳
    const now = BigInt(Date.now());
    
    // 确保必填字段有值
    if (!bannerData.title) {
      throw new Error('轮播图标题不能为空');
    }
    if (!bannerData.image_url) {
      throw new Error('图片地址不能为空');
    }
    
    const createData = {
      id,
      title: bannerData.title,
      image_url: bannerData.image_url,
      sort_order: bannerData.sort_order || 0,
      link_url: bannerData.link_url || null,
      link_type: bannerData.link_type || 3,
      status: bannerData.status !== undefined ? parseInt(bannerData.status) : 1,
      remark: bannerData.remark || null,
      created_at: now,
      updated_at: now,
      created_by: bannerData.created_by ? BigInt(bannerData.created_by) : null,
      updated_by: bannerData.created_by ? BigInt(bannerData.created_by) : null
    };
    
    console.log('创建轮播图数据:', createData);
    
    const newBanner = await this.prisma.MallBanner.create({
      data: createData
    });

    return convertToCamelCase(newBanner);
  }

  /**
   * 更新商城轮播图
   * @param {string} id 轮播图ID
   * @param {Object} bannerData 更新的轮播图数据
   * @returns {Object} 更新后的轮播图对象
   */
  async update(id, bannerData) {
    // 检查轮播图是否存在
    const existingBanner = await this.prisma.MallBanner.findUnique({
      where: { id: BigInt(id) }
    });

    if (!existingBanner) {
      throw new Error('轮播图不存在');
    }

    // 如果要更新标题，检查新标题是否已被使用
    if (bannerData.title && bannerData.title !== existingBanner.title) {
      const bannerWithSameTitle = await this.prisma.MallBanner.findFirst({
        where: { 
          title: bannerData.title,
          deleted_at: null,
          id: { not: BigInt(id) }
        }
      });

      if (bannerWithSameTitle) {
        throw new Error('轮播图标题已存在');
      }
    }
    
    // 更新时间戳
    const now = BigInt(Date.now());
    
    // 构建更新数据
    const updateData = {};
    
    // 只更新提供的字段
    if (bannerData.title !== undefined) updateData.title = bannerData.title;
    if (bannerData.image_url !== undefined) updateData.image_url = bannerData.image_url;
    if (bannerData.sort_order !== undefined) updateData.sort_order = bannerData.sort_order;
    if (bannerData.link_url !== undefined) updateData.link_url = bannerData.link_url;
    if (bannerData.link_type !== undefined) updateData.link_type = bannerData.link_type;
    if (bannerData.status !== undefined) updateData.status = parseInt(bannerData.status);
    if (bannerData.remark !== undefined) updateData.remark = bannerData.remark;
    
    // 添加审计字段
    updateData.updated_at = now;
    if (bannerData.updated_by) updateData.updated_by = BigInt(bannerData.updated_by);
    
    console.log('更新轮播图数据:', updateData);
    
    // 更新轮播图
    const updatedBanner = await this.prisma.MallBanner.update({
      where: { id: BigInt(id) },
      data: updateData
    });

    return convertToCamelCase(updatedBanner);
  }

  /**
   * 删除商城轮播图（软删除）
   * @param {string} id 轮播图ID
   * @param {string} updatedBy 操作人ID
   * @returns {Object} 删除结果
   */
  async delete(id, updatedBy) {
    // 检查轮播图是否存在
    const existingBanner = await this.prisma.MallBanner.findUnique({
      where: { id: BigInt(id) }
    });

    if (!existingBanner) {
      throw new Error('轮播图不存在');
    }

    console.log('删除轮播图:', id);

    // 软删除轮播图
    const now = BigInt(Date.now());
    await this.prisma.MallBanner.update({
      where: { id: BigInt(id) },
      data: {
        deleted_at: now,
        updated_at: now,
        updated_by: updatedBy ? BigInt(updatedBy) : null
      }
    });

    return convertToCamelCase({ success: true });
  }

  /**
   * 根据ID获取商城轮播图详情
   * @param {string} id 轮播图ID
   * @returns {Object} 轮播图详情
   */
  async getById(id) {
    const banner = await this.prisma.MallBanner.findFirst({
      where: { 
        id: BigInt(id),
        deleted_at: null
      }
    });

    if (!banner) {
      throw new Error('轮播图不存在');
    }

    return convertToCamelCase(banner);
  }
}

module.exports = MallBannerService;
