const express = require('express');
const router = express.Router();
const MallBannerController = require('../controllers/MallBannerController');
const { prisma } = require('../../../../../core/database/prisma');
const RouterConfig = require('../../../../../core/routes/RouterConfig');

// 获取base schema的prisma客户端
const controller = new MallBannerController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/master/mall/banner:
 *   get:
 *     tags: [商城管理/轮播图管理]
 *     summary: 获取商城轮播图列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/PageSizeParam'
 *       - in: query
 *         name: title
 *         schema:
 *           type: string
 *         description: 轮播图标题（模糊搜索）
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: 状态：1-启用，0-禁用
 *       - in: query
 *         name: link_type
 *         schema:
 *           type: integer
 *           enum: [1, 2, 3]
 *         description: 网址类型：1-内部链接，2-外部链接，3-无链接
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *         description: 关键字搜索（标题、备注）
 *     responses:
 *       200:
 *         $ref: '#/components/responses/MallBannerListResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.get('/', controller.list.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/banner:
 *   post:
 *     tags: [商城管理/轮播图管理]
 *     summary: 创建商城轮播图
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MallBannerCreateRequest'
 *     responses:
 *       201:
 *         $ref: '#/components/responses/MallBannerResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 */
protectedRouter.post('/', controller.create.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/banner/{id}:
 *   put:
 *     tags: [商城管理/轮播图管理]
 *     summary: 更新商城轮播图
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/MallBannerUpdateRequest'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/MallBannerResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 轮播图不存在
 */
protectedRouter.put('/:id', controller.update.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/banner/{id}:
 *   delete:
 *     tags: [商城管理/轮播图管理]
 *     summary: 删除商城轮播图
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       204:
 *         description: 删除成功
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 轮播图不存在
 */
protectedRouter.delete('/:id', controller.delete.bind(controller));

/**
 * @swagger
 * /api/v1/master/mall/banner/{id}:
 *   get:
 *     tags: [商城管理/轮播图管理]
 *     summary: 获取商城轮播图详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/IdParam'
 *     responses:
 *       200:
 *         $ref: '#/components/responses/MallBannerResponse'
 *       '200_error':
 *         $ref: '#/components/responses/Error'
 *       401:
 *         description: 未授权，请先登录
 *       404:
 *         description: 轮播图不存在
 */
protectedRouter.get('/:id', controller.getById.bind(controller));

module.exports = router;
