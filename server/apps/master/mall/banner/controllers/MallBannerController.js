const BaseController = require('../../../../../core/controllers/BaseController');
const MallBannerService = require('../services/MallBannerService');
const MallBannerDto = require('../dto/MallBannerDto');

/**
 * 商城轮播图管理控制器
 * 处理商城轮播图相关的请求和响应
 */
class MallBannerController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
    this.bannerService = new MallBannerService(prisma);
  }

  /**
   * 创建商城轮播图
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async create(req, res) {
    try {
      console.log('创建轮播图请求数据:', req.body);
      
      // 处理前端传递的字段
      const formattedData = {
        title: req.body.title,
        imageUrl: req.body.image_url || req.body.imageUrl,
        sortOrder: req.body.sort_order || req.body.sortOrder || 0,
        linkUrl: req.body.link_url || req.body.linkUrl || '',
        linkType: req.body.link_type || req.body.linkType || 3,
        status: req.body.status !== undefined ? parseInt(req.body.status) : 1,
        remark: req.body.remark || ''
      };
      
      console.log('格式化后的数据:', formattedData);
      
      // 验证请求数据
      const { error, value } = MallBannerDto.validateCreate(formattedData);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      // 添加创建者信息
      value.created_by = req.user?.id;
      
      console.log('最终传递给服务层的数据:', value);
      
      // 创建轮播图
      const banner = await this.bannerService.create(value);
      
      // 处理BigInt序列化
      const serializedBanner = JSON.parse(JSON.stringify(banner, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));
      
      return res.status(200).json({
        success: true,
        message: '创建商城轮播图成功',
        code: 200,
        data: serializedBanner
      });
    } catch (err) {
      console.error('创建商城轮播图失败:', err);
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 更新商城轮播图
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async update(req, res) {
    try {
      console.log('更新轮播图请求数据:', req.body);
      
      // 处理前端传递的字段
      const formattedData = {
        id: req.params.id,
        title: req.body.title,
        imageUrl: req.body.image_url || req.body.imageUrl,
        sortOrder: req.body.sort_order || req.body.sortOrder,
        linkUrl: req.body.link_url || req.body.linkUrl,
        linkType: req.body.link_type || req.body.linkType,
        remark: req.body.remark
      };
      
      // 处理 status 字段
      if (req.body.status !== undefined) {
        formattedData.status = parseInt(req.body.status);
      }
      
      console.log('格式化后的数据:', formattedData);
      
      // 去除未定义的字段
      Object.keys(formattedData).forEach(key => {
        if (formattedData[key] === undefined) {
          delete formattedData[key];
        }
      });
      
      console.log('去除未定义字段后的数据:', formattedData);
      
      // 验证请求数据
      const { error, value } = MallBannerDto.validateUpdate(formattedData);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      console.log('验证后的数据:', value);
      
      // 添加更新者信息
      value.updated_by = req.user?.id;
      
      // 更新轮播图
      const updatedBanner = await this.bannerService.update(req.params.id, value);
      
      // 处理BigInt序列化
      const serializedBanner = JSON.parse(JSON.stringify(updatedBanner, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));
      
      return res.status(200).json({
        success: true,
        message: '更新商城轮播图成功',
        code: 200,
        data: serializedBanner
      });
    } catch (error) {
      const { message, code } = this.handleDbError(error);
      return this.fail(res, message, code);
    }
  }

  /**
   * 删除商城轮播图
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async delete(req, res) {
    try {
      // 传入当前操作用户ID作为更新人
      await this.bannerService.delete(req.params.id, req.user?.id);
      
      return this.success(res, '删除商城轮播图成功', null, 200);
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取商城轮播图详情
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async getById(req, res) {
    try {
      const banner = await this.bannerService.getById(req.params.id);
      
      // 处理BigInt序列化
      const serializedBanner = JSON.parse(JSON.stringify(banner, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));
      
      return this.success(res, '获取商城轮播图详情成功', serializedBanner);
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }

  /**
   * 获取商城轮播图列表
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async list(req, res) {
    try {
      // 参数校验（包含分页参数）
      const { error, value } = MallBannerDto.validateQuery(req.query);
      if (error) {
        return this.fail(res, error.details[0].message, 400);
      }
      
      // 提取分页参数
      const { page, pageSize, ...filters } = value;
      // 组装分页参数
      const pagination = this.getPagination({ page, pageSize });
      // 调用服务获取轮播图列表
      const result = await this.bannerService.list({ ...filters, ...pagination });
      // 处理BigInt序列化
      const serializedItems = JSON.parse(JSON.stringify(result.list, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      }));

      // 使用服务层返回的pageSize计算总页数，确保与实际分页一致
      const actualPageSize = result.pageSize || parseInt(pageSize);
      const totalPage = Math.ceil(result.total / actualPageSize);
      
      return res.status(200).json({
        success: true,
        message: '获取商城轮播图列表成功',
        code: 200,
        data: {
          items: serializedItems,
          pageInfo: {
            total: result.total,
            currentPage: parseInt(page),
            totalPage: totalPage
          }
        }
      });
    } catch (err) {
      const { message, code } = this.handleDbError(err);
      return this.fail(res, message, code);
    }
  }
}

module.exports = MallBannerController;
