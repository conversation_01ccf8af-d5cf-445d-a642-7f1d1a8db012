const Joi = require('joi');
const BaseDto = require('../../../../../core/dto/BaseDto');

/**
 * 商城轮播图数据传输对象
 * 用于验证商城轮播图接口的请求参数
 */
class MallBannerDto extends BaseDto {
  /**
   * 验证创建商城轮播图的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateCreate(data) {
    const schema = Joi.object({
      title: Joi.string().required().max(100).messages({
        'string.base': '轮播图标题必须是字符串',
        'string.empty': '轮播图标题不能为空',
        'string.max': '轮播图标题长度不能超过100个字符',
        'any.required': '轮播图标题是必填项'
      }),
      imageUrl: Joi.string().required().max(500).messages({
        'string.base': '图片地址必须是字符串',
        'string.empty': '图片地址不能为空',
        'string.max': '图片地址长度不能超过500个字符',
        'any.required': '图片地址是必填项'
      }),
      sortOrder: Joi.number().integer().min(0).default(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      linkUrl: Joi.string().max(500).allow(null, '').messages({
        'string.base': '跳转网址必须是字符串',
        'string.max': '跳转网址长度不能超过500个字符'
      }),
      linkType: Joi.number().integer().valid(1, 2, 3).default(3).messages({
        'number.base': '网址类型必须是数字',
        'number.integer': '网址类型必须是整数',
        'any.only': '网址类型只能是1(内部链接)、2(外部链接)或3(无链接)'
      }),
      status: Joi.number().integer().valid(0, 1).default(1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(启用)'
      }),
      remark: Joi.string().max(500).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过500个字符'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证更新商城轮播图的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).required().messages({
        'any.required': 'ID不能为空',
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      title: Joi.string().max(100).messages({
        'string.base': '轮播图标题必须是字符串',
        'string.max': '轮播图标题长度不能超过100个字符'
      }),
      imageUrl: Joi.string().max(500).messages({
        'string.base': '图片地址必须是字符串',
        'string.max': '图片地址长度不能超过500个字符'
      }),
      sortOrder: Joi.number().integer().min(0).messages({
        'number.base': '排序必须是数字',
        'number.integer': '排序必须是整数',
        'number.min': '排序不能小于0'
      }),
      linkUrl: Joi.string().max(500).allow(null, '').messages({
        'string.base': '跳转网址必须是字符串',
        'string.max': '跳转网址长度不能超过500个字符'
      }),
      linkType: Joi.number().integer().valid(1, 2, 3).messages({
        'number.base': '网址类型必须是数字',
        'number.integer': '网址类型必须是整数',
        'any.only': '网址类型只能是1(内部链接)、2(外部链接)或3(无链接)'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(启用)'
      }),
      remark: Joi.string().max(500).allow(null, '').messages({
        'string.base': '备注必须是字符串',
        'string.max': '备注长度不能超过500个字符'
      }),
      updatedBy: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': '更新者ID格式不正确，必须为数字字符串'
      })
    });

    return this.validate(data, schema);
  }

  /**
   * 验证查询商城轮播图的参数
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证结果，包含转换后的数据
   */
  static validateQuery(data) {
    const schema = Joi.object({
      id: Joi.string().pattern(/^[0-9]+$/).messages({
        'string.pattern.base': 'ID格式不正确，必须为数字字符串'
      }),
      title: Joi.string().allow('').optional().messages({
        'string.base': '轮播图标题必须是字符串'
      }),
      linkType: Joi.number().integer().valid(1, 2, 3).messages({
        'number.base': '网址类型必须是数字',
        'number.integer': '网址类型必须是整数',
        'any.only': '网址类型只能是1(内部链接)、2(外部链接)或3(无链接)'
      }),
      status: Joi.number().integer().valid(0, 1).messages({
        'number.base': '状态必须是数字',
        'number.integer': '状态必须是整数',
        'any.only': '状态只能是0(禁用)或1(启用)'
      }),
      keyword: Joi.string().allow('').optional().messages({
        'string.base': '关键字必须是字符串'
      }),
      // 分页参数
      page: Joi.number().integer().min(1).default(1).messages({
        'number.base': '页码必须是数字',
        'number.integer': '页码必须是整数',
        'number.min': '页码不能小于1'
      }),
      pageSize: Joi.number().integer().min(1).max(100).default(10).messages({
        'number.base': '每页条数必须是数字',
        'number.integer': '每页条数必须是整数',
        'number.min': '每页条数不能小于1',
        'number.max': '每页条数不能超过100'
      })
    });

    return this.validate(data, schema);
  }
}

module.exports = MallBannerDto;
