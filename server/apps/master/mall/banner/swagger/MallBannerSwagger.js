/**
 * 商城轮播图管理 Swagger 文档定义
 */

module.exports = {
  schemas: {
    // 商城轮播图基础模型
    MallBanner: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: '轮播图ID，16位雪花算法'
        },
        title: {
          type: 'string',
          description: '轮播图标题，必填'
        },
        image_url: {
          type: 'string',
          description: '图片地址，必填'
        },
        sort_order: {
          type: 'integer',
          description: '排序，数字越小越靠前，默认0'
        },
        link_url: {
          type: 'string',
          description: '跳转网址，可为空'
        },
        link_type: {
          type: 'integer',
          enum: [1, 2, 3],
          description: '网址类型：1-内部链接，2-外部链接，3-无链接'
        },
        status: {
          type: 'integer',
          enum: [0, 1],
          description: '状态：1-启用，0-禁用'
        },
        remark: {
          type: 'string',
          description: '备注信息'
        },
        created_at: {
          type: 'string',
          description: '创建时间戳（毫秒）'
        },
        updated_at: {
          type: 'string',
          description: '更新时间戳（毫秒）'
        }
      }
    },
    
    // 创建商城轮播图请求
    MallBannerCreateRequest: {
      type: 'object',
      required: ['title', 'image_url'],
      properties: {
        title: {
          type: 'string',
          description: '轮播图标题，必填',
          maxLength: 100
        },
        image_url: {
          type: 'string',
          description: '图片地址，必填',
          maxLength: 500
        },
        sort_order: {
          type: 'integer',
          description: '排序，数字越小越靠前',
          default: 0,
          minimum: 0
        },
        link_url: {
          type: 'string',
          description: '跳转网址，可为空',
          maxLength: 500
        },
        link_type: {
          type: 'integer',
          enum: [1, 2, 3],
          description: '网址类型：1-内部链接，2-外部链接，3-无链接',
          default: 3
        },
        status: {
          type: 'integer',
          enum: [0, 1],
          description: '状态：1-启用，0-禁用',
          default: 1
        },
        remark: {
          type: 'string',
          description: '备注信息',
          maxLength: 500
        }
      }
    },
    
    // 更新商城轮播图请求
    MallBannerUpdateRequest: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: '轮播图标题',
          maxLength: 100
        },
        image_url: {
          type: 'string',
          description: '图片地址',
          maxLength: 500
        },
        sort_order: {
          type: 'integer',
          description: '排序，数字越小越靠前',
          minimum: 0
        },
        link_url: {
          type: 'string',
          description: '跳转网址',
          maxLength: 500
        },
        link_type: {
          type: 'integer',
          enum: [1, 2, 3],
          description: '网址类型：1-内部链接，2-外部链接，3-无链接'
        },
        status: {
          type: 'integer',
          enum: [0, 1],
          description: '状态：1-启用，0-禁用'
        },
        remark: {
          type: 'string',
          description: '备注信息',
          maxLength: 500
        }
      }
    }
  },

  responses: {
    // 轮播图响应
    MallBannerResponse: {
      description: '轮播图操作成功响应',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: {
                type: 'boolean',
                example: true
              },
              message: {
                type: 'string',
                example: '操作成功'
              },
              code: {
                type: 'integer',
                example: 200
              },
              data: {
                $ref: '#/components/schemas/MallBanner'
              }
            }
          }
        }
      }
    },

    // 轮播图列表响应
    MallBannerListResponse: {
      description: '轮播图列表响应',
      content: {
        'application/json': {
          schema: {
            type: 'object',
            properties: {
              success: {
                type: 'boolean',
                example: true
              },
              message: {
                type: 'string',
                example: '获取轮播图列表成功'
              },
              code: {
                type: 'integer',
                example: 200
              },
              data: {
                type: 'object',
                properties: {
                  items: {
                    type: 'array',
                    items: {
                      $ref: '#/components/schemas/MallBanner'
                    }
                  },
                  pageInfo: {
                    type: 'object',
                    properties: {
                      total: {
                        type: 'integer',
                        description: '总记录数'
                      },
                      currentPage: {
                        type: 'integer',
                        description: '当前页码'
                      },
                      totalPage: {
                        type: 'integer',
                        description: '总页数'
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
};
