/**
 * 第三方商品属性集数据模型
 * 专门处理第三方平台商品属性集创建和多平台关联的数据库操作
 */
const { prisma } = require('../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const GoodsSourceTypeEnum = require('../constants/GoodsSourceTypeEnum');

class ThirdPartyGoodsAttributeSetModel {
  constructor() {
    this.tableName = 'goods_attribute_sets';
    this.relationTableName = 'goods_attribute_set_platform_relations';
    this.schema = 'base';
  }

  /**
   * 验证渠道平台店铺关系
   * @param {string} channelId - 渠道ID
   * @param {string} platformId - 平台ID
   * @param {string} storeId - 店铺ID
   * @returns {Promise<Object>} - 验证结果
   */
  async validateChannelPlatformStore(channelId, platformId, storeId) {
    try {
      // 验证渠道是否存在
      const channel = await prisma.channel.findUnique({
        where: { id: BigInt(channelId) }
      });
      
      if (!channel) {
        return { valid: false, message: `渠道ID ${channelId} 不存在` };
      }

      // 验证平台是否存在
      const platform = await prisma.platform.findUnique({
        where: { id: BigInt(platformId) }
      });
      
      if (!platform) {
        return { valid: false, message: `平台ID ${platformId} 不存在` };
      }

      // 验证店铺是否存在
      const store = await prisma.store.findUnique({
        where: { id: BigInt(storeId) }
      });
      
      if (!store) {
        return { valid: false, message: `店铺ID ${storeId} 不存在` };
      }

      return { valid: true, message: '验证通过' };
    } catch (error) {
      console.error('验证渠道平台店铺关系失败:', error);
      return { valid: false, message: '验证渠道平台店铺关系时发生错误' };
    }
  }

  /**
   * 检查属性集名称是否已存在
   * @param {string} name - 属性集名称
   * @param {string} excludeId - 排除的ID（用于更新时检查）
   * @returns {Promise<boolean>} - 是否存在
   */
  async checkAttributeSetNameExists(name, excludeId = null) {
    try {
      const whereCondition = {
        name: name,
        deleted_at: null
      };

      if (excludeId) {
        whereCondition.id = { not: BigInt(excludeId) };
      }

      const existingAttributeSet = await prisma.goodsAttributeSet.findFirst({
        where: whereCondition
      });

      return !!existingAttributeSet;
    } catch (error) {
      console.error('检查属性集名称是否存在失败:', error);
      return false;
    }
  }

  /**
   * 创建第三方商品属性集
   * @param {Object} attributeSetData - 属性集数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建结果
   */
  async createThirdPartyAttributeSet(attributeSetData, userId) {
    return await prisma.$transaction(async (tx) => {
      const now = BigInt(Date.now());
      
      // 1. 验证渠道平台店铺关系
      const validation = await this.validateChannelPlatformStore(
        attributeSetData.channelId, 
        attributeSetData.platformId, 
        attributeSetData.storeId
      );
      
      if (!validation.valid) {
        throw new Error(validation.message);
      }

      // 2. 检查属性集名称是否已存在
      const nameExists = await this.checkAttributeSetNameExists(attributeSetData.name);
      if (nameExists) {
        throw new Error(`属性集名称 "${attributeSetData.name}" 已存在`);
      }

      // 3. 生成属性集ID
      const attributeSetId = generateSnowflakeId();
      
      // 4. 创建商品属性集
      const attributeSet = await tx.goodsAttributeSet.create({
        data: {
          id: BigInt(attributeSetId),
          name: attributeSetData.name,
          sort_order: attributeSetData.sortOrder || 0,
          created_at: now,
          updated_at: now,
          created_by: userId,
          updated_by: userId
        }
      });

      // 5. 创建平台关联关系
      const relationId = generateSnowflakeId();
      const relation = await tx.goodsAttributeSetPlatformRelation.create({
        data: {
          id: BigInt(relationId),
          attribute_set_id: BigInt(attributeSetId),
          channel_id: BigInt(attributeSetData.channelId),
          platform_id: BigInt(attributeSetData.platformId),
          store_id: BigInt(attributeSetData.storeId),
          platform_attribute_set_id: attributeSetData.platformAttributeSetId || null,
          platform_attribute_set_name: attributeSetData.platformAttributeSetName || null,
          source_type: GoodsSourceTypeEnum.THIRD_PARTY,
          created_at: now,
          updated_at: now
        }
      });

      return {
        success: true,
        data: {
          attributeSetId: attributeSetId.toString(),
          relationId: relationId.toString(),
          name: attributeSet.name,
          sortOrder: attributeSet.sort_order,
          channelId: attributeSetData.channelId,
          platformId: attributeSetData.platformId,
          storeId: attributeSetData.storeId,
          platformAttributeSetId: relation.platform_attribute_set_id,
          platformAttributeSetName: relation.platform_attribute_set_name,
          createdAt: now.toString()
        }
      };
    });
  }

  /**
   * 更新第三方商品属性集
   * @param {string} id - 属性集ID
   * @param {Object} attributeSetData - 更新数据
   * @param {BigInt} userId - 更新者ID
   * @returns {Promise<Object>} - 更新结果
   */
  async updateThirdPartyAttributeSet(id, attributeSetData, userId) {
    return await prisma.$transaction(async (tx) => {
      const now = BigInt(Date.now());
      const attributeSetId = BigInt(id);

      // 1. 检查属性集是否存在
      const existingAttributeSet = await tx.goodsAttributeSet.findFirst({
        where: {
          id: attributeSetId,
          deleted_at: null
        }
      });

      if (!existingAttributeSet) {
        throw new Error(`属性集ID ${id} 不存在`);
      }

      // 2. 如果更新了渠道平台店铺，需要验证关系
      if (attributeSetData.channelId || attributeSetData.platformId || attributeSetData.storeId) {
        // 获取当前关联信息
        const currentRelation = await tx.goodsAttributeSetPlatformRelation.findFirst({
          where: { attribute_set_id: attributeSetId }
        });

        const channelId = attributeSetData.channelId || currentRelation?.channel_id?.toString();
        const platformId = attributeSetData.platformId || currentRelation?.platform_id?.toString();
        const storeId = attributeSetData.storeId || currentRelation?.store_id?.toString();

        const validation = await this.validateChannelPlatformStore(channelId, platformId, storeId);
        if (!validation.valid) {
          throw new Error(validation.message);
        }
      }

      // 3. 如果更新了名称，检查是否重复
      if (attributeSetData.name) {
        const nameExists = await this.checkAttributeSetNameExists(attributeSetData.name, id);
        if (nameExists) {
          throw new Error(`属性集名称 "${attributeSetData.name}" 已存在`);
        }
      }

      // 4. 更新属性集基本信息
      const updateData = {
        updated_at: now,
        updated_by: userId
      };

      if (attributeSetData.name !== undefined) updateData.name = attributeSetData.name;
      if (attributeSetData.sortOrder !== undefined) updateData.sort_order = attributeSetData.sortOrder;

      const updatedAttributeSet = await tx.goodsAttributeSet.update({
        where: { id: attributeSetId },
        data: updateData
      });

      // 5. 更新平台关联关系
      const relationUpdateData = { updated_at: now };
      
      if (attributeSetData.channelId !== undefined) relationUpdateData.channel_id = BigInt(attributeSetData.channelId);
      if (attributeSetData.platformId !== undefined) relationUpdateData.platform_id = BigInt(attributeSetData.platformId);
      if (attributeSetData.storeId !== undefined) relationUpdateData.store_id = BigInt(attributeSetData.storeId);
      if (attributeSetData.platformAttributeSetId !== undefined) relationUpdateData.platform_attribute_set_id = attributeSetData.platformAttributeSetId;
      if (attributeSetData.platformAttributeSetName !== undefined) relationUpdateData.platform_attribute_set_name = attributeSetData.platformAttributeSetName;

      const updatedRelation = await tx.goodsAttributeSetPlatformRelation.updateMany({
        where: { attribute_set_id: attributeSetId },
        data: relationUpdateData
      });

      // 6. 获取更新后的完整信息
      const finalRelation = await tx.goodsAttributeSetPlatformRelation.findFirst({
        where: { attribute_set_id: attributeSetId }
      });

      return {
        success: true,
        data: {
          attributeSetId: id,
          name: updatedAttributeSet.name,
          sortOrder: updatedAttributeSet.sort_order,
          channelId: finalRelation?.channel_id?.toString(),
          platformId: finalRelation?.platform_id?.toString(),
          storeId: finalRelation?.store_id?.toString(),
          platformAttributeSetId: finalRelation?.platform_attribute_set_id,
          platformAttributeSetName: finalRelation?.platform_attribute_set_name,
          updatedAt: now.toString()
        }
      };
    });
  }
}

module.exports = ThirdPartyGoodsAttributeSetModel;
