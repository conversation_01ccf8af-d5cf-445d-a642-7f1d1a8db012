const { prisma } = require('../../../core/database/prisma');
const { recursiveSnakeToCamel } = require('../../../shared/utils/format');

/**
 * 处理 BigInt 类型的数据，将其转换为字符串
 * @param {*} data - 需要处理的数据
 * @returns {*} - 处理后的数据
 */
const handleBigInt = (data) => {
  if (data === null || data === undefined) return data;
  if (typeof data === 'bigint') return data.toString();
  if (Array.isArray(data)) return data.map(item => handleBigInt(item));
  if (typeof data === 'object') {
    const newData = {};
    for (const key in data) {
      newData[key] = handleBigInt(data[key]);
    }
    return newData;
  }
  return data;
};

/**
 * 发票申请记录模型
 * 处理发票申请的创建、查询、更新等操作
 */
class InvoiceApplicationModel {
  constructor() {
    this.prisma = prisma;
  }

  /**
   * 确保 BigInt 类型
   * @param {string|number|BigInt} id
   * @returns {BigInt}
   */
  ensureBigInt(id) {
    if (typeof id === 'bigint') return id;
    if (typeof id === 'string' || typeof id === 'number') {
      return BigInt(id);
    }
    throw new Error('Invalid ID type');
  }

  /**
   * 根据订单ID获取发票申请记录
   * @param {string|BigInt} orderId - 订单ID
   * @returns {Object|null} 发票申请记录
   */
  async getApplicationByOrderId(orderId) {
    try {
      const orderIdBigInt = this.ensureBigInt(orderId);
      console.log(`[InvoiceApplicationModel] 查询订单发票申请，订单ID: ${orderId}, BigInt: ${orderIdBigInt}`);
      
      const applications = await this.prisma.$queryRaw`
        SELECT ia.*
        FROM base.invoice_applications ia
        WHERE ia.order_id = ${orderIdBigInt} AND ia.deleted_at IS NULL
        ORDER BY ia.created_at DESC
        LIMIT 1
      `;
      
      console.log(`[InvoiceApplicationModel] 查询结果:`, applications);
      
      const result = recursiveSnakeToCamel(handleBigInt(applications));
      console.log(`[InvoiceApplicationModel] 处理后的结果:`, result);
      
      return result.length > 0 ? result[0] : null;
    } catch (error) {
      console.error('获取订单发票申请失败:', error);
      throw error;
    }
  }

  /**
   * 根据用户ID获取发票申请列表
   * @param {string|BigInt} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Array} 发票申请列表
   */
  async getApplicationsByUserId(userId, options = {}) {
    try {
      const userIdBigInt = this.ensureBigInt(userId);
      const { status, limit = 20, offset = 0 } = options;
      
      let whereClause = `ia.user_id = ${userIdBigInt} AND ia.deleted_at IS NULL`;
      if (status !== undefined) {
        whereClause += ` AND ia.application_status = ${status}`;
      }
      
      const applications = await this.prisma.$queryRaw`
        SELECT ia.*
        FROM base.invoice_applications ia
        WHERE ${whereClause}
        ORDER BY ia.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;
      
      return recursiveSnakeToCamel(handleBigInt(applications));
    } catch (error) {
      console.error('获取用户发票申请列表失败:', error);
      throw error;
    }
  }

  /**
   * 创建发票申请记录
   * @param {Object} applicationData - 申请数据
   * @returns {Object} 创建的申请记录
   */
  async createApplication(applicationData) {
    try {
      console.log(`[InvoiceApplicationModel] 创建发票申请:`, applicationData);
      
      const application = await this.prisma.invoice_applications.create({
        data: {
          ...applicationData,
          created_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
      
      console.log(`[InvoiceApplicationModel] 创建成功:`, application);
      return recursiveSnakeToCamel(handleBigInt(application));
    } catch (error) {
      console.error('创建发票申请失败:', error);
      throw error;
    }
  }

  /**
   * 更新发票申请状态
   * @param {string|BigInt} applicationId - 申请ID
   * @param {number} status - 新状态
   * @param {Object} updateData - 更新数据
   * @returns {Object} 更新后的申请记录
   */
  async updateApplicationStatus(applicationId, status, updateData = {}) {
    try {
      const applicationIdBigInt = this.ensureBigInt(applicationId);
      
      const application = await this.prisma.invoice_applications.update({
        where: { id: applicationIdBigInt },
        data: {
          application_status: status,
          updated_at: BigInt(Date.now()),
          ...updateData
        }
      });
      
      return recursiveSnakeToCamel(handleBigInt(application));
    } catch (error) {
      console.error('更新发票申请状态失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取发票申请记录
   * @param {string|BigInt} applicationId - 申请ID
   * @returns {Object|null} 发票申请记录
   */
  async getApplicationById(applicationId) {
    try {
      const applicationIdBigInt = this.ensureBigInt(applicationId);
      
      const application = await this.prisma.invoice_applications.findUnique({
        where: { id: applicationIdBigInt }
      });
      
      return application ? recursiveSnakeToCamel(handleBigInt(application)) : null;
    } catch (error) {
      console.error('获取发票申请记录失败:', error);
      throw error;
    }
  }

  /**
   * 软删除发票申请记录
   * @param {string|BigInt} applicationId - 申请ID
   * @returns {Object} 删除后的申请记录
   */
  async deleteApplication(applicationId) {
    try {
      const applicationIdBigInt = this.ensureBigInt(applicationId);
      
      const application = await this.prisma.invoice_applications.update({
        where: { id: applicationIdBigInt },
        data: {
          deleted_at: BigInt(Date.now()),
          updated_at: BigInt(Date.now())
        }
      });
      
      return recursiveSnakeToCamel(handleBigInt(application));
    } catch (error) {
      console.error('删除发票申请记录失败:', error);
      throw error;
    }
  }
}

module.exports = InvoiceApplicationModel;
