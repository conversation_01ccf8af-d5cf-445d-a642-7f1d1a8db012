/**
 * 第三方商品SPU数据模型
 * 专门处理第三方平台商品创建的数据库操作
 */
const BaseModel = require('../../../core/models/BaseModel');
const { generateSnowflakeId } = require('../../../core/utils/snowflake');
const slugify = require('slugify');

class ThirdPartyGoodsSpuModel extends BaseModel {
  constructor(prisma) {
    super(prisma);
    this.tableName = 'goods_spus';
    this.schema = 'base';
  }

  /**
   * 验证渠道、平台、店铺的关联关系
   * @param {string} channelId - 渠道ID
   * @param {string} platformId - 平台ID  
   * @param {string} storeId - 店铺ID
   * @returns {Promise<Object>} - 验证结果
   */
  async validateChannelPlatformStore(channelId, platformId, storeId) {
    try {
      // 验证渠道是否存在
      const channel = await this.prisma.channel.findUnique({
        where: { 
          id: BigInt(channelId),
          deleted_at: null 
        }
      });
      
      if (!channel) {
        return { valid: false, message: `渠道ID ${channelId} 不存在` };
      }

      // 验证平台是否存在且属于指定渠道
      const platform = await this.prisma.platform.findUnique({
        where: { 
          id: BigInt(platformId),
          deleted_at: null 
        }
      });
      
      if (!platform) {
        return { valid: false, message: `平台ID ${platformId} 不存在` };
      }
      
      if (platform.channel_id.toString() !== channelId) {
        return { valid: false, message: `平台 ${platformId} 不属于渠道 ${channelId}` };
      }

      // 验证店铺是否存在且属于指定平台
      const store = await this.prisma.store.findUnique({
        where: { 
          id: BigInt(storeId),
          deleted_at: null 
        }
      });
      
      if (!store) {
        return { valid: false, message: `店铺ID ${storeId} 不存在` };
      }
      
      if (store.platform_id.toString() !== platformId) {
        return { valid: false, message: `店铺 ${storeId} 不属于平台 ${platformId}` };
      }

      return { 
        valid: true, 
        data: { channel, platform, store } 
      };
    } catch (error) {
      console.error('验证渠道平台店铺关系失败:', error);
      return { valid: false, message: '验证渠道平台店铺关系时发生错误' };
    }
  }

  /**
   * 检查SKU编码是否已存在
   * @param {Array} skuCodes - SKU编码数组
   * @returns {Promise<Array>} - 已存在的SKU编码
   */
  async checkSkuCodesExist(skuCodes) {
    try {
      const existingSkus = await this.prisma.goodsSku.findMany({
        where: {
          sku_code: { in: skuCodes },
          deleted_at: null
        },
        select: { sku_code: true }
      });
      
      return existingSkus.map(sku => sku.sku_code);
    } catch (error) {
      console.error('检查SKU编码存在性失败:', error);
      throw error;
    }
  }

  /**
   * 生成唯一的SPU编码
   * @returns {Promise<string>} - SPU编码
   */
  async generateSpuCode() {
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    let counter = 1;
    let spuCode;

    do {
      spuCode = `TP_SPU_${timestamp}_${counter.toString().padStart(3, '0')}`;
      const existing = await this.prisma.goodsSpu.findUnique({
        where: { spu_code: spuCode }
      });
      if (!existing) break;
      counter++;
    } while (counter <= 999);

    if (counter > 999) {
      throw new Error('无法生成唯一的SPU编码');
    }

    return spuCode;
  }

  /**
   * 生成商品slug
   * @param {string} name - 商品名称
   * @returns {string} - 生成的slug
   */
  generateSlug(name) {
    // 使用 slugify 将中文名称转换为URL友好的格式
    const baseSlug = slugify(name, {
      lower: true,      // 转换为小写
      strict: true,     // 严格模式，移除特殊字符
      locale: 'zh'      // 中文支持
    });

    // 添加时间戳确保唯一性
    const timestamp = Math.floor(Date.now() / 1000);
    return `${baseSlug || 'product'}-${timestamp}`;
  }

  /**
   * 创建第三方商品
   * @param {Object} productData - 商品数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建结果
   */
  async createThirdPartyProduct(productData, userId) {
    return await this.prisma.$transaction(async (tx) => {
      const now = BigInt(Date.now());
      
      // 1. 验证渠道平台店铺关系
      const validation = await this.validateChannelPlatformStore(
        productData.channelId, 
        productData.platformId, 
        productData.storeId
      );
      
      if (!validation.valid) {
        throw new Error(validation.message);
      }

      // 2. 检查SKU编码唯一性
      const skuCodes = productData.skus.map(sku => sku.skuCode);
      const existingSkuCodes = await this.checkSkuCodesExist(skuCodes);
      
      if (existingSkuCodes.length > 0) {
        throw new Error(`SKU编码已存在: ${existingSkuCodes.join(', ')}`);
      }

      // 3. 生成SPU数据
      const spuId = generateSnowflakeId();
      const spuCode = await this.generateSpuCode();
      const slug = this.generateSlug(productData.name);
      
      // 4. 创建商品SPU
      const spu = await tx.goodsSpu.create({
        data: {
          id: BigInt(spuId),
          spu_code: spuCode,
          name: productData.name,
          subtitle: productData.subtitle || null,
          slug: slug,
          description: productData.description || null,
          goods_brand_id: null, // 第三方商品不关联品牌
          goods_freight_template_id: null, // 第三方商品不关联运费模板
          source_type: 1, // 第三方订单 // 标记为第三方商品
          created_channel_id: BigInt(productData.channelId), // 记录创建来源
          created_platform_id: BigInt(productData.platformId), // 记录创建来源
          created_store_id: BigInt(productData.storeId), // 记录创建来源
          status: 1, // 默认上架
          is_virtual: 0, // 默认实体商品
          sort_order: 0,
          total_sales: 0,
          total_stock: 0, // 稍后计算
          created_at: now,
          updated_at: now,
          created_by: userId,
          updated_by: userId
        }
      });

      // 5. 创建SKU和渠道关联
      const skuPromises = productData.skus.map(async (skuData) => {
        const sku = await tx.goodsSku.create({
          data: {
            goods_spu_id: BigInt(spuId),
            sku_code: skuData.skuCode,
            sales_price: skuData.salesPrice,
            stock: skuData.stock,
            market_price: null,
            cost_price: null,
            weight: null,
            volume: null,
            unit: null,
            is_enabled: 1,
            created_at: now,
            updated_at: now
          }
        });

        // 6. 创建SKU平台关联（支持多平台销售）
        await tx.goodsSkuPlatformRelation.create({
          data: {
            goods_sku_id: sku.id,
            channel_id: BigInt(productData.channelId),
            platform_id: BigInt(productData.platformId),
            store_id: BigInt(productData.storeId),
            is_enabled: 1,
            created_at: now,
            updated_at: now
          }
        });

        return sku;
      });

      const skus = await Promise.all(skuPromises);

      // 7. 计算并更新总库存
      const totalStock = skus.reduce((sum, sku) => sum + sku.stock, 0);
      await tx.goodsSpu.update({
        where: { id: BigInt(spuId) },
        data: { total_stock: totalStock }
      });

      return {
        spu: this.processResult(spu),
        skus: this.processResult(skus),
        totalStock,
        skuCount: skus.length
      };
    });
  }

  /**
   * 获取第三方商品列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 商品列表和分页信息
   */
  async getThirdPartyProductList(options) {
    const {
      keyword,
      channelId,
      platformId, 
      storeId,
      status,
      startTime,
      endTime,
      skip,
      take
    } = options;

    // 构建查询条件
    const where = {
      deleted_at: null,
      source_type: 1 // 只查询第三方订单
    };

    if (keyword) {
      where.OR = [
        { name: { contains: keyword } },
        { subtitle: { contains: keyword } },
        { spu_code: { contains: keyword } }
      ];
    }

    if (status !== undefined) {
      where.status = status;
    }

    if (startTime && endTime) {
      where.created_at = {
        gte: BigInt(startTime),
        lte: BigInt(endTime)
      };
    }

    // 直接通过SPU表的字段筛选渠道/平台/店铺
    if (channelId) {
      where.source_channel_id = BigInt(channelId);
    }

    if (platformId) {
      where.source_platform_id = BigInt(platformId);
    }

    if (storeId) {
      where.source_store_id = BigInt(storeId);
    }

    // 查询总数
    const total = await this.prisma.goodsSpu.count({ where });

    // 查询数据
    const items = await this.prisma.goodsSpu.findMany({
      where,
      include: {
        goods_skus: {
          where: { deleted_at: null },
          include: {
            goods_sku_platform_relations: {
              where: { deleted_at: null },
              include: {
                channel: { select: { id: true, name: true } },
                platform: { select: { id: true, name: true } },
                store: { select: { id: true, name: true } }
              }
            }
          }
        }
      },
      orderBy: { created_at: 'desc' },
      skip,
      take
    });

    return {
      items: this.processResult(items),
      total
    };
  }

  /**
   * 根据ID获取第三方商品详情
   * @param {string} id - 商品ID
   * @returns {Promise<Object|null>} - 商品详情
   */
  async getThirdPartyProductById(id) {
    try {
      const spu = await this.prisma.goodsSpu.findUnique({
        where: {
          id: BigInt(id),
          deleted_at: null,
          source_type: 1 // 确保只查询第三方订单
        },
        include: {
          goods_skus: {
            where: { deleted_at: null },
            include: {
              goods_sku_channels: {
                where: { deleted_at: null },
                include: {
                  channel: { select: { id: true, name: true } },
                  platform: { select: { id: true, name: true } },
                  store: { select: { id: true, name: true } }
                }
              }
            }
          }
        }
      });

      return this.processResult(spu);
    } catch (error) {
      console.error(`获取第三方商品详情失败 ID:${id}`, error);
      return null;
    }
  }
}

module.exports = ThirdPartyGoodsSpuModel;
