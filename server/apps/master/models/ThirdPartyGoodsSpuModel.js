/**
 * 第三方商品SPU数据模型
 * 专门处理第三方平台商品创建和多平台关联的数据库操作
 */
const { prisma } = require('../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const slugify = require('slugify');
const GoodsSourceTypeEnum = require('../constants/GoodsSourceTypeEnum');
const GoodsSyncStatusEnum = require('../constants/GoodsSyncStatusEnum');

class ThirdPartyGoodsSpuModel {
  constructor() {
    this.tableName = 'goods_spus';
    this.schema = 'base';
  }

  /**
   * 验证渠道、平台、店铺的关联关系
   * @param {string} channelId - 渠道ID
   * @param {string} platformId - 平台ID  
   * @param {string} storeId - 店铺ID
   * @returns {Promise<Object>} - 验证结果
   */
  async validateChannelPlatformStore(channelId, platformId, storeId) {
    try {
      // 验证渠道是否存在
      const channel = await prisma.channel.findUnique({
        where: {
          id: BigInt(channelId)
        }
      });

      if (!channel || channel.deleted_at !== null) {
        return { valid: false, message: `渠道ID ${channelId} 不存在` };
      }

      // 验证平台是否存在且属于指定渠道
      const platform = await prisma.$queryRaw`
        SELECT * FROM "base"."platform"
        WHERE "id" = ${BigInt(platformId)} AND "deleted_at" IS NULL
      `;

      if (!platform || platform.length === 0) {
        return { valid: false, message: `平台ID ${platformId} 不存在` };
      }

      const platformData = platform[0];

      if (platformData.channel_id.toString() !== channelId) {
        return { valid: false, message: `平台 ${platformId} 不属于渠道 ${channelId}` };
      }

      // 验证店铺是否存在且属于指定平台
      const store = await prisma.$queryRaw`
        SELECT * FROM "base"."store"
        WHERE "id" = ${BigInt(storeId)} AND "deleted_at" IS NULL
      `;

      if (!store || store.length === 0) {
        return { valid: false, message: `店铺ID ${storeId} 不存在` };
      }

      const storeData = store[0];

      if (storeData.platform_id.toString() !== platformId) {
        return { valid: false, message: `店铺 ${storeId} 不属于平台 ${platformId}` };
      }

      return {
        valid: true,
        data: { channel, platform: platformData, store: storeData }
      };
    } catch (error) {
      console.error('验证渠道平台店铺关系失败:', error);
      console.error('错误详情:', error.message);
      console.error('错误堆栈:', error.stack);
      return { valid: false, message: `验证渠道平台店铺关系时发生错误: ${error.message}` };
    }
  }

  /**
   * 检查SKU编码是否已存在
   * @param {Array} skuCodes - SKU编码数组
   * @returns {Promise<Array>} - 已存在的SKU编码
   */
  async checkSkuCodesExist(skuCodes) {
    try {
      const existingSkus = await prisma.goodsSku.findMany({
        where: {
          sku_code: { in: skuCodes },
          deleted_at: null
        },
        select: { sku_code: true }
      });
      
      return existingSkus.map(sku => sku.sku_code);
    } catch (error) {
      console.error('检查SKU编码存在性失败:', error);
      throw error;
    }
  }

  /**
   * 生成唯一的SPU编码
   * @returns {Promise<string>} - SPU编码
   */
  async generateSpuCode() {
    const timestamp = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    let counter = 1;
    let spuCode;

    do {
      spuCode = `TP_SPU_${timestamp}_${counter.toString().padStart(3, '0')}`;
      const existing = await prisma.goodsSpu.findUnique({
        where: { spu_code: spuCode }
      });
      if (!existing) break;
      counter++;
    } while (counter <= 999);

    if (counter > 999) {
      throw new Error('无法生成唯一的SPU编码');
    }

    return spuCode;
  }

  /**
   * 生成商品slug
   * @param {string} name - 商品名称
   * @returns {string} - 生成的slug
   */
  generateSlug(name) {
    // 使用 slugify 将中文名称转换为URL友好的格式
    const baseSlug = slugify(name, {
      lower: true,      // 转换为小写
      strict: true,     // 严格模式，移除特殊字符
      locale: 'zh'      // 中文支持
    });

    // 添加时间戳确保唯一性
    const timestamp = Math.floor(Date.now() / 1000);
    return `${baseSlug || 'product'}-${timestamp}`;
  }

  /**
   * 创建第三方商品
   * @param {Object} productData - 商品数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建结果
   */
  async createThirdPartyProduct(productData, userId) {
    return await prisma.$transaction(async (tx) => {
      const now = BigInt(Date.now());
      
      // 1. 验证渠道平台店铺关系
      const validation = await this.validateChannelPlatformStore(
        productData.channelId, 
        productData.platformId, 
        productData.storeId
      );
      
      if (!validation.valid) {
        throw new Error(validation.message);
      }

      // 2. 检查SKU编码唯一性
      const skuCodes = productData.skus.map(sku => sku.skuCode);
      const existingSkuCodes = await this.checkSkuCodesExist(skuCodes);
      
      if (existingSkuCodes.length > 0) {
        throw new Error(`SKU编码已存在: ${existingSkuCodes.join(', ')}`);
      }

      // 3. 生成SPU数据
      const spuId = generateSnowflakeId();
      const spuCode = await this.generateSpuCode();
      const slug = this.generateSlug(productData.name);
      
      // 4. 创建商品SPU
      const spu = await tx.goodsSpu.create({
        data: {
          id: BigInt(spuId),
          spu_code: spuCode,
          name: productData.name,
          subtitle: productData.subtitle || null,
          slug: slug,
          description: productData.description || null,
          goods_freight_template_id: null, // 第三方商品不关联运费模板
          status: 1, // 默认上架
          is_virtual: 0, // 默认实体商品
          sort_order: 0,
          total_sales: 0,
          total_stock: 0, // 稍后计算
          source_type: GoodsSourceTypeEnum.THIRD_PARTY, // 设置为第三方商品
          created_channel_id: BigInt(productData.channelId),
          created_platform_id: BigInt(productData.platformId),
          created_store_id: BigInt(productData.storeId),
          created_at: now,
          updated_at: now,
          created_by: userId,
          updated_by: userId
        }
      });

      // 4.1. 创建SPU图片（如果有）
      if (productData.spuImages && productData.spuImages.length > 0) {
        const imagePromises = productData.spuImages.map((imageUrl, index) =>
          tx.goodsImage.create({
            data: {
              goods_spu_id: BigInt(spuId),
              goods_sku_id: null, // SPU图片
              image_url: imageUrl,
              sort_order: index,
              is_default: index === 0, // 第一张设为默认
              created_at: now,
              updated_at: now
            }
          })
        );
        await Promise.all(imagePromises);
      }

      // 5. 创建SKU和渠道关联
      const skuPromises = productData.skus.map(async (skuData) => {
        const skuId = generateSnowflakeId();
        const sku = await tx.goodsSku.create({
          data: {
            id: skuId,
            goods_spu_id: BigInt(spuId),
            sku_code: skuData.skuCode,
            sales_price: skuData.salesPrice,
            stock: skuData.stock,
            market_price: skuData.marketPrice || null,
            cost_price: null,
            weight: skuData.weight || null,
            volume: skuData.volume || null,
            unit: skuData.unit || null,
            is_enabled: 1,
            created_at: now,
            updated_at: now
          }
        });

        // 5.1. 创建SKU图片（如果有）
        if (skuData.image) {
          await tx.goodsImage.create({
            data: {
              goods_spu_id: BigInt(spuId),
              goods_sku_id: sku.id,
              image_url: skuData.image,
              sort_order: 0,
              is_default: true,
              created_at: now,
              updated_at: now
            }
          });
        }

        // 6. 创建SKU渠道关联
        await tx.goodsSkuChannel.create({
          data: {
            goods_sku_id: sku.id,
            channel_id: BigInt(productData.channelId),
            third_party_sku_code: sku.skuCode,
            is_enabled: 1,
            created_at: now,
            updated_at: now,
            created_by: userId,
            updated_by: userId
          }
        });

        // 7. 创建SKU平台关系记录
        await tx.goodsSkuPlatformRelation.create({
          data: {
            id: generateSnowflakeId(),
            goods_sku_id: sku.id,
            channel_id: BigInt(productData.channelId),
            platform_id: BigInt(productData.platformId),
            store_id: BigInt(productData.storeId),
            third_party_sku_code: sku.sku_code,
            is_enabled: 1,
            created_at: now,
            updated_at: now,
            push_status: 0,
            sync_status: 0
          }
        });

        return sku;
      });

      const skus = await Promise.all(skuPromises);

      // 7. 计算并更新总库存
      const totalStock = skus.reduce((sum, sku) => sum + sku.stock, 0);
      await tx.goodsSpu.update({
        where: { id: BigInt(spuId) },
        data: { total_stock: totalStock }
      });

      return {
        spu: spu,
        skus: skus,
        totalStock,
        skuCount: skus.length
      };
    });
  }

  /**
   * 获取第三方商品列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} - 商品列表和分页信息
   */
  async getThirdPartyProductList(options) {
    const {
      keyword,
      channelId,
      platformId, 
      storeId,
      status,
      startTime,
      endTime,
      skip,
      take
    } = options;

    // 构建查询条件
    const where = {
      deleted_at: null,
      source_type: GoodsSourceTypeEnum.THIRD_PARTY // 只查询第三方订单
    };

    if (keyword) {
      where.OR = [
        { name: { contains: keyword } },
        { subtitle: { contains: keyword } },
        { spu_code: { contains: keyword } }
      ];
    }

    if (status !== undefined) {
      where.status = status;
    }

    if (startTime && endTime) {
      where.created_at = {
        gte: BigInt(startTime),
        lte: BigInt(endTime)
      };
    }

    // 直接通过SPU表的字段筛选渠道/平台/店铺
    if (channelId) {
      where.source_channel_id = BigInt(channelId);
    }

    if (platformId) {
      where.source_platform_id = BigInt(platformId);
    }

    if (storeId) {
      where.source_store_id = BigInt(storeId);
    }

    // 查询总数
    const total = await prisma.goodsSpu.count({ where });

    // 查询数据
    const items = await prisma.goodsSpu.findMany({
      where,
      include: {
        goods_skus: {
          where: { deleted_at: null },
          include: {
            goods_sku_platform_relations: {
              where: { deleted_at: null },
              include: {
                channel: { select: { id: true, name: true } },
                platform: { select: { id: true, name: true } },
                store: { select: { id: true, name: true } }
              }
            }
          }
        }
      },
      orderBy: { created_at: 'desc' },
      skip,
      take
    });

    return {
      items: this.processResult(items),
      total
    };
  }

  /**
   * 根据ID获取第三方商品详情
   * @param {string} id - 商品ID
   * @returns {Promise<Object|null>} - 商品详情
   */
  async getThirdPartyProductById(id) {
    try {
      const spu = await prisma.goodsSpu.findFirst({
        where: {
          id: BigInt(id),
          deleted_at: null,
          source_type: GoodsSourceTypeEnum.THIRD_PARTY // 确保只查询第三方订单
        },
        include: {
          goods_skus: {
            where: { deleted_at: null },
            include: {
              goods_sku_channels: {
                where: { deleted_at: null },
                include: {
                  channel: { select: { id: true, name: true } },
                  platform: { select: { id: true, name: true } },
                  store: { select: { id: true, name: true } }
                }
              }
            }
          }
        }
      });

      return this.processResult(spu);
    } catch (error) {
      console.error(`获取第三方商品详情失败 ID:${id}`, error);
      return null;
    }
  }

  /**
   * 更新第三方商品
   * @param {string} spuId - 商品SPU ID
   * @param {Object} updateData - 更新数据
   * @param {BigInt} userId - 更新者ID
   * @returns {Promise<Object>} - 更新结果
   */
  async updateThirdPartyProduct(spuId, updateData, userId) {
    const transaction = await prisma.$transaction(async (tx) => {
      try {
        console.log('开始更新第三方商品事务:', { spuId, updateFields: Object.keys(updateData) });

        // 1. 获取当前商品信息
        const existingSpu = await tx.goodsSpu.findFirst({
          where: {
            id: BigInt(spuId),
            deleted_at: null
          },
          include: {
            goods_skus: {
              where: { deleted_at: null },
              orderBy: { created_at: 'asc' }
            }
          }
        });

        if (!existingSpu) {
          throw new Error('商品不存在');
        }

        // 2. 准备SPU更新数据
        const now = BigInt(Date.now());
        const spuUpdateData = {
          updated_at: now,
          updated_by: userId
        };

        // 添加基本字段更新
        if (updateData.name !== undefined) {
          spuUpdateData.name = updateData.name;
          spuUpdateData.slug = this.generateSlug(updateData.name); // 生成唯一slug
        }

        if (updateData.subtitle !== undefined) {
          spuUpdateData.subtitle = updateData.subtitle;
        }

        if (updateData.description !== undefined) {
          spuUpdateData.description = updateData.description;
        }

        // 3. 处理SPU图片更新（如果有）
        if (updateData.spuImages !== undefined) {
          // 删除现有SPU图片
          await tx.goodsImage.deleteMany({
            where: {
              goods_spu_id: BigInt(spuId),
              goods_sku_id: null // SPU图片
            }
          });

          // 创建新的SPU图片
          if (updateData.spuImages.length > 0) {
            const imagePromises = updateData.spuImages.map((imageUrl, index) =>
              tx.goodsImage.create({
                data: {
                  goods_spu_id: BigInt(spuId),
                  goods_sku_id: null,
                  image_url: imageUrl,
                  sort_order: index,
                  is_default: index === 0,
                  created_at: now,
                  updated_at: now
                }
              })
            );
            await Promise.all(imagePromises);
          }
        }

        // 4. 更新SPU基本信息
        console.log('准备更新SPU，数据:', spuUpdateData);
        console.log('SPU ID:', spuId, 'BigInt:', BigInt(spuId));
        const updatedSpu = await tx.goodsSpu.update({
          where: { id: BigInt(spuId) },
          data: spuUpdateData
        });

        console.log('SPU基本信息更新成功:', { spuId, updatedFields: Object.keys(spuUpdateData) });

        // 4. 处理SKU更新（如果有）
        let updatedSkus = existingSpu.goods_skus;
        if (updateData.skus && updateData.skus.length > 0) {
          updatedSkus = await this.updateSkusForSpu(tx, spuId, updateData.skus, userId);
        }

        // 5. 重新计算商品统计信息
        const stats = this.calculateProductStats(updatedSkus);

        // 6. 更新SPU统计信息
        const finalUpdatedSpu = await tx.goodsSpu.update({
          where: { id: BigInt(spuId) },
          data: {
            total_stock: stats.totalStock,
            updated_at: now
          }
        });

        console.log('第三方商品更新事务完成:', {
          spuId: finalUpdatedSpu.id.toString(),
          spuCode: finalUpdatedSpu.spu_code,
          skuCount: updatedSkus.length,
          totalStock: stats.totalStock
        });

        return {
          spu: this.formatSpuData(finalUpdatedSpu),
          skus: updatedSkus.map(sku => this.formatSkuData(sku)),
          skuCount: updatedSkus.length,
          totalStock: stats.totalStock,
          priceRange: stats.priceRange
        };
      } catch (error) {
        console.error('更新第三方商品事务失败:', error);
        throw error;
      }
    });

    return transaction;
  }

  /**
   * 更新商品的SKU信息
   * @param {Object} tx - 事务对象
   * @param {string} spuId - 商品SPU ID
   * @param {Array} skuUpdates - SKU更新数据
   * @param {BigInt} userId - 更新者ID
   * @returns {Promise<Array>} - 更新后的SKU列表
   */
  async updateSkusForSpu(tx, spuId, skuUpdates, userId) {
    const now = BigInt(Date.now());
    const updatedSkus = [];

    for (const skuUpdate of skuUpdates) {
      if (skuUpdate.id) {
        // 更新现有SKU
        const existingSku = await tx.goodsSku.findFirst({
          where: {
            id: BigInt(skuUpdate.id),
            goods_spu_id: BigInt(spuId),
            deleted_at: null
          }
        });

        if (!existingSku) {
          throw new Error(`SKU ID ${skuUpdate.id} 不存在或不属于当前商品`);
        }

        // 检查SKU编码冲突（排除当前SKU）
        if (skuUpdate.skuCode && skuUpdate.skuCode !== existingSku.sku_code) {
          const conflictSku = await tx.goodsSku.findFirst({
            where: {
              sku_code: skuUpdate.skuCode,
              id: { not: BigInt(skuUpdate.id) },
              deleted_at: null
            }
          });

          if (conflictSku) {
            throw new Error(`SKU编码 ${skuUpdate.skuCode} 已存在`);
          }
        }

        // 更新SKU
        const updatedSku = await tx.goodsSku.update({
          where: { id: BigInt(skuUpdate.id) },
          data: {
            sku_code: skuUpdate.skuCode || existingSku.sku_code,
            sales_price: skuUpdate.salesPrice !== undefined ? skuUpdate.salesPrice : existingSku.sales_price,
            stock: skuUpdate.stock !== undefined ? skuUpdate.stock : existingSku.stock,
            market_price: skuUpdate.marketPrice !== undefined ? skuUpdate.marketPrice : existingSku.market_price,
            weight: skuUpdate.weight !== undefined ? skuUpdate.weight : existingSku.weight,
            volume: skuUpdate.volume !== undefined ? skuUpdate.volume : existingSku.volume,
            unit: skuUpdate.unit !== undefined ? skuUpdate.unit : existingSku.unit,
            updated_at: now
          }
        });

        // 处理SKU图片更新
        if (skuUpdate.image !== undefined) {
          // 删除现有SKU图片
          await tx.goodsImage.deleteMany({
            where: {
              goods_spu_id: BigInt(spuId),
              goods_sku_id: skuUpdate.id
            }
          });

          // 创建新的SKU图片（如果有）
          if (skuUpdate.image) {
            await tx.goodsImage.create({
              data: {
                goods_spu_id: BigInt(spuId),
                goods_sku_id: skuUpdate.id,
                image_url: skuUpdate.image,
                sort_order: 0,
                is_default: true,
                created_at: now,
                updated_at: now
              }
            });
          }
        }

        updatedSkus.push(updatedSku);
      } else {
        // 创建新SKU（如果需要支持）
        throw new Error('暂不支持在更新时创建新SKU，请使用创建接口');
      }
    }

    // 获取所有当前商品的SKU（包括未更新的）
    const allSkus = await tx.goodsSku.findMany({
      where: {
        goods_spu_id: BigInt(spuId),
        deleted_at: null
      },
      orderBy: { created_at: 'asc' }
    });

    return allSkus;
  }

  /**
   * 根据SKU编码查找SKU（排除指定商品）
   * @param {Array} skuCodes - SKU编码列表
   * @param {string} excludeSpuId - 排除的商品SPU ID
   * @returns {Promise<Array>} - 冲突的SKU列表
   */
  async findSkusByCodes(skuCodes, excludeSpuId = null) {
    try {
      const whereCondition = {
        sku_code: { in: skuCodes },
        deleted_at: null
      };

      // 如果指定了排除的商品ID，则排除该商品的SKU
      if (excludeSpuId) {
        whereCondition.goods_spu_id = { not: BigInt(excludeSpuId) };
      }

      const skus = await prisma.goodsSku.findMany({
        where: whereCondition,
        select: {
          id: true,
          sku_code: true,
          goods_spu_id: true
        }
      });

      return skus;
    } catch (error) {
      console.error('查找SKU编码冲突失败:', error);
      return [];
    }
  }

  /**
   * 根据ID查找第三方商品（用于验证）
   * @param {string} spuId - 商品SPU ID
   * @returns {Promise<Object|null>} - 商品信息
   */
  async findThirdPartyProductById(spuId) {
    try {
      console.log('查找第三方商品，spuId:', spuId);
      console.log('Prisma 客户端状态:', !!prisma);
      console.log('goodsSpu 模型状态:', !!prisma.goodsSpu);

      const whereCondition = {
        id: BigInt(spuId),
        deleted_at: null,
        source_type: GoodsSourceTypeEnum.THIRD_PARTY
      };
      console.log('查询条件:', whereCondition);
      console.log('THIRD_PARTY 枚举值:', GoodsSourceTypeEnum.THIRD_PARTY);

      const product = await prisma.goodsSpu.findFirst({
        where: whereCondition
      });

      console.log('查找结果:', !!product);
      if (product) {
        console.log('找到的商品信息:', {
          id: product.id.toString(),
          name: product.name,
          source_type: product.source_type,
          deleted_at: product.deleted_at
        });
      }
      return product;
    } catch (error) {
      console.error('查找第三方商品失败:', error);
      return null;
    }
  }

  /**
   * 计算商品统计信息
   * @param {Array} skus - SKU列表
   * @returns {Object} - 统计信息
   */
  calculateProductStats(skus) {
    let totalStock = 0;
    let minPrice = null;
    let maxPrice = null;

    for (const sku of skus) {
      totalStock += sku.stock || 0;

      if (sku.sales_price) {
        if (minPrice === null || sku.sales_price < minPrice) {
          minPrice = sku.sales_price;
        }
        if (maxPrice === null || sku.sales_price > maxPrice) {
          maxPrice = sku.sales_price;
        }
      }
    }

    return {
      totalStock,
      priceRange: minPrice !== null ? { min: minPrice, max: maxPrice } : null
    };
  }

  /**
   * 格式化SPU数据
   * @param {Object} spu - SPU数据
   * @returns {Object} - 格式化后的SPU数据
   */
  formatSpuData(spu) {
    return {
      id: spu.id.toString(),
      name: spu.name,
      subtitle: spu.subtitle,
      description: spu.description,
      status: spu.status,
      totalStock: spu.total_stock,
      createdAt: spu.created_at,
      updatedAt: spu.updated_at
    };
  }

  /**
   * 格式化SKU数据
   * @param {Object} sku - SKU数据
   * @returns {Object} - 格式化后的SKU数据
   */
  formatSkuData(sku) {
    return {
      id: sku.id.toString(),
      name: sku.name,
      skuCode: sku.sku_code,
      salesPrice: sku.sales_price,
      marketPrice: sku.market_price,
      stock: sku.stock,
      weight: sku.weight,
      volume: sku.volume,
      unit: sku.unit
    };
  }
}

module.exports = ThirdPartyGoodsSpuModel;
