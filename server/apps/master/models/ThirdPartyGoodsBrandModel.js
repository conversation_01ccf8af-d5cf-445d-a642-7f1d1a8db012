const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const GoodsSourceTypeEnum = require('../constants/GoodsSourceTypeEnum');

/**
 * 第三方商品品牌模型
 * 处理第三方平台的品牌数据操作
 */
class ThirdPartyGoodsBrandModel {
  constructor(prisma) {
    this.prisma = prisma;
  }

  /**
   * 验证渠道、平台、店铺关联关系
   * @param {string} channelId 渠道ID
   * @param {string} platformId 平台ID
   * @param {string} storeId 店铺ID
   * @returns {Promise<boolean>} 验证结果
   */
  async validateChannelPlatformStore(channelId, platformId, storeId) {
    // 验证渠道是否存在（channel表没有status字段，只检查deleted_at）
    const channel = await this.prisma.channel.findFirst({
      where: {
        id: BigInt(channelId),
        deleted_at: null
      }
    });

    if (!channel) {
      throw new Error(`渠道ID ${channelId} 不存在`);
    }

    // 验证平台是否存在且属于该渠道
    const platform = await this.prisma.platform.findFirst({
      where: {
        id: BigInt(platformId),
        channel_id: BigInt(channelId),
        deleted_at: null,
        status: 1
      }
    });

    if (!platform) {
      throw new Error(`平台ID ${platformId} 不存在或不属于渠道 ${channelId}`);
    }

    // 验证店铺是否存在且属于该平台
    const store = await this.prisma.store.findFirst({
      where: {
        id: BigInt(storeId),
        platform_id: BigInt(platformId),
        deleted_at: null,
        status: 1
      }
    });

    if (!store) {
      throw new Error(`店铺ID ${storeId} 不存在或不属于平台 ${platformId}`);
    }

    return true;
  }

  /**
   * 创建第三方商品品牌
   * @param {Object} brandData 品牌数据
   * @returns {Promise<Object>} 创建的品牌信息
   */
  async createThirdPartyBrand(brandData) {
    const { channelId, platformId, storeId, name, logoUrl, description, platformBrandId, platformBrandName } = brandData;

    // 验证关联关系
    await this.validateChannelPlatformStore(channelId, platformId, storeId);

    // 使用事务确保数据一致性
    return await this.prisma.$transaction(async (tx) => {
      const currentTime = BigInt(Date.now());
      const brandId = generateSnowflakeId();
      const relationId = generateSnowflakeId();

      // 创建品牌记录
      const brand = await tx.goodsBrand.create({
        data: {
          id: brandId,
          name: name,
          logo_url: logoUrl || null,
          description: description || null,
          source_type: GoodsSourceTypeEnum.THIRD_PARTY,
          created_channel_id: BigInt(channelId),
          created_platform_id: BigInt(platformId),
          created_store_id: BigInt(storeId),
          created_at: currentTime,
          updated_at: currentTime
        }
      });

      // 创建平台关联记录
      await tx.goodsBrandPlatformRelation.create({
        data: {
          id: relationId,
          goods_brand_id: brandId,
          channel_id: BigInt(channelId),
          platform_id: BigInt(platformId),
          store_id: BigInt(storeId),
          platform_brand_id: platformBrandId || null,
          platform_brand_name: platformBrandName || null,
          push_status: 0,
          sync_status: 0,
          created_at: currentTime,
          updated_at: currentTime
        }
      });

      return brand;
    });
  }

  /**
   * 更新第三方商品品牌
   * @param {string} brandId 品牌ID
   * @param {Object} brandData 更新的品牌数据
   * @returns {Promise<Object>} 更新后的品牌信息
   */
  async updateThirdPartyBrand(brandId, brandData) {
    // 验证品牌是否存在且为第三方品牌
    const existingBrand = await this.prisma.goodsBrand.findFirst({
      where: {
        id: BigInt(brandId),
        source_type: GoodsSourceTypeEnum.THIRD_PARTY,
        deleted_at: null
      }
    });

    if (!existingBrand) {
      throw new Error('品牌不存在或不是第三方品牌');
    }

    // 使用事务更新品牌和关联信息
    return await this.prisma.$transaction(async (tx) => {
      const currentTime = BigInt(Date.now());
      
      // 准备更新数据
      const updateData = {
        updated_at: currentTime
      };

      if (brandData.name !== undefined) {
        updateData.name = brandData.name;
      }
      if (brandData.logoUrl !== undefined) {
        updateData.logo_url = brandData.logoUrl;
      }
      if (brandData.description !== undefined) {
        updateData.description = brandData.description;
      }

      // 更新品牌信息
      const updatedBrand = await tx.goodsBrand.update({
        where: { id: BigInt(brandId) },
        data: updateData
      });

      // 如果有平台相关字段需要更新，更新关联表
      if (brandData.platformBrandId !== undefined || brandData.platformBrandName !== undefined) {
        const relationUpdateData = {
          updated_at: currentTime
        };

        if (brandData.platformBrandId !== undefined) {
          relationUpdateData.platform_brand_id = brandData.platformBrandId;
        }
        if (brandData.platformBrandName !== undefined) {
          relationUpdateData.platform_brand_name = brandData.platformBrandName;
        }

        await tx.goodsBrandPlatformRelation.updateMany({
          where: {
            goods_brand_id: BigInt(brandId),
            deleted_at: null
          },
          data: relationUpdateData
        });
      }

      return updatedBrand;
    });
  }

  /**
   * 格式化品牌数据为API响应格式
   * @param {Object} brand 品牌数据
   * @returns {Object} 格式化后的品牌数据
   */
  formatBrandResponse(brand) {
    return {
      id: brand.id.toString(),
      name: brand.name,
      logoUrl: brand.logo_url,
      description: brand.description,
      sourceType: brand.source_type,
      createdChannelId: brand.created_channel_id?.toString(),
      createdPlatformId: brand.created_platform_id?.toString(),
      createdStoreId: brand.created_store_id?.toString(),
      createdAt: brand.created_at.toString(),
      updatedAt: brand.updated_at.toString()
    };
  }
}

module.exports = ThirdPartyGoodsBrandModel;
