/**
 * 第三方商品分类数据模型
 * 为第三方平台提供简化的分类管理接口
 */
const { prisma } = require('../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const GoodsSourceTypeEnum = require('../constants/GoodsSourceTypeEnum');

class ThirdPartyGoodsCategoryModel {
  constructor() {
    this.prisma = prisma;
    console.log('GoodsSourceTypeEnum loaded:', GoodsSourceTypeEnum);
    console.log('GoodsSourceTypeEnum.THIRD_PARTY:', GoodsSourceTypeEnum?.THIRD_PARTY);
  }

  /**
   * 验证渠道、平台、店铺关系是否有效
   * @param {string} channelId - 渠道ID
   * @param {string} platformId - 平台ID  
   * @param {string} storeId - 店铺ID
   * @returns {Promise<boolean>} 验证结果
   */
  async validateChannelPlatformStore(channelId, platformId, storeId) {
    try {
      // 验证渠道是否存在
      const channel = await this.prisma.channel.findFirst({
        where: {
          id: BigInt(channelId),
          deleted_at: null
        }
      });

      if (!channel) {
        throw new Error(`渠道ID ${channelId} 不存在`);
      }

      // 验证平台是否存在
      const platform = await this.prisma.platform.findFirst({
        where: {
          id: BigInt(platformId),
          deleted_at: null
        }
      });

      if (!platform) {
        throw new Error(`平台ID ${platformId} 不存在`);
      }

      // 验证店铺是否存在
      const store = await this.prisma.store.findFirst({
        where: {
          id: BigInt(storeId),
          deleted_at: null
        }
      });

      if (!store) {
        throw new Error(`店铺ID ${storeId} 不存在`);
      }

      return true;
    } catch (error) {
      console.error('验证渠道平台店铺关系失败:', error);
      throw error;
    }
  }

  /**
   * 验证父分类是否存在且为第三方分类
   * @param {string} parentId - 父分类ID
   * @returns {Promise<Object|null>} 父分类信息
   */
  async validateParentCategory(parentId) {
    if (!parentId) return null;

    try {
      const parentCategory = await this.prisma.goodsCategory.findFirst({
        where: {
          id: BigInt(parentId),
          source_type: GoodsSourceTypeEnum.THIRD_PARTY,
          deleted_at: null
        }
      });

      if (!parentCategory) {
        throw new Error(`父分类ID ${parentId} 不存在或不是第三方分类`);
      }

      return parentCategory;
    } catch (error) {
      console.error('验证父分类失败:', error);
      throw error;
    }
  }

  /**
   * 创建第三方商品分类
   * @param {Object} categoryData - 分类数据
   * @param {BigInt} userId - 用户ID
   * @returns {Promise<Object>} 创建结果
   */
  async createThirdPartyCategory(categoryData, userId) {
    try {
      // 1. 验证渠道、平台、店铺关系
      await this.validateChannelPlatformStore(
        categoryData.channelId,
        categoryData.platformId,
        categoryData.storeId
      );

      // 2. 验证父分类（如果提供）
      const parentCategory = await this.validateParentCategory(categoryData.parentId);

      // 3. 计算分类层级
      let level = 1;
      if (parentCategory) {
        level = (parentCategory.level || 1) + 1;
      }

      // 4. 使用事务创建分类
      return await this.prisma.$transaction(async (tx) => {
        const now = BigInt(Date.now());
        const categoryId = generateSnowflakeId();

        // 创建分类
        const newCategory = await tx.goodsCategory.create({
          data: {
            id: categoryId,
            goods_parent_category_id: parentCategory ? BigInt(categoryData.parentId) : null,
            name: categoryData.name,
            description: categoryData.description || null,
            image_url: categoryData.imageUrl || null,
            sort_order: categoryData.sortOrder || 0,
            is_enabled: 1, // 默认启用
            level: level,
            meta_title: categoryData.metaTitle || null,
            meta_keywords: categoryData.metaKeywords || null,
            meta_description: categoryData.metaDescription || null,
            source_type: GoodsSourceTypeEnum.THIRD_PARTY,
            created_channel_id: BigInt(categoryData.channelId),
            created_platform_id: BigInt(categoryData.platformId),
            created_store_id: BigInt(categoryData.storeId),
            created_at: now,
            updated_at: now,
            created_by: userId,
            updated_by: userId
          }
        });

        return this.formatCategoryData(newCategory);
      });
    } catch (error) {
      console.error('创建第三方商品分类失败:', error);
      throw error;
    }
  }

  /**
   * 更新第三方商品分类
   * @param {string} categoryId - 分类ID
   * @param {Object} categoryData - 更新数据
   * @param {BigInt} userId - 用户ID
   * @returns {Promise<Object>} 更新结果
   */
  async updateThirdPartyCategory(categoryId, categoryData, userId) {
    try {
      // 1. 验证分类是否存在且为第三方分类
      const existingCategory = await this.prisma.goodsCategory.findFirst({
        where: {
          id: BigInt(categoryId),
          source_type: GoodsSourceTypeEnum.THIRD_PARTY,
          deleted_at: null
        }
      });

      if (!existingCategory) {
        throw new Error('分类不存在或不是第三方分类');
      }

      // 2. 如果更新了渠道、平台、店铺，需要验证关系
      if (categoryData.channelId || categoryData.platformId || categoryData.storeId) {
        await this.validateChannelPlatformStore(
          categoryData.channelId || existingCategory.created_channel_id.toString(),
          categoryData.platformId || existingCategory.created_platform_id.toString(),
          categoryData.storeId || existingCategory.created_store_id.toString()
        );
      }

      // 3. 验证父分类（如果提供）
      let parentCategory = null;
      let level = existingCategory.level;
      if (categoryData.parentId !== undefined) {
        parentCategory = await this.validateParentCategory(categoryData.parentId);
        level = parentCategory ? (parentCategory.level || 1) + 1 : 1;
      }

      // 4. 使用事务更新分类
      return await this.prisma.$transaction(async (tx) => {
        const now = BigInt(Date.now());
        
        // 准备更新数据
        const updateData = {
          updated_at: now,
          updated_by: userId
        };

        // 只更新提供的字段
        if (categoryData.name !== undefined) updateData.name = categoryData.name;
        if (categoryData.description !== undefined) updateData.description = categoryData.description;
        if (categoryData.imageUrl !== undefined) updateData.image_url = categoryData.imageUrl;
        if (categoryData.sortOrder !== undefined) updateData.sort_order = categoryData.sortOrder;
        if (categoryData.metaTitle !== undefined) updateData.meta_title = categoryData.metaTitle;
        if (categoryData.metaKeywords !== undefined) updateData.meta_keywords = categoryData.metaKeywords;
        if (categoryData.metaDescription !== undefined) updateData.meta_description = categoryData.metaDescription;
        if (categoryData.channelId !== undefined) updateData.created_channel_id = BigInt(categoryData.channelId);
        if (categoryData.platformId !== undefined) updateData.created_platform_id = BigInt(categoryData.platformId);
        if (categoryData.storeId !== undefined) updateData.created_store_id = BigInt(categoryData.storeId);
        
        if (categoryData.parentId !== undefined) {
          updateData.goods_parent_category_id = parentCategory ? BigInt(categoryData.parentId) : null;
          updateData.level = level;
        }

        // 更新分类
        const updatedCategory = await tx.goodsCategory.update({
          where: { id: BigInt(categoryId) },
          data: updateData
        });

        return this.formatCategoryData(updatedCategory);
      });
    } catch (error) {
      console.error('更新第三方商品分类失败:', error);
      throw error;
    }
  }

  /**
   * 格式化分类数据
   * @param {Object} category - 原始分类数据
   * @returns {Object} 格式化后的分类数据
   */
  formatCategoryData(category) {
    return {
      id: category.id.toString(),
      name: category.name,
      description: category.description,
      imageUrl: category.image_url,
      parentId: category.goods_parent_category_id ? category.goods_parent_category_id.toString() : null,
      level: category.level,
      sortOrder: category.sort_order,
      isEnabled: category.is_enabled,
      metaTitle: category.meta_title,
      metaKeywords: category.meta_keywords,
      metaDescription: category.meta_description,
      createdAt: category.created_at.toString(),
      updatedAt: category.updated_at.toString()
    };
  }
}

module.exports = ThirdPartyGoodsCategoryModel;
