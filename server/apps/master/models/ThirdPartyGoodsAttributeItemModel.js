/**
 * 第三方商品属性参数项数据模型
 * 专门处理第三方平台商品属性参数项创建和多平台关联的数据库操作
 */
const { prisma } = require('../../../core/database/prisma');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');
const GoodsSourceTypeEnum = require('../constants/GoodsSourceTypeEnum');

class ThirdPartyGoodsAttributeItemModel {
  constructor() {
    this.tableName = 'goods_attribute_items';
    this.relationTableName = 'goods_attribute_item_platform_relations';
    this.schema = 'base';
  }

  /**
   * 验证渠道平台店铺关系
   * @param {string} channelId - 渠道ID
   * @param {string} platformId - 平台ID
   * @param {string} storeId - 店铺ID
   * @returns {Promise<Object>} - 验证结果
   */
  async validateChannelPlatformStore(channelId, platformId, storeId) {
    try {
      // 验证渠道是否存在
      const channel = await prisma.channel.findUnique({
        where: { id: BigInt(channelId) }
      });
      
      if (!channel) {
        return { valid: false, message: `渠道ID ${channelId} 不存在` };
      }

      // 验证平台是否存在
      const platform = await prisma.platform.findUnique({
        where: { id: BigInt(platformId) }
      });
      
      if (!platform) {
        return { valid: false, message: `平台ID ${platformId} 不存在` };
      }

      // 验证店铺是否存在
      const store = await prisma.store.findUnique({
        where: { id: BigInt(storeId) }
      });
      
      if (!store) {
        return { valid: false, message: `店铺ID ${storeId} 不存在` };
      }

      return { valid: true, message: '验证通过' };
    } catch (error) {
      console.error('验证渠道平台店铺关系失败:', error);
      return { valid: false, message: '验证渠道平台店铺关系时发生错误' };
    }
  }

  /**
   * 验证属性集是否存在
   * @param {string} attributeSetId - 属性集ID
   * @returns {Promise<boolean>} - 是否存在
   */
  async validateAttributeSetExists(attributeSetId) {
    try {
      const attributeSet = await prisma.goodsAttributeSet.findFirst({
        where: {
          id: BigInt(attributeSetId),
          deleted_at: null
        }
      });

      return !!attributeSet;
    } catch (error) {
      console.error('验证属性集是否存在失败:', error);
      return false;
    }
  }

  /**
   * 检查属性参数项名称在同一属性集下是否已存在
   * @param {string} name - 属性参数项名称
   * @param {string} attributeSetId - 属性集ID
   * @param {string} excludeId - 排除的ID（用于更新时检查）
   * @returns {Promise<boolean>} - 是否存在
   */
  async checkAttributeItemNameExists(name, attributeSetId, excludeId = null) {
    try {
      const whereCondition = {
        name: name,
        goods_attribute_set_id: BigInt(attributeSetId),
        deleted_at: null
      };

      if (excludeId) {
        whereCondition.id = { not: BigInt(excludeId) };
      }

      const existingAttributeItem = await prisma.goodsAttributeItem.findFirst({
        where: whereCondition
      });

      return !!existingAttributeItem;
    } catch (error) {
      console.error('检查属性参数项名称是否存在失败:', error);
      return false;
    }
  }

  /**
   * 创建第三方商品属性参数项
   * @param {Object} attributeItemData - 属性参数项数据
   * @param {BigInt} userId - 创建者ID
   * @returns {Promise<Object>} - 创建结果
   */
  async createThirdPartyAttributeItem(attributeItemData, userId) {
    return await prisma.$transaction(async (tx) => {
      const now = BigInt(Date.now());
      
      // 1. 验证渠道平台店铺关系
      const validation = await this.validateChannelPlatformStore(
        attributeItemData.channelId, 
        attributeItemData.platformId, 
        attributeItemData.storeId
      );
      
      if (!validation.valid) {
        throw new Error(validation.message);
      }

      // 2. 验证属性集是否存在
      const attributeSetExists = await this.validateAttributeSetExists(attributeItemData.goodsAttributeSetId);
      if (!attributeSetExists) {
        throw new Error(`属性集ID ${attributeItemData.goodsAttributeSetId} 不存在`);
      }

      // 3. 检查属性参数项名称在同一属性集下是否已存在
      const nameExists = await this.checkAttributeItemNameExists(
        attributeItemData.name, 
        attributeItemData.goodsAttributeSetId
      );
      if (nameExists) {
        throw new Error(`属性参数项名称 "${attributeItemData.name}" 在该属性集下已存在`);
      }

      // 4. 生成属性参数项ID
      const attributeItemId = generateSnowflakeId();
      
      // 5. 创建商品属性参数项
      const attributeItem = await tx.goodsAttributeItem.create({
        data: {
          id: BigInt(attributeItemId),
          goods_attribute_set_id: BigInt(attributeItemData.goodsAttributeSetId),
          name: attributeItemData.name,
          type: attributeItemData.type || 'text',
          value: attributeItemData.value || null,
          is_required: attributeItemData.isRequired || 0,
          is_filterable: attributeItemData.isFilterable || 0,
          sort_order: attributeItemData.sortOrder || 0,
          is_enabled: attributeItemData.isEnabled !== undefined ? attributeItemData.isEnabled : 1,
          created_at: now,
          updated_at: now,
          created_by: userId,
          updated_by: userId
        }
      });

      // 6. 创建平台关联关系
      const relationId = generateSnowflakeId();
      const relation = await tx.goodsAttributeItemPlatformRelation.create({
        data: {
          id: BigInt(relationId),
          attribute_item_id: BigInt(attributeItemId),
          channel_id: BigInt(attributeItemData.channelId),
          platform_id: BigInt(attributeItemData.platformId),
          store_id: BigInt(attributeItemData.storeId),
          platform_attribute_item_id: attributeItemData.platformAttributeItemId || null,
          platform_attribute_item_name: attributeItemData.platformAttributeItemName || null,
          source_type: GoodsSourceTypeEnum.THIRD_PARTY,
          created_at: now,
          updated_at: now
        }
      });

      return {
        success: true,
        data: {
          attributeItemId: attributeItemId.toString(),
          relationId: relationId.toString(),
          name: attributeItem.name,
          goodsAttributeSetId: attributeItemData.goodsAttributeSetId,
          type: attributeItem.type,
          value: attributeItem.value,
          isRequired: attributeItem.is_required,
          isFilterable: attributeItem.is_filterable,
          sortOrder: attributeItem.sort_order,
          isEnabled: attributeItem.is_enabled,
          channelId: attributeItemData.channelId,
          platformId: attributeItemData.platformId,
          storeId: attributeItemData.storeId,
          platformAttributeItemId: relation.platform_attribute_item_id,
          platformAttributeItemName: relation.platform_attribute_item_name,
          createdAt: now.toString()
        }
      };
    });
  }

  /**
   * 更新第三方商品属性参数项
   * @param {string} id - 属性参数项ID
   * @param {Object} attributeItemData - 更新数据
   * @param {BigInt} userId - 更新者ID
   * @returns {Promise<Object>} - 更新结果
   */
  async updateThirdPartyAttributeItem(id, attributeItemData, userId) {
    return await prisma.$transaction(async (tx) => {
      const now = BigInt(Date.now());
      const attributeItemId = BigInt(id);

      // 1. 检查属性参数项是否存在
      const existingAttributeItem = await tx.goodsAttributeItem.findFirst({
        where: {
          id: attributeItemId,
          deleted_at: null
        }
      });

      if (!existingAttributeItem) {
        throw new Error(`属性参数项ID ${id} 不存在`);
      }

      // 2. 如果更新了渠道平台店铺，需要验证关系
      if (attributeItemData.channelId || attributeItemData.platformId || attributeItemData.storeId) {
        // 获取当前关联信息
        const currentRelation = await tx.goodsAttributeItemPlatformRelation.findFirst({
          where: { attribute_item_id: attributeItemId }
        });

        const channelId = attributeItemData.channelId || currentRelation?.channel_id?.toString();
        const platformId = attributeItemData.platformId || currentRelation?.platform_id?.toString();
        const storeId = attributeItemData.storeId || currentRelation?.store_id?.toString();

        const validation = await this.validateChannelPlatformStore(channelId, platformId, storeId);
        if (!validation.valid) {
          throw new Error(validation.message);
        }
      }

      // 3. 如果更新了属性集ID，需要验证
      if (attributeItemData.goodsAttributeSetId) {
        const attributeSetExists = await this.validateAttributeSetExists(attributeItemData.goodsAttributeSetId);
        if (!attributeSetExists) {
          throw new Error(`属性集ID ${attributeItemData.goodsAttributeSetId} 不存在`);
        }
      }

      // 4. 如果更新了名称，检查是否重复
      if (attributeItemData.name) {
        const attributeSetId = attributeItemData.goodsAttributeSetId || existingAttributeItem.goods_attribute_set_id.toString();
        const nameExists = await this.checkAttributeItemNameExists(attributeItemData.name, attributeSetId, id);
        if (nameExists) {
          throw new Error(`属性参数项名称 "${attributeItemData.name}" 在该属性集下已存在`);
        }
      }

      // 5. 更新属性参数项基本信息
      const updateData = {
        updated_at: now,
        updated_by: userId
      };

      if (attributeItemData.name !== undefined) updateData.name = attributeItemData.name;
      if (attributeItemData.goodsAttributeSetId !== undefined) updateData.goods_attribute_set_id = BigInt(attributeItemData.goodsAttributeSetId);
      if (attributeItemData.type !== undefined) updateData.type = attributeItemData.type;
      if (attributeItemData.value !== undefined) updateData.value = attributeItemData.value;
      if (attributeItemData.isRequired !== undefined) updateData.is_required = attributeItemData.isRequired;
      if (attributeItemData.isFilterable !== undefined) updateData.is_filterable = attributeItemData.isFilterable;
      if (attributeItemData.sortOrder !== undefined) updateData.sort_order = attributeItemData.sortOrder;
      if (attributeItemData.isEnabled !== undefined) updateData.is_enabled = attributeItemData.isEnabled;

      const updatedAttributeItem = await tx.goodsAttributeItem.update({
        where: { id: attributeItemId },
        data: updateData
      });

      // 6. 更新平台关联关系
      const relationUpdateData = { updated_at: now };
      
      if (attributeItemData.channelId !== undefined) relationUpdateData.channel_id = BigInt(attributeItemData.channelId);
      if (attributeItemData.platformId !== undefined) relationUpdateData.platform_id = BigInt(attributeItemData.platformId);
      if (attributeItemData.storeId !== undefined) relationUpdateData.store_id = BigInt(attributeItemData.storeId);
      if (attributeItemData.platformAttributeItemId !== undefined) relationUpdateData.platform_attribute_item_id = attributeItemData.platformAttributeItemId;
      if (attributeItemData.platformAttributeItemName !== undefined) relationUpdateData.platform_attribute_item_name = attributeItemData.platformAttributeItemName;

      await tx.goodsAttributeItemPlatformRelation.updateMany({
        where: { attribute_item_id: attributeItemId },
        data: relationUpdateData
      });

      // 7. 获取更新后的完整信息
      const finalRelation = await tx.goodsAttributeItemPlatformRelation.findFirst({
        where: { attribute_item_id: attributeItemId }
      });

      return {
        success: true,
        data: {
          attributeItemId: id,
          name: updatedAttributeItem.name,
          goodsAttributeSetId: updatedAttributeItem.goods_attribute_set_id.toString(),
          type: updatedAttributeItem.type,
          value: updatedAttributeItem.value,
          isRequired: updatedAttributeItem.is_required,
          isFilterable: updatedAttributeItem.is_filterable,
          sortOrder: updatedAttributeItem.sort_order,
          isEnabled: updatedAttributeItem.is_enabled,
          channelId: finalRelation?.channel_id?.toString(),
          platformId: finalRelation?.platform_id?.toString(),
          storeId: finalRelation?.store_id?.toString(),
          platformAttributeItemId: finalRelation?.platform_attribute_item_id,
          platformAttributeItemName: finalRelation?.platform_attribute_item_name,
          updatedAt: now.toString()
        }
      };
    });
  }
}

module.exports = ThirdPartyGoodsAttributeItemModel;
