/**
 * 发票类型枚举
 * 定义发票的不同类型
 */

const InvoiceTypeEnum = {
  /**
   * 不开发票 - 用户选择不需要发票
   */
  NONE: 0,
  
  /**
   * 个人发票 - 个人消费者发票
   */
  PERSONAL: 1,
  
  /**
   * 企业发票 - 企业/公司发票，包含完整的企业信息
   */
  COMPANY: 2,
  
  /**
   * 根据类型码获取类型名称
   * @param {number} typeCode - 类型码
   * @returns {string} - 类型名称
   */
  getTypeName(typeCode) {
    switch (parseInt(typeCode)) {
      case this.NONE:
        return '不开发票';
      case this.PERSONAL:
        return '个人发票';
      case this.COMPANY:
        return '企业发票';
      default:
        return '未知类型';
    }
  },
  
  /**
   * 获取所有发票类型选项
   * @returns {Array} - 发票类型选项数组
   */
  getAllTypes() {
    return [
      { value: this.NONE, label: this.getTypeName(this.NONE) },
      { value: this.PERSONAL, label: this.getTypeName(this.PERSONAL) },
      { value: this.COMPANY, label: this.getTypeName(this.COMPANY) }
    ];
  },
  
  /**
   * 验证发票类型是否有效
   * @param {number} typeCode - 类型码
   * @returns {boolean} - 是否有效
   */
  isValidType(typeCode) {
    const validTypes = [this.NONE, this.PERSONAL, this.COMPANY];
    return validTypes.includes(parseInt(typeCode));
  }
};

module.exports = InvoiceTypeEnum;
