/**
 * 商品同步状态枚举
 * 定义商品在第三方平台的推送和同步状态
 */

const GoodsSyncStatusEnum = {
  /**
   * 推送状态枚举
   */
  PUSH_STATUS: {
    /**
     * 未推送 - 商品尚未推送到第三方平台
     */
    NOT_PUSHED: 0,
    
    /**
     * 推送中 - 正在推送到第三方平台
     */
    PUSHING: 1,
    
    /**
     * 推送成功 - 已成功推送到第三方平台
     */
    PUSH_SUCCESS: 2,
    
    /**
     * 推送失败 - 推送到第三方平台失败
     */
    PUSH_FAILED: 3
  },

  /**
   * 同步状态枚举
   */
  SYNC_STATUS: {
    /**
     * 未同步 - 尚未从第三方平台同步数据
     */
    NOT_SYNCED: 0,
    
    /**
     * 同步中 - 正在从第三方平台同步数据
     */
    SYNCING: 1,
    
    /**
     * 同步成功 - 已成功从第三方平台同步数据
     */
    SYNC_SUCCESS: 2,
    
    /**
     * 同步失败 - 从第三方平台同步数据失败
     */
    SYNC_FAILED: 3
  },

  /**
   * 获取推送状态名称
   * @param {number} statusCode - 推送状态码
   * @returns {string} - 推送状态名称
   */
  getPushStatusName(statusCode) {
    switch (parseInt(statusCode)) {
      case this.PUSH_STATUS.NOT_PUSHED:
        return '未推送';
      case this.PUSH_STATUS.PUSHING:
        return '推送中';
      case this.PUSH_STATUS.PUSH_SUCCESS:
        return '推送成功';
      case this.PUSH_STATUS.PUSH_FAILED:
        return '推送失败';
      default:
        return '未知状态';
    }
  },

  /**
   * 获取同步状态名称
   * @param {number} statusCode - 同步状态码
   * @returns {string} - 同步状态名称
   */
  getSyncStatusName(statusCode) {
    switch (parseInt(statusCode)) {
      case this.SYNC_STATUS.NOT_SYNCED:
        return '未同步';
      case this.SYNC_STATUS.SYNCING:
        return '同步中';
      case this.SYNC_STATUS.SYNC_SUCCESS:
        return '同步成功';
      case this.SYNC_STATUS.SYNC_FAILED:
        return '同步失败';
      default:
        return '未知状态';
    }
  },

  /**
   * 获取所有推送状态选项
   * @returns {Array} - 推送状态选项数组
   */
  getAllPushStatusOptions() {
    return [
      { label: '未推送', value: this.PUSH_STATUS.NOT_PUSHED },
      { label: '推送中', value: this.PUSH_STATUS.PUSHING },
      { label: '推送成功', value: this.PUSH_STATUS.PUSH_SUCCESS },
      { label: '推送失败', value: this.PUSH_STATUS.PUSH_FAILED }
    ];
  },

  /**
   * 获取所有同步状态选项
   * @returns {Array} - 同步状态选项数组
   */
  getAllSyncStatusOptions() {
    return [
      { label: '未同步', value: this.SYNC_STATUS.NOT_SYNCED },
      { label: '同步中', value: this.SYNC_STATUS.SYNCING },
      { label: '同步成功', value: this.SYNC_STATUS.SYNC_SUCCESS },
      { label: '同步失败', value: this.SYNC_STATUS.SYNC_FAILED }
    ];
  },

  /**
   * 验证推送状态是否有效
   * @param {number} statusCode - 推送状态码
   * @returns {boolean} - 是否有效
   */
  isValidPushStatus(statusCode) {
    const validStatuses = Object.values(this.PUSH_STATUS);
    return validStatuses.includes(parseInt(statusCode));
  },

  /**
   * 验证同步状态是否有效
   * @param {number} statusCode - 同步状态码
   * @returns {boolean} - 是否有效
   */
  isValidSyncStatus(statusCode) {
    const validStatuses = Object.values(this.SYNC_STATUS);
    return validStatuses.includes(parseInt(statusCode));
  },

  /**
   * 判断是否需要重试推送
   * @param {number} pushStatus - 推送状态
   * @param {number} retryCount - 重试次数
   * @param {number} maxRetries - 最大重试次数
   * @returns {boolean} - 是否需要重试
   */
  shouldRetryPush(pushStatus, retryCount, maxRetries = 3) {
    return parseInt(pushStatus) === this.PUSH_STATUS.PUSH_FAILED && 
           parseInt(retryCount) < maxRetries;
  },

  /**
   * 判断是否需要重试同步
   * @param {number} syncStatus - 同步状态
   * @param {number} retryCount - 重试次数
   * @param {number} maxRetries - 最大重试次数
   * @returns {boolean} - 是否需要重试
   */
  shouldRetrySync(syncStatus, retryCount, maxRetries = 3) {
    return parseInt(syncStatus) === this.SYNC_STATUS.SYNC_FAILED && 
           parseInt(retryCount) < maxRetries;
  }
};

module.exports = GoodsSyncStatusEnum;
