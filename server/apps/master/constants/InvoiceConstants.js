/**
 * 发票相关常量定义
 */

/**
 * 订单开票状态枚举
 */
const ORDER_INVOICE_STATUS = {
  NOT_APPLIED: 0,    // 未申请开票
  APPLIED: 1,        // 已申请开票
  INVOICED: 2        // 已开票
};

/**
 * 订单开票状态文本映射
 */
const ORDER_INVOICE_STATUS_TEXT = {
  [ORDER_INVOICE_STATUS.NOT_APPLIED]: '未申请开票',
  [ORDER_INVOICE_STATUS.APPLIED]: '已申请开票',
  [ORDER_INVOICE_STATUS.INVOICED]: '已开票'
};

/**
 * 发票类型枚举
 */
const INVOICE_TYPE = {
  ELECTRONIC: 1,     // 电子发票
  PAPER: 2          // 纸质发票
};

/**
 * 发票类型文本映射
 */
const INVOICE_TYPE_TEXT = {
  [INVOICE_TYPE.ELECTRONIC]: '电子发票',
  [INVOICE_TYPE.PAPER]: '纸质发票'
};

/**
 * 发票状态枚举
 */
const INVOICE_STATUS = {
  ISSUED: 0,         // 已开具
  SENT: 1,          // 已发送
  VOIDED: 2         // 已作废
};

/**
 * 发票状态文本映射
 */
const INVOICE_STATUS_TEXT = {
  [INVOICE_STATUS.ISSUED]: '已开具',
  [INVOICE_STATUS.SENT]: '已发送',
  [INVOICE_STATUS.VOIDED]: '已作废'
};

/**
 * 发票抬头类型枚举
 */
const INVOICE_HEADER_TYPE = {
  PERSONAL: 1,       // 个人抬头
  ENTERPRISE: 2      // 企业抬头
};

/**
 * 发票抬头类型文本映射
 */
const INVOICE_HEADER_TYPE_TEXT = {
  [INVOICE_HEADER_TYPE.PERSONAL]: '个人抬头',
  [INVOICE_HEADER_TYPE.ENTERPRISE]: '企业抬头'
};

/**
 * 发票申请状态枚举
 */
const INVOICE_APPLICATION_STATUS = {
  PENDING: 0,        // 待审核
  APPROVED: 1,       // 已通过
  REJECTED: 2        // 已拒绝
};

/**
 * 发票申请状态文本映射
 */
const INVOICE_APPLICATION_STATUS_TEXT = {
  [INVOICE_APPLICATION_STATUS.PENDING]: '待审核',
  [INVOICE_APPLICATION_STATUS.APPROVED]: '已通过',
  [INVOICE_APPLICATION_STATUS.REJECTED]: '已拒绝'
};

module.exports = {
  // 订单开票状态
  ORDER_INVOICE_STATUS,
  ORDER_INVOICE_STATUS_TEXT,
  
  // 发票类型
  INVOICE_TYPE,
  INVOICE_TYPE_TEXT,
  
  // 发票状态
  INVOICE_STATUS,
  INVOICE_STATUS_TEXT,
  
  // 发票抬头类型
  INVOICE_HEADER_TYPE,
  INVOICE_HEADER_TYPE_TEXT,
  
  // 发票申请状态
  INVOICE_APPLICATION_STATUS,
  INVOICE_APPLICATION_STATUS_TEXT
};
