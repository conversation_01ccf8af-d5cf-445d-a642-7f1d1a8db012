/**
 * 商品来源类型枚举
 * 定义商品的创建来源类型
 */

const GoodsSourceTypeEnum = {
  /**
   * 系统订单 - 内部系统创建的商品
   */
  SYSTEM: 0,
  
  /**
   * 第三方订单 - 第三方平台创建的商品
   */
  THIRD_PARTY: 1,
  
  /**
   * 根据来源类型码获取来源名称
   * @param {number} sourceTypeCode - 来源类型码
   * @returns {string} - 来源类型名称
   */
  getSourceTypeName(sourceTypeCode) {
    switch (parseInt(sourceTypeCode)) {
      case this.SYSTEM:
        return '系统订单';
      case this.THIRD_PARTY:
        return '第三方订单';
      default:
        return '未知来源';
    }
  },

  /**
   * 获取所有来源类型选项
   * @returns {Array} - 来源类型选项数组
   */
  getAllOptions() {
    return [
      { label: '系统订单', value: this.SYSTEM },
      { label: '第三方订单', value: this.THIRD_PARTY }
    ];
  },

  /**
   * 验证来源类型是否有效
   * @param {number} sourceTypeCode - 来源类型码
   * @returns {boolean} - 是否有效
   */
  isValid(sourceTypeCode) {
    const validTypes = [this.SYSTEM, this.THIRD_PARTY];
    return validTypes.includes(parseInt(sourceTypeCode));
  }
};

module.exports = GoodsSourceTypeEnum;
