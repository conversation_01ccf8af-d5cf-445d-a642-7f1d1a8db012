const ThirdPartyGoodsBrandService = require('../services/ThirdPartyGoodsBrandService');

/**
 * 第三方商品品牌控制器
 * 处理第三方平台的品牌HTTP请求
 */
class ThirdPartyGoodsBrandController {
  constructor(prisma) {
    this.prisma = prisma;
    this.thirdPartyGoodsBrandService = new ThirdPartyGoodsBrandService(prisma);
  }

  /**
   * 创建第三方商品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async createThirdPartyBrand(req, res) {
    try {
      const brandData = req.body;
      
      const result = await this.thirdPartyGoodsBrandService.createThirdPartyBrand(brandData);
      
      res.status(200).json({
        code: 200,
        message: '第三方商品品牌创建成功',
        data: result
      });
    } catch (error) {
      console.error('创建第三方商品品牌失败:', error);
      
      // 处理验证错误
      if (error.message && error.message.includes('请求数据验证失败')) {
        return res.status(200).json({
          code: 500,
          message: error.message,
          data: 400
        });
      }
      
      // 处理业务逻辑错误
      if (error.message && (
        error.message.includes('不存在') || 
        error.message.includes('不属于')
      )) {
        return res.status(200).json({
          code: 500,
          message: error.message,
          data: 404
        });
      }
      
      // 处理其他错误
      res.status(200).json({
        code: 500,
        message: '创建第三方商品品牌失败',
        data: 500
      });
    }
  }

  /**
   * 更新第三方商品品牌
   * @param {Object} req 请求对象
   * @param {Object} res 响应对象
   */
  async updateThirdPartyBrand(req, res) {
    try {
      const { id } = req.params;
      const brandData = req.body;
      
      if (!id) {
        return res.status(200).json({
          code: 500,
          message: '品牌ID不能为空',
          data: 400
        });
      }
      
      const result = await this.thirdPartyGoodsBrandService.updateThirdPartyBrand(id, brandData);
      
      res.status(200).json({
        code: 200,
        message: '第三方商品品牌更新成功',
        data: result
      });
    } catch (error) {
      console.error(`更新第三方商品品牌失败 (ID: ${req.params.id}):`, error);
      
      // 处理验证错误
      if (error.message && error.message.includes('请求数据验证失败')) {
        return res.status(200).json({
          code: 500,
          message: error.message,
          data: 400
        });
      }
      
      // 处理品牌不存在错误
      if (error.message && error.message.includes('品牌不存在')) {
        return res.status(200).json({
          code: 500,
          message: error.message,
          data: 404
        });
      }
      
      // 处理其他错误
      res.status(200).json({
        code: 500,
        message: '更新第三方商品品牌失败',
        data: 500
      });
    }
  }
}

module.exports = ThirdPartyGoodsBrandController;
