/**
 * Master端订单报备管理控制器
 * 处理报备列表、详情、审核等功能
 */
const BaseController = require('../../../core/controllers/BaseController');
const { Prisma } = require('@prisma/client');

class OrderReportController extends BaseController {
    /**
     * 构造函数
     * @param {Object} prisma - Prisma客户端实例
     */
    constructor(prisma) {
        super(prisma);
        console.log('Master OrderReportController initialized with prisma:', !!this.prisma);
    }

    /**
     * 生成雪花算法ID
     * @returns {BigInt} 生成的ID
     */
    generateId() {
        const timestamp = BigInt(Date.now());
        const machineId = BigInt(1);
        const sequence = BigInt(Math.floor(Math.random() * 4096));

        return (timestamp << BigInt(22)) | (machineId << BigInt(12)) | sequence;
    }

    /**
     * 获取报备列表
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async getReportList(req, res) {
        try {
            const {
                page = 1,
                pageSize = 10,
                platform_id,
                audit_status,
                start_date,
                end_date,
                receiver_name,
                provider_id
            } = req.query;

            const offset = BigInt((page - 1) * pageSize);
            const limit = BigInt(pageSize);

            // 构建查询条件
            let whereConditions = ['r."deleted_at" IS NULL'];
            let queryParams = [];

            if (platform_id) {
                whereConditions.push(`r."platform_id" = $${queryParams.length + 1}`);
                queryParams.push(platform_id);
            }

            if (audit_status !== undefined && audit_status !== '') {
                whereConditions.push(`r."audit_status" = $${queryParams.length + 1}`);
                queryParams.push(parseInt(audit_status));
            }

            if (start_date) {
                whereConditions.push(`r."created_at" >= $${queryParams.length + 1}`);
                queryParams.push(BigInt(start_date));
            }

            if (end_date) {
                whereConditions.push(`r."created_at" <= $${queryParams.length + 1}`);
                queryParams.push(BigInt(end_date));
            }

            if (receiver_name) {
                whereConditions.push(`r."recipient_name" LIKE $${queryParams.length + 1}`);
                queryParams.push(`%${receiver_name}%`);
            }

            if (provider_id) {
                whereConditions.push(`r."provider_id" = $${queryParams.length + 1}`);
                queryParams.push(BigInt(provider_id));
            }

            const whereClause = whereConditions.join(' AND ');

            // 查询总数
            const countQuery = `
                SELECT COUNT(*) as total
                FROM "provider"."order_report" r
                WHERE ${whereClause}
            `;

            const countResult = await this.prisma.$queryRawUnsafe(countQuery, ...queryParams);
            const total = parseInt(countResult[0].total);

            // 查询列表数据
            const listQuery = `
                SELECT
                    r.id,
                    r.platform_id,
                    r.report_type,
                    r.report_amount,
                    r.expected_order_time,
                    r.related_order_number,
                    r.expected_shipping_time,
                    r.recipient_name,
                    r.contact_phone,
                    r.shipping_address,
                    r.remark,
                    r.audit_status,
                    r.audit_remark,
                    r.reporter_id,
                    r.provider_id,
                    r.created_at,
                    r.updated_at,
                    c.name as platform_name,
                    pu.company_name as provider_company_name
                FROM "provider"."order_report" r
                LEFT JOIN "base"."channel" c ON r.platform_id = c.id::text
                LEFT JOIN "provider"."provider_user" pu ON r.provider_id = pu.id
                WHERE ${whereClause}
                ORDER BY r.created_at DESC
                LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
            `;

            queryParams.push(limit, offset);
            const reports = await this.prisma.$queryRawUnsafe(listQuery, ...queryParams);

            // 格式化数据
            const formattedReports = reports.map(report => ({
                ...report,
                id: report.id.toString(),
                report_amount: parseFloat(report.report_amount || 0),
                reporter_id: report.reporter_id.toString(),
                provider_id: report.provider_id.toString(),
                created_at: report.created_at.toString(),
                updated_at: report.updated_at.toString(),
                expected_order_time: report.expected_order_time ? report.expected_order_time.toString() : null,
                expected_shipping_time: report.expected_shipping_time ? report.expected_shipping_time.toString() : null,
                provider_company_name: report.provider_company_name || '未知服务商'
            }));

            return this.successList(res, formattedReports, total, parseInt(page), parseInt(pageSize), '获取报备列表成功');

        } catch (error) {
            console.error('获取报备列表失败:', error);
            return this.fail(res, '获取报备列表失败', 500);
        }
    }

    /**
     * 获取报备详情
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async getReportDetail(req, res) {
        try {
            const { id } = req.params;

            if (!id) {
                return this.fail(res, '报备ID不能为空', 400);
            }

            // 将ID转换为BigInt类型
            const reportIdBigInt = BigInt(id);

            // 查询报备单信息
            const reportResult = await this.prisma.$queryRaw`
                SELECT
                    r.id, r.customer_name, r.customer_region, r.platform_id, r.report_type,
                    r.report_amount, r.expected_order_time, r.related_order_number, r.expected_shipping_time,
                    r.recipient_name, r.contact_phone, r.shipping_address, r.remark, r.audit_status,
                    r.audit_remark, r.reporter_id, r.provider_id, r.created_at, r.updated_at,
                    r.audit_user_id, r.audit_time,
                    c.name as platform_name,
                    pu.company_name as provider_company_name,
                    su.username as audit_username
                FROM "provider"."order_report" r
                LEFT JOIN "base"."channel" c ON r.platform_id = c.id::text
                LEFT JOIN "provider"."provider_user" pu ON r.provider_id = pu.id
                LEFT JOIN "base"."system_user" su ON r.audit_user_id = su.id
                WHERE r.id = ${reportIdBigInt}
                AND r.deleted_at IS NULL
            `;

            if (!reportResult || reportResult.length === 0) {
                return this.fail(res, '报备单不存在', 404);
            }

            const report = reportResult[0];

            // 查询报备商品详情
            const itemsResult = await this.prisma.$queryRaw`
                SELECT
                    id, product_id, product_name, product_image, specification, quantity,
                    unit_price, report_price, subtotal, product_sku
                FROM "provider"."order_report_item"
                WHERE order_report_id = ${reportIdBigInt}
                AND deleted_at IS NULL
                ORDER BY id
            `;

            // 格式化报备信息
            const formattedReport = {
                ...report,
                id: report.id.toString(),
                report_amount: parseFloat(report.report_amount || 0),
                reporter_id: report.reporter_id.toString(),
                provider_id: report.provider_id.toString(),
                created_at: report.created_at.toString(),
                updated_at: report.updated_at.toString(),
                expected_order_time: report.expected_order_time ? report.expected_order_time.toString() : null,
                expected_shipping_time: report.expected_shipping_time ? report.expected_shipping_time.toString() : null,
                provider_company_name: report.provider_company_name || '未知服务商',
                audit_user_id: report.audit_user_id ? report.audit_user_id.toString() : null,
                audit_time: report.audit_time ? report.audit_time.toString() : null,
                audit_username: report.audit_username || null,
                items: itemsResult.map(item => ({
                    ...item,
                    id: item.id.toString(),
                    product_id: item.product_id.toString(),
                    quantity: parseInt(item.quantity),
                    unit_price: parseFloat(item.unit_price || 0),
                    report_price: parseFloat(item.report_price || 0),
                    subtotal: parseFloat(item.subtotal || 0)
                }))
            };

            return this.success(res, formattedReport, '获取报备详情成功');

        } catch (error) {
            console.error('获取报备详情失败:', error);
            return this.fail(res, '获取报备详情失败', 500);
        }
    }

    /**
     * 审核报备
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async auditReport(req, res) {
        try {
            const { id } = req.params;
            const { audit_status, audit_remark, audit_user_id } = req.body;

            if (!id) {
                return this.fail(res, '报备ID不能为空', 400);
            }

            if (audit_status === undefined || audit_status === null) {
                return this.fail(res, '审核状态不能为空', 400);
            }

            // 验证审核状态值
            const validStatuses = [2, 3]; // 2-审核通过, 3-审核驳回
            if (!validStatuses.includes(parseInt(audit_status))) {
                return this.fail(res, '无效的审核状态', 400);
            }

            // 获取当前用户ID（从认证中间件获取）
            const userId = req.user?.id ? BigInt(req.user.id) : null;
            if (!userId) {
                return this.fail(res, '用户未登录', 401);
            }

            const reportIdBigInt = BigInt(id);
            const timestamp = BigInt(Date.now());

            // 使用传入的audit_user_id或默认使用当前登录用户ID
            const auditUserId = audit_user_id ? BigInt(audit_user_id) : userId;
            const auditTime = timestamp;

            // 检查报备单是否存在且状态为待审核
            const existingReport = await this.prisma.$queryRaw`
                SELECT id, audit_status
                FROM "provider"."order_report"
                WHERE id = ${reportIdBigInt}
                AND deleted_at IS NULL
            `;

            if (!existingReport || existingReport.length === 0) {
                return this.fail(res, '报备单不存在', 404);
            }

            if (existingReport[0].audit_status !== 1) {
                return this.fail(res, '只能审核待审核状态的报备单', 400);
            }

            // 更新审核状态，添加审核人ID和审核时间
            await this.prisma.$executeRaw`
                UPDATE "provider"."order_report"
                SET
                    audit_status = ${parseInt(audit_status)},
                    audit_remark = ${audit_remark || null},
                    audit_user_id = ${auditUserId},
                    audit_time = ${auditTime},
                    updated_at = ${timestamp},
                    updated_by = ${userId}
                WHERE id = ${reportIdBigInt}
            `;

            const statusText = audit_status == 2 ? '审核通过' : '审核驳回';
            return this.success(res, null, `报备${statusText}成功`);

        } catch (error) {
            console.error('审核报备失败:', error);
            return this.fail(res, '审核报备失败', 500);
        }
    }
}

module.exports = OrderReportController;
