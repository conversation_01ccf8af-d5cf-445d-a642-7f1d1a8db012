const BaseController = require('../../../core/controllers/BaseController');

/**
 * 服务商订单管理控制器
 * 处理服务商端的订单列表查询，基于权限控制
 */
class CooperativeOrderController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma - Prisma客户端实例
   */
  constructor(prisma) {
    super();
    this.prisma = prisma;
  }

  /**
   * 获取服务商订单列表
   * 只返回当前登录用户有权限查看的订单（基于 provider.order_assignment 表的 salesman_id）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrders(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      // 获取查询参数
      const page = parseInt(req.query.page) || 1;
      const pageSize = parseInt(req.query.pageSize) || 10;
      const sortField = req.query.sortField || 'created_at';
      const sortOrder = req.query.sortOrder || 'desc';

      // 构建过滤条件
      const filters = {};
      
      // 订单编号筛选
      if (req.query.orderNumber) {
        filters.orderNumber = req.query.orderNumber;
      }
      
      // 第三方订单编号筛选
      if (req.query.thirdPartyOrderSn) {
        filters.thirdPartyOrderSn = req.query.thirdPartyOrderSn;
      }

      // 商品名称筛选
      if (req.query.productName) {
        filters.productName = req.query.productName;
      }

      // 收货人筛选
      if (req.query.receiverName) {
        filters.receiverName = req.query.receiverName;
      }

      // 订单来源筛选
      if (req.query.orderSource) {
        filters.orderSource = req.query.orderSource;
      }

      // 订单状态筛选
      if (req.query.orderStatus) {
        filters.orderStatus = req.query.orderStatus;
      }

      // 时间范围筛选
      if (req.query.timeRange) {
        filters.timeRange = req.query.timeRange;
      }

      // 下单时间范围筛选
      if (req.query.orderTimeStart && req.query.orderTimeEnd) {
        filters.orderTimeStart = req.query.orderTimeStart;
        filters.orderTimeEnd = req.query.orderTimeEnd;
      }

      // 调用服务获取订单列表
      const result = await this.getOrdersWithPermission(userId, filters, page, pageSize, sortField, sortOrder);

      // 返回成功响应
      this.success(res, {
        items: result.orders || [],
        pageInfo: {
          total: result.total || 0,
          currentPage: page,
          totalPage: Math.ceil((result.total || 0) / pageSize),
          pageSize: pageSize
        }
      }, '获取订单列表成功', 200);

    } catch (error) {
      console.error('获取服务商订单列表失败:', error);
      this.fail(res, `获取订单列表失败: ${error.message}`, null, 500);
    }
  }

  /**
   * 获取有权限的订单列表
   * @param {string} userId - 当前用户ID
   * @param {Object} filters - 过滤条件
   * @param {number} page - 页码
   * @param {number} pageSize - 每页大小
   * @param {string} sortField - 排序字段
   * @param {string} sortOrder - 排序方向
   * @returns {Promise<Object>} - 订单列表和总数
   */
  async getOrdersWithPermission(userId, filters, page, pageSize, sortField, sortOrder) {
    try {
      // 使用Prisma ORM查询而不是原生SQL
      const userIdBigInt = BigInt(userId);

      // 首先获取用户有权限的订单ID列表
      const orderAssignments = await this.prisma.orderAssignment.findMany({
        where: {
          salesman_id: userIdBigInt,
          deleted_at: null
        },
        select: {
          order_id: true
        }
      });

      if (orderAssignments.length === 0) {
        return {
          orders: [],
          total: 0
        };
      }

      const orderIds = orderAssignments.map(oa => oa.order_id);

      // 构建订单查询条件
      const orderWhere = {
        id: {
          in: orderIds
        },
        deleted_at: null
      };

      // 添加筛选条件
      if (filters.orderNumber) {
        try {
          // 尝试将订单号转换为BigInt
          const orderIdBigInt = BigInt(filters.orderNumber);
          orderWhere.id = {
            ...orderWhere.id,
            equals: orderIdBigInt
          };
        } catch (error) {
          // 如果转换失败，说明不是有效的订单ID，返回空结果
          return {
            orders: [],
            total: 0
          };
        }
      }

      if (filters.orderStatus !== undefined) {
        orderWhere.order_status = parseInt(filters.orderStatus);
      }

      if (filters.orderTimeStart && filters.orderTimeEnd) {
        try {
          orderWhere.created_at = {
            gte: BigInt(new Date(filters.orderTimeStart).getTime()),
            lte: BigInt(new Date(filters.orderTimeEnd).getTime())
          };
        } catch (error) {
          console.warn('时间筛选参数无效:', error.message);
        }
      } else if (filters.timeRange) {
        const now = Date.now();
        let startTime;

        switch (filters.timeRange) {
          case 'recent6m':
            startTime = now - (6 * 30 * 24 * 60 * 60 * 1000);
            break;
          case 'recent3m':
            startTime = now - (3 * 30 * 24 * 60 * 60 * 1000);
            break;
          case 'recent1m':
            startTime = now - (30 * 24 * 60 * 60 * 1000);
            break;
          case 'recent1w':
            startTime = now - (7 * 24 * 60 * 60 * 1000);
            break;
        }

        if (startTime) {
          try {
            orderWhere.created_at = {
              gte: BigInt(startTime)
            };
          } catch (error) {
            console.warn('时间范围筛选参数无效:', error.message);
          }
        }
      }

      // 获取总数
      const total = await this.prisma.orders.count({
        where: orderWhere
      });

      // 获取订单列表
      const orders = await this.prisma.orders.findMany({
        where: orderWhere,
        include: {
          order_items: {
            select: {
              product_name: true,
              sku_specifications: true,
              product_image: true,
              unit_price: true,
              quantity: true,
              total_price: true
            }
          },
          order_shipping_info: {
            select: {
              recipient_name: true,
              recipient_phone: true,
              street_address: true,
              region_province_id: true,
              region_city_id: true,
              region_district_id: true,
              region_path_name: true
            }
          },
          channel: {
            select: {
              name: true,
              icon_url: true
            }
          }
        },
        orderBy: {
          [sortField]: sortOrder.toLowerCase()
        },
        skip: (page - 1) * pageSize,
        take: pageSize
      });

      // 获取订单分配信息
      const orderAssignmentMap = {};
      const assignments = await this.prisma.orderAssignment.findMany({
        where: {
          order_id: {
            in: orderIds
          },
          salesman_id: userIdBigInt,
          deleted_at: null
        }
      });

      assignments.forEach(assignment => {
        orderAssignmentMap[assignment.order_id.toString()] = assignment;
      });

      // 获取业务员信息
      const salesmanIds = [...new Set(assignments.map(a => a.salesman_id))];
      const salesmen = await this.prisma.baseSystemUser.findMany({
        where: {
          id: {
            in: salesmanIds
          }
        },
        select: {
          id: true,
          nickname: true
        }
      });

      const salesmanMap = {};
      salesmen.forEach(salesman => {
        salesmanMap[salesman.id.toString()] = salesman;
      });

      // 处理订单数据
      const processedOrders = orders.map(order => {
        const assignment = orderAssignmentMap[order.id.toString()];
        const salesman = assignment ? salesmanMap[assignment.salesman_id.toString()] : null;

        return {
          id: order.id.toString(),
          third_party_order_sn: order.third_party_order_sn,
          order_status: order.order_status,
          payment_status: order.payment_status,
          total_amount: parseFloat(order.total_amount || 0),
          paid_amount: parseFloat(order.paid_amount || 0),
          created_at: order.created_at ? order.created_at.toString() : null,
          updated_at: order.updated_at ? order.updated_at.toString() : null,
          recipient_name: order.order_shipping_info?.recipient_name || '',
          recipient_phone: order.order_shipping_info?.recipient_phone || '',
          recipient_address: order.order_shipping_info?.street_address || '',
          channel_name: order.channel?.name || '',
          channel_icon_url: order.channel?.icon_url || '',
          rate: assignment ? parseFloat(assignment.rate || 0) : 0,
          assignment_amount: assignment ? parseFloat(assignment.assignment_amount || 0) : 0,
          salesman_name: salesman?.nickname || '',
          products: order.order_items.map(item => ({
            product_name: item.product_name,
            product_spec: item.sku_specifications ? JSON.stringify(item.sku_specifications) : '标准规格',
            product_image: item.product_image,
            unit_price: parseFloat(item.unit_price || 0),
            quantity: parseInt(item.quantity || 0),
            subtotal_amount: parseFloat(item.total_price || 0)
          }))
        };
      });

      return {
        orders: processedOrders,
        total: total
      };



    } catch (error) {
      console.error('查询有权限的订单列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrderDetail(req, res) {
    try {
      // 获取当前用户ID
      const userId = req.user?.id;
      if (!userId) {
        return this.fail(res, '用户未登录', 401);
      }

      const { id } = req.params;
      if (!id) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      let orderId;
      try {
        orderId = BigInt(id);
      } catch (error) {
        return this.fail(res, '订单ID格式无效', 400);
      }



      // 检查用户是否有权限查看该订单
      const permissionQuery = `
        SELECT COUNT(*) as count
        FROM "provider"."order_assignment" oa
        WHERE oa.order_id = $1
        AND oa.salesman_id = $2::bigint
        AND oa.deleted_at IS NULL
      `;

      const permissionResult = await this.prisma.$queryRawUnsafe(permissionQuery, orderId, userId);
      const hasPermission = parseInt(permissionResult[0]?.count || 0) > 0;

      if (!hasPermission) {
        // 先检查订单是否存在
        const orderExists = await this.prisma.orders.findUnique({
          where: { id: orderId },
          select: { id: true }
        });

        if (!orderExists) {
          return this.fail(res, '订单不存在', 404);
        } else {
          return this.fail(res, '无权限查看该订单', 403);
        }
      }

      // 查询订单详情
      const orderQuery = `
        SELECT
          o.*,
          osi.recipient_name,
          osi.recipient_phone,
          osi.street_address as recipient_address,
          osi.region_province_id as recipient_province,
          osi.region_city_id as recipient_city,
          osi.region_district_id as recipient_district,
          c.name as channel_name,
          c.icon_url as channel_icon_url,
          oa.rate,
          oa.assignment_amount,
          su.nickname as salesman_name
        FROM "base"."orders" o
        LEFT JOIN "base"."order_shipping_info" osi ON o.id = osi.order_id
        LEFT JOIN "base"."channel" c ON o.channel_id = c.id
        LEFT JOIN "provider"."order_assignment" oa ON o.id = oa.order_id AND oa.salesman_id = $2::bigint
        LEFT JOIN "base"."system_user" su ON oa.salesman_id = su.id
        WHERE o.id = $1 AND o.deleted_at IS NULL
      `;

      const orderResult = await this.prisma.$queryRawUnsafe(orderQuery, orderId, userId);
      
      if (!orderResult || orderResult.length === 0) {
        return this.fail(res, '订单不存在', 404);
      }

      const order = orderResult[0];

      // 查询订单项
      const orderItemsQuery = `
        SELECT *
        FROM "base"."order_items"
        WHERE order_id = $1
        ORDER BY created_at ASC
      `;

      const orderItems = await this.prisma.$queryRawUnsafe(orderItemsQuery, orderId);

      // 组装返回数据
      const orderDetail = {
        ...order,
        id: order.id.toString(),
        total_amount: parseFloat(order.total_amount || 0),
        paid_amount: parseFloat(order.paid_amount || 0),
        assignment_amount: parseFloat(order.assignment_amount || 0),
        rate: parseFloat(order.rate || 0),
        created_at: order.created_at ? order.created_at.toString() : null,
        updated_at: order.updated_at ? order.updated_at.toString() : null,
        products: orderItems.map(item => ({
          product_name: item.product_name,
          product_spec: item.sku_specifications ? JSON.stringify(item.sku_specifications) : '标准规格',
          product_image: item.product_image,
          unit_price: parseFloat(item.unit_price || 0),
          quantity: parseInt(item.quantity || 0),
          subtotal_amount: parseFloat(item.total_price || 0)
        }))
      };

      this.success(res, orderDetail, '获取订单详情成功', 200);

    } catch (error) {
      console.error('获取订单详情失败:', error);
      this.fail(res, `获取订单详情失败: ${error.message}`, null, 500);
    }
  }
}

module.exports = CooperativeOrderController;
