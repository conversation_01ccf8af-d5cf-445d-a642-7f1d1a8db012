/**
 * 第三方商品属性参数项控制器
 * 处理第三方平台商品属性参数项相关的HTTP请求
 */
const ThirdPartyGoodsAttributeItemService = require('../../services/third-party-goods/ThirdPartyGoodsAttributeItemService');

class ThirdPartyGoodsAttributeItemController {
  constructor() {
    this.service = new ThirdPartyGoodsAttributeItemService();
  }

  /**
   * 创建第三方商品属性参数项
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createThirdPartyAttributeItem(req, res) {
    try {
      const attributeItemData = req.body;
      const userId = req.user?.id || BigInt(1); // 默认用户ID

      const result = await this.service.createThirdPartyAttributeItem(attributeItemData, userId);

      return res.status(200).json(result);
    } catch (error) {
      console.error('创建第三方商品属性参数项控制器错误:', error);
      return res.status(500).json({
        success: false,
        code: 500,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 更新第三方商品属性参数项
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateThirdPartyAttributeItem(req, res) {
    try {
      const { id } = req.params;
      const attributeItemData = req.body;
      const userId = req.user?.id || BigInt(1); // 默认用户ID

      const result = await this.service.updateThirdPartyAttributeItem(id, attributeItemData, userId);

      return res.status(200).json(result);
    } catch (error) {
      console.error('更新第三方商品属性参数项控制器错误:', error);
      return res.status(500).json({
        success: false,
        code: 500,
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = ThirdPartyGoodsAttributeItemController;
