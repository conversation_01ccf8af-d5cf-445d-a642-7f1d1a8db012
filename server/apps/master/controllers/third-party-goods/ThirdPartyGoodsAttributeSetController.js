/**
 * 第三方商品属性集控制器
 * 处理第三方平台商品属性集相关的HTTP请求
 */
const ThirdPartyGoodsAttributeSetService = require('../../services/third-party-goods/ThirdPartyGoodsAttributeSetService');

class ThirdPartyGoodsAttributeSetController {
  constructor() {
    this.service = new ThirdPartyGoodsAttributeSetService();
  }

  /**
   * 创建第三方商品属性集
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createThirdPartyAttributeSet(req, res) {
    try {
      const attributeSetData = req.body;
      const userId = req.user?.id || BigInt(1); // 默认用户ID

      const result = await this.service.createThirdPartyAttributeSet(attributeSetData, userId);

      return res.status(200).json(result);
    } catch (error) {
      console.error('创建第三方商品属性集控制器错误:', error);
      return res.status(500).json({
        success: false,
        code: 500,
        message: '服务器内部错误'
      });
    }
  }

  /**
   * 更新第三方商品属性集
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateThirdPartyAttributeSet(req, res) {
    try {
      const { id } = req.params;
      const attributeSetData = req.body;
      const userId = req.user?.id || BigInt(1); // 默认用户ID

      const result = await this.service.updateThirdPartyAttributeSet(id, attributeSetData, userId);

      return res.status(200).json(result);
    } catch (error) {
      console.error('更新第三方商品属性集控制器错误:', error);
      return res.status(500).json({
        success: false,
        code: 500,
        message: '服务器内部错误'
      });
    }
  }
}

module.exports = ThirdPartyGoodsAttributeSetController;
