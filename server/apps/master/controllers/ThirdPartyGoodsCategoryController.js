/**
 * 第三方商品分类控制器
 * 处理第三方平台分类管理的HTTP请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const ThirdPartyGoodsCategoryService = require('../services/ThirdPartyGoodsCategoryService');
const { ThirdPartyGoodsCategoryDto } = require('../dto/ThirdPartyGoodsCategoryDto');

class ThirdPartyGoodsCategoryController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
    this.thirdPartyGoodsCategoryService = new ThirdPartyGoodsCategoryService(prisma);
  }

  /**
   * 创建第三方商品分类
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createThirdPartyCategory(req, res) {
    try {
      console.log('接收到创建第三方商品分类请求:', {
        body: req.body,
        source: 'third-party-api'
      });

      // 1. 数据验证
      const { error, value } = ThirdPartyGoodsCategoryDto.validateCreate(req.body);
      if (error) {
        console.error('第三方商品分类数据验证失败:', JSON.stringify(error, null, 2));
        return this.fail(res, `请求数据验证失败: ${error.details.map(d => d.message).join(', ')}`, 400);
      }

      // 2. 第三方接口使用系统默认用户ID (设为1)
      const userId = BigInt(1);

      // 3. 调用服务创建分类
      const result = await this.thirdPartyGoodsCategoryService.createThirdPartyCategory(value, userId);

      // 4. 返回响应
      if (result.success) {
        this.success(res, result.data, result.message, 200);
      } else {
        // 根据错误类型返回不同的状态码
        let statusCode = 500;
        if (result.message.includes('不存在') || result.message.includes('不属于')) {
          statusCode = 404;
        } else if (result.message.includes('已存在') || result.message.includes('重复') || 
                   result.message.includes('验证失败') || result.message.includes('无效')) {
          statusCode = 400;
        }
        this.fail(res, result.message, statusCode);
      }
    } catch (error) {
      console.error('创建第三方商品分类异常:', error);
      this.fail(res, '创建第三方商品分类失败', 500);
    }
  }

  /**
   * 更新第三方商品分类
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateThirdPartyCategory(req, res) {
    try {
      const categoryId = req.params.id;
      
      console.log('接收到更新第三方商品分类请求:', {
        categoryId,
        body: req.body,
        source: 'third-party-api'
      });

      // 1. 验证分类ID
      if (!categoryId || !/^\d+$/.test(categoryId)) {
        return this.fail(res, '分类ID必须是有效的数字', 400);
      }

      // 2. 数据验证
      const { error, value } = ThirdPartyGoodsCategoryDto.validateUpdate(req.body);
      if (error) {
        console.error('第三方商品分类更新数据验证失败:', JSON.stringify(error, null, 2));
        return this.fail(res, `请求数据验证失败: ${error.details.map(d => d.message).join(', ')}`, 400);
      }

      // 3. 第三方接口使用系统默认用户ID (设为1)
      const userId = BigInt(1);

      // 4. 调用服务更新分类
      const result = await this.thirdPartyGoodsCategoryService.updateThirdPartyCategory(categoryId, value, userId);

      // 5. 返回响应
      if (result.success) {
        this.success(res, result.data, result.message, 200);
      } else {
        // 根据错误类型返回不同的状态码
        let statusCode = 500;
        if (result.message.includes('不存在') || result.message.includes('不属于')) {
          statusCode = 404;
        } else if (result.message.includes('已存在') || result.message.includes('重复') || 
                   result.message.includes('验证失败') || result.message.includes('无效')) {
          statusCode = 400;
        }
        this.fail(res, result.message, statusCode);
      }
    } catch (error) {
      console.error('更新第三方商品分类异常:', error);
      this.fail(res, '更新第三方商品分类失败', 500);
    }
  }
}

module.exports = ThirdPartyGoodsCategoryController;
