/**
 * 第三方商品SPU控制器
 * 处理第三方平台商品创建和多平台关联的HTTP请求
 */
const BaseController = require('../../../core/controllers/BaseController');
const ThirdPartyGoodsSpuService = require('../services/ThirdPartyGoodsSpuService');
const { ThirdPartyGoodsSpuDto } = require('../dto/ThirdPartyGoodsSpuDto');

class ThirdPartyGoodsSpuController extends BaseController {
  /**
   * 构造函数
   * @param {Object} prisma Prisma客户端实例
   */
  constructor(prisma) {
    super(prisma);
    this.thirdPartyGoodsSpuService = new ThirdPartyGoodsSpuService(prisma);
  }

  /**
   * 创建第三方商品
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createThirdPartyProduct(req, res) {
    try {
      console.log('接收到创建第三方商品请求:', {
        body: req.body,
        userId: req.user?.id
      });

      // 1. 数据验证
      const { error, value } = ThirdPartyGoodsSpuDto.validateCreate(req.body);
      if (error) {
        console.error('第三方商品数据验证失败:', JSON.stringify(error, null, 2));
        return this.fail(res, `请求数据验证失败: ${error.details.map(d => d.message).join(', ')}`, 400);
      }

      // 2. 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户身份验证失败', 401);
      }

      // 3. 调用服务创建商品
      const result = await this.thirdPartyGoodsSpuService.createThirdPartyProduct(value, userId);

      // 4. 返回响应
      if (result.success) {
        this.success(res, result.data, result.message, 200);
      } else {
        // 根据错误类型返回不同的状态码
        let statusCode = 500;
        if (result.message.includes('不存在') || result.message.includes('不属于')) {
          statusCode = 400;
        } else if (result.message.includes('已存在')) {
          statusCode = 400;
        }
        
        this.fail(res, result.message, statusCode);
      }
    } catch (error) {
      console.error('创建第三方商品异常:', error);
      
      // 处理特定错误类型
      if (error.message.includes('SKU编码') && error.message.includes('已存在')) {
        return this.fail(res, error.message, 400);
      }
      
      // 返回通用错误响应
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取第三方商品列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getThirdPartyProductList(req, res) {
    try {
      console.log('接收到获取第三方商品列表请求:', req.query);

      // 1. 验证查询参数
      const { error, value } = ThirdPartyGoodsSpuDto.validateListQuery(req.query);
      if (error) {
        return this.fail(res, '请求参数验证失败', 400);
      }

      // 2. 获取分页参数
      const { page, pageSize, skip, take } = this.getPagination(req.query);

      // 3. 构建查询选项
      const options = {
        ...value,
        skip,
        take,
        page,
        pageSize
      };

      // 4. 调用服务获取商品列表
      const result = await this.thirdPartyGoodsSpuService.getThirdPartyProductList(options);

      // 5. 返回响应
      if (result.success) {
        this.successList(
          res,
          result.data.items,
          result.data.total,
          page,
          pageSize,
          result.message
        );
      } else {
        this.fail(res, result.message, 500);
      }
    } catch (error) {
      console.error('获取第三方商品列表异常:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 获取第三方商品详情
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getThirdPartyProductById(req, res) {
    try {
      const { id } = req.params;
      
      console.log('接收到获取第三方商品详情请求:', { id });

      // 1. 验证ID参数
      if (!id || !/^\d+$/.test(id)) {
        return this.fail(res, '商品ID格式不正确', 400);
      }

      // 2. 调用服务获取商品详情
      const result = await this.thirdPartyGoodsSpuService.getThirdPartyProductById(id);

      // 3. 返回响应
      if (result.success) {
        this.success(res, result.data, result.message, 200);
      } else {
        const statusCode = result.message.includes('不存在') ? 404 : 500;
        this.fail(res, result.message, statusCode);
      }
    } catch (error) {
      console.error('获取第三方商品详情异常:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 更新第三方商品
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateThirdPartyProduct(req, res) {
    try {
      const { id } = req.params;
      const productData = req.body;
      
      console.log('接收到更新第三方商品请求:', { id, body: productData });

      // 1. 验证ID参数
      if (!id || !/^\d+$/.test(id)) {
        return this.fail(res, '商品ID格式不正确', 400);
      }

      // 2. 验证更新数据
      const { error, value } = ThirdPartyGoodsSpuDto.validateUpdate(productData);
      if (error) {
        console.error('第三方商品更新数据验证失败:', JSON.stringify(error, null, 2));
        return this.fail(res, `请求数据验证失败: ${error.details.map(d => d.message).join(', ')}`, 400);
      }

      // 3. 检查是否有数据需要更新
      if (Object.keys(value).length === 0) {
        return this.fail(res, '没有提供需要更新的数据', 400);
      }

      // 4. 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户身份验证失败', 401);
      }

      // TODO: 实现更新逻辑
      // 目前暂时返回未实现的响应
      this.fail(res, '更新功能暂未实现', 501);
    } catch (error) {
      console.error('更新第三方商品异常:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 删除第三方商品（软删除）
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async deleteThirdPartyProduct(req, res) {
    try {
      const { id } = req.params;
      
      console.log('接收到删除第三方商品请求:', { id });

      // 1. 验证ID参数
      if (!id || !/^\d+$/.test(id)) {
        return this.fail(res, '商品ID格式不正确', 400);
      }

      // 2. 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户身份验证失败', 401);
      }

      // TODO: 实现删除逻辑
      // 目前暂时返回未实现的响应
      this.fail(res, '删除功能暂未实现', 501);
    } catch (error) {
      console.error('删除第三方商品异常:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }

  /**
   * 批量创建第三方商品
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async batchCreateThirdPartyProducts(req, res) {
    try {
      const { products } = req.body;
      
      console.log('接收到批量创建第三方商品请求:', { 
        productCount: products?.length || 0 
      });

      // 1. 验证批量数据
      if (!Array.isArray(products) || products.length === 0) {
        return this.fail(res, '请提供有效的商品数组', 400);
      }

      if (products.length > 50) {
        return this.fail(res, '单次最多只能创建50个商品', 400);
      }

      // 2. 获取当前用户ID
      const userId = req.user?.id ? BigInt(req.user.id) : null;
      if (!userId) {
        return this.fail(res, '用户身份验证失败', 401);
      }

      // TODO: 实现批量创建逻辑
      // 目前暂时返回未实现的响应
      this.fail(res, '批量创建功能暂未实现', 501);
    } catch (error) {
      console.error('批量创建第三方商品异常:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, code);
    }
  }
}

module.exports = ThirdPartyGoodsSpuController;
