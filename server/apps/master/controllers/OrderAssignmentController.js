/**
 * 订单指派控制器
 * 处理订单指派相关的业务逻辑
 */
const BaseController = require('../../../core/controllers/BaseController');
const { generateSnowflakeId } = require('../../../shared/utils/snowflake');

class OrderAssignmentController extends BaseController {
    constructor(prisma) {
        super();
        this.prisma = prisma;
        console.log('OrderAssignmentController initialized with prisma:', !!prisma);
    }

    /**
     * 生成雪花算法ID
     * @returns {BigInt} 生成的ID
     */
    generateId() {
        const timestamp = BigInt(Date.now());
        const machineId = BigInt(1);
        const sequence = BigInt(Math.floor(Math.random() * 4096));

        return (timestamp << BigInt(22)) | (machineId << BigInt(12)) | sequence;
    }

    /**
     * 创建订单指派
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async createAssignment(req, res) {
        try {
            const {
                order_id,
                order_report_id,
                provider_id,
                salesman_id,
                rate,
                assignment_amount,
                remark
            } = req.body;

            // 验证必填参数
            if (!order_id || !order_report_id || !provider_id || !salesman_id || !rate) {
                return this.fail(res, '订单ID、报备ID、服务商ID、业务员ID和费率不能为空', 400);
            }

            // 获取当前用户ID
            const userId = req.user?.id ? BigInt(req.user.id) : null;
            if (!userId) {
                return this.fail(res, '用户未登录', 401);
            }

            // 转换ID为BigInt
            const orderIdBigInt = BigInt(order_id);
            const orderReportIdBigInt = BigInt(order_report_id);
            const providerIdBigInt = BigInt(provider_id);
            const salesmanIdBigInt = BigInt(salesman_id);
            const currentTime = BigInt(Date.now());

            // 检查订单是否存在
            const orderExists = await this.prisma.$queryRaw`
                SELECT id FROM "base"."orders" 
                WHERE id = ${orderIdBigInt} AND deleted_at IS NULL
            `;

            if (!orderExists || orderExists.length === 0) {
                return this.fail(res, '订单不存在', 404);
            }

            // 检查报备信息是否存在且已通过审核
            const reportExists = await this.prisma.$queryRaw`
                SELECT id, provider_id FROM "provider"."order_report" 
                WHERE id = ${orderReportIdBigInt} 
                AND audit_status = 2 
                AND deleted_at IS NULL
            `;

            if (!reportExists || reportExists.length === 0) {
                return this.fail(res, '报备信息不存在或未通过审核', 404);
            }

            // 验证报备信息的服务商ID与指派的服务商ID是否一致
            if (reportExists[0].provider_id.toString() !== provider_id) {
                return this.fail(res, '报备信息的服务商与指派的服务商不一致', 400);
            }

            // 检查是否已经指派过该订单
            const existingAssignment = await this.prisma.$queryRaw`
                SELECT id FROM "provider"."order_assignment" 
                WHERE order_id = ${orderIdBigInt} AND deleted_at IS NULL
            `;

            if (existingAssignment && existingAssignment.length > 0) {
                return this.fail(res, '该订单已经被指派过了', 400);
            }

            // 创建指派记录
            console.log('准备创建指派记录:', {
                orderIdBigInt,
                orderReportIdBigInt,
                providerIdBigInt,
                salesmanIdBigInt,
                rate: parseFloat(rate),
                assignment_amount: assignment_amount ? parseFloat(assignment_amount) : null
            });

            // 使用数据库序列生成ID
            try {
                // 执行插入，不指定id字段，让数据库使用序列自动生成
                const result = await this.prisma.$queryRaw`
                    INSERT INTO "provider"."order_assignment" (
                        order_id, order_report_id, provider_id, salesman_id,
                        rate, assignment_amount, assignment_status, remark,
                        assigned_by, assigned_at
                    ) VALUES (
                        ${orderIdBigInt}, ${orderReportIdBigInt}, 
                        ${providerIdBigInt}, ${salesmanIdBigInt}, ${parseFloat(rate)}, 
                        ${assignment_amount ? parseFloat(assignment_amount) : null}, 1, ${remark || null},
                        ${userId}, ${currentTime}
                    )
                    RETURNING id
                `;

                console.log('插入结果:', result);

                if (!result || result.length === 0) {
                    throw new Error('插入失败，未返回ID');
                }

                const insertedId = result[0].id;
                console.log('指派创建成功，ID:', insertedId.toString());

                // 使用雪花算法生成ID
                const followerId = generateSnowflakeId();
                console.log('生成的跟单员ID(雪花算法):', followerId.toString());

                // 添加订单跟单员记录，包含id字段
                await this.prisma.$executeRaw`
                    INSERT INTO "base"."order_followers" (id, order_id, follower_id, created_at, updated_at, created_by)
                    VALUES (${followerId}, ${orderIdBigInt}, ${providerIdBigInt}, ${currentTime}, ${currentTime}, ${userId})
                    ON CONFLICT (order_id, follower_id) DO NOTHING
                `;

                return this.success(res, {
                    assignment_id: insertedId.toString(),
                    order_id: order_id,
                    provider_id: provider_id,
                    salesman_id: salesman_id,
                    rate: parseFloat(rate),
                    assignment_status: 1
                }, '订单指派成功');

            } catch (error) {
                console.error('处理指派失败:', error.message);
                return this.fail(res, '订单ID、报备ID或服务商ID无效: ' + error.message, 400);
            }
        } catch (err) {
            console.error('创建订单指派失败:', err.message);
            return this.fail(res, err.message, 500);
        }
    }

    /**
     * 获取订单指派列表
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async getAssignmentList(req, res) {
        try {
            const {
                page = 1,
                pageSize = 10,
                provider_id,
                assignment_status,
                start_date,
                end_date
            } = req.query;

            const offset = BigInt((page - 1) * pageSize);
            const limit = BigInt(pageSize);

            // 构建查询条件
            let whereConditions = ['a."deleted_at" IS NULL'];
            let queryParams = [];

            if (provider_id) {
                whereConditions.push(`a."provider_id" = $${queryParams.length + 1}`);
                queryParams.push(BigInt(provider_id));
            }

            if (assignment_status !== undefined && assignment_status !== '') {
                whereConditions.push(`a."assignment_status" = $${queryParams.length + 1}`);
                queryParams.push(parseInt(assignment_status));
            }

            if (start_date) {
                whereConditions.push(`a."assigned_at" >= $${queryParams.length + 1}`);
                queryParams.push(BigInt(new Date(start_date).getTime()));
            }

            if (end_date) {
                whereConditions.push(`a."assigned_at" <= $${queryParams.length + 1}`);
                queryParams.push(BigInt(new Date(end_date).getTime()));
            }

            const whereClause = whereConditions.join(' AND ');

            // 查询总数
            const countQuery = `
                SELECT COUNT(*) as total
                FROM "provider"."order_assignment" a
                WHERE ${whereClause}
            `;

            const countResult = await this.prisma.$queryRawUnsafe(countQuery, ...queryParams);
            const total = parseInt(countResult[0].total);

            // 查询列表数据
            const listQuery = `
                SELECT
                    a.id,
                    a.order_id,
                    a.order_report_id,
                    a.provider_id,
                    a.salesman_id,
                    a.rate,
                    a.assignment_amount,
                    a.assignment_status,
                    a.remark,
                    a.assigned_by,
                    a.assigned_at,
                    a.accepted_at,
                    a.completed_at,
                    o.total_amount as order_amount,
                    r.customer_name,
                    r.platform_id,
                    c.name as platform_name
                FROM "provider"."order_assignment" a
                LEFT JOIN "base"."orders" o ON a.order_id = o.id
                LEFT JOIN "provider"."order_report" r ON a.order_report_id = r.id
                LEFT JOIN "base"."channel" c ON r.platform_id = c.id::text
                WHERE ${whereClause}
                ORDER BY a.assigned_at DESC
                LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
            `;

            queryParams.push(limit, offset);
            const assignments = await this.prisma.$queryRawUnsafe(listQuery, ...queryParams);

            // 格式化数据
            const formattedAssignments = assignments.map(assignment => ({
                ...assignment,
                id: assignment.id.toString(),
                order_id: assignment.order_id.toString(),
                order_report_id: assignment.order_report_id.toString(),
                provider_id: assignment.provider_id.toString(),
                salesman_id: assignment.salesman_id.toString(),
                assigned_by: assignment.assigned_by.toString(),
                rate: parseFloat(assignment.rate || 0),
                assignment_amount: parseFloat(assignment.assignment_amount || 0),
                order_amount: parseFloat(assignment.order_amount || 0),
                assigned_at: assignment.assigned_at.toString(),
                accepted_at: assignment.accepted_at ? assignment.accepted_at.toString() : null,
                completed_at: assignment.completed_at ? assignment.completed_at.toString() : null
            }));

            return this.successList(res, formattedAssignments, total, parseInt(page), parseInt(pageSize), '获取指派列表成功');

        } catch (error) {
            console.error('获取指派列表失败:', error);
            return this.fail(res, '获取指派列表失败', 500);
        }
    }

    /**
     * 获取指派详情
     * @param {Object} req - 请求对象
     * @param {Object} res - 响应对象
     */
    async getAssignmentDetail(req, res) {
        try {
            const { id } = req.params;

            if (!id) {
                return this.fail(res, '指派ID不能为空', 400);
            }

            try {
                const assignmentIdBigInt = BigInt(id);

                const assignmentResult = await this.prisma.$queryRaw`
                    SELECT
                        a.*,
                        o.total_amount as order_amount,
                        o.order_status,
                        o.payment_status,
                        r.customer_name,
                        r.platform_id,
                        r.report_amount,
                        c.name as platform_name
                    FROM "provider"."order_assignment" a
                    LEFT JOIN "base"."orders" o ON a.order_id = o.id
                    LEFT JOIN "provider"."order_report" r ON a.order_report_id = r.id
                    LEFT JOIN "base"."channel" c ON r.platform_id = c.id::text
                    WHERE a.id = ${assignmentIdBigInt}
                    AND a.deleted_at IS NULL
                `;

                if (!assignmentResult || assignmentResult.length === 0) {
                    return this.fail(res, '指派记录不存在', 404);
                }

                const assignment = assignmentResult[0];

                // 格式化数据
                const formattedAssignment = {
                    ...assignment,
                    id: assignment.id.toString(),
                    order_id: assignment.order_id.toString(),
                    order_report_id: assignment.order_report_id.toString(),
                    provider_id: assignment.provider_id.toString(),
                    salesman_id: assignment.salesman_id.toString(),
                    assigned_by: assignment.assigned_by.toString(),
                    rate: parseFloat(assignment.rate || 0),
                    assignment_amount: parseFloat(assignment.assignment_amount || 0),
                    order_amount: parseFloat(assignment.order_amount || 0),
                    report_amount: parseFloat(assignment.report_amount || 0),
                    assigned_at: assignment.assigned_at.toString(),
                    accepted_at: assignment.accepted_at ? assignment.accepted_at.toString() : null,
                    completed_at: assignment.completed_at ? assignment.completed_at.toString() : null,
                    created_at: assignment.created_at.toString(),
                    updated_at: assignment.updated_at.toString()
                };

                return this.success(res, formattedAssignment, '获取指派详情成功');

            } catch (err) {
                console.error('处理指派ID失败:', err.message);
                return this.fail(res, '指派ID无效: ' + err.message, 400);
            }
        } catch (err) {
            console.error('获取指派详情失败:', err.message);
            return this.fail(res, err.message, 500);
        }
    }
}

module.exports = OrderAssignmentController;
