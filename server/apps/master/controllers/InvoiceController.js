/**
 * 发票管理控制器
 */
const BaseController = require('../../../core/controllers/BaseController');
const InvoiceService = require('../services/InvoiceService');

class InvoiceController extends BaseController {
  constructor(prisma) {
    super(prisma);
    this.invoiceService = new InvoiceService();
  }

  /**
   * 根据订单ID获取发票信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getInvoiceByOrderId(req, res) {
    try {
      const { orderId } = req.params;
      
      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      const result = await this.invoiceService.getInvoiceByOrderId(orderId);
      
      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('获取发票信息失败:', error);
      this.fail(res, '获取发票信息失败', 500);
    }
  }

  /**
   * 创建发票记录
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createInvoice(req, res) {
    try {
      const invoiceData = req.body;
      const operatorId = req.user?.id;

      if (!operatorId) {
        return this.fail(res, '用户未登录', 401);
      }

      const result = await this.invoiceService.createInvoice(invoiceData, operatorId);

      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('创建发票记录失败:', error);
      this.fail(res, '创建发票记录失败', 500);
    }
  }

  /**
   * 更新发票信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async updateInvoice(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const operatorId = req.user?.id;

      if (!id) {
        return this.fail(res, '发票ID不能为空', 400);
      }

      if (!operatorId) {
        return this.fail(res, '用户未登录', 401);
      }

      const result = await this.invoiceService.updateInvoice(id, updateData, operatorId);

      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('更新发票信息失败:', error);
      this.fail(res, '更新发票信息失败', 500);
    }
  }

  /**
   * 作废发票
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async voidInvoice(req, res) {
    try {
      const { id } = req.params;
      const { reason } = req.body;
      const operatorId = req.user?.id;

      if (!id) {
        return this.fail(res, '发票ID不能为空', 400);
      }

      if (!operatorId) {
        return this.fail(res, '用户未登录', 401);
      }

      const result = await this.invoiceService.voidInvoice(id, reason, operatorId);

      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('作废发票失败:', error);
      this.fail(res, '作废发票失败', 500);
    }
  }

  /**
   * 获取订单发票信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrderInvoiceInfo(req, res) {
    try {
      const { orderId } = req.params;

      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      const result = await this.invoiceService.checkInvoiceApplication(orderId);

      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('获取订单发票信息失败:', error);
      this.fail(res, '获取订单发票信息失败', 500);
    }
  }

  /**
   * 记录订单发票文件信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async recordInvoiceFiles(req, res) {
    try {
      const { orderId } = req.params;
      const { fileUrls } = req.body;
      const operatorId = req.user?.id;

      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      if (!fileUrls || fileUrls.length === 0) {
        return this.fail(res, '没有上传文件信息', 400);
      }

      if (!operatorId) {
        return this.fail(res, '用户未登录', 401);
      }

      const result = await this.invoiceService.recordInvoiceFiles(orderId, fileUrls, operatorId);

      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('记录发票文件失败:', error);
      this.fail(res, '记录发票文件失败', 500);
    }
  }

  /**
   * 检查订单是否有发票申请
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async checkInvoiceApplication(req, res) {
    try {
      const { orderId } = req.params;

      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      const result = await this.invoiceService.checkInvoiceApplication(orderId);

      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('检查发票申请失败:', error);
      this.fail(res, '检查发票申请失败', 500);
    }
  }

  /**
   * 查看订单的所有发票
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getOrderInvoices(req, res) {
    try {
      const { orderId } = req.params;

      if (!orderId) {
        return this.fail(res, '订单ID不能为空', 400);
      }

      const result = await this.invoiceService.getOrderInvoices(orderId);

      if (result.success) {
        this.success(res, result.data, result.message);
      } else {
        this.fail(res, result.message, 400);
      }
    } catch (error) {
      console.error('查看订单发票失败:', error);
      this.fail(res, '查看订单发票失败', 500);
    }
  }
}

module.exports = InvoiceController;
