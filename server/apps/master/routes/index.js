const express = require('express');
const router = express.Router();
const userRoutes = require('../system/user/routes/UserManagementRoute');
const authRoutes = require('../system/user/routes/AuthRoute');
const deptRoutes = require('../system/user/routes/DeptManagementRoute');
const menuRoutes = require('../system/user/routes/MenuManagementRoute');
const roleRoutes = require('../system/user/routes/RoleManagementRoute');
const authMiddleware = require('../../../core/middleware/AuthMiddleware');
// 配置管理路由已统一到 ConfigureRoute
const configureRoutes = require('../system/configure/routes/ConfigureRoute');
// 商城用户管理路由
const mallUserRoutes = require('../mall/user/routes/MallUserRoute');
// 商城会员等级管理路由
const mallUserLevelRoutes = require('../mall/user-level/routes/MallUserLevelRoute');
// 商城会员标签组管理路由
const mallTagGroupRoutes = require('../mall/user-tag-group/routes/MallTagGroupRoute');
// 商城会员标签管理路由
const mallTagRoutes = require('../mall/user-tag/routes/MallTagRoute');
// 商城新闻分类管理路由
const mallNewsManagementRoutes = require('../mall/news-management/routes/newsManagementRoutes');
// 消息管理路由
const messageManagementRoutes = require('./messageManagementRoutes');

// 集成模块路由
const integrationRoutes = require('../system/integration/routes/IntegrationRoute');
const wechatRoutes = require('../system/integration/routes/WechatRoute');
const aliyunRoutes = require('../system/integration/routes/AliyunRoute');
const express100Routes = require('../system/integration/routes/Express100Route');
const expressTrackRoutes = require('../system/integration/routes/ExpressTrackRoute');
const smsRoutes = require('../system/integration/routes/SMSRoute');
const emailRoutes = require('../system/integration/email/routes/EmailRoute');

// 平台管理模块路由
const channelRoutes = require('../platformManagement/channel/routes/ChannelRoute');
const platformRoutes = require('../platformManagement/platform/routes/PlatformRoute');
const storeRoutes = require('../platformManagement/store/routes/StoreRoute');

// 日志模块路由
const operationLogRoutes = require('../system/log/routes/OperationLogRoute');

// 留言板路由
const messageBoardRoutes = require('./MessageBoardRoute');

// 微信支付路由
const wechatPayRoutes = require('./WechatPayRoute');

// 支付配置路由
const paymentConfigRoutes = require('./PaymentConfigRoute');

// 安全中心路由
const securityCenterRoutes = require('../system/config/securityCenter/routes');



/**
 * @swagger
 * tags:
 *   - name: 
 *     description: 
 *   - name: 
 *     description: 
 *   - name: 
 *     description: 
 */
const goodsCategoryRoutes = require('./GoodsCategoryRoute');
const goodsBrandRoutes = require('./GoodsBrandRoute');
const goodsServiceRoutes = require('./GoodsServiceRoute');
const goodsAttrTemplateRoutes = require('./GoodsAttrTemplateRoute');
const goodsTagRoutes = require('./GoodsTagRoute');
const goodsFreightTemplateRoutes = require('./GoodsFreightTemplateRoute');
const goodsSpuRoutes = require('./GoodsSpuRoute');
const goodsSkuRoutes = require('./GoodsSkuRoute');
const goodsDraftRoutes = require('./GoodsDraftRoute');

const regionRoutes = require('./RegionRoute');
const orderRoutes = require('./OrderRoute');
const orderPackageRoutes = require('./OrderPackageRoute'); // 订单包裹路由
const orderRemarkRoutes = require('./OrderRemarkRoute'); // 订单备注路由
const orderRefundRoutes = require('./OrderRefundRoute'); // 订单退款路由
const orderReportRoutes = require('./OrderReportRoute'); // 报备管理路由
const orderAssignmentRoutes = require('./OrderAssignmentRoute'); // 订单指派路由
const expressCompanyCodeRoutes = require('./ExpressCompanyCodeRoute');
const paymentMethodRoutes = require('./paymentMethodRoutes'); // 支付方式路由
const invoiceRoutes = require('./InvoiceRoute'); // 发票管理路由


// 自主品牌相关路由
const selfOwnedBrandRoutes = require('./SelfOwnedBrandRoute');
const selfBrandEmpowerRoutes = require('./SelfBrandEmpowerRoute');
const selfBrandRuleCodeRoutes = require('./SelfBrandRuleCodeRoute');
const selfProductBrandRoutes = require('./SelfProductBrandRoute');
const selfSourceCodeRoutes = require('./SelfSourceCodeRoute'); // 自主品牌溯源码路由

// 登录路由已移至 AuthRoute.js
// 不再在此处定义，保持路由定义方式一致

// 
router.post('/system/integration/aliyun/sms/verification-code', (req, res) => {
  const AuthController = require('../system/user/controllers/AuthController');
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();
  const controller = new AuthController(prisma);
  controller.getSmsVerificationCode(req, res);
});

// 手机号登录路由已移至 AuthRoute.js
// 不再在此处定义，保持路由定义方式一致

// 
router.post('/system/integration/aliyun/oss/upload', (req, res) => {
  const multer = require('multer');
  const upload = multer({ storage: multer.memoryStorage() });
  
  upload.single('file')(req, res, (err) => {
    if (err) {
      return res.status(400).json({
        code: 400,
        message: '',
        data: null
      });
    }
    
    const AliyunController = require('../system/integration/controllers/AliyunController');
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    const controller = new AliyunController(prisma);
    controller.uploadImage(req, res);
  });
});

// 
router.post('/system/integration/aliyun/sms/send', (req, res) => {
  const AliyunController = require('../system/integration/controllers/AliyunController');
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();
  const controller = new AliyunController(prisma);
  controller.sendSMS(req, res);
});

// 注册路由已移除，因为 AuthController 中没有 register 方法
// 如需添加注册功能，请先在 AuthController 中实现相应方法

//
router.use('/message-board', messageBoardRoutes);

// 微信支付路由（公开访问，不需要认证）
router.use('/wechat-pay', wechatPayRoutes);

// 支付配置路由（需要认证，放在 authMiddleware 之后）

// 第三方平台路由已移除
// router.use('/third-party-platform', thirdPartyPlatformRoutes);

const selfSourceCodeQueryRoutes = require('./SelfSourceCodeQueryRoute');
router.use('/self-source-code-query', selfSourceCodeQueryRoutes); // 自主品牌溯源码查询路由
router.use('/orders', orderRoutes);



router.use('/order-remark', orderRemarkRoutes); // 订单备注路由
router.use('/order-refund', orderRefundRoutes); // 订单退款路由
router.use('/payment-methods', paymentMethodRoutes); // 支付方式路由
router.use('/express-company-code', expressCompanyCodeRoutes); // 快递公司编码路由（公开访问）
router.use('/invoices', invoiceRoutes); // 发票管理路由

// 第三方商品管理路由（无需鉴权）
const thirdPartyGoodsSpuRoutes = require('./ThirdPartyGoodsSpuRoute');
router.use('/third-party/goods-spu', thirdPartyGoodsSpuRoutes);

// 第三方商品分类管理路由（无需鉴权）
const thirdPartyGoodsCategoryRoutes = require('./ThirdPartyGoodsCategoryRoute');
router.use('/third-party/goods-category', thirdPartyGoodsCategoryRoutes);

router.use(authMiddleware);

// 
//
router.use('/goods-spu', goodsSpuRoutes);
router.use('/goods-sku', goodsSkuRoutes);
router.use('/goods-category', goodsCategoryRoutes);
router.use('/goods-brand', goodsBrandRoutes);
router.use('/goods-service', goodsServiceRoutes);
// 商品属性模板路由（使用直接导入方式）
router.use('/goods-attr-template', goodsAttrTemplateRoutes);
router.use('/goods-tag', goodsTagRoutes);
router.use('/goods-freight-template', goodsFreightTemplateRoutes);

// 自主品牌相关路由
router.use('/self-owned-brand', selfOwnedBrandRoutes);
router.use('/self-brand-empower', selfBrandEmpowerRoutes);
router.use('/self-brand-rule-code', selfBrandRuleCodeRoutes);
router.use('/self-product-brand', selfProductBrandRoutes);
router.use('/self-source-code', selfSourceCodeRoutes); // 自主品牌溯源码路由

//
router.use('/system/user', userRoutes);
router.use('/order-package', orderPackageRoutes); // 订单包裹路由
router.use('/order-report', orderReportRoutes); // 报备管理路由
router.use('/order-assignment', orderAssignmentRoutes); // 订单指派路由


// 用户认证路由
router.use('/auth', (req, res, next) => {
  // 将登录和验证码接口排除在鉴权之外
  if (req.path === '/login' || req.path === '/captcha') {
    return next('route');
  }
  next();
}, authRoutes);

// 
router.use('/system/dept', deptRoutes);

// 
router.use('/system/menu', menuRoutes);

// 
router.use('/system/role', roleRoutes);

// 
router.use('/region', regionRoutes);

// 商城用户路由已移至新的注册方式

// 
router.use('/system/integration', integrationRoutes);
router.use('/system/integration/wechat', wechatRoutes);
router.use('/system/integration/aliyun', aliyunRoutes);
router.use('/system/integration/express100', express100Routes);
router.use('/system/integration/express-track', expressTrackRoutes);
router.use('/system/integration/sms', smsRoutes);
router.use('/system/integration/email', emailRoutes);

// 平台管理模块渠道路由
router.use('/platformManagement/channel', channelRoutes);

// 平台管理模块平台路由
router.use('/platformManagement/platform', platformRoutes);

// 平台管理模块店铺路由
router.use('/platformManagement/store', storeRoutes);

// 
router.use('/system/configure', configureRoutes);

// 
router.use('/system/logs', operationLogRoutes);

// 商城用户管理路由
router.use('/mall/user', mallUserRoutes);

// 商城会员等级管理路由
router.use('/mall/user-level', mallUserLevelRoutes);

// 商城会员标签组管理路由
router.use('/mall/user-tag-group', mallTagGroupRoutes);

// 商城会员标签管理路由
router.use('/mall/user-tag', mallTagRoutes);

// 商城轮播图管理路由
const mallBannerRoutes = require('../mall/banner/routes/MallBannerRoute');
router.use('/mall/banner', mallBannerRoutes);

// 商城新闻分类管理路由
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
router.use('/mall/news-categories', mallNewsManagementRoutes.createNewsManagementRoutes(prisma));

// 商城新闻文章管理路由
router.use('/mall/news-articles', mallNewsManagementRoutes.createNewsArticleRoutes(prisma));

// 消息管理路由
router.use('/message-management', messageManagementRoutes);



// 草稿箱路由（需要用户登录）
router.use('/goods-draft', goodsDraftRoutes);

// 支付配置路由（需要认证）
router.use('/payment-config', paymentConfigRoutes);

// 安全中心路由（需要认证）
router.use('/system/config/securityCenter', securityCenterRoutes);

// 服务商订单管理路由（需要认证）
const cooperativeOrderRoutes = require('./CooperativeOrderRoute');
router.use('/cooperative/orders', cooperativeOrderRoutes);

module.exports = router;
