/**
 * 第三方商品属性集路由
 * 定义第三方平台商品属性集相关的API路由
 */
const express = require('express');
const ThirdPartyGoodsAttributeSetController = require('../../controllers/third-party-goods/ThirdPartyGoodsAttributeSetController');

const router = express.Router();
const controller = new ThirdPartyGoodsAttributeSetController();

/**
 * @swagger
 * /api/v1/master/third-party-goods/attribute-sets:
 *   post:
 *     tags:
 *       - 第三方商品属性集
 *     summary: 创建第三方商品属性集
 *     description: 为第三方平台创建商品属性集，支持多渠道、平台、店铺关联
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - channelId
 *               - platformId
 *               - storeId
 *             properties:
 *               name:
 *                 type: string
 *                 description: 属性集名称
 *                 example: "服装属性集"
 *               channelId:
 *                 type: string
 *                 description: 关联渠道ID
 *                 example: "1001"
 *               platformId:
 *                 type: string
 *                 description: 关联平台ID
 *                 example: "2001"
 *               storeId:
 *                 type: string
 *                 description: 关联店铺ID
 *                 example: "3001"
 *               sortOrder:
 *                 type: integer
 *                 description: 排序值
 *                 example: 0
 *               platformAttributeSetId:
 *                 type: string
 *                 description: 平台属性集ID
 *                 example: "platform_attr_set_123"
 *               platformAttributeSetName:
 *                 type: string
 *                 description: 平台属性集名称
 *                 example: "Platform Clothing Attributes"
 *     responses:
 *       200:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "第三方商品属性集创建成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     attributeSetId:
 *                       type: string
 *                       example: "1234567890123456789"
 *                     name:
 *                       type: string
 *                       example: "服装属性集"
 *                     sortOrder:
 *                       type: integer
 *                       example: 0
 *                     channelId:
 *                       type: string
 *                       example: "1001"
 *                     platformId:
 *                       type: string
 *                       example: "2001"
 *                     storeId:
 *                       type: string
 *                       example: "3001"
 *                     platformAttributeSetId:
 *                       type: string
 *                       example: "platform_attr_set_123"
 *                     platformAttributeSetName:
 *                       type: string
 *                       example: "Platform Clothing Attributes"
 *                     createdAt:
 *                       type: string
 *                       example: "1703123456789"
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: "数据验证失败"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                         example: "name"
 *                       message:
 *                         type: string
 *                         example: "属性集名称为必填项"
 */
router.post('/', controller.createThirdPartyAttributeSet.bind(controller));

/**
 * @swagger
 * /api/v1/master/third-party-goods/attribute-sets/{id}:
 *   put:
 *     tags:
 *       - 第三方商品属性集
 *     summary: 更新第三方商品属性集
 *     description: 更新第三方平台商品属性集信息
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 属性集ID
 *         example: "1234567890123456789"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 属性集名称
 *                 example: "更新后的服装属性集"
 *               channelId:
 *                 type: string
 *                 description: 关联渠道ID
 *                 example: "1002"
 *               platformId:
 *                 type: string
 *                 description: 关联平台ID
 *                 example: "2002"
 *               storeId:
 *                 type: string
 *                 description: 关联店铺ID
 *                 example: "3002"
 *               sortOrder:
 *                 type: integer
 *                 description: 排序值
 *                 example: 10
 *               platformAttributeSetId:
 *                 type: string
 *                 description: 平台属性集ID
 *                 example: "updated_platform_attr_set_123"
 *               platformAttributeSetName:
 *                 type: string
 *                 description: 平台属性集名称
 *                 example: "Updated Platform Clothing Attributes"
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "第三方商品属性集更新成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     attributeSetId:
 *                       type: string
 *                       example: "1234567890123456789"
 *                     name:
 *                       type: string
 *                       example: "更新后的服装属性集"
 *                     sortOrder:
 *                       type: integer
 *                       example: 10
 *                     channelId:
 *                       type: string
 *                       example: "1002"
 *                     platformId:
 *                       type: string
 *                       example: "2002"
 *                     storeId:
 *                       type: string
 *                       example: "3002"
 *                     platformAttributeSetId:
 *                       type: string
 *                       example: "updated_platform_attr_set_123"
 *                     platformAttributeSetName:
 *                       type: string
 *                       example: "Updated Platform Clothing Attributes"
 *                     updatedAt:
 *                       type: string
 *                       example: "1703123456789"
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 属性集不存在
 */
router.put('/:id', controller.updateThirdPartyAttributeSet.bind(controller));

module.exports = router;
