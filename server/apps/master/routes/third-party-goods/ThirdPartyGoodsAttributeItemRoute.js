/**
 * 第三方商品属性参数项路由
 * 定义第三方平台商品属性参数项相关的API路由
 */
const express = require('express');
const router = express.Router();
const ThirdPartyGoodsAttributeItemController = require('../../controllers/third-party-goods/ThirdPartyGoodsAttributeItemController');

const controller = new ThirdPartyGoodsAttributeItemController();

/**
 * @swagger
 * components:
 *   schemas:
 *     ThirdPartyGoodsAttributeItemCreateRequest:
 *       type: object
 *       required:
 *         - name
 *         - goodsAttributeSetId
 *         - channelId
 *         - platformId
 *         - storeId
 *       properties:
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: 属性参数项名称
 *           example: "颜色"
 *         goodsAttributeSetId:
 *           type: string
 *           pattern: '^\\d+$'
 *           description: 属性集ID
 *           example: "1234567890123456789"
 *         channelId:
 *           type: string
 *           pattern: '^\\d+$'
 *           description: 渠道ID
 *           example: "1001"
 *         platformId:
 *           type: string
 *           pattern: '^\\d+$'
 *           description: 平台ID
 *           example: "2001"
 *         storeId:
 *           type: string
 *           pattern: '^\\d+$'
 *           description: 店铺ID
 *           example: "3001"
 *         type:
 *           type: string
 *           maxLength: 50
 *           description: 属性类型
 *           example: "select"
 *           default: "text"
 *         value:
 *           oneOf:
 *             - type: string
 *             - type: number
 *             - type: object
 *             - type: array
 *           description: 属性值（JSON格式）
 *           example: ["红色", "蓝色", "绿色"]
 *         isRequired:
 *           type: integer
 *           enum: [0, 1]
 *           description: 是否必填（0-否，1-是）
 *           example: 1
 *           default: 0
 *         isFilterable:
 *           type: integer
 *           enum: [0, 1]
 *           description: 是否可筛选（0-否，1-是）
 *           example: 1
 *           default: 0
 *         sortOrder:
 *           type: integer
 *           minimum: 0
 *           description: 排序顺序
 *           example: 10
 *           default: 0
 *         isEnabled:
 *           type: integer
 *           enum: [0, 1]
 *           description: 是否启用（0-禁用，1-启用）
 *           example: 1
 *           default: 1
 *         platformAttributeItemId:
 *           type: string
 *           maxLength: 100
 *           description: 平台属性参数项ID
 *           example: "platform_attr_item_123"
 *         platformAttributeItemName:
 *           type: string
 *           maxLength: 255
 *           description: 平台属性参数项名称
 *           example: "平台颜色属性"
 *
 *     ThirdPartyGoodsAttributeItemUpdateRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           maxLength: 100
 *           description: 属性参数项名称
 *           example: "尺寸"
 *         goodsAttributeSetId:
 *           type: string
 *           pattern: '^\\d+$'
 *           description: 属性集ID
 *           example: "1234567890123456789"
 *         channelId:
 *           type: string
 *           pattern: '^\\d+$'
 *           description: 渠道ID
 *           example: "1001"
 *         platformId:
 *           type: string
 *           pattern: '^\\d+$'
 *           description: 平台ID
 *           example: "2001"
 *         storeId:
 *           type: string
 *           pattern: '^\\d+$'
 *           description: 店铺ID
 *           example: "3001"
 *         type:
 *           type: string
 *           maxLength: 50
 *           description: 属性类型
 *           example: "select"
 *         value:
 *           oneOf:
 *             - type: string
 *             - type: number
 *             - type: object
 *             - type: array
 *           description: 属性值（JSON格式）
 *           example: ["S", "M", "L", "XL"]
 *         isRequired:
 *           type: integer
 *           enum: [0, 1]
 *           description: 是否必填（0-否，1-是）
 *           example: 0
 *         isFilterable:
 *           type: integer
 *           enum: [0, 1]
 *           description: 是否可筛选（0-否，1-是）
 *           example: 1
 *         sortOrder:
 *           type: integer
 *           minimum: 0
 *           description: 排序顺序
 *           example: 20
 *         isEnabled:
 *           type: integer
 *           enum: [0, 1]
 *           description: 是否启用（0-禁用，1-启用）
 *           example: 1
 *         platformAttributeItemId:
 *           type: string
 *           maxLength: 100
 *           description: 平台属性参数项ID
 *           example: "platform_attr_item_456"
 *         platformAttributeItemName:
 *           type: string
 *           maxLength: 255
 *           description: 平台属性参数项名称
 *           example: "平台尺寸属性"
 *
 *     ThirdPartyGoodsAttributeItemResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           description: 操作是否成功
 *           example: true
 *         code:
 *           type: integer
 *           description: 响应代码
 *           example: 200
 *         message:
 *           type: string
 *           description: 响应消息
 *           example: "第三方商品属性参数项创建成功"
 *         data:
 *           type: object
 *           properties:
 *             attributeItemId:
 *               type: string
 *               description: 属性参数项ID
 *               example: "1234567890123456789"
 *             name:
 *               type: string
 *               description: 属性参数项名称
 *               example: "颜色"
 *             goodsAttributeSetId:
 *               type: string
 *               description: 属性集ID
 *               example: "1234567890123456789"
 *             type:
 *               type: string
 *               description: 属性类型
 *               example: "select"
 *             value:
 *               description: 属性值
 *               example: ["红色", "蓝色", "绿色"]
 *             isRequired:
 *               type: integer
 *               description: 是否必填
 *               example: 1
 *             isFilterable:
 *               type: integer
 *               description: 是否可筛选
 *               example: 1
 *             sortOrder:
 *               type: integer
 *               description: 排序顺序
 *               example: 10
 *             isEnabled:
 *               type: integer
 *               description: 是否启用
 *               example: 1
 *             channelId:
 *               type: string
 *               description: 渠道ID
 *               example: "1001"
 *             platformId:
 *               type: string
 *               description: 平台ID
 *               example: "2001"
 *             storeId:
 *               type: string
 *               description: 店铺ID
 *               example: "3001"
 *             platformAttributeItemId:
 *               type: string
 *               description: 平台属性参数项ID
 *               example: "platform_attr_item_123"
 *             platformAttributeItemName:
 *               type: string
 *               description: 平台属性参数项名称
 *               example: "平台颜色属性"
 *             createdAt:
 *               type: string
 *               description: 创建时间戳
 *               example: "1703123456789"
 *             updatedAt:
 *               type: string
 *               description: 更新时间戳
 *               example: "1703123456789"
 *
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         code:
 *           type: integer
 *           example: 400
 *         message:
 *           type: string
 *           example: "数据验证失败"
 *         errors:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               field:
 *                 type: string
 *                 example: "name"
 *               message:
 *                 type: string
 *                 example: "属性参数项名称为必填项"
 */

/**
 * @swagger
 * /api/v1/master/third-party/goods-attribute-item:
 *   post:
 *     tags:
 *       - 第三方商品属性参数项管理
 *     summary: 创建第三方商品属性参数项
 *     description: 为第三方平台创建商品属性参数项，支持多渠道、平台、店铺关联
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ThirdPartyGoodsAttributeItemCreateRequest'
 *           example:
 *             name: "颜色"
 *             goodsAttributeSetId: "1234567890123456789"
 *             channelId: "1001"
 *             platformId: "2001"
 *             storeId: "3001"
 *             type: "select"
 *             value: ["红色", "蓝色", "绿色"]
 *             isRequired: 1
 *             isFilterable: 1
 *             sortOrder: 10
 *             isEnabled: 1
 *             platformAttributeItemId: "platform_attr_item_123"
 *             platformAttributeItemName: "平台颜色属性"
 *     responses:
 *       200:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ThirdPartyGoodsAttributeItemResponse'
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/', controller.createThirdPartyAttributeItem.bind(controller));

/**
 * @swagger
 * /api/v1/master/third-party/goods-attribute-item/{id}:
 *   put:
 *     tags:
 *       - 第三方商品属性参数项管理
 *     summary: 更新第三方商品属性参数项
 *     description: 更新指定的第三方商品属性参数项信息
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^\\d+$'
 *         description: 属性参数项ID
 *         example: "1234567890123456789"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ThirdPartyGoodsAttributeItemUpdateRequest'
 *           example:
 *             name: "尺寸"
 *             type: "select"
 *             value: ["S", "M", "L", "XL"]
 *             isRequired: 0
 *             isFilterable: 1
 *             sortOrder: 20
 *             platformAttributeItemId: "platform_attr_item_456"
 *             platformAttributeItemName: "平台尺寸属性"
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ThirdPartyGoodsAttributeItemResponse'
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put('/:id', controller.updateThirdPartyAttributeItem.bind(controller));

module.exports = router;
