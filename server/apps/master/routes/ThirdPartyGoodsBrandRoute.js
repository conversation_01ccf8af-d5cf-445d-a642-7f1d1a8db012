/**
 * 第三方商品品牌路由
 * 为第三方平台提供简化的品牌管理接口
 */
const express = require('express');
const router = express.Router();
const ThirdPartyGoodsBrandController = require('../controllers/ThirdPartyGoodsBrandController');
const { prisma } = require('../../../core/database/prisma');
const controller = new ThirdPartyGoodsBrandController(prisma);

// 第三方品牌接口不需要鉴权，直接使用基础路由

/**
 * @swagger
 * /api/v1/master/third-party/goods-brand:
 *   post:
 *     summary: 创建第三方商品品牌
 *     description: 为第三方平台创建商品品牌，无需鉴权
 *     tags: [第三方商品品牌管理]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - channelId
 *               - platformId
 *               - storeId
 *             properties:
 *               name:
 *                 type: string
 *                 description: 品牌名称
 *                 maxLength: 100
 *                 example: "测试品牌"
 *               channelId:
 *                 type: string
 *                 description: 关联渠道ID
 *                 pattern: '^\\d+$'
 *                 example: "196281255023742976"
 *               platformId:
 *                 type: string
 *                 description: 关联平台ID
 *                 pattern: '^\\d+$'
 *                 example: "196281256542081024"
 *               storeId:
 *                 type: string
 *                 description: 关联店铺ID
 *                 pattern: '^\\d+$'
 *                 example: "196281256613384192"
 *               logoUrl:
 *                 type: string
 *                 description: 品牌Logo URL
 *                 format: uri
 *                 maxLength: 255
 *                 example: "https://example.com/logo.jpg"
 *               description:
 *                 type: string
 *                 description: 品牌描述
 *                 maxLength: 1000
 *                 example: "这是一个测试品牌"
 *               platformBrandId:
 *                 type: string
 *                 description: 第三方平台品牌ID
 *                 maxLength: 100
 *                 example: "platform_brand_123"
 *               platformBrandName:
 *                 type: string
 *                 description: 第三方平台品牌名称
 *                 maxLength: 255
 *                 example: "Platform Brand Name"
 *           example:
 *             name: "测试第三方品牌"
 *             channelId: "196281255023742976"
 *             platformId: "196281256542081024"
 *             storeId: "196281256613384192"
 *             logoUrl: "https://example.com/brand-logo.jpg"
 *             description: "这是一个测试的第三方商品品牌"
 *             platformBrandId: "third_party_brand_001"
 *             platformBrandName: "Third Party Brand"
 *     responses:
 *       200:
 *         description: 品牌创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "第三方商品品牌创建成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 品牌ID
 *                     name:
 *                       type: string
 *                       description: 品牌名称
 *                     logoUrl:
 *                       type: string
 *                       description: 品牌Logo URL
 *                     description:
 *                       type: string
 *                       description: 品牌描述
 *                     sourceType:
 *                       type: integer
 *                       description: 来源类型
 *                     createdChannelId:
 *                       type: string
 *                       description: 创建渠道ID
 *                     createdPlatformId:
 *                       type: string
 *                       description: 创建平台ID
 *                     createdStoreId:
 *                       type: string
 *                       description: 创建店铺ID
 *                     createdAt:
 *                       type: string
 *                       description: 创建时间戳
 *                     updatedAt:
 *                       type: string
 *                       description: 更新时间戳
 *       400:
 *         description: 数据验证失败
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "请求数据验证失败: 品牌名称不能为空"
 *                 data:
 *                   type: integer
 *                   example: 400
 *       404:
 *         description: 关联的渠道、平台或店铺不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "渠道ID 123 不存在"
 *                 data:
 *                   type: integer
 *                   example: 404
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "创建第三方商品品牌失败"
 *                 data:
 *                   type: integer
 *                   example: 500
 */
router.post('/', (req, res) => controller.createThirdPartyBrand(req, res));

/**
 * @swagger
 * /api/v1/master/third-party/goods-brand/{id}:
 *   put:
 *     summary: 更新第三方商品品牌
 *     description: 更新第三方平台的商品品牌信息，无需鉴权
 *     tags: [第三方商品品牌管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 品牌ID
 *         example: "196284462319931392"
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 品牌名称
 *                 maxLength: 100
 *                 example: "更新后的品牌名称"
 *               logoUrl:
 *                 type: string
 *                 description: 品牌Logo URL
 *                 format: uri
 *                 maxLength: 255
 *                 example: "https://example.com/updated-logo.jpg"
 *               description:
 *                 type: string
 *                 description: 品牌描述
 *                 maxLength: 1000
 *                 example: "更新后的品牌描述"
 *               platformBrandId:
 *                 type: string
 *                 description: 第三方平台品牌ID
 *                 maxLength: 100
 *                 example: "updated_platform_brand_123"
 *               platformBrandName:
 *                 type: string
 *                 description: 第三方平台品牌名称
 *                 maxLength: 255
 *                 example: "Updated Platform Brand Name"
 *           example:
 *             name: "更新后的第三方品牌"
 *             logoUrl: "https://example.com/updated-brand-logo.jpg"
 *             description: "这是更新后的第三方商品品牌描述"
 *             platformBrandId: "updated_third_party_brand_001"
 *             platformBrandName: "Updated Third Party Brand"
 *     responses:
 *       200:
 *         description: 品牌更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "第三方商品品牌更新成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 品牌ID
 *                     name:
 *                       type: string
 *                       description: 品牌名称
 *                     logoUrl:
 *                       type: string
 *                       description: 品牌Logo URL
 *                     description:
 *                       type: string
 *                       description: 品牌描述
 *                     sourceType:
 *                       type: integer
 *                       description: 来源类型
 *                     createdChannelId:
 *                       type: string
 *                       description: 创建渠道ID
 *                     createdPlatformId:
 *                       type: string
 *                       description: 创建平台ID
 *                     createdStoreId:
 *                       type: string
 *                       description: 创建店铺ID
 *                     createdAt:
 *                       type: string
 *                       description: 创建时间戳
 *                     updatedAt:
 *                       type: string
 *                       description: 更新时间戳
 *       400:
 *         description: 数据验证失败
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "请求数据验证失败: 至少需要提供一个要更新的字段"
 *                 data:
 *                   type: integer
 *                   example: 400
 *       404:
 *         description: 品牌不存在或不是第三方品牌
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "品牌不存在或不是第三方品牌"
 *                 data:
 *                   type: integer
 *                   example: 404
 *       500:
 *         description: 服务器内部错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: "更新第三方商品品牌失败"
 *                 data:
 *                   type: integer
 *                   example: 500
 */
router.put('/:id', (req, res) => controller.updateThirdPartyBrand(req, res));

module.exports = router;
