/**
 * Master端订单报备管理路由
 * 处理报备列表、详情、审核等功能的路由配置
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const OrderReportController = require('../controllers/OrderReportController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new OrderReportController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/master/order-report/list:
 *   get:
 *     tags: [Master端报备管理]
 *     summary: 获取报备列表
 *     description: 获取所有报备记录的分页列表，支持多种筛选条件
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: report_sn
 *         schema:
 *           type: string
 *         description: 报备单号（模糊搜索）
 *       - in: query
 *         name: customer_name
 *         schema:
 *           type: string
 *         description: 客户名称（模糊搜索）
 *       - in: query
 *         name: platform_id
 *         schema:
 *           type: string
 *         description: 平台ID
 *       - in: query
 *         name: provider_id
 *         schema:
 *           type: string
 *         description: 服务商ID
 *       - in: query
 *         name: audit_status
 *         schema:
 *           type: integer
 *           enum: [1, 2, 3]
 *         description: 审核状态（1-待审核，2-审核通过，3-审核驳回）
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: integer
 *           format: int64
 *         description: 开始时间（时间戳，毫秒）
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: integer
 *           format: int64
 *         description: 结束时间（时间戳，毫秒）
 *       - in: query
 *         name: customer_region
 *         schema:
 *           type: string
 *         description: 客户区域（模糊搜索）
 *       - in: query
 *         name: receiver_name
 *         schema:
 *           type: string
 *         description: 收货人姓名（模糊搜索）
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取报备列表成功"
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: 报备ID
 *                           report_sn:
 *                             type: string
 *                             description: 报备单号
 *                           customer_name:
 *                             type: string
 *                             description: 客户名称
 *                           customer_region:
 *                             type: string
 *                             description: 客户区域
 *                           platform_id:
 *                             type: string
 *                             description: 平台ID
 *                           platform_name:
 *                             type: string
 *                             description: 平台名称
 *                           report_type:
 *                             type: integer
 *                             description: 报备类型
 *                           report_amount:
 *                             type: number
 *                             description: 报备金额
 *                           recipient_name:
 *                             type: string
 *                             description: 收货人姓名
 *                           contact_phone:
 *                             type: string
 *                             description: 联系电话
 *                           audit_status:
 *                             type: integer
 *                             description: 审核状态
 *                           created_at:
 *                             type: string
 *                             description: 创建时间
 *                     pageInfo:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: 总记录数
 *                         currentPage:
 *                           type: integer
 *                           description: 当前页码
 *                         totalPage:
 *                           type: integer
 *                           description: 总页数
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/list', controller.getReportList.bind(controller));

/**
 * @swagger
 * /api/v1/master/order-report/detail/{id}:
 *   get:
 *     tags: [Master端报备管理]
 *     summary: 获取报备详情
 *     description: 根据报备ID获取报备详细信息，包括商品列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 报备ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取报备详情成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 报备ID
 *                     report_sn:
 *                       type: string
 *                       description: 报备单号
 *                     customer_name:
 *                       type: string
 *                       description: 客户名称
 *                     customer_region:
 *                       type: string
 *                       description: 客户区域
 *                     platform_id:
 *                       type: string
 *                       description: 平台ID
 *                     platform_name:
 *                       type: string
 *                       description: 平台名称
 *                     provider_id:
 *                       type: string
 *                       description: 服务商ID
 *                     provider_company_name:
 *                       type: string
 *                       description: 服务商公司名称
 *                     report_type:
 *                       type: integer
 *                       description: 报备类型
 *                     report_amount:
 *                       type: number
 *                       description: 报备金额
 *                     expected_order_time:
 *                       type: string
 *                       description: 预计下单时间
 *                     related_order_number:
 *                       type: string
 *                       description: 关联订单号
 *                     expected_shipping_time:
 *                       type: string
 *                       description: 预计发货时间
 *                     recipient_name:
 *                       type: string
 *                       description: 收货人姓名
 *                     contact_phone:
 *                       type: string
 *                       description: 联系电话
 *                     shipping_address:
 *                       type: string
 *                       description: 收货地址
 *                     remark:
 *                       type: string
 *                       description: 备注
 *                     audit_status:
 *                       type: integer
 *                       description: 审核状态
 *                     audit_remark:
 *                       type: string
 *                       description: 审核备注
 *                     audit_user_id:
 *                       type: string
 *                       description: 审核人ID
 *                     audit_username:
 *                       type: string
 *                       description: 审核人用户名
 *                     audit_time:
 *                       type: string
 *                       description: 审核时间
 *                     items:
 *                       type: array
 *                       description: 商品列表
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: 商品项ID
 *                           product_name:
 *                             type: string
 *                             description: 商品名称
 *                           specification:
 *                             type: string
 *                             description: 规格
 *                           quantity:
 *                             type: integer
 *                             description: 数量
 *                           unit_price:
 *                             type: number
 *                             description: 单价
 *                           report_price:
 *                             type: number
 *                             description: 报备价
 *                           subtotal:
 *                             type: number
 *                             description: 小计
 *                           product_image:
 *                             type: string
 *                             description: 商品图片
 *       401:
 *         description: 未授权
 *       404:
 *         description: 报备单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/detail/:id', controller.getReportDetail.bind(controller));

/**
 * @swagger
 * /api/v1/master/order-report/audit/{id}:
 *   post:
 *     tags: [Master端报备管理]
 *     summary: 审核报备
 *     description: 对报备进行审核，可以通过或驳回
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 报备ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - audit_status
 *             properties:
 *               audit_status:
 *                 type: integer
 *                 enum: [2, 3]
 *                 description: 审核状态（2-审核通过，3-审核驳回）
 *               audit_remark:
 *                 type: string
 *                 description: 审核备注
 *               audit_user_id:
 *                 type: string
 *                 description: 审核人ID，不传则使用当前登录用户ID
 *     responses:
 *       200:
 *         description: 审核成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "报备审核通过成功"
 *                 data:
 *                   type: object
 *                   nullable: true
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 报备单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/audit/:id', controller.auditReport.bind(controller));

module.exports = router;
