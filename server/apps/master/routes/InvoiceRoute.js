/**
 * 发票管理路由
 */
const express = require('express');
const router = express.Router();
const InvoiceController = require('../controllers/InvoiceController');
const { prisma } = require('../../../core/database/prisma');

// 导入认证中间件
const authMiddleware = require('../../../core/middleware/AuthMiddleware');

// 创建控制器实例
const invoiceController = new InvoiceController(prisma);

/**
 * @route GET /api/v1/master/invoices/:orderId
 * @desc 根据订单ID获取发票信息
 * @access Private
 */
router.get('/:orderId', authMiddleware, (req, res) => {
  invoiceController.getInvoiceByOrderId(req, res);
});

/**
 * @route POST /api/v1/master/invoices
 * @desc 创建发票记录
 * @access Private
 */
router.post('/', authMiddleware, (req, res) => {
  invoiceController.createInvoice(req, res);
});

/**
 * @route PUT /api/v1/master/invoices/:id
 * @desc 更新发票信息
 * @access Private
 */
router.put('/:id', authMiddleware, (req, res) => {
  invoiceController.updateInvoice(req, res);
});

/**
 * @route PUT /api/v1/master/invoices/:id/void
 * @desc 作废发票
 * @access Private
 */
router.put('/:id/void', authMiddleware, (req, res) => {
  invoiceController.voidInvoice(req, res);
});

/**
 * @route POST /api/v1/master/invoices/upload/:orderId
 * @desc 为订单记录发票文件信息
 * @access Private
 */
router.post('/upload/:orderId', authMiddleware, (req, res) => {
  invoiceController.recordInvoiceFiles(req, res);
});

/**
 * @route GET /api/v1/master/invoices/order/:orderId
 * @desc 获取订单发票信息（包含申请和发票记录）
 * @access Private
 */
router.get('/order/:orderId', authMiddleware, (req, res) => {
  invoiceController.getOrderInvoiceInfo(req, res);
});

/**
 * @route GET /api/v1/master/invoices/check-application/:orderId
 * @desc 检查订单是否有发票申请
 * @access Private
 */
router.get('/check-application/:orderId', authMiddleware, (req, res) => {
  invoiceController.checkInvoiceApplication(req, res);
});

/**
 * @route GET /api/v1/master/invoices/view/:orderId
 * @desc 查看订单的所有发票
 * @access Private
 */
router.get('/view/:orderId', authMiddleware, (req, res) => {
  invoiceController.getOrderInvoices(req, res);
});

module.exports = router;
