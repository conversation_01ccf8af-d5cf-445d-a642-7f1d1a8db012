/**
 * 订单指派路由
 * 处理订单指派相关的路由配置
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const OrderAssignmentController = require('../controllers/OrderAssignmentController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new OrderAssignmentController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/master/order-assignment/create:
 *   post:
 *     tags: [Master端订单指派]
 *     summary: 创建订单指派
 *     description: 将订单指派给服务商
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - order_id
 *               - order_report_id
 *               - provider_id
 *               - salesman_id
 *               - rate
 *             properties:
 *               order_id:
 *                 type: string
 *                 description: 订单ID
 *               order_report_id:
 *                 type: string
 *                 description: 报备信息ID
 *               provider_id:
 *                 type: string
 *                 description: 服务商ID
 *               salesman_id:
 *                 type: string
 *                 description: 业务员ID
 *               rate:
 *                 type: number
 *                 description: 费率
 *               assignment_amount:
 *                 type: number
 *                 description: 指派金额
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 指派成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "订单指派成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     assignment_id:
 *                       type: string
 *                       description: 指派记录ID
 *                     order_id:
 *                       type: string
 *                       description: 订单ID
 *                     provider_id:
 *                       type: string
 *                       description: 服务商ID
 *                     salesman_id:
 *                       type: string
 *                       description: 业务员ID
 *                     rate:
 *                       type: number
 *                       description: 费率
 *                     assignment_status:
 *                       type: integer
 *                       description: 指派状态
 *       400:
 *         description: 参数错误
 *       401:
 *         description: 未授权
 *       404:
 *         description: 订单或报备信息不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/create', controller.createAssignment.bind(controller));

/**
 * @swagger
 * /api/v1/master/order-assignment/list:
 *   get:
 *     tags: [Master端订单指派]
 *     summary: 获取订单指派列表
 *     description: 获取订单指派记录的分页列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: provider_id
 *         schema:
 *           type: string
 *         description: 服务商ID
 *       - in: query
 *         name: assignment_status
 *         schema:
 *           type: integer
 *         description: 指派状态
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取指派列表成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: 指派记录ID
 *                           order_id:
 *                             type: string
 *                             description: 订单ID
 *                           order_report_id:
 *                             type: string
 *                             description: 报备信息ID
 *                           provider_id:
 *                             type: string
 *                             description: 服务商ID
 *                           salesman_id:
 *                             type: string
 *                             description: 业务员ID
 *                           rate:
 *                             type: number
 *                             description: 费率
 *                           assignment_amount:
 *                             type: number
 *                             description: 指派金额
 *                           assignment_status:
 *                             type: integer
 *                             description: 指派状态
 *                           customer_name:
 *                             type: string
 *                             description: 客户名称
 *                           platform_name:
 *                             type: string
 *                             description: 平台名称
 *                           assigned_at:
 *                             type: string
 *                             description: 指派时间
 *                     pageInfo:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: 总记录数
 *                         currentPage:
 *                           type: integer
 *                           description: 当前页码
 *                         totalPage:
 *                           type: integer
 *                           description: 总页数
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/list', controller.getAssignmentList.bind(controller));

/**
 * @swagger
 * /api/v1/master/order-assignment/detail/{id}:
 *   get:
 *     tags: [Master端订单指派]
 *     summary: 获取订单指派详情
 *     description: 根据ID获取订单指派的详细信息
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 指派记录ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "获取指派详情成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 指派记录ID
 *                     order_id:
 *                       type: string
 *                       description: 订单ID
 *                     order_report_id:
 *                       type: string
 *                       description: 报备信息ID
 *                     provider_id:
 *                       type: string
 *                       description: 服务商ID
 *                     salesman_id:
 *                       type: string
 *                       description: 业务员ID
 *                     rate:
 *                       type: number
 *                       description: 费率
 *                     assignment_amount:
 *                       type: number
 *                       description: 指派金额
 *                     assignment_status:
 *                       type: integer
 *                       description: 指派状态
 *                     customer_name:
 *                       type: string
 *                       description: 客户名称
 *                     platform_name:
 *                       type: string
 *                       description: 平台名称
 *                     order_amount:
 *                       type: number
 *                       description: 订单金额
 *                     report_amount:
 *                       type: number
 *                       description: 报备金额
 *                     assigned_at:
 *                       type: string
 *                       description: 指派时间
 *                     accepted_at:
 *                       type: string
 *                       description: 接受时间
 *                     completed_at:
 *                       type: string
 *                       description: 完成时间
 *       401:
 *         description: 未授权
 *       404:
 *         description: 指派记录不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/detail/:id', controller.getAssignmentDetail.bind(controller));

module.exports = router;
