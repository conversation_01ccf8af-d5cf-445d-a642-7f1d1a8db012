/**
 * 第三方商品SPU路由
 * 为第三方平台提供简化的商品管理接口，支持多平台关联
 */
const express = require('express');
const router = express.Router();
const ThirdPartyGoodsSpuController = require('../controllers/ThirdPartyGoodsSpuController');
const { prisma } = require('../../../core/database/prisma');
const RouterConfig = require('../../../core/routes/RouterConfig');
const controller = new ThirdPartyGoodsSpuController(prisma);

// 第三方商品接口不需要鉴权，直接使用基础路由
// const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/master/third-party/goods-spu:
 *   post:
 *     summary: 创建第三方商品
 *     description: 为第三方平台创建商品，只需要必填字段，支持多SKU和多平台关联，无需鉴权
 *     tags: [第三方商品管理]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - channelId
 *               - platformId
 *               - storeId
 *               - skus
 *             properties:
 *               name:
 *                 type: string
 *                 description: 商品名称
 *                 maxLength: 255
 *                 example: "第三方商品名称"
 *               channelId:
 *                 type: string
 *                 description: 渠道ID
 *                 pattern: '^\\d+$'
 *                 example: "123456789"
 *               platformId:
 *                 type: string
 *                 description: 平台ID
 *                 pattern: '^\\d+$'
 *                 example: "987654321"
 *               storeId:
 *                 type: string
 *                 description: 店铺ID
 *                 pattern: '^\\d+$'
 *                 example: "456789123"
 *               skus:
 *                 type: array
 *                 description: SKU数组，至少包含1个SKU
 *                 minItems: 1
 *                 maxItems: 100
 *                 items:
 *                   type: object
 *                   required:
 *                     - skuCode
 *                     - salesPrice
 *                     - stock
 *                   properties:
 *                     skuCode:
 *                       type: string
 *                       description: SKU编码，只能包含字母、数字、下划线和横线
 *                       maxLength: 100
 *                       pattern: '^[A-Za-z0-9_-]+$'
 *                       example: "SKU001"
 *                     salesPrice:
 *                       type: number
 *                       description: 销售价格
 *                       minimum: 0.01
 *                       maximum: 999999.99
 *                       example: 99.99
 *                     stock:
 *                       type: integer
 *                       description: 库存数量
 *                       minimum: 0
 *                       maximum: 999999
 *                       example: 100
 *               subtitle:
 *                 type: string
 *                 description: 商品副标题（可选）
 *                 maxLength: 500
 *                 example: "商品副标题"
 *               description:
 *                 type: string
 *                 description: 商品描述（可选）
 *                 maxLength: 5000
 *                 example: "商品详细描述"
 *           examples:
 *             single_sku:
 *               summary: 单SKU商品
 *               value:
 *                 name: "第三方单规格商品"
 *                 channelId: "123456789"
 *                 platformId: "987654321"
 *                 storeId: "456789123"
 *                 skus:
 *                   - skuCode: "SKU001"
 *                     salesPrice: 99.99
 *                     stock: 100
 *             multi_sku:
 *               summary: 多SKU商品
 *               value:
 *                 name: "第三方多规格商品"
 *                 channelId: "123456789"
 *                 platformId: "987654321"
 *                 storeId: "456789123"
 *                 subtitle: "多种规格可选"
 *                 skus:
 *                   - skuCode: "SKU001"
 *                     salesPrice: 99.99
 *                     stock: 100
 *                   - skuCode: "SKU002"
 *                     salesPrice: 89.99
 *                     stock: 50
 *     responses:
 *       200:
 *         description: 商品创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "第三方商品创建成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 商品ID
 *                       example: "1234567890123456789"
 *                     spuCode:
 *                       type: string
 *                       description: 商品SPU编码
 *                       example: "TP_SPU_20241225_001"
 *                     name:
 *                       type: string
 *                       description: 商品名称
 *                       example: "第三方商品名称"
 *                     skuCount:
 *                       type: integer
 *                       description: SKU数量
 *                       example: 2
 *                     totalStock:
 *                       type: integer
 *                       description: 总库存
 *                       example: 150
 *                     createdAt:
 *                       type: integer
 *                       description: 创建时间戳（毫秒）
 *                       example: 1703472000000
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "SKU编码SKU001已存在"
 *                 data:
 *                   type: null
 *                   example: null
 *       500:
 *         description: 服务器内部错误
 */
router.post('/', (req, res) => controller.createThirdPartyProduct(req, res));

/**
 * @swagger
 * /api/v1/master/third-party/goods-spu/{id}:
 *   put:
 *     summary: 更新第三方商品
 *     description: 更新第三方平台商品信息，支持更新商品基本信息和SKU信息，无需鉴权
 *     tags: [第三方商品管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^\\d+$'
 *         description: 商品SPU ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 商品名称（可选）
 *                 maxLength: 255
 *                 example: "更新后的商品名称"
 *               subtitle:
 *                 type: string
 *                 description: 商品副标题（可选）
 *                 maxLength: 500
 *                 example: "更新后的副标题"
 *               description:
 *                 type: string
 *                 description: 商品描述（可选）
 *                 maxLength: 5000
 *                 example: "更新后的商品描述"
 *               skus:
 *                 type: array
 *                 description: SKU列表（可选，如果提供则会更新SKU信息）
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: SKU ID（更新现有SKU时必填）
 *                       example: "123456789"
 *                     skuCode:
 *                       type: string
 *                       description: SKU编码
 *                       maxLength: 100
 *                       example: "SKU001-UPDATED"
 *                     salesPrice:
 *                       type: number
 *                       description: 销售价格
 *                       minimum: 0
 *                       example: 109.99
 *                     stock:
 *                       type: integer
 *                       description: 库存数量
 *                       minimum: 0
 *                       example: 150
 *           examples:
 *             update_basic_info:
 *               summary: 更新基本信息
 *               value:
 *                 name: "更新后的商品名称"
 *                 subtitle: "更新后的副标题"
 *                 description: "更新后的商品描述"
 *             update_with_skus:
 *               summary: 更新商品和SKU信息
 *               value:
 *                 name: "更新后的商品名称"
 *                 skus:
 *                   - id: "123456789"
 *                     skuCode: "SKU001-UPDATED"
 *                     salesPrice: 109.99
 *                     stock: 150
 *     responses:
 *       200:
 *         description: 更新商品成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "更新第三方商品成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "1234567890123456789"
 *                     spuCode:
 *                       type: string
 *                       example: "TP_SPU_20241225_001"
 *                     name:
 *                       type: string
 *                       example: "更新后的商品名称"
 *                     subtitle:
 *                       type: string
 *                       example: "更新后的副标题"
 *                     updatedAt:
 *                       type: integer
 *                       example: 1703500800000
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "商品不存在或SKU编码已存在"
 *                 data:
 *                   type: null
 *                   example: null
 *       404:
 *         description: 商品不存在
 *       500:
 *         description: 服务器内部错误
 */
router.put('/:id', (req, res) => controller.updateThirdPartyProduct(req, res));

module.exports = router;
