/**
 * 第三方商品SPU路由
 * 为第三方平台提供简化的商品管理接口
 */
const express = require('express');
const router = express.Router();
const ThirdPartyGoodsSpuController = require('../controllers/ThirdPartyGoodsSpuController');
const { prisma } = require('../../../core/database/prisma');
const RouterConfig = require('../../../core/routes/RouterConfig');
const controller = new ThirdPartyGoodsSpuController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/v1/master/third-party/goods-spu:
 *   post:
 *     summary: 创建第三方商品
 *     description: 为第三方平台创建商品，只需要必填字段，支持多SKU
 *     tags: [第三方商品管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - channelId
 *               - platformId
 *               - storeId
 *               - skus
 *             properties:
 *               name:
 *                 type: string
 *                 description: 商品名称
 *                 maxLength: 255
 *                 example: "第三方商品名称"
 *               channelId:
 *                 type: string
 *                 description: 渠道ID
 *                 pattern: '^\\d+$'
 *                 example: "123456789"
 *               platformId:
 *                 type: string
 *                 description: 平台ID
 *                 pattern: '^\\d+$'
 *                 example: "987654321"
 *               storeId:
 *                 type: string
 *                 description: 店铺ID
 *                 pattern: '^\\d+$'
 *                 example: "456789123"
 *               skus:
 *                 type: array
 *                 description: SKU数组，至少包含1个SKU
 *                 minItems: 1
 *                 maxItems: 100
 *                 items:
 *                   type: object
 *                   required:
 *                     - skuCode
 *                     - salesPrice
 *                     - stock
 *                   properties:
 *                     skuCode:
 *                       type: string
 *                       description: SKU编码，只能包含字母、数字、下划线和横线
 *                       maxLength: 100
 *                       pattern: '^[A-Za-z0-9_-]+$'
 *                       example: "SKU001"
 *                     salesPrice:
 *                       type: number
 *                       description: 销售价格
 *                       minimum: 0.01
 *                       maximum: 999999.99
 *                       example: 99.99
 *                     stock:
 *                       type: integer
 *                       description: 库存数量
 *                       minimum: 0
 *                       maximum: 999999
 *                       example: 100
 *               subtitle:
 *                 type: string
 *                 description: 商品副标题（可选）
 *                 maxLength: 500
 *                 example: "商品副标题"
 *               description:
 *                 type: string
 *                 description: 商品描述（可选）
 *                 maxLength: 5000
 *                 example: "商品详细描述"
 *           examples:
 *             single_sku:
 *               summary: 单SKU商品
 *               value:
 *                 name: "第三方单规格商品"
 *                 channelId: "123456789"
 *                 platformId: "987654321"
 *                 storeId: "456789123"
 *                 skus:
 *                   - skuCode: "SKU001"
 *                     salesPrice: 99.99
 *                     stock: 100
 *             multi_sku:
 *               summary: 多SKU商品
 *               value:
 *                 name: "第三方多规格商品"
 *                 channelId: "123456789"
 *                 platformId: "987654321"
 *                 storeId: "456789123"
 *                 subtitle: "多种规格可选"
 *                 skus:
 *                   - skuCode: "SKU001"
 *                     salesPrice: 99.99
 *                     stock: 100
 *                   - skuCode: "SKU002"
 *                     salesPrice: 89.99
 *                     stock: 50
 *     responses:
 *       200:
 *         description: 商品创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "第三方商品创建成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 商品ID
 *                       example: "1234567890123456789"
 *                     spuCode:
 *                       type: string
 *                       description: 商品SPU编码
 *                       example: "TP_SPU_20241225_001"
 *                     name:
 *                       type: string
 *                       description: 商品名称
 *                       example: "第三方商品名称"
 *                     skuCount:
 *                       type: integer
 *                       description: SKU数量
 *                       example: 2
 *                     totalStock:
 *                       type: integer
 *                       description: 总库存
 *                       example: 150
 *                     createdAt:
 *                       type: integer
 *                       description: 创建时间戳（毫秒）
 *                       example: 1703472000000
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "SKU编码SKU001已存在"
 *                 data:
 *                   type: null
 *                   example: null
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.post('/', (req, res) => controller.createThirdPartyProduct(req, res));

/**
 * @swagger
 * /api/v1/master/third-party/goods-spu:
 *   get:
 *     summary: 获取第三方商品列表
 *     description: 获取第三方商品列表，支持分页、筛选和排序
 *     tags: [第三方商品管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 *           maxLength: 255
 *         description: 搜索关键词（商品名称、副标题、商品编码）
 *       - in: query
 *         name: channelId
 *         schema:
 *           type: string
 *           pattern: '^\\d+$'
 *         description: 渠道ID筛选
 *       - in: query
 *         name: platformId
 *         schema:
 *           type: string
 *           pattern: '^\\d+$'
 *         description: 平台ID筛选
 *       - in: query
 *         name: storeId
 *         schema:
 *           type: string
 *           pattern: '^\\d+$'
 *         description: 店铺ID筛选
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2]
 *         description: 商品状态（0-草稿，1-上架，2-下架）
 *       - in: query
 *         name: startTime
 *         schema:
 *           type: integer
 *         description: 开始时间戳（毫秒）
 *       - in: query
 *         name: endTime
 *         schema:
 *           type: integer
 *         description: 结束时间戳（毫秒）
 *     responses:
 *       200:
 *         description: 获取商品列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取第三方商品列表成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             example: "1234567890123456789"
 *                           spuCode:
 *                             type: string
 *                             example: "TP_SPU_20241225_001"
 *                           name:
 *                             type: string
 *                             example: "第三方商品名称"
 *                           subtitle:
 *                             type: string
 *                             example: "商品副标题"
 *                           status:
 *                             type: integer
 *                             example: 1
 *                           statusText:
 *                             type: string
 *                             example: "已上架"
 *                           skuCount:
 *                             type: integer
 *                             example: 2
 *                           totalStock:
 *                             type: integer
 *                             example: 150
 *                           priceRange:
 *                             type: string
 *                             example: "¥89.99 - ¥99.99"
 *                           channels:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                   example: "123456789"
 *                                 name:
 *                                   type: string
 *                                   example: "淘宝"
 *                           createdAt:
 *                             type: integer
 *                             example: 1703472000000
 *                           updatedAt:
 *                             type: integer
 *                             example: 1703472000000
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                           example: 1
 *                         pageSize:
 *                           type: integer
 *                           example: 20
 *                         total:
 *                           type: integer
 *                           example: 100
 *                         totalPages:
 *                           type: integer
 *                           example: 5
 */
protectedRouter.get('/', (req, res) => controller.getThirdPartyProductList(req, res));

/**
 * @swagger
 * /api/v1/master/third-party/goods-spu/{id}:
 *   get:
 *     summary: 获取第三方商品详情
 *     description: 根据ID获取第三方商品详细信息
 *     tags: [第三方商品管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^\\d+$'
 *         description: 商品SPU ID
 *     responses:
 *       200:
 *         description: 获取商品详情成功
 *       404:
 *         description: 商品不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/:id', (req, res) => controller.getThirdPartyProductById(req, res));

module.exports = router;
