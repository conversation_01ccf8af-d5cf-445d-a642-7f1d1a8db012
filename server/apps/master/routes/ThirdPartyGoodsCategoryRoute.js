/**
 * 第三方商品分类路由
 * 为第三方平台提供简化的分类管理接口
 */
const express = require('express');
const router = express.Router();
const ThirdPartyGoodsCategoryController = require('../controllers/ThirdPartyGoodsCategoryController');
const { prisma } = require('../../../core/database/prisma');
const controller = new ThirdPartyGoodsCategoryController(prisma);

// 第三方分类接口不需要鉴权，直接使用基础路由

/**
 * @swagger
 * /api/v1/master/third-party/goods-category:
 *   post:
 *     summary: 创建第三方商品分类
 *     description: 为第三方平台创建商品分类，支持层级结构，无需鉴权
 *     tags: [第三方商品分类管理]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - channelId
 *               - platformId
 *               - storeId
 *             properties:
 *               name:
 *                 type: string
 *                 description: 分类名称
 *                 maxLength: 100
 *                 example: "第三方商品分类"
 *               channelId:
 *                 type: string
 *                 description: 渠道ID
 *                 pattern: '^\\d+$'
 *                 example: "123456789"
 *               platformId:
 *                 type: string
 *                 description: 平台ID
 *                 pattern: '^\\d+$'
 *                 example: "987654321"
 *               storeId:
 *                 type: string
 *                 description: 店铺ID
 *                 pattern: '^\\d+$'
 *                 example: "456789123"
 *               parentId:
 *                 type: string
 *                 description: 父分类ID（可选，用于创建子分类）
 *                 pattern: '^\\d+$'
 *                 example: "111222333"
 *               description:
 *                 type: string
 *                 description: 分类描述（可选）
 *                 maxLength: 1000
 *                 example: "这是一个第三方商品分类的描述"
 *               imageUrl:
 *                 type: string
 *                 description: 分类图片URL（可选）
 *                 format: uri
 *                 maxLength: 500
 *                 example: "https://example.com/category-image.jpg"
 *               sortOrder:
 *                 type: integer
 *                 description: 排序值（可选，默认为0）
 *                 minimum: 0
 *                 maximum: 999999
 *                 example: 100
 *               metaTitle:
 *                 type: string
 *                 description: SEO标题（可选）
 *                 maxLength: 200
 *                 example: "第三方商品分类 - SEO标题"
 *               metaKeywords:
 *                 type: string
 *                 description: SEO关键词（可选）
 *                 maxLength: 500
 *                 example: "第三方,商品,分类,关键词"
 *               metaDescription:
 *                 type: string
 *                 description: SEO描述（可选）
 *                 maxLength: 500
 *                 example: "第三方商品分类的SEO描述信息"
 *           examples:
 *             top_level_category:
 *               summary: 顶级分类
 *               value:
 *                 name: "电子产品"
 *                 channelId: "123456789"
 *                 platformId: "987654321"
 *                 storeId: "456789123"
 *                 description: "各类电子产品分类"
 *                 imageUrl: "https://example.com/electronics.jpg"
 *                 sortOrder: 100
 *             sub_category:
 *               summary: 子分类
 *               value:
 *                 name: "智能手机"
 *                 channelId: "123456789"
 *                 platformId: "987654321"
 *                 storeId: "456789123"
 *                 parentId: "111222333"
 *                 description: "各品牌智能手机"
 *                 sortOrder: 10
 *     responses:
 *       200:
 *         description: 分类创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "第三方商品分类创建成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       description: 分类ID
 *                       example: "1234567890123456789"
 *                     name:
 *                       type: string
 *                       description: 分类名称
 *                       example: "第三方商品分类"
 *                     level:
 *                       type: integer
 *                       description: 分类层级
 *                       example: 1
 *                     parentId:
 *                       type: string
 *                       description: 父分类ID
 *                       example: null
 *                     createdAt:
 *                       type: string
 *                       description: 创建时间戳（毫秒）
 *                       example: "1703472000000"
 *       400:
 *         description: 请求参数错误
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 400
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "渠道ID 123456789 不存在"
 *                 data:
 *                   type: null
 *                   example: null
 *       404:
 *         description: 关联资源不存在
 *       500:
 *         description: 服务器内部错误
 */
router.post('/', (req, res) => controller.createThirdPartyCategory(req, res));

/**
 * @swagger
 * /api/v1/master/third-party/goods-category/{id}:
 *   put:
 *     summary: 更新第三方商品分类
 *     description: 更新第三方平台商品分类信息，无需鉴权
 *     tags: [第三方商品分类管理]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^\\d+$'
 *         description: 分类ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: 分类名称（可选）
 *                 maxLength: 100
 *                 example: "更新后的分类名称"
 *               description:
 *                 type: string
 *                 description: 分类描述（可选）
 *                 maxLength: 1000
 *                 example: "更新后的分类描述"
 *               imageUrl:
 *                 type: string
 *                 description: 分类图片URL（可选）
 *                 format: uri
 *                 maxLength: 500
 *                 example: "https://example.com/new-category-image.jpg"
 *               sortOrder:
 *                 type: integer
 *                 description: 排序值（可选）
 *                 minimum: 0
 *                 maximum: 999999
 *                 example: 200
 *               parentId:
 *                 type: string
 *                 description: 父分类ID（可选，null表示设为顶级分类）
 *                 pattern: '^\\d+$'
 *                 example: "222333444"
 *               channelId:
 *                 type: string
 *                 description: 渠道ID（可选）
 *                 pattern: '^\\d+$'
 *                 example: "123456789"
 *               platformId:
 *                 type: string
 *                 description: 平台ID（可选）
 *                 pattern: '^\\d+$'
 *                 example: "987654321"
 *               storeId:
 *                 type: string
 *                 description: 店铺ID（可选）
 *                 pattern: '^\\d+$'
 *                 example: "456789123"
 *           examples:
 *             update_basic_info:
 *               summary: 更新基本信息
 *               value:
 *                 name: "更新后的分类名称"
 *                 description: "更新后的分类描述"
 *                 sortOrder: 200
 *             change_parent:
 *               summary: 更改父分类
 *               value:
 *                 parentId: "222333444"
 *     responses:
 *       200:
 *         description: 更新分类成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "第三方商品分类更新成功"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "1234567890123456789"
 *                     name:
 *                       type: string
 *                       example: "更新后的分类名称"
 *                     updatedAt:
 *                       type: string
 *                       example: "1703500800000"
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 分类不存在
 *       500:
 *         description: 服务器内部错误
 */
router.put('/:id', (req, res) => controller.updateThirdPartyCategory(req, res));

module.exports = router;
