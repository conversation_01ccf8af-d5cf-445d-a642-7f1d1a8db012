/**
 * 服务商订单路由
 * 处理服务商端订单相关的路由配置
 */
const express = require('express');
const router = express.Router();
const { prisma } = require('../../../core/database/prisma');
const CooperativeOrderController = require('../controllers/CooperativeOrderController');
const RouterConfig = require('../../../core/routes/RouterConfig');

// 创建控制器实例
const controller = new CooperativeOrderController(prisma);

// 创建受JWT保护的路由
const protectedRouter = RouterConfig.authRoute(router);

/**
 * @swagger
 * /api/master/cooperative/orders:
 *   get:
 *     summary: 获取服务商订单列表
 *     description: 获取当前登录用户有权限查看的订单列表，基于 provider.order_assignment 表的 salesman_id 进行权限控制
 *     tags: [服务商订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: sortField
 *         schema:
 *           type: string
 *           default: created_at
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: 排序方向
 *       - in: query
 *         name: orderNumber
 *         schema:
 *           type: string
 *         description: 订单编号（模糊搜索）
 *       - in: query
 *         name: thirdPartyOrderSn
 *         schema:
 *           type: string
 *         description: 第三方订单编号（模糊搜索）
 *       - in: query
 *         name: productName
 *         schema:
 *           type: string
 *         description: 商品名称（模糊搜索）
 *       - in: query
 *         name: receiverName
 *         schema:
 *           type: string
 *         description: 收货人姓名（模糊搜索）
 *       - in: query
 *         name: orderSource
 *         schema:
 *           type: string
 *         description: 订单来源
 *       - in: query
 *         name: orderStatus
 *         schema:
 *           type: integer
 *         description: 订单状态
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [recent6m, recent3m, recent1m, recent1w]
 *         description: 时间范围
 *       - in: query
 *         name: orderTimeStart
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 下单开始时间
 *       - in: query
 *         name: orderTimeEnd
 *         schema:
 *           type: string
 *           format: date-time
 *         description: 下单结束时间
 *     responses:
 *       200:
 *         description: 成功获取订单列表
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取订单列表成功"
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             description: 订单ID
 *                           third_party_order_sn:
 *                             type: string
 *                             description: 第三方订单编号
 *                           order_status:
 *                             type: integer
 *                             description: 订单状态
 *                           payment_status:
 *                             type: integer
 *                             description: 支付状态
 *                           total_amount:
 *                             type: number
 *                             description: 订单总金额
 *                           payable_amount:
 *                             type: number
 *                             description: 应付金额
 *                           paid_amount:
 *                             type: number
 *                             description: 已付金额
 *                           assignment_amount:
 *                             type: number
 *                             description: 分配金额
 *                           rate:
 *                             type: number
 *                             description: 费率
 *                           recipient_name:
 *                             type: string
 *                             description: 收货人姓名
 *                           recipient_phone:
 *                             type: string
 *                             description: 收货人电话
 *                           recipient_address:
 *                             type: string
 *                             description: 收货地址
 *                           channel_name:
 *                             type: string
 *                             description: 渠道名称
 *                           channel_icon_url:
 *                             type: string
 *                             description: 渠道图标URL
 *                           salesman_name:
 *                             type: string
 *                             description: 业务员姓名
 *                           created_at:
 *                             type: string
 *                             description: 创建时间
 *                           products:
 *                             type: array
 *                             description: 订单商品列表
 *                             items:
 *                               type: object
 *                               properties:
 *                                 product_name:
 *                                   type: string
 *                                   description: 商品名称
 *                                 product_spec:
 *                                   type: string
 *                                   description: 商品规格
 *                                 product_image:
 *                                   type: string
 *                                   description: 商品图片
 *                                 unit_price:
 *                                   type: number
 *                                   description: 单价
 *                                 quantity:
 *                                   type: integer
 *                                   description: 数量
 *                                 subtotal_amount:
 *                                   type: number
 *                                   description: 小计金额
 *                     pageInfo:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: integer
 *                           description: 总记录数
 *                         currentPage:
 *                           type: integer
 *                           description: 当前页码
 *                         totalPage:
 *                           type: integer
 *                           description: 总页数
 *                         pageSize:
 *                           type: integer
 *                           description: 每页数量
 *       401:
 *         description: 未授权
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/', controller.getOrders.bind(controller));

/**
 * @swagger
 * /api/master/cooperative/orders/{id}:
 *   get:
 *     summary: 获取服务商订单详情
 *     description: 根据订单ID获取订单详情，需要验证当前用户是否有权限查看该订单
 *     tags: [服务商订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 成功获取订单详情
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "获取订单详情成功"
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   description: 订单详情数据
 *       401:
 *         description: 未授权
 *       403:
 *         description: 无权限查看该订单
 *       404:
 *         description: 订单不存在
 *       500:
 *         description: 服务器内部错误
 */
protectedRouter.get('/:id', controller.getOrderDetail.bind(controller));

module.exports = router;
