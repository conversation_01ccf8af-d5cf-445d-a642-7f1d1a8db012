/**
 * 第三方商品属性参数项DTO
 * 用于第三方平台商品属性参数项的创建和更新
 */
const Joi = require('joi');
const GoodsSourceTypeEnum = require('../../constants/GoodsSourceTypeEnum');

/**
 * 第三方商品属性参数项创建DTO
 */
const createThirdPartyGoodsAttributeItemDto = Joi.object({
  // 必填字段
  name: Joi.string()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.empty': '属性参数项名称不能为空',
      'string.min': '属性参数项名称至少1个字符',
      'string.max': '属性参数项名称不能超过100个字符',
      'any.required': '属性参数项名称为必填项'
    }),

  goodsAttributeSetId: Joi.string()
    .pattern(/^\d+$/)
    .required()
    .messages({
      'string.pattern.base': '属性集ID必须为数字字符串',
      'any.required': '属性集ID为必填项'
    }),
  
  channelId: Joi.string()
    .pattern(/^\d+$/)
    .required()
    .messages({
      'string.pattern.base': '渠道ID必须为数字字符串',
      'any.required': '渠道ID为必填项'
    }),
  
  platformId: Joi.string()
    .pattern(/^\d+$/)
    .required()
    .messages({
      'string.pattern.base': '平台ID必须为数字字符串',
      'any.required': '平台ID为必填项'
    }),
  
  storeId: Joi.string()
    .pattern(/^\d+$/)
    .required()
    .messages({
      'string.pattern.base': '店铺ID必须为数字字符串',
      'any.required': '店铺ID为必填项'
    }),

  // 可选字段
  type: Joi.string()
    .max(50)
    .default('text')
    .messages({
      'string.max': '属性类型不能超过50个字符'
    }),

  value: Joi.alternatives()
    .try(
      Joi.string(),
      Joi.number(),
      Joi.object(),
      Joi.array()
    )
    .allow(null)
    .messages({
      'alternatives.types': '属性值格式无效'
    }),

  isRequired: Joi.number()
    .integer()
    .valid(0, 1)
    .default(0)
    .messages({
      'number.base': '是否必填必须为数字',
      'number.integer': '是否必填必须为整数',
      'any.only': '是否必填只能为0或1'
    }),

  isFilterable: Joi.number()
    .integer()
    .valid(0, 1)
    .default(0)
    .messages({
      'number.base': '是否可筛选必须为数字',
      'number.integer': '是否可筛选必须为整数',
      'any.only': '是否可筛选只能为0或1'
    }),

  sortOrder: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.base': '排序值必须为数字',
      'number.integer': '排序值必须为整数',
      'number.min': '排序值不能小于0'
    }),

  isEnabled: Joi.number()
    .integer()
    .valid(0, 1)
    .default(1)
    .messages({
      'number.base': '是否启用必须为数字',
      'number.integer': '是否启用必须为整数',
      'any.only': '是否启用只能为0或1'
    }),

  platformAttributeItemId: Joi.string()
    .max(100)
    .allow(null, '')
    .messages({
      'string.max': '平台属性参数项ID不能超过100个字符'
    }),

  platformAttributeItemName: Joi.string()
    .max(255)
    .allow(null, '')
    .messages({
      'string.max': '平台属性参数项名称不能超过255个字符'
    })
});

/**
 * 第三方商品属性参数项更新DTO
 */
const updateThirdPartyGoodsAttributeItemDto = Joi.object({
  // 可选字段 - 更新时所有字段都是可选的
  name: Joi.string()
    .min(1)
    .max(100)
    .messages({
      'string.empty': '属性参数项名称不能为空',
      'string.min': '属性参数项名称至少1个字符',
      'string.max': '属性参数项名称不能超过100个字符'
    }),

  goodsAttributeSetId: Joi.string()
    .pattern(/^\d+$/)
    .messages({
      'string.pattern.base': '属性集ID必须为数字字符串'
    }),
  
  channelId: Joi.string()
    .pattern(/^\d+$/)
    .messages({
      'string.pattern.base': '渠道ID必须为数字字符串'
    }),
  
  platformId: Joi.string()
    .pattern(/^\d+$/)
    .messages({
      'string.pattern.base': '平台ID必须为数字字符串'
    }),
  
  storeId: Joi.string()
    .pattern(/^\d+$/)
    .messages({
      'string.pattern.base': '店铺ID必须为数字字符串'
    }),

  type: Joi.string()
    .max(50)
    .messages({
      'string.max': '属性类型不能超过50个字符'
    }),

  value: Joi.alternatives()
    .try(
      Joi.string(),
      Joi.number(),
      Joi.object(),
      Joi.array()
    )
    .allow(null)
    .messages({
      'alternatives.types': '属性值格式无效'
    }),

  isRequired: Joi.number()
    .integer()
    .valid(0, 1)
    .messages({
      'number.base': '是否必填必须为数字',
      'number.integer': '是否必填必须为整数',
      'any.only': '是否必填只能为0或1'
    }),

  isFilterable: Joi.number()
    .integer()
    .valid(0, 1)
    .messages({
      'number.base': '是否可筛选必须为数字',
      'number.integer': '是否可筛选必须为整数',
      'any.only': '是否可筛选只能为0或1'
    }),

  sortOrder: Joi.number()
    .integer()
    .min(0)
    .messages({
      'number.base': '排序值必须为数字',
      'number.integer': '排序值必须为整数',
      'number.min': '排序值不能小于0'
    }),

  isEnabled: Joi.number()
    .integer()
    .valid(0, 1)
    .messages({
      'number.base': '是否启用必须为数字',
      'number.integer': '是否启用必须为整数',
      'any.only': '是否启用只能为0或1'
    }),

  platformAttributeItemId: Joi.string()
    .max(100)
    .allow(null, '')
    .messages({
      'string.max': '平台属性参数项ID不能超过100个字符'
    }),

  platformAttributeItemName: Joi.string()
    .max(255)
    .allow(null, '')
    .messages({
      'string.max': '平台属性参数项名称不能超过255个字符'
    })
});

module.exports = {
  createThirdPartyGoodsAttributeItemDto,
  updateThirdPartyGoodsAttributeItemDto
};
