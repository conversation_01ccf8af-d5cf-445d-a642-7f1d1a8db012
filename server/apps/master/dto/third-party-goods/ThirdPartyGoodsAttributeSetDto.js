/**
 * 第三方商品属性集DTO
 * 用于第三方平台商品属性集的创建和更新
 */
const Joi = require('joi');
const GoodsSourceTypeEnum = require('../../constants/GoodsSourceTypeEnum');

/**
 * 第三方商品属性集创建DTO
 */
const createThirdPartyGoodsAttributeSetDto = Joi.object({
  // 必填字段
  name: Joi.string()
    .min(1)
    .max(100)
    .required()
    .messages({
      'string.empty': '属性集名称不能为空',
      'string.min': '属性集名称至少1个字符',
      'string.max': '属性集名称不能超过100个字符',
      'any.required': '属性集名称为必填项'
    }),
  
  channelId: Joi.string()
    .pattern(/^\d+$/)
    .required()
    .messages({
      'string.pattern.base': '渠道ID必须为数字字符串',
      'any.required': '渠道ID为必填项'
    }),
  
  platformId: Joi.string()
    .pattern(/^\d+$/)
    .required()
    .messages({
      'string.pattern.base': '平台ID必须为数字字符串',
      'any.required': '平台ID为必填项'
    }),
  
  storeId: Joi.string()
    .pattern(/^\d+$/)
    .required()
    .messages({
      'string.pattern.base': '店铺ID必须为数字字符串',
      'any.required': '店铺ID为必填项'
    }),

  // 可选字段
  sortOrder: Joi.number()
    .integer()
    .min(0)
    .default(0)
    .messages({
      'number.base': '排序值必须为数字',
      'number.integer': '排序值必须为整数',
      'number.min': '排序值不能小于0'
    }),

  platformAttributeSetId: Joi.string()
    .max(100)
    .allow(null, '')
    .messages({
      'string.max': '平台属性集ID不能超过100个字符'
    }),

  platformAttributeSetName: Joi.string()
    .max(255)
    .allow(null, '')
    .messages({
      'string.max': '平台属性集名称不能超过255个字符'
    })
});

/**
 * 第三方商品属性集更新DTO
 */
const updateThirdPartyGoodsAttributeSetDto = Joi.object({
  // 可选字段 - 更新时所有字段都是可选的
  name: Joi.string()
    .min(1)
    .max(100)
    .messages({
      'string.empty': '属性集名称不能为空',
      'string.min': '属性集名称至少1个字符',
      'string.max': '属性集名称不能超过100个字符'
    }),
  
  channelId: Joi.string()
    .pattern(/^\d+$/)
    .messages({
      'string.pattern.base': '渠道ID必须为数字字符串'
    }),
  
  platformId: Joi.string()
    .pattern(/^\d+$/)
    .messages({
      'string.pattern.base': '平台ID必须为数字字符串'
    }),
  
  storeId: Joi.string()
    .pattern(/^\d+$/)
    .messages({
      'string.pattern.base': '店铺ID必须为数字字符串'
    }),

  sortOrder: Joi.number()
    .integer()
    .min(0)
    .messages({
      'number.base': '排序值必须为数字',
      'number.integer': '排序值必须为整数',
      'number.min': '排序值不能小于0'
    }),

  platformAttributeSetId: Joi.string()
    .max(100)
    .allow(null, '')
    .messages({
      'string.max': '平台属性集ID不能超过100个字符'
    }),

  platformAttributeSetName: Joi.string()
    .max(255)
    .allow(null, '')
    .messages({
      'string.max': '平台属性集名称不能超过255个字符'
    })
});

module.exports = {
  createThirdPartyGoodsAttributeSetDto,
  updateThirdPartyGoodsAttributeSetDto
};
