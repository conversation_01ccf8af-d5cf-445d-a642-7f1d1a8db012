const Joi = require('joi');

/**
 * 第三方商品品牌 DTO
 * 为第三方平台提供简化的品牌管理数据验证
 */
class ThirdPartyGoodsBrandDto {
  /**
   * 创建第三方商品品牌的验证规则
   */
  static createSchema = Joi.object({
    // 必填字段
    name: Joi.string()
      .required()
      .min(1)
      .max(100)
      .messages({
        'string.empty': '品牌名称不能为空',
        'any.required': '品牌名称为必填项',
        'string.max': '品牌名称长度不能超过100个字符'
      }),
    
    channelId: Joi.string()
      .required()
      .pattern(/^\d+$/)
      .messages({
        'string.empty': '渠道ID不能为空',
        'any.required': '渠道ID为必填项',
        'string.pattern.base': '渠道ID必须是数字字符串'
      }),
    
    platformId: Joi.string()
      .required()
      .pattern(/^\d+$/)
      .messages({
        'string.empty': '平台ID不能为空',
        'any.required': '平台ID为必填项',
        'string.pattern.base': '平台ID必须是数字字符串'
      }),
    
    storeId: Joi.string()
      .required()
      .pattern(/^\d+$/)
      .messages({
        'string.empty': '店铺ID不能为空',
        'any.required': '店铺ID为必填项',
        'string.pattern.base': '店铺ID必须是数字字符串'
      }),

    // 可选字段
    logoUrl: Joi.string()
      .uri()
      .max(255)
      .allow('', null)
      .messages({
        'string.uri': 'Logo URL格式不正确',
        'string.max': 'Logo URL长度不能超过255个字符'
      }),
    
    description: Joi.string()
      .max(1000)
      .allow('', null)
      .messages({
        'string.max': '品牌描述长度不能超过1000个字符'
      }),

    // 第三方平台相关字段
    platformBrandId: Joi.string()
      .max(100)
      .allow('', null)
      .messages({
        'string.max': '第三方平台品牌ID长度不能超过100个字符'
      }),
    
    platformBrandName: Joi.string()
      .max(255)
      .allow('', null)
      .messages({
        'string.max': '第三方平台品牌名称长度不能超过255个字符'
      })
  });

  /**
   * 更新第三方商品品牌的验证规则
   */
  static updateSchema = Joi.object({
    // 可选字段（更新时所有字段都是可选的）
    name: Joi.string()
      .min(1)
      .max(100)
      .messages({
        'string.empty': '品牌名称不能为空',
        'string.max': '品牌名称长度不能超过100个字符'
      }),
    
    logoUrl: Joi.string()
      .uri()
      .max(255)
      .allow('', null)
      .messages({
        'string.uri': 'Logo URL格式不正确',
        'string.max': 'Logo URL长度不能超过255个字符'
      }),
    
    description: Joi.string()
      .max(1000)
      .allow('', null)
      .messages({
        'string.max': '品牌描述长度不能超过1000个字符'
      }),

    // 第三方平台相关字段
    platformBrandId: Joi.string()
      .max(100)
      .allow('', null)
      .messages({
        'string.max': '第三方平台品牌ID长度不能超过100个字符'
      }),
    
    platformBrandName: Joi.string()
      .max(255)
      .allow('', null)
      .messages({
        'string.max': '第三方平台品牌名称长度不能超过255个字符'
      })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  });

  /**
   * 验证创建第三方商品品牌的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证后的数据
   */
  static validateCreate(data) {
    const { error, value } = this.createSchema.validate(data, { 
      abortEarly: false,
      stripUnknown: true 
    });
    
    if (error) {
      const messages = error.details.map(detail => detail.message);
      throw new Error(`请求数据验证失败: ${messages.join(', ')}`);
    }
    
    return value;
  }

  /**
   * 验证更新第三方商品品牌的数据
   * @param {Object} data 要验证的数据
   * @returns {Object} 验证后的数据
   */
  static validateUpdate(data) {
    const { error, value } = this.updateSchema.validate(data, { 
      abortEarly: false,
      stripUnknown: true 
    });
    
    if (error) {
      const messages = error.details.map(detail => detail.message);
      throw new Error(`请求数据验证失败: ${messages.join(', ')}`);
    }
    
    return value;
  }
}

module.exports = ThirdPartyGoodsBrandDto;
