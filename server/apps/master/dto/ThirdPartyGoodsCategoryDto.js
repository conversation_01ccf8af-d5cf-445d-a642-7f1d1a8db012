/**
 * 第三方商品分类数据传输对象
 * 为第三方平台提供简化的分类管理接口
 */
const Joi = require('joi');

/**
 * 第三方商品分类DTO类
 */
class ThirdPartyGoodsCategoryDto {
  /**
   * 验证创建第三方商品分类的数据
   * @param {Object} data - 待验证的数据
   * @returns {Object} 验证结果
   */
  static validateCreate(data) {
    const schema = Joi.object({
      // 必需字段
      name: Joi.string()
        .trim()
        .min(1)
        .max(100)
        .required()
        .messages({
          'string.empty': '分类名称不能为空',
          'string.min': '分类名称至少需要1个字符',
          'string.max': '分类名称不能超过100个字符',
          'any.required': '分类名称是必需的'
        }),

      channelId: Joi.string()
        .pattern(/^\d+$/)
        .required()
        .messages({
          'string.pattern.base': '渠道ID必须是数字字符串',
          'any.required': '渠道ID是必需的'
        }),

      platformId: Joi.string()
        .pattern(/^\d+$/)
        .required()
        .messages({
          'string.pattern.base': '平台ID必须是数字字符串',
          'any.required': '平台ID是必需的'
        }),

      storeId: Joi.string()
        .pattern(/^\d+$/)
        .required()
        .messages({
          'string.pattern.base': '店铺ID必须是数字字符串',
          'any.required': '店铺ID是必需的'
        }),

      // 可选字段
      parentId: Joi.string()
        .pattern(/^\d+$/)
        .allow(null, '')
        .optional()
        .messages({
          'string.pattern.base': '父分类ID必须是数字字符串'
        }),

      description: Joi.string()
        .trim()
        .max(1000)
        .allow('', null)
        .optional()
        .messages({
          'string.max': '分类描述不能超过1000个字符'
        }),

      imageUrl: Joi.string()
        .uri()
        .max(500)
        .allow('', null)
        .optional()
        .messages({
          'string.uri': '图片URL格式不正确',
          'string.max': '图片URL不能超过500个字符'
        }),

      sortOrder: Joi.number()
        .integer()
        .min(0)
        .max(999999)
        .default(0)
        .optional()
        .messages({
          'number.integer': '排序值必须是整数',
          'number.min': '排序值不能小于0',
          'number.max': '排序值不能大于999999'
        }),

      metaTitle: Joi.string()
        .trim()
        .max(200)
        .allow('', null)
        .optional()
        .messages({
          'string.max': 'SEO标题不能超过200个字符'
        }),

      metaKeywords: Joi.string()
        .trim()
        .max(500)
        .allow('', null)
        .optional()
        .messages({
          'string.max': 'SEO关键词不能超过500个字符'
        }),

      metaDescription: Joi.string()
        .trim()
        .max(500)
        .allow('', null)
        .optional()
        .messages({
          'string.max': 'SEO描述不能超过500个字符'
        })
    });

    return schema.validate(data, { 
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });
  }

  /**
   * 验证更新第三方商品分类的数据
   * @param {Object} data - 待验证的数据
   * @returns {Object} 验证结果
   */
  static validateUpdate(data) {
    const schema = Joi.object({
      // 更新时所有字段都是可选的，但至少要有一个字段
      name: Joi.string()
        .trim()
        .min(1)
        .max(100)
        .optional()
        .messages({
          'string.empty': '分类名称不能为空',
          'string.min': '分类名称至少需要1个字符',
          'string.max': '分类名称不能超过100个字符'
        }),

      channelId: Joi.string()
        .pattern(/^\d+$/)
        .optional()
        .messages({
          'string.pattern.base': '渠道ID必须是数字字符串'
        }),

      platformId: Joi.string()
        .pattern(/^\d+$/)
        .optional()
        .messages({
          'string.pattern.base': '平台ID必须是数字字符串'
        }),

      storeId: Joi.string()
        .pattern(/^\d+$/)
        .optional()
        .messages({
          'string.pattern.base': '店铺ID必须是数字字符串'
        }),

      parentId: Joi.string()
        .pattern(/^\d+$/)
        .allow(null, '')
        .optional()
        .messages({
          'string.pattern.base': '父分类ID必须是数字字符串'
        }),

      description: Joi.string()
        .trim()
        .max(1000)
        .allow('', null)
        .optional()
        .messages({
          'string.max': '分类描述不能超过1000个字符'
        }),

      imageUrl: Joi.string()
        .uri()
        .max(500)
        .allow('', null)
        .optional()
        .messages({
          'string.uri': '图片URL格式不正确',
          'string.max': '图片URL不能超过500个字符'
        }),

      sortOrder: Joi.number()
        .integer()
        .min(0)
        .max(999999)
        .optional()
        .messages({
          'number.integer': '排序值必须是整数',
          'number.min': '排序值不能小于0',
          'number.max': '排序值不能大于999999'
        }),

      metaTitle: Joi.string()
        .trim()
        .max(200)
        .allow('', null)
        .optional()
        .messages({
          'string.max': 'SEO标题不能超过200个字符'
        }),

      metaKeywords: Joi.string()
        .trim()
        .max(500)
        .allow('', null)
        .optional()
        .messages({
          'string.max': 'SEO关键词不能超过500个字符'
        }),

      metaDescription: Joi.string()
        .trim()
        .max(500)
        .allow('', null)
        .optional()
        .messages({
          'string.max': 'SEO描述不能超过500个字符'
        })
    }).min(1).messages({
      'object.min': '至少需要提供一个要更新的字段'
    });

    return schema.validate(data, { 
      abortEarly: false,
      stripUnknown: true,
      convert: true
    });
  }
}

module.exports = {
  ThirdPartyGoodsCategoryDto
};
