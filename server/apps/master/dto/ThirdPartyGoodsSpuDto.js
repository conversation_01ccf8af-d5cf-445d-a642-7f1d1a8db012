/**
 * 第三方商品SPU数据传输对象
 * 用于验证第三方平台创建商品的请求数据
 */
const Joi = require('joi');
const GoodsSourceTypeEnum = require('../constants/GoodsSourceTypeEnum');
const GoodsSyncStatusEnum = require('../constants/GoodsSyncStatusEnum');

class ThirdPartyGoodsSpuDto {
  /**
   * 验证创建第三方商品的数据
   * @param {Object} data - 商品数据
   * @returns {Object} - 验证结果
   */
  static validateCreate(data) {
    const schema = Joi.object({
      // 商品基本信息
      name: Joi.string()
        .trim()
        .min(1)
        .max(255)
        .required()
        .messages({
          'string.empty': '商品名称不能为空',
          'string.min': '商品名称不能为空',
          'string.max': '商品名称不能超过255个字符',
          'any.required': '商品名称为必填项'
        }),

      // 渠道、平台、店铺信息
      channelId: Joi.alternatives()
        .try(
          Joi.string().pattern(/^\d+$/),
          Joi.number().integer().positive()
        )
        .required()
        .messages({
          'alternatives.match': '渠道ID格式不正确',
          'any.required': '渠道ID为必填项'
        }),

      platformId: Joi.alternatives()
        .try(
          Joi.string().pattern(/^\d+$/),
          Joi.number().integer().positive()
        )
        .required()
        .messages({
          'alternatives.match': '平台ID格式不正确',
          'any.required': '平台ID为必填项'
        }),

      storeId: Joi.alternatives()
        .try(
          Joi.string().pattern(/^\d+$/),
          Joi.number().integer().positive()
        )
        .required()
        .messages({
          'alternatives.match': '店铺ID格式不正确',
          'any.required': '店铺ID为必填项'
        }),

      // SKU信息数组
      skus: Joi.array()
        .min(1)
        .max(100) // 限制最多100个SKU
        .items(
          Joi.object({
            skuCode: Joi.string()
              .trim()
              .min(1)
              .max(100)
              .pattern(/^[A-Za-z0-9_-]+$/)
              .required()
              .messages({
                'string.empty': 'SKU编码不能为空',
                'string.min': 'SKU编码不能为空',
                'string.max': 'SKU编码不能超过100个字符',
                'string.pattern.base': 'SKU编码只能包含字母、数字、下划线和横线',
                'any.required': 'SKU编码为必填项'
              }),

            salesPrice: Joi.number()
              .positive()
              .precision(2)
              .max(999999.99)
              .required()
              .messages({
                'number.base': '销售价格必须为数字',
                'number.positive': '销售价格必须大于0',
                'number.precision': '销售价格最多保留2位小数',
                'number.max': '销售价格不能超过999999.99',
                'any.required': '销售价格为必填项'
              }),

            stock: Joi.number()
              .integer()
              .min(0)
              .max(999999)
              .required()
              .messages({
                'number.base': '库存数量必须为数字',
                'number.integer': '库存数量必须为整数',
                'number.min': '库存数量不能小于0',
                'number.max': '库存数量不能超过999999',
                'any.required': '库存数量为必填项'
              })
          })
        )
        .required()
        .messages({
          'array.min': '至少需要提供1个SKU',
          'array.max': '最多只能提供100个SKU',
          'any.required': 'SKU信息为必填项'
        }),

      // 可选字段
      subtitle: Joi.string()
        .trim()
        .max(500)
        .allow('')
        .allow(null)
        .optional()
        .messages({
          'string.max': '商品副标题不能超过500个字符'
        }),

      description: Joi.string()
        .trim()
        .max(5000)
        .allow('')
        .allow(null)
        .optional()
        .messages({
          'string.max': '商品描述不能超过5000个字符'
        })
    });

    // 验证数据
    const result = schema.validate(data, { 
      abortEarly: false,
      stripUnknown: true // 移除未知字段
    });

    // 如果验证通过，进行额外的业务验证
    if (!result.error && result.value.skus) {
      const skuCodes = result.value.skus.map(sku => sku.skuCode);
      const uniqueSkuCodes = [...new Set(skuCodes)];
      
      // 检查SKU编码是否重复
      if (skuCodes.length !== uniqueSkuCodes.length) {
        return {
          error: {
            details: [{
              message: '同一商品内SKU编码不能重复',
              path: ['skus'],
              type: 'array.unique'
            }]
          }
        };
      }
    }

    return result;
  }

  /**
   * 验证查询参数
   * @param {Object} query - 查询参数
   * @returns {Object} - 验证结果
   */
  static validateListQuery(query) {
    const schema = Joi.object({
      page: Joi.number().integer().min(1).default(1),
      pageSize: Joi.number().integer().min(1).max(100).default(20),
      keyword: Joi.string().trim().max(255).allow('').optional(),
      channelId: Joi.alternatives().try(
        Joi.string().pattern(/^\d+$/),
        Joi.number().integer().positive()
      ).optional(),
      platformId: Joi.alternatives().try(
        Joi.string().pattern(/^\d+$/),
        Joi.number().integer().positive()
      ).optional(),
      storeId: Joi.alternatives().try(
        Joi.string().pattern(/^\d+$/),
        Joi.number().integer().positive()
      ).optional(),
      status: Joi.number().integer().valid(0, 1, 2).optional(),
      startTime: Joi.number().integer().positive().optional(),
      endTime: Joi.number().integer().positive().optional()
    });

    return schema.validate(query, { 
      abortEarly: false,
      stripUnknown: true
    });
  }

  /**
   * 验证更新数据
   * @param {Object} data - 更新数据
   * @returns {Object} - 验证结果
   */
  static validateUpdate(data) {
    // 更新时所有字段都是可选的
    const updateSchema = Joi.object({
      name: Joi.string()
        .trim()
        .min(1)
        .max(255)
        .optional()
        .messages({
          'string.empty': '商品名称不能为空',
          'string.min': '商品名称至少需要1个字符',
          'string.max': '商品名称不能超过255个字符'
        }),

      subtitle: Joi.string()
        .trim()
        .max(500)
        .allow('')
        .optional()
        .messages({
          'string.max': '商品副标题不能超过500个字符'
        }),

      description: Joi.string()
        .trim()
        .max(5000)
        .allow('')
        .optional()
        .messages({
          'string.max': '商品描述不能超过5000个字符'
        }),

      skus: Joi.array()
        .items(
          Joi.object({
            id: Joi.string()
              .pattern(/^\d+$/)
              .required()
              .messages({
                'string.pattern.base': 'SKU ID必须是数字字符串',
                'any.required': '更新SKU时必须提供SKU ID'
              }),

            skuCode: Joi.string()
              .trim()
              .min(1)
              .max(100)
              .optional()
              .messages({
                'string.empty': 'SKU编码不能为空',
                'string.min': 'SKU编码至少需要1个字符',
                'string.max': 'SKU编码不能超过100个字符'
              }),

            salesPrice: Joi.number()
              .min(0)
              .precision(2)
              .optional()
              .messages({
                'number.min': '销售价格不能小于0',
                'number.precision': '销售价格最多保留2位小数'
              }),

            stock: Joi.number()
              .integer()
              .min(0)
              .optional()
              .messages({
                'number.integer': '库存必须是整数',
                'number.min': '库存不能小于0'
              })
          })
        )
        .min(1)
        .optional()
        .messages({
          'array.min': 'SKU列表至少需要1个SKU'
        })
    }).min(1).messages({
      'object.min': '至少需要提供一个要更新的字段'
    });

    return updateSchema.validate(data, {
      abortEarly: false,
      stripUnknown: true
    });
  }
}

// 导出验证模式
const thirdPartyGoodsSpuCreateSchema = {
  validate: (data) => ThirdPartyGoodsSpuDto.validateCreate(data)
};

const thirdPartyGoodsSpuUpdateSchema = {
  validate: (data) => ThirdPartyGoodsSpuDto.validateUpdate(data)
};

const thirdPartyGoodsSpuListSchema = {
  validate: (data) => ThirdPartyGoodsSpuDto.validateListQuery(data)
};

module.exports = {
  ThirdPartyGoodsSpuDto,
  thirdPartyGoodsSpuCreateSchema,
  thirdPartyGoodsSpuUpdateSchema,
  thirdPartyGoodsSpuListSchema
};
