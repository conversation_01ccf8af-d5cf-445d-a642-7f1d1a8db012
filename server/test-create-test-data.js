/**
 * 创建测试数据 - 渠道、平台、店铺
 */

const { PrismaClient } = require('@prisma/client');
const { generateSnowflakeId } = require('./shared/utils/snowflake');

const prisma = new PrismaClient();

async function createTestData() {
  try {
    console.log('开始创建测试数据...');

    // 创建测试渠道
    const channelId = generateSnowflakeId();
    const channel = await prisma.channel.create({
      data: {
        id: channelId,
        name: '测试渠道',
        icon_url: 'https://example.com/channel-icon.png',
        is_built_in: 0,
        created_at: BigInt(Date.now()),
        updated_at: BigInt(Date.now())
      }
    });
    console.log('✅ 创建测试渠道成功:', channel.id.toString());

    // 创建测试平台
    const platformId = generateSnowflakeId();
    const platform = await prisma.platform.create({
      data: {
        id: platformId,
        name: '测试平台',
        code: 'TEST_PLATFORM',
        channel_id: channelId,
        created_at: BigInt(Date.now()),
        updated_at: BigInt(Date.now())
      }
    });
    console.log('✅ 创建测试平台成功:', platform.id.toString());

    // 创建测试店铺
    const storeId = generateSnowflakeId();
    const store = await prisma.store.create({
      data: {
        id: storeId,
        name: '测试店铺',
        code: 'TEST_STORE',
        platform_id: platformId,
        created_at: BigInt(Date.now()),
        updated_at: BigInt(Date.now())
      }
    });
    console.log('✅ 创建测试店铺成功:', store.id.toString());

    console.log('\n=== 测试数据创建完成 ===');
    console.log(`渠道ID: ${channelId.toString()}`);
    console.log(`平台ID: ${platformId.toString()}`);
    console.log(`店铺ID: ${storeId.toString()}`);
    
    console.log('\n=== 可用于测试的API请求 ===');
    console.log(`curl -X POST "http://localhost:4000/api/v1/master/third-party/goods-category" \\
  -H "Content-Type: application/json" \\
  -d '{
    "name": "测试分类",
    "channelId": "${channelId.toString()}",
    "platformId": "${platformId.toString()}", 
    "storeId": "${storeId.toString()}"
  }'`);

    return { channelId, platformId, storeId };

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
if (require.main === module) {
  createTestData().catch(console.error);
}

module.exports = { createTestData };
