<template>
  <footer class="footer py-4">
    <div class="container py-4 py-md-5 px-4 px-md-3 text-body-secondary">
      <div class="row align-items-center mb-4">
        <div class="footer-content">
          <div class="footer-left">
            <img :src="basisData.logo_url" class="footer-logo" />
          </div>
          <div class="footer-right">
            <h5 class="follow-us-text">关注我们</h5>
            <div class="social-icons">
              <a-space :size="10">
                <div class="social-item">
                  <div class="icon-content">
                    <i class="iconfont icon-weixin"></i>
                  </div>
                  <div class="qr-popup">
                    <a-image :src="basisData.wechat_qrcode" :preview="false" alt="微信二维码" />
                    <p>扫码关注</p>
                  </div>
                </div>
                <!-- <div class="social-item">
                  <div class="icon-content">
                    <i class="iconfont icon-douyin" style="top: 1px;"></i>
                  </div>
                  <div class="qr-popup">
                    <a-image :src="douyinQrCodeUrl" alt="抖音二维码" />
                    <p>扫码关注</p>
                  </div>
                </div> -->
              </a-space>
            </div>
          </div>
        </div>
      </div>
      <hr class="my-6">
      <div class="row" style="padding-top: 1.5rem;">
        <div class="col-6 col-md-4 col-lg-3 mb-4">
          <h5 class="mb-3">关于我们</h5>
          <ul class="list-unstyled">
            <li v-for="(item, i) in footerLinks.about" :key="`about-${i}`">
              <a :href="item.url" :data-page="item.page">{{ item.title }}</a>
            </li>
          </ul>
        </div>
        <div class="col-6 col-md-4 col-lg-3 mb-4">
          <h5 class="mb-3">新闻动态</h5>
          <ul class="list-unstyled">
            <li v-for="(item, i) in newsCategoryList" :key="item.id">
              <a href="/website/one/news">{{ item.name }}</a>
            </li>
          </ul>
        </div>
        <div class="col-6 col-md-4 col-lg-3 mb-4">
          <h5 class="mb-3">企业社会责任</h5>
          <ul class="list-unstyled">
            <li v-for="(item, i) in footerLinks.responsibility" :key="`resp-${i}`">
              <a :href="item.url">{{ item.title }}</a>
            </li>
          </ul>
        </div>
        <div class="col-6 col-md-4 col-lg-3 mb-4">
          <h5 class="mb-3">加入我们</h5>
          <ul class="list-unstyled">
            <li v-for="(item, i) in footerLinks.join" :key="`join-${i}`">
              <a :href="item.url" :data-page="item.page">{{ item.title }}</a>
            </li>
          </ul>
        </div>
      </div>
      <div class="row mt-4">
        <div class="col-12 text-center">
          <p class="mb-1">{{basisData?.copyright}}</p>
          <p class="mb-0">{{basisData?.icp_number}}</p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import officialApi from '@/api/master/officialWebsiteModule';
// 定义属性
const props = defineProps({
  // 基础URL，用于构建资源路径
  baseUrl: {
    type: String,
    default: '/website/one/assets'
  },
});

// 页脚链接数据
const footerLinks = ref({
  about: [
    { title: '公司简介', url: '/website/one/profile', page: 'profile' },
    { title: '企业文化', url: '/website/one/profile', page: 'profile' },
  ],
  news: [
    { title: '企业新闻', url: '/website/one/news', page: 'news' },
    { title: '行业新闻', url: '/website/one/news', page: 'news' }
  ],
  responsibility: [
    { title: '公益慈善', url: '/website/one/responsibility', page: 'responsibility' },
    { title: '校企合作', url: '/website/one/responsibility', page: 'responsibility' },
  ],
  service: [
    { title: '服务协议', url: '/website/one/service', page: 'service' },
    { title: '隐私政策', url: '/website/one/service', page: 'service' },
    { title: '知识产权', url: '/website/one/service', page: 'service' },
    { title: '成功案例', url: '#' },
    { title: '合作品牌', url: '#' }
  ],
  contact: [
    { title: '服务网点', url: '#' },
    { title: '官方商城', url: '#' },
    { title: '聚灵云平台', url: '#' },
    { title: '供应商自荐', url: '#' }
  ],
  join: [
    { title: '社会招聘', url: '/website/one/joinus', page: 'joinus' },
    { title: '校园招聘', url: '/website/one/joinus', page: 'joinus' }
  ]
});

const basisData = ref({});
const getData = () =>{
    officialApi.basisManagement.getData().then(res => {
        if(res.code == 200){
            basisData.value = res.data;
        }
    })
}

//获取新闻分类
const newsCategoryList = ref([]);

const getNewsCategoryList = () => {
    officialApi.newsManagement.getList({page:1,pageSize:4}).then(res => {
        if(res.code == 200){
            newsCategoryList.value = res.data.items;
        }
    })
}


// 页面加载完成后执行的逻辑
onMounted(() => {
  getData()
  getNewsCategoryList()
  // 加载字体图标
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;
    
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }
  
  // 加载图标字体
  loadCSS(`${props.baseUrl}/vendor/iconfont/iconfont.css`, 'iconfont-css');
});

// 处理导航链接点击
function handleLinkClick(e) {
  const target = e.target;
  
  // 检查是否是页脚中的链接
  if (target.tagName === 'A' && target.dataset.page) {
    e.preventDefault();
    const page = target.dataset.page;
    
    if (page === 'joinus') {
      window.location.href = '/website/one/joinus';
    } else if (page === 'profile') {
      window.location.href = '/website/one/profile';
    } else if (page === 'news') {
      window.location.href = '/website/one/news';
    } else if (page === 'responsibility') {
      window.location.href = '/website/one/responsibility';
    }
  }
}
</script>

<style scoped>
/* 防止横向滚动条 */
.container {
  max-width: 1200px;
  overflow-x: hidden;
}
.footer {
  background-color: #f8f9fa;
  color: #6c757d;
}

.footer h5 {
  color: #343a40;
  font-weight: 500;
  font-size: 1.1rem;
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-unstyled li {
  margin-bottom: 0.5rem;
}

.list-unstyled a {
  color: #6c757d;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.2s ease;
}

.list-unstyled a:hover {
  color: #007bff;
  text-decoration: none;
}

.footer-logo {
  max-height: 50px;
  width: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 100%;
}

.footer-left {
  display: flex;
  align-items: center;
}

.footer-right {
  margin-right: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.follow-us-text {
  margin: 0;
  white-space: nowrap;
}

.social-icons {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  align-items: center;
}

.social-item {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #e8e8e8;
  cursor: pointer;
  margin-right: 10px;
}

.icon-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-content i {
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
}

.social-item:hover .qr-popup {
  opacity: 1;
  visibility: visible;
}





.douyin-btn:hover i {
  color: #000;
}

.qr-popup {
  position: absolute;
  top: 40px;
  right: -50px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 15px;
  text-align: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  width: 160px;
}

.qr-popup img {
  width: 100%;
  height: auto;
  border-radius: 4px;
}

.qr-popup p {
  margin-top: 8px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}
/* 移动端适配 */
@media (max-width: 767.98px) {
  .footer-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
    max-width: 100%;
  }
  
  .footer-logo {
    max-height: 40px;
  }
  
  .footer-right {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .follow-us-text {
    font-size: 0.9rem;
    margin: 0;
    display: flex;
    align-items: center;
  }
  
  .social-item {
    width: 32px;
    height: 32px;
    margin-right: 0;
  }
  
  .icon-content i {
    line-height: 32px;
  }
}
</style>
