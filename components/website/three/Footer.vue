<template>
  <footer class="bg-black text-white py-6 md:py-8">
    <div class="container mx-auto px-4">
      <!-- LOGO区域和导航栏 -->
      <div class="flex justify-between items-center mb-6 md:mb-10">
        <div class="flex-shrink-0 mx-auto md:mx-0">
          <img :src="basisData.logo_url" alt="Logo" class="logo-image"/>
        </div>

        <!-- 页脚导航栏 - 在移动端隐藏 -->
        <div class="flex-grow hidden md:block">
          <ul class="flex justify-end space-x-">
            <li
              v-for="(item, index) in footerNavItems"
              :key="index"
              class="text-gray-300 hover:text-white nav-item"
            >
              <a 
                @click="handleNavClick($event, item.key)" 
                :class="{active: isActive(item.key)}"
                class="transition duration-300 ease-in-out"
              >{{ item.title }}</a>
            </li>
          </ul>
        </div>
      </div>

      <!-- 顶部分隔线 -->
      <div class="border-b border-gray-700 mb-6 md:mb-20"></div>

      <!-- 主要内容区域 -->
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3 md:gap-8 mb-4 md:mb-6">
        <!-- 联系方式 -->
        <div class="col-span-1 mb-2 md:mb-0">
          <div class="mb-2">
            <span class="text-gray-400">电话：</span>
            <span>{{basisData?.service_phone}}</span>
          </div>
        </div>

        <!-- 邮箱 -->
        <div class="col-span-1 mb-2 1md:mb-0">
          <div class="mb-2">
            <span class="text-gray-400">公司名称：</span>
            <span>{{basisData?.company_name}}</span>
          </div>
        </div>

        <!-- 地址 -->
        <div class="col-span-1 mb-2 md:mb-0">
          <div class="mb-2">
            <span class="text-gray-400">地址：</span>
            <span>{{basisData?.company_address}}</span>
          </div>
        </div>
        <!-- LOGO区域 -->
        <div class="col-span-1 mb-2 md:mb-0">
          <div class="flex flex-wrap space-x-4 items-center" >
            <span class="text-gray-400" style="margin-bottom: 16px;">平台：</span>
            <div class="wechat-container relative">
              <i class="iconfont icon-weixin" style="top: 1px; font-size: 24px;background-color: black;margin-top: -10px;"></i>
              <div class="wechat-qrcode hidden absolute bottom-10 left-1/2 transform -translate-x-1/2 bg-white p-2 rounded-md shadow-lg z-10">
                <a-image :preview="false" :src="basisData.wechat_qrcode" alt="微信二维码" width="120" height="120" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 版权信息 -->
      <div class="text-center text-gray-400 text-sm">
        <p>{{basisData?.copyright}}</p>
      </div>
    </div>
  </footer>
</template>
  
<script setup>
import { ref, computed,onMounted } from "vue";
import officialApi from '@/api/master/officialWebsiteModule';
// 定义属性
const props = defineProps({
  baseUrl: {
    type: String,
    default: "/website/one/assets"
  }
});

// 页脚导航栏项目
const footerNavItems = ref([
  { title: "首页", key: "index", url: "/website/three/index" },
  { title: "关于我们", key: "about", url: "/website/three/about" },
  { title: "产品中心", key: "product", url: "/website/three/product" },
  { title: "核心服务", key: "service", url: "/website/three/service" },
  { title: "新闻中心", key: "news", url: "/website/three/news" },
  { title: "联系我们", key: "contact", url: "/website/three/contact" }
]);

// 判断导航项是否激活
function isActive(key) {
  const currentPath = window.location.pathname;
  
  if (key === 'about' && currentPath.includes('about')) {
    return true;
  } else if (key === 'news' && currentPath.includes('news')) {
    return true;
  } else if (key === 'service' && currentPath.includes('service')) {
    return true;
  } else if (key === 'contact' && currentPath.includes('contact')) {
    return true;
  } else if (key === 'product' && currentPath.includes('product')) {
    return true;
  } else if (key === 'index' && (currentPath.includes('index') || currentPath.endsWith('/website/three/'))) {
    return true;
  }
  
  return false;
}

// 处理导航点击事件
function handleNavClick(event, page) {
  event.preventDefault();
  
  // 根据页面类型跳转到不同页面
  if (page === 'contact') {
    window.location.href = '/website/three/contact';
  } else if (page === 'about') {
    window.location.href = '/website/three/about';
  } else if (page === 'product') {
    window.location.href = '/website/three/product';
  } else if (page === 'news') {
    window.location.href = '/website/three/news';
  } else if (page === 'service') {
    window.location.href = '/website/three/service';
  } else if (page === 'index') {
    window.location.href = '/website/three/index';
  }
}

const basisData = ref({});
const getData = () =>{
    officialApi.basisManagement.getData().then(res => {
        if(res.code == 200){
            basisData.value = res.data;
        }
    })
}
onMounted(() => {
    getData();
})
</script>
  
  <style scoped>
  .logo-image {
    max-height: 50px;
    width: 100%;
    height: auto;
  }
.flex-grow a {
  color: white;
  text-decoration: none;
  cursor: pointer;
}

.flex-grow a.active {
  color: #E70012; /* 使用蓝色作为高亮颜色，可以根据需要调整 */
  font-weight: 600;
  border-bottom: 2px solid #E70012;
  padding-bottom: 4px;
}

.flex-grow .nav-item {
  padding: 0px 20px;
}

/* 微信二维码悬浮样式 */
.wechat-container {
  cursor: pointer;
}

.wechat-container:hover .wechat-qrcode {
  display: block;
}

.wechat-qrcode {
  transition: all 0.3s ease;
}

/* 移动端样式优化 */
@media (min-width: 768px) {
  .flex-grow .nav-item {
    padding: 0px 40px;
  }
}

@media (max-width: 767px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>