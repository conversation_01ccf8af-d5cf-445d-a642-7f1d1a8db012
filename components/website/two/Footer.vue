<template>
  <footer class="footer">
    <div class="container">
      <div class="row footer-content-row">
        <!-- LOGO区域 -->
        <div class="col-md-3 col-6">
          <div class="footer-section logo-section">
            <img :src="basisData.logo_url" class="footer-logo" />
            <div class="social-icons">
              <div class="wechat-container" @click="toggleQrcode">
                <i class="iconfont icon-weixin"></i>
                <div class="qrcode-popup" :class="{ 'show-qrcode': isQrcodeVisible }">
                  <a-image :src="basisData.wechat_qrcode" alt="微信二维码" width="120" :preview="false"/>
                  <p>扫码关注我们</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 导航区域 -->
        <div class="col-md-3 col-6">
          <div class="footer-section">
            <h5>导航</h5>
            <ul class="footer-links">
              <li v-for="(item, i) in footerLinks.navigation" :key="`nav-${i}`">
                <a :href="item.url" @click.prevent="handleNavClick(item.url)">{{ item.title }}</a>
              </li>
            </ul>
          </div>
        </div>
        
        <!-- 产品分类区域 -->
        <div class="col-md-3 col-6">
          <div class="footer-section">
            <h5>产品分类</h5>
            <ul class="footer-links">
              <li v-for="(item, i) in footerLinks.service" :key="`service-${i}`">
                <a :href="'/website/two/product'">{{ item.name }}</a>
              </li>
            </ul>
          </div>
        </div>
        
        <!-- 联系我们区域 -->
        <div class="col-md-3 col-6">
          <div class="footer-section">
            <h5>联系我们</h5>
            <ul class="footer-links contact-info">
              <li>
                {{ basisData.company_name }}
              </li>
              <li>
                {{ basisData.company_address }}
              </li>
              <li>
                {{ basisData.service_phone }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      
      <!-- 版权信息 -->
      <div class="copyright">
        <p>©{{ basisData.copyright }}{{basisData.icp_number}}</p>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import officialApi from '@/api/master/officialWebsiteModule';
// 定义属性
const props = defineProps({
  // 基础URL，用于构建资源路径
  baseUrl: {
    type: String,
    default: ''
  }
});

// 产品分类数据
const footerLinks = ref({
  navigation: [
    { title: '首页', url: '/website/two/index' },
    { title: '新闻中心', url: '/website/two/news' },
    { title: '服务支持', url: '/website/two/support' },
    { title: '关于我们', url: '/website/two/about' }
  ],
  service: [
 ],

});

const basisData = ref({});
const isQrcodeVisible = ref(false);
const getData = () =>{
    officialApi.basisManagement.getData().then(res => {
        if(res.code == 200){
            basisData.value = res.data;
        }
    })
}

const getProductList = () => {
  officialApi.productManagement.getList({page: 1, pageSize: 5}).then(res => {
    if(res.code == 200){
      footerLinks.value.service = res.data.items;
    }
  })
}

// 页面加载完成后执行的逻辑
onMounted(() => {
    getData();
    getProductList();
  // 加载字体图标
  function loadCSS(href, id) {
    if (document.getElementById(id)) return;
    
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    link.id = id;
    document.head.appendChild(link);
  }
  
  // 加载Bootstrap和其他必要的CSS
  loadCSS('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css', 'bootstrap-css');
});

// 处理导航链接点击
function handleNavClick(url) {
  window.location.href = url;
}

// 切换微信二维码的显示/隐藏
function toggleQrcode(event) {
  event.stopPropagation();
  isQrcodeVisible.value = !isQrcodeVisible.value;
}

// 点击页面其他区域关闭二维码
function closeQrcode(event) {
  if (isQrcodeVisible.value && !event.target.closest('.wechat-container')) {
    isQrcodeVisible.value = false;
  }
}

// 监听点击事件
onMounted(() => {
  document.addEventListener('click', closeQrcode);
});

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', closeQrcode);
});
</script>

<style scoped lang="less">

.footer {
  background-color: #2c3e50;
  color: #ffffff;
  padding: 70px 0 20px;
 
}

.footer-logo {
  display: block;
  margin: 0 auto 20px;
}

.social-icons {
  display: flex;
  justify-content: center;
  margin-bottom: 15px;
}

.wechat-container {
  position: relative;
  cursor: pointer;
}

.wechat-container i {
  font-size: 24px;
  background-color: #757C84;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.qrcode-popup {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #fff;
  border-radius: 8px;
  padding: 10px 1px 1px 1px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  display: none;
  z-index: 1000;
  text-align: center;
  width: 160px;
  margin-top: 5px;
}

.qrcode-popup p {
  margin-top: 2px;
  font-size: 12px;
  color: #333;
}

/* 桌面端悬停显示 */
@media (min-width: 1024px) {
  .wechat-container:hover .qrcode-popup {
    display: block;
  }
}

/* 显示二维码的类 */
.qrcode-popup.show-qrcode {
  display: block;
}

.social-icon img {
  width: 24px;
  height: 24px;
  filter: brightness(0) invert(1); /* 将图标转为白色 */
  margin-right: 15px;
}

.footer-section {
  text-align: center;
  margin-bottom: 25px;
}

.footer-section h5 {
  color: white;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 15px;
  padding-bottom: 8px;
  position: relative;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: #ddd;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: white;
}

.contact-info li {
  color: #ddd;
  font-size: 14px;
  margin-bottom: 10px;
}

.copyright {
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 30px;
  padding-top: 20px;
}

.copyright p {
  color: #aaa;
  font-size: 14px;
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .footer {
    padding: 40px 0 15px;
  }
  
  .footer-section {
    margin-bottom: 20px;
    text-align: center;
  }
  
  .logo-section {
    text-align: center;
    margin-bottom: 30px;
  }
  
  .social-icons {
    justify-content: center;
  }
  
  .footer-content-row {
    margin-top: 10px;
  }
  
  .footer-links {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .footer-links li {
    margin-bottom: 8px;
  }
  
  .copyright {
    margin-top: 10px;
    padding-top: 15px;
  }
  
  .contact-info {
    font-size: 13px;
  }
}

/* 小屏幕手机适配 */
@media (max-width: 576px) {
  .footer {
    padding: 30px 0 10px;
  }
  
  .footer-section h5 {
    font-size: 15px;
    margin-bottom: 10px;
  }
  
  .footer-links a,
  .contact-info li {
    font-size: 13px;
  }
  
  .copyright p {
    font-size: 12px;
  }
  
  .footer-logo {
    margin-bottom: 15px;
  }
  
  .wechat-container i {
    width: 32px;
    height: 32px;
    font-size: 18px;
  }
  
  .qrcode-popup {
    width: 140px;
    padding: 5px;
  }
  
  /* 确保每行两个区域有足够的间距 */
  .footer-content-row > div:nth-child(3),
  .footer-content-row > div:nth-child(4) {
    margin-top: 20px;
  }
}
</style>
