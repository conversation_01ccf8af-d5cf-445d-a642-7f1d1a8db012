<template>
  <nav
    class="navbar navbar-expand-lg fixed-top"
    :class="{'not-homepage': !isHomePage || scrollY >= 56}"
    :style="{backgroundColor: navBgColor}"
  >
    <div class="container d-flex justify-content-between">
      <a class="navbar-brand" href="/website/two/index">
        <img :src="basisData.logo_url"  />
      </a>
      <button
        class="navbar-toggler"
        type="button"
        @click="toggleMenu"
        aria-controls="navbarNav"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>
      <div
        class="navbar-collapse justify-content-center"
        :class="{show: isMenuOpen}"
        id="navbarNav"
      >
        <ul class="navbar-nav">
          <li
            v-for="(item, index) in navItems"
            :key="index"
            class="nav-item"
            v-show="!item.mobileOnly || (item.mobileOnly && isMenuOpen)"
          >
            <a
             style="cursor: pointer;"
              class="nav-link"
              :class="{active: isActive(item.key)}"
              :data-page="item.key"
              @click="handleNavClick($event, item.key)"
            >{{ item.title }}</a>
          </li>
        </ul>
      </div>
      <div class="contact-btn-container d-none d-lg-flex align-items-center">
        <a href="/website/two/contact" class="contact-btn" @click.prevent="handleContactClick">联系我们</a>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import officialApi from '@/api/master/officialWebsiteModule';
// 定义属性
const props = defineProps({
  // 基础URL，用于构建资源路径
  baseUrl: {
    type: String,
    default: "/website/one/assets"
  }
});


const basisData = ref({});
const getData = () =>{
    officialApi.basisManagement.getData().then(res => {
        if(res.code == 200){
            basisData.value = res.data;
        }
    })
}


// 导航项目
const navItems = ref([
  { title: "首页", key: "index", url: "/website/two/index" },
  { title: "产品中心", key: "product", url: "/website/two/product" },
  { title: "服务支持", key: "support", url: "/website/two/support" },
  { title: "新闻中心", key: "news", url: "/website/two/news" },
  { title: "关于我们", key: "about", url: "/website/two/about" },
  { title: "联系我们", key: "contact", url: "/website/two/contact", mobileOnly: true }
]);

// 状态管理
const scrollY = ref(0);
const isHomePage = ref(true);
const navBgColor = computed(() => {
  return isHomePage.value ? "rgba(234, 238, 247)" : "#ffffff";
});
const isMenuOpen = ref(false);

// 切换菜单显示/隐藏
function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value;
}

// 获取当前页面路径并判断是否为首页
onMounted(() => {
  getData()
  const currentPath = window.location.pathname;
  isHomePage.value =
    currentPath === "/" ||
    currentPath.includes("index") ||
    currentPath.endsWith("/");

  // 设置当前页面高亮
  setActiveNavItem();
  
  // 如果当前是网站根路径或空路径，默认选中首页
  if (currentPath === "/" || 
      currentPath.endsWith("/website/two/") || 
      currentPath.endsWith("/website/two/index") || 
      currentPath === "") {
    // 这里可以保持现状，因为isActive函数已经处理了默认选中首页
  }
});





// 检查导航项目的可见性
function checkNavItemsVisibility() {
  // 在下一个微任务中执行，确保DOM已更新
  setTimeout(() => {
    const navItems = document.querySelectorAll(".nav-item");
    console.log("导航项目数量:", navItems.length);
    // 检查导航项目的可见性和样式
    navItems.forEach((item, index) => {
      const computedStyle = window.getComputedStyle(item);
    });
  }, 500);
}

// 判断导航项是否激活
function isActive(key) {
  const currentPath = window.location.pathname;

  // 如果是首页路径或空路径，默认选中index
  if (currentPath === "/" || 
      currentPath.endsWith("/website/two/") || 
      currentPath.endsWith("/website/two/index") || 
      currentPath === "") {
    return key === "index"; // 首页默认选中
  }

  // 其他页面正常判断
  if (key === "index" && (currentPath.includes("/index") || currentPath.endsWith("/website/two/"))) {
    return true;
  } else if (key === "product" && currentPath.includes("/product")) {
    return true;
  } else if (key === "support" && currentPath.includes("/support")) {
    return true;
  } else if (key === "news" && currentPath.includes("/news")) {
    return true;
  } else if (key === "about" && currentPath.includes("/about")) {
    return true;
  } else if (key === "contact" && currentPath.includes("/contact")) {
    return true;
  }

  return false;
}

// 设置当前页面的导航高亮
function setActiveNavItem() {
  // 逻辑已经在isActive函数中实现
  // Vue会自动应用CSS类
}

function handleNavClick(event, page) {
  event.preventDefault();

  // 根据页面类型跳转到不同页面
  if (page === "index") {
    window.location.href = "/website/two/index";
  } else if (page === "product") {
    window.location.href = "/website/two/product";
  } else if (page === "support") {
    window.location.href = "/website/two/support";
  } else if (page === "news") {
    window.location.href = "/website/two/news";
  } else if (page === "about") {
    window.location.href = "/website/two/about";
  } else if (page === "contact") {
    window.location.href = "/website/two/contact";
  }
}

// 处理联系我们按钮点击事件
function handleContactClick() {
  window.location.href = "/website/two/contact";
}

</script>

<style scoped>
.navbar {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1030;
  padding: 10px 0;
}

.not-homepage {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand img {
  height: 40px;
  transition: all 0.3s ease;
}

/* 桌面端导航菜单 */
.desktop-menu {
  display: flex;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0 15px;
}

.nav-link {
  color: #333 !important;
  font-weight: 500;
  padding: 0.5rem 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  display: block;
}

.navbar-nav .nav-item .nav-link {
  color: #333 !important;
}

/* 确保移动端菜单中所有导航项文字颜色一致 */
@media (max-width: 992px) {
  .navbar-nav .nav-item .nav-link {
    color: #333 !important;
  }
}
.contact-btn-container {
  margin-left: 20px;
}

.contact-btn {
  display: inline-block;
  padding: 6px 20px;
  background-color: #1A65FF;
  color: white !important;
  border-radius: 20px;
  line-height: 21px;
  color:white;
  font-size: 15px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.contact-btn:hover {
  background-color: #0d5ecf;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(18, 120, 255, 0.3);
}
.navbar.not-homepage .nav-link {
  color: #333 !important;
}

.nav-link:hover,
.nav-link.active {
  color: #1A65FF !important;
  transform: translateY(-2px);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
}

.menu-icon {
  display: block;
  width: 25px;
  height: 3px;
  background-color: #333;
  position: relative;
}

.menu-icon:before,
.menu-icon:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 3px;
  background-color: #333;
  left: 0;
}

.menu-icon:before {
  top: -8px;
}

.menu-icon:after {
  bottom: -8px;
}

/* 移动端导航菜单 */
.mobile-menu {
  display: none;
  position: absolute;
  top: 60px;
  left: 0;
  width: 100%;
  background-color: white;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  z-index: 1029;
}

.mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-nav-item {
  border-bottom: 1px solid #eee;
}

.mobile-nav-link {
  display: block;
  padding: 15px 20px;
  color: #333 !important;
  text-decoration: none;
  text-align: center;
  font-weight: 500;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: #007bff !important;
  background-color: #f8f9fa;
}

/* 响应式布局 */
@media (max-width: 992px) {
  .navbar {
    background-color: white !important;
  }

  .navbar-nav {
    flex-direction: column;
    margin-top: 10px;
    width: 100%;
    align-items: center; /* 水平居中 */
    text-align: center; /* 文本居中 */
  }

  .navbar-collapse {
    position: absolute;
    top: 56px;
    left: 0;
    right: 0;
    background-color: white;
    padding: 10px;
    width: 100%;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    padding-top: 0;
    padding-bottom: 0;
  }

  .navbar-collapse.show {
    max-height: 300px; /* 足够容纳菜单项 */
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .nav-item {
    width: 100%;
    text-align: center;
  }

  .navbar .nav-link {
    padding: 0.7rem 1rem; /* 增加垂直空间，使点击区域更大 */
    text-align: center;
    display: block;
    color: #333 !important;
  }

  .navbar .nav-link:hover,
  .navbar .nav-link.active {
    color: #007bff !important;
  }

  .navbar-toggler {
    border-color: rgba(0, 0, 0, 0.1);
    z-index: 1031;
  }

  .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  }
}
</style>
