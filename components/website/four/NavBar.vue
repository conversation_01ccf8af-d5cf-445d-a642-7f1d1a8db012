<template>
  <nav
    class="navbar navbar-expand-lg fixed-top"
    :class="{'not-homepage': !isHomePage || scrollY >= 56}"
    :style="{backgroundColor: navBgColor}"
  >
    <!-- 添加调试信息 -->
    <!-- <div style="position: fixed; top: 100px; left: 10px; background: white; z-index: 2000; padding: 10px;">
        导航项目: {{ navItems.length }}<br>
        isHomePage: {{ isHomePage }}<br>
        当前路径: {{ window?.location?.pathname }}
    </div>-->
    <div class="container d-flex justify-content-between">
      <a class="navbar-brand" href="/website/four/index">
        <img :src="currentLogoUrl" />
      </a>
      <button
        class="navbar-toggler"
        type="button"
        @click="toggleMenu"
        aria-controls="navbarNav"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="navbar-collapse" :class="{show: isMenuOpen}" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li v-for="(item, index) in navItems" :key="index" class="nav-item">
            <a
              class="nav-link"
              :class="{active: isActive(item.key)}"
              :data-page="item.key"
              @click="handleNavClick($event, item.key)"
            >{{ item.title }}</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>
</template>
  
  <script setup>
import { ref, computed, onMounted, onUnmounted } from "vue";
import officialApi from "@/api/master/officialWebsiteModule";

// 导航项目
const navItems = ref([
  { title: "首页", key: "index", url: "#" },
  { title: "关于我们", key: "about", url: "#" },
  { title: "产品中心", key: "product", url: "#" },
  { title: "新闻中心", key: "news", url: "#" },
  { title: "社会招聘", key: "joinus", url: "#" },
  { title: "联系我们", key: "contact", url: "#" }
]);

// 状态管理
const scrollY = ref(0);
const isHomePage = ref(true);
const navBgColor = ref("rgba(0, 0, 0, 0.4)");
const isMenuOpen = ref(false);

// 切换菜单显示/隐藏
function toggleMenu() {
  isMenuOpen.value = !isMenuOpen.value;
}

const basisData = ref({});
const getData = () => {
  officialApi.basisManagement.getData().then(res => {
    if (res.code == 200) {
      basisData.value = res.data;
    }
    if (res.data.banner_url) {
      basisData.value.banner_url = res.data.banner_url.split(",");
    }
    console.log(basisData.value, "xxxxx");
  });
};

// 获取当前页面路径并判断是否为首页
onMounted(() => {
  getData();
  const currentPath = window.location.pathname;
  isHomePage.value =
    currentPath === "/" ||
    currentPath.includes("index") ||
    currentPath.endsWith("/");

  // 如果不是首页，直接使用白色背景
  if (!isHomePage.value) {
    navBgColor.value = "white";
  } else {
    // 如果是首页，添加滚动监听
    window.addEventListener("scroll", handleScroll);
  }

  // 设置当前页面高亮
  setActiveNavItem();
});

onUnmounted(() => {
  // 移除滚动监听器
  window.removeEventListener("scroll", handleScroll);
});

// 处理滚动事件
function handleScroll() {
  scrollY.value = window.scrollY;

  if (scrollY.value >= 56) {
    navBgColor.value = "white";
    // 这里不需要手动改变文字颜色，因为我们使用 CSS 类来切换颜色
    // 添加 not-homepage 类将自动应用相关样式
  } else {
    navBgColor.value = "rgba(0, 0, 0, 0.4)";
  }
  // 手动检查样式，确保导航项目可见
  checkNavItemsVisibility();
}

// 检查导航项目的可见性
function checkNavItemsVisibility() {
  // 在下一个微任务中执行，确保DOM已更新
  setTimeout(() => {
    const navItems = document.querySelectorAll(".nav-item");
    console.log("导航项目数量:", navItems.length);
    // 检查导航项目的可见性和样式
    navItems.forEach((item, index) => {
      const computedStyle = window.getComputedStyle(item);
    });
  }, 500);
}

// 判断导航项是否激活
function isActive(key) {
  const currentPath = window.location.pathname;

  if (key === "about" && currentPath.includes("about")) {
    return true;
  } else if (key === "news" && currentPath.includes("news")) {
    return true;
  } else if (
    key === "joinus" &&
    currentPath.includes("joinus")
  ) {
    return true;
  } else if (key === "contact" && currentPath.includes("contact")) {
    return true;
  } else if (key === "product" && currentPath.includes("product")) {
    return true;
  } else if (key === "index" && currentPath.includes("index")) {
    return true;
  }

  return false;
}

// 设置当前页面的导航高亮
function setActiveNavItem() {
  // 逻辑已经在isActive函数中实现
  // Vue会自动应用CSS类
}

// 处理导航点击事件
function handleNavClick(event, page) {
  event.preventDefault();

  // 根据页面类型跳转到不同页面
  if (page === "joinus") {
    window.location.href = "/website/four/joinus";
  } else if (page === "about") {
    window.location.href = "/website/four/about";
  } else if (page === "product") {
    window.location.href = "/website/four/product";
  } else if (page === "news") {
    window.location.href = "/website/four/news";
  } else if (page === "contact") {
    window.location.href = "/website/four/contact";
  } else if (page === "index") {
    window.location.href = "/website/four/index";
  }
}

// 计算当前应该显示的logo URL
const currentLogoUrl = computed(() => {
  return basisData.value.logo_url;
});
</script>
  
<style scoped>
  .navbar-nav .nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 1rem;
    right: 1rem;
    height: 2px;
    background-color: #F44217 !important;
    transition: all 0.3s ease;
}
.navbar {
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1030;
  padding: 10px 0;
}

.not-homepage {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand img {
  height: 40px;
  transition: all 0.3s ease;
}

/* 桌面端导航菜单 */
.desktop-menu {
  display: flex;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0 15px;
}

.nav-link {
  cursor: pointer;
  color: #fff !important;
  font-weight: 500;
  padding: 0.5rem 1rem;
  text-decoration: none;
  transition: all 0.3s ease;
  display: block;
}

.nav-link:hover,
.nav-link.active {
  color: #F44217 !important;
  transform: translateY(-2px);
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
}

.menu-icon {
  display: block;
  width: 25px;
  height: 3px;
  background-color: #333;
  position: relative;
}

.menu-icon:before,
.menu-icon:after {
  content: "";
  position: absolute;
  width: 100%;
  height: 3px;
  background-color: #333;
  left: 0;
}

.menu-icon:before {
  top: -8px;
}

.menu-icon:after {
  bottom: -8px;
}

/* 移动端导航菜单 */
.mobile-menu {
  display: none;
  position: absolute;
  top: 60px;
  left: 0;
  width: 100%;
  background-color: white;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  z-index: 1029;
}

.mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-nav-item {
  border-bottom: 1px solid #eee;
}

.mobile-nav-link {
  display: block;
  padding: 15px 20px;
  color: #333 !important;
  text-decoration: none;
  text-align: center;
  font-weight: 500;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
  color: #007bff !important;
  background-color: #f8f9fa;
}

/* 响应式布局 */
@media (max-width: 992px) {
  .navbar {
    background-color: white !important;
  }

  .navbar-nav {
    flex-direction: column;
    margin-top: 10px;
    width: 100%;
    align-items: center; /* 水平居中 */
    text-align: center; /* 文本居中 */
  }

  .navbar-collapse {
    position: absolute;
    top: 56px;
    left: 0;
    right: 0;
    background-color: white;
    padding: 10px;
    width: 100%;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out, padding 0.3s ease-in-out;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    padding-top: 0;
    padding-bottom: 0;
  }

  .navbar-collapse.show {
    max-height: 300px; /* 足够容纳菜单项 */
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .nav-item {
    width: 100%;
    text-align: center;
  }

  .navbar .nav-link {
    padding: 0.7rem 1rem; /* 增加垂直空间，使点击区域更大 */
    text-align: center;
    display: block;
    color: #333 !important;
  }

  .navbar .nav-link:hover,
  .navbar .nav-link.active {
    color: #007bff !important;
  }

  .navbar-toggler {
    border-color: rgba(0, 0, 0, 0.1);
    z-index: 1031;
  }

  .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  }
}
</style>
  