<template>
  <footer class="bg-dark-blue text-white py-8" style="background-color: #2B2B2B;">
    <div class="container mx-auto px-4">
      <!-- LOGO、联系信息和二维码区域 -->
      <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <!-- LOGO区域 -->
        <div class="flex-shrink-0 mb-4 md:mb-0">
          <img :src="basisData.logo_url || '/placeholder-logo.png'" alt="Logo" class="logo-image" :preview="false"/>
        </div>
        
        <!-- 竖线分隔符 -->
        <div class="hidden md:block h-20 border-l border-gray-600 "></div>
        
        <!-- 联系信息区域 -->
        <div class="flex flex-col space-y-2 text-sm mb-4 md:mb-0">
          <div class="contact_title">联系我们</div>
          <div class="flex items-center">
            <span class="text-gray-400 mr-2">联系电话：</span>
            <span>{{ basisData.service_phone }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-gray-400 mr-2">公司名称：</span>
            <span>{{ basisData.company_name }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-gray-400 mr-2">地址：</span>
            <span>{{ basisData.company_address }}</span>
          </div>
        </div>
        
        <!-- 二维码区域 -->
        <div class="flex space-x-8">
          <div class="flex flex-col items-center">
            <a-image 
            :preview="false"
              :src="basisData.wechat_qrcode || '/placeholder-qrcode.png'" 
              alt="微信" 
              :width="100" 
              :height="100" 
              class="mb-2" 
            />
            <span class="text-sm text-gray-400">微信</span>
          </div>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="border-b border-gray-700 my-4"></div>
      
      <!-- 版权信息 -->
      <div class="text-center text-gray-400 text-sm mt-6">
        <p>{{ basisData.copyright }}</p>
      </div>
    </div>
  </footer>
</template>
    
<script setup>
import { ref, computed, onMounted } from "vue";
import officialApi from "@/api/master/officialWebsiteModule";
// 定义属性
const props = defineProps({
  baseUrl: {
    type: String,
    default: "/website/one/assets"
  }
});



const basisData = ref({});
const getData = () => {
  officialApi.basisManagement.getData().then(res => {
    if (res.code == 200) {
      basisData.value = res.data;
    }
  });
};
onMounted(() => {
  getData();
});
</script>
    
<style scoped>
.logo-image {
    max-height: 50px;
    width: 100%;
    height: auto;
}

.contact_title{
    margin-bottom: 16px;
    font-weight: 600;
    font-size: 18px;
    color: #FFFFFF;
}
.flex-grow a {
  color: white;
  text-decoration: none;
  cursor: pointer;
}

.flex-grow a.active {
  color: #e70012; /* 使用蓝色作为高亮颜色，可以根据需要调整 */
  font-weight: 600;
  border-bottom: 2px solid #e70012;
  padding-bottom: 4px;
}

.flex-grow .nav-item {
  padding: 0px 20px;
}

/* 微信二维码悬浮样式 */
.wechat-container {
  cursor: pointer;
}

.wechat-container:hover .wechat-qrcode {
  display: block;
}

.wechat-qrcode {
  transition: all 0.3s ease;
}

/* 移动端样式优化 */
@media (min-width: 768px) {
  .flex-grow .nav-item {
    padding: 0px 40px;
  }
}

@media (max-width: 767px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
</style>