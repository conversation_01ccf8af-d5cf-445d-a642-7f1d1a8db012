<template>
  <div class="advantages-section" :style="{ backgroundImage: `url('/website/one/assets/images/bg3.png')` }">
    <div class="container">
      <div class="row">
        <div class="col-md-12">
          <h2 class="section-title">我们的三大优势</h2>
        </div>
      </div>
      <div class="advantages-slider">
        <div class="advantages-slider-inner">
          <div v-for="(advantage, index) in enterpriseInformations" :key="index" class="advantage-item">
            <div class="advantage-image">
              <a-image :src="advantage.cover_image" :width="650" :height="338" :preview="false" fit="cover"/>
            </div>
            <div class="advantage-content">
              <div class="advantage-number">{{ index + 1 }}</div>
              <h3 class="advantage-title">{{ advantage.title }}</h3>
              <p class="advantage-description">{{ advantage.description }}</p>
              <div class="slider-controls">
                <button class="prev-btn" @click="prevSlide"><icon-left /></button>
                <button class="next-btn" @click="nextSlide"><icon-right /></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import officialApi from '@/api/master/officialWebsiteModule';
import { ref, onMounted } from 'vue';

// 优势数据
const advantages = ref([
  {
    number: '01',
    title: '专利申请服务',
    description: '提供专利申请，采用前沿专利技术，外观设计专利的申请代理服务。包括专利申请文件的撰写、递交、答复审查意见等，我们为各种申请人全程提供专门的专利申请服务，制定合理的申请策略，提高专利申请的成功率。',
    image: '/website/one/assets/images/advantage1.jpg'
  },
  {
    number: '02',
    title: '技术研发支持',
    description: '我们拥有一支高素质的研发团队，专注于前沿技术的研究与应用。为客户提供从概念设计到产品实现的全流程技术支持，帮助企业快速实现技术创新，提升产品竞争力。',
    image: '/website/one/assets/images/advantage2.jpg'
  },
  {
    number: '03',
    title: '全球市场拓展',
    description: '依托我们的国际化资源网络，为企业提供全球市场拓展服务。包括市场调研、渠道建设、品牌推广等全方位支持，助力企业产品和服务在国际市场上取得成功。',
    image: '/website/one/assets/images/advantage3.jpg'
  }
]);

// 当前显示的优势索引
const currentIndex = ref(0);

// 下一张幻灯片
const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % advantages.value.length;
  updateSlider();
};

// 上一张幻灯片
const prevSlide = () => {
  currentIndex.value = (currentIndex.value - 1 + advantages.value.length) % advantages.value.length;
  updateSlider();
};

// 更新幻灯片位置
const updateSlider = () => {
  const slider = document.querySelector('.advantages-slider-inner');
  if (slider) {
    slider.style.transform = `translateX(-${currentIndex.value * 100}%)`;
  }
};


// 自动播放
let autoplayInterval;
const startAutoplay = () => {
  autoplayInterval = setInterval(() => {
    nextSlide();
  }, 10000);
};

// 停止自动播放
const stopAutoplay = () => {
  clearInterval(autoplayInterval);
};



//企业信息
const enterpriseInformations = ref([]);
const getEnterpriseInformations = () =>{

    officialApi.getEnterpriseInformations({page:1,pageSize:3,cate_name:'服务支持'}).then(res => {
        if(res.code == 200){
            enterpriseInformations.value = res.data.items;
        }
    })
}

onMounted(() => {
  getEnterpriseInformations();
  startAutoplay();
  // 鼠标悬停时停止自动播放
  const sliderElement = document.querySelector('.advantages-slider');
  if (sliderElement) {
    sliderElement.addEventListener('mouseenter', stopAutoplay);
    sliderElement.addEventListener('mouseleave', startAutoplay);
  }
});
</script>

<style scoped>
.advantages-section {
  padding: 80px 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  color: #fff;
}

.advantages-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.advantages-section .container {
  position: relative;
  z-index: 2;
}

.section-title {
  text-align: center;
  margin-bottom: 50px;
  font-size: 2.5rem;
  font-weight: 700;
  color: #fff;
}

.advantages-slider {
  padding: 20px;
  background-color: #fff;
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  max-width: 1200px;
}

.advantages-slider-inner {
  display: flex;
  transition: transform 0.5s ease;
}

.advantage-item {
  min-width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}

.advantage-content {
  flex: 1;
  padding-left: 30px;
}

.advantage-number {
  font-size: 3rem;
  font-weight: 700;
  color: #ff6b00;
  margin-bottom: 10px;
}

.advantage-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.advantage-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #333;
}

.advantage-image {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slider-controls {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.prev-btn,
.next-btn {
  background-color: #ff6b00;
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 10px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.prev-btn:hover,
.next-btn:hover {
  background-color: #e05a00;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .advantage-item {
    flex-direction: column;
  }
  
  .advantage-content {
    padding-right: 0;
    padding-bottom: 30px;
    text-align: center;
  }
  
  .slider-controls {
    display: none;
  }
  
  .advantage-image {
    width: 100%;
  }
  
  .advantage-image :deep(img) {
    max-width: 100%;
    height: auto;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
  }
  
  .advantage-number {
    font-size: 2.5rem;
  }
  
  .advantage-title {
    font-size: 1.5rem;
  }
  
  .advantages-section {
    padding: 60px 0;
  }
}

@media (max-width: 576px) {
  .section-title {
    font-size: 1.8rem;
    margin-bottom: 30px;
  }
  
  .advantage-number {
    font-size: 2rem;
  }
  
  .advantage-title {
    font-size: 1.3rem;
  }
  
  .advantage-description {
    font-size: 0.9rem;
  }
  
  .advantages-section {
    padding: 40px 0;
  }
  
  .advantage-image :deep(img) {
    max-width: 100%;
    height: auto;
  }
}
</style>
