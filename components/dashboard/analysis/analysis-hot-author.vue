<script setup lang="ts">
import { ref } from 'vue'

const tableData = ref([
  { author: '张三', content: '5000字', click: '9000' },
  { author: '李四', content: '4500字', click: '12000' },
  { author: '王五', content: '3500字', click: '7000' },
  { author: '赵六', content: '4800字', click: '8500' },
  { author: '刘七', content: '5200字', click: '11000' },
  { author: '孙八', content: '3000字', click: '6000' },
  { author: '周九', content: '5500字', click: '9800' },
])

const columns = [
  { dataIndex: 'index', title: '排名', width: 80, 
    render: ({ rowIndex }) => rowIndex + 1 
  },
  { dataIndex: 'author', title: '作者' },
  { dataIndex: 'content', title: '内容量' },
  { dataIndex: 'click', title: '点击量' },
]
</script>

<template>
  <div class="mine-card w-auto xl:w-4/12 xl:ml-4">
    <div class="text-base mt-1.5">
      <div>热门作者榜单</div>
    </div>

    <div class="mt-5">
      <a-table 
        :columns="columns" 
        :data="tableData" 
        :pagination="false"
        :bordered="false"
        stripe
      />
    </div>
  </div>
</template>

<style scoped>
.mine-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  margin-bottom: 16px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.mt-1\.5 {
  margin-top: 0.375rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.w-auto {
  width: auto;
}

.xl\:w-4\/12 {
  width: 33.333333%;
}

.xl\:ml-4 {
  margin-left: 1rem;
}

@media (min-width: 1280px) {
  .xl\:w-4\/12 {
    width: 33.333333%;
  }
  
  .xl\:ml-4 {
    margin-left: 1rem;
  }
}
</style>
