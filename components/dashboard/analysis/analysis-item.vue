<script lang="ts" setup>
import getAnalysisData from '~/data/analysis'
import { useEcharts } from '~/composables/useEcharts'

const props = withDefaults(defineProps<{
  name: string
  title: string
  chartType: string
}>(), {
  name: 'visitors',
  title: '',
  chartType: 'line',
})

const renderData = ref<Record<string, any>>({
  count: 0,
  growth: 0,
  chartData: [],
})

const chartOption = ref({})

const { lineChartOptionsFactory, barChartOptionsFactory, pieChartOptionsFactory } = getAnalysisData()

const { chartOption: lineChartOption, data: lineData }
  = lineChartOptionsFactory()
const { chartOption: barChartOption, data: barData }
  = barChartOptionsFactory()
const { chartOption: pieChartOption, data: pieData }
  = pieChartOptionsFactory()

const ele = ref()
const { setOption } = useEcharts(ele)

switch (props.name) {
  case 'visitors': {
    const { getVisitorsData } = getAnalysisData()
    renderData.value = getVisitorsData()
    break
  }
  case 'published': {
    const { getPublishedData } = getAnalysisData()
    renderData.value = getPublishedData()
    break
  }
  case 'contentTimer': {
    const { getContentTimerData } = getAnalysisData()
    renderData.value = getContentTimerData()
    break
  }
}

if (props.chartType === 'bar') {
  renderData.value.chartData.forEach((el, idx) => {
    barData.value.push({
      value: el.y,
      itemStyle: {
        color: idx % 2 ? '#2CAB40' : '#86DF6C',
      },
    })
  })
  chartOption.value = barChartOption.value
}
else if (props.chartType === 'line') {
  renderData.value.chartData.forEach((el) => {
    if (el.name === '2021') {
      lineData.value[0].push(el.y)
    }
    else {
      lineData.value[1].push(el.y)
    }
  })
  chartOption.value = lineChartOption.value
}
else {
  renderData.value.chartData.forEach((el) => {
    pieData.value.push(el)
  })
  chartOption.value = pieChartOption.value
}
setOption(chartOption.value)
</script>

<template>
  <div class="mine-layout">
    <div class="content-wrap">
      <div class="content">
        <a-statistic :value="renderData.count">
          <template #title>
            <div class="text-base">
              {{ props.title }}
            </div>
          </template>
        </a-statistic>
        <div class="desc mt-2">
          <div class="text-sm">
            较昨日
          </div>
          <div class="flex items-center text-red-500">
            {{ renderData.growth }}
            <ma-svg-icon name="ic:baseline-arrow-upward" />
          </div>
        </div>
      </div>
      <div class="chart">
        <div ref="ele" class="h-[100px]" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.content-wrap {
  display: flex;
  border-radius: 0.25rem;
  margin-top: 1.25rem;
  padding: 0.75rem;
  background-color: rgba(var(--primary-6), 0.05);
  gap: 0.75rem;
  align-items: center;
}

.chart {
  width: calc(100% - 100px);
}

.text-red-500 {
  color: #f56c6c;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}
</style>
