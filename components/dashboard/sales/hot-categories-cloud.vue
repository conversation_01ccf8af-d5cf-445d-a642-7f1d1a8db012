<script setup lang="ts">
import { useEcharts } from '~/composables/useEcharts'
import { ref, onMounted, watch } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '热销类目'
  },
  data: {
    type: Array,
    required: true
  }
})

const chartRef = ref()
const { setOption } = useEcharts(chartRef)

const renderChart = () => {
  
  if (props.data && props.data.length > 0) {
    const option = {
      tooltip: {
        show: true
      },
      series: [{
        type: 'wordCloud',
        shape: 'circle',
        left: 'center',
        top: 'center',
        width: '90%',
        height: '90%',
        right: null,
        bottom: null,
        sizeRange: [12, 30],
        rotationRange: [-90, 90],
        rotationStep: 45,
        gridSize: 8,
        drawOutOfBound: false,
        textStyle: {
          fontFamily: 'sans-serif',
          fontWeight: 'bold',
          color: function () {
            return 'rgb(' + [
              Math.round(Math.random() * 160 + 50),
              Math.round(Math.random() * 160 + 50),
              Math.round(Math.random() * 160 + 50)
            ].join(',') + ')';
          }
        },
        emphasis: {
          focus: 'self',
          textStyle: {
            shadowBlur: 10,
            shadowColor: '#333'
          }
        },
        data: props.data
      }]
    }
    
    setOption(option)
  }
}

onMounted(() => {
  renderChart()
})

watch(() => props.data, () => {
  renderChart()
}, { deep: true })
</script>

<template>
  <div class="hot-categories-card">
    <div class="categories-header">
      <div class="categories-title">{{ title }}</div>
    </div>
    <div class="categories-content">
      <div class="categories-chart" ref="chartRef"></div>
    </div>
  </div>
</template>

<style scoped>
.hot-categories-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.categories-header {
  margin-bottom: 16px;
}

.categories-title {
  font-size: 14px;
  color: #4E5969;
}

.categories-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.categories-chart {
  flex: 1;
  min-height: 200px;
}
</style>
