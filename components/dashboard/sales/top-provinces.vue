<script setup lang="ts">
import { ref, computed } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '本月热销省份TOP5'
  },
  data: {
    type: Array,
    required: true
  },
  type: {
    type: String,
    default: 'salesAmount' // 'salesAmount' 或 'orderCount'
  }
})

const typeOptions = [
  { label: '销售额', value: 'salesAmount' },
  { label: '订单量', value: 'orderCount' }
]

const displayType = ref(props.type || 'salesAmount')

const maxValue = computed(() => {
  if (!props.data || props.data.length === 0) return 0
  return Math.max(...props.data.map(item => item.value || 0))
})

const getBarWidth = (value) => {
  if (maxValue.value === 0) return '0%'
  return ((value || 0) / maxValue.value * 100) + '%'
}

const getValueText = (value) => {
  if (!value) return displayType.value === 'salesAmount' ? '¥0' : '0单'
  
  if (displayType.value === 'salesAmount') {
    return '¥' + value.toLocaleString('zh-CN')
  } else {
    return value + '单'
  }
}
</script>

<template>
  <div class="top-provinces-card">
    <div class="provinces-header">
      <div class="provinces-title">{{ title }}</div>
      <div class="provinces-type">
        <a-radio-group v-model="displayType" type="button" size="small">
          <a-radio v-for="option in typeOptions" :key="option.value" :value="option.value">
            {{ option.label }}
          </a-radio>
        </a-radio-group>
      </div>
    </div>
    <div class="provinces-content">
      <div class="provinces-list">
        <div v-for="(item, index) in data" :key="index" class="province-item">
          <div class="item-name">{{ item.name }}</div>
          <div class="item-bar-wrapper">
            <div class="item-bar" :style="{ width: getBarWidth(item.value) }"></div>
          </div>
          <div class="item-value">{{ getValueText(item.value) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.top-provinces-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.provinces-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.provinces-title {
  font-size: 14px;
  color: #4E5969;
}

.provinces-content {
  flex: 1;
}

.provinces-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.province-item {
  display: flex;
  align-items: center;
}

.item-name {
  width: 70px;
  font-size: 12px;
  color: #4E5969;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-bar-wrapper {
  flex: 1;
  height: 8px;
  background-color: #F2F3F5;
  border-radius: 4px;
  margin: 0 8px;
  overflow: hidden;
}

.item-bar {
  height: 100%;
  background-color: #165DFF;
  border-radius: 4px;
}

.item-value {
  width: 80px;
  font-size: 12px;
  color: #1D2129;
  text-align: right;
}
</style>
