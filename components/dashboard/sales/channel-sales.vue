<script setup lang="ts">
import { ref, computed, defineProps, onMounted, watch } from 'vue';
import { useEcharts } from '~/composables/useEcharts';

const props = defineProps({
  title: {
    type: String,
    default: '渠道销售额'
  },
  channels: {
    type: Array,
    required: true
  },
  selected: {
    type: String,
    default: ''
  },
  data: {
    type: Array,
    default: () => []
  }
})

const selectedChannel = ref(props.selected)

const hasData = computed(() => {
  return props.data && props.data.length > 0
})

const chartRef = ref()
const { setOption } = useEcharts(chartRef)

const renderChart = () => {
  if (hasData.value) {
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)'
      },
      series: [
        {
          type: 'pie',
          radius: ['50%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false
          },
          emphasis: {
            label: {
              show: false
            }
          },
          labelLine: {
            show: false
          },
          data: props.data
        }
      ],
      color: ['#165DFF', '#0FC6C2', '#722ED1', '#F7BA1E', '#F53F3F']
    }
    
    setOption(option)
  }
}

onMounted(() => {
  renderChart()
})

watch(() => props.data, () => {
  renderChart()
}, { deep: true })
</script>

<template>
  <div class="channel-sales-card">
    <div class="channel-header">
      <div class="channel-title">{{ title }}</div>
      <div class="channel-selector">
        <a-select v-model="selectedChannel" size="small" style="width: 120px">
          <a-option v-for="channel in channels" :key="channel" :value="channel">
            {{ channel }}
          </a-option>
        </a-select>
      </div>
    </div>
    <div class="channel-content">
      <template v-if="hasData">
        <!-- 有数据时显示图表 -->
        <div class="channel-chart" ref="chartRef"></div>
      </template>
      <template v-else>
        <!-- 无数据时显示空状态 -->
        <div class="channel-empty">
          <div class="empty-icon">
            <icon-inbox :size="48" :style="{color: '#C9CDD4'}" />
          </div>
          <div class="empty-text">暂无数据</div>
          <div class="empty-desc">该渠道暂无销售数据</div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.channel-sales-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.channel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.channel-title {
  font-size: 14px;
  color: #4E5969;
}

.channel-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.channel-chart {
  width: 100%;
  height: 100%;
}

.channel-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.empty-icon {
  margin-bottom: 8px;
}

.empty-text {
  font-size: 14px;
  color: #1D2129;
  margin-bottom: 4px;
}

.empty-desc {
  font-size: 12px;
  color: #86909C;
}
</style>
