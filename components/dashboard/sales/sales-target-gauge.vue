<script setup lang="ts">
import { useEcharts } from '~/composables/useEcharts'

const props = defineProps({
  target: {
    type: Number,
    required: true
  },
  current: {
    type: Number,
    required: true
  },
  rate: {
    type: Number,
    required: true
  },
  color: {
    type: String,
    default: '#165DFF'
  }
})

const chartRef = ref()
const { setOption } = useEcharts(chartRef)

const formattedTarget = computed(() => {
  return props.target.toLocaleString('zh-CN')
})

const formattedCurrent = computed(() => {
  return props.current.toLocaleString('zh-CN')
})

const formattedRate = computed(() => {
  return props.rate > 100 ? 100 : props.rate.toFixed(2)
})

onMounted(() => {
  const option = {
    series: [
      {
        type: 'gauge',
        startAngle: 90,
        endAngle: -270,
        pointer: {
          show: false
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          itemStyle: {
            color: props.color
          }
        },
        axisLine: {
          lineStyle: {
            width: 10,
            color: [
              [1, '#E5E6EB']
            ]
          }
        },
        splitLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        title: {
          show: false
        },
        detail: {
          valueAnimation: true,
          offsetCenter: [0, 0],
          formatter: function(value) {
            return '{value|' + value.toFixed(2) + '%}\n{label|完成率}';
          },
          rich: {
            value: {
              fontSize: 24,
              fontWeight: 'bold',
              color: '#1D2129'
            },
            label: {
              fontSize: 14,
              color: '#4E5969',
              padding: [5, 0, 0, 0]
            }
          }
        },
        data: [
          {
            value: props.rate > 100 ? 100 : props.rate
          }
        ]
      }
    ]
  }
  
  setOption(option)
})
</script>

<template>
  <div class="sales-target-card">
    <div class="target-header">
      <div class="target-title">销售目标</div>
    </div>
    <div class="target-content">
      <div class="target-info">
        <div class="target-item">
          <div class="item-label">月度目标</div>
          <div class="item-value">¥{{ formattedTarget }}</div>
        </div>
        <div class="target-item">
          <div class="item-label">当前完成</div>
          <div class="item-value">¥{{ formattedCurrent }}</div>
        </div>
      </div>
      <div class="target-chart" ref="chartRef"></div>
    </div>
  </div>
</template>

<style scoped>
.sales-target-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.target-header {
  margin-bottom: 16px;
}

.target-title {
  font-size: 14px;
  color: #4E5969;
}

.target-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.target-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.target-item {
  display: flex;
  flex-direction: column;
}

.item-label {
  font-size: 12px;
  color: #86909C;
  margin-bottom: 4px;
}

.item-value {
  font-size: 16px;
  font-weight: 600;
  color: #1D2129;
}

.target-chart {
  flex: 1;
  min-height: 150px;
}
</style>
