<script setup lang="ts">
import { useEcharts } from '~/composables/useEcharts'

const props = defineProps({
  title: {
    type: String,
    default: '月度销售数据'
  },
  months: {
    type: Array,
    required: true
  },
  data: {
    type: Array,
    required: true
  }
})

const chartRef = ref()
const { setOption } = useEcharts(chartRef)

onMounted(() => {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: props.data.map(item => item.name),
      bottom: 0,
      itemWidth: 10,
      itemHeight: 10,
      textStyle: {
        fontSize: 12,
        color: '#4E5969'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: props.months,
        axisLine: {
          lineStyle: {
            color: '#E5E6EB'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          color: '#86909C',
          fontSize: 12
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#E5E6EB',
            type: 'dashed'
          }
        },
        axisLabel: {
          color: '#86909C',
          fontSize: 12
        }
      }
    ],
    series: props.data.map((item, index) => {
      const colors = ['#165DFF', '#0FC6C2', '#722ED1', '#F7BA1E'];
      return {
        name: item.name,
        type: 'line',
        stack: item.name.includes('售后') ? '售后' : '销售',
        smooth: true,
        emphasis: {
          focus: 'series'
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.2
        },
        lineStyle: {
          width: 2
        },
        itemStyle: {
          color: colors[index % colors.length]
        },
        data: item.data
      };
    })
  };
  
  setOption(option)
})
</script>

<template>
  <div class="monthly-sales-card">
    <div class="monthly-header">
      <div class="monthly-title">{{ title }}</div>
      <div class="monthly-actions">
        <a-radio-group type="button" size="small" default-value="month">
          <a-radio value="month">本月</a-radio>
          <a-radio value="year">年度</a-radio>
        </a-radio-group>
      </div>
    </div>
    <div class="monthly-content">
      <div class="monthly-chart" ref="chartRef"></div>
    </div>
  </div>
</template>

<style scoped>
.monthly-sales-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monthly-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.monthly-title {
  font-size: 14px;
  color: #4E5969;
}

.monthly-content {
  flex: 1;
}

.monthly-chart {
  height: 300px;
  width: 100%;
}
</style>
