<script setup lang="ts">
import { useEcharts } from '~/composables/useEcharts'
import { IconUp, IconDown } from '@arco-design/web-vue/es/icon'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [Number, String],
    required: true
  },
  prefix: {
    type: String,
    default: ''
  },
  suffix: {
    type: String,
    default: ''
  },
  growth: {
    type: Number,
    default: 0
  },
  trend: {
    type: Array,
    default: () => []
  },
  precision: {
    type: Number,
    default: 2
  },
  color: {
    type: String,
    default: '#165DFF'
  }
})

const formattedValue = computed(() => {
  try {
    if (typeof props.value === 'number') {
      return props.value.toLocaleString('zh-CN', {
        minimumFractionDigits: 0,
        maximumFractionDigits: props.precision || 2
      })
    }
    return props.value
  } catch (error) {
    console.error('格式化数值出错:', error)
    return props.value
  }
})

const formattedGrowth = computed(() => {
  try {
    const growth = props.growth || 0
    return growth.toFixed(2)
  } catch (error) {
    console.error('格式化增长率出错:', error)
    return '0.00'
  }
})

const isPositiveGrowth = computed(() => {
  return (props.growth || 0) >= 0
})

const chartRef = ref()
const { setOption } = useEcharts(chartRef)

// 安全地渲染图表
const renderChart = () => {
  try {
    // 确保 trend 是数组且有数据
    if (!Array.isArray(props.trend) || props.trend.length === 0) {
      console.warn('趋势数据不是有效数组或为空')
      return
    }

    // 确保所有 trend 数据项都是数字
    const validData = props.trend.map(item => typeof item === 'number' ? item : 0)

    const option = {
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0
      },
      xAxis: {
        type: 'category',
        show: false
      },
      yAxis: {
        type: 'value',
        show: false
      },
      series: [
        {
          type: 'line',
          data: validData,
          symbol: 'none',
          smooth: true,
          lineStyle: {
            color: props.color || '#165DFF',
            width: 2
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: (props.color || '#165DFF') + '40' // 40% 透明度
                },
                {
                  offset: 1,
                  color: (props.color || '#165DFF') + '00' // 0% 透明度
                }
              ]
            }
          }
        }
      ]
    }
    
    // 在下一个渲染周期中设置图表选项
    nextTick(() => {
      try {
        setOption(option)
      } catch (error) {
        console.error('设置图表选项出错:', error)
      }
    })
  } catch (error) {
    console.error('渲染图表出错:', error)
  }
}

onMounted(() => {
  renderChart()
})

// 当 trend 或 color 属性变化时重新渲染图表
watch([() => props.trend, () => props.color], () => {
  nextTick(() => renderChart())
}, { deep: true })
</script>

<template>
  <div class="kpi-card">
    <div class="kpi-header">
      <div class="kpi-title">{{ title }}</div>
    </div>
    <div class="kpi-content">
      <div class="kpi-value">
        <span class="prefix">{{ prefix }}</span>
        {{ formattedValue }}
        <span class="suffix">{{ suffix }}</span>
      </div>
      <div class="kpi-growth" :class="{ 'positive': isPositiveGrowth, 'negative': !isPositiveGrowth }">
        <span class="growth-value">{{ isPositiveGrowth ? '+' : '' }}{{ formattedGrowth }}%</span>
        <span class="growth-icon">
          <icon-up v-if="isPositiveGrowth" />
          <icon-down v-else />
        </span>
      </div>
    </div>
    <div class="kpi-chart" ref="chartRef"></div>
  </div>
</template>

<style scoped>
.kpi-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.kpi-header {
  margin-bottom: 8px;
}

.kpi-title {
  font-size: 14px;
  color: #4E5969;
}

.kpi-content {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
}

.kpi-value {
  font-size: 24px;
  font-weight: 600;
  color: #1D2129;
  margin-right: 8px;
}

.prefix, .suffix {
  font-size: 14px;
  font-weight: normal;
  color: #4E5969;
}

.kpi-growth {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.positive {
  color: #10B981;
}

.negative {
  color: #F53F3F;
}

.growth-icon {
  margin-left: 4px;
  display: flex;
  align-items: center;
}

.kpi-chart {
  flex: 1;
  min-height: 40px;
}
</style>
