<script setup lang="ts">
import { useEcharts } from '~/composables/useEcharts'

const props = defineProps({
  title: {
    type: String,
    default: '财务数据概览'
  },
  receivables: {
    type: Object,
    required: true
  },
  payables: {
    type: Object,
    required: true
  }
})

const receivablesChartRef = ref()
const payablesChartRef = ref()
const { setOption: setReceivablesOption } = useEcharts(receivablesChartRef)
const { setOption: setPayablesOption } = useEcharts(payablesChartRef)

onMounted(() => {
  // 应收款图表
  const receivablesOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      bottom: '0',
      left: 'center',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        fontSize: 12,
        color: '#4E5969'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '40%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { name: '已收款', value: props.receivables.received },
          { name: '待收款', value: props.receivables.pending }
        ]
      }
    ],
    color: ['#10B981', '#F7BA1E']
  }
  
  // 应付款图表
  const payablesOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      bottom: '0',
      left: 'center',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        fontSize: 12,
        color: '#4E5969'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '40%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { name: '已付款', value: props.payables.paid },
          { name: '待付款', value: props.payables.pending }
        ]
      }
    ],
    color: ['#10B981', '#F7BA1E']
  }
  
  setReceivablesOption(receivablesOption)
  setPayablesOption(payablesOption)
})

const formatCurrency = (value) => {
  return value.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}
</script>

<template>
  <div class="financial-overview-card">
    <div class="financial-header">
      <div class="financial-title">{{ title }}</div>
    </div>
    <div class="financial-content">
      <div class="financial-item">
        <div class="item-header">
          <div class="item-title">应收款统计</div>
          <div class="item-total">总额：¥{{ formatCurrency(receivables.total) }}</div>
        </div>
        <div class="item-chart" ref="receivablesChartRef"></div>
      </div>
      <div class="financial-item">
        <div class="item-header">
          <div class="item-title">应付款统计</div>
          <div class="item-total">总额：¥{{ formatCurrency(payables.total) }}</div>
        </div>
        <div class="item-chart" ref="payablesChartRef"></div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.financial-overview-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.financial-header {
  margin-bottom: 16px;
}

.financial-title {
  font-size: 14px;
  color: #4E5969;
}

.financial-content {
  flex: 1;
  display: flex;
  gap: 16px;
}

.financial-item {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-title {
  font-size: 13px;
  color: #4E5969;
}

.item-total {
  font-size: 12px;
  color: #86909C;
}

.item-chart {
  flex: 1;
  min-height: 200px;
}
</style>
