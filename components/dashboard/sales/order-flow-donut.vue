<script setup lang="ts">
import { useEcharts } from '~/composables/useEcharts'

const props = defineProps({
  title: {
    type: String,
    default: '订单流量对比'
  },
  total: {
    type: Number,
    required: true
  },
  data: {
    type: Array,
    required: true
  }
})

const chartRef = ref()
const { setOption } = useEcharts(chartRef)

onMounted(() => {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      bottom: '0',
      left: 'center',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 20,
      textStyle: {
        fontSize: 12,
        color: '#4E5969'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '40%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: props.data
      }
    ],
    color: ['#165DFF', '#0FC6C2', '#722ED1', '#F7BA1E', '#F53F3F']
  }
  
  setOption(option)
})
</script>

<template>
  <div class="order-flow-card">
    <div class="order-header">
      <div class="order-title">{{ title }}</div>
    </div>
    <div class="order-content">
      <div class="order-chart" ref="chartRef"></div>
      <div class="order-total">
        <div class="total-label">总订单数</div>
        <div class="total-value">{{ total }}单</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.order-flow-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.order-header {
  margin-bottom: 16px;
}

.order-title {
  font-size: 14px;
  color: #4E5969;
}

.order-content {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.order-chart {
  flex: 1;
  min-height: 200px;
}

.order-total {
  position: absolute;
  left: 50%;
  top: 40%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.total-label {
  font-size: 12px;
  color: #86909C;
}

.total-value {
  font-size: 16px;
  font-weight: 600;
  color: #1D2129;
}
</style>
