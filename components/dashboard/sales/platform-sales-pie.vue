<script setup lang="ts">
import { useEcharts } from '~/composables/useEcharts'

const props = defineProps({
  title: {
    type: String,
    default: '平台销售额'
  },
  data: {
    type: Array,
    required: true
  }
})

const chartRef = ref()
const { setOption } = useEcharts(chartRef)

const totalSales = computed(() => {
  return props.data.reduce((sum, item) => sum + item.value, 0)
})

const formattedTotal = computed(() => {
  return totalSales.value.toLocaleString('zh-CN')
})

onMounted(() => {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 10,
      textStyle: {
        fontSize: 12,
        color: '#4E5969'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['30%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data: props.data
      }
    ],
    color: ['#165DFF', '#0FC6C2', '#722ED1', '#F7BA1E', '#F53F3F', '#7BC616', '#86909C', '#B71DE8', '#0E42D2', '#F77234']
  }
  
  setOption(option)
})
</script>

<template>
  <div class="platform-sales-card">
    <div class="platform-header">
      <div class="platform-title">{{ title }}</div>
    </div>
    <div class="platform-content">
      <div class="platform-chart" ref="chartRef"></div>
      <div class="platform-total">
        <div class="total-label">总销售额</div>
        <div class="total-value">¥{{ formattedTotal }}</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.platform-sales-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.platform-header {
  margin-bottom: 16px;
}

.platform-title {
  font-size: 14px;
  color: #4E5969;
}

.platform-content {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.platform-chart {
  flex: 1;
  min-height: 200px;
}

.platform-total {
  position: absolute;
  left: 30%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.total-label {
  font-size: 12px;
  color: #86909C;
}

.total-value {
  font-size: 16px;
  font-weight: 600;
  color: #1D2129;
}
</style>
