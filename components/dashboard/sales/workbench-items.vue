<script setup lang="ts">
const props = defineProps({
  items: {
    type: Array,
    default: () => []
  }
})
</script>

<template>
  <div class="workbench-card">
    <div class="workbench-header">
      <div class="workbench-title">工作台</div>
    </div>
    <div class="workbench-content">
      <div class="workbench-items">
        <div v-for="(item, index) in items" :key="index" class="workbench-item">
          <div class="item-icon">
            <component :is="`icon-${item.icon}`" />
            <span class="item-badge">{{ item.count }}</span>
          </div>
          <div class="item-name">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.workbench-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.workbench-header {
  margin-bottom: 16px;
}

.workbench-title {
  font-size: 14px;
  color: #4E5969;
}

.workbench-content {
  flex: 1;
}

.workbench-items {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.workbench-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(20% - 16px);
}

.item-icon {
  position: relative;
  font-size: 24px;
  color: #165DFF;
  margin-bottom: 8px;
}

.item-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #F53F3F;
  color: #fff;
  font-size: 12px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  border-radius: 8px;
  padding: 0 4px;
}

.item-name {
  font-size: 12px;
  color: #4E5969;
  text-align: center;
}
</style>
