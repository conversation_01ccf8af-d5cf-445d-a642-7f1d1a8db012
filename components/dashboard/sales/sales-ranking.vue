<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
  valueLabel: {
    type: String,
    default: '销售额'
  },
  showIndex: {
    type: Boolean,
    default: true
  }
})
</script>

<template>
  <div class="sales-ranking-card">
    <div class="ranking-header">
      <div class="ranking-title">{{ title }}</div>
    </div>
    <div class="ranking-content">
      <div class="ranking-list">
        <div v-for="(item, index) in data" :key="index" class="ranking-item">
          <div class="item-index" v-if="showIndex" :class="'rank-' + (index + 1)">{{ index + 1 }}</div>
          <div class="item-info">
            <div class="item-name">{{ item.name }}</div>
            <div class="item-progress">
              <div class="progress-bar" :style="{ width: item.rate + '%' }"></div>
            </div>
          </div>
          <div class="item-value">
            <div class="value-amount">¥{{ item.amount.toLocaleString('zh-CN') }}</div>
            <div class="value-rate">{{ item.rate.toFixed(2) }}%</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.sales-ranking-card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ranking-header {
  margin-bottom: 16px;
}

.ranking-title {
  font-size: 14px;
  color: #4E5969;
}

.ranking-content {
  flex: 1;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
}

.item-index {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  margin-right: 8px;
  background-color: #F2F3F5;
  color: #4E5969;
}

.rank-1 {
  background-color: #F7BA1E;
  color: #fff;
}

.rank-2 {
  background-color: #86909C;
  color: #fff;
}

.rank-3 {
  background-color: #B54909;
  color: #fff;
}

.item-info {
  flex: 1;
  margin-right: 8px;
}

.item-name {
  font-size: 12px;
  color: #4E5969;
  margin-bottom: 4px;
}

.item-progress {
  height: 4px;
  background-color: #F2F3F5;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #165DFF;
  border-radius: 2px;
}

.item-value {
  text-align: right;
  min-width: 80px;
}

.value-amount {
  font-size: 12px;
  font-weight: 600;
  color: #1D2129;
}

.value-rate {
  font-size: 12px;
  color: #86909C;
}
</style>
