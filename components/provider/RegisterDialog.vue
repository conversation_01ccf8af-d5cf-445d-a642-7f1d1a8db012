<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
-->
<script setup>
import { ref, reactive, onBeforeUnmount, onMounted, onUnmounted } from "vue";
import { Message } from "@arco-design/web-vue";
import authApi from "@/api/provider/auth.js";

const visible = ref(false);
const currentStep = ref(1);
const router = useRouter();
// 表单引用
const basicFormRef = ref(null);
const companyFormRef = ref(null);
const loading = ref(false);
const passwordStrength = ref("弱");
const passwordStrengthColor = ref("text-red-500");
const isMobile = ref(false);

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

onUnmounted(() => {
  window.removeEventListener("resize", checkMobile);
});

// 检查密码强度
const checkPasswordStrength = () => {
  const password = basicForm.password;
  if (!password) {
    passwordStrength.value = "弱";
    passwordStrengthColor.value = "text-red-500";
    return;
  }

  // 计算密码强度
  let score = 0;

  // 检查长度 (8-20位)
  if (password.length >= 8 && password.length <= 20) score++;

  // 检查字符类型（需由字母、数字和半角符号两种及以上组合）
  const hasLetter = /[a-zA-Z]/.test(password);
  const hasNumber = /[0-9]/.test(password);
  const hasSymbol = /[^a-zA-Z0-9]/.test(password);

  // 计算包含的字符类型数量
  const typeCount = [hasLetter, hasNumber, hasSymbol].filter(Boolean).length;
  if (typeCount >= 2) score += 2;

  // 检查是否与用户名相同
  if (password !== basicForm.username) score++;
  else score = 0; // 如果与用户名相同，强制设为0分

  // 设置密码强度
  if (score <= 2) {
    passwordStrength.value = "弱";
    passwordStrengthColor.value = "text-red-500";
  } else if (score <= 3) {
    passwordStrength.value = "中";
    passwordStrengthColor.value = "text-yellow-500";
  } else {
    passwordStrength.value = "强";
    passwordStrengthColor.value = "text-green-500";
  }
};

// 基础信息表单数据
const basicForm = reactive({
  username: "",
  nickname: "",
  password: "",
  confirmPassword: "",
  email: "",
  phone: "",
  verificationCode: ""
});

// 企业信息表单数据
const companyForm = reactive({
  // 基本信息
  companyName: "",

  // 营业执照
  businessLicense: "",
  businessLicenseList: [],

  // 法人身份证
  idCardFrontList: [],
  idCardBackList: [],

  // 纳税评级
  taxLevel: "",
  taxCertificateList: [],

  // 办公地址
  province: "",
  detailAddress: "",

  // 联系人信息
  contacts: [
    {
      position: "", // 职务
      name: "", // 姓名
      phone: "", // 电话
      email: "" // 邮箱
    }
  ],

  // 银行信息
  bankName: "", // 开户银行
  bankBranch: "", // 开户网点
  bankAccount: "", // 对公银行账号
  bankCertificateList: [] // 开户证明
});

// 打开弹窗
const open = () => {
  visible.value = true;
};

// 关闭弹窗
const close = () => {
  visible.value = false;
  currentStep.value = 1;
  // 重置表单
  basicForm.username = "";
  basicForm.nickname = "";
  basicForm.password = "";
  basicForm.confirmPassword = "";
  basicForm.email = "";
  basicForm.phone = "";
  basicForm.verificationCode = "";
};

// 短信验证码相关
const countdown = ref(0);
const timer = ref(null);

// 获取短信验证码
const getVerificationCode = async () => {
  if (countdown.value > 0) return;

  // 验证手机号
  const phoneRegex = /^1[3-9]\d{9}$/;
  const phone = basicForm.phone?.trim();
  if (!basicForm.phone || !phoneRegex.test(phone)) {
    Message.error("请输入正确的手机号");
    return;
  }

  try {
    // 调用获取验证码API
    const response = await authApi.getCaptcha({ phoneNumber: phone, templateCode: "SMS_276465873", type: "register" });

    if (response.code === 200) {
      countdown.value = 60;
      Message.success("验证码已发送到您的手机");

      // 开发环境直接显示验证码，方便测试
      if (response.data && response.data.captcha) {
        console.log("验证码：", response.data.captcha);
      }

      // 倒计时
      timer.value = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer.value);
        }
      }, 1000);
    } else {
      throw new Error(response.message || "获取验证码失败");
    }
  } catch (error) {
    console.error("发送验证码失败:", error);
    Message.error(error.message || "发送验证码失败，请稍后重试");
  }
};

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  if (timer.value) {
    clearInterval(timer.value);
  }
});

// 立即注册
const handleRegister = async () => {
  try {
    // 验证必填字段
    const requiredFields = {
      username: "用户名",
      password: "登录密码",
      phone: "手机号",
      verificationCode: "验证码"
    };

    // 验证密码规则
    const password = basicForm.password;
    if (password) {
      // 检查长度
      if (password.length < 8 || password.length > 20) {
        Message.error("密码长度必须为8-20位");
        return;
      }

      // 检查是否包含至少两种字符类型
      const hasLetter = /[a-zA-Z]/.test(password);
      const hasNumber = /[0-9]/.test(password);
      const hasSymbol = /[^a-zA-Z0-9]/.test(password);
      const typeCount = [hasLetter, hasNumber, hasSymbol].filter(Boolean)
        .length;
      if (typeCount < 2) {
        Message.error("密码需包含字母、数字和半角符号中的两种及以上");
        return;
      }

      // 检查是否与用户名相同
      if (password === basicForm.username) {
        Message.error("密码不能与用户名相同");
        return;
      }
    }

    // 检查必填字段是否填写
    const missingFields = [];
    for (const [field, label] of Object.entries(requiredFields)) {
      if (!basicForm[field]) {
        missingFields.push(label);
      }
    }

    if (missingFields.length > 0) {
      throw new Error(`请填写以下必填项：${missingFields.join("、")}`);
    }

    // 验证密码强度
    if (passwordStrength.value === "弱") {
      throw new Error("密码强度太弱，请按照提示设置更安全的密码");
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    const phone = basicForm.phone?.trim();
    if (!basicForm.phone || !phoneRegex.test(phone)) {
      throw new Error("请输入正确的手机号码");
    }

    // 验证邮箱格式（如果填写了邮箱）
    if (
      basicForm.email &&
      !/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/.test(basicForm.email)
    ) {
      throw new Error("请输入正确的邮箱地址");
    }

    // 执行表单验证
    await basicFormRef.value.validate();
    loading.value = true;

    try {
      // 调用注册 API
      const response = await authApi.register({
        username: basicForm.username,
        password: basicForm.password,
        phone: basicForm.phone,
        captcha: basicForm.verificationCode
      });
      const realToken = response.data.token;
      const user_provider = response.data;
      if (response.code === 200) {
        const tool = await import("@/utils/tool");
        tool.default.local.set("token_provider", realToken);
        tool.default.local.set('user_provider', user_provider);

        // 设置过期时间，默认一天
        const expiresIn = response.data.expires_in || 86400;
        const expiresTime = Date.now() + expiresIn * 1000;
        tool.default.local.set("expires_provider", expiresTime);
        Message.success("注册成功");
        setTimeout(() => {
          router.push("/provider/information");
        }, 1500);
        close();
      } else {
        throw new Error(response.message || "注册失败");
      }
    } catch (error) {
      Message.error(error.message || "注册失败，请稍后重试");
    } finally {
      loading.value = false;
    }
  } catch (error) {
    if (error.message) {
      Message.error(error.message);
    } else {
      console.error("注册失败:", error);
      Message.error("注册失败，请检查表单信息是否填写正确");
    }
  } finally {
    loading.value = false;
  }
};

defineExpose({
  open,
  close
});
</script>

<template>
  <a-modal
    :visible="visible"
    @update:visible="visible = $event"
    @cancel="close"
    :footer="false"
    :mask-closable="false"
    :width="isMobile ? '90%' : '30%'"
    title="注册新账号"
  >
    <div class="register-content">
      <div class="form-container">
        <div class="form-step">
          <a-form auto-label-width :model="basicForm" layout="horizontal" ref="basicFormRef">
            <a-form-item
              field="username"
              label="用户名"
              :rules="[{ required: true, message: '用户名必填' }]"
            >
              <a-input v-model="basicForm.username" placeholder="请输入用户名" />
            </a-form-item>

            <!-- <a-form-item
              field="nickname"
              label="昵称"
              :rules="[{ required: true, message: '昵称必填' }]"
            >
              <a-input v-model="basicForm.nickname" placeholder="请输入昵称" />
            </a-form-item>-->

            <a-form-item
              field="password"
              label="登录密码"
              :rules="[
                { required: true, message: '登录密码必填' },
                { minLength: 8, message: '密码长度不能少于8位' },
                { maxLength: 20, message: '密码长度不能超过20位' },
                { 
                  validator: (value) => {
                    // 检查是否包含至少两种字符类型
                    const hasLetter = /[a-zA-Z]/.test(value);
                    const hasNumber = /[0-9]/.test(value);
                    const hasSymbol = /[^a-zA-Z0-9]/.test(value);
                    const typeCount = [hasLetter, hasNumber, hasSymbol].filter(Boolean).length;
                    return typeCount >= 2 || '密码需包含字母、数字和半角符号中的两种及以上';
                  } 
                },
                { 
                  validator: (value) => value !== basicForm.username || '密码不能与用户名相同'
                }
              ]"
            >
              <a-tooltip
                position="right"
                trigger="focus"
                :content-style="{ backgroundColor: '#fff', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }"
              >
                <template #content>
                  <div class="p-3">
                    <div
                      :class="['font-semibold mb-2', passwordStrengthColor]"
                    >安全等级：{{ passwordStrength }}</div>
                    <ul class="text-sm space-y-1 text-gray-600">
                      <li>• 8-20位</li>
                      <li>• 需由字母、数字和半角符号两种及以上组合</li>
                      <li>• 密码与用户名不能相同</li>
                    </ul>
                  </div>
                </template>
                <a-input-password
                  v-model="basicForm.password"
                  placeholder="请输入登录密码"
                  allow-clear
                  @input="checkPasswordStrength"
                />
              </a-tooltip>
            </a-form-item>

            <!-- <a-form-item
              field="confirmPassword"
              label="确认密码"
              :rules="[{ required: true, message: '确认密码必填' }]"
            >
              <a-input-password
                v-model="basicForm.confirmPassword"
                placeholder="请再次输入密码"
              />
            </a-form-item>-->

            <!-- <a-form-item field="email" label="邮箱">
              <a-input v-model="basicForm.email" placeholder="请输入邮箱地址" />
            </a-form-item>-->

            <a-form-item
              field="phone"
              label="手机号"
              :rules="[
                { required: true, message: '手机号必填' },
                { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
              ]"
            >
              <a-input v-model="basicForm.phone" placeholder="请输入手机号" />
            </a-form-item>

            <a-form-item
              field="verificationCode"
              label="验证码"
              :rules="[{ required: true, message: '验证码必填' }]"
            >
              <div class="verification-code">
                <a-input
                  :style="isMobile ? 'width: 55%' : 'width: 80%'"
                  v-model="basicForm.verificationCode"
                  placeholder="请输入验证码"
                />
                <a-button
                  class="get-code-btn ml-2 sm:ml-3"
                  @click="getVerificationCode"
                  :disabled="countdown > 0"
                  :style="isMobile ? 'font-size: 12px; padding: 0 8px;' : ''"
                >{{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}</a-button>
              </div>
            </a-form-item>

            <div class="form-actions" style="justify-content: flex-end;">
              <a-button
                type="primary"
                @click="handleRegister"
                :loading="loading"
                class="bg-gradient-to-r from-red-500 to-orange-500 border-none font-semibold"
                style="width:95%"
              >立即注册</a-button>
            </div>
          </a-form>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<style scoped lang="less">
.register-content {
  width: 100%;
  background-color: #fff;
  padding: 15px;

  @media (min-width: 768px) {
    padding: 20px;
  }
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
}

.form-step {
  display: flex;
  align-items: center;
}

.verification-code {
  display: flex;
  gap: 5px;
  align-items: center;

  @media (min-width: 768px) {
    gap: 10px;
  }

  .get-code-btn {
    white-space: nowrap;
    min-width: 100px;

    @media (min-width: 768px) {
      min-width: 120px;
    }
  }
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;

  @media (min-width: 768px) {
    margin-top: 20px;
  }
}

:deep(.arco-form-item-label-col) {
  label::before {
    margin-right: 4px;
  }
}

:deep(.arco-form-item) {
  margin-bottom: 15px;

  @media (min-width: 768px) {
    margin-bottom: 20px;
  }
}
</style>
