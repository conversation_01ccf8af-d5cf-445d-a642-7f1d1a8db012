<template>
  <div class="commit-history">
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" title="提交记录">
        <a-spin :loading="loading">
          <a-table 
            :columns="columns" 
            :data="commits" 
            :pagination="false" 
            :scroll="{ x: '100%', y: '600px' }" 
            :style="{ width: '100%' }"
          >
            <template #message="{ record }">
              <div class="commit-message">
                <div class="commit-title">{{ record.title }}</div>
                <div class="commit-description" v-if="record.description">{{ record.description }}</div>
              </div>
            </template>
            <template #author="{ record }">
              <div class="commit-author">
                <a-avatar :size="24" :src="record.author_avatar_url" />
                <span class="author-name">{{ record.author_name }}</span>
              </div>
            </template>
            <template #stats="{ record }">
              <div class="commit-stats">
                <span class="stats-added">+{{ record.stats?.additions || 0 }}</span>
                <span class="stats-deleted">-{{ record.stats?.deletions || 0 }}</span>
              </div>
            </template>
          </a-table>
        </a-spin>
      </a-tab-pane>
      <a-tab-pane key="2" title="贡献统计">
        <ContributionStats :commits="commits" :loading="loading" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import ContributionStats from './ContributionStats.vue';

const loading = ref(true);
const commits = ref([]);

// 表格列定义
const columns = [
  {
    title: "提交信息",
    dataIndex: "message",
    slotName: "message",
    width: '40%',
  },
  {
    title: "作者",
    dataIndex: "author",
    slotName: "author",
    width: '15%',
  },
  {
    title: "提交时间",
    dataIndex: "committed_date",
    width: '15%',
  },
  {
    title: "代码变更",
    slotName: "stats",
    width: '30%',
  },
];

// 格式化提交信息，将第一行作为标题，其余作为描述
const formatCommitMessage = (message) => {
  const lines = message.split('\n').filter(line => line.trim());
  return {
    title: lines[0] || '',
    description: lines.slice(1).join('\n')
  };
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  const date = new Date(dateTimeStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 作者映射表，用于合并相同作者的不同账号
const authorMappings = {
  '<EMAIL>': '沈均',
  '<EMAIL>': '沈均',
  '<EMAIL>': '沈均',
  '<EMAIL>': '沈均',
  '<EMAIL>': '宾志伟',
  '<EMAIL>': '宾志伟',
  '<EMAIL>': '王勇',
  '<EMAIL>': '林俊铭',
  '<EMAIL>': '杨雷',
  '70481579@': '杨雷',
  '<EMAIL>': '谭越东',
  '<EMAIL>': '谭越东',
  '<EMAIL>': '苏纪欢',
  '<EMAIL>': '饶伟波',
};

// 从本地JSON文件获取提交记录
const fetchCommits = async () => {
  loading.value = true;
  try {
    // 直接从本地JSON文件获取GitLab贡献数据
    const response = await fetch('/data/gitlab-contributions.json');
    if (!response.ok) {
      throw new Error(`获取GitLab贡献数据失败: HTTP状态码 ${response.status}`);
    }
    
    const contributionsData = await response.json();
    
    // 如果没有提交记录，则显示错误
    if (!contributionsData || !contributionsData.commits || contributionsData.commits.length === 0) {
      throw new Error('没有找到GitLab提交记录');
    }
    
    // 处理提交记录
    const processedCommits = contributionsData.commits.map(commit => {
      // 提取提交信息的标题和描述
      const commitMessage = commit.message || '';
      const messageParts = commitMessage.split('\n\n');
      const title = messageParts[0] || '无提交信息';
      const description = messageParts.slice(1).join('\n\n');
      
      // 处理作者名称，合并相同作者的不同账号
      let authorName = commit.author_name || '未知作者';
      const authorEmail = commit.author_email || '';
      
      // 检查是否需要映射作者名称
      if (authorEmail && authorMappings[authorEmail]) {
        authorName = authorMappings[authorEmail];
      }
      
      return {
        id: commit.id,
        title,
        description,
        author_name: authorName,
        author_email: authorEmail,
        author_avatar_url: commit.author_avatar_url || `https://www.gravatar.com/avatar/${(authorEmail || '').trim().toLowerCase().split('').reduce((a,b)=>{a=((a<<5)-a)+b.charCodeAt(0);return a&a},0)}?s=80&d=identicon`,
        committed_date: formatDateTime(commit.committed_date),
        web_url: commit.web_url || '',
        stats: {
          additions: commit.stats?.additions || 0,
          deletions: commit.stats?.deletions || 0,
          total: commit.stats?.total || 0
        }
      };
    });
    
    // 按提交时间倒序排序
    commits.value = processedCommits.sort((a, b) => {
      const dateA = new Date(a.committed_date.replace(/-/g, '/'));
      const dateB = new Date(b.committed_date.replace(/-/g, '/'));
      return dateB - dateA; // 倒序排列
    });
    
    // 显示最后更新时间
    if (contributionsData.lastUpdated) {
      console.log(`GitLab贡献数据最后更新时间: ${new Date(contributionsData.lastUpdated).toLocaleString()}`);
    }
  } catch (error) {
    console.error('获取GitLab提交记录失败:', error);
    // 显示错误提示
    window.$message.error('获取GitLab提交记录失败，请检查数据文件');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchCommits();
});
</script>

<style scoped>
.commit-history {
  margin-top: 16px;
  width: 100%;
}

.commit-message {
  display: flex;
  flex-direction: column;
}

.commit-title {
  font-weight: 500;
}

.commit-description {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
  white-space: pre-line;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.commit-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-size: 14px;
}

.commit-stats {
  display: flex;
  gap: 8px;
}

.stats-added {
  color: #00b42a;
  font-weight: 500;
}

.stats-deleted {
  color: #f53f3f;
  font-weight: 500;
}

:deep(.arco-table-container) {
  width: 100%;
}

:deep(.arco-table-tr) {
  width: 100%;
}

:deep(.arco-table) {
  width: 100%;
}
</style>