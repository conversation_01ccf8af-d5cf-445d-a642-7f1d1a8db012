<template>
  <div class="frequency-stats-container">
    <a-spin :loading="loading">
      <div class="frequency-stats">
        <div class="stat-item">
          <div class="stat-title">平均每日提交次数</div>
          <div class="stat-value">{{ frequencyStats.averagePerDay.toFixed(2) }}</div>
        </div>
        <div class="stat-item">
          <div class="stat-title">最活跃的日期</div>
          <div class="stat-value">{{ frequencyStats.mostActiveDate }}</div>
          <div class="stat-subvalue">{{ frequencyStats.mostActiveDateCount }} 次提交</div>
        </div>
        <div class="stat-item">
          <div class="stat-title">最活跃的星期</div>
          <div class="stat-value">{{ getWeekdayName(frequencyStats.mostActiveWeekday) }}</div>
          <div class="stat-subvalue">{{ frequencyStats.mostActiveWeekdayCount }} 次提交</div>
        </div>
        <div class="stat-item">
          <div class="stat-title">最活跃的时段</div>
          <div class="stat-value">{{ frequencyStats.mostActiveHour }}:00 - {{ frequencyStats.mostActiveHour + 1 }}:00</div>
          <div class="stat-subvalue">{{ frequencyStats.mostActiveHourCount }} 次提交</div>
        </div>
        <div class="stat-item">
          <div class="stat-title">最近一次提交</div>
          <div class="stat-value">{{ frequencyStats.lastCommitDate }}</div>
          <div class="stat-subvalue">{{ frequencyStats.daysSinceLastCommit }} 天前</div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, watch, defineProps } from 'vue';

const props = defineProps({
  commits: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const frequencyStats = ref({
  averagePerDay: 0,
  mostActiveDate: '',
  mostActiveDateCount: 0,
  mostActiveWeekday: 0,
  mostActiveWeekdayCount: 0,
  mostActiveHour: 0,
  mostActiveHourCount: 0,
  lastCommitDate: '',
  daysSinceLastCommit: 0
});

// 获取星期几的名称
const getWeekdayName = (weekday) => {
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  return weekdays[weekday] || '未知';
};

// 计算提交频率统计
const calculateFrequencyStats = () => {
  if (!props.commits || props.commits.length === 0) return;
  
  // 按日期分组
  const dateCommits = {};
  // 按星期几分组
  const weekdayCommits = {0: 0, 1: 0, 2: 0, 3: 0, 4: 0, 5: 0, 6: 0};
  // 按小时分组
  const hourCommits = {};
  
  // 最早和最晚的提交日期
  let earliestDate = new Date();
  let latestDate = new Date(0);
  
  props.commits.forEach(commit => {
    // 解析提交日期
    const commitDate = new Date(commit.committed_date.replace(/-/g, '/'));
    
    // 提取日期部分作为键
    const dateKey = commitDate.toISOString().split('T')[0];
    dateCommits[dateKey] = (dateCommits[dateKey] || 0) + 1;
    
    // 统计星期几
    const weekday = commitDate.getDay(); // 0-6, 0表示星期日
    weekdayCommits[weekday] = (weekdayCommits[weekday] || 0) + 1;
    
    // 统计小时
    const hour = commitDate.getHours();
    hourCommits[hour] = (hourCommits[hour] || 0) + 1;
    
    // 更新最早和最晚日期
    if (commitDate < earliestDate) earliestDate = new Date(commitDate);
    if (commitDate > latestDate) latestDate = new Date(commitDate);
  });
  
  // 计算日期范围内的天数
  const daysDiff = Math.ceil((latestDate - earliestDate) / (1000 * 60 * 60 * 24)) + 1;
  
  // 找出提交最多的日期
  let mostActiveDate = '';
  let mostActiveDateCount = 0;
  Object.entries(dateCommits).forEach(([date, count]) => {
    if (count > mostActiveDateCount) {
      mostActiveDate = date;
      mostActiveDateCount = count;
    }
  });
  
  // 格式化最活跃日期
  if (mostActiveDate) {
    const dateParts = mostActiveDate.split('-');
    mostActiveDate = `${dateParts[0]}-${dateParts[1]}-${dateParts[2]}`;
  }
  
  // 找出提交最多的星期几
  let mostActiveWeekday = 0;
  let mostActiveWeekdayCount = 0;
  Object.entries(weekdayCommits).forEach(([weekday, count]) => {
    if (count > mostActiveWeekdayCount) {
      mostActiveWeekday = parseInt(weekday);
      mostActiveWeekdayCount = count;
    }
  });
  
  // 找出提交最多的小时
  let mostActiveHour = 0;
  let mostActiveHourCount = 0;
  Object.entries(hourCommits).forEach(([hour, count]) => {
    if (count > mostActiveHourCount) {
      mostActiveHour = parseInt(hour);
      mostActiveHourCount = count;
    }
  });
  
  // 计算最近一次提交距今天数
  const lastCommitDate = new Date(latestDate);
  const today = new Date();
  const daysSinceLastCommit = Math.floor((today - lastCommitDate) / (1000 * 60 * 60 * 24));
  
  // 更新统计数据
  frequencyStats.value = {
    averagePerDay: props.commits.length / daysDiff,
    mostActiveDate,
    mostActiveDateCount,
    mostActiveWeekday,
    mostActiveWeekdayCount,
    mostActiveHour,
    mostActiveHourCount,
    lastCommitDate: `${lastCommitDate.getFullYear()}-${String(lastCommitDate.getMonth() + 1).padStart(2, '0')}-${String(lastCommitDate.getDate()).padStart(2, '0')}`,
    daysSinceLastCommit
  };
};

// 监听提交数据变化，重新计算统计数据
watch(() => props.commits, calculateFrequencyStats, { immediate: true, deep: true });
</script>

<style scoped>
.frequency-stats-container {
  width: 100%;
}

.frequency-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

/* 修复 a-spin 宽度问题 */
:deep(.arco-spin) {
  display: block;
  width: 100%;
}

.stat-item {
  padding: 12px;
  background-color: #f7f8fa;
  border-radius: 4px;
}

.stat-title {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
}

.stat-subvalue {
  font-size: 12px;
  color: #4e5969;
  margin-top: 2px;
}
</style>