<template>
  <div class="contribution-ranking">
    <a-card class="stats-card">
      <template #title>
        <div class="card-title">
          <IconTrophy />
          <span>代码贡献排行榜</span>
        </div>
      </template>
      <a-spin :loading="loading">
        <div class="ranking-list">
          <div class="ranking-header">
            <div class="rank-column">排名</div>
            <div class="author-column">贡献者</div>
            <div class="stats-column">提交次数</div>
            <div class="stats-column">新增行数</div>
            <div class="stats-column">删除行数</div>
            <div class="stats-column">净贡献行数</div>
          </div>
          <div v-for="(contributor, index) in sortedContributors" :key="index" class="ranking-item">
            <div class="rank-column">
              <div :class="['rank-badge', getRankClass(index)]">{{ index + 1 }}</div>
            </div>
            <div class="author-column">
              <div class="contributor-info">
                <span class="author-name">{{ contributor.name }}</span>
              </div>
            </div>
            <div class="stats-column">{{ contributor.commits }}</div>
            <div class="stats-column stats-added">+{{ contributor.additions }}</div>
            <div class="stats-column stats-deleted">-{{ contributor.deletions }}</div>
            <div class="stats-column net-contribution">{{ contributor.netContribution }}</div>
          </div>
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script setup>
import { computed, defineProps } from 'vue';
import { IconTrophy } from '@arco-design/web-vue/es/icon';

const props = defineProps({
  commits: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 计算每个贡献者的统计数据
const contributors = computed(() => {
  if (!props.commits || props.commits.length === 0) return [];
  
  // 按作者分组统计
  const authorStats = {};
  
  props.commits.forEach(commit => {
    const author = commit.author_name;
    
    if (!authorStats[author]) {
      authorStats[author] = {
        name: author,
        email: commit.author_email || '',
        avatar_url: commit.author_avatar_url || '',
        commits: 0,
        additions: 0,
        deletions: 0
      };
    }
    
    authorStats[author].commits += 1;
    authorStats[author].additions += commit.stats?.additions || 0;
    authorStats[author].deletions += commit.stats?.deletions || 0;
  });
  
  // 转换为数组并计算净贡献行数
  return Object.values(authorStats).map(author => ({
    ...author,
    netContribution: author.additions - author.deletions
  }));
});

// 按净贡献行数排序的贡献者列表
const sortedContributors = computed(() => {
  return [...contributors.value].sort((a, b) => b.netContribution - a.netContribution);
});

// 获取排名样式类
const getRankClass = (index) => {
  if (index === 0) return 'rank-first';
  if (index === 1) return 'rank-second';
  if (index === 2) return 'rank-third';
  return '';
};
</script>

<style scoped>
.contribution-ranking {
  margin-top: 16px;
  width: 100%;
}

.stats-card {
  height: 100%;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* 修复 a-spin 宽度问题 */
:deep(.arco-spin) {
  display: block;
  width: 100%;
}

.ranking-header {
  display: flex;
  padding: 8px 0;
  font-weight: 500;
  border-bottom: 1px solid #e5e6eb;
  color: #4e5969;
}

.ranking-item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f2f3f5;
  align-items: center;
}

.ranking-item:last-child {
  border-bottom: none;
}

.rank-column {
  width: 60px;
  text-align: center;
}

.author-column {
  flex: 1;
  min-width: 150px;
}

.stats-column {
  width: 100px;
  text-align: center;
}

.rank-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #f2f3f5;
  font-weight: 500;
}

.rank-first {
  background-color: #FFD700;
  color: #fff;
}

.rank-second {
  background-color: #C0C0C0;
  color: #fff;
}

.rank-third {
  background-color: #CD7F32;
  color: #fff;
}

.contributor-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-weight: 500;
}

.stats-added {
  color: #00b42a;
  font-weight: 500;
}

.stats-deleted {
  color: #f53f3f;
  font-weight: 500;
}

.net-contribution {
  font-weight: 600;
  color: #165dff;
}
</style>