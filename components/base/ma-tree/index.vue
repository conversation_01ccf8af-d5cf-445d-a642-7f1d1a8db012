<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://github.com/mineadmin
-->

<script setup lang="ts">
import { ref, watch, computed, useAttrs } from 'vue';
import { get } from 'lodash-es';
import type { TreeNodeData, TreeInstance } from '@arco-design/web-vue';

defineOptions({ name: 'MaTree' });

const props = defineProps<{
  data?: TreeNodeData[]; 
  fieldNames?: { key?: string; title?: string; children?: string; disabled?: string };
  showCheckbox?: boolean;
}>();

const emit = defineEmits(['update:checkedKeys', 'update:selectedKeys', 'select']);

const attrs = useAttrs();

const filterText = ref<string>('');
const treeRef = ref<TreeInstance>();
const isExpand = ref<boolean>(false);
const checkStrictly = ref<boolean>(attrs['check-strictly'] as boolean ?? false);
const checkedKeys = ref<Array<string | number>>(attrs['checked-keys'] as Array<string | number> ?? []);
const selectedKeys = ref<Array<string | number>>(attrs['selected-keys'] as Array<string | number> ?? []);

const treeFieldNames = computed(() => {
  return props.fieldNames ?? { key: 'key', title: 'title', children: 'children' };
});

const titleField = computed(() => treeFieldNames.value.title || 'title');

watch(filterText, (val) => {
  treeRef.value?.filter(val);
});

function filterNode(value: string, nodeData: TreeNodeData) {
  if (!value) {
    return true;
  }
  const title = get(nodeData, titleField.value) as string | undefined;
  return !!title?.includes(value);
}

function toggle() {
  isExpand.value = !isExpand.value;
  treeRef.value?.expandAll(isExpand.value);
}

function handleSelectAll(isChecked: boolean) {
  if (props.showCheckbox) {
    if (isChecked) {
      const allKeys = getAllNodeKeys(treeRef.value?.flattenTreeData ?? []);
      checkedKeys.value = allKeys;
    } else {
      checkedKeys.value = [];
    }
    emit('update:checkedKeys', checkedKeys.value);
  }
}

function handleInvert() {
  if (props.showCheckbox && checkStrictly.value) {
    const allKeys = getAllNodeKeys(treeRef.value?.flattenTreeData ?? []);
    const currentChecked = new Set(checkedKeys.value);
    const newCheckedKeys: Array<string | number> = [];
    allKeys.forEach(key => {
      if (!currentChecked.has(key)) {
        newCheckedKeys.push(key);
      }
    });
    checkedKeys.value = newCheckedKeys;
    emit('update:checkedKeys', checkedKeys.value);
  }
}

function getAllNodeKeys(nodes: TreeNodeData[]): Array<string | number> {
    const keys: Array<string | number> = [];
    const keyField = treeFieldNames.value.key || 'key';
    const disabledField = treeFieldNames.value.disabled || 'disabled';
    nodes.forEach(node => {
        const key = get(node, keyField);
        if (key !== undefined && key !== null) {
            keys.push(key);
        }
    });
    return keys;
}

function handleCheck(newCheckedKeys: (string | number)[]) {
  checkedKeys.value = newCheckedKeys;
  emit('update:checkedKeys', newCheckedKeys);
};

function handleSelect(newSelectedKeys: (string | number)[], data: { selected?: boolean; selectedNodes: TreeNodeData[]; node?: TreeNodeData; e?: Event; }) {
    selectedKeys.value = newSelectedKeys;
    emit('update:selectedKeys', newSelectedKeys);
    emit('select', newSelectedKeys, data);
};

defineExpose({
  toggle,
  handleSelectAll,
  handleInvert,
  arcoTree: treeRef,
});

</script>

<template>
  <div>
    <div class="mb-2 sticky top-0 bg-white dark:bg-gray-800 z-10 py-1">
      <div v-if="showCheckbox" class="flex items-center flex-wrap gap-x-4 mb-1">
        <a-checkbox v-model="checkStrictly">
          <div class="flex items-center gap-x-1">
            {{ '严格模式' }}
            <a-tooltip content="父子节点选中状态是否关联"> 
              <icon-info-circle />
            </a-tooltip>
          </div>
        </a-checkbox>
        <a-checkbox @change="handleSelectAll"> 
          {{ '全选' }}
        </a-checkbox>
        <a-checkbox v-if="checkStrictly" @change="handleInvert">
          {{ '反选' }}
        </a-checkbox>
      </div>
      <div class="flex items-center justify-between gap-x-2">
        <a-input
          v-model="filterText"
          :placeholder="'搜索名称'" 
          allow-clear
          style="flex-grow: 1;"
        >
          <template #prefix>
            <icon-search />
          </template>
        </a-input>
        <a-button-group>
          <a-button @click="toggle()">
            {{ isExpand ? '全部折叠' : '全部展开' }}
          </a-button>
          <slot name="buttons" />
        </a-button-group>
      </div>
      <div class="ma-tree-extra mt-1">
        <slot name="extra-controls" />
      </div>
    </div>

    <a-tree
      ref="treeRef"
      :data="data"                 
      :field-names="treeFieldNames" 
      :show-checkbox="showCheckbox"
      :check-strictly="checkStrictly"
      :filter-tree-node="filterNode" 
      :checked-keys="checkedKeys"
      :selected-keys="selectedKeys"
      @check="handleCheck"
      @select="handleSelect"
      class="w-full ma-tree-component" 
      v-bind="$attrs" 
    >
      <!-- Keep default slot for title rendering -->
      <template #default="{ node, data: nodeData }">
         <slot v-bind="{ node, nodeData }">
            <span class="truncate">{{ get(nodeData, titleField) }}</span>
         </slot>
      </template>

      <!-- Forward other slots -->
      <template v-for="(_, slot) in $slots" #[slot]="scope">
        <slot v-if="slot !== 'default'" :name="slot" v-bind="scope" /> 
      </template>

    </a-tree>
  </div>
</template>

<style lang="less" scoped>
/* Add any specific styles for ma-tree here if needed */
</style>
