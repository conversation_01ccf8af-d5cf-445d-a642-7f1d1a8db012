<template>
  <div class="upload-file w-full">
    <a-upload
      :custom-request="uploadFileHandler"
      :show-file-list="false"
      :multiple="config.multiple"
      :accept="config.accept"
      :disabled="config.disabled"
      :tip="config.tip"
      :draggable="config.draggable"
      :limit="config.limit"
    >
      <template #upload-button v-if="config.draggable">
        <slot name="customer">
          <div
            style="background-color: var(--color-fill-2); border: 1px dashed var(--color-fill-4);"
            class="rounded text-center p-7 w-full"
          >
            <div>
              <icon-upload class="text-5xl text-gray-400"/>
              <div class="text-red-600 font-bold">
                {{ config.title === 'buttonText' ? $t('upload.buttonText') : config.title }}
              </div>
              将文件拖到此处，或<span style="color: #3370FF">点击上传</span>
            </div>
          </div>
        </slot>
      </template>
    </a-upload>
  </div>
  <!-- 单文件 -->
  <div
    class="file-list mt-2"
    v-if="!config.multiple && currentItem?.url && config.showList && currentItem?.status === 'complete'"
  >
    <a-tooltip content="点击文件名预览/下载" position="tr">
      <a :href="currentItem.url"
         v-if="currentItem?.url"
         class="file-name"
         target="_blank"
      >{{ currentItem.name || '未命名文件' }}</a>
    </a-tooltip>

    <a-button
      type="text"
      size="small"
      @click="removeSignFile()"
      v-if="currentItem.status === 'complete'"
    >
      <template #icon>
        <icon-delete/>
      </template>
    </a-button>
  </div>
   <!-- 单文件上传中 -->
  <div class="file-list mt-2" v-if="!config.multiple && currentItem?.status === 'uploading' && config.showList">
    <span class="file-name">{{ currentItem.name || '上传中...' }} ({{ currentItem.percent || 0 }}%)</span>
    <!-- Optionally, add a cancel button for uploading state -->
  </div>


  <!-- 多文件 -->
  <div
    v-if="config.multiple && config.showList && showFileList.length > 0"
    class="file-list-container mt-2" 
  >
    <div 
      class="file-list"
      v-for="(file, idx) in showFileList" :key="file.id || file.url || idx"
    >
      <a-tooltip content="点击文件名预览/下载" position="tr">
        <a :href="file.url"
           v-if="file?.url && file.status === 'complete'"
           class="file-name"
           target="_blank"
        >{{ file.name || '未命名文件' }}</a>
         <span v-else-if="file.status === 'uploading'" class="file-name">
            {{ file.name || '上传中...' }} ({{ file.percent || 0 }}%)
        </span>
      </a-tooltip>

      <a-button
        type="text"
        size="small"
        @click="removeFile(idx)"
        v-if="file.status === 'complete'"
      >
        <template #icon>
          <icon-delete/>
        </template>
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, watch, computed } from 'vue' // Added computed
import tool from '~/utils/tool.js'
import { isArray, throttle } from 'lodash'
import { getFileUrl } from '../js/utils.js' // Assuming utils.js is in a parent 'js' folder
import { Message } from '@arco-design/web-vue'
import commonApi from "~/api/common.js";
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  modelValue: {
    type: [String, Number, Array, Object], // Added Object for completeness
    default: () => ([]) // Changed default to empty array for multiple, null/undefined for single is fine
  }
})
const emit = defineEmits(['update:modelValue'])
const config = inject('config')
const storageMode = inject('storageMode')

// This will hold file objects being actively uploaded for multi-upload
const uploadingFiles = ref([]); 

// This will hold successfully uploaded files or initialized files
const completedFiles = ref([]);

const currentItem = ref({}) // For single file upload status and details

// Combines uploading and completed files for display in multi-mode
const showFileList = computed(() => {
  console.log('uploadingFiles', uploadingFiles.value);
  if (!config.multiple) return []; // Only for multiple
  // Map uploading files to a common structure
  const activeUploads = uploadingFiles.value.map(fileItem => ({
    uid: fileItem.uid, // ArUpload fileItem has uid
    name: fileItem.file?.name || '上传中...',
    percent: fileItem.percent || 0,
    status: fileItem.status || 'uploading',
    url: fileItem.url, // Might be undefined during upload
  }));
  return [...activeUploads, ...completedFiles.value];
});


const extractFileNameFromUrl = (url) => {
  if (typeof url !== 'string' || !url) return '附件';
  try {
    const decodedUrl = decodeURIComponent(url);
    return decodedUrl.substring(decodedUrl.lastIndexOf('/') + 1) || '附件';
  } catch (e) {
    // Fallback for malformed URI
    return url.substring(url.lastIndexOf('/') + 1) || '附件';
  }
};

const uploadFileHandler = async (options) => {
  if (!options.fileItem) return;

  const fileItem = options.fileItem; // ArUpload passes the fileItem

  // Size check
  if (fileItem.file.size > config.size) {
    Message.warning(`${fileItem.file.name} ${t("upload.sizeLimit")}`);
    options.onError(); // Important to call onError for ArUpload
    return;
  }

  // Add to uploading list or set currentItem for UI feedback
  if (!config.multiple) {
    currentItem.value = {
      uid: fileItem.uid,
      name: fileItem.file.name,
      percent: 0,
      status: 'uploading',
      url: '' // No URL yet
    };
  } else {
    // For multiple, add to a temporary uploading list if you want individual progress
    // The default ArUpload show-file-list handles this, but we have it false
    // So, we manage our own list.
    const existingFileIndex = uploadingFiles.value.findIndex(f => f.uid === fileItem.uid);
    if (existingFileIndex === -1) {
      uploadingFiles.value.push({
        uid: fileItem.uid,
        name: fileItem.file.name,
        percent: 0,
        status: 'uploading',
        url: ''
      });
    }
  }
  
  const updateProgress = (percent) => {
    if (!config.multiple) {
      if (currentItem.value.uid === fileItem.uid) {
        currentItem.value.percent = percent;
      }
    } else {
      const uploadingFile = uploadingFiles.value.find(f => f.uid === fileItem.uid);
      if (uploadingFile) {
        uploadingFile.percent = percent;
      }
    }
    options.onProgress({ percent }); // Notify ArUpload about progress
  };

  try {
    const formData = new FormData();
    formData.append('file', fileItem.file);
    if (config.requestData) {
      for (let key in config.requestData) {
        formData.append(key, config.requestData[key]);
      }
    }

    updateProgress(50); // Mock progress

    const response = await commonApi.uploadImage(formData); // Assuming uploadImage uses a config for onUploadProgress

    if (response && response.code === 200) {
      const responseData = response.data || {};
      const uploadedFile = {
        id: responseData.id || '', // API response specific
        url: responseData.fileUrl || '',
        // Use originalName for display, fallback to fileName
        name: responseData.originalName || responseData.fileName || '未命名文件',
        path: responseData.filePath || '',
        size: responseData.fileSize || 0,
        type: responseData.fileType || '',
        storage_mode: responseData.storageType || 'aliyun',
        md5: responseData.md5 || '',
        status: 'complete',
        percent: 100,
        uid: fileItem.uid // Keep UID for mapping if needed
      };

      // Ensure URL is complete (if API returns relative paths)
      // The example API returns full URL, so attachUrl might not be strictly needed here
      // but good for robustness if API behavior changes.
      if (uploadedFile.url && !uploadedFile.url.startsWith('http') && storageMode && storageMode[uploadedFile.storage_mode]) {
        uploadedFile.url = tool.attachUrl(uploadedFile.url, storageMode[uploadedFile.storage_mode]);
      }
      
      updateProgress(100);
      options.onSuccess(response.data); // Pass API response data to ArUpload

      if (!config.multiple) {
        currentItem.value = { ...uploadedFile }; // Update currentItem with full details
        
        let returnValue = uploadedFile;
        if (config.returnType === 'url') returnValue = uploadedFile.url;
        else if (config.returnType === 'id') returnValue = uploadedFile.id;
        else if (config.returnType === 'object') returnValue = { ...uploadedFile }; // Or a subset
        else returnValue = uploadedFile[config.returnType] || uploadedFile.url; // Custom field or fallback to URL
        
        emit('update:modelValue', returnValue);
      } else {
        // Remove from uploading and add to completed
        uploadingFiles.value = uploadingFiles.value.filter(f => f.uid !== fileItem.uid);
        completedFiles.value.push(uploadedFile);

        let currentModelValue = Array.isArray(props.modelValue) ? [...props.modelValue] : [];
        
        let newFileValue;
        if (config.returnType === 'url') newFileValue = uploadedFile.url;
        else if (config.returnType === 'id') newFileValue = uploadedFile.id;
        else if (config.returnType === 'object') newFileValue = { ...uploadedFile };
        else newFileValue = uploadedFile[config.returnType] || uploadedFile.url;

        // Add new value, avoid duplicates if re-uploading or initializing
        if (!currentModelValue.some(val => JSON.stringify(val) === JSON.stringify(newFileValue))) {
             currentModelValue.push(newFileValue);
        }
        emit('update:modelValue', currentModelValue);
      }
      Message.success(`上传成功`);

    } else {
      Message.error(response.message || `${fileItem.file.name} 上传失败`);
      options.onError(response); // Notify ArUpload about error
       if (!config.multiple) {
        currentItem.value = { ...currentItem.value, status: 'error', percent: 0 };
      } else {
        const uploadingFile = uploadingFiles.value.find(f => f.uid === fileItem.uid);
        if (uploadingFile) {
          uploadingFile.status = 'error';
          uploadingFile.percent = 0;
        }
        // Optionally remove from uploadingFiles after a delay or let user remove
      }
    }
  } catch (error) {
    console.error('Upload error:', error);
    Message.error(`${fileItem.file.name} 上传出错`);
    options.onError(error); // Notify ArUpload about error
    if (!config.multiple) {
      currentItem.value = { ...currentItem.value, status: 'error', percent: 0 };
    } else {
      const uploadingFile = uploadingFiles.value.find(f => f.uid === fileItem.uid);
      if (uploadingFile) {
        uploadingFile.status = 'error';
        uploadingFile.percent = 0;
      }
    }
  }
};


const removeSignFile = () => {
  currentItem.value = {};
  emit('update:modelValue', null);
};

const removeFile = (idx) => {
  const fileToRemove = completedFiles.value[idx];
  completedFiles.value.splice(idx, 1);

  if (Array.isArray(props.modelValue)) {
    let updatedModelValue = props.modelValue.filter(val => {
      if (config.returnType === 'url') return val !== fileToRemove.url;
      if (config.returnType === 'id') return val !== fileToRemove.id;
      if (config.returnType === 'object') return val.id ? val.id !== fileToRemove.id : val.url !== fileToRemove.url; // Example for object
      // For custom return types, you might need more specific comparison
      return val !== fileToRemove[config.returnType];
    });
    emit('update:modelValue', updatedModelValue);
  }
};

const init = throttle(async () => {
  uploadingFiles.value = []; // Clear any stale uploads

  if (config.multiple) {
    if (isArray(props.modelValue) && props.modelValue.length > 0) {
      const processedFiles = await Promise.all(
        props.modelValue.map(async (itemValue) => {
          // getFileUrl returns an object like { url: '...', name: '...' (optional) }
          // or the original item if it's already an object with a url
          const fileDetails = await getFileUrl(config.returnType, itemValue, storageMode);
          
          let url = fileDetails.url;
          let name = fileDetails.name; // Use name from getFileUrl if it provides one

          if (typeof itemValue === 'string') { // If modelValue item is just a string (URL or ID)
            if (config.returnType === 'url' || (url && (url.startsWith('http') || url.includes('/')))) {
              // If it's a URL, try to extract name
               if (!name) name = extractFileNameFromUrl(url);
            } else if (config.returnType === 'id') {
              // If it's an ID, we can't get a real name or URL without an API call here.
              // `getFileUrl` (as provided) will set `url` to the ID.
              // This is a limitation if a user-friendly name/preview is needed for ID-only modelValue.
              if (!name) name = `文件ID: ${itemValue}`;
              // `url` would be the ID itself, which isn't previewable.
              // For true preview with ID, `getFileUrl` would need to fetch details.
            }
          } else if (typeof itemValue === 'object' && itemValue !== null) { // If modelValue item is an object
             if (!name) name = itemValue.originalName || itemValue.name || extractFileNameFromUrl(url);
          }
          
          return {
            id: (typeof itemValue === 'object' && itemValue.id) ? itemValue.id : (config.returnType === 'id' ? itemValue : tool.md5(url || Date.now().toString())), // Generate an ID if not available
            url: url,
            name: name || '附件',
            status: 'complete',
            percent: 100,
            // Store original value based on returnType for removal logic
            originalValue: itemValue 
          };
        })
      );
      completedFiles.value = processedFiles.filter(f => f.url); // Only add if URL is valid
    } else {
      completedFiles.value = [];
    }
  } else if (props.modelValue) { // Single file
    const itemValue = props.modelValue;
    const fileDetails = await getFileUrl(config.returnType, itemValue, storageMode);

    let url = fileDetails.url;
    let name = fileDetails.name;

    if (typeof itemValue === 'string') {
      if (config.returnType === 'url' || (url && (url.startsWith('http') || url.includes('/')))) {
         if (!name) name = extractFileNameFromUrl(url);
      } else if (config.returnType === 'id') {
         if (!name) name = `文件ID: ${itemValue}`;
      }
    } else if (typeof itemValue === 'object' && itemValue !== null) {
      if (!name) name = itemValue.originalName || itemValue.name || extractFileNameFromUrl(url);
    }
    
    currentItem.value = {
      id: (typeof itemValue === 'object' && itemValue.id) ? itemValue.id : (config.returnType === 'id' ? itemValue : tool.md5(url || Date.now().toString())),
      url: url,
      name: name || '附件',
      status: 'complete',
      percent: 100
    };
  } else {
    removeSignFile(); // Clears currentItem
    completedFiles.value = []; // Also clear for multiple, though covered by above
  }
}, 200); // Reduced throttle time for quicker UI updates on prop change


watch(() => props.modelValue, (newVal, oldVal) => {
  // Basic deep comparison for arrays/objects to prevent unnecessary re-inits
  if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    init();
  }
}, {
  deep: true, immediate: true
});
</script>

<style lang="less" scoped>
.file-list-container {
  display: flex;
  flex-direction: column;
  gap: 8px; // Spacing between multiple file items
}
.file-list {
  background-color: var(--color-primary-light-1);
  border-radius: 4px;
  height: 36px;
  padding: 0 5px 0 10px; // More padding left for name
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  .file-name {
    flex-grow: 1; // Allow name to take available space
    margin-right: 8px; // Space before delete button
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #165DFF;
    font-size: 14px;
  }
}
</style>