<template>
  <div class="upload-image flex flex-col">
    <!-- 单图 -->
    <a-space wrap>
      <div
        :class="'image-list ' + (config.rounded ? 'rounded-full' : '')"
        v-if="!config.multiple && currentItem && currentItem.url && config.showList"
      >
        <a-button class="delete" @click="removeSignImage()">
          <template #icon>
            <icon-delete />
          </template>
        </a-button>
        <div class="image-container">
          <a-image
            width="130"
            height="130"
            :class="config.rounded ? 'rounded-full' : ''"
            :src="currentItem.url"
            :preview="true"
            :loading-style="{position: 'absolute', left: '50%', top: '50%', transform: 'translate(-50%, -50%)'}"
          />
          <!-- 上传中的蒙层 -->
          <div class="upload-mask" v-if="currentItem.status === 'uploading'">
            <a-spin />
            <div class="upload-text">上传中 {{ currentItem.percent || 0 }}%</div>
          </div>
        </div>
      </div>
      <!-- 多图显示 -->
      <template v-else-if="config.multiple && config.showList">
        <div
          :class="'image-list ' + (config.rounded ? 'rounded-full' : '')"
          v-for="(image, idx) in showImgList"
          :key="idx"
        >
          <a-button class="delete" @click="removeImage(idx)">
            <template #icon>
              <icon-delete />
            </template>
          </a-button>
          <div class="image-container">
            <template v-if="isVideoFile(image)">
              <video
                width="130"
                controls
                :src="image.url"
                :class="config.rounded ? 'rounded-full' : ''"
                style="object-fit: cover;height:100%"
                autoplay
              >
                您的浏览器不支持 video 标签。
              </video>
            </template>
            <template v-else>
              <a-image
                width="130"
                height="130"
                :class="config.rounded ? 'rounded-full' : ''"
                :src="image.url"
                :preview="true"
                :loading-style="{position: 'absolute', left: '50%', top: '50%', transform: 'translate(-50%, -50%)'}"
              />
            </template>
            <!-- 上传中的蒙层 -->
            <div class="upload-mask" v-if="image.status === 'uploading'">
              <a-spin />
              <div class="upload-text">上传中 {{ image.percent || 0 }}%</div>
            </div>
          </div>
        </div>
      </template>

      <a-upload
        :custom-request="uploadImageHandler"
        @before-upload="beforeUploadValidation"
        :show-file-list="false"
        :multiple="config.multiple"
        :accept="config.accept || '.jpg,jpeg,.gif,.png,.svg,.bpm'"
        :disabled="config.disabled"
        :limit="config.limit"
      >
        <template #upload-button>
          <slot name="customer">
            <div
              :class="
                'upload-skin ' +
                (config.rounded ? 'rounded-full' : 'rounded-sm')
              "
              v-if="!props.modelValue || config.multiple"
            >
              <div class="icon text-3xl">
                <component :is="config.icon" />
              </div>
              <div class="title">
                {{
                  config.title === "buttonText"
                    ? $t("upload.buttonText")
                    : config.title
                }}
              </div>
            </div>
          </slot>
        </template>
      </a-upload>
    </a-space>
    <div class="upload-tip text-gray-500 text-xs">{{config.tip?config.tip:'最多上传5张图片，建议尺寸800x800px，大小不超过2MB'}}</div>
  </div>
</template>
<script setup>
import { ref, inject, watch } from "vue";
import tool from "~/utils/tool.js";
import { isArray, throttle } from "lodash";
import { getFileUrl, uploadRequest } from "../js/utils.js";
import { Message } from "@arco-design/web-vue";
import commonApi from "~/api/common.js";

import { useI18n } from "vue-i18n";

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: () => {},
  },
});
const emit = defineEmits(["update:modelValue"]);

const isVideoFile = (file) => {
  if (!file || !file.url) {
    return false;
  }
  const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.wmv', '.flv'];
  const url = file.url.toLowerCase();
  return videoExtensions.some(ext => url.endsWith(ext));
};
const config = inject("config");
console.log("🚀 ~ file: image-upload.vue:115 ~ config:", config)
const storageMode = inject("storageMode");
const showImgList = ref([]);
const signImage = ref();
const currentItem = ref({});

// 添加一个标志位，避免重复初始化
const isUpdatingFromUpload = ref(false);

const beforeUploadValidation = (file) => {
  console.log("🚀 ~ file: image-upload.vue:126 ~ file:", file)
  // 1. 文件数量校验
  if (config.multiple) {
    if (typeof config.limit === 'number' && config.limit > 0) {
      console.log("🚀 ~ file: image-upload.vue:130 ~ showImgList:", showImgList)

      const activeImageCount = showImgList.value.filter(
        item => item.url 
      ).length;
      if (activeImageCount >= config.limit) {
        Message.error(`最多只能上传 ${config.limit} 个文件。`);
        return false; // 已达到或超过限制，不允许添加更多
      }
    }
  } else { // 单图上传模式
    // 检查 currentItem 是否已有一个状态为 'success' 的图片
    if (currentItem.value && currentItem.value.url && currentItem.value.status === 'success') {
      Message.error(`只能上传 1 个文件。请先删除当前文件再尝试上传。`);
      return false;
    }
  }

  // 2. 文件类型校验
  let allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif']; // 默认允许的类型
  let acceptMessage = 'JPG, PNG, 或 GIF';

  if (config.accept && typeof config.accept === 'string' && config.accept.trim() !== '') {
    const extensions = config.accept.split(',').map(ext => ext.trim().toLowerCase().substring(1));
    const mimeTypeMap = {
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'svg': 'image/svg+xml',
      'bmp': 'image/bmp',
      'mp4': 'video/mp4',
      // 根据需要添加更多映射
    };
    allowedMimeTypes = extensions.map(ext => mimeTypeMap[ext]).filter(mime => mime);
    // 如果通过config.accept指定了类型，但转换后为空（可能是不支持的扩展名），则阻止上传
    if (extensions.length > 0 && allowedMimeTypes.length === 0) {
        Message.error(`配置的 accept 类型 (${config.accept}) 无法解析为有效的图片类型。`);
        return false;
    }
    // 如果 allowedMimeTypes 为空数组但 extensions 不为空，说明配置的都是无法识别的类型
    // 如果 extensions 为空（例如 config.accept 为空字符串或只有逗号），则使用默认值
    if (allowedMimeTypes.length > 0) {
        acceptMessage = extensions.map(ext => ext.toUpperCase()).join(', ');
    } else if (extensions.length > 0 && allowedMimeTypes.length === 0) {
        // config.accept 有值，但都是无法识别的扩展名
        Message.error(`文件 ${file.name} 类型不受支持。请上传 ${extensions.map(ext => ext.toUpperCase()).join(', ')} 格式的图片。`);
        return false;
    }
    // 如果 config.accept 为空或无效，则 allowedMimeTypes 仍为默认值
  }

  if (allowedMimeTypes.length > 0 && !allowedMimeTypes.includes(file.type)) {
    Message.error(`文件 ${file.name} 类型不受支持。请上传 ${acceptMessage} 格式的图片。`);
    return false;
  }

  // 文件大小校验 (使用注入的config中的size，默认为2MB)
  const maxSizeMB = config.size || 2;
  const maxSizeInBytes = maxSizeMB * 1024 * 1024;

  if (file.size > maxSizeInBytes) {
    Message.error(`文件 ${file.name} 大小超过 ${maxSizeMB}MB 限制。`);
    return false;
  }

  return true;
};

const uploadImageHandler = async (options) => {
  if (!options.fileItem) return;
  
  // 检查是否是多文件上传
  const isMultipleFiles = config.multiple && options.fileList && options.fileList.length > 0;
  const files = isMultipleFiles ? options.fileList : [options.fileItem.file];
  console.log("🚀 ~ file: image-upload.vue:130 ~ files:", files)
  
  // 创建上传进度显示
  if (!config.multiple) {
    // 单图模式
    currentItem.value = {
      ...options.fileItem,
      status: 'uploading',
      percent: 0,
      url: options.fileItem.url || URL.createObjectURL(options.fileItem.file)
    };
  } else {
    // 多图模式 - 为每个文件创建预览项
    const uploadItems = files.map(file => {
      // 创建临时的blob URL用于预览
      const blobUrl = URL.createObjectURL(file);
      console.log(`创建了临时URL预览：${file.name} -> ${blobUrl}`);
      
      return {
        file,
        status: 'uploading',
        percent: 0,
        url: blobUrl,
        _isTemporary: true, // 标记为临时项
        _fileName: file.name // 保存文件名，用于后续匹配
      };
    });
    
    // 记录当前列表长度，用于后续更新
    const startIndex = showImgList.value.length;
    
    // 先添加所有预览项，显示上传进度
    uploadItems.forEach((item, idx) => {
      item.index = startIndex + idx;
      showImgList.value.push(item);
    });
    
    console.log('初始化后的图片列表：', JSON.parse(JSON.stringify(showImgList.value)));
  }
  
  try {
    // 创建一个数组来存储所有成功上传的结果
    const uploadResults = [];
    
    // 设置标志位，表示正在从上传处理更新数据
    isUpdatingFromUpload.value = true;
    
    // 依次处理每个文件，而不是并行上传
    // 这样可以确保所有文件都能正确上传和返回
    for (let index = 0; index < files.length; index++) {
      const file = files[index];
      try {
        // 创建表单数据
        const formData = new FormData();
        formData.append('file', file);
        if (config.requestData) {
          for (let key in config.requestData) {
            formData.append(key, config.requestData[key]);
          }
        }
        
        // 更新进度为50%
        if (!config.multiple) {
          currentItem.value.percent = 50;
        } else {
          // 使用文件名匹配，而不是使用URL包含文件名，因为这可能不可靠
          const itemIndex = showImgList.value.findIndex(item => 
            item._isTemporary && item._fileName === file.name);
          if (itemIndex >= 0) {
            showImgList.value[itemIndex].percent = 50;
          }
        }
        
        // 调用上传API
        const response = await commonApi.uploadImage(formData);
        
        // 检查响应状态
        if (response && response.code === 200) {
          // 上传成功，处理返回的数据
          const responseData = response.data || {};
          
          // 构建结果对象，适配新的接口返回值结构
          const result = {
            url: responseData.fileUrl || '',
            path: responseData.filePath || '',
            name: responseData.fileName || '',
            size: responseData.fileSize || 0,
            type: responseData.fileType || '',
            id: responseData.id || '',
            storage_mode: responseData.storageType || 'aliyun', // 使用返回的存储类型
            md5: responseData.md5 || ''
          };
          
          // 确保 URL 是完整的
          if (result.url && !result.url.startsWith('http')) {
            result.url = tool.attachUrl(result.url, storageMode[result.storage_mode]);
          }
          
          // 预加载图片以减少闪烁
          const preloadImage = (url) => {
            return new Promise((resolve) => {
              const img = new Image();
              img.onload = () => resolve(url);
              img.onerror = () => resolve(url); // 即使加载失败也继续
              img.src = url;
            });
          };
          
          // 预加载图片
          await preloadImage(result.url);
          
          // 更新上传项状态
          if (!config.multiple) {
            // 单图模式
            currentItem.value = {
              url: result.url,
              percent: 100,
              status: 'complete'
            };
          } else {
            // 多图模式 - 更新对应的预览项
            // 使用文件名来匹配预览项，这样更可靠
            const itemIndex = showImgList.value.findIndex(item => 
              item._isTemporary && item._fileName === file.name);
              
            console.log(`尝试匹配文件 ${file.name} 的预览项，索引：${itemIndex}`);
              
            if (itemIndex >= 0) {
              // 保存原始项以便调试
              const originalItem = {...showImgList.value[itemIndex]};
              
              // 更新现有项，保留原始索引和文件名
              showImgList.value[itemIndex] = {
                ...result,
                status: 'complete',
                percent: 100,
                _isTemporary: false,
                _fileName: file.name,
                index: originalItem.index
              };
              
              console.log(`成功更新预览项，从 ${originalItem.url} 替换为 ${result.url}`);
            } else {
              console.warn(`找不到文件 ${file.name} 的预览项，直接添加新项`);
              // 如果找不到匹配项，直接添加新项
              showImgList.value.push({
                ...result,
                status: 'complete',
                percent: 100,
                _fileName: file.name
              });
            }
          }
          
          // 将成功上传的结果添加到数组中
          uploadResults.push(result);
        } else {
          // 上传失败
          console.error('上传失败，响应:', response);
          
          // 更新失败状态
          if (config.multiple) {
            const itemIndex = showImgList.value.findIndex(item => 
              item._isTemporary && item._fileName === file.name);
            if (itemIndex >= 0) {
              showImgList.value[itemIndex].status = 'error';
              showImgList.value[itemIndex].percent = 0;
            }
          }
        }
      } catch (error) {
        console.error(`上传第 ${index + 1} 张图片出错:`, error);
      }
    }
    
    // 所有文件处理完毕，使用 uploadResults 作为有效结果
    const validResults = uploadResults;
    
    console.log('有效的上传结果：', validResults);
    console.log('当前图片列表：', showImgList.value);
    
    // 在上传完成后触发更新
    if (validResults.length > 0) {
      if (!config.multiple) {
        // 单图模式，直接返回第一个有效结果
        let value = validResults[0];
        if (config.returnType === 'url') {
          value = value.url || '';
        } else if (config.returnType === 'path') {
          value = value.path || '';
        } else if (config.returnType === 'id') {
          value = value.id || '';
        } else {
          value = value[config.returnType] || value.url || '';
        }
        emit('update:modelValue', value);
        console.log('单图上传完成，返回值格式：', value);
      } else {
        // 多图模式
        // 直接使用所有成功上传的结果
        // 不再从 showImgList 中过滤，因为这可能导致部分图片丢失
        
        // 直接使用所有有效的上传结果
        let files = [];
        
        // 打印原始上传结果，便于调试
        console.log('原始上传结果：', validResults);
        
        // 根据返回类型提取URL
        if (config.returnType === 'url') {
          files = validResults.map(item => item.url || '');
        } else if (config.returnType === 'path') {
          files = validResults.map(item => item.path || '');
        } else if (config.returnType === 'id') {
          files = validResults.map(item => item.id || '');
        } else {
          files = validResults.map(item => item[config.returnType] || item.url || '');
        }
        
        // 打印提取的URL
        console.log('提取的URL：', files);
        
        // 如果模型值已经存在，则将新上传的URL与已有的URL合并
        if (Array.isArray(props.modelValue) && props.modelValue.length > 0) {
          // 将已有的URL与新上传的URL合并，并去重
          const existingUrls = props.modelValue.filter(url => 
            typeof url === 'string' && !url.startsWith('blob:'));
          
          // 合并并去重
          files = [...new Set([...existingUrls, ...files])];
          console.log('合并后的URL数组：', files);
        }
        
        // 触发更新
        emit('update:modelValue', files);
        console.log('多图片上传完成，返回值格式：', files);
      }
      
      // 显示成功提示
      if (validResults.length === files.length) {
        Message.success(files.length > 1 ? `成功上传了 ${validResults.length} 张图片` : '图片上传成功');
      } else {
        Message.warning(`已上传 ${validResults.length}/${files.length} 张图片，部分图片上传失败`);
      }
    } else {
      // 所有图片都上传失败
      Message.error('所有图片上传失败');
      if (!config.multiple) {
        currentItem.value = {};
      }
    }
    
    // 延迟重置标志位，确保watch中的init不会重复处理
    setTimeout(() => {
      isUpdatingFromUpload.value = false;
    }, 100);
  } catch (error) {
    console.error('上传过程中发生错误:', error);
    Message.error('上传图片过程中发生错误');
    if (!config.multiple) {
      currentItem.value = {};
    }
    
    // 重置标志位
    isUpdatingFromUpload.value = false;
  }
};

const removeSignImage = () => {
  currentItem.value = {};
  signImage.value = undefined;
  emit("update:modelValue", null);
};

const removeImage = (idx) => {
  showImgList.value.splice(idx, 1);
  let files = [];
  
  // 过滤掉所有blob开头的URL，只保留真实上传后的图片URL
  const filteredList = showImgList.value.filter(item => {
    const url = typeof item === 'string' ? item : (item.url || '');
    return !url.startsWith('blob:');
  });
  
  // 统一处理返回格式，确保每个项都是字符串
  if (config.returnType === 'url') {
    files = filteredList.map(item => {
      if (typeof item === 'string') return item;
      return item.url || '';
    });
  } else {
    files = filteredList.map(item => {
      if (typeof item === 'string') return item;
      return item[config.returnType] || item.url || '';
    });
  }
  
  console.log('删除图片后的数据格式：', files);
  emit("update:modelValue", files);
};

const init = throttle(async () => {
  if (config.multiple) {
    if (isArray(props.modelValue) && props.modelValue.length > 0) {
      // 统一处理初始数据，确保每个项都是字符串或对象
      const normalizedModelValue = props.modelValue.map(item => {
        // 如果已经是字符串，直接返回
        if (typeof item === 'string') return item;
        // 如果是对象且有url属性，返回url
        if (item && typeof item === 'object' && item.url) return item.url;
        // 其他情况返回原始值
        return item;
      });
      
      const result = await normalizedModelValue.map(async (item) => {
        return await getFileUrl(config.returnType, item, storageMode);
      });
      const data = await Promise.all(result);
      
      // 确保showImgList中的每个项都有统一的格式
      if (config.returnType === "url") {
        showImgList.value = data.map((url) => {
          // 如果已经是字符串，包装为对象
          if (typeof url === 'string') return { url };
          // 如果是对象，确保有url属性
          return { url: url.url || '' };
        });
      } else {
        showImgList.value = data.map((item) => {
          // 如果已经是字符串，包装为对象
          if (typeof item === 'string') {
            return {
              url: item,
              [config.returnType]: item
            };
          }
          // 如果是对象，确保有必要的属性
          return {
            url: item.url || '',
            [config.returnType]: item[config.returnType] || item.url || '',
          };
        });
      }
    } else {
      showImgList.value = [];
    }
  } else if (props.modelValue) {
    // 先初始化一个空对象，确保有全新的状态
    currentItem.value = {
      percent: 100,
      status: 'complete'
    };
    
    if (config.returnType === "url") {
      signImage.value = props.modelValue;
      currentItem.value.url = props.modelValue;
    } else {
      const result = await getFileUrl(
        config.returnType,
        props.modelValue,
        storageMode
      );
      signImage.value = result.url;
      currentItem.value.url = result.url;
    }
    // 再次确认状态被正确设置
    currentItem.value.percent = 100;
    currentItem.value.status = 'complete';
    
    console.log('初始化完成，当前状态:', currentItem.value);
  } else {
    removeSignImage();
  }
}, 1000);

watch(
  () => props.modelValue,
  (val) => {
    // 只有不是从上传过程中更新数据时，才执行初始化
    if (!isUpdatingFromUpload.value) {
      init();
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<style lang="less" scoped>
.upload-skin {
  background-color: var(--color-fill-2);
  border: 1px dashed var(--color-fill-4);
  width: 130px;
  height: 130px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .icon,
  .title {
    color: var(--color-text-3);
  }
}

.image-list {
  cursor: pointer;
  position: relative;
  background-color: var(--color-fill-2);
  width: 130px;
  height: 130px;

  .delete {
    position: absolute;
    z-index: 99;
    right: 3px;
    top: 3px;
    display: none;
  }

  .progress {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
  }
  
  .image-container {
    position: relative;
    width: 100%;
    height: 100%;
  }
}

/* 上传蒙层样式 */
.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #fff;
  z-index: 10;
  border-radius: var(--border-radius-medium);
  
  .upload-text {
    margin-top: 8px;
    font-size: 14px;
  }
}

.image-list:hover {
  .delete {
    display: block;
  }
}

.upload-skin:hover {
  border: 1px dashed rgb(var(--primary-6));
}
</style>
