import commonApi from '~/api/common.js'
import tool from '~/utils/tool.js'
import file2md5 from 'file2md5'

export const getFileUrl = async (returnType, value, storageMode) => {
  try {
    // 不管returnType是什么，都直接处理为URL
    if (typeof value === 'string') {
      // 如果是字符串，直接返回该字符串作为URL
      return { url: value }
    } else if (value && typeof value === 'object' && value.url) {
      // 如果是对象并且已经有url属性，直接返回该对象
      return value
    } else {
      // 其他情况，返回一个空对象
      console.log('无法处理的value类型:', typeof value, '返回空对象')
      return { url: '' }
    }
  } catch (error) {
    console.error('获取文件URL出错:', error)
    return { url: '' } // 发生错误时返回空对象
  }
}

export const uploadRequest = async (file, type, method, requestData = {}) => {
  const dataForm = new FormData()
  dataForm.append('file', file) // 直接使用'file'作为参数名
  
  // 添加其他请求参数（如果有）
  for (let name in requestData) {
    dataForm.append(name, requestData[name])
  }
  
  const response = await commonApi[method](dataForm)
  console.log('接口原始响应:', response) // 添加日志输出
  // 直接返回完整的响应对象，不再只返回data字段
  return response
}
