<template>
  <div class="floating-buttons-container">
    <!-- 需求提交按钮 -->
    <a-button class="floating-button demand-button" shape="circle" @click="openDemandModal">
      <icon-message />
    </a-button>
    
    <!-- AI机器人按钮 -->
    <a-button class="floating-button robot-button" shape="circle" @click="openRobotModal">
      <icon-robot />
    </a-button>
    
    <!-- 需求提交弹窗组件 -->
    <demand-modal ref="demandModalRef" />
    
    <!-- AI机器人弹窗组件 -->
    <robot-modal ref="robotModalRef" />
  </div>
</template>

<script setup>
import { ref } from 'vue'

// 导入弹窗组件
import DemandModal from './demand-modal.vue'
import RobotModal from './robot-modal.vue'

// 组件引用
const demandModalRef = ref(null)
const robotModalRef = ref(null)

// 打开需求弹窗
const openDemandModal = () => {
  console.log('点击需求按钮')
  demandModalRef.value?.open()
}

// 打开机器人弹窗
const openRobotModal = () => {
  console.log('点击机器人按钮')
  robotModalRef.value?.open()
}
</script>

<style scoped>
.floating-buttons-container {
  position: fixed;
  right: 15px;
  bottom: 100px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.floating-button {
  margin-bottom: 3px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.floating-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.button-icon {
  width: 20px;
  height: 20px;
}
</style>
