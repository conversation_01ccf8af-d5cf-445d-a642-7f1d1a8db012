<template>
  <a-modal
    :visible="visible"
    @update:visible="visible = $event"
    title="需求提交"
    :width="800"
    :mask-closable="false"
    :footer="false"
  >
    <!-- 使用ma-form组件和wang-editor -->
    <ma-form 
      v-model="demandForm" 
      :columns="formColumns" 
      layout="vertical" 
      :options="{ showButtons: false }"
    />
    
    <div class="flex justify-end gap-2 mt-4">
      <a-button @click="close" :disabled="submitting">关闭</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="submitting">提交</a-button>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import MaForm from '~/components/base/ma-form/index.vue'
import commonApi from '@/api/common'

// 控制弹窗显示
const visible = ref(false)
const submitting = ref(false)

// 需求表单数据
const demandForm = reactive({
  submitter: '',
  content: '',
  remark: '',
  current_url: ''
})

// 表单配置
const formColumns = [
  {
    title: '当前链接',
    dataIndex: 'current_url',
    formType: 'input',
    disabled: true
  },
  {
    title: '提交人',
    dataIndex: 'submitter',
    formType: 'input',
    placeholder: '请输入提交人姓名'
  },
  {
    title: '内容',
    dataIndex: 'content',
    formType: 'wang-editor',
    placeholder: '请输入需求内容',
    height: 300
  },
  {
    title: '备注',
    dataIndex: 'remark',
    formType: 'textarea',
    placeholder: '请输入备注信息（选填）',
    rows: 3
  }
]

// 打开弹窗
const open = () => {
  // 获取当前URL
  demandForm.current_url = window.location.href
  visible.value = true
}

// 关闭弹窗
const close = () => {
  visible.value = false
}

// 提交需求
const handleSubmit = async () => {
  if (!demandForm.content) {
    Message.warning('请填写需求内容')
    return
  }
  
  try {
    submitting.value = true
    
    // 调用API提交留言
    const response = await commonApi.messageBoard(demandForm)
    
    if (response.code === 200) {
      // 提交成功
      Message.success(response.message || '需求提交成功')
      
      // 重置表单
      demandForm.submitter = ''
      demandForm.content = ''
      demandForm.remark = ''
      
      // 关闭弹窗
      close()
    } else {
      // 提交失败
      Message.error(response.message || '需求提交失败，请稍后重试')
    }
  } catch (error) {
    console.error('需求提交出错:', error)
    Message.error('需求提交出错，请稍后重试')
  } finally {
    submitting.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close
})
</script>
