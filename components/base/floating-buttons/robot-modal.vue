<template>
  <a-modal
    :visible="visible"
    @update:visible="visible = $event"
    title="智能助手"
    :width="800"
    :mask-closable="false"
    :footer="false"
    :unmount-on-close="false"
  >
    <div class="ai-robot-container">
      <!-- 聊天消息区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <div v-if="messages.length === 0" class="empty-state">
          <icon-robot class="text-5xl text-gray-300 mb-2" />
          <p class="text-center text-gray-500">您可以在下方输入问题，开始与AI助手对话</p>
        </div>
        
        <div v-else class="message-list">
          <div 
            v-for="(message, index) in messages" 
            :key="index"
            :class="['message-item', message.role === 'user' ? 'user-message' : 'ai-message']"
          >
            <div class="message-avatar">
              <icon-user v-if="message.role === 'user'" />
              <icon-robot v-else />
            </div>
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(message.content)"></div>
            </div>
          </div>
        </div>
        
        <!-- 加载指示器 -->
        <div v-if="loading" class="loading-indicator">
          <a-spin />
          <span class="ml-2">AI助手思考中...</span>
        </div>
      </div>
      
      <!-- 输入区域 -->
      <div class="chat-input-area">
        <a-textarea
          v-model="userInput"
          placeholder="请输入您的问题..."
          :auto-size="{ minRows: 2, maxRows: 5 }"
          allow-clear
          @keypress="handleKeyPress"
        />
        <div class="flex justify-between items-center mt-2">
          <div></div>
          <div class="flex gap-2">
            <a-button @click="close">关闭</a-button>
            <a-button type="primary" :loading="loading" @click="sendMessage">发送</a-button>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { Message } from '@arco-design/web-vue'

// Dify API 配置
const DIFY_CONFIG = {
  baseUrl: 'https://dify.ioa.8080bl.com/v1',  // Dify API 地址
  apiKey: 'app-W8EA4zdAI3ra7q7nunxJEJ0S',     // API Key
  userId: 'default-user'
}

// 控制弹窗显示
const visible = ref(false)
const loading = ref(false)
const userInput = ref('')
const messagesContainer = ref(null)

// 聊天消息
const messages = reactive([])
const conversationId = ref('')

// 打开弹窗
const open = () => {
  visible.value = true
  console.log('AI机器人弹窗已打开')
  
  // 如果是首次打开且没有消息，添加一条欢迎消息
  if (messages.length === 0) {
    messages.push({
      role: 'assistant',
      content: '您好！我是智能助手，有什么可以帮您解答的问题吗？'
    })
  }
}

// 关闭弹窗
const close = () => {
  visible.value = false
}

// 清空聊天记录
const clearChat = () => {
  // 清空本地消息列表
  messages.splice(0, messages.length)
  conversationId.value = ''
  
  // 添加欢迎消息
  messages.push({
    role: 'assistant',
    content: '您好！我是智能助手，有什么可以帮您解答的问题吗？'
  })
  
  Message.success('对话历史已清空')
}

// 格式化消息内容（处理换行符等）
const formatMessage = (content) => {
  if (!content) return ''
  return content
    .replace(/\n/g, '<br>')
    .replace(/\r\n/g, '<br>')
}

// 发送消息到Dify API
const sendMessage = async () => {
  if (!userInput.value.trim()) {
    Message.warning('请输入问题内容')
    return
  }
  
  // 添加用户消息到列表
  const userMessage = userInput.value.trim()
  messages.push({
    role: 'user',
    content: userMessage
  })
  
  // 清空输入框
  userInput.value = ''
  
  // 滚动到底部
  await nextTick()
  scrollToBottom()
  
  // 设置加载状态
  loading.value = true
  
  try {
    // 发送请求到Dify API
    const response = await fetch(`${DIFY_CONFIG.baseUrl}/chat-messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DIFY_CONFIG.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        inputs: {},
        query: userMessage,
        response_mode: 'blocking',
        conversation_id: conversationId.value || '',
        user: DIFY_CONFIG.userId
      })
    })
    
    if (!response.ok) {
      throw new Error(`请求失败: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('Dify API 响应:', data)
    
    // 保存对话ID
    if (data.conversation_id) {
      conversationId.value = data.conversation_id
    }
    
    // 添加AI回复到消息列表
    if (data.answer) {
      messages.push({
        role: 'assistant',
        content: data.answer
      })
      
      // 滚动到底部
      await nextTick()
      scrollToBottom()
    } else {
      throw new Error('API返回的数据格式不正确')
    }
  } catch (error) {
    console.error('发送消息出错:', error)
    Message.error('发送消息失败，请稍后重试')
    
    // 添加错误消息
    messages.push({
      role: 'assistant',
      content: '抱歉，我遇到了一些问题，无法回答您的问题。请稍后再试。'
    })
    
    // 滚动到底部
    await nextTick()
    scrollToBottom()
  } finally {
    loading.value = false
  }
}

// 处理按键事件（Ctrl+Enter 发送消息）
const handleKeyPress = (e) => {
  if (e.ctrlKey && e.key === 'Enter') {
    e.preventDefault()
    sendMessage()
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close
})
</script>

<style lang="less" scoped>
.ai-robot-container {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #f9f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  gap: 12px;
  max-width: 85%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #e8f3ff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: #f2f3ff;
}

.message-content {
  background-color: #fff;
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.user-message .message-content {
  background-color: rgb(var(--primary-6));
  color: #fff;
}

.message-text {
  line-height: 1.5;
  word-break: break-word;
}

.user-message .message-text :deep(a) {
  color: #fff;
  text-decoration: underline;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 16px;
  color: #86909c;
}

.chat-input-area {
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
}
</style>
