<template>
  <div class="contribution-stats">
    <a-row :gutter="16" class="mb-4">
      <a-col :span="24">
        <a-card class="stats-card">
          <template #title>
            <div class="card-title">
              <IconTag />
              <span>每日提交数量趋势</span>
            </div>
          </template>
          <CommitTrendChart :commits="commits" :loading="loading" />
        </a-card>
      </a-col>
    </a-row>
    
    <a-row :gutter="16" class="mb-4">
      <a-col :span="24">
        <ContributionRanking :commits="commits" :loading="loading" />
      </a-col>
    </a-row>
    
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card class="stats-card">
          <template #title>
            <div class="card-title">
              <IconUserGroup />
              <span>贡献者统计</span>
            </div>
          </template>
          <ContributorsList :contributors="contributors" :loading="loading" />
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card class="stats-card">
          <template #title>
            <div class="card-title">
              <IconCalendar />
              <span>提交频率分析</span>
            </div>
          </template>
          <CommitFrequencyStats :commits="commits" :loading="loading" />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { IconUserGroup, IconCalendar, IconTag } from '@arco-design/web-vue/es/icon';
import CommitTrendChart from './CommitTrendChart.vue';
import ContributorsList from './ContributorsList.vue';
import CommitFrequencyStats from './CommitFrequencyStats.vue';
import ContributionRanking from './ContributionRanking.vue';

const props = defineProps({
  commits: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 计算贡献者统计
const contributors = computed(() => {
  if (!props.commits || props.commits.length === 0) return [];
  
  // 按作者分组统计
  const authorStats = {};
  
  props.commits.forEach(commit => {
    const author = commit.author_name;
    
    if (!authorStats[author]) {
      authorStats[author] = {
        name: author,
        email: commit.author_email || '',
        avatar_url: commit.author_avatar_url || '',
        commits: 0,
        additions: 0,
        deletions: 0
      };
    }
    
    authorStats[author].commits += 1;
    authorStats[author].additions += commit.stats?.additions || 0;
    authorStats[author].deletions += commit.stats?.deletions || 0;
  });
  
  // 转换为数组并计算百分比
  const totalCommits = props.commits.length;
  const contributorsList = Object.values(authorStats).map(author => ({
    ...author,
    percentage: Math.round((author.commits / totalCommits) * 100)
  }));
  
  // 按提交次数降序排序
  return contributorsList.sort((a, b) => b.commits - a.commits);
});
</script>

<style scoped>
.contribution-stats {
  margin-top: 16px;
  width: 100%;
  display: block;
}

.stats-card {
  height: 100%;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>