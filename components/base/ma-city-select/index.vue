<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<script setup lang="ts">
import type { Area, ModelType } from './type.ts'
import common from '@/api/common'
defineOptions({ name: 'MaCitySelect' })

const { mode = 'name', showLevel = 3 } = defineProps<{
  mode: 'name' | 'code'
  showLevel: 1 | 2 | 3
}>()

// 定义默认提示文本（使用硬编码中文）
const placeholders = {
  province: '请选择省/直辖市/自治区',
  city: '请选择地级市/市辖区',
  area: '请选择区县'
}

const model = defineModel<ModelType>({ province: undefined, city: undefined, area: undefined})
const province = ref<Area>([])
const city = ref<Area>([])
const area = ref<Area>([])

function provinceChange(val, clear = true) {
  if (clear) {
    model.value.city = undefined
    model.value.area = undefined
    city.value = []
    area.value = []
  }
  // 从当前加载的province数据中查找
  city.value = province.value.find((item: Area) => mode === 'name' ? item.name === val : item.code === val)?.children ?? []
}

function cityChange(val, clear = true) {
  if (clear) {
    model.value.area = undefined
    area.value = []
  }
  // 从当前加载的city数据中查找
  area.value = city.value.find((item: Area) => mode === 'name' ? item.name === val : item.code === val)?.children ?? []
}

onMounted(async () => {
  // 初始化时获取区域数据
  await getTreeList()
  
  // 如果已有选中值，则初始化对应的市和区数据
  if (model.value.province) {
    city.value = province.value.find(
      (item: Area) => mode === 'name' ? item.name === model.value.province : item.code === model.value.province,
    )?.children ?? []
    
    if (model.value.city) {
      area.value = city.value.find(
        (item: Area) => mode === 'name' ? item.name === model.value.city : item.code === model.value.city,
      )?.children ?? []
    }
  }
})

const getTreeList = async () => {
  try {
    const res = await common.getTreeRegion()
    if (res && res.code === 200 && res.data) {
      // 清空现有数据
      province.value = []
      
      // 将API返回的数据转换为组件需要的格式并加载到province中
      res.data.forEach((item) => {
        const provinceItem = {
          name: item.name,
          code: item.code.toString(),
          children: []
        }
        
        // 处理市级数据
        if (item.children && item.children.length > 0) {
          item.children.forEach((cityItem) => {
            const city = {
              name: cityItem.name,
              code: cityItem.code.toString(),
              children: []
            }
            
            // 处理区县级数据
            if (cityItem.children && cityItem.children.length > 0) {
              cityItem.children.forEach((areaItem) => {
                city.children.push({
                  name: areaItem.name,
                  code: areaItem.code.toString(),
                  children: []
                })
              })
            }
            
            provinceItem.children.push(city)
          })
        }
        
        province.value.push(provinceItem)
      })
      
      console.log('区域数据加载成功，共加载省份数量：', province.value.length)
    } else {
      console.error('获取区域数据失败：', res)
      // 加载失败时使用备用的空数据结构
      province.value = []
    }
  } catch (error) {
    console.error('获取区域数据出错：', error)
    // 出错时使用备用的空数据结构
    province.value = []
  }
}
</script>

<template>
  <div class="grid grid-cols-3 gap-x-2 w-full" v-bind="$attrs">
    <el-select
      v-model="model.province"
      class="w-full"
      :placeholder="placeholders.province"
      clearable
      @change="provinceChange"
      @clear="
        () => {
          model.province = undefined
          model.city = undefined
          model.area = undefined
        }
      "
    >
      <el-option v-for="item in province" :key="item" :label="item.name" :value="mode === 'name' ? item.name : item.code" />
    </el-select>
    <el-select
      v-if="showLevel >= 2"
      v-model="model.city"
      class="w-full"
      :placeholder="placeholders.city"
      clearable
      @change="cityChange"
      @clear="
        () => {
          model.city = undefined
          model.area = undefined
        }
      "
    >
      <el-option v-for="item in city" :key="item" :label="item.name" :value="mode === 'name' ? item.name : item.code" />
    </el-select>
    <el-select
      v-if="showLevel >= 3"
      v-model="model.area"
      class="w-full"
      :placeholder="placeholders.area"
      clearable
      @clear="
        () => {
          model.area = undefined
        }
      "
    >
      <el-option v-for="item in area" :key="item" :label="item.name" :value="mode === 'name' ? item.name : item.code" />
    </el-select>
  </div>
</template>