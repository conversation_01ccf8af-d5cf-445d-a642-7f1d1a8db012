<!--
 - MineAdmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="w-full">
    <a-input-group class="w-full">
      <a-input placeholder="请点击右侧按钮选择图标" v-if="props.preview" allow-clear v-model="currentIcon" />
      <div class="icon-container" v-if="props.preview">
        <!-- URL类型图标使用a-image渲染 -->
        <a-image v-if="currentIcon && isUrlIcon(currentIcon)" :src="currentIcon" :width="24" :height="24" />
        <!-- Arco Design图标使用component渲染 -->
        <component :is="currentIcon" v-else-if="currentIcon && !isIconfont" />
        <!-- Iconfont图标使用i标签渲染 -->
        <i class="iconfont" :class="currentIcon" v-else-if="currentIcon && isIconfont" style="font-size: 24px;"/>
      </div>
      <a-button type="primary" @click="() => visible = true">选择图标</a-button>
    </a-input-group>

    <a-modal v-model:visible="visible" width="800px" draggable :footer="false">
      <template #title>选择图标</template>
      <a-tabs class="tabs">
        <a-tab-pane v-if="showTabs.includes('arco')" key="arco" title="Arco Design">
          <ul class="arco">
            <li
              v-for="icon in arcodesignIcons"
              :key="icon"
              @click="selectIcon(icon, 'arco')"
            >
              <component :is="icon" />
            </li>
          </ul>
        </a-tab-pane>
        <!-- <a-tab-pane v-if="showTabs.includes('mine')" key="mine" title="MineAdmin">
          <ul class="mine">
            <li
              v-for="icon in mineadminIcons"
              :key="icon"
              @click="selectIcon(icon, 'mine')"
            >
              <component :is="icon" />
            </li>
          </ul>
        </a-tab-pane> -->
        
        <a-tab-pane v-if="showTabs.includes('iconfont')" key="iconfont" title="Iconfont">
          <ul class="iconfont">
            <li
              v-for="icon in iconList"
              :key="icon"
              @click="selectIcon(icon, 'iconfont')"
            >
              <i class="iconfont" :class="icon" style="font-size: 32px;"/>
            </li>
          </ul>
        </a-tab-pane>

      </a-tabs>
    </a-modal>
  </div>
</template>

<script setup>
  import { extractIconClassNames } from '@/utils/iconExtractor';
  import iconfontCss from '@/assets/css/iconfont.css?raw';
  import { reactive, ref, computed } from 'vue'
  import * as arcoIcons from '@arco-design/web-vue/es/icon'

  const mineadminIcons = reactive([])
  const arcodesignIcons = reactive([])
  const visible = ref(false)

  const props = defineProps({
    modelValue: { type: String },
    preview: { type: Boolean, default: true },
    showTabs: { type: Array, default: () => ['arco', 'iconfont'] },
  })

  const emit = defineEmits(['update:modelValue'])

  // 是否为iconfont图标
// 判断是否为iconfont图标，如果是以icon-开头或在iconList中存在的图标类名，则认为是iconfont图标
const isIconfont = ref(false)

const currentIcon = computed({
    get() {
      return props.modelValue;
    },
    set(value) {
      // 检查是否为URL类型的图标
      const isUrlIcon = value && (value.startsWith('http://') || value.startsWith('https://') || value.startsWith('/'));
      
      if (isUrlIcon) {
        // 如果是URL类型，直接更新值
        isIconfont.value = false;
        emit('update:modelValue', value);
      } else if ((/^[^\d].*/.test(value) && value) || !value) {
        // html标签名不能以数字开头
        // 设置值时同时更新isIconfont标志
        if (value && (value.startsWith('icon-') || iconList.value.includes(value))) {
          isIconfont.value = true;
        }
        emit('update:modelValue', value);
      }
    }
  });

  // 只有当需要显示arco图标时才加载
  if (props.showTabs.includes('arco')) {
    for (let icon in arcoIcons) {
      arcodesignIcons.push(icon)
    }
    arcodesignIcons.pop()
  }

  const modules = import.meta.glob('../../assets/ma-icons/*.vue')
  for (const path in modules) {
    const name = path.match(/([A-Za-z0-9_-]+)/g)[2]
    mineadminIcons.push(`MaIcon${name}`)
  }

  const selectIcon = (icon, className) => {
    currentIcon.value = icon
    isIconfont.value = className === 'iconfont'
    console.log('选择图标:', icon, '类型:', className, '是否iconfont:', isIconfont.value);
    visible.value = false
  }

  const handlerChange = (value) => { 
    selectIcon(value, '')
  }

  const iconList = ref([]);

  // 检查是否为URL类型图标
const isUrlIcon = (iconName) => {
  if (!iconName) return false;
  return iconName.startsWith('http://') || iconName.startsWith('https://') || iconName.startsWith('/');
};

// 检查图标类型的函数
const checkIconType = (iconName) => {
  if (!iconName) return false;
  // 如果是URL类型，则不是iconfont图标
  if (isUrlIcon(iconName)) return false;
  // 如果以icon-开头，则认为是iconfont图标
  if (iconName.startsWith('icon-')) return true;
  // 如果在iconList中存在，则认为是iconfont图标
  if (iconList.value.includes(iconName)) return true;
  return false;
}

onMounted(() => {
  // 只有当需要显示iconfont图标时才加载
  if (props.showTabs.includes('iconfont')) {
    try {
      // 使用导入的CSS文件内容
      console.log('开始解析CSS文件内容');
      console.log('CSS内容长度:', iconfontCss.length);
      
      // 使用提取工具提取图标类名
      iconList.value = extractIconClassNames(iconfontCss);
      
      // 如果提取失败，使用正则表达式直接提取
      if (iconList.value.length === 0) {
        console.log('直接使用正则表达式提取');
        const regex = /\.([a-zA-Z0-9_-]+):before\s*{/g;
        const icons = [];
        let match;
        
        while ((match = regex.exec(iconfontCss)) !== null) {
          icons.push(match[1]);
        }
        
        iconList.value = [...new Set(icons)];
        console.log('直接提取到的图标列表:', iconList.value);
      }
      
      console.log('最终图标列表长度:', iconList.value.length);
      
      // 初始化时检查当前图标是否为iconfont图标
      if (props.modelValue) {
        // 如果是URL类型，则不是iconfont图标
        if (isUrlIcon(props.modelValue)) {
          isIconfont.value = false;
        } else {
          isIconfont.value = checkIconType(props.modelValue);
        }
        console.log('初始化图标类型检查:', props.modelValue, isIconfont.value);
      }
    } catch (error) {
      console.error('处理图标列表时出错:', error);
    }
  } else {
    // 即使不加载iconfont，也需要检查当前图标类型
    if (props.modelValue) {
      if (isUrlIcon(props.modelValue)) {
        isIconfont.value = false;
      } else if (props.modelValue.startsWith('icon-')) {
        isIconfont.value = true;
      }
      console.log('初始化图标类型检查(无iconfont加载):', props.modelValue, isIconfont.value);
    }
  }
});

</script>

<style scoped lang="less">
.icon-container {
  width: 50px; height: 32px;
  background-color: var(--color-fill-1);
  display: flex; justify-content: center;
  align-items: center;
}

.icon {
  width: 1em; fill: var(--color-text-1);
}

.tabs {

  ul {
    display: flex; flex-wrap: wrap; padding-left: 7px;
  }

  li {
    border: 2px solid var(--color-fill-4); margin-bottom: 10px;
    margin-right: 6px; padding: 5px; cursor: pointer;
  }
  
  li:hover, li.active {
    border: 2px solid rgb(var(--primary-6));
  }

  & li svg {
    width: 2.4em; height: 2.4em;
  }
}

.icon-preview-box {
  cursor: pointer;
  height: 36px;
  width: 36px;
  line-height: 36px;
  text-align: center;
  font-size: 24px;
  display: inline-block;
  border: 1px solid var(--color-border-2);
  margin-right: 8px;
  margin-bottom: 8px;
  &:hover {
    color: rgb(var(--primary-6));
    border-color: rgb(var(--primary-6));
  }
}

.icon-select-body {
  max-height: 600px;
  height: 600px;
  overflow-y: auto;
  ul {
    padding-left: 0;
    list-style: none;
    display: grid;
    gap: 5px;
    grid-template-columns: repeat(10, minmax(0, 1fr));
    li {
      list-style: none;
      text-align: center;
      width: 60px;
      height: 60px;
      line-height: 60px;
      border-radius: 2px;
      cursor: pointer;
      transition: all 0.3s;
      color: #666;
      &:hover {
        background-color: #e8f3ff;
        color: rgb(var(--primary-6));
      }
    }
  }
  
  /* 增加iconfont图标大小 */
  .iconfont {
    font-size: 32px !important;
  }
}

:deep(.arco-select-option-content) {
  width: 100%;
}
</style>