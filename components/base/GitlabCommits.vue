<template>
  <div class="gitlab-commits">
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" title="提交记录">
        <a-spin :loading="loading">
          <a-table 
            :columns="columns" 
            :data="commits" 
            :pagination="false" 
            :scroll="{ x: '100%', y: '600px' }" 
            :style="{ width: '100%' }"
          >
            <template #message="{ record }">
              <div class="commit-message">
                <div class="commit-title">{{ record.title }}</div>
                <div class="commit-description" v-if="record.description">{{ record.description }}</div>
              </div>
            </template>
            <template #author="{ record }">
              <div class="commit-author">
                <a-avatar :size="24" :src="record.author_avatar_url" />
                <span class="author-name">{{ record.author_name }}</span>
              </div>
            </template>
            <template #stats="{ record }">
              <div class="commit-stats">
                <span class="stats-added">+{{ record.stats.additions || 0 }}</span>
                <span class="stats-deleted">-{{ record.stats.deletions || 0 }}</span>
              </div>
            </template>

          </a-table>
        </a-spin>
      </a-tab-pane>
      <a-tab-pane key="2" title="贡献统计">
        <GitlabContributionStats :commits="commits" :loading="loading" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { IconLink } from "@arco-design/web-vue/es/icon";
import GitlabContributionStats from "./GitlabContributionStats.vue";
import { ********************** } from "~/api/gitlab/contributions";

const props = defineProps({
  projectUrl: {
    type: String,
    required: true
  },
  token: {
    type: String,
    required: true
  },
  limit: {
    type: Number,
    default: 10
  }
});

const loading = ref(true);
const commits = ref([]);

// 表格列定义
const columns = [
  {
    title: "提交信息",
    dataIndex: "message",
    slotName: "message",
    width: '40%',
  },
  {
    title: "作者",
    dataIndex: "author",
    slotName: "author",
    width: '15%',
  },
  {
    title: "提交时间",
    dataIndex: "committed_date",
    width: '15%',
  },
  {
    title: "代码变更",
    slotName: "stats",
    width: '30%',
  },

];

// 格式化提交信息，将第一行作为标题，其余作为描述
const formatCommitMessage = (message) => {
  const lines = message.split('\n').filter(line => line.trim());
  return {
    title: lines[0] || '',
    description: lines.slice(1).join('\n')
  };
};

// 格式化日期时间
const formatDateTime = (dateTimeStr) => {
  const date = new Date(dateTimeStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 从GitLab API获取提交记录
const fetchCommits = async () => {
  loading.value = true;
  try {
    // 从本地JSON文件获取GitLab贡献数据
    const response = await **********************();
    
    if (!response || !response.data || !response.data.success) {
      throw new Error(`获取GitLab贡献数据失败: ${response?.data?.message || '未知错误'}`);
    }
    
    const contributionsData = response.data.data;
    
    // 如果没有提交记录，则显示错误
    if (!contributionsData || !contributionsData.commits || contributionsData.commits.length === 0) {
      throw new Error('没有找到GitLab提交记录');
    }
    
    // 处理提交记录 - 使用所有提交记录，不限制数量
    const processedCommits = contributionsData.commits.map(commit => {
      // 提取提交信息的标题和描述
      const commitMessage = commit.message || '';
      const messageParts = commitMessage.split('\n\n');
      const title = messageParts[0] || '无提交信息';
      const description = messageParts.slice(1).join('\n\n');
      
      return {
        id: commit.id,
        title,
        description,
        author_name: commit.author_name || '未知作者',
        author_email: commit.author_email || '',
        author_avatar_url: commit.author_avatar_url || `https://www.gravatar.com/avatar/${(commit.author_email || '').trim().toLowerCase().split('').reduce((a,b)=>{a=((a<<5)-a)+b.charCodeAt(0);return a&a},0)}?s=80&d=identicon`,
        committed_date: formatDateTime(commit.committed_date),
        web_url: commit.web_url || `${props.projectUrl}/-/commit/${commit.id}`,
        stats: {
          additions: commit.stats?.additions || 0,
          deletions: commit.stats?.deletions || 0,
          total: commit.stats?.total || 0
        }
      };
    });
    
    // 按提交时间倒序排序
    commits.value = processedCommits.sort((a, b) => {
      const dateA = new Date(a.committed_date.replace(/-/g, '/'));
      const dateB = new Date(b.committed_date.replace(/-/g, '/'));
      return dateB - dateA; // 倒序排列
    });
    
    // 显示最后更新时间
    if (contributionsData.lastUpdated) {
      console.log(`GitLab贡献数据最后更新时间: ${new Date(contributionsData.lastUpdated).toLocaleString()}`);
    }
  } catch (error) {
    console.error('获取GitLab提交记录失败:', error);
    // 显示错误提示
    window.$message.error('获取GitLab提交记录失败，请检查网络或令牌配置');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchCommits();
});
</script>

<style scoped>
.gitlab-commits {
  margin-top: 16px;
  width: 100%;
}

.commit-message {
  display: flex;
  flex-direction: column;
}

.commit-title {
  font-weight: 500;
}

.commit-description {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
  white-space: pre-line;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.commit-author {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-size: 14px;
}

.commit-stats {
  display: flex;
  gap: 8px;
}

.stats-added {
  color: #00b42a;
  font-weight: 500;
}

.stats-deleted {
  color: #f53f3f;
  font-weight: 500;
}

:deep(.arco-table-container) {
  width: 100%;
}

:deep(.arco-table-tr) {
  width: 100%;
}

:deep(.arco-table) {
  width: 100%;
}
</style>
