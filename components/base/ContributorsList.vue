<template>
  <div class="contributors-list">
    <a-spin :loading="loading">
      <div class="contributor-list">
        <div v-for="(contributor, index) in contributors" :key="index" class="contributor-item">
          <div class="contributor-info">
            <a-avatar :size="32" :src="contributor.avatar_url" />
            <div class="contributor-details">
              <div class="contributor-name">{{ contributor.name }}</div>
              <div class="contributor-email">{{ contributor.email }}</div>
            </div>
          </div>
          <div class="contribution-data">
            <div class="contribution-commits">
              <span class="data-label">提交次数:</span>
              <span class="data-value">{{ contributor.commits }}</span>
            </div>
            <div class="contribution-percentage">
              <a-progress
                :percent="contributor.percentage"
                :stroke-color="getColorByIndex(index)"
                :show-text="true"
                size="small"
              />
            </div>
            <div class="contribution-code">
              <span class="stats-added">+{{ contributor.additions }}</span>
              <span class="stats-deleted">-{{ contributor.deletions }}</span>
            </div>
          </div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

const props = defineProps({
  contributors: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 根据索引获取不同的颜色
const getColorByIndex = (index) => {
  const colors = [
    '#165DFF', '#0FC6C2', '#722ED1', '#F7BA1E', 
    '#FF7D00', '#EB0AA4', '#7BC616', '#86909C'
  ];
  return colors[index % colors.length];
};
</script>

<style scoped>
.contributors-list {
  width: 100%;
}

.contributor-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

/* 修复 a-spin 宽度问题 */
:deep(.arco-spin) {
  display: block;
  width: 100%;
}

.contributor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f2f3f5;
}

.contributor-item:last-child {
  border-bottom: none;
}

.contributor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contributor-details {
  display: flex;
  flex-direction: column;
}

.contributor-name {
  font-weight: 500;
}

.contributor-email {
  font-size: 12px;
  color: #86909c;
}

.contribution-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.contribution-commits {
  display: flex;
  gap: 4px;
}

.data-label {
  font-size: 12px;
  color: #86909c;
}

.data-value {
  font-weight: 500;
}

.contribution-percentage {
  width: 120px;
}

.contribution-code {
  display: flex;
  gap: 8px;
}

.stats-added {
  color: #00b42a;
  font-weight: 500;
}

.stats-deleted {
  color: #f53f3f;
  font-weight: 500;
}
</style>