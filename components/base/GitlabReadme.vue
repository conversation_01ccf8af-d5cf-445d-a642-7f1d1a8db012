<template>
  <div class="gitlab-readme">
    <a-spin :loading="loading">
      <div v-if="error" class="error-message">
        <a-alert type="error" :content="error" />
      </div>
      <div v-else-if="readmeContent" class="markdown-content" v-html="renderedContent"></div>
      <div v-else class="empty-message">
        <a-empty description="暂无文档内容" />
      </div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { marked } from 'marked';
import DOMPurify from 'dompurify';

const props = defineProps({
  projectUrl: {
    type: String,
    required: true
  },
  branch: {
    type: String,
    default: 'master'
  },
  filePath: {
    type: String,
    default: 'README.md'
  },
  token: {
    type: String,
    required: true
  }
});

const loading = ref(true);
const readmeContent = ref('');
const error = ref('');

// 渲染Markdown内容为HTML
const renderedContent = computed(() => {
  if (!readmeContent.value) return '';
  
  try {
    // 使用marked将Markdown转换为HTML
    const html = marked(readmeContent.value);
    // 使用DOMPurify清理HTML，防止XSS攻击
    return DOMPurify.sanitize(html);
  } catch (e) {
    console.error('Markdown渲染错误:', e);
    return `<div class="error">Markdown渲染错误: ${e.message}</div>`;
  }
});

// 从GitLab API获取README内容
const fetchReadme = async () => {
  loading.value = true;
  error.value = '';
  
  try {
    // 使用固定的项目访问令牌
    const token = props.token;
    
    // 从URL中提取项目ID
    const urlParts = props.projectUrl.split('/');
    const projectId = urlParts.slice(-2).join('/');
    const encodedProjectId = encodeURIComponent(projectId);
    const encodedFilePath = encodeURIComponent(props.filePath);
    
    // 构建API URL
    const apiUrl = `https://ali.git.8080bl.com/api/v4/projects/${encodedProjectId}/repository/files/${encodedFilePath}/raw?ref=${props.branch}`;
    
    // 发送请求到GitLab API
    const response = await fetch(apiUrl, {
      headers: {
        'PRIVATE-TOKEN': token
      }
    });
    
    if (!response.ok) {
      throw new Error(`GitLab API请求失败: ${response.status} - ${response.statusText}`);
    }
    
    // 获取文件内容
    const content = await response.text();
    readmeContent.value = content;
    
  } catch (error) {
    console.error('获取README内容失败:', error);
    error.value = `获取文档内容失败: ${error.message || '未知错误'}`;
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchReadme();
});
</script>

<style scoped>
.gitlab-readme {
  width: 100%;
  padding: 16px;
}

.error-message {
  margin: 16px 0;
}

.empty-message {
  padding: 32px 0;
  text-align: center;
}

.markdown-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  max-width: 100%;
  overflow-x: auto;
}

.markdown-content :deep(h1) {
  font-size: 2em;
  margin-top: 0.67em;
  margin-bottom: 0.67em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-content :deep(h2) {
  font-size: 1.5em;
  margin-top: 0.83em;
  margin-bottom: 0.83em;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-content :deep(h3) {
  font-size: 1.17em;
  margin-top: 1em;
  margin-bottom: 1em;
}

.markdown-content :deep(h4) {
  font-size: 1em;
  margin-top: 1.33em;
  margin-bottom: 1.33em;
}

.markdown-content :deep(p) {
  margin-top: 1em;
  margin-bottom: 1em;
}

.markdown-content :deep(ul), .markdown-content :deep(ol) {
  padding-left: 2em;
  margin-top: 1em;
  margin-bottom: 1em;
}

.markdown-content :deep(li) {
  margin-bottom: 0.5em;
}

.markdown-content :deep(code) {
  font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace;
  background-color: rgba(27, 31, 35, 0.05);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 85%;
}

.markdown-content :deep(pre) {
  background-color: #f6f8fa;
  border-radius: 3px;
  padding: 16px;
  overflow: auto;
  margin-top: 1em;
  margin-bottom: 1em;
}

.markdown-content :deep(pre code) {
  background-color: transparent;
  padding: 0;
  font-size: 100%;
  white-space: pre;
}

.markdown-content :deep(blockquote) {
  margin-left: 0;
  padding-left: 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.markdown-content :deep(table) {
  border-collapse: collapse;
  width: 100%;
  margin-top: 1em;
  margin-bottom: 1em;
}

.markdown-content :deep(th), .markdown-content :deep(td) {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-content :deep(tr:nth-child(2n)) {
  background-color: #f6f8fa;
}

.markdown-content :deep(img) {
  max-width: 100%;
  box-sizing: content-box;
}

.markdown-content :deep(a) {
  color: #0366d6;
  text-decoration: none;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}
</style>
