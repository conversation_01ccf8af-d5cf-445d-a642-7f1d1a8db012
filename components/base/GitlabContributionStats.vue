<template>
  <div class="contribution-stats" style="width: 100%;">
    <a-row :gutter="16" class="mb-4" style="width: 100%; margin: 0;">
      <a-col :span="24" style="width: 100%;">
        <a-card class="stats-card" :style="{ width: '100%' }">
          <template #title>
            <div class="card-title">
              <IconTag />
              <span>每日提交数量趋势</span>
            </div>
          </template>
          <a-spin :loading="loading">
            <div ref="chartRef" class="commit-trend-chart" style="width: 100%; height: 400px; display: block;"></div>
          </a-spin>
        </a-card>
      </a-col>
    </a-row>
    
    <a-row :gutter="16">
      <a-col :span="12">
        <a-card class="stats-card">
          <template #title>
            <div class="card-title">
              <IconUserGroup />
              <span>贡献者统计</span>
            </div>
          </template>
          <a-spin :loading="loading">
            <div class="contributor-list">
              <div v-for="(contributor, index) in contributors" :key="index" class="contributor-item">
                <div class="contributor-info">
                  <a-avatar :size="32" :src="contributor.avatar_url" />
                  <div class="contributor-details">
                    <div class="contributor-name">{{ contributor.name }}</div>
                    <div class="contributor-email">{{ contributor.email }}</div>
                  </div>
                </div>
                <div class="contribution-data">
                  <div class="contribution-commits">
                    <span class="data-label">提交次数:</span>
                    <span class="data-value">{{ contributor.commits }}</span>
                  </div>
                  <div class="contribution-percentage">
                    <a-progress
                      :percent="contributor.percentage"
                      :stroke-color="getColorByIndex(index)"
                      :show-text="true"
                      size="small"
                    />
                  </div>
                  <div class="contribution-code">
                    <span class="stats-added">+{{ contributor.additions }}</span>
                    <span class="stats-deleted">-{{ contributor.deletions }}</span>
                  </div>
                </div>
              </div>
            </div>
          </a-spin>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card class="stats-card">
          <template #title>
            <div class="card-title">
              <IconCalendar />
              <span>提交频率分析</span>
            </div>
          </template>
          <a-spin :loading="loading">
            <div class="frequency-stats">
              <div class="stat-item">
                <div class="stat-title">平均每日提交次数</div>
                <div class="stat-value">{{ frequencyStats.averagePerDay.toFixed(2) }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-title">最活跃的日期</div>
                <div class="stat-value">{{ frequencyStats.mostActiveDate }}</div>
                <div class="stat-subvalue">{{ frequencyStats.mostActiveDateCount }} 次提交</div>
              </div>
              <div class="stat-item">
                <div class="stat-title">最活跃的星期</div>
                <div class="stat-value">{{ getWeekdayName(frequencyStats.mostActiveWeekday) }}</div>
                <div class="stat-subvalue">{{ frequencyStats.mostActiveWeekdayCount }} 次提交</div>
              </div>
              <div class="stat-item">
                <div class="stat-title">最活跃的时段</div>
                <div class="stat-value">{{ frequencyStats.mostActiveHour }}:00 - {{ frequencyStats.mostActiveHour + 1 }}:00</div>
                <div class="stat-subvalue">{{ frequencyStats.mostActiveHourCount }} 次提交</div>
              </div>
              <div class="stat-item">
                <div class="stat-title">最近一次提交</div>
                <div class="stat-value">{{ frequencyStats.lastCommitDate }}</div>
                <div class="stat-subvalue">{{ frequencyStats.daysSinceLastCommit }} 天前</div>
              </div>
            </div>
          </a-spin>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue';
import { 
  IconUserGroup, 
  IconCalendar,
  IconTag
} from '@arco-design/web-vue/es/icon';
import * as echarts from 'echarts';

const props = defineProps({
  commits: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
});

// 图表引用
const chartRef = ref(null);
let commitChart = null;

// 每个人每天的提交数据
const dailyCommitsByAuthor = ref({});

// 贡献者统计数据
const contributors = ref([]);

// 提交频率统计数据
const frequencyStats = ref({
  averagePerDay: 0,
  mostActiveDate: '',
  mostActiveDateCount: 0,
  mostActiveWeekday: 0,
  mostActiveWeekdayCount: 0,
  mostActiveHour: 0,
  mostActiveHourCount: 0,
  lastCommitDate: '',
  daysSinceLastCommit: 0
});

// 根据索引获取不同的颜色
const getColorByIndex = (index) => {
  const colors = ['#165DFF', '#0FC6C2', '#722ED1', '#F7BA1E', '#FC9153', '#3491FA', '#9FDB1D', '#F77234'];
  return colors[index % colors.length];
};

// 获取星期几的名称
const getWeekdayName = (weekday) => {
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  return weekdays[weekday];
};

// 计算贡献者统计
const calculateContributorStats = () => {
  if (!props.commits || props.commits.length === 0) return;

  // 按作者分组统计提交次数和代码变更
  const contributorMap = {};
  let totalCommits = props.commits.length;
  
  props.commits.forEach(commit => {
    const authorKey = `${commit.author_name}|${commit.author_email}`;
    
    if (!contributorMap[authorKey]) {
      contributorMap[authorKey] = {
        name: commit.author_name,
        email: commit.author_email,
        avatar_url: commit.author_avatar_url,
        commits: 0,
        additions: 0,
        deletions: 0
      };
    }
    
    contributorMap[authorKey].commits += 1;
    contributorMap[authorKey].additions += commit.stats.additions || 0;
    contributorMap[authorKey].deletions += commit.stats.deletions || 0;
  });
  
  // 转换为数组并计算百分比
  const contributorArray = Object.values(contributorMap);
  contributorArray.forEach(contributor => {
    contributor.percentage = Math.round((contributor.commits / totalCommits) * 100);
  });
  
  // 按提交次数排序
  contributors.value = contributorArray.sort((a, b) => b.commits - a.commits);
};

// 计算提交频率统计
const calculateFrequencyStats = () => {
  if (!props.commits || props.commits.length === 0) return;
  
  const dateMap = {};
  const weekdayMap = {};
  const hourMap = {};
  
  let firstCommitDate = new Date();
  let lastCommitDate = new Date(0);
  
  props.commits.forEach(commit => {
    // 解析提交日期时间
    const commitDate = new Date(commit.committed_date.replace(/-/g, '/'));
    
    // 更新最早和最晚提交日期
    if (commitDate < firstCommitDate) firstCommitDate = commitDate;
    if (commitDate > lastCommitDate) lastCommitDate = commitDate;
    
    // 按日期统计
    const dateKey = commitDate.toISOString().split('T')[0];
    dateMap[dateKey] = (dateMap[dateKey] || 0) + 1;
    
    // 按星期几统计
    const weekday = commitDate.getDay();
    weekdayMap[weekday] = (weekdayMap[weekday] || 0) + 1;
    
    // 按小时统计
    const hour = commitDate.getHours();
    hourMap[hour] = (hourMap[hour] || 0) + 1;
  });
  
  // 计算日期范围内的天数
  const daysDiff = Math.ceil((lastCommitDate - firstCommitDate) / (1000 * 60 * 60 * 24)) || 1;
  
  // 找出最活跃的日期、星期和小时
  let mostActiveDate = '';
  let mostActiveDateCount = 0;
  let mostActiveWeekday = 0;
  let mostActiveWeekdayCount = 0;
  let mostActiveHour = 0;
  let mostActiveHourCount = 0;
  
  Object.entries(dateMap).forEach(([date, count]) => {
    if (count > mostActiveDateCount) {
      mostActiveDate = date;
      mostActiveDateCount = count;
    }
  });
  
  Object.entries(weekdayMap).forEach(([weekday, count]) => {
    if (count > mostActiveWeekdayCount) {
      mostActiveWeekday = parseInt(weekday);
      mostActiveWeekdayCount = count;
    }
  });
  
  Object.entries(hourMap).forEach(([hour, count]) => {
    if (count > mostActiveHourCount) {
      mostActiveHour = parseInt(hour);
      mostActiveHourCount = count;
    }
  });
  
  // 计算自最后一次提交以来的天数
  const now = new Date();
  const daysSinceLastCommit = Math.ceil((now - lastCommitDate) / (1000 * 60 * 60 * 24));
  
  // 更新频率统计数据
  frequencyStats.value = {
    averagePerDay: props.commits.length / daysDiff,
    mostActiveDate: mostActiveDate.replace(/(\d{4})-(\d{2})-(\d{2})/, '$1年$2月$3日'),
    mostActiveDateCount,
    mostActiveWeekday,
    mostActiveWeekdayCount,
    mostActiveHour,
    mostActiveHourCount,
    lastCommitDate: lastCommitDate.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' }),
    daysSinceLastCommit
  };
};

// 初始化图表
const initChart = () => {
  if (chartRef.value) {
    try {
      console.log('初始化图表');
      // 强制设置容器大小
      const container = chartRef.value;
      container.style.width = '100%';
      container.style.height = '400px';
      container.style.display = 'block';
      
      // 强制重绘布局
      setTimeout(() => {
        // 确保容器大小正确
        console.log('容器尺寸检查:', container.offsetWidth, container.offsetHeight);
        
        // 使用容器的实际尺寸初始化图表
        commitChart = echarts.init(container);
        updateChart();
        
        // 响应式处理
        window.addEventListener('resize', () => {
          commitChart && commitChart.resize();
        });
      }, 0);
    } catch (error) {
      console.error('图表初始化错误:', error);
    }
  } else {
    console.warn('图表容器未找到');
  }
};

// 计算每个人每天的提交数据
const calculateDailyCommitsByAuthor = () => {
  if (!props.commits || props.commits.length === 0) {
    console.warn('没有提交数据');
    return;
  }
  
  console.log('处理提交数据，总数:', props.commits.length);
  
  const result = {};
  const dateSet = new Set();
  
  // 按作者和日期分组统计提交次数
  props.commits.forEach(commit => {
    try {
      const author = commit.author_name;
      // 检查日期格式
      let date;
      if (typeof commit.committed_date === 'string') {
        // 尝试提取日期部分
        if (commit.committed_date.includes(' ')) {
          date = commit.committed_date.split(' ')[0]; // 格式如 "2025-04-10 15:30:00"
        } else if (commit.committed_date.includes('T')) {
          date = commit.committed_date.split('T')[0]; // 格式如 "2025-04-10T15:30:00Z"
        } else {
          date = commit.committed_date; // 假设已经是日期格式
        }
      } else {
        console.warn('提交日期格式异常:', commit.committed_date);
        date = new Date().toISOString().split('T')[0]; // 使用当前日期作为后备
      }
      
      if (!result[author]) {
        result[author] = {};
      }
      
      if (!result[author][date]) {
        result[author][date] = 0;
      }
      
      result[author][date] += 1;
      dateSet.add(date);
    } catch (error) {
      console.error('处理提交数据错误:', error, commit);
    }
  });
  
  // 将日期排序
  const sortedDates = Array.from(dateSet).sort();
  console.log('日期范围:', sortedDates);
  
  // 完善数据，确保每个作者在每个日期都有数据
  Object.keys(result).forEach(author => {
    sortedDates.forEach(date => {
      if (!result[author][date]) {
        result[author][date] = 0;
      }
    });
  });
  
  dailyCommitsByAuthor.value = {
    authors: Object.keys(result),
    dates: sortedDates,
    data: result
  };
  
  console.log('处理后的数据:', dailyCommitsByAuthor.value);
};

// 更新图表数据
const updateChart = () => {
  if (!commitChart) {
    console.warn('图表实例不存在');
    return;
  }
  
  if (!dailyCommitsByAuthor.value.authors || dailyCommitsByAuthor.value.authors.length === 0) {
    console.warn('没有作者数据');
    
    // 显示一个空的图表，带有提示信息
    commitChart.setOption({
      title: {
        text: '暂无提交数据',
        left: 'center',
        top: 'center',
        textStyle: {
          color: '#999',
          fontSize: 16
        }
      }
    });
    return;
  }
  
  try {
    console.log('更新图表数据');
    const { authors, dates, data } = dailyCommitsByAuthor.value;
    
    // 如果没有日期数据，添加一个默认日期
    const chartDates = dates.length > 0 ? dates : [new Date().toISOString().split('T')[0]];
    
    const series = authors.map((author, index) => {
      const authorData = chartDates.map(date => (data[author] && data[author][date]) || 0);
      
      return {
        name: author,
        type: 'line',
        data: authorData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 2
        },
        itemStyle: {
          color: getColorByIndex(index)
        }
      };
    });
    
    const option = {
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          let result = params[0].axisValue + '<br/>';
          params.forEach(param => {
            const color = param.color;
            const marker = `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${color};"></span>`;
            result += marker + param.seriesName + ': ' + param.value + ' 次提交<br/>';
          });
          return result;
        }
      },
      legend: {
        data: authors,
        type: 'scroll',
        orient: 'horizontal',
        top: 0
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '50px',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartDates,
        axisLabel: {
          rotate: 45,
          formatter: function(value) {
            if (value && value.length >= 5) {
              return value.substring(5); // 只显示月-日
            }
            return value;
          }
        }
      },
      yAxis: {
        type: 'value',
        minInterval: 1, // 确保只显示整数
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series: series
    };
    
    console.log('设置图表选项');
    commitChart.setOption(option);
  } catch (error) {
    console.error('更新图表错误:', error);
  }
};

// 监听提交数据变化
const updateStats = () => {
  calculateContributorStats();
  calculateFrequencyStats();
  calculateDailyCommitsByAuthor();
  
  // 如果图表已初始化，更新图表数据
  if (commitChart) {
    updateChart();
  }
};

// 当提交数据变化时更新统计
watch(() => props.commits, updateStats, { immediate: true, deep: true });

// 使用ResizeObserver监听容器大小变化
const setupResizeObserver = () => {
  if (!chartRef.value) return;
  
  const resizeObserver = new ResizeObserver(() => {
    if (commitChart) {
      console.log('容器大小变化，重新调整图表');
      commitChart.resize();
    }
  });
  
  resizeObserver.observe(chartRef.value);
  
  // 返回清理函数
  return () => {
    resizeObserver.disconnect();
  };
};

// 延迟初始化图表的函数
const delayedInitChart = () => {
  // 首先检查DOM元素是否存在
  if (!chartRef.value) {
    console.warn('图表容器不存在，等待重试');
    setTimeout(delayedInitChart, 500);
    return;
  }
  
  // 强制设置容器尺寸
  const container = chartRef.value;
  container.style.width = '100%';
  container.style.height = '400px';
  container.style.display = 'block';
  
  // 等待一帧让浏览器更新DOM
  requestAnimationFrame(() => {
    console.log('容器尺寸检查:', container.offsetWidth, container.offsetHeight);
    
    // 如果容器宽度仍然为0，尝试强制设置一个最小宽度
    if (container.offsetWidth === 0) {
      console.log('强制设置容器宽度');
      container.style.width = '800px';
      container.style.minWidth = '800px';
    }
    
    // 检查容器大小
    if (container.offsetWidth > 0 && container.offsetHeight > 0) {
      console.log('容器大小正常，初始化图表', container.offsetWidth, container.offsetHeight);
      initChart();
      setupResizeObserver();
    } else {
      console.log('容器大小异常，等待重试', container.offsetWidth, container.offsetHeight);
      // 如果容器还没有大小，等待并重试
      setTimeout(delayedInitChart, 500);
    }
  });
};

onMounted(() => {
  console.log('组件挂载');
  updateStats();
  
  // 确保DOM已渲染
  nextTick(() => {
    console.log('DOM已更新，准备初始化图表');
    // 使用递归函数确保容器有大小
    delayedInitChart();
  });
});

// 组件卸载时清理图表实例
onBeforeUnmount(() => {
  if (commitChart) {
    console.log('清理图表实例');
    commitChart.dispose();
    commitChart = null;
  }
});
</script>

<style scoped>
.contribution-stats {
  margin-top: 16px;
  width: 100%;
  display: block;
}

.commit-trend-chart {
  min-height: 400px;
  width: 100% !important;
  /* 确保容器有内容时也有高度 */
  background-color: #f7f8fa;
  display: block !important;
  box-sizing: border-box;
}

.stats-card {
  height: 100%;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.contributor-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.contributor-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f2f3f5;
}

.contributor-item:last-child {
  border-bottom: none;
}

.contributor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contributor-details {
  display: flex;
  flex-direction: column;
}

.contributor-name {
  font-weight: 500;
}

.contributor-email {
  font-size: 12px;
  color: #86909c;
}

.contribution-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.contribution-commits {
  display: flex;
  gap: 4px;
}

.data-label {
  font-size: 12px;
  color: #86909c;
}

.data-value {
  font-weight: 500;
}

.contribution-percentage {
  width: 120px;
}

.contribution-code {
  display: flex;
  gap: 8px;
}

.stats-added {
  color: #00b42a;
  font-weight: 500;
}

.stats-deleted {
  color: #f53f3f;
  font-weight: 500;
}

.frequency-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  padding: 12px;
  background-color: #f7f8fa;
  border-radius: 4px;
}

.stat-title {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
}

.stat-subvalue {
  font-size: 12px;
  color: #4e5969;
  margin-top: 2px;
}
</style>
