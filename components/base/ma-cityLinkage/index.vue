<!--
 - <PERSON><PERSON>dmin is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <a-cascader
    v-if="props.type === 'cascader'"
    v-model="val"
    :field-names="
      props.mode == 'name'
        ? { value: 'name', label: 'name' }
        : { value: 'code', label: 'name' }
    "
    :options="jsonData"
    allow-search
    check-strictly
    expand-trigger="hover"
    path-mode
    placeholder="请选择省市区"
  />
  <a-space v-else>
    <a-select
      v-model="selectData.province"
      :field-names="
        props.mode == 'name'
          ? { value: 'name', label: 'name' }
          : { value: 'code', label: 'name' }
      "
      :options="province"
      :style="{ width: '220px' }"
      allow-clear
      allow-search
      placeholder="请选择省/直辖市/自治区"
      @change="provinceChange"
      @clear="
        () => {
          selectData.city = [];
          selectData.area = [];
          selectData.province = [];
          selectData.city = [];
          selectData.area = [];
          province.value = [];
        }
      "
    />
    <a-select
      v-model="selectData.city"
      :field-names="
        props.mode == 'name'
          ? { value: 'name', label: 'name' }
          : { value: 'code', label: 'name' }
      "
      :options="city"
      :style="{ width: '220px' }"
      allow-clear
      allow-search
      placeholder="请选择地级市/市辖区"
      @change="cityChange"
      @clear="
        () => {
          selectData.city = [];
          selectData.area = [];
          selectData.city = [];
          selectData.area = [];
        }
      "
    />
    <a-select
      v-model="selectData.area"
      :field-names="
        props.mode == 'name'
          ? { value: 'name', label: 'name' }
          : { value: 'code', label: 'name' }
      "
      :options="area"
      :style="{ width: '220px' }"
      allow-clear
      allow-search
      placeholder="请选择区县"
      @clear="
        () => {
          selectData.area = [];
          selectData.area = [];
        }
      "
    />
  </a-space>
</template>

<script setup>
import { ref, watch, onMounted } from "vue";
import { isObject } from "lodash";
import commonApi from '@/api/common';
import mallAddressApi from '@/api/mall/address';

const val = ref();
const selectData = ref({ province: [], city: [], area: [] });
const province = ref([]);
const city = ref([]);
const area = ref([]);
const jsonData = ref([]); // 用于存储API返回的数据

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: [Number, String, Object],
  type: { type: String, default: "select" },
  mode: { type: String, default: "name" },
});

// 获取区域数据
const getRegionData = async (forceRefresh = false) => {
  try {
    // 使用通用API的带缓存增强版本获取地区数据
    const res = await commonApi.getTreeRegionWithCache({ forceRefresh });
    
    if (!res || res.code !== 200) {
      console.error('获取区域数据失败：', res);
      return;
    }
    
    processRegionData(res);
  } catch (error) {
    console.error('获取区域数据出错：', error);
  }
};

// 处理区域数据
const processRegionData = (res) => {
  if (res && res.code === 200 && res.data) {
    // 将API返回的数据转换为组件需要的格式
    jsonData.value = res.data.map(province => {
      // 处理省级数据
      const provinceItem = {
        code: province.code.toString(),
        name: province.name
      };
      
      // 如果有子级数据，才添加children属性
      if (province.children && province.children.length > 0) {
        provinceItem.children = province.children.map(city => {
          // 处理市级数据
          const cityItem = {
            code: city.code.toString(),
            name: city.name
          };
          
          // 如果有子级数据，才添加children属性
          if (city.children && city.children.length > 0) {
            cityItem.children = city.children.map(area => {
              // 处理区县级数据
              return {
                code: area.code.toString(),
                name: area.name
                // 区县级不需要children属性
              };
            });
          }
          
          return cityItem;
        });
      }
      
      return provinceItem;
    });
    
    // 如果是select模式，需要初始化省份数据
    if (props.type === "select") {
      province.value = jsonData.value.map((item) => {
        return { code: item.code, name: item.name };
      });
    }
    
    console.log('区域数据加载成功，共加载省份数量：', jsonData.value.length);
    
    // 数据加载完成后，初始化已选择的值
    setSelectData();
  } else {
    console.error('获取区域数据失败：', res);
  }
};

const provinceChange = (val, clear = true) => {
  if (clear) {
    selectData.value.city = [];
    selectData.value.area = [];
    area.value = [];
    city.value = [];
  }
  jsonData.value.forEach((item) => {
    if (props.mode == "name" && val == item.name) {
      city.value = item.children;
    }
    if (props.mode == "code" && val == item.code) {
      city.value = item.children;
    }
  });
};

const cityChange = (val, clear = true) => {
  if (clear) {
    selectData.value.area = [];
    area.value = [];
  }
  city.value.forEach((item) => {
    if (props.mode == "name" && val == item.name) {
      area.value = item.children;
    }
    if (props.mode == "code" && val == item.code) {
      area.value = item.children;
    }
  });
};

const setSelectData = () => {
  if (props.type === "select") {
    if (val.value && isObject(val.value)) {
      selectData.value.province = val.value.province ? val.value.province : "";
      selectData.value.city = val.value.city ? val.value.city : "";
      selectData.value.area = val.value.area ? val.value.area : "";
      selectData.value.province && provinceChange(selectData.value.province, false);
      selectData.value.city &&
        selectData.value.province &&
        cityChange(selectData.value.city, false);
    }
  }
};

val.value = props.modelValue;

watch(
  () => props.modelValue,
  (vl) => {
    val.value = vl;
    setSelectData();
  },
  { deep: true }
);

watch(
  () => val.value,
  (vl) => emit("update:modelValue", vl)
);

watch(
  () => selectData.value,
  (vl) => emit("update:modelValue", vl),
  { deep: true }
);

// 组件挂载时获取区域数据
onMounted(() => {
  getRegionData();
});
</script>
