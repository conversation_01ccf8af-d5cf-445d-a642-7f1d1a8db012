<template>
  <div class="commit-trend-chart-container">
    <a-spin :loading="loading">
      <div ref="chartRef" class="commit-trend-chart"></div>
    </a-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick, defineProps } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  commits: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const chartRef = ref(null);
let commitChart = null;

// 根据索引获取不同的颜色
const getColorByIndex = (index) => {
  const colors = [
    '#165DFF', '#0FC6C2', '#722ED1', '#F7BA1E', 
    '#FF7D00', '#EB0AA4', '#7BC616', '#86909C'
  ];
  return colors[index % colors.length];
};

// 计算每个人每天的提交数据
const calculateDailyCommitsByAuthor = () => {
  if (!props.commits || props.commits.length === 0) return { dates: [], series: [] };
  
  // 按作者和日期分组
  const authorCommits = {};
  const dateSet = new Set();
  
  props.commits.forEach(commit => {
    // 解析提交日期
    const commitDate = new Date(commit.committed_date.replace(/-/g, '/'));
    // 提取日期部分作为键
    const dateKey = commitDate.toISOString().split('T')[0];
    dateSet.add(dateKey);
    
    // 按作者分组
    const author = commit.author_name;
    if (!authorCommits[author]) {
      authorCommits[author] = {};
    }
    
    // 统计每个作者每天的提交次数
    authorCommits[author][dateKey] = (authorCommits[author][dateKey] || 0) + 1;
  });
  
  // 将日期排序
  const dates = Array.from(dateSet).sort();
  
  // 构建每个作者的系列数据
  const series = Object.entries(authorCommits).map(([author, dateCounts], index) => {
    const data = dates.map(date => dateCounts[date] || 0);
    
    return {
      name: author,
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: data,
      itemStyle: {
        color: getColorByIndex(index)
      }
    };
  });
  
  return { dates, series };
};

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  // 创建图表实例
  commitChart = echarts.init(chartRef.value);
  
  // 设置基础配置
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: []
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: []
    },
    yAxis: {
      type: 'value'
    },
    series: []
  };
  
  // 应用配置
  commitChart.setOption(option);
  
  // 更新图表数据
  updateChart();
};

// 更新图表数据
const updateChart = () => {
  if (!commitChart) return;
  
  // 计算每个人每天的提交数据
  const { dates, series } = calculateDailyCommitsByAuthor();
  
  // 更新图表配置
  commitChart.setOption({
    xAxis: {
      data: dates
    },
    legend: {
      data: series.map(s => s.name)
    },
    series: series
  });
};

// 使用ResizeObserver监听容器大小变化
const setupResizeObserver = () => {
  if (!chartRef.value) return;
  
  const resizeObserver = new ResizeObserver(() => {
    if (commitChart) {
      commitChart.resize();
    }
  });
  
  resizeObserver.observe(chartRef.value);
  
  // 返回清理函数
  return () => {
    resizeObserver.disconnect();
  };
};

// 延迟初始化图表的函数
const delayedInitChart = () => {
  // 首先检查DOM元素是否存在
  if (!chartRef.value) {
    setTimeout(delayedInitChart, 500);
    return;
  }
  
  // 强制设置容器尺寸
  const container = chartRef.value;
  container.style.width = '100%';
  container.style.height = '400px';
  container.style.display = 'block';
  
  // 等待一帧让浏览器更新DOM
  requestAnimationFrame(() => {
    // 如果容器宽度仍然为0，尝试强制设置一个最小宽度
    if (container.offsetWidth === 0) {
      container.style.width = '800px';
      container.style.minWidth = '800px';
    }
    
    // 检查容器大小
    if (container.offsetWidth > 0 && container.offsetHeight > 0) {
      initChart();
      setupResizeObserver();
    } else {
      // 如果容器还没有大小，等待并重试
      setTimeout(delayedInitChart, 500);
    }
  });
};

// 监听提交数据变化，重新更新图表
watch(() => props.commits, () => {
  if (commitChart) {
    updateChart();
  }
}, { deep: true });

onMounted(() => {
  // 确保DOM已渲染
  nextTick(() => {
    // 使用递归函数确保容器有大小
    delayedInitChart();
  });
});

// 组件卸载时清理图表实例
onBeforeUnmount(() => {
  if (commitChart) {
    commitChart.dispose();
    commitChart = null;
  }
});
</script>

<style scoped>
.commit-trend-chart-container {
  width: 100%;
}

.commit-trend-chart {
  min-height: 400px;
  width: 100% !important;
  background-color: #f7f8fa;
  display: block !important;
  box-sizing: border-box;
}

/* 修复 a-spin 宽度问题 */
:deep(.arco-spin) {
  display: block;
  width: 100%;
}
</style>