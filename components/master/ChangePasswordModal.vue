<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => $emit('update:visible', val)"
    title="修改密码"
    @cancel="handleCancel"
    @before-ok="handleOk"
  >
    <a-form :model="formData" ref="formRef" :rules="rules">
      <a-form-item field="oldPassword" label="原密码" validate-trigger="blur">
        <a-input-password
          v-model="formData.oldPassword"
          placeholder="请输入原密码"
          allow-clear
        />
      </a-form-item>
      <a-form-item field="newPassword" label="新密码" validate-trigger="blur">
        <a-input-password
          v-model="formData.newPassword"
          placeholder="请输入新密码"
          allow-clear
        />
      </a-form-item>
      <a-form-item field="confirmPassword" label="确认密码" validate-trigger="blur">
        <a-input-password
          v-model="formData.confirmPassword"
          placeholder="请再次输入新密码"
          allow-clear
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Message } from '@arco-design/web-vue';
import providerApi from '@/api/master/provider';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  providerId: {
    type: [String, Number],
    required: true
  }
});

const emit = defineEmits(['update:visible', 'success']);

const formRef = ref(null);
const formData = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

const rules = {
  oldPassword: [
    { required: true, message: '请输入原密码' },
    { minLength: 8, message: '密码长度不能小于8位' },
    { maxLength: 20, message: '密码长度不能超过20位' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码' },
    { minLength: 8, message: '密码长度不能小于8位' },
    { maxLength: 20, message: '密码长度不能超过20位' },
    { 
      validator: (value, callback) => {
        // 检查密码是否包含至少两种字符类型（字母、数字、符号）
        let hasLetter = /[a-zA-Z]/.test(value);
        let hasNumber = /[0-9]/.test(value);
        let hasSymbol = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(value);
        
        let typeCount = 0;
        if (hasLetter) typeCount++;
        if (hasNumber) typeCount++;
        if (hasSymbol) typeCount++;
        
        if (typeCount < 2) {
          callback('密码需由字母、数字和半角符号两种及以上组合');
          return;
        }
        callback();
      }
    }
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码' },
    {
      validator: (value, callback) => {
        if (value !== formData.newPassword) {
          callback('两次输入的密码不一致');
        }
        callback();
      }
    }
  ]
};

const handleCancel = () => {
  emit('update:visible', false);
  formRef.value?.resetFields();
};

const handleOk = async (done) => {
  try {
    await formRef.value.validate();
    // 调用后端API修改密码
    await providerApi.user.changePassword({
      providerId: props.providerId,
      oldPassword: formData.oldPassword,
      newPassword: formData.newPassword
    });
    Message.success('密码修改成功');
    emit('success');
    handleCancel();
    done();
  } catch (error) {
    done(false);
    console.error('修改密码失败:', error);
    if (error.response && error.response.data && error.response.data.message) {
      Message.error(error.response.data.message);
    } else if (error.message) {
      Message.error(error.message);
    } else {
      Message.error('修改密码失败，请重试');
    }
  }
};
</script>

<style scoped>
.arco-form-item {
  margin-bottom: 24px;
}
</style>
