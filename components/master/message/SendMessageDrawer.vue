<template>
  <a-drawer
    :visible="visible"
    title="发送消息"
    width="800px"
    @ok="handleClose"
    @cancel="handleClose"
    unmount-on-close
  >
    <template #footer>
      <a-button @click="handleClose">取消</a-button>
      <a-button type="primary" @click="handleSend" :loading="loading">发送</a-button>
    </template>
    
    <a-form :model="form" layout="vertical">
      <a-form-item label="用户ID" required>
        <a-input-group compact>
          <a-input
            v-model="form.userId"
            placeholder="请输入用户ID"
            style="width: calc(100% - 80px)"
            @blur="validateUser"
            @input="handleUserIdInput"
            @pressEnter="validateUser"
          />
          <a-button
            type="primary"
            :loading="userValidateLoading"
            @click="validateUser"
            style="width: 80px"
          >
            验证
          </a-button>
        </a-input-group>
        <!-- 用户信息显示 -->
        <div v-if="selectedUser" class="user-preview mt-2 p-3 bg-gray-50 rounded">
          <div class="flex items-center space-x-3">
            <a-avatar :size="40" :src="selectedUser.avatar">
              {{ selectedUser.nickname?.[0] || selectedUser.username?.[0] || 'U' }}
            </a-avatar>
            <div>
              <div class="font-medium">{{ selectedUser.nickname || selectedUser.username }}</div>
              <div class="text-sm text-gray-500">ID: {{ selectedUser.id }}</div>
              <div class="text-sm text-gray-500" v-if="selectedUser.phone">手机: {{ selectedUser.phone }}</div>
            </div>
          </div>
        </div>
        <!-- 用户不存在提示 -->
        <div v-if="userNotFound" class="mt-2 text-red-500 text-sm">
          <i class="i-carbon-warning"></i> 用户不存在，请检查用户ID是否正确
        </div>
      </a-form-item>

      <a-form-item label="消息类型" required>
        <a-select v-model="form.messageType" placeholder="选择消息类型">
          <a-option value="system">系统消息</a-option>
          <a-option value="order">订单消息</a-option>
        </a-select>
      </a-form-item>

      <a-form-item label="优先级" required>
        <a-select v-model="form.priority" placeholder="选择优先级">
          <a-option :value="1">普通</a-option>
          <a-option :value="2">重要</a-option>
          <a-option :value="3">紧急</a-option>
        </a-select>
      </a-form-item>

      <a-form-item label="消息标题" required>
        <a-input v-model="form.title" placeholder="请输入消息标题" />
      </a-form-item>

      <a-form-item label="消息内容">
        <a-textarea
          v-model="form.content"
          placeholder="请输入消息内容"
          :rows="4"
        />
      </a-form-item>

      <a-form-item label="操作链接">
        <a-input v-model="form.actionUrl" placeholder="可选，用户点击后跳转的链接" />
      </a-form-item>

      <a-form-item label="操作文本">
        <a-input v-model="form.actionText" placeholder="可选，操作按钮的文本" />
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import messageManagementApi from '~/api/master/messageManagement'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)
const userValidateLoading = ref(false)
const selectedUser = ref(null)
const userNotFound = ref(false)
let validateTimer = null

// 表单数据
const form = reactive({
  userId: '',
  messageType: 'system',
  priority: 1,
  title: '',
  content: '',
  actionUrl: '',
  actionText: ''
})

// 监听visible变化，重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 处理用户ID输入
const handleUserIdInput = (value) => {
  console.log('用户ID输入:', value, 'form.userId:', form.userId)
  selectedUser.value = null
  userNotFound.value = false

  if (validateTimer) {
    clearTimeout(validateTimer)
  }
}

// 验证用户（防抖处理）
const validateUser = async (event) => {
  if (validateTimer) {
    clearTimeout(validateTimer)
  }
  
  validateTimer = setTimeout(async () => {
    await doValidateUser(event)
  }, 300)
}

// 实际执行用户验证
const doValidateUser = async (event) => {
  const userId = event?.target?.value?.trim() || form.userId?.trim()
  
  selectedUser.value = null
  userNotFound.value = false

  if (!userId) {
    return
  }

  try {
    userValidateLoading.value = true
    const response = await messageManagementApi.getUserById(userId)

    if (response.code === 200 && response.data) {
      selectedUser.value = response.data
      userNotFound.value = false
      Message.success('用户验证成功')
    } else {
      selectedUser.value = null
      userNotFound.value = true
      Message.warning('用户不存在，请检查用户ID是否正确')
    }
  } catch (error) {
    console.error('验证用户失败:', error)
    selectedUser.value = null
    userNotFound.value = true
    Message.error('验证用户失败，请稍后再试')
  } finally {
    userValidateLoading.value = false
  }
}

// 发送消息
const handleSend = async () => {
  try {
    console.log('发送消息表单数据:', form)
    console.log('用户验证状态:', selectedUser.value)

    if (!form.userId || !form.title) {
      console.log('必填字段检查失败:', { userId: form.userId, title: form.title })
      Message.error('请填写必填字段')
      return
    }

    if (!selectedUser.value) {
      console.log('用户验证失败')
      Message.error('请先验证用户ID是否正确')
      return
    }

    loading.value = true
    const response = await messageManagementApi.sendMessageToUser(form)

    if (response.code === 200) {
      Message.success('消息发送成功')
      emit('success')
      handleClose()
    } else {
      Message.error(response.message || '发送消息失败')
    }
  } catch (error) {
    console.error('发送消息失败:', error)
    Message.error('发送消息失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    userId: '',
    messageType: 'system',
    priority: 1,
    title: '',
    content: '',
    actionUrl: '',
    actionText: ''
  })
  selectedUser.value = null
  userNotFound.value = false
}
</script>

<style scoped>
.user-preview {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.user-preview:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}
</style>
