<template>
  <a-drawer
    :visible="visible"
    title="批量发送消息"
    width="800px"
    @ok="handleClose"
    @cancel="handleClose"
    unmount-on-close
  >
    <template #footer>
      <a-button @click="handleClose">取消</a-button>
      <a-button type="primary" @click="handleSend" :loading="loading">发送</a-button>
    </template>
    
    <a-form :model="form" layout="vertical">
<!--      <a-divider orientation="left">消息内容</a-divider>-->
      
      <a-form-item label="消息类型" required>
        <a-select v-model="form.messageType" placeholder="选择消息类型">
          <a-option value="system">系统消息</a-option>
          <a-option value="order">订单消息</a-option>
        </a-select>
      </a-form-item>

      <a-form-item label="优先级" required>
        <a-select v-model="form.priority" placeholder="选择优先级">
          <a-option :value="1">普通</a-option>
          <a-option :value="2">重要</a-option>
          <a-option :value="3">紧急</a-option>
        </a-select>
      </a-form-item>

      <a-form-item label="消息标题" required>
        <a-input v-model="form.title" placeholder="请输入消息标题" />
      </a-form-item>

      <a-form-item label="消息内容">
        <a-textarea
          v-model="form.content"
          placeholder="请输入消息内容"
          :rows="4"
        />
      </a-form-item>

      <a-form-item label="操作链接">
        <a-input v-model="form.actionUrl" placeholder="可选，用户点击后跳转的链接" />
      </a-form-item>

      <a-form-item label="操作文本">
        <a-input v-model="form.actionText" placeholder="可选，操作按钮的文本" />
      </a-form-item>
      
      <a-divider orientation="left">用户筛选条件</a-divider>
      
<!--      <a-form-item label="用户等级">-->
<!--        <a-select v-model="form.userConditions.userLevel" placeholder="选择用户等级" allow-clear>-->
<!--          <a-option :value="1">普通用户</a-option>-->
<!--          <a-option :value="2">VIP用户</a-option>-->
<!--          <a-option :value="3">超级VIP</a-option>-->
<!--        </a-select>-->
<!--      </a-form-item>-->

      <a-form-item label="注册时间范围">
        <a-range-picker
          v-model="form.registrationDateRange"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="最后登录时间范围">
        <a-range-picker
          v-model="form.lastLoginDateRange"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="用户状态">
        <a-select v-model="form.userConditions.status" placeholder="选择用户状态" allow-clear>
          <a-option :value="1">正常</a-option>
          <a-option :value="0">禁用</a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import messageManagementApi from '~/api/master/messageManagement'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'success'])

// 响应式数据
const loading = ref(false)

// 表单数据
const form = reactive({
  messageType: 'system',
  priority: 1,
  title: '',
  content: '',
  actionUrl: '',
  actionText: '',
  userConditions: {
    userLevel: undefined,
    status: undefined
  },
  registrationDateRange: [],
  lastLoginDateRange: []
})

// 监听visible变化，重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 批量发送消息
const handleSend = async () => {
  try {
    if (!form.title) {
      Message.error('请填写消息标题')
      return
    }

    loading.value = true

    // 处理日期范围
    const userConditions = { ...form.userConditions }

    if (form.registrationDateRange && form.registrationDateRange.length === 2) {
      // 处理日期格式，支持多种日期对象类型
      const startDate = form.registrationDateRange[0]
      const endDate = form.registrationDateRange[1]

      userConditions.registrationDateStart = startDate instanceof Date
        ? startDate.toISOString().split('T')[0]
        : (startDate.format ? startDate.format('YYYY-MM-DD') : startDate)

      userConditions.registrationDateEnd = endDate instanceof Date
        ? endDate.toISOString().split('T')[0]
        : (endDate.format ? endDate.format('YYYY-MM-DD') : endDate)
    }

    if (form.lastLoginDateRange && form.lastLoginDateRange.length === 2) {
      // 处理日期格式，支持多种日期对象类型
      const startDate = form.lastLoginDateRange[0]
      const endDate = form.lastLoginDateRange[1]

      userConditions.lastLoginStart = startDate instanceof Date
        ? startDate.toISOString().split('T')[0]
        : (startDate.format ? startDate.format('YYYY-MM-DD') : startDate)

      userConditions.lastLoginEnd = endDate instanceof Date
        ? endDate.toISOString().split('T')[0]
        : (endDate.format ? endDate.format('YYYY-MM-DD') : endDate)
    }

    const messageData = {
      title: form.title,
      content: form.content,
      messageType: form.messageType,
      priority: form.priority,
      actionUrl: form.actionUrl,
      actionText: form.actionText
    }

    const response = await messageManagementApi.sendBulkMessage(messageData, userConditions)

    if (response.code === 200) {
      Message.success(response.data.message || '批量发送成功')
      emit('success')
      handleClose()
    } else {
      Message.error(response.message || '批量发送失败')
    }
  } catch (error) {
    console.error('批量发送失败:', error)
    Message.error('批量发送失败，请稍后再试')
  } finally {
    loading.value = false
  }
}

// 关闭抽屉
const handleClose = () => {
  emit('update:visible', false)
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    messageType: 'system',
    priority: 1,
    title: '',
    content: '',
    actionUrl: '',
    actionText: '',
    userConditions: {
      userLevel: undefined,
      status: undefined
    },
    registrationDateRange: [],
    lastLoginDateRange: []
  })
}
</script>

<style scoped>
/* 组件样式 */
</style>
