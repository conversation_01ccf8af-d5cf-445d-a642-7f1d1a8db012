<!--
 - 客户工作台 - 销售数据分析组件
 - 
 - <AUTHOR>
-->
<template>
  <a-card class="h-full">
    <div class="mb-4">
      <h2 class="text-lg font-bold">销售数据分析</h2>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <!-- 热销类目排行 -->
      <a-card class="border border-gray-200 shadow-sm">
        <template #title>
          <h3 class="text-base font-medium">热销类目排行</h3>
        </template>
        <div ref="categoryChartRef" style="width: 100%; height: 300px;"></div>
      </a-card>
      
      <!-- 热销品牌排行 -->
      <a-card class="border border-gray-200 shadow-sm">
        <template #title>
          <h3 class="text-base font-medium">热销品牌排行</h3>
        </template>
        <div ref="brandChartRef" style="width: 100%; height: 300px;"></div>
      </a-card>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 销售额环比分析 -->
      <a-card class="border border-gray-200 shadow-sm">
        <template #title>
          <h3 class="text-base font-medium">销售额环比分析</h3>
        </template>
        <div ref="growthChartRef" style="width: 100%; height: 300px;"></div>
      </a-card>
      
      <!-- 销售渠道分析 -->
      <a-card class="border border-gray-200 shadow-sm">
        <template #title>
          <h3 class="text-base font-medium">销售渠道分析</h3>
        </template>
        <div ref="channelChartRef" style="width: 100%; height: 300px;"></div>
      </a-card>
    </div>
  </a-card>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart, PieChart, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  BarChart,
  PieChart,
  LineChart,
  CanvasRenderer
]);

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

// 图表DOM引用
const categoryChartRef = ref(null);
const brandChartRef = ref(null);
const growthChartRef = ref(null);
const channelChartRef = ref(null);

// 图表实例
let categoryChart = null;
let brandChart = null;
let growthChart = null;
let channelChart = null;

// 格式化金额，超过万元时显示为"x.xx万"
const formatAmountWithUnit = (amount) => {
  if (amount >= 10000000) {
    return (amount / 10000000).toFixed(2) + '千万';
  } else if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + '万';
  } else {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
};

// 窗口大小变化时重新调整图表大小
const resizeHandler = () => {
  categoryChart?.resize();
  brandChart?.resize();
  growthChart?.resize();
  channelChart?.resize();
};

// 初始化热销类目排行图表
const initCategoryChart = () => {
  if (!categoryChartRef.value) return;
  
  categoryChart = echarts.init(categoryChartRef.value);
  categoryChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params) => {
        const data = props.data.hotCategories[params[0].dataIndex];
        return `${data.name}<br/>销售额: ¥${formatAmountWithUnit(data.amount)}<br/>销售量: ${data.count}件<br/>占比: ${data.ratio}%`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => formatAmountWithUnit(value)
      }
    },
    yAxis: {
      type: 'category',
      data: props.data.hotCategories.map(item => item.name),
      axisLabel: { interval: 0 }
    },
    series: [{
      name: '销售额',
      type: 'bar',
      data: props.data.hotCategories.map(item => ({
        value: item.amount,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      })),
      label: {
        show: true,
        position: 'right',
        formatter: (params) => formatAmountWithUnit(params.value)
      }
    }]
  });
};

// 初始化热销品牌排行图表
const initBrandChart = () => {
  if (!brandChartRef.value) return;
  
  brandChart = echarts.init(brandChartRef.value);
  brandChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'shadow' },
      formatter: (params) => {
        const data = props.data.hotBrands[params[0].dataIndex];
        return `${data.name}<br/>销售额: ¥${formatAmountWithUnit(data.amount)}<br/>销售量: ${data.count}件<br/>占比: ${data.ratio}%`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => formatAmountWithUnit(value)
      }
    },
    yAxis: {
      type: 'category',
      data: props.data.hotBrands.map(item => item.name),
      axisLabel: { interval: 0 }
    },
    series: [{
      name: '销售额',
      type: 'bar',
      data: props.data.hotBrands.map(item => ({
        value: item.amount,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#ff9a9e' },
            { offset: 1, color: '#fad0c4' }
          ])
        }
      })),
      label: {
        show: true,
        position: 'right',
        formatter: (params) => formatAmountWithUnit(params.value)
      }
    }]
  });
};

// 初始化销售额环比分析图表
const initGrowthChart = () => {
  if (!growthChartRef.value) return;
  
  growthChart = echarts.init(growthChartRef.value);
  growthChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: { color: '#999' }
      },
      formatter: (params) => {
        const data = props.data.salesGrowth[params[0].dataIndex];
        return `${data.month}<br/>本期: ¥${formatAmountWithUnit(data.current)}<br/>上期: ¥${formatAmountWithUnit(data.previous)}<br/>环比增长: ${data.growth}%`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    legend: {
      data: ['本期', '上期', '增长率']
    },
    xAxis: {
      type: 'category',
      data: props.data.salesGrowth.map(item => item.month),
      axisPointer: { type: 'shadow' }
    },
    yAxis: [
      {
        type: 'value',
        name: '销售额',
        min: 0,
        axisLabel: {
          formatter: (value) => formatAmountWithUnit(value)
        }
      },
      {
        type: 'value',
        name: '增长率',
        min: 0,
        max: 20,
        interval: 5,
        axisLabel: {
          formatter: '{value}%'
        }
      }
    ],
    series: [
      {
        name: '本期',
        type: 'bar',
        barWidth: '20%',
        data: props.data.salesGrowth.map(item => item.current),
        itemStyle: { color: '#5470c6' }
      },
      {
        name: '上期',
        type: 'bar',
        barWidth: '20%',
        data: props.data.salesGrowth.map(item => item.previous),
        itemStyle: { color: '#91cc75' }
      },
      {
        name: '增长率',
        type: 'line',
        yAxisIndex: 1,
        data: props.data.salesGrowth.map(item => item.growth),
        itemStyle: { color: '#ee6666' },
        label: {
          show: true,
          position: 'top',
          formatter: '{c}%'
        }
      }
    ]
  });
};

// 初始化销售渠道分析图表
const initChannelChart = () => {
  if (!channelChartRef.value) return;
  
  channelChart = echarts.init(channelChartRef.value);
  channelChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}元 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      data: props.data.salesChannels.map(item => item.name)
    },
    series: [{
      name: '销售渠道',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: { show: false },
      data: props.data.salesChannels.map(item => ({
        value: item.amount,
        name: item.name
      }))
    }]
  });
};

// 初始化所有图表
const initAllCharts = () => {
  // 销毁旧的图表实例
  categoryChart?.dispose();
  brandChart?.dispose();
  growthChart?.dispose();
  channelChart?.dispose();
  
  // 初始化新的图表实例
  initCategoryChart();
  initBrandChart();
  initGrowthChart();
  initChannelChart();
};

// 创建MutationObserver
let observer = null;

// 组件挂载时初始化图表
onMounted(() => {
  // 延迟初始化，确保DOM已经渲染
  setTimeout(() => {
    try {
      initAllCharts();
      window.addEventListener('resize', resizeHandler);
      
      // 使用更精确的选择器定位容器
      const container = categoryChartRef.value?.parentElement;
      if (container) {
        // 仅监听属性变化，不监听子元素
        observer = new MutationObserver(() => {
          // 防止频繁触发，使用节流
          if (categoryChart && brandChart && growthChart && channelChart) {
            requestAnimationFrame(resizeHandler);
          }
        });
        observer.observe(container, { attributes: true });
      }
    } catch (error) {
      console.error('初始化图表错误:', error);
    }
  }, 300);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', resizeHandler);
  if (observer) {
    observer.disconnect();
    observer = null;
  }
  categoryChart?.dispose();
  brandChart?.dispose();
  growthChart?.dispose();
  channelChart?.dispose();
  
  categoryChart = null;
  brandChart = null;
  growthChart = null;
  channelChart = null;
});
</script>

<style scoped>
.arco-card {
  height: 100%;
}
</style>
