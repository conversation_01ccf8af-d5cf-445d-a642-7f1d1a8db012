<!--
 - 客户工作台 - 客户统计组件
 - 
 - <AUTHOR>
-->
<template>
  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <a-card class="bg-blue-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-blue-500 text-white mr-4">
          <icon-user class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">客户总数</div>
          <div class="text-2xl font-bold">{{ statistics.totalCustomers }}</div>
        </div>
      </div>
    </a-card>
    <a-card class="bg-green-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-green-500 text-white mr-4">
          <icon-user-add class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">本月新增</div>
          <div class="text-2xl font-bold">{{ statistics.newCustomersThisMonth }}</div>
        </div>
      </div>
    </a-card>
    <a-card class="bg-purple-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-purple-500 text-white mr-4">
          <icon-fire class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">活跃客户</div>
          <div class="text-2xl font-bold">{{ statistics.activeCustomers }}</div>
        </div>
      </div>
    </a-card>
    <a-card class="bg-orange-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-orange-500 text-white mr-4">
          <icon-star class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">我的客户</div>
          <div class="text-2xl font-bold">{{ statistics.myCustomers }}</div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
defineProps({
  statistics: {
    type: Object,
    required: true,
    default: () => ({
      totalCustomers: 0,
      newCustomersThisMonth: 0,
      activeCustomers: 0,
      myCustomers: 0
    })
  }
});
</script>

<style scoped>
.a-card {
  height: 100%;
}
</style>
