<!--
 - 客户工作台 - 团队概况组件
 - 
 - <AUTHOR>
-->
<template>
  <a-card class="h-full">
    <div class="mb-4">
      <h2 class="text-lg font-bold">团队概况</h2>
    </div>
    
    <!-- 数据概览 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-blue-50 rounded-lg p-4">
        <div class="text-gray-500 mb-1">目标销售金额</div>
        <div class="text-xl font-bold">¥{{ formatAmountWithUnit(data.targetSalesAmount) }}</div>
      </div>
      
      <div class="bg-green-50 rounded-lg p-4">
        <div class="text-gray-500 mb-1">目标完成率</div>
        <div class="text-xl font-bold">{{ data.targetCompletionRate }}%</div>
        <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div class="bg-green-500 h-2 rounded-full" :style="`width: ${data.targetCompletionRate}%`"></div>
        </div>
      </div>
      
      <div class="bg-orange-50 rounded-lg p-4">
        <div class="text-gray-500 mb-1">当日订单数</div>
        <div class="text-xl font-bold">{{ data.dailyOrderCount }}</div>
      </div>
      
      <div class="bg-purple-50 rounded-lg p-4">
        <div class="text-gray-500 mb-1">当日下单金额</div>
        <div class="text-xl font-bold">¥{{ formatAmountWithUnit(data.dailyOrderAmount) }}</div>
      </div>
    </div>
    
    <!-- 应收情况 -->
    <div class="mb-6">
      <h3 class="text-base font-medium mb-2">应收情况</h3>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">三个月内</div>
            <div class="text-lg font-bold">¥{{ formatAmountWithUnit(data.receivables.within3Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.receivables.within3Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超三个月</div>
            <div class="text-lg font-bold text-yellow-500">¥{{ formatAmountWithUnit(data.receivables.over3Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.receivables.over3Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超六个月</div>
            <div class="text-lg font-bold text-orange-500">¥{{ formatAmountWithUnit(data.receivables.over6Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.receivables.over6Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超一年</div>
            <div class="text-lg font-bold text-red-500">¥{{ formatAmountWithUnit(data.receivables.overOneYear.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.receivables.overOneYear.count }} 笔</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 回款情况 -->
    <div class="mb-6">
      <h3 class="text-base font-medium mb-2">回款情况</h3>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">三个月内</div>
            <div class="text-lg font-bold">¥{{ formatAmountWithUnit(data.payments.within3Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.payments.within3Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超三个月</div>
            <div class="text-lg font-bold text-yellow-500">¥{{ formatAmountWithUnit(data.payments.over3Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.payments.over3Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超六个月</div>
            <div class="text-lg font-bold text-orange-500">¥{{ formatAmountWithUnit(data.payments.over6Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.payments.over6Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超一年</div>
            <div class="text-lg font-bold text-red-500">¥{{ formatAmountWithUnit(data.payments.overOneYear.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.payments.overOneYear.count }} 笔</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      <!-- 团队成员销售占比饼图 -->
      <div>
        <h3 class="text-base font-medium mb-2">团队成员销售占比</h3>
        <div class="flex items-center justify-between mb-2">
          <a-radio-group v-model="salesChartType" type="button" size="small">
            <a-radio value="pie">饼图</a-radio>
            <a-radio value="bar">柱状图</a-radio>
          </a-radio-group>
          <a-select v-model="salesDisplayCount" size="small" style="width: 120px;" v-if="data.teamMembers.length > 10">
            <a-option value="all">显示全部</a-option>
            <a-option value="top5">前5名</a-option>
            <a-option value="top10">前10名</a-option>
          </a-select>
        </div>
        <div ref="teamSalesChartRef" style="width: 100%; height: 300px;"></div>
      </div>
      
      <!-- 团队成员目标完成率对比条形图 -->
      <div>
        <h3 class="text-base font-medium mb-2">团队成员目标完成率</h3>
        <div class="flex items-center justify-between mb-2">
          <a-select v-model="targetDisplayCount" size="small" style="width: 120px;" v-if="data.teamMembers.length > 10">
            <a-option value="all">显示全部</a-option>
            <a-option value="top5">前5名</a-option>
            <a-option value="top10">前10名</a-option>
            <a-option value="bottom5">后5名</a-option>
          </a-select>
          <a-switch v-model="showTargetTable" size="small">
            <template #checked>表格</template>
            <template #unchecked>图表</template>
          </a-switch>
        </div>
        <div v-show="!showTargetTable" ref="teamTargetChartRef" style="width: 100%; height: 300px;"></div>
        <div v-show="showTargetTable" class="overflow-auto" style="max-height: 300px;">
          <a-table :columns="teamMembersColumns" :data="getFilteredTeamMembers()" :pagination="false" size="small">
            <template #completionRate="{ record }">
              <div class="flex items-center">
                <div class="w-full bg-gray-200 rounded-full h-2 mr-2" style="width: 100px;">
                  <div class="h-2 rounded-full" :style="{
                    width: `${record.completionRate}%`,
                    backgroundColor: getCompletionRateColor(record.completionRate)
                  }"></div>
                </div>
                <span>{{ record.completionRate }}%</span>
              </div>
            </template>
          </a-table>
        </div>
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 本月订单数曲线图 -->
      <div>
        <h3 class="text-base font-medium mb-2">本月订单数</h3>
        <div ref="monthlyOrderCountChartRef" style="width: 100%; height: 250px;"></div>
      </div>
      
      <!-- 本月下单金额曲线图 -->
      <div>
        <h3 class="text-base font-medium mb-2">本月下单金额</h3>
        <div ref="monthlyOrderAmountChartRef" style="width: 100%; height: 250px;"></div>
      </div>
      
      <!-- 新增客户数曲线图 -->
      <div>
        <h3 class="text-base font-medium mb-2">新增客户数</h3>
        <div ref="newCustomersChartRef" style="width: 100%; height: 250px;"></div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts/core';
import { PieChart, BarChart, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkLineComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkLineComponent,
  PieChart,
  BarChart,
  LineChart,
  CanvasRenderer
]);

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({
      targetSalesAmount: 1000000,
      targetCompletionRate: 75,
      dailyOrderCount: 12,
      dailyOrderAmount: 56000,
      teamMembers: [
        { name: '张三', salesAmount: 280000, targetAmount: 400000, completionRate: 70 },
        { name: '李四', salesAmount: 350000, targetAmount: 400000, completionRate: 87.5 },
        { name: '王五', salesAmount: 120000, targetAmount: 300000, completionRate: 40 },
        { name: '赵六', salesAmount: 180000, targetAmount: 300000, completionRate: 60 }
      ],
      monthlyOrderCount: [
        { date: '05-01', count: 8 },
        { date: '05-02', count: 10 },
        { date: '05-03', count: 6 },
        { date: '05-04', count: 12 },
        { date: '05-05', count: 15 },
        { date: '05-06', count: 8 },
        { date: '05-07', count: 9 },
        { date: '05-08', count: 11 },
        { date: '05-09', count: 12 }
      ],
      monthlyOrderAmount: [
        { date: '05-01', amount: 42000 },
        { date: '05-02', amount: 56000 },
        { date: '05-03', amount: 36000 },
        { date: '05-04', amount: 68000 },
        { date: '05-05', amount: 78000 },
        { date: '05-06', amount: 45000 },
        { date: '05-07', amount: 52000 },
        { date: '05-08', amount: 63000 },
        { date: '05-09', amount: 56000 }
      ],
      newCustomers: [
        { date: '05-01', count: 3 },
        { date: '05-02', count: 5 },
        { date: '05-03', count: 2 },
        { date: '05-04', count: 4 },
        { date: '05-05', count: 6 },
        { date: '05-06', count: 3 },
        { date: '05-07', count: 4 },
        { date: '05-08', count: 5 },
        { date: '05-09', count: 3 }
      ]
    })
  }
});

// 视图控制变量
const salesChartType = ref('pie'); // 销售占比图表类型：饼图或柱状图
const salesDisplayCount = ref('all'); // 销售占比显示数量
const targetDisplayCount = ref('all'); // 目标完成率显示数量
const showTargetTable = ref(false); // 是否以表格形式显示目标完成率

// 团队成员表格列定义
const teamMembersColumns = [
  { title: '成员名称', dataIndex: 'name' },
  { title: '目标金额', dataIndex: 'targetAmount', render: ({ record }) => `¥${formatAmountWithUnit(record.targetAmount)}` },
  { title: '实际销售', dataIndex: 'salesAmount', render: ({ record }) => `¥${formatAmountWithUnit(record.salesAmount)}` },
  { title: '完成率', slotName: 'completionRate' }
];

// 图表DOM引用
const teamSalesChartRef = ref(null);
const teamTargetChartRef = ref(null);
const monthlyOrderCountChartRef = ref(null);
const monthlyOrderAmountChartRef = ref(null);
const newCustomersChartRef = ref(null);

// 图表实例
let teamSalesChart = null;
let teamTargetChart = null;
let monthlyOrderCountChart = null;
let monthlyOrderAmountChart = null;
let newCustomersChart = null;

// 获取筛选后的团队成员数据
const getFilteredTeamMembers = () => {
  let members = [...props.data.teamMembers];
  
  // 按完成率排序
  members.sort((a, b) => b.completionRate - a.completionRate);
  
  // 根据选择进行筛选
  if (targetDisplayCount.value === 'top5') {
    return members.slice(0, 5);
  } else if (targetDisplayCount.value === 'top10') {
    return members.slice(0, 10);
  } else if (targetDisplayCount.value === 'bottom5') {
    return members.slice(-5);
  }
  
  return members;
};

// 获取销售图表数据
const getSalesChartData = () => {
  let members = [...props.data.teamMembers];
  
  // 按销售金额排序
  members.sort((a, b) => b.salesAmount - a.salesAmount);
  
  // 根据选择进行筛选
  if (salesDisplayCount.value === 'top5') {
    members = members.slice(0, 5);
  } else if (salesDisplayCount.value === 'top10') {
    members = members.slice(0, 10);
  }
  
  // 如果成员超过20人且显示全部，则将其他成员合并为"其他"
  if (salesDisplayCount.value === 'all' && props.data.teamMembers.length > 20) {
    const topMembers = members.slice(0, 19);
    const otherMembers = members.slice(19);
    
    if (otherMembers.length > 0) {
      const otherSalesAmount = otherMembers.reduce((sum, member) => sum + member.salesAmount, 0);
      topMembers.push({
        name: '其他',
        salesAmount: otherSalesAmount,
        targetAmount: 0,
        completionRate: 0
      });
    }
    
    return topMembers;
  }
  
  return members;
};

// 格式化金额，超过万元时显示为"x.xx万"
const formatAmountWithUnit = (amount) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + '万';
  } else {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
};

// 初始化图表
const initCharts = () => {
  // 初始化团队成员销售占比图
  updateSalesChart();
  
  // 初始化团队成员目标完成率图
  updateTargetChart();
  
  // 初始化本月订单数曲线图
  monthlyOrderCountChart = echarts.init(monthlyOrderCountChartRef.value);
  updateMonthlyOrderCountChart();
  
  // 初始化本月下单金额曲线图
  monthlyOrderAmountChart = echarts.init(monthlyOrderAmountChartRef.value);
  updateMonthlyOrderAmountChart();
  
  // 初始化新增客户数曲线图
  newCustomersChart = echarts.init(newCustomersChartRef.value);
  updateNewCustomersChart();
};

  // 更新销售占比图
const updateSalesChart = () => {
  if (!teamSalesChartRef.value) return;
  
  if (!teamSalesChart) {
    teamSalesChart = echarts.init(teamSalesChartRef.value);
  } else {
    // 清除之前的图表配置
    teamSalesChart.clear();
  }
  
  const chartData = getSalesChartData();
  
  // 饼图配置
  if (salesChartType.value === 'pie') {
    teamSalesChart.setOption({
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}元 ({d}%)'
      },
      legend: {
        type: chartData.length > 15 ? 'scroll' : 'plain',
        orient: 'vertical',
        // 确保图例在图表区域内
        right: '5%',
        top: 'middle',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          fontSize: 12,
          overflow: 'truncate',
          width: 60
        },
        formatter: function(name) {
          // 如果名称过长，截取前4个字符并添加省略号
          if (name.length > 4) {
            return name.substring(0, 4) + '...';
          }
          return name;
        },
        data: chartData.map(item => item.name)
      },
      series: [
        {
          name: '销售金额',
          type: 'pie',
          radius: ['35%', '65%'],  // 缩小半径，留出更多空间给图例
          center: ['40%', '50%'],  // 将饼图向左移动，给右侧图例留出空间
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 1
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '14',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: chartData.map(item => ({
            value: item.salesAmount,
            name: item.name
          }))
        }
      ]
    }, true);
  } 
  // 柱状图配置
  else if (salesChartType.value === 'bar') {
    // 按销售金额降序排序
    chartData.sort((a, b) => b.salesAmount - a.salesAmount);
    
    teamSalesChart.setOption({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          return `${params[0].name}<br/>销售金额: ¥${formatAmountWithUnit(params[0].value)}`;
        }
      },
      grid: {
        left: '15%',
        right: '5%',
        bottom: '5%',
        top: '5%',
        containLabel: true
      },
      xAxis: {
        type: 'value',
        axisLabel: {
          formatter: function(value) {
            return formatAmountWithUnit(value);
          }
        }
      },
      yAxis: {
        type: 'category',
        data: chartData.map(item => item.name),
        axisLabel: {
          interval: 0,
          margin: 8,
          rotate: chartData.length > 10 ? 30 : 0,
          formatter: function(value) {
            // 如果名称过长，截取前6个字符并添加省略号
            if (value.length > 6) {
              return value.substring(0, 6) + '...';
            }
            return value;
          }
        }
      },
      series: [
        {
          name: '销售金额',
          type: 'bar',
          barWidth: chartData.length > 15 ? '60%' : '70%',
          data: chartData.map(item => ({
            value: item.salesAmount,
            itemStyle: {
              color: '#1890ff'
            }
          }))
        }
      ]
    }, true);
  }
};

// 更新目标完成率图
const updateTargetChart = () => {
  // 如果当前是表格模式或者DOM引用不存在，则直接返回
  if (!teamTargetChartRef.value || showTargetTable.value) return;
  
  // 检查图表实例是否存在，如果不存在则初始化
  if (!teamTargetChart) {
    try {
      teamTargetChart = echarts.init(teamTargetChartRef.value);
    } catch (error) {
      console.error('初始化目标完成率图表失败:', error);
      return;
    }
  }
  
  const chartData = getFilteredTeamMembers();
  
  // 清除之前的图表配置
  teamTargetChart.clear();
  
  // 重新设置图表配置
  teamTargetChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const member = chartData.find(m => m.name === params[0].name);
        return `${params[0].name}<br/>目标金额: ¥${formatAmountWithUnit(member.targetAmount)}<br/>已完成: ¥${formatAmountWithUnit(member.salesAmount)}<br/>完成率: ${member.completionRate}%`;
      }
    },
    grid: {
      left: '15%',  // 增加左侧空间，防止标签被裁剪
      right: '5%',
      bottom: '5%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      max: 100,
      axisLabel: {
        formatter: '{value}%'
      }
    },
    yAxis: {
      type: 'category',
      data: chartData.map(item => item.name),
      axisLabel: {
        interval: 0,
        margin: 8,
        rotate: chartData.length > 10 ? 30 : 0,
        formatter: function(value) {
          // 如果名称过长，截取前6个字符并添加省略号
          if (value.length > 6) {
            return value.substring(0, 6) + '...';
          }
          return value;
        }
      }
    },
    series: [
      {
        name: '完成率',
        type: 'bar',
        barWidth: chartData.length > 15 ? '60%' : '70%',  // 根据数据量调整柱子宽度
        data: chartData.map(item => ({
          value: item.completionRate,
          itemStyle: {
            color: getCompletionRateColor(item.completionRate)
          }
        }))
      }
    ]
  }, true);
};

// 更新本月订单数曲线图
const updateMonthlyOrderCountChart = () => {
  if (!monthlyOrderCountChartRef.value || !monthlyOrderCountChart) return;
  
  monthlyOrderCountChart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: props.data.monthlyOrderCount.map(item => item.date),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: props.data.monthlyOrderCount.map(item => item.count),
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0.1)'
              }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 8
      }
    ]
  }, true);
};

// 更新本月下单金额曲线图
const updateMonthlyOrderAmountChart = () => {
  if (!monthlyOrderAmountChartRef.value || !monthlyOrderAmountChart) return;
  
  monthlyOrderAmountChart.setOption({
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return `${params[0].name}<br/>${params[0].seriesName}: ¥${formatAmountWithUnit(params[0].value)}`;
      }
    },
    xAxis: {
      type: 'category',
      data: props.data.monthlyOrderAmount.map(item => item.date),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return formatAmountWithUnit(value);
        }
      }
    },
    series: [
      {
        name: '下单金额',
        data: props.data.monthlyOrderAmount.map(item => item.amount),
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#52c41a'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(82, 196, 26, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(82, 196, 26, 0.1)'
              }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 8
      }
    ]
  }, true);
};

// 更新新增客户数曲线图
const updateNewCustomersChart = () => {
  if (!newCustomersChartRef.value || !newCustomersChart) return;
  
  newCustomersChart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: props.data.newCustomers.map(item => item.date),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: props.data.newCustomers.map(item => item.count),
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#722ed1'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(114, 46, 209, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(114, 46, 209, 0.1)'
              }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 8
      }
    ]
  }, true);
};

// 监听视图控制变量的变化
watch(salesChartType, () => {
  updateSalesChart();
});

watch(salesDisplayCount, () => {
  updateSalesChart();
});

watch(targetDisplayCount, () => {
  if (!showTargetTable.value) {
    updateTargetChart();
  }
});

// 当切换图表/表格视图时的处理
watch(showTargetTable, (newVal, oldVal) => {
  // 等待DOM更新
  nextTick(() => {
    if (!newVal) { // 切换到图表视图
      // 如果图表实例不存在但DOM引用存在，初始化图表
      if (!teamTargetChart && teamTargetChartRef.value) {
        teamTargetChart = echarts.init(teamTargetChartRef.value);
        updateTargetChart();
      } else if (teamTargetChart) {
        // 如果图表实例存在，先调用resize再更新
        teamTargetChart.resize();
        updateTargetChart();
      }
    }
  });
});

// 初始化图表
onMounted(() => {
  initCharts();
  
  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    teamSalesChart && teamSalesChart.resize();
    teamTargetChart && teamTargetChart.resize();
    monthlyOrderCountChart && monthlyOrderCountChart.resize();
    monthlyOrderAmountChart && monthlyOrderAmountChart.resize();
    newCustomersChart && newCustomersChart.resize();
  });
});

// 根据完成率获取颜色
const getCompletionRateColor = (rate) => {
  if (rate >= 90) return '#52c41a'; // 绿色
  if (rate >= 70) return '#1890ff'; // 蓝色
  if (rate >= 50) return '#faad14'; // 黄色
  return '#f5222d'; // 红色
};
</script>

<style scoped>
.arco-card {
  height: 100%;
}
</style>
