<!--
 - 客户工作台 - 商机跟进组件
 - 
 - <AUTHOR>
-->
<template>
  <a-card title="商机跟进" class="h-full">
    <div class="mb-4">
      <div class="text-gray-500 mb-2">商机总金额</div>
      <div class="text-xl font-bold">¥{{ formatNumber(data.totalAmount) }}</div>
    </div>

    <a-table :columns="columns" :data="data.items" :pagination="false">
      <template #stage="{ record }">
        <a-tag :color="getStageColor(record.stage)">{{ record.stage }}</a-tag>
      </template>
      <template #probability="{ record }">
        <a-progress
          :percent="record.probability"
          :stroke-width="8"
          :show-text="true"
          size="small"
        />
      </template>
      <template #amount="{ record }">
        ¥{{ formatNumber(record.amount) }}
      </template>
      <template #operation>
        <div>
          <a-button type="text" size="small" class="mr-2">查看详情</a-button>
          <a-button type="primary" size="small">跟进</a-button>
        </div>
      </template>
    </a-table>
  </a-card>
</template>

<script setup>
defineProps({
  data: {
    type: Object,
    required: true
  }
});

// 格式化数字为千分位
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 根据商机阶段获取标签颜色
const getStageColor = (stage) => {
  const stageColors = {
    '初步接触': 'blue',
    '需求确认': 'cyan',
    '方案提交': 'green',
    '商务谈判': 'orange',
    '合同签订': 'red'
  };
  return stageColors[stage] || 'default';
};

// 表格列定义
const columns = [
  { title: '商机编号', dataIndex: 'id' },
  { title: '客户名称', dataIndex: 'customer' },
  { title: '联系人', dataIndex: 'contact' },
  { title: '产品/服务', dataIndex: 'products' },
  { title: '金额', dataIndex: 'amount', slotName: 'amount' },
  { title: '阶段', dataIndex: 'stage', slotName: 'stage' },
  { title: '成功概率', dataIndex: 'probability', slotName: 'probability' },
  { title: '下次跟进时间', dataIndex: 'nextFollowTime' },
  { title: '操作', slotName: 'operation' }
];
</script>

<style scoped>
.a-card {
  height: 100%;
}
</style>
