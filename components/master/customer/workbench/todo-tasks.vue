<!--
 - 客户工作台 - 待办事项组件
 - 
 - <AUTHOR>
-->
<template>
  <a-card class="h-full">
    <div class="mb-4">
      <h2 class="text-lg font-bold">待办事项</h2>
    </div>
    
    <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
      <!-- 物流异常 -->
      <div 
        class="bg-red-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
        @click="showLogisticsExceptionDetail"
      >
        <div class="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-2">
          <icon-exclamation-circle class="text-red-500 text-xl" />
        </div>
        <div class="text-gray-500 text-sm">物流异常</div>
        <div class="text-xl font-bold">{{ data.logisticsException }}</div>
      </div>
      
      <!-- 待发货 -->
      <div 
        class="bg-orange-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
        @click="showPendingShipmentDetail"
      >
        <div class="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center mb-2">
          <icon-send class="text-orange-500 text-xl" />
        </div>
        <div class="text-gray-500 text-sm">待发货</div>
        <div class="text-xl font-bold">{{ data.pendingShipment }}</div>
      </div>
      
      <!-- 待收货 -->
      <div 
        class="bg-yellow-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
        @click="showPendingReceiptDetail"
      >
        <div class="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center mb-2">
          <icon-send class="text-yellow-500 text-xl" />
        </div>
        <div class="text-gray-500 text-sm">待收货</div>
        <div class="text-xl font-bold">{{ data.pendingReceipt }}</div>
      </div>
      
      <!-- 待验收 -->
      <div 
        class="bg-green-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
        @click="showPendingAcceptanceDetail"
      >
        <div class="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
          <icon-check-circle class="text-green-500 text-xl" />
        </div>
        <div class="text-gray-500 text-sm">待验收</div>
        <div class="text-xl font-bold">{{ data.pendingAcceptance }}</div>
      </div>
      
      <!-- 待开票 -->
      <div 
        class="bg-blue-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
        @click="showPendingInvoiceDetail"
      >
        <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-2">
          <icon-file class="text-blue-500 text-xl" />
        </div>
        <div class="text-gray-500 text-sm">待开票</div>
        <div class="text-xl font-bold">{{ data.pendingInvoice }}</div>
      </div>
      
      <!-- 待付款 -->
      <div 
        class="bg-purple-50 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:shadow-md transition-shadow"
        @click="showPendingPaymentDetail"
      >
        <div class="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-2">
          <icon-send class="text-purple-500 text-xl" />
        </div>
        <div class="text-gray-500 text-sm">待付款</div>
        <div class="text-xl font-bold">{{ data.pendingPayment }}</div>
      </div>
    </div>
    
    <!-- 物流异常详情弹窗 -->
    <a-modal v-model:visible="logisticsExceptionVisible" title="物流异常详情" :width="1200">
      <a-table :columns="logisticsExceptionColumns" :data="data.logisticsExceptionItems" :pagination="false">
        <template #status="{ record }">
          <a-tag color="red">{{ record.status }}</a-tag>
        </template>
        <template #operation="{ record }">
          <div>
            <a-button type="text" size="small" class="mr-2">查看详情</a-button>
            <a-button type="primary" size="small">处理异常</a-button>
          </div>
        </template>
      </a-table>
    </a-modal>
    
    <!-- 待发货详情弹窗 -->
    <a-modal v-model:visible="pendingShipmentVisible" title="待发货详情" :width="1200">
      <a-table :columns="pendingShipmentColumns" :data="data.pendingShipmentItems" :pagination="false">
        <template #operation="{ record }">
          <div>
            <a-button type="text" size="small" class="mr-2">查看详情</a-button>
            <a-button type="primary" size="small">发货</a-button>
          </div>
        </template>
      </a-table>
    </a-modal>
    
    <!-- 待收货详情弹窗 -->
    <a-modal v-model:visible="pendingReceiptVisible" title="待收货详情" :width="1200">
      <a-table :columns="pendingReceiptColumns" :data="data.pendingReceiptItems" :pagination="false">
        <template #operation="{ record }">
          <div>
            <a-button type="text" size="small" class="mr-2">查看详情</a-button>
            <a-button type="primary" size="small">提醒收货</a-button>
          </div>
        </template>
      </a-table>
    </a-modal>
    
    <!-- 待验收详情弹窗 -->
    <a-modal v-model:visible="pendingAcceptanceVisible" title="待验收详情" :width="1200">
      <a-table :columns="pendingAcceptanceColumns" :data="data.pendingAcceptanceItems" :pagination="false">
        <template #operation="{ record }">
          <div>
            <a-button type="text" size="small" class="mr-2">查看详情</a-button>
            <a-button type="primary" size="small">确认验收</a-button>
          </div>
        </template>
      </a-table>
    </a-modal>
    
    <!-- 待开票详情弹窗 -->
    <a-modal v-model:visible="pendingInvoiceVisible" title="待开票详情" :width="1200">
      <a-table :columns="pendingInvoiceColumns" :data="data.pendingInvoiceItems" :pagination="false">
        <template #operation="{ record }">
          <div>
            <a-button type="text" size="small" class="mr-2">查看详情</a-button>
            <a-button type="primary" size="small">开具发票</a-button>
          </div>
        </template>
      </a-table>
    </a-modal>
    
    <!-- 待付款详情弹窗 -->
    <a-modal v-model:visible="pendingPaymentVisible" title="待付款详情" :width="1200">
      <a-table :columns="pendingPaymentColumns" :data="data.pendingPaymentItems" :pagination="false">
        <template #amount="{ record }">
          <span class="text-red-500 font-bold">¥{{ formatNumber(record.amount) }}</span>
        </template>
        <template #operation="{ record }">
          <div>
            <a-button type="text" size="small" class="mr-2">查看详情</a-button>
            <a-button type="primary" size="small">提醒付款</a-button>
          </div>
        </template>
      </a-table>
    </a-modal>
  </a-card>
</template>

<script setup>
import { ref } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({
      logisticsException: 5,
      pendingShipment: 12,
      pendingReceipt: 8,
      pendingAcceptance: 6,
      pendingInvoice: 10,
      pendingPayment: 15,
      logisticsExceptionItems: [],
      pendingShipmentItems: [],
      pendingReceiptItems: [],
      pendingAcceptanceItems: [],
      pendingInvoiceItems: [],
      pendingPaymentItems: []
    })
  }
});

// 弹窗可见性控制
const logisticsExceptionVisible = ref(false);
const pendingShipmentVisible = ref(false);
const pendingReceiptVisible = ref(false);
const pendingAcceptanceVisible = ref(false);
const pendingInvoiceVisible = ref(false);
const pendingPaymentVisible = ref(false);

// 显示详情弹窗
const showLogisticsExceptionDetail = () => {
  logisticsExceptionVisible.value = true;
};

const showPendingShipmentDetail = () => {
  pendingShipmentVisible.value = true;
};

const showPendingReceiptDetail = () => {
  pendingReceiptVisible.value = true;
};

const showPendingAcceptanceDetail = () => {
  pendingAcceptanceVisible.value = true;
};

const showPendingInvoiceDetail = () => {
  pendingInvoiceVisible.value = true;
};

const showPendingPaymentDetail = () => {
  pendingPaymentVisible.value = true;
};

// 格式化数字为千分位
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 表格列定义
const logisticsExceptionColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '客户名称', dataIndex: 'customerName' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '物流单号', dataIndex: 'trackingNumber' },
  { title: '异常状态', dataIndex: 'status', slotName: 'status' },
  { title: '异常时间', dataIndex: 'exceptionTime' },
  { title: '操作', slotName: 'operation' }
];

const pendingShipmentColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '客户名称', dataIndex: 'customerName' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '下单时间', dataIndex: 'orderTime' },
  { title: '预计发货时间', dataIndex: 'expectedShipTime' },
  { title: '操作', slotName: 'operation' }
];

const pendingReceiptColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '客户名称', dataIndex: 'customerName' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '发货时间', dataIndex: 'shipTime' },
  { title: '物流单号', dataIndex: 'trackingNumber' },
  { title: '操作', slotName: 'operation' }
];

const pendingAcceptanceColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '客户名称', dataIndex: 'customerName' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '收货时间', dataIndex: 'receiveTime' },
  { title: '验收期限', dataIndex: 'acceptanceDeadline' },
  { title: '操作', slotName: 'operation' }
];

const pendingInvoiceColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '客户名称', dataIndex: 'customerName' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '验收时间', dataIndex: 'acceptanceTime' },
  { title: '发票类型', dataIndex: 'invoiceType' },
  { title: '操作', slotName: 'operation' }
];

const pendingPaymentColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '客户名称', dataIndex: 'customerName' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '开票时间', dataIndex: 'invoiceTime' },
  { title: '应付金额', dataIndex: 'amount', slotName: 'amount' },
  { title: '付款期限', dataIndex: 'paymentDeadline' },
  { title: '操作', slotName: 'operation' }
];
</script>

<style scoped>
.arco-card {
  height: 100%;
}
</style>
