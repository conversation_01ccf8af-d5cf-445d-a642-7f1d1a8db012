<!--
 - 客户工作台 - 应收数据组件
 - 
 - <AUTHOR>
-->
<template>
  <a-card title="应收数据" class="h-full">
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
      <div class="text-center">
        <div class="text-gray-500 mb-2">总应收金额</div>
        <div class="text-xl font-bold">¥{{ formatNumber(data.totalAmount) }}</div>
      </div>
      <div class="text-center">
        <div class="text-gray-500 mb-2">总应收笔数</div>
        <div class="text-xl font-bold">{{ data.totalCount }}</div>
      </div>
      <div class="text-center">
        <div class="text-gray-500 mb-2">已回款金额</div>
        <div class="text-xl font-bold">¥{{ formatNumber(data.receivedAmount) }}</div>
      </div>
      <div class="text-center">
        <div class="text-gray-500 mb-2">已回款笔数</div>
        <div class="text-xl font-bold">{{ data.receivedCount }}</div>
      </div>
    </div>

    <a-tabs>
      <a-tab-pane key="1" title="待收款明细">
        <a-table :columns="receivableColumns" :data="data.receivableDetails" :pagination="false">
          <template #amount="{ record }">
            ¥{{ formatNumber(record.amount) }}
          </template>
          <template #operation>
            <a-button type="text" size="small">查看详情</a-button>
          </template>
        </a-table>
      </a-tab-pane>
      <a-tab-pane key="2" title="已收款明细">
        <a-table :columns="receivedColumns" :data="data.receivedDetails" :pagination="false">
          <template #amount="{ record }">
            ¥{{ formatNumber(record.amount) }}
          </template>
          <template #operation>
            <a-button type="text" size="small">查看详情</a-button>
          </template>
        </a-table>
      </a-tab-pane>
    </a-tabs>
  </a-card>
</template>

<script setup>
defineProps({
  data: {
    type: Object,
    required: true
  }
});

// 格式化数字为千分位
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 表格列定义
const receivableColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '指派时间', dataIndex: 'assignTime' },
  { title: '金额', dataIndex: 'amount', slotName: 'amount' },
  { title: '操作', slotName: 'operation' }
];

const receivedColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '付款时间', dataIndex: 'paymentTime' },
  { title: '金额', dataIndex: 'amount', slotName: 'amount' },
  { title: '操作', slotName: 'operation' }
];
</script>

<style scoped>
.a-card {
  height: 100%;
}
</style>
