<!--
 - 客户工作台 - 客户分布图表组件
 - 
 - <AUTHOR>
-->
<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <!-- 客户行业分布 -->
    <a-card title="客户行业分布" class="h-full">
      <div ref="industryChartRef" style="width: 100%; height: 300px;"></div>
    </a-card>
    
    <!-- 客户地区分布 -->
    <a-card title="客户地区分布" class="h-full">
      <div ref="regionChartRef" style="width: 100%; height: 300px;"></div>
    </a-card>
    
    <!-- 客户等级分布 -->
    <a-card title="客户等级分布" class="h-full">
      <div ref="levelChartRef" style="width: 100%; height: 300px;"></div>
    </a-card>
    
    <!-- 客户来源分布 -->
    <a-card title="客户来源分布" class="h-full">
      <div ref="sourceChartRef" style="width: 100%; height: 300px;"></div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts/core';
import { PieChart, BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  PieChart,
  BarChart,
  CanvasRenderer
]);

// 图表DOM引用
const industryChartRef = ref(null);
const regionChartRef = ref(null);
const levelChartRef = ref(null);
const sourceChartRef = ref(null);

// 图表实例
let industryChart = null;
let regionChart = null;
let levelChart = null;
let sourceChart = null;

// 模拟数据
const industryData = [
  { value: 35, name: '制造业' },
  { value: 25, name: 'IT/互联网' },
  { value: 15, name: '教育' },
  { value: 10, name: '医疗' },
  { value: 8, name: '金融' },
  { value: 7, name: '其他' }
];

const regionData = [
  { value: 30, name: '华东' },
  { value: 25, name: '华南' },
  { value: 20, name: '华北' },
  { value: 15, name: '西南' },
  { value: 10, name: '其他' }
];

const levelData = [
  { value: 15, name: 'A级' },
  { value: 30, name: 'B级' },
  { value: 45, name: 'C级' },
  { value: 10, name: 'D级' }
];

const sourceData = [
  { value: 40, name: '官网' },
  { value: 30, name: '销售顾问' },
  { value: 15, name: '电话营销' },
  { value: 10, name: '展会' },
  { value: 5, name: '其他' }
];

// 初始化图表
onMounted(() => {
  // 初始化行业分布图表
  industryChart = echarts.init(industryChartRef.value);
  industryChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: industryData.map(item => item.name)
    },
    series: [
      {
        name: '行业分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: industryData
      }
    ]
  });

  // 初始化地区分布图表
  regionChart = echarts.init(regionChartRef.value);
  regionChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: regionData.map(item => item.name)
    },
    series: [
      {
        name: '地区分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: regionData
      }
    ]
  });

  // 初始化客户等级分布图表
  levelChart = echarts.init(levelChartRef.value);
  levelChart.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: levelData.map(item => item.name),
        axisTick: {
          alignWithLabel: true
        }
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '客户数量',
        type: 'bar',
        barWidth: '60%',
        data: levelData.map(item => ({
          value: item.value,
          itemStyle: {
            color: getColorByLevel(item.name)
          }
        }))
      }
    ]
  });

  // 初始化客户来源分布图表
  sourceChart = echarts.init(sourceChartRef.value);
  sourceChart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: sourceData.map(item => item.name)
    },
    series: [
      {
        name: '来源分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '14',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: sourceData
      }
    ]
  });

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    industryChart && industryChart.resize();
    regionChart && regionChart.resize();
    levelChart && levelChart.resize();
    sourceChart && sourceChart.resize();
  });
});

// 根据客户等级获取颜色
const getColorByLevel = (level) => {
  const levelColors = {
    'A级': '#ff4d4f',
    'B级': '#ffa940',
    'C级': '#52c41a',
    'D级': '#1890ff'
  };
  return levelColors[level] || '#8c8c8c';
};
</script>

<style scoped>
.a-card {
  height: 100%;
}
</style>
