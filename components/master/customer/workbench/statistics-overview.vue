<!--
 - 客户工作台 - 统计概览组件（包含客户统计和今日数据）
 - 
 - <AUTHOR>
-->
<template>
  <div class="grid grid-cols-1 md:grid-cols-7 gap-4 mb-6">
    <!-- 客户总数 -->
    <a-card class="bg-blue-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-blue-500 text-white mr-4">
          <icon-user class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">客户总数</div>
          <div class="text-2xl font-bold">{{ statistics.totalCustomers }}</div>
        </div>
      </div>
    </a-card>
    
    <!-- 本月新增 -->
    <a-card class="bg-green-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-green-500 text-white mr-4">
          <icon-user-add class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">本月新增</div>
          <div class="text-2xl font-bold">{{ statistics.newCustomersThisMonth }}</div>
        </div>
      </div>
    </a-card>
    
    <!-- 活跃客户 -->
    <a-card class="bg-purple-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-purple-500 text-white mr-4">
          <icon-fire class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">活跃客户</div>
          <div class="text-2xl font-bold">{{ statistics.activeCustomers }}</div>
        </div>
      </div>
    </a-card>
    
    <!-- 我的客户 -->
    <a-card class="bg-orange-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-orange-500 text-white mr-4">
          <icon-star class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">我的客户</div>
          <div class="text-2xl font-bold">{{ statistics.myCustomers }}</div>
        </div>
      </div>
    </a-card>
    
    <!-- 今日下单金额 -->
    <a-card class="bg-cyan-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-cyan-500 text-white mr-4">
          <icon-message class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">今日下单金额</div>
          <div class="text-2xl font-bold">¥{{ formatAmountWithUnit(todayData.orderAmount) }}</div>
        </div>
      </div>
    </a-card>
    
    <!-- 今日下单单量 -->
    <a-card class="bg-indigo-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-indigo-500 text-white mr-4">
          <icon-message class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">今日下单单量</div>
          <div class="text-2xl font-bold">{{ todayData.orderCount }}</div>
        </div>
      </div>
    </a-card>
    
    <!-- 今日新增客户微信 -->
    <a-card class="bg-teal-50 border-0">
      <div class="flex items-center">
        <div class="p-3 rounded-lg bg-teal-500 text-white mr-4">
          <icon-message class="text-xl" />
        </div>
        <div>
          <div class="text-gray-500">今日新增客户微信</div>
          <div class="text-2xl font-bold text-green-500" v-if="todayData.newCustomerWechat">是</div>
          <div class="text-2xl font-bold text-red-500" v-else>否</div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
defineProps({
  statistics: {
    type: Object,
    required: true,
    default: () => ({
      totalCustomers: 0,
      newCustomersThisMonth: 0,
      activeCustomers: 0,
      myCustomers: 0
    })
  },
  todayData: {
    type: Object,
    required: true,
    default: () => ({
      orderAmount: 0,
      orderCount: 0,
      newCustomerWechat: false
    })
  }
});

// 格式化数字为千分位
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 格式化金额，超过万元时显示为"x.xx万"
const formatAmountWithUnit = (amount) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + '万';
  } else {
    return formatNumber(amount);
  }
};
</script>

<style scoped>
.arco-card {
  height: 100%;
}
</style>
