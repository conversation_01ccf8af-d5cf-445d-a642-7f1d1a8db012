<!--
 - 客户工作台 - 悬浮快捷操作按钮组件
 - 
 - <AUTHOR>
-->
<template>
  <div 
    class="quick-actions-container"
    :style="{ top: position.y + 'px', left: position.x + 'px' }"
    ref="containerRef"
  >
    <!-- 悬浮主按钮 -->
    <div 
      class="quick-action-button" 
      :class="{'active': showActions}"
      @mousedown="startDrag"
      @click="handleMainButtonClick"
    >
      <icon-plus class="text-white text-xl" v-if="!showActions" />
      <icon-close class="text-white text-xl" v-else />
      <span class="quick-action-main-label" v-if="!showActions">快捷操作</span>
    </div>
    
    <!-- 弹出的快捷操作按钮 -->
    <div class="quick-action-menu" v-show="showActions">
      <!-- 新增客户 -->
      <div 
        class="quick-action-item blue" 
        style="top: -280px;"
        @click="handleAction('customer')"
      >
        <div class="quick-action-icon">
          <icon-user-add class="text-xl" />
        </div>
        <div class="quick-action-label">新增客户</div>
      </div>
      
      <!-- 新增联系人 -->
      <div 
        class="quick-action-item green" 
        style="top: -220px;"
        @click="handleAction('contact')"
      >
        <div class="quick-action-icon">
          <icon-phone class="text-xl" />
        </div>
        <div class="quick-action-label">新增联系人</div>
      </div>
      
      <!-- 新增商机 -->
      <div 
        class="quick-action-item purple" 
        style="top: -160px;"
        @click="handleAction('opportunity')"
      >
        <div class="quick-action-icon">
          <icon-bulb class="text-xl" />
        </div>
        <div class="quick-action-label">新增商机</div>
      </div>
      
      <!-- 新增订单 -->
      <div 
        class="quick-action-item orange" 
        style="top: -100px;"
        @click="handleAction('order')"
      >
        <div class="quick-action-icon">
          <icon-bulb class="text-xl" />
        </div>
        <div class="quick-action-label">新增订单</div>
      </div>
    </div>
    
    <!-- 背景遮罩 -->
    <div 
      class="quick-action-backdrop" 
      v-show="showActions"
      @click="showActions = false"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { Message } from '@arco-design/web-vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const showActions = ref(false);
const containerRef = ref(null);

// 按钮位置
const position = ref({ x: 0, y: 0 });
// 拖拽状态
const isDragging = ref(false);
// 拖拽距离
const dragOffset = ref({ x: 0, y: 0 });

// 初始化位置
onMounted(() => {
  // 默认定位在右下角
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  position.value = {
    x: viewportWidth - 100,
    y: viewportHeight - 100
  };
  
  // 保存位置到本地存储
  const savedPosition = localStorage.getItem('quickActionsPosition');
  if (savedPosition) {
    try {
      const pos = JSON.parse(savedPosition);
      // 确保按钮不会超出屏幕
      if (pos.x >= 0 && pos.x <= viewportWidth - 80 && 
          pos.y >= 0 && pos.y <= viewportHeight - 80) {
        position.value = pos;
      }
    } catch (e) {
      console.error('解析保存的位置失败', e);
    }
  }
  
  // 添加窗口调整事件
  window.addEventListener('resize', handleWindowResize);
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize);
  window.removeEventListener('mousemove', handleDrag);
  window.removeEventListener('mouseup', stopDrag);
});

// 处理窗口调整
const handleWindowResize = () => {
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  // 确保按钮不会超出屏幕
  if (position.value.x > viewportWidth - 80) {
    position.value.x = viewportWidth - 80;
  }
  
  if (position.value.y > viewportHeight - 80) {
    position.value.y = viewportHeight - 80;
  }
};

// 开始拖拽
const startDrag = (event) => {
  // 防止在菜单弹出时拖拽
  if (showActions.value) return;
  
  event.preventDefault();
  isDragging.value = true;
  
  // 计算鼠标与元素左上角的偏移量
  dragOffset.value = {
    x: event.clientX - position.value.x,
    y: event.clientY - position.value.y
  };
  
  // 添加事件监听
  window.addEventListener('mousemove', handleDrag);
  window.addEventListener('mouseup', stopDrag);
};

// 拖拽过程
const handleDrag = (event) => {
  if (!isDragging.value) return;
  
  // 计算新位置
  const newX = event.clientX - dragOffset.value.x;
  const newY = event.clientY - dragOffset.value.y;
  
  // 限制范围，防止拖出屏幕
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  position.value = {
    x: Math.max(0, Math.min(newX, viewportWidth - 80)),
    y: Math.max(0, Math.min(newY, viewportHeight - 80))
  };
};

// 结束拖拽
const stopDrag = () => {
  isDragging.value = false;
  
  // 移除事件监听
  window.removeEventListener('mousemove', handleDrag);
  window.removeEventListener('mouseup', stopDrag);
  
  // 保存位置到本地存储
  localStorage.setItem('quickActionsPosition', JSON.stringify(position.value));
};

// 处理主按钮点击
const handleMainButtonClick = (event) => {
  // 如果正在拖拽，不切换菜单状态
  if (isDragging.value) return;
  
  // 切换快捷操作显示状态
  showActions.value = !showActions.value;
};

// 处理快捷操作点击事件
const handleAction = (type) => {
  showActions.value = false;
  
  switch (type) {
    case 'customer':
      router.push('/master/customer/list/add');
      break;
    case 'contact':
      router.push('/master/customer/contacts/add');
      break;
    case 'opportunity':
      router.push('/master/customer/opportunity/add');
      break;
    case 'order':
      router.push('/master/customer/orders/add');
      break;
    default:
      Message.info('功能开发中');
      break;
  }
};
</script>

<style scoped>
.quick-actions-container {
  position: fixed;
  z-index: 1000;
  /* 不再使用right和bottom定位，改为通过top和left动态定位 */
  user-select: none; /* 防止拖拽时选中文本 */
}

.quick-action-button {
  width: 56px;
  height: 56px;
  border-radius: 28px;
  background-color: rgb(var(--primary-6));
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: move; /* 指示可拖动 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s;
  z-index: 1002;
  position: relative;
  padding-right: 8px; /* 为文字留出空间 */
}

.quick-action-button:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  border-radius: 28px; /* 悬停时保持圆角，不变成椭圆 */
  width: auto; /* 悬停时自动调整宽度以适应文字 */
  padding-right: 16px; /* 悬停时增加右侧内边距 */
  padding-left: 16px; /* 悬停时增加左侧内边距 */
}

.quick-action-button.active {
  transform: rotate(45deg);
  background-color: rgb(var(--danger-6));
  border-radius: 50%; /* 激活时恢复为圆形 */
  width: 56px; /* 激活时恢复为固定宽度 */
  padding: 0; /* 激活时移除内边距 */
}

.quick-action-main-label {
  margin-left: 8px;
  color: white;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none; /* 防止文字影响点击事件 */
}

.quick-action-button:hover .quick-action-main-label {
  opacity: 1;
}

.quick-action-menu {
  position: absolute;
  right: 8px;
  bottom: 8px;
  z-index: 1001;
}

.quick-action-item {
  position: absolute;
  right: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  transition: all 0.3s ease-out;
  animation: fadeInUp 0.3s ease-out forwards;
}

.quick-action-item:hover {
  transform: scale(1.1);
}

.quick-action-item:hover .quick-action-label {
  opacity: 1;
  transform: translateX(0);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.quick-action-icon {
  color: white;
}

.quick-action-label {
  position: absolute;
  right: 42px; /* 更靠近按钮 */
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.8); /* 更深的背景色提高可读性 */
  color: white;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0;
  transform: translateX(0);
  transition: all 0.2s;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.quick-action-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

/* 位置调整现在直接在HTML中使用内联样式指定 */

/* 颜色设置 */
.quick-action-item.blue {
  background-color: rgb(var(--primary-6));
}

.quick-action-item.green {
  background-color: rgb(var(--success-6));
}

.quick-action-item.purple {
  background-color: rgb(var(--purple-6));
}

.quick-action-item.orange {
  background-color: rgb(var(--warning-6));
}

/* 动画效果 */
.quick-action-enter-active,
.quick-action-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.quick-action-enter-from,
.quick-action-leave-to {
  opacity: 0;
  transform: scale(0.5);
}
</style>
