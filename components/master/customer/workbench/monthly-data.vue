<!--
 - 客户工作台 - 本月数据组件
 - 
 - <AUTHOR>
-->
<template>
  <a-card title="本月数据" class="h-full">
    <div class="grid grid-cols-2 gap-4">
      <div class="flex flex-col justify-center">
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center">
            <div class="text-gray-500 mb-2">月度销售金额</div>
            <div class="text-xl font-bold text-gray-800">¥{{ formatNumber(data.targetAmount) }}</div>
          </div>
          <div class="text-center">
            <div class="text-gray-500 mb-2">完成金额</div>
            <div class="text-xl font-bold text-gray-800">¥{{ formatNumber(data.completedAmount) }}</div>
          </div>
          <div class="text-center">
            <div class="text-gray-500 mb-2">完成单量</div>
            <div class="text-xl font-bold text-gray-800">{{ data.completedOrders }}</div>
          </div>
          <div class="text-center">
            <div class="text-gray-500 mb-2">完成率</div>
            <div class="flex justify-center items-center">
              <a-progress
                :percent="data.completionRate"
                :stroke-width="8"
                :show-text="false"
                style="width: 80px"
                :stroke-color="'#52c41a'"
              />
              <span class="ml-2 text-xl font-bold text-gray-800">{{ data.completionRate }}%</span>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4 mt-4">
          <div class="text-center">
            <div class="text-gray-500 mb-2">自主品牌金额</div>
            <div class="text-xl font-bold text-gray-800">¥{{ formatNumber(data.ownBrandAmount) }}</div>
          </div>
          <div class="text-center">
            <div class="text-gray-500 mb-2">自主品牌单量</div>
            <div class="text-xl font-bold text-gray-800">{{ data.ownBrandOrders }}</div>
          </div>
        </div>
      </div>
      <div class="flex items-center justify-center">
        <div ref="chartRef" style="height: 240px; width: 100%;"></div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  BarChart,
  CanvasRenderer
]);

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
});

const chartRef = ref(null);
let chart = null;

// 格式化数字为千分位
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

onMounted(() => {
  // 初始化图表
  setTimeout(() => {
    initChart();
  }, 0);

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    chart && chart.resize();
  });
});

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;
  
  chart = echarts.init(chartRef.value);
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['目标金额', '完成金额', '自主品牌'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        data: ['本月销售']
      }
    ],
    yAxis: [
      {
        type: 'value'
      }
    ],
    series: [
      {
        name: '目标金额',
        type: 'bar',
        barWidth: '30%',
        data: [props.data.targetAmount],
        itemStyle: {
          color: '#1890ff'
        }
      },
      {
        name: '完成金额',
        type: 'bar',
        barWidth: '30%',
        data: [props.data.completedAmount],
        itemStyle: {
          color: '#52c41a'
        }
      },
      {
        name: '自主品牌',
        type: 'bar',
        barWidth: '30%',
        data: [props.data.ownBrandAmount],
        itemStyle: {
          color: '#faad14'
        }
      }
    ]
  };
  
  chart.setOption(option);
};
</script>

<style scoped>
.arco-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.arco-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
