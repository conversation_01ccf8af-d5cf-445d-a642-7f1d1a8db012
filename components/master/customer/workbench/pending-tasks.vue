<!--
 - 客户工作台 - 待处理事项组件
 - 
 - <AUTHOR>
-->
<template>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <!-- 待下推采购及ERP数量 -->
    <a-card class="h-full">
      <template #title>
        <div class="flex items-center">
          <icon-shopping-cart class="mr-2" />
          <span>待下推采购及ERP</span>
          <a-badge :count="purchaseErpData.count" class="ml-2" />
        </div>
      </template>
      <a-button type="primary" @click="showPurchaseErpDetail">查看详情</a-button>
    </a-card>

    <!-- 待提醒客户物流信息数量 -->
    <a-card class="h-full">
      <template #title>
        <div class="flex items-center">
          <icon-send class="mr-2" />
          <span>待提醒客户物流信息</span>
          <a-badge :count="logisticsData.count" class="ml-2" />
        </div>
      </template>
      <a-button type="primary" @click="showLogisticsDetail">查看详情</a-button>
    </a-card>

    <!-- 待提醒客户验收 -->
    <a-card class="h-full">
      <template #title>
        <div class="flex items-center">
          <icon-check-circle class="mr-2" />
          <span>待提醒客户验收</span>
          <a-badge :count="acceptanceData.count" class="ml-2" />
        </div>
      </template>
      <a-button type="primary" @click="showAcceptanceDetail">查看详情</a-button>
    </a-card>

    <!-- 待提醒及开票数量 -->
    <a-card class="h-full">
      <template #title>
        <div class="flex items-center">
          <icon-file class="mr-2" />
          <span>待提醒及开票</span>
          <a-badge :count="invoiceData.count" class="ml-2" />
        </div>
      </template>
      <a-button type="primary" @click="showInvoiceDetail">查看详情</a-button>
    </a-card>

    <!-- 待提醒客户付款数量 -->
    <a-card class="h-full">
      <template #title>
        <div class="flex items-center">
          <icon-money class="mr-2" />
          <span>待提醒客户付款</span>
          <a-badge :count="paymentData.count" class="ml-2" />
        </div>
      </template>
      <a-button type="primary" @click="showPaymentDetail">查看详情</a-button>
    </a-card>
  </div>

  <!-- 待下推采购及ERP详情弹窗 -->
  <a-modal v-model:visible="purchaseErpVisible" title="待下推采购及ERP详情" :width="700">
    <a-table :columns="purchaseErpColumns" :data="purchaseErpData.items" :pagination="false">
      <template #remark="{ record }">
        <a-input v-model="record.remark" placeholder="请输入未处理说明" />
      </template>
      <template #reminderTime="{ record }">
        <a-date-picker
          v-model="record.reminderTime"
          show-time
          format="YYYY-MM-DD HH:mm"
          placeholder="请选择提醒时间"
        />
      </template>
      <template #operation>
        <a-button type="text" size="small">查看详情</a-button>
      </template>
    </a-table>
  </a-modal>

  <!-- 待提醒客户物流信息详情弹窗 -->
  <a-modal v-model:visible="logisticsVisible" title="待提醒客户物流信息详情" :width="700">
    <a-table :columns="logisticsColumns" :data="logisticsData.items" :pagination="false">
      <template #remark="{ record }">
        <a-input v-model="record.remark" placeholder="请输入未处理说明" />
      </template>
      <template #reminderTime="{ record }">
        <a-date-picker
          v-model="record.reminderTime"
          show-time
          format="YYYY-MM-DD HH:mm"
          placeholder="请选择提醒时间"
        />
      </template>
      <template #operation="{ record }">
        <div>
          <a-button type="text" size="small" class="mr-2">查看详情</a-button>
          <a-button type="primary" size="small" @click="copyLogisticsTemplate(record)">复制提醒模板</a-button>
        </div>
      </template>
    </a-table>
  </a-modal>

  <!-- 待提醒客户验收详情弹窗 -->
  <a-modal v-model:visible="acceptanceVisible" title="待提醒客户验收详情" :width="700">
    <a-table :columns="acceptanceColumns" :data="acceptanceData.items" :pagination="false">
      <template #remark="{ record }">
        <a-input v-model="record.remark" placeholder="请输入未处理说明" />
      </template>
      <template #operation>
        <div>
          <a-button type="text" size="small" class="mr-2">查看详情</a-button>
          <a-button type="primary" size="small">确认送达</a-button>
        </div>
      </template>
    </a-table>
  </a-modal>

  <!-- 待提醒及开票详情弹窗 -->
  <a-modal v-model:visible="invoiceVisible" title="待提醒及开票详情" :width="700">
    <a-table :columns="invoiceColumns" :data="invoiceData.items" :pagination="false">
      <template #status="{ record }">
        <a-tag :color="record.status === 'pending' ? 'orange' : 'blue'">
          {{ record.status === 'pending' ? '待申请' : '已申请未下载' }}
        </a-tag>
      </template>
      <template #remark="{ record }">
        <a-input v-model="record.remark" placeholder="请输入未处理说明" />
      </template>
      <template #operation="{ record }">
        <div>
          <a-button type="text" size="small" class="mr-2">查看详情</a-button>
          <a-button 
            type="primary" 
            size="small" 
            v-if="record.status === 'pending'"
          >
            申请开票
          </a-button>
          <a-button 
            type="primary" 
            size="small" 
            v-else
          >
            下载发票
          </a-button>
        </div>
      </template>
    </a-table>
  </a-modal>

  <!-- 待提醒客户付款详情弹窗 -->
  <a-modal v-model:visible="paymentVisible" title="待提醒客户付款详情" :width="700">
    <a-table :columns="paymentColumns" :data="paymentData.items" :pagination="false">
      <template #amount="{ record }">
        ¥{{ formatNumber(record.amount) }}
      </template>
      <template #remark="{ record }">
        <a-input v-model="record.remark" placeholder="请输入未处理说明" />
      </template>
      <template #reminderTime="{ record }">
        <a-date-picker
          v-model="record.reminderTime"
          show-time
          format="YYYY-MM-DD HH:mm"
          placeholder="请选择提醒时间"
        />
      </template>
      <template #operation>
        <div>
          <a-button type="text" size="small" class="mr-2">查看详情</a-button>
          <a-button type="primary" size="small">发起认款</a-button>
        </div>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  purchaseErpData: {
    type: Object,
    required: true
  },
  logisticsData: {
    type: Object,
    required: true
  },
  acceptanceData: {
    type: Object,
    required: true
  },
  invoiceData: {
    type: Object,
    required: true
  },
  paymentData: {
    type: Object,
    required: true
  }
});

// 弹窗可见性控制
const purchaseErpVisible = ref(false);
const logisticsVisible = ref(false);
const acceptanceVisible = ref(false);
const invoiceVisible = ref(false);
const paymentVisible = ref(false);

// 显示详情弹窗
const showPurchaseErpDetail = () => {
  purchaseErpVisible.value = true;
};

const showLogisticsDetail = () => {
  logisticsVisible.value = true;
};

const showAcceptanceDetail = () => {
  acceptanceVisible.value = true;
};

const showInvoiceDetail = () => {
  invoiceVisible.value = true;
};

const showPaymentDetail = () => {
  paymentVisible.value = true;
};

// 复制物流提醒模板
const copyLogisticsTemplate = (record) => {
  const template = `领导您好，您采购的商品已经发货，物流信息如下：
商品：${record.products}
物流公司：${record.logistics.company}
物流单号：${record.logistics.trackingNumber}
到货麻烦您验收后再签收，如有问题欢迎随时沟通。`;
  
  // 复制到剪贴板
  navigator.clipboard.writeText(template).then(() => {
    Message.success('物流提醒模板已复制到剪贴板');
  }).catch(() => {
    Message.error('复制失败，请手动复制');
  });
};

// 格式化数字为千分位
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 表格列定义
const purchaseErpColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '指派时间', dataIndex: 'assignTime' },
  { title: '订单来源', dataIndex: 'source' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '未处理说明', dataIndex: 'remark', slotName: 'remark' },
  { title: '改提醒时间', dataIndex: 'reminderTime', slotName: 'reminderTime' },
  { title: '操作', slotName: 'operation' }
];

const logisticsColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '指派时间', dataIndex: 'assignTime' },
  { title: '订单来源', dataIndex: 'source' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '未处理说明', dataIndex: 'remark', slotName: 'remark' },
  { title: '改提醒时间', dataIndex: 'reminderTime', slotName: 'reminderTime' },
  { title: '操作', slotName: 'operation' }
];

const acceptanceColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '指派时间', dataIndex: 'assignTime' },
  { title: '订单来源', dataIndex: 'source' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '签收时间', dataIndex: 'signTime' },
  { title: '未处理说明', dataIndex: 'remark', slotName: 'remark' },
  { title: '操作', slotName: 'operation' }
];

const invoiceColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '指派时间', dataIndex: 'assignTime' },
  { title: '订单来源', dataIndex: 'source' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '状态', dataIndex: 'status', slotName: 'status' },
  { title: '未处理说明', dataIndex: 'remark', slotName: 'remark' },
  { title: '操作', slotName: 'operation' }
];

const paymentColumns = [
  { title: '订单编号', dataIndex: 'orderId' },
  { title: '指派时间', dataIndex: 'assignTime' },
  { title: '订单来源', dataIndex: 'source' },
  { title: '商品信息', dataIndex: 'products' },
  { title: '金额', dataIndex: 'amount', slotName: 'amount' },
  { title: '开票时间', dataIndex: 'invoiceTime' },
  { title: '未处理说明', dataIndex: 'remark', slotName: 'remark' },
  { title: '改提醒时间', dataIndex: 'reminderTime', slotName: 'reminderTime' },
  { title: '操作', slotName: 'operation' }
];
</script>

<style scoped>
.a-card {
  height: 100%;
}
</style>
