<!--
 - 客户工作台 - 今日数据组件
 - 
 - <AUTHOR>
-->
<template>
  <a-card title="今日数据" class="h-full">
    <div class="flex justify-between items-center">
      <div class="text-center flex-1">
        <div class="text-gray-500 mb-2">今日下单金额</div>
        <div class="text-xl font-bold text-gray-800">¥{{ formatNumber(data.orderAmount) }}</div>
      </div>
      <div class="text-center flex-1">
        <div class="text-gray-500 mb-2">今日下单单量</div>
        <div class="text-xl font-bold text-gray-800">{{ data.orderCount }}</div>
      </div>
      <div class="text-center flex-1">
        <div class="text-gray-500 mb-2">今日新增客户微信</div>
        <div class="text-xl font-bold">
          <span class="text-green-500 font-bold" v-if="data.newCustomerWechat">是</span>
          <span class="text-red-500 font-bold" v-else>否</span>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
defineProps({
  data: {
    type: Object,
    required: true
  }
});

// 格式化数字为千分位
const formatNumber = (num) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};
</script>

<style scoped>
.a-card {
  height: 100%;
}
</style>
