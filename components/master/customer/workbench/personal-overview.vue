<!--
 - 客户工作台 - 我的概况组件
 - 
 - <AUTHOR>
-->
<template>
  <a-card class="h-full">
    <div class="mb-4">
      <h2 class="text-lg font-bold">我的概况</h2>
    </div>
    
    <!-- 数据概览 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-blue-50 rounded-lg p-4">
        <div class="text-gray-500 mb-1">目标销售金额</div>
        <div class="text-xl font-bold">¥{{ formatAmountWithUnit(data.targetSalesAmount) }}</div>
      </div>
      
      <div class="bg-green-50 rounded-lg p-4">
        <div class="text-gray-500 mb-1">目标完成率</div>
        <div class="text-xl font-bold">{{ data.targetCompletionRate }}%</div>
        <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
          <div class="bg-green-500 h-2 rounded-full" :style="`width: ${data.targetCompletionRate}%`"></div>
        </div>
      </div>
      
      <div class="bg-orange-50 rounded-lg p-4">
        <div class="text-gray-500 mb-1">当日订单数</div>
        <div class="text-xl font-bold">{{ data.dailyOrderCount }}</div>
      </div>
      
      <div class="bg-purple-50 rounded-lg p-4">
        <div class="text-gray-500 mb-1">当日下单金额</div>
        <div class="text-xl font-bold">¥{{ formatAmountWithUnit(data.dailyOrderAmount) }}</div>
      </div>
    </div>
    
    <!-- 应收情况 -->
    <div class="mb-6">
      <h3 class="text-base font-medium mb-2">应收情况</h3>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">三个月内</div>
            <div class="text-lg font-bold">¥{{ formatAmountWithUnit(data.receivables.within3Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.receivables.within3Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超三个月</div>
            <div class="text-lg font-bold text-yellow-500">¥{{ formatAmountWithUnit(data.receivables.over3Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.receivables.over3Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超六个月</div>
            <div class="text-lg font-bold text-orange-500">¥{{ formatAmountWithUnit(data.receivables.over6Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.receivables.over6Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超一年</div>
            <div class="text-lg font-bold text-red-500">¥{{ formatAmountWithUnit(data.receivables.overOneYear.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.receivables.overOneYear.count }} 笔</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 回款情况 -->
    <div class="mb-6">
      <h3 class="text-base font-medium mb-2">回款情况</h3>
      <div class="bg-white rounded-lg border border-gray-200 p-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">三个月内</div>
            <div class="text-lg font-bold">¥{{ formatAmountWithUnit(data.payments.within3Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.payments.within3Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超三个月</div>
            <div class="text-lg font-bold text-yellow-500">¥{{ formatAmountWithUnit(data.payments.over3Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.payments.over3Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超六个月</div>
            <div class="text-lg font-bold text-orange-500">¥{{ formatAmountWithUnit(data.payments.over6Months.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.payments.over6Months.count }} 笔</div>
          </div>
          <div class="flex flex-col">
            <div class="text-gray-500 mb-1">超一年</div>
            <div class="text-lg font-bold text-red-500">¥{{ formatAmountWithUnit(data.payments.overOneYear.amount) }}</div>
            <div class="text-sm text-gray-500">{{ data.payments.overOneYear.count }} 笔</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 图表区域 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 本月订单数曲线图 -->
      <div>
        <h3 class="text-base font-medium mb-2">本月订单数</h3>
        <div ref="monthlyOrderCountChartRef" style="width: 100%; height: 250px;"></div>
      </div>
      
      <!-- 本月下单金额曲线图 -->
      <div>
        <h3 class="text-base font-medium mb-2">本月下单金额</h3>
        <div ref="monthlyOrderAmountChartRef" style="width: 100%; height: 250px;"></div>
      </div>
      
      <!-- 新增客户数曲线图 -->
      <div>
        <h3 class="text-base font-medium mb-2">新增客户数</h3>
        <div ref="newCustomersChartRef" style="width: 100%; height: 250px;"></div>
      </div>
    </div>
  </a-card>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkLineComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  MarkLineComponent,
  LineChart,
  CanvasRenderer
]);

const props = defineProps({
  data: {
    type: Object,
    required: true,
    default: () => ({
      targetSalesAmount: 300000,
      targetCompletionRate: 65,
      dailyOrderCount: 5,
      dailyOrderAmount: 28000,
      monthlyOrderCount: [
        { date: '05-01', count: 3 },
        { date: '05-02', count: 4 },
        { date: '05-03', count: 2 },
        { date: '05-04', count: 5 },
        { date: '05-05', count: 6 },
        { date: '05-06', count: 4 },
        { date: '05-07', count: 3 },
        { date: '05-08', count: 5 },
        { date: '05-09', count: 4 }
      ],
      monthlyOrderAmount: [
        { date: '05-01', amount: 18000 },
        { date: '05-02', amount: 22000 },
        { date: '05-03', amount: 15000 },
        { date: '05-04', amount: 28000 },
        { date: '05-05', amount: 32000 },
        { date: '05-06', amount: 24000 },
        { date: '05-07', amount: 19000 },
        { date: '05-08', amount: 26000 },
        { date: '05-09', amount: 23000 }
      ],
      newCustomers: [
        { date: '05-01', count: 1 },
        { date: '05-02', count: 2 },
        { date: '05-03', count: 1 },
        { date: '05-04', count: 2 },
        { date: '05-05', count: 3 },
        { date: '05-06', count: 1 },
        { date: '05-07', count: 2 },
        { date: '05-08', count: 2 },
        { date: '05-09', count: 1 }
      ]
    })
  }
});

// 图表DOM引用
const monthlyOrderCountChartRef = ref(null);
const monthlyOrderAmountChartRef = ref(null);
const newCustomersChartRef = ref(null);

// 图表实例
let monthlyOrderCountChart = null;
let monthlyOrderAmountChart = null;
let newCustomersChart = null;

// 格式化金额，超过万元时显示为"x.xx万"
const formatAmountWithUnit = (amount) => {
  if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + '万';
  } else {
    return amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
};

// 初始化图表
onMounted(() => {
  // 初始化本月订单数曲线图
  monthlyOrderCountChart = echarts.init(monthlyOrderCountChartRef.value);
  monthlyOrderCountChart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: props.data.monthlyOrderCount.map(item => item.date),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: props.data.monthlyOrderCount.map(item => item.count),
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(24, 144, 255, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(24, 144, 255, 0.1)'
              }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 8
      }
    ]
  });

  // 初始化本月下单金额曲线图
  monthlyOrderAmountChart = echarts.init(monthlyOrderAmountChartRef.value);
  monthlyOrderAmountChart.setOption({
    tooltip: {
      trigger: 'axis',
      formatter: function(params) {
        return `${params[0].name}<br/>${params[0].seriesName}: ¥${formatAmountWithUnit(params[0].value)}`;
      }
    },
    xAxis: {
      type: 'category',
      data: props.data.monthlyOrderAmount.map(item => item.date),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return formatAmountWithUnit(value);
        }
      }
    },
    series: [
      {
        name: '下单金额',
        data: props.data.monthlyOrderAmount.map(item => item.amount),
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#52c41a'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(82, 196, 26, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(82, 196, 26, 0.1)'
              }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 8
      }
    ]
  });

  // 初始化新增客户数曲线图
  newCustomersChart = echarts.init(newCustomersChartRef.value);
  newCustomersChart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: props.data.newCustomers.map(item => item.date),
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: props.data.newCustomers.map(item => item.count),
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#722ed1'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(114, 46, 209, 0.3)'
              },
              {
                offset: 1,
                color: 'rgba(114, 46, 209, 0.1)'
              }
            ]
          }
        },
        symbol: 'circle',
        symbolSize: 8
      }
    ]
  });

  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    monthlyOrderCountChart && monthlyOrderCountChart.resize();
    monthlyOrderAmountChart && monthlyOrderAmountChart.resize();
    newCustomersChart && newCustomersChart.resize();
  });
});
</script>

<style scoped>
.arco-card {
  height: 100%;
}
</style>
