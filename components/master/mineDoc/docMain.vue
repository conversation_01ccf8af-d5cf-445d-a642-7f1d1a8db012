<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="doc-container">
    <a-layout>
      <a-layout-header class="header">
        <div class="logo">
          <!-- <img src="/assets/logo.svg" alt="Logo" height="30"> -->
          <span>聚灵云4.0 开发文档</span>
        </div>
        <a-menu
          mode="horizontal"
          :selected-keys="['1']"
          :style="{ lineHeight: '64px' }"
        >
          <a-menu-item key="1">首页</a-menu-item>
          <a-menu-item key="2">API文档</a-menu-item>
          <a-menu-item key="3">组件</a-menu-item>
          <a-menu-item key="4">示例</a-menu-item>
        </a-menu>
      </a-layout-header>
      <a-layout>
        <a-layout-sider width="200" style="background: #fff">
          <a-menu
            mode="inline"
            :default-selected-keys="['1']"
            :default-open-keys="['sub1']"
            :style="{ height: '100%', borderRight: 0 }"
          >
            <a-sub-menu key="sub1">
              <template #title>
                <span>
                  <icon-apps />
                  开始使用
                </span>
              </template>
              <a-menu-item key="1">介绍</a-menu-item>
              <a-menu-item key="2">快速开始</a-menu-item>
              <a-menu-item key="3">安装</a-menu-item>
            </a-sub-menu>
            <a-sub-menu key="sub2">
              <template #title>
                <span>
                  <icon-code />
                  开发指南
                </span>
              </template>
              <a-menu-item key="5">目录结构</a-menu-item>
              <a-menu-item key="6">路由</a-menu-item>
              <a-menu-item key="7">状态管理</a-menu-item>
              <a-menu-item key="8">API调用</a-menu-item>
            </a-sub-menu>
            <a-sub-menu key="sub3">
              <template #title>
                <span>
                  <icon-desktop />
                  组件
                </span>
              </template>
              <a-menu-item key="9">基础组件</a-menu-item>
              <a-menu-item key="10">表单组件</a-menu-item>
              <a-menu-item key="11">数据展示</a-menu-item>
            </a-sub-menu>
          </a-menu>
        </a-layout-sider>
        <a-layout-content :style="{ padding: '0 24px', minHeight: '280px' }">
          <a-card :bordered="false" style="margin-top: 16px">
            <template #title>聚灵云4.0 开发文档</template>
            <p>欢迎使用聚灵云4.0开发文档。本文档将帮助您快速上手并使用聚灵云4.0进行开发。</p>
            <h2>系统介绍</h2>
            <p>聚灵云4.0是一个基于Nuxt 3和Arco Design Vue的现代化Web应用框架，提供了丰富的组件和功能，帮助开发者快速构建高质量的Web应用。</p>
            <h2>主要特性</h2>
            <ul>
              <li>基于Nuxt 3和Vue 3，享受最新的前端技术栈</li>
              <li>集成Arco Design Vue组件库，提供美观的UI界面</li>
              <li>内置多种布局方式，满足不同场景需求</li>
              <li>完善的权限管理系统</li>
              <li>丰富的组件和工具函数</li>
            </ul>
          </a-card>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup>
import { IconApps, IconCode, IconDesktop } from '@arco-design/web-vue/es/icon'
</script>

<style scoped>
.doc-container {
  height: 100%;
}

.header {
  display: flex;
  align-items: center;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.logo {
  display: flex;
  align-items: center;
  margin-right: 48px;
}

.logo span {
  margin-left: 12px;
  font-size: 18px;
  font-weight: 500;
  color: #1d2129;
}
</style>
