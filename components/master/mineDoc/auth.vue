<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <div class="auth-container">
    <a-card class="auth-card">
      <template #title>
        <div class="auth-title">文档访问授权</div>
      </template>
      <a-form :model="form" @submit="handleSubmit">
        <a-form-item field="password" label="访问密码">
          <a-input-password v-model="form.password" placeholder="请输入文档访问密码" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" long :loading="loading">
            验证并访问
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useDocStore } from '~/store'

const docStore = useDocStore()
const loading = ref(false)
const form = reactive({
  password: ''
})

const handleSubmit = async () => {
  if (!form.password) {
    Message.error('请输入访问密码')
    return
  }
  
  loading.value = true
  try {
    // 这里应该有一个验证密码的API调用
    // 为了示例，我们直接使用一个简单的密码验证
    if (form.password === 'admin123') {
      docStore.setAuth(true)
      Message.success('验证成功')
    } else {
      Message.error('密码错误')
    }
  } catch (error) {
    Message.error('验证失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.auth-card {
  width: 400px;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.auth-title {
  font-size: 18px;
  font-weight: 500;
  color: #1d2129;
  text-align: center;
}
</style>
