<template>
  <div class="mr-2 flex justify-end lg:justify-between w-full lg:w-auto">
    <a-space class="mr-0 lg:mr-5" size="medium">
      <a-tooltip :content="$t('sys.store')" v-if="isDev">
        <a-button
          :shape="'circle'"
          class="hidden lg:inline"
          @click="$router.push('/store')"
        >
          <template #icon>
            <icon-apps :size="16" :rotate="45" />
          </template>
        </a-button>
      </a-tooltip>

      <a-tooltip :content="$t('sys.search')">
        <a-button
          :shape="'circle'"
          @click="() => (appStore.searchOpen = true)"
          class="hidden lg:inline"
        >
          <template #icon>
            <icon-search />
          </template>
        </a-button>
      </a-tooltip>

      <!--      <a-tooltip content="锁屏">-->
      <!--        <a-button :shape="'circle'" class="hidden lg:inline">-->
      <!--          <template #icon>-->
      <!--            <icon-lock />-->
      <!--          </template>-->
      <!--        </a-button>-->
      <!--      </a-tooltip>-->

      <a-tooltip
        :content="
          isFullScreen ? $t('sys.closeFullScreen') : $t('sys.fullScreen')
        "
      >
        <a-button :shape="'circle'" class="hidden lg:inline" @click="screen">
          <template #icon>
            <icon-fullscreen-exit v-if="isFullScreen" />
            <icon-fullscreen v-else />
          </template>
        </a-button>
      </a-tooltip>

      <a-trigger trigger="click">
        <a-button :shape="'circle'">
          <template #icon>
            <a-badge
              :count="5"
              dot
              :dotStyle="{ width: '5px', height: '5px' }"
              v-if="messageStore.messageList.length > 0"
            >
              <icon-notification />
            </a-badge>
            <icon-notification v-else />
          </template>
        </a-button>

        <template #content>
          <message-notification />
        </template>
      </a-trigger>

      <a-tooltip :content="$t('sys.pageSetting')">
        <a-button
          :shape="'circle'"
          @click="() => (appStore.settingOpen = true)"
          class="hidden lg:inline"
        >
          <template #icon>
            <icon-settings />
          </template>
        </a-button>
      </a-tooltip>
      <a-tooltip :content="$t('sys.cart')" v-if="hasTokenProvider">
        <a-button
          :shape="'circle'"
          @click="() => toReportCar()"
          class="hidden lg:inline"
        >
          <template #icon>
            <svg t="1749036658402" class="icon" viewBox="0 0 1028 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4415" width="12" height="12"><path d="M332.8 790.528q19.456 0 36.864 7.168t30.208 19.968 20.48 30.208 7.68 36.864-7.68 36.864-20.48 30.208-30.208 20.48-36.864 7.68q-20.48 0-37.888-7.68t-30.208-20.48-20.48-30.208-7.68-36.864 7.68-36.864 20.48-30.208 30.208-19.968 37.888-7.168zM758.784 792.576q19.456 0 37.376 7.168t30.72 19.968 20.48 30.208 7.68 36.864-7.68 36.864-20.48 30.208-30.72 20.48-37.376 7.68-36.864-7.68-30.208-20.48-20.48-30.208-7.68-36.864 7.68-36.864 20.48-30.208 30.208-19.968 36.864-7.168zM930.816 210.944q28.672 0 44.544 7.68t22.528 18.944 6.144 24.064-3.584 22.016-13.312 37.888-22.016 62.976-23.552 68.096-18.944 53.248q-13.312 40.96-33.28 56.832t-49.664 15.872l-35.84 0-65.536 0-86.016 0-96.256 0-253.952 0 14.336 92.16 517.12 0q49.152 0 49.152 41.984 0 20.48-9.728 35.84t-38.4 14.336l-49.152 0-94.208 0-118.784 0-119.808 0-99.328 0-55.296 0q-20.48 0-34.304-9.216t-23.04-24.064-14.848-32.256-8.704-32.768q-1.024-6.144-5.632-29.696t-11.264-58.88-14.848-78.848-16.384-87.552q-19.456-103.424-44.032-230.4l-76.8 0q-15.36 0-25.6-7.68t-16.896-18.432-9.216-23.04-2.56-22.528q0-20.48 13.824-33.792t37.376-12.288l103.424 0q20.48 0 32.768 6.144t19.456 15.36 10.24 18.944 5.12 16.896q2.048 8.192 4.096 23.04t4.096 30.208q3.072 18.432 6.144 38.912l700.416 0zM892.928 302.08l-641.024-2.048 35.84 185.344 535.552 1.024z" p-id="4416" fill="#707070"></path></svg>
          </template>
          <a-tag style="position: absolute; top: -4px; right: -10px; font-size: 12px; color: white; background-color: red; border-radius: 10px; padding: 0 5px; min-width: 16px; height: 16px; line-height: 16px; text-align: center;">10</a-tag>
        </a-button>
      </a-tooltip>
    </a-space>
    <a-dropdown @select="handleSelect" trigger="hover">
      <a-avatar class="bg-blue-500 text-3xl avatar" style="top: -1px">
       
        <img src="/assets/avatar.jpg"
      />
      </a-avatar>

      <template #content>
        <a-doption value="userCenter"
          ><icon-user /> {{ $t("sys.userCenter") }}</a-doption
        >
        <a-doption value="clearCache"
          ><icon-delete /> {{ $t("sys.clearCache") }}</a-doption
        >
        <a-divider style="margin: 5px 0" />
        <a-doption value="logout"
          ><icon-poweroff /> {{ $t("sys.logout") }}</a-doption
        >
      </template>
    </a-dropdown>

    <a-modal
      v-model:visible="showLogoutModal"
      @ok="handleLogout"
      @cancel="handleLogoutCancel"
    >
      <template #title>{{ $t("sys.logoutAlert") }}</template>
      <div>{{ $t("sys.logoutMessage") }}</div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useAppStore, useUserStore, useMessageStore } from "~/store/index.js";
import tool from "~/utils/tool.js";
import MessageNotification from "~/components/master/layout/components/message-notification.vue";
import { useI18n } from "vue-i18n";
import { Message } from "@arco-design/web-vue";
// import WsMessage from '~/ws-serve/message.js'
import { info } from "~/utils/common.js";
import commonApi from "~/api/common.js";
import authApi from "~/api/master/auth.js";
import providerAuthApi from "~/api/provider/auth.js";

const { t } = useI18n();
const messageStore = useMessageStore();
const userStore = useUserStore();
const appStore = useAppStore();
const setting = ref(null);
const router = useRouter();
const isFullScreen = ref(false);
const showLogoutModal = ref(false);
const isDev = ref(import.meta.env.DEV);
const hasTokenProvider = ref(false);

onMounted(() => {
  // 检查 localStorage 中是否存在 token_provider
  hasTokenProvider.value = !!localStorage.getItem('token_provider');
});

const toReportCar = () => {
  import("@/utils/common").then((module) => {
    const router = useRouter();
    module.navigateWithTag(router, "/provider/reportCar");
  });
}
const handleSelect = async (name) => {
  if (name === "userCenter") {
    router.push({ name: "userCenter" });
  }
  if (name === "clearCache") {
    const res = await commonApi.clearAllCache();
    tool.local.remove("dictData");
    res.success && Message.success(res.message);
  }
  if (name === "logout") {
    showLogoutModal.value = true;
    document.querySelector("#__nuxt").style.filter = "grayscale(1)";
  }
};

// 导航函数
const navigateTo = (path) => {
  window.location.href = path;
};

const handleLogout = async () => {
  try {
    const currentPath = window.location.pathname;
    
    // 提取路径前缀
    const pathParts = currentPath.split('/');
    const moduleName = pathParts.length >= 2 ? pathParts[1] : 'master';

    // 根据不同端执行不同的退出逻辑
    if (moduleName === 'master') {
      // 中控台退出：调用后端接口
      console.log('中控台退出登录，调用接口：/api/v1/master/auth/logout');
      await authApi.logout();
    } else if(moduleName === 'provider'){
      // debugger
      console.log('服务商退出登录，调用接口：/api/v1/provider/auth/logout');
      await providerAuthApi.logout();
    } else {
      // 商家端退出：只清除缓存，不调用接口
      console.log('商家端退出登录，仅清除缓存');
    }
    
    // 调用 userStore 的退出方法
    await userStore.logout();
    
    // 清除标签页缓存
    localStorage.removeItem('tags');
    
    // 清除用户信息和权限相关缓存
    localStorage.removeItem('routers');
    localStorage.removeItem('permissions');
    
    // 清除其他可能的缓存
    sessionStorage.clear(); // 清除所有会话存储
    
    // 重置 UI 状态
    document.querySelector("#__nuxt").style.filter = "grayscale(0)";
    
    // 成功提示
    Message.success('已安全退出系统');

    // 跳转到对应的登录页
    console.log(`退出系统，跳转到 /${moduleName}/login`);
    if(moduleName!=='provider'){
      window.location.href = `/${moduleName}/login`;
    }else{
      window.location.href = `/provider/index`;
    }
  } catch (error) {
    console.error('退出登录失败', error);
    Message.error('退出登录失败，请重试');
    document.querySelector("#__nuxt").style.filter = "grayscale(0)";
  }
};
const handleLogoutCancel = () => {
  document.querySelector("#__nuxt").style.filter = "grayscale(0)";
};

const screen = () => {
  tool.screen(document.documentElement);
  isFullScreen.value = !isFullScreen.value;
};

// if (appStore.ws) {
//   const Wsm = new WsMessage()
//   // Wsm.connection()
//   Wsm.getMessage()

//   Wsm.ws.on("ev_new_message", (msg, data) => {
//     if (data.length > messageStore.messageList.length) {
//       info('新消息提示', '您有新的消息，请注意查收！')
//     }
//     messageStore.messageList = data
//   });
// }
</script>
<style scoped>
:deep(.arco-avatar-text) {
  top: 1px;
}

:deep(.arco-divider-horizontal) {
  margin: 5px 0;
}

.avatar {
  cursor: pointer;
  margin-top: 6px;
}
</style>
