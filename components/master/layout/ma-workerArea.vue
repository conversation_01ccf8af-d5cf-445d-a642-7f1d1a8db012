<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->
<template>
  <a-layout-content class="work-area customer-scrollbar relative">
    <div class="h-full" :class="{ 'p-3': $route.path.indexOf('maIframe') === -1 }">
      <NuxtPage>
        <template #default="{ Component, route }">
          <Transition
              :name="appStore.animation"
              mode="out-in"
          >
            <KeepAlive :include="keepStore.keepAlives">
              <component
                  :is="Component"
                  :key="route.fullPath"
                  v-if="keepStore.show"
              />
            </KeepAlive>
          </Transition>
        </template>
      </NuxtPage>
      <iframe-view />
    </div>
  </a-layout-content>
</template>

<script setup>
  import { useAppStore, useKeepAliveStore } from '~/store/index.js'
  import IframeView from '~/components/master/layout/components/iframe-view.vue'
  const appStore = useAppStore()
  const keepStore = useKeepAliveStore()
</script>