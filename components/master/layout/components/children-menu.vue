<!--
 - <PERSON><PERSON><PERSON><PERSON> is committed to providing solutions for quickly building web applications
 - Please view the LICENSE file that was distributed with this source code,
 - For the full copyright and license information.
 - Thank you very much for using MineAdmin.
 -
 - <AUTHOR>
 - @Link   https://gitee.com/xmo/mineadmin-vue
-->

<template>
  <a-layout-content class="sys-menus">
    <template v-for="menu in modelValue" :key="menu.id">
      <template v-if="!menu.meta.hidden">
        <a-menu-item
          v-if="!menu.children || menu.children.length === 0"
          :key="menu.name"
          @click="routerPush(menu)"
        >
          <template #icon v-if="menu.meta.icon">
            <component
              :is="menu.meta.icon"
              :class="menu.meta.icon.indexOf('ma') > 0 ? 'icon' : ''"
            />
          </template>
          {{
            appStore.i18n
              ? $t(`menus.${menu.name}`).indexOf(".") > 0
                ? menu.meta.title
                : $t(`menus.${menu.name}`)
              : menu.meta.title
          }}
        </a-menu-item>
        <a-sub-menu v-else :key="menu.name">
          <template #icon v-if="menu.meta.icon">
            <component
              :is="menu.meta.icon"
              :class="menu.meta.icon.indexOf('ma') > 0 ? 'icon' : ''"
            />
          </template>
          <template #title @click="routerPush(menu.path)">
            {{
              appStore.i18n
                ? $t(`menus.${menu.name}`).indexOf(".") > 0
                  ? menu.meta.title
                  : $t(`menus.${menu.name}`)
                : menu.meta.title
            }}
          </template>
          <template v-if="menu.children">
            <children-menu v-model="menu.children" />
          </template>
        </a-sub-menu>
      </template>
    </template>
  </a-layout-content>
</template>
<script setup>
import { useTagStore, useAppStore } from "~/store/index.js";
import { Message } from "@arco-design/web-vue";
import { nextTick } from "vue";

defineProps({ modelValue: Array });

const router = useRouter();
const emits = defineEmits(["go"]);
const appStore = useAppStore();
const tagStore = useTagStore();

const routerPush = (menu) => {
  try {
    if (menu.meta && menu.meta.type === "L") {
      window.open(menu.path);
    } else if (menu.path) {
      // 使用 nextTick 确保在 DOM 更新后再进行路由跳转
      nextTick(() => {
        // 使用 catch 捕获路由错误
        router.push(menu.path).catch((err) => {
          console.warn("路由跳转错误", err);
          // 可以在这里添加错误处理，如显示提示信息
          Message.warning("页面跳转失败，请稍后再试");
        });

        // 添加标签
        try {
          tagStore.addTag({
            name: menu.name,
            title: menu.meta.title,
            path: menu.path,
          });
        } catch (error) {
          console.warn("添加标签错误", error);
        }
      });
    } else {
      console.warn("菜单未配置组件或者path");
    }
  } catch (error) {
    console.error("菜单点击错误", error);
  }
};
</script>

<style>
.sys-menus .icon {
  width: 1em;
  height: 1em;
}
.arco-menu-selected .icon {
  fill: rgb(var(--primary-6));
}
</style>
