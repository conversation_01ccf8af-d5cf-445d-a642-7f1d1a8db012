<template>
  <div class="w-full md:w-1/5 border-r border-gray-200 md:min-h-[600px] p-3 sm:p-4">
    <!-- 移动端折叠按钮 -->
    <div class="flex justify-between items-center md:hidden mb-2">
      <h3 class="font-medium text-gray-700">会员导航</h3>
      <button @click="toggleNav" class="p-2 rounded-lg hover:bg-gray-100">
        <i :class="[isNavOpen ? 'i-carbon-chevron-up' : 'i-carbon-chevron-down', 'text-lg text-gray-600']"></i>
      </button>
    </div>
    
    <!-- 移动端水平滚动导航 (小屏幕) -->
    <div class="md:hidden overflow-x-auto pb-2 mb-2" v-if="isNavOpen">
      <div class="flex space-x-2 whitespace-nowrap">
        <NuxtLink v-for="item in navItems" :key="item.path"
          :to="item.path"
          class="inline-block px-3 py-2 rounded-lg text-sm"
          :class="getHorizontalNavClass(item.name)">
          <i :class="[item.icon, 'mr-1']"></i>
          {{ item.label }}
        </NuxtLink>
      </div>
    </div>
    
    <!-- 标准导航列表 (中大屏幕) -->
    <nav :class="{'hidden': !isNavOpen && isMobile}">
      <ul>
        <li class="mb-1">
          <NuxtLink to="/mall/user/center" 
             :class="getNavLinkClass('center')">
            <i class="i-carbon-home mr-2"></i>
            <span>会员中心</span>
          </NuxtLink>
        </li>
        <li class="mb-1">
          <NuxtLink to="/mall/user/orders" 
             :class="getNavLinkClass('orders')">
            <i class="i-carbon-shopping-cart mr-2"></i>
            <span>我的订单</span>
          </NuxtLink>
        </li>
        <li class="mb-1">
          <NuxtLink to="/mall/user/profile" 
             :class="getNavLinkClass('profile')">
            <i class="i-carbon-user-profile mr-2"></i>
            <span>个人资料</span>
          </NuxtLink>
        </li>
        <li class="mb-1">
          <NuxtLink to="/mall/user/addresses" 
             :class="getNavLinkClass('addresses')">
            <i class="i-carbon-location mr-2"></i>
            <span>收货地址</span>
          </NuxtLink>
        </li>
        <li class="mb-1">
          <NuxtLink to="/mall/user/favorites" 
             :class="getNavLinkClass('favorites')">
            <i class="i-carbon-favorite mr-2"></i>
            <span>我的收藏</span>
          </NuxtLink>
        </li>
        <li class="mb-1">
          <NuxtLink to="/mall/user/history" 
             :class="getNavLinkClass('history')">
            <i class="i-carbon-favorite mr-2"></i>
            <span>浏览历史</span>
          </NuxtLink>
        </li>
        <!-- <li class="mb-1">
          <NuxtLink to="/mall/user/points" 
             :class="getNavLinkClass('points')">
            <i class="i-carbon-badge mr-2"></i>
            我的积分
          </NuxtLink>
        </li>
        <li class="mb-1">
          <NuxtLink to="/mall/user/coupons" 
             :class="getNavLinkClass('coupons')">
            <i class="i-carbon-ticket mr-2"></i>
            我的优惠券
          </NuxtLink>
        </li> -->
        <li class="mb-1">
          <NuxtLink to="/mall/user/userSecurity" 
             :class="getNavLinkClass('security')">
            <i class="i-carbon-security mr-2"></i>
            <span>账户安全</span>
          </NuxtLink>
        </li>
        <li class="mb-1">
          <NuxtLink to="/mall/user/messages" 
             :class="getNavLinkClass('messages')">
            <i class="i-carbon-notification mr-2"></i>
            <span>消息中心</span>
            <span v-if="userStore.unreadMessageCount > 0"
                  class="ml-auto bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[20px] text-center">
              {{ userStore.unreadMessageCount }}
            </span>
          </NuxtLink>
        </li>
      </ul>
    </nav>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useMallUserStore } from '~/store/mall/user';
import { useRoute } from 'vue-router';

const route = useRoute();
const userStore = useMallUserStore();

// 根据当前路由确定导航项的激活状态
// 响应式断点检测
const isMobile = ref(false);
const isNavOpen = ref(true);

// 导航项数据
const navItems = [
  { name: 'center', path: '/mall/user/center', label: '会员中心', icon: 'i-carbon-home' },
  { name: 'orders', path: '/mall/user/orders', label: '我的订单', icon: 'i-carbon-shopping-cart' },
  { name: 'profile', path: '/mall/user/profile', label: '个人资料', icon: 'i-carbon-user-profile' },
  { name: 'addresses', path: '/mall/user/addresses', label: '收货地址', icon: 'i-carbon-location' },
  { name: 'favorites', path: '/mall/user/favorites', label: '我的收藏', icon: 'i-carbon-favorite' },
  { name: 'history', path: '/mall/user/history', label: '浏览历史', icon: 'i-carbon-favorite' },
  { name: 'security', path: '/mall/user/userSecurity', label: '账户安全', icon: 'i-carbon-security' },
  { name: 'messages', path: '/mall/user/messages', label: '消息中心', icon: 'i-carbon-notification' },
];

// 切换导航显示
const toggleNav = () => {
  isNavOpen.value = !isNavOpen.value;
};

// 检测窗口大小
const checkScreenSize = () => {
  if (process.client) {
    isMobile.value = window.innerWidth < 768; // md断点
    isNavOpen.value = !isMobile.value; // 在移动端默认收起，桌面端展开
  }
};

// 组件挂载时检测屏幕尺寸
onMounted(() => {
  checkScreenSize();
  window.addEventListener('resize', checkScreenSize);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('resize', checkScreenSize);
  }
});

// 标准导航链接样式
const getNavLinkClass = (pageName) => {
  const currentPath = route.path;
  const isActive = currentPath.includes(`/mall/user/${pageName}`);
  
  return isActive 
    ? 'flex items-center px-3 py-2 sm:px-4 sm:py-3 rounded-lg text-blue-600 bg-blue-50 font-medium'
    : 'flex items-center px-3 py-2 sm:px-4 sm:py-3 rounded-lg text-gray-700 hover:bg-gray-50 hover:text-blue-600';
};

// 水平导航链接样式
const getHorizontalNavClass = (pageName) => {
  const currentPath = route.path;
  const isActive = currentPath.includes(`/mall/user/${pageName}`);
  
  return isActive 
    ? 'bg-blue-50 text-blue-600 font-medium'
    : 'bg-gray-50 text-gray-700';
};
</script>

<style scoped>
/* 导航组件特定样式 */
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
}
.overflow-x-auto::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}
</style>
