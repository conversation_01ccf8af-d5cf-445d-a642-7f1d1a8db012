<template>
  <a-modal
    :visible="localVisible"
    :title="title"
    @cancel="handleCancel"
    @ok="handleOk"
    width="500px"
    :mask-closable="false"
  >
    <div class="wechat-bind-modal">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex flex-col items-center justify-center py-8">
        <a-spin size="large" />
        <p class="mt-4 text-gray-500">正在加载微信绑定二维码...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="flex flex-col items-center justify-center py-8">
        <div class="text-red-500 text-4xl mb-4">
          <i class="i-carbon-warning-alt"></i>
        </div>
        <p class="text-gray-700 mb-4">{{ errorMessage || '获取二维码失败，请稍后再试' }}</p>
        <a-button type="primary" @click="getQrcode">重新获取</a-button>
      </div>
      
      <!-- 二维码展示 -->
      <div v-else-if="qrcodeUrl" class="flex flex-col items-center justify-center py-4">
        <p class="text-gray-700 mb-4">请使用微信扫描下方二维码完成绑定</p>
        <div class="qrcode-container border border-gray-200 p-2 rounded-md">
          <a-image :src="qrcodeUrl" width="180" height="180" :preview="false" />
        </div>
        <p class="text-gray-500 text-sm mt-4">二维码有效期 {{ Math.floor(expireIn / 60) }} 分钟</p>
        
        <!-- 扫码状态提示 -->
        <div v-if="bindStatus === 'SCANNED'" class="mt-4 p-3 bg-blue-50 text-blue-700 rounded-md w-full text-center">
          <p><i class="i-carbon-checkmark-outline mr-1"></i> 扫码成功，请在微信中确认绑定</p>
        </div>
        <div v-else-if="bindStatus === 'BOUND'" class="mt-4 p-3 bg-green-50 text-green-700 rounded-md w-full text-center">
          <p><i class="i-carbon-checkmark mr-1"></i> 微信绑定成功！</p>
        </div>
        <div v-else-if="bindStatus === 'EXPIRED'" class="mt-4 p-3 bg-red-50 text-red-700 rounded-md w-full text-center">
          <p><i class="i-carbon-time mr-1"></i> 二维码已过期，请重新获取</p>
          <a-button type="primary" size="small" class="mt-2" @click="getQrcode">重新获取</a-button>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-between items-center">
        <span class="text-gray-500 text-sm" v-if="qrcodeUrl && bindStatus !== 'BOUND'">
          <i class="i-carbon-information mr-1"></i> 绑定后可使用微信扫码快速登录
        </span>
        <div>
          <a-button @click="handleCancel">{{ bindStatus === 'BOUND' ? '完成' : '取消' }}</a-button>
          <a-button v-if="bindStatus === 'BOUND'" type="primary" @click="handleSuccess" class="ml-2">刷新页面</a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { getBindQrcode, checkBindStatus } from '@/api/mall/wechat';
import { useMallUserStore } from '@/store/mall/user';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '绑定微信账号'
  }
});

const emit = defineEmits(['update:visible', 'success', 'cancel']);

const userStore = useMallUserStore();
const loading = ref(false);
const error = ref(false);
const errorMessage = ref('');
const qrcodeUrl = ref('');
const sceneStr = ref('');
const expireIn = ref(0);
const bindStatus = ref('CREATED');
const pollingTimer = ref(null);
const localVisible = ref(props.visible);

// 监听外部传入的 visible 属性变化
watch(() => props.visible, (val) => {
  localVisible.value = val;
  if (val) {
    getQrcode();
  } else {
    stopPolling();
  }
});

// 监听内部 localVisible 变化，向外部发送更新事件
watch(localVisible, (val) => {
  emit('update:visible', val);
});

// 获取微信绑定二维码
const getQrcode = async () => {
  try {
    loading.value = true;
    error.value = false;
    bindStatus.value = 'CREATED';
    
    const token = userStore.token;
    if (!token) {
      throw new Error('用户未登录');
    }
    
    const result = await getBindQrcode(token);
    
    if (result.code === 200 && result.data) {
      qrcodeUrl.value = result.data.qrcodeUrl;
      sceneStr.value = result.data.sceneStr;
      expireIn.value = result.data.expireIn;
      
      // 开始轮询绑定状态
      startPolling();
    } else {
      throw new Error(result.message || '获取二维码失败');
    }
  } catch (err) {
    console.error('获取微信绑定二维码失败:', err);
    error.value = true;
    errorMessage.value = err.message || '获取二维码失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

// 开始轮询绑定状态
const startPolling = () => {
  stopPolling(); // 先停止之前的轮询
  
  pollingTimer.value = setInterval(async () => {
    try {
      if (!sceneStr.value || !props.visible) {
        stopPolling();
        return;
      }
      
      const token = userStore.token;
      if (!token) {
        stopPolling();
        return;
      }
      
      const result = await checkBindStatus(sceneStr.value, token);
      
      if (result.code === 200 && result.data) {
        bindStatus.value = result.data.status;
        
        // 如果绑定成功或过期，停止轮询
        if (['BOUND', 'EXPIRED'].includes(result.data.status)) {
          stopPolling();
          
          if (result.data.status === 'BOUND') {
            Message.success('微信绑定成功');
            setTimeout(() => {
              handleSuccess();
            }, 2000);
          }
        }
      } else {
        console.error('查询绑定状态失败:', result.message);
      }
    } catch (err) {
      console.error('查询绑定状态失败:', err);
    }
  }, 2000); // 每2秒查询一次
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value);
    pollingTimer.value = null;
  }
};

// 处理取消
const handleCancel = () => {
  stopPolling();
  localVisible.value = false;
  emit('cancel');
};

// 处理确认
const handleOk = () => {
  stopPolling();
  localVisible.value = false;
};

// 处理绑定成功
const handleSuccess = () => {
  stopPolling();
  localVisible.value = false;
  emit('success');
};

// 组件卸载时停止轮询
onUnmounted(() => {
  stopPolling();
});
</script>

<style scoped>
.wechat-bind-modal {
  min-height: 300px;
}
</style>
