<template>
  <div class="product-list-container bg-white rounded-lg p-6">
    <!-- 标题部分 -->
    <div class="title-section flex items-center justify-between mb-4">
      <div class="title-left flex items-center">
        <div class="title-bar w-1 h-5 bg-primary mr-2"></div>
        <h3 class="text-lg font-bold">{{ title }}</h3>
      </div>
      <div class="title-right">
        <slot v-if="!rightSwitch" name="title-right"></slot>
        <div v-if="rightSwitch" @click="handleRightSwitch" class="text-xs text-red-600 flex items-center cursor-pointer">
          <i class="iconfont icon-reload animate-spin animate-duration-750 animate-ease-in-out transform" :class="rightLoading ? 'rotate-180' : ''"></i>
          <span>换一批</span>
        </div>
      </div>
    </div>

    <!-- 商品列表部分 -->
    <div class="product-list-content">
      <a-spin :loading="loading" tip="加载中...">
        <div v-if="errorMsg" class="error-message text-center py-4 text-red-600">{{ errorMsg }}</div>
        <div v-if="displayItems.length === 0 && !loading && !errorMsg" class="empty-message text-center py-4 text-gray-500">
          暂无商品数据
        </div>
        <div v-if="autoLoad" class="product-grid" :style="gridStyle">
          <div v-for="item in displayItems" :key="item.id" class="product-item">
            <div @click="goToDetail(item.id)" class="product-card bg-white overflow-hidden transition-all border border-transparent hover:border-red-600">
              <!-- 商品图片 -->
              <div class="product-image-container" :style="imageHeight > 0 ? { height: `${imageHeight}px` } : { height: 'auto' }">
                <a-image
                  v-if="item.imageUrl"
                  :src="item.imageUrl"
                  :width="imageWidth > 0 ? imageWidth : '100%'"
                  :height="imageHeight > 0 ? imageHeight : 'auto'"
                  fit="cover"
                  class="product-image"
                  :preview="false"
                />
                <a-image
                  v-else
                  src="/images/placeholder.png"
                  :width="imageWidth > 0 ? imageWidth : '100%'"
                  :height="imageHeight > 0 ? imageHeight : 'auto'"
                  fit="cover"
                  class="product-image"
                  :preview="false"
                />
              </div>
              
              <!-- 商品信息 -->
              <div class="product-info p-3">
                <!-- 商品名称 -->
                <div class="product-name text-sm font-medium mb-2 line-clamp-2" :title="item.name">
                  {{ item.name }}
                </div>
                
                <!-- 商品价格和销量 -->
                <div class="product-meta flex justify-between items-center">
                  <div class="product-price text-red-600 font-bold">
                    <span v-if="item.salePrice > 0">¥{{ formatPrice(item.salePrice) }}</span>
                    <span v-else class="text-gray-400">暂无价格</span>
                  </div>
                  <div class="product-sales text-xs text-gray-500">
                    已售 {{ item.totalSales || 0 }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </a-spin>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import goodsApi from '@/api/mall/goods';

// 定义组件属性
const props = defineProps({
  // 列表左侧标题
  title: {
    type: String,
    default: '推荐商品'
  },
  // 右侧切换按钮
  rightSwitch: {
    type: Boolean,
    default: false
  },
  // 右侧切换按钮标题
  rightTitle: {
    type: String,
    default: '换一批'
  },
  // 商品图片宽度
  imageWidth: {
    type: Number,
    default: 0 // 0表示使用自适应宽度
  },
  // 商品图片高度
  imageHeight: {
    type: Number,
    default: 0 // 0表示使用自适应高度
  },
  // 一行展示商品数量（移动端）
  columnsPerRow: {
    type: Number,
    default: 2
  },
  // 中等屏幕一行展示商品数量
  columnsPerRowMd: {
    type: Number,
    default: 3
  },
  // 大屏幕一行展示商品数量
  columnsPerRowLg: {
    type: Number,
    default: 5
  },
  // 商品数量（已废弃，使用响应式数量配置）
  count: {
    type: Number,
    default: 0 // 0表示使用响应式配置
  },
  // 小屏幕商品数量（<768px）
  countSm: {
    type: Number,
    default: 4
  },
  // 中等屏幕商品数量（768px-1535px）
  countMd: {
    type: Number,
    default: 6
  },
  // 大屏幕商品数量（≥1536px）
  countLg: {
    type: Number,
    default: 5
  },
  // 是否随机获取
  random: {
    type: Boolean,
    default: true
  },
  // 是否自动加载数据
  autoLoad: {
    type: Boolean,
    default: true
  },
  // 商品列表数据（外部传入时使用）
  items: {
    type: Array,
    default: () => []
  },
  // 商品间距
  gap: {
    type: Number,
    default: 2 // 使用相对较小的间距，在样式中转换为rem
  },
  // 是否启用响应式商品数量
  enableResponsiveCount: {
    type: Boolean,
    default: true
  }
});

// 定义事件
const emit = defineEmits(['right-switch', 'screen-size-change', 'count-change']);

// 商品列表数据
const productList = ref([]);

// 右侧切换按钮加载状态
const rightLoading = ref(false);

// 加载状态
const loading = ref(false);

// 错误信息
const errorMsg = ref('');

// 当前屏幕尺寸
const screenSize = ref('sm');

// 防抖定时器
let resizeTimer = null;

// 计算当前应该加载的商品数量
const currentCount = computed(() => {
  if (!props.enableResponsiveCount || props.count > 0) {
    return props.count || 5; // 如果禁用响应式或手动设置了count，使用固定值
  }

  // 根据屏幕尺寸返回对应的商品数量
  switch (screenSize.value) {
    case 'sm':
      return props.countSm;
    case 'md':
      return props.countMd;
    case 'lg':
      return props.countLg;
    default:
      return props.countSm;
  }
});

// 计算最终展示的商品列表
const displayItems = computed(() => {
  // 如果外部传入了商品列表，则优先使用外部数据
  return props.items.length > 0 ? props.items : productList.value;
});

// 跳转到商品详情页
const goToDetail = (id) => {
  import('@/utils/common').then(module => {
    const { $router } = useNuxtApp();
    module.navigateWithTag($router, `/mall/product/${id}`);
  });
}
// 计算网格样式
const gridStyle = computed(() => {
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${props.columnsPerRow}, 1fr)`,
    gap: `${props.gap}rem`,
    width: '100%',
    height: '100%',
    overflowX: 'hidden'
  };
});

// 格式化价格
const formatPrice = (price) => {
  return parseFloat(price).toFixed(2);
};

// 检测屏幕尺寸
const detectScreenSize = () => {
  const width = window.innerWidth;
  if (width < 768) {
    screenSize.value = 'sm';
  } else if (width < 1536) {
    screenSize.value = 'md';
  } else {
    screenSize.value = 'lg';
  }
};

// 防抖处理屏幕尺寸变化
const handleResize = () => {
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
  resizeTimer = setTimeout(() => {
    const oldSize = screenSize.value;
    const oldCount = currentCount.value;
    detectScreenSize();

    // 发射屏幕尺寸变化事件
    if (oldSize !== screenSize.value) {
      emit('screen-size-change', {
        oldSize,
        newSize: screenSize.value,
        oldCount,
        newCount: currentCount.value
      });
    }

    // 发射商品数量变化事件
    if (oldCount !== currentCount.value) {
      emit('count-change', {
        oldCount,
        newCount: currentCount.value,
        screenSize: screenSize.value
      });
    }

    // 如果屏幕尺寸发生变化且启用了响应式商品数量，重新获取数据
    if (props.enableResponsiveCount && oldSize !== screenSize.value && props.autoLoad && props.items.length === 0) {
      fetchFeaturedProducts();
    }
  }, 300);
};

// 获取精选商品列表
const fetchFeaturedProducts = async () => {
  try {
    loading.value = true;
    errorMsg.value = '';

    const response = await goodsApi.getFeaturedProducts({
      count: currentCount.value,
      random: props.random
    });

    if (response && response.code === 200 && response.data) {
      productList.value = response.data;
    } else {
      errorMsg.value = response.message || '获取商品列表失败';
    }
  } catch (error) {
    console.error('获取精选商品列表出错:', error);
    errorMsg.value = '获取商品列表失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

// 右侧切换按钮点击事件
const handleRightSwitch = () => {
  rightLoading.value = true;
  fetchFeaturedProducts();
  setTimeout(() => {
    rightLoading.value = false;
  }, 750);
  emit('right-switch');
};

// 监听响应式商品数量变化
watch(currentCount, (newCount, oldCount) => {
  // 当商品数量发生变化且启用了自动加载时，重新获取数据
  if (newCount !== oldCount && props.autoLoad && props.items.length === 0) {
    fetchFeaturedProducts();
  }
});

// 暴露方法和属性给父组件
defineExpose({
  fetchFeaturedProducts,
  currentCount,
  screenSize
});

// 组件挂载时的初始化
onMounted(() => {
  // 初始化屏幕尺寸检测
  detectScreenSize();

  // 添加窗口尺寸变化监听器
  window.addEventListener('resize', handleResize);

  // 如果设置了自动加载，则获取数据
  if (props.autoLoad && props.items.length === 0) {
    fetchFeaturedProducts();
  }
});

// 组件卸载时清理
onUnmounted(() => {
  // 移除窗口尺寸变化监听器
  window.removeEventListener('resize', handleResize);

  // 清理防抖定时器
  if (resizeTimer) {
    clearTimeout(resizeTimer);
  }
});
</script>

<style scoped>
/* 响应式网格布局 */
@media (min-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(v-bind(props.columnsPerRowMd), 1fr) !important;
  }
}

@media (min-width: 1024px) and (max-width: 1535px) {
  .product-grid {
    grid-template-columns: repeat(v-bind(props.columnsPerRowMd), 1fr) !important;
  }
}

@media (min-width: 1536px) {
  .product-grid {
    grid-template-columns: repeat(v-bind(props.columnsPerRowLg), 1fr) !important;
  }
}

.product-list-content {
  width: 100%;
  overflow-x: hidden;
}
.product-list-container {
  background-color: #ffffff;
  margin-bottom: 1.25rem;
}

.title-bar {
  background-color: #e4393c;
}

.product-card {
  transition: all 0.3s ease;
  box-sizing: border-box;
  height: 100%;
}

.product-name {
  height: 2.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff;
  max-width: 100%;
}

.product-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  max-width: 100%;
  aspect-ratio: 1/1; /* 宽高比1:1 */
}

/* 移动端样式优化 */
@media (max-width: 768px) {
  .product-list-container {
    padding: 0.75rem;
    width: 100%;
    overflow-x: hidden;
  }
  
  .product-info {
    padding: 0.5rem;
  }
  
  .product-name {
    font-size: 0.75rem;
    height: auto;
    min-height: 2.25rem;
  }
  
  .product-meta {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .product-price {
    margin-bottom: 0.25rem;
  }
  
  .product-grid {
    width: 100%;
  }
  
  .product-item {
    width: 100%;
  }
}

/* 针对1536px以下屏幕的样式优化 */
@media (max-width: 1536px) {
  .product-list-container {
    width: 100%;
    overflow-x: hidden;
  }
  
  .product-grid {
    width: 100%;
    height: 100%;
  }
  
  .product-item {
    width: 100%;
    height: 100%;
  }
}
</style>
