<template>
  <div class="location-selector">
    <div class="current-location" @click="toggleSelector">
      <i class="iconfont icon-location" style="height: 100%;"></i>
      <span>{{ currentProvince || '未定位' }}</span>
      <i class="iconfont icon-arrow-down" :class="{ 'rotate': showSelector }"></i>
    </div>
    <div v-if="showSelector" class="province-selector">
      <div class="province-grid">
        <div
          v-for="province in provinces"
          :key="province.code"
          class="province-item whitespace-nowrap"
          :class="{ 'active': province.code === currentProvinceCode }"
          @click="selectProvince(province)"
        >
          {{ province.name }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useMallLocalStore } from '@/store/mall/local'

// 获取本地存储
const localStore = useMallLocalStore()
// 响应式数据
const provinces = ref([])
const showSelector = ref(false)
const currentProvinceCode = ref(null)
const currentProvince = ref(null)



// 切换选择器显示状态
const toggleSelector = () => {
  showSelector.value = !showSelector.value
}

// 选择省份
const selectProvince = (province) => {
  currentProvinceCode.value = province.code
  currentProvince.value = province.name
  showSelector.value = false
  
  // 触发选择事件，可以被父组件监听
  emit('select', province)
}

// 定义组件事件
const emit = defineEmits(['select'])

// 组件挂载时获取位置信息
onMounted(async () => {
  console.log('组件挂载时获取位置信息')
  // 初始化本地信息
  localStore.initLocalInfo()
  // 获取省市县树状数据
  await localStore.fetchProvinceTree()
  // 获取IP定位信息
  await localStore.fetchLocationInfo()
  provinces.value = localStore.allProvinces
  // 获取位置信息
  currentProvinceCode.value = localStore.locationInfo.cityCode
  currentProvince.value = localStore.locationInfo.cityName
})

// 点击外部关闭选择器
const handleClickOutside = (event) => {
  const selectorElement = document.querySelector('.location-selector')
  if (selectorElement && !selectorElement.contains(event.target)) {
    showSelector.value = false
  }
}

// 添加和移除全局点击事件监听器
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.location-selector {
  position: relative;
  display: inline-block;
  font-size: 14px;
}

.current-location {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.icon-arrow-down {
  width: 0;
  height: 0;
  margin-left: 4px;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #666;
  transition: transform 0.3s;
}

.icon-arrow-down.rotate {
  transform: rotate(180deg);
}

.province-selector {
  position: absolute;
  top: 100%;
  left: 0;
  width: 400px;
  background-color: #fff;
  border: 1px solid #eee;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 10px;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.province-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 2px;
}

.province-item {
  white-space: nowrap;
  font-weight: bold;
  padding: 2px 2px;
  width: 100%;
  text-align: center;
  cursor: pointer;
  transition: background-color 0.2s;
  border-radius: 4px;
  overflow: hidden;
}

.province-item:hover {
  background-color: #f5f5f5;
}

.province-item.active {
  background-color: #f00;
  color: #fff;
}
</style>
