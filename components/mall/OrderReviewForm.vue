<template>
  <div class="order-review-form">
    <!-- 评分区域 -->
    <div class="mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="flex items-center">
          <span class="text-gray-600 w-20">商品质量：</span>
          <a-rate v-model="formData.qualityRating"  />
          <span class="ml-2 text-gray-500">{{ formData.qualityRating }}分</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-600 w-20">服务态度：</span>
          <a-rate v-model="formData.serviceRating"  />
          <span class="ml-2 text-gray-500">{{ formData.serviceRating }}分</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-600 w-20">物流速度：</span>
          <a-rate v-model="formData.logisticsRating"  />
          <span class="ml-2 text-gray-500">{{ formData.logisticsRating }}分</span>
        </div>
      </div>
    </div>

    <!-- 评价内容 -->
    <div class="mb-6">
      <div class="mb-2">
        <span class="text-gray-600">评价内容：</span>
        <span class="text-gray-400 text-sm ml-2">(选填，10-500字)</span>
      </div>
      <a-textarea
        v-model="formData.reviewContent"
        placeholder="请描述您的使用体验、商品优缺点等..."
        :max-length="500"
        :rows="4"
        show-word-limit
        :auto-size="{ minRows: 4, maxRows: 8 }"
      />
    </div>

    <!-- 图片上传 -->
    <div class="mb-6">
      <div class="mb-2">
        <span class="text-gray-600">上传图片：</span>
        <span class="text-gray-400 text-sm ml-2">(选填，最多5张，支持JPG/PNG格式，单张不超过5MB)</span>
      </div>
      <div class="flex flex-wrap gap-4">
        <!-- 已上传的图片 -->
        <div 
          v-for="(image, index) in formData.images" 
          :key="index"
          class="relative w-24 h-24 border border-gray-300 rounded-lg overflow-hidden"
        >
          <img :src="image.fileUrl" :alt="image.originalName" class="w-full h-full object-cover">
          <button
            @click="removeImage(index)"
            class="absolute top-1 right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center hover:bg-red-600"
          >
            ×
          </button>
        </div>
        
        <!-- 上传按钮 -->
        <div 
          v-if="formData.images.length < 5"
          class="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center cursor-pointer hover:border-blue-500 hover:bg-blue-50"
          @click="triggerFileUpload"
        >
          <div class="text-center">
            <i class="i-carbon-add text-2xl text-gray-400"></i>
            <div class="text-xs text-gray-400 mt-1">上传图片</div>
          </div>
        </div>
      </div>
      
      <!-- 隐藏的文件输入框 -->
      <input
        ref="fileInput"
        type="file"
        accept="image/jpeg,image/jpg,image/png"
        multiple
        class="hidden"
        @change="handleFileSelect"
      >
    </div>

    <!-- 匿名评价选项 -->
    <div class="mb-6">
      <a-checkbox v-model="formData.isAnonymous">
        匿名评价（开启后将隐藏您的用户名）
      </a-checkbox>
    </div>

    <!-- 提交按钮 -->
    <div class="flex justify-end">
      <a-button 
        type="primary" 
        size="large"
        :loading="isSubmitting"
        @click="handleSubmit"
      >
        提交评价
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, defineEmits } from 'vue';
import { Message } from '@arco-design/web-vue';
import commonApi from '@/api/common';

// 定义props
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  orderId: {
    type: [String, Number],
    required: true
  }
});

// 定义emits
const emit = defineEmits(['submit', 'loading']);

// 响应式数据
const fileInput = ref(null);
const isSubmitting = ref(false);
const isUploading = ref(false);

// 表单数据
const formData = reactive({
  qualityRating: 5,
  serviceRating: 5,
  logisticsRating: 5,
  reviewContent: '',
  isAnonymous: false,
  images: []
});

// 触发文件选择
const triggerFileUpload = () => {
  if (formData.images.length >= 5) {
    Message.warning('最多只能上传5张图片');
    return;
  }
  fileInput.value?.click();
};

// 处理文件选择
const handleFileSelect = async (event) => {
  const files = Array.from(event.target.files);
  
  if (files.length === 0) return;
  
  // 检查文件数量限制
  if (formData.images.length + files.length > 5) {
    Message.warning('最多只能上传5张图片');
    return;
  }

  // 验证文件
  for (const file of files) {
    if (!validateFile(file)) {
      return;
    }
  }

  // 上传文件
  isUploading.value = true;
  emit('loading', props.item.id, true);

  try {
    for (const file of files) {
      await uploadFile(file);
    }
  } catch (error) {
    console.error('文件上传失败:', error);
  } finally {
    isUploading.value = false;
    emit('loading', props.item.id, false);
    // 清空文件输入框
    event.target.value = '';
  }
};

// 验证文件
const validateFile = (file) => {
  // 检查文件类型
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    Message.error('请上传JPG或PNG格式的图片');
    return false;
  }

  // 检查文件大小（5MB）
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    Message.error('图片大小不能超过5MB');
    return false;
  }

  return true;
};

// 上传文件
const uploadFile = async (file) => {
  try {
    const uploadFormData = new FormData();
    uploadFormData.append('file', file);
    uploadFormData.append('Dir', 'uploads/reviews');
    uploadFormData.append('Module', 'mall');
    uploadFormData.append('bizType', 'review');
    uploadFormData.append('bizId', props.item.id.toString());
    uploadFormData.append('isPublic', 'true');

    const response = await commonApi.uploadImage(uploadFormData);

    if (response.code === 200 && response.data) {
      // 添加到图片列表
      formData.images.push({
        fileUrl: response.data.fileUrl,
        filePath: response.data.filePath,
        originalName: response.data.originalName,
        fileSize: response.data.fileSize,
        extension: response.data.extension
      });

      Message.success('图片上传成功');
    } else {
      throw new Error(response.message || '图片上传失败');
    }
  } catch (error) {
    console.error('图片上传失败:', error);
    Message.error(error.message || '图片上传失败');
    throw error;
  }
};

// 移除图片
const removeImage = (index) => {
  formData.images.splice(index, 1);
};

// 提交评价
const handleSubmit = async () => {
  // 基本验证
  if (formData.reviewContent.trim() && formData.reviewContent.trim().length < 10) {
    Message.error('评价内容至少需要10个字符');
    return;
  }

  if (formData.reviewContent.trim().length > 500) {
    Message.error('评价内容不能超过500个字符');
    return;
  }

  isSubmitting.value = true;
  emit('loading', props.item.id, true);

  try {
    const reviewData = {
      orderId: props.orderId.toString(),
      orderItemId: props.item.id.toString(),
      qualityRating: formData.qualityRating,
      serviceRating: formData.serviceRating,
      logisticsRating: formData.logisticsRating,
      reviewContent: formData.reviewContent.trim(),
      isAnonymous: formData.isAnonymous,
      images: formData.images
    };

    // 触发提交事件
    emit('submit', reviewData);
  } catch (error) {
    console.error('提交评价失败:', error);
    Message.error('提交评价失败');
  } finally {
    isSubmitting.value = false;
    emit('loading', props.item.id, false);
  }
};
</script>

<style scoped>
.order-review-form {
  /* 可以添加一些自定义样式 */
}
</style>
