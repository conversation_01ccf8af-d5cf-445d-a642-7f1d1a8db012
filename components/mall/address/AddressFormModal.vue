<template>
  <a-modal
    :visible="localVisible"
    :title="isEdit ? '编辑收货地址' : '新增收货地址'"
    @cancel="handleCancel"
    @before-ok="handleSubmit"
  >
    <a-form ref="addressFormRef" :model="addressForm" layout="vertical" :rules="addressRules">
      <a-form-item field="name" label="收货人" validate-trigger="blur" required>
        <a-input v-model="addressForm.name" placeholder="请输入收货人姓名" allow-clear />
        <template #extra><span class="text-red-500">*</span> 必填项</template>
      </a-form-item>
      
      <a-form-item field="phone" label="手机号码" validate-trigger="blur" required>
        <a-input v-model="addressForm.phone" placeholder="请输入手机号码" allow-clear />
        <template #extra><span class="text-red-500">*</span> 必填项，请输入正确的11位手机号码</template>
      </a-form-item>
      
      <a-form-item label="所在地区" required>
        <a-space>
          <a-select v-model="addressForm.province" placeholder="省份" style="width: 120px" @change="handleProvinceChange">
            <a-option v-for="province in provinceOptions" :key="province.code" :value="province.name">
              {{ province.name }}
            </a-option>
          </a-select>
          <a-select v-model="addressForm.city" placeholder="城市" style="width: 120px" @change="handleCityChange" :disabled="!addressForm.province">
            <a-option v-for="city in cityOptions" :key="city.code" :value="city.name">
              {{ city.name }}
            </a-option>
          </a-select>
          <a-select v-model="addressForm.district" placeholder="区县" style="width: 120px" :disabled="!addressForm.city">
            <a-option v-for="district in districtOptions" :key="district.code" :value="district.name">
              {{ district.name }}
            </a-option>
          </a-select>
        </a-space>
        <template #extra><span class="text-red-500">*</span> 必填项</template>
      </a-form-item>
      
      <a-form-item field="address" label="详细地址" validate-trigger="blur" required>
        <a-textarea v-model="addressForm.address" placeholder="请输入详细地址，如街道、门牌号等" allow-clear />
        <template #extra><span class="text-red-500">*</span> 必填项</template>
      </a-form-item>
      
      <a-form-item field="postcode" label="邮政编码">
        <a-input v-model="addressForm.postcode" placeholder="请输入邮政编码（选填）" allow-clear />
      </a-form-item>
      
      <a-form-item>
        <a-checkbox v-model="addressForm.isDefault">
          设为默认收货地址
        </a-checkbox>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { Message } from '@arco-design/web-vue';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  editAddress: {
    type: Object,
    default: () => null
  },
  regionData: {
    type: Object,
    default: () => ({ provinces: [], cities: {}, districts: {} })
  }
});

const emit = defineEmits(['update:visible', 'submit', 'cancel']);

// 本地可见性状态，用于双向绑定
const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 表单引用
const addressFormRef = ref(null);

// 是否为编辑模式
const isEdit = computed(() => !!props.editAddress);

// 地址表单数据
const addressForm = reactive({
  name: '',
  phone: '',
  province: '',
  city: '',
  district: '',
  address: '',
  postcode: '',
  isDefault: false
});

// 表单验证规则
const addressRules = {
  name: [
    { required: true, message: '请输入收货人姓名' },
  ],
  phone: [
    { required: true, message: '请输入手机号码' },
    { match: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式' }
  ],
  address: [
    { required: true, message: '请输入详细地址' },
  ]
};

// 地区选项
const provinceOptions = computed(() => props.regionData.provinces || []);
const cityOptions = ref([]);
const districtOptions = ref([]);

// 处理省份变更
const handleProvinceChange = (province) => {
  addressForm.city = '';
  addressForm.district = '';
  cityOptions.value = props.regionData.cities[province] || [];
};

// 处理城市变更
const handleCityChange = (city) => {
  addressForm.district = '';
  const cityData = cityOptions.value.find(item => item.name === city);
  if (cityData) {
    districtOptions.value = props.regionData.districts[cityData.code] || [];
  } else {
    districtOptions.value = [];
  }
};



// 表单提交
const handleSubmit = async (done) => {
  if (!addressFormRef.value) {
    done(false);
    return;
  }
  
  try {
    // 表单验证
    await addressFormRef.value.validate();
    // 如果验证通过，不会抛出异常
    
    // 验证省市区是否已选择
    if (!addressForm.province || !addressForm.city || !addressForm.district) {
      Message.error('请选择完整的所在地区');
      done(false);
      return;
    }
    
    // 构建提交数据
    const formData = {
      ...addressForm,
      isDefault: addressForm.isDefault ? 1 : 0
    };
    
    // 提交表单
    emit('submit', formData);
    localVisible.value = false;
    done(true);
  } catch (error) {
    console.error('提交地址表单时发生错误:', error);
    done(false);
  }
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
  localVisible.value = false;
};

// 监听编辑地址变化
watch(() => props.editAddress, (newAddress) => {
  if (newAddress) {
    Object.keys(addressForm).forEach(key => {
      addressForm[key] = newAddress[key] !== undefined ? newAddress[key] : addressForm[key];
    });
    
    // 如果有自定义标签
    if (addressForm.tag && !commonTags.includes(addressForm.tag)) {
      customTag.value = addressForm.tag;
    }
    
    // 更新城市和区县选项
    if (addressForm.province) {
      cityOptions.value = props.regionData.cities[addressForm.province] || [];
      
      if (addressForm.city) {
        const cityData = cityOptions.value.find(item => item.name === addressForm.city);
        if (cityData) {
          districtOptions.value = props.regionData.districts[cityData.code] || [];
        }
      }
    }
  } else {
    // 重置表单
    Object.keys(addressForm).forEach(key => {
      if (key === 'isDefault') {
        addressForm[key] = false;
      } else {
        addressForm[key] = '';
      }
    });
  }
}, { immediate: true });
</script>

<style scoped>
.arco-tag {
  cursor: pointer;
  transition: all 0.3s;
}
</style>
