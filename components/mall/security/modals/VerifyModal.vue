<template>
  <!-- 实名认证模态框 -->
  <a-modal :visible="visible" @update:visible="(val) => emit('update:visible', val)" :title="getVerifyModalTitle(isVerified)" @cancel="handleCancel" width="600px" max-height="800px">
    <div class="verify-form">
      <a-form :model="currentVerifyForm" layout="vertical">
        <!-- 未认证状态提示 -->
        <div v-if="isVerified === 0" class="mb-4 p-3 bg-yellow-50 rounded">
          <div class="flex items-center">
            <i class="i-carbon-information text-yellow-600 text-xl mr-2"></i>
            <span class="font-medium">您尚未完成实名认证</span>
          </div>
          <div class="mt-1 ml-7 text-sm text-gray-600">
            实名认证后可享受更多会员特权和安全保障。
          </div>
        </div>
        
        <!-- 审核中或已认证状态提示 -->
        <div v-if="isVerified === 1 || isVerified === 2" class="mb-4 p-3 rounded" :class="{'bg-blue-50': isVerified === 1, 'bg-green-50': isVerified === 2}">
          <div class="flex items-center">
            <i :class="[isVerified === 1 ? 'i-carbon-time text-blue-600' : 'i-carbon-checkmark-filled text-green-600', 'text-xl mr-2']"></i>
            <span class="font-medium">{{ isVerified === 1 ? '您的实名认证正在审核中' : '您已通过实名认证' }}</span>
          </div>
          <div class="mt-1 ml-7 text-sm text-gray-600">
            <div>认证姓名：{{ currentVerifyForm.realName }}</div>
            <div>身份证号：{{ maskIdCard(currentVerifyForm.idCardNumber) }}</div>
            <div v-if="isVerified === 2">认证时间：{{ verifiedTime }}</div>
          </div>
        </div>
        
        <!-- 认证失败提示 -->
        <div v-if="isVerified === 3" class="mb-4 p-3 bg-red-50 rounded">
          <div class="flex items-center">
            <i class="i-carbon-warning-filled text-red-600 text-xl mr-2"></i>
            <span class="font-medium">实名认证未通过</span>
          </div>
          <div class="mt-1 ml-7 text-sm text-gray-600">
            <div>失败原因：{{ verifyFailReason || '认证信息不符合要求' }}</div>
            <div>请修改信息后重新提交认证</div>
          </div>
        </div>

        <a-form-item field="realName" label="真实姓名">
          <a-input 
            v-model="currentVerifyForm.realName" 
            placeholder="请输入真实姓名" 
            :readonly="isVerified === 1 || isVerified === 2"
          />
        </a-form-item>
        
        <a-form-item field="idCardNumber" label="身份证号码">
          <a-input 
            v-model="currentVerifyForm.idCardNumber" 
            placeholder="请输入身份证号" 
            :readonly="isVerified === 1 || isVerified === 2"
          />
        </a-form-item>
        
        <a-form-item field="authPhone" label="认证手机号">
          <a-input 
            v-model="currentVerifyForm.authPhone" 
            placeholder="请输入认证手机号" 
            :readonly="isVerified === 1 || isVerified === 2"
          />
        </a-form-item>
        
        <a-form-item field="bankCardNo" label="银行卡号">
          <a-input 
            v-model="currentVerifyForm.bankCardNo" 
            placeholder="请输入银行卡号" 
            :readonly="isVerified === 1 || isVerified === 2"
          />
          <div class="text-xs text-gray-500 mt-1">请输入您的银行卡号，用于提现和账户验证</div>
        </a-form-item>
        
        <a-form-item field="idCardFront" label="身份证正面照片">
          <div class="relative w-64 h-40 border border-gray-300 rounded-md overflow-hidden mb-2">
            <a-image 
              v-if="currentVerifyForm.idCardFront" 
              :src="currentVerifyForm.idCardFront" 
              alt="身份证正面" 
              class="w-full h-full object-cover"
            />
            <div 
              v-else 
              class="w-full h-full flex flex-col items-center justify-center bg-gray-50"
            >
              <i class="i-carbon-image text-gray-400 text-3xl mb-2"></i>
              <span class="text-sm text-gray-500">点击上传身份证正面照片</span>
            </div>
            <div 
              v-if="isVerified !== 1 && isVerified !== 2" 
              class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity cursor-pointer"
              @click="openIdCardFrontUpload"
            >
              <button type="button" class="text-white bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded-md text-sm">
                更换照片
              </button>
            </div>
          </div>
          <div class="text-xs text-gray-500">
            {{ isVerified === 1 || isVerified === 2 ? '身份证人像面照片' : '请上传清晰的身份证人像面照片' }}
          </div>
        </a-form-item>
        
        <a-form-item field="idCardBack" label="身份证反面照片">
          <div class="relative w-64 h-40 border border-gray-300 rounded-md overflow-hidden mb-2">
            <a-image 
              v-if="currentVerifyForm.idCardBack" 
              :src="currentVerifyForm.idCardBack" 
              alt="身份证反面" 
              class="w-full h-full object-cover"
            />
            <div 
              v-else 
              class="w-full h-full flex flex-col items-center justify-center bg-gray-50"
            >
              <i class="i-carbon-image text-gray-400 text-3xl mb-2"></i>
              <span class="text-sm text-gray-500">点击上传身份证反面照片</span>
            </div>
            <div 
              v-if="isVerified !== 1 && isVerified !== 2" 
              class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity cursor-pointer"
              @click="openIdCardBackUpload"
            >
              <button type="button" class="text-white bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded-md text-sm">
                更换照片
              </button>
            </div>
          </div>
          <div class="text-xs text-gray-500">
            {{ isVerified === 1 || isVerified === 2 ? '身份证国徽面照片' : '请上传清晰的身份证国徽面照片' }}
          </div>
        </a-form-item>
        
        <div class="text-gray-500 text-sm mt-4 bg-gray-50 p-3 rounded">
          <div class="flex items-center">
            <i class="i-carbon-information text-blue-600 mr-2"></i>
            <span>温馨提示：</span>
          </div>
          <ul class="list-disc pl-6 mt-2 space-y-1">
            <li>请确保上传的证件清晰可见，信息准确无误</li>
            <li>我们将严格保护您的个人信息安全</li>
            <li>认证成功后，身份信息将无法修改</li>
          </ul>
        </div>
      </a-form>
    </div>
    <template #footer>
      <a-button @click="handleCancel">关闭</a-button>
      <a-button 
        v-if="isVerified === 0 || isVerified === 3" 
        type="primary" 
        @click="handleSubmit"
      >提交认证</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import userApi from '~/api/mall/user';

// 接收属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isVerified: {
    type: Number,
    default: 0
  },
  verifiedTime: {
    type: String,
    default: ''
  },
  verifyFailReason: {
    type: String,
    default: ''
  },
  verifyForm: {
    type: Object,
    default: () => ({
      realName: '',
      idCardNumber: '',
      authPhone: '',
      bankCardNo: '',
      idCardFront: '',
      idCardBack: ''
    })
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'open-upload', 'cancel']);

// 表单数据
const currentVerifyForm = ref({
  realName: '',
  idCardNumber: '',
  authPhone: '',
  bankCardNo: '',
  idCardFront: '',
  idCardBack: ''
});

// 文件上传相关引用
const idCardFrontInput = ref(null);
const idCardBackInput = ref(null);
const isIdCardFrontUploading = ref(false);
const isIdCardBackUploading = ref(false);

// 监听模态框可见性变化，初始化或重置表单
watch(() => props.visible, (newVal) => {
  console.log(newVal,'newVal');
  if (newVal) {
    // 模态框打开时，初始化表单数据
    initForm();
  } else {
    // 模态框关闭时，重置表单
    resetForm();
  }
});

// 获取实名认证模态框标题
const getVerifyModalTitle = (status) => {
  switch (status) {
    case 0: // 未认证
      return '实名认证';
    case 1: // 审核中
      return '实名认证进度';
    case 2: // 已认证
      return '实名认证信息';
    case 3: // 认证失败
      return '重新认证';
    default:
      return '实名认证';
  }
};

// 隐藏身份证号中间部分
const maskIdCard = (idCard) => {
  if (!idCard || idCard.length < 10) return idCard;
  return idCard.replace(/^(.{4})(.*)(.{4})$/, '$1**********$3');
};

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
  resetForm();
};

// 提交表单
const handleSubmit = () => {
  emit('submit', currentVerifyForm.value);
};

// 初始化表单数据
const initForm = () => {
  currentVerifyForm.value = {
    realName: props.verifyForm.realName || '',
    idCardNumber: props.verifyForm.idCardNumber || '',
    authPhone: props.verifyForm.authPhone || '',
    bankCardNo: props.verifyForm.bankCardNo || '',
    idCardFront: props.verifyForm.idCardFront || '',
    idCardBack: props.verifyForm.idCardBack || ''
  };
};

// 重置表单
const resetForm = () => {
  currentVerifyForm.value = {
    realName: '',
    idCardNumber: '',
    authPhone: '',
    bankCardNo: '',
    idCardFront: '',
    idCardBack: ''
  };
};

// 打开身份证正面照片上传
const openIdCardFrontUpload = () => {
  // 创建一个隐藏的文件输入框
  if (!idCardFrontInput.value) {
    idCardFrontInput.value = document.createElement('input');
    idCardFrontInput.value.type = 'file';
    idCardFrontInput.value.accept = 'image/jpeg,image/png,image/gif,image/webp';
    idCardFrontInput.value.style.display = 'none';
    idCardFrontInput.value.addEventListener('change', handleIdCardFrontChange);
    document.body.appendChild(idCardFrontInput.value);
  }
  idCardFrontInput.value.click();
};

// 打开身份证反面照片上传
const openIdCardBackUpload = () => {
  // 创建一个隐藏的文件输入框
  if (!idCardBackInput.value) {
    idCardBackInput.value = document.createElement('input');
    idCardBackInput.value.type = 'file';
    idCardBackInput.value.accept = 'image/jpeg,image/png,image/gif,image/webp';
    idCardBackInput.value.style.display = 'none';
    idCardBackInput.value.addEventListener('change', handleIdCardBackChange);
    document.body.appendChild(idCardBackInput.value);
  }
  idCardBackInput.value.click();
};

// 处理身份证正面照片变更
const handleIdCardFrontChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // 检查文件类型
  if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
    Message.error('只支持 JPG、PNG、GIF、WEBP 格式的图片');
    return;
  }
  
  // 检查文件大小
  if (file.size > 2 * 1024 * 1024) {
    Message.error('图片大小不能超过 2MB');
    return;
  }
  
  // 上传身份证正面照片
  isIdCardFrontUploading.value = true;
  
  try {
    // 显示上传中提示
    const loadingInstance = Message.loading('正在上传身份证正面照片...');
    
    // 上传文件
    console.log('开始上传身份证正面照片...');
    const response = await userApi.profile.uploadAvatar(file);
    console.log('上传身份证正面照片响应:', response);
    
    // 关闭加载提示
    loadingInstance.close();
    
    // 判断上传是否成功
    if (response.code !== 0 && response.code !== 200) {
      // 上传失败
      console.error('上传身份证正面照片失败，状态码:', response.code, '消息:', response.message);
      Message.error('上传身份证正面照片失败: ' + (response.message || '未知错误'));
      return;
    }
    
    // 获取文件URL
    if (!response.data || !response.data.fileUrl) {
      console.error('响应数据中没有fileUrl字段');
      Message.error('获取上传文件地址失败');
      return;
    }
    
    const fileUrl = response.data.fileUrl;
    console.log('获取到的身份证正面照片URL:', fileUrl);
    
    // 更新身份证正面照片地址
    currentVerifyForm.value.idCardFront = fileUrl;
    
    Message.success('身份证正面照片上传成功');
  } catch (error) {
    console.error('上传身份证正面照片失败:', error);
    Message.error('上传身份证正面照片失败，请稍后再试');
  } finally {
    isIdCardFrontUploading.value = false;
    // 清空文件输入框，以便可以重新选择同一文件
    if (event.target) {
      event.target.value = '';
    }
  }
};

// 处理身份证反面照片变更
const handleIdCardBackChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  // 检查文件类型
  if (!['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)) {
    Message.error('只支持 JPG、PNG、GIF、WEBP 格式的图片');
    return;
  }
  
  // 检查文件大小
  if (file.size > 2 * 1024 * 1024) {
    Message.error('图片大小不能超过 2MB');
    return;
  }
  
  // 上传身份证反面照片
  isIdCardBackUploading.value = true;
  
  try {
    // 显示上传中提示
    const loadingInstance = Message.loading('正在上传身份证反面照片...');
    
    // 上传文件
    console.log('开始上传身份证反面照片...');
    const response = await userApi.profile.uploadAvatar(file);
    console.log('上传身份证反面照片响应:', response);
    
    // 关闭加载提示
    loadingInstance.close();
    
    // 判断上传是否成功
    if (response.code !== 0 && response.code !== 200) {
      // 上传失败
      console.error('上传身份证反面照片失败，状态码:', response.code, '消息:', response.message);
      Message.error('上传身份证反面照片失败: ' + (response.message || '未知错误'));
      return;
    }
    
    // 获取文件URL
    if (!response.data || !response.data.fileUrl) {
      console.error('响应数据中没有fileUrl字段');
      Message.error('获取上传文件地址失败');
      return;
    }
    
    const fileUrl = response.data.fileUrl;
    console.log('获取到的身份证反面照片URL:', fileUrl);
    
    // 更新身份证反面照片地址
    currentVerifyForm.value.idCardBack = fileUrl;
    
    Message.success('身份证反面照片上传成功');
  } catch (error) {
    console.error('上传身份证反面照片失败:', error);
    Message.error('上传身份证反面照片失败，请稍后再试');
  } finally {
    isIdCardBackUploading.value = false;
    // 清空文件输入框，以便可以重新选择同一文件
    if (event.target) {
      event.target.value = '';
    }
  }
};
</script>

<style scoped>
/* 实名认证表单样式 */
.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}
</style>
