<template>
  <!-- 绑定手机模态框 -->
  <a-modal :visible="visible" @update:visible="(val) => emit('update:visible', val)" :title="phone ? '修改手机绑定' : '绑定手机'" @cancel="handleCancel" width="500px" :mask-closable="false">
    <div class="phone-form">
      <a-form :model="phoneForm" layout="vertical">
        <a-form-item field="newPhone" :label="phone ? '新手机号' : '手机号'" :validate-status="formErrors.newPhone ? 'error' : ''" :help="formErrors.newPhone">
          <a-input v-model="phoneForm.newPhone" placeholder="请输入手机号" />
        </a-form-item>
        <a-form-item field="verifyCode" label="验证码" :validate-status="formErrors.verifyCode ? 'error' : ''" :help="formErrors.verifyCode">
          <div class="flex space-x-2">
            <a-input v-model="phoneForm.verifyCode" placeholder="请输入验证码" class="flex-1" />
            <a-button 
              :disabled="codeCountdown > 0" 
              @click="sendCode"
              :loading="isSendingCode"
            >
              {{ codeCountdown > 0 ? `${codeCountdown}秒后重新获取` : '获取验证码' }}
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="isSubmitting">确认</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import userApi from '@/api/mall/user';
import { useMallUserStore } from '@/store/mall/user';

// 接收属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  phone: {
    type: String,
    default: ''
  },
  countdown: {
    type: Number,
    default: 0
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'send-code', 'cancel']);
const codeCountdown = ref(0);

// 用户store
const userStore = useMallUserStore();
// 表单数据
const phoneForm = ref({
  newPhone: '',
  verifyCode: ''
});

// 表单验证状态
const formErrors = ref({
  newPhone: '',
  verifyCode: ''
});

// 加载状态
const isSubmitting = ref(false);
const isSendingCode = ref(false);

// 监听模态框可见性变化，重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm();
  }
});

// 验证表单
const validateForm = () => {
  let isValid = true;
  formErrors.value = {
    newPhone: '',
    verifyCode: ''
  };
  
  // 验证手机号
  if (!phoneForm.value.newPhone) {
    formErrors.value.newPhone = '请输入手机号';
    isValid = false;
  } else if (!/^1[3-9]\d{9}$/.test(phoneForm.value.newPhone)) {
    formErrors.value.newPhone = '手机号格式不正确';
    isValid = false;
  }
  
  // 验证验证码
  if (!phoneForm.value.verifyCode) {
    formErrors.value.verifyCode = '请输入验证码';
    isValid = false;
  } else if (!/^\d{6}$/.test(phoneForm.value.verifyCode)) {
    formErrors.value.verifyCode = '验证码格式不正确';
    isValid = false;
  }
  
  return isValid;
};

// 发送验证码
const sendCode = async () => {
  // 验证手机号
  if (!phoneForm.value.newPhone) {
    formErrors.value.newPhone = '请输入手机号';
    return;
  } else if (!/^1[3-9]\d{9}$/.test(phoneForm.value.newPhone)) {
    formErrors.value.newPhone = '手机号格式不正确';
    return;
  }

  // 如果已经在倒计时中，不重复发送
  if (codeCountdown.value > 0) {
    return;
  }

  try {
    isSendingCode.value = true;

    // 调用发送手机绑定短信验证码API
    const result = await userStore.sendPhoneBindSmsCode(phoneForm.value.newPhone, 'change_phone');

    if (result.success) {
      Message.success(result.message || '验证码已发送，请注意查收');
      // 执行倒计时
      codeCountdown.value = 60;
      const timer = setInterval(() => {
        codeCountdown.value--;
        if (codeCountdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    } else {
      Message.error(result.message || '验证码发送失败，请稍后重试');
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    Message.error('验证码发送失败，请稍后重试');
  } finally {
    isSendingCode.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  if (!validateForm()) {
    Message.error('请检查表单填写是否正确');
    return;
  }
  
  try {
    isSubmitting.value = true;
    
    // 调用绑定/修改手机号API
    const response = await userApi.profile.updatePhone(phoneForm.value.newPhone, phoneForm.value.verifyCode);
    
    if (response && response.code === 200) {
      emit('submit', phoneForm.value);
      emit('update:visible', false);
      resetForm();
    } else {
      Message.error(response?.message || (props.phone ? '手机号修改失败' : '手机号绑定失败'));
    }
  } catch (error) {
    console.error(props.phone ? '修改手机号失败:' : '绑定手机号失败:', error);
    Message.error(props.phone ? '修改手机号失败，请稍后重试' : '绑定手机号失败，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 重置表单
const resetForm = () => {
  phoneForm.value = {
    newPhone: '',
    verifyCode: ''
  };
  
  // 重置错误信息
  formErrors.value = {
    newPhone: '',
    verifyCode: ''
  };
};
</script>

<style scoped>
/* 手机绑定表单样式 */
</style>
