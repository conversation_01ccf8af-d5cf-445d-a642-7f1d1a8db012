<template>
  <!-- 安全问题模态框 -->
  <a-modal :visible="visible" @update:visible="(val) => emit('update:visible', val)" title="设置安全问题" @cancel="handleCancel" width="600px" :mask-closable="false">
    <div class="security-question-form">
      <a-form :model="securityQuestionForm" layout="vertical">
        <div class="text-gray-500 text-sm mb-4 bg-gray-50 p-3 rounded">
          <div class="flex items-center">
            <i class="i-carbon-information text-blue-600 mr-2"></i>
            <span class="font-medium">安全问题说明</span>
          </div>
          <p class="mt-1 ml-7">设置安全问题可以帮助您在忘记密码时进行身份验证，请认真设置并牢记答案。</p>
        </div>
        
        <a-form-item field="question1" label="安全问题1" :validate-status="formErrors.question1 ? 'error' : ''" :help="formErrors.question1">
          <a-select v-model="securityQuestionForm.question1" placeholder="请选择安全问题">
            <a-option value="您的出生地是？">您的出生地是？</a-option>
            <a-option value="您母亲的姓名是？">您母亲的姓名是？</a-option>
            <a-option value="您父亲的姓名是？">您父亲的姓名是？</a-option>
            <a-option value="您的小学校名是？">您的小学校名是？</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="answer1" label="答案1" :validate-status="formErrors.answer1 ? 'error' : ''" :help="formErrors.answer1">
          <a-input v-model="securityQuestionForm.answer1" placeholder="请输入答案" />
        </a-form-item>
        
        <a-form-item field="question2" label="安全问题2" :validate-status="formErrors.question2 ? 'error' : ''" :help="formErrors.question2">
          <a-select v-model="securityQuestionForm.question2" placeholder="请选择安全问题">
            <a-option value="您最喜欢的颜色是？">您最喜欢的颜色是？</a-option>
            <a-option value="您最喜欢的食物是？">您最喜欢的食物是？</a-option>
            <a-option value="您最喜欢的电影是？">您最喜欢的电影是？</a-option>
            <a-option value="您的宠物名字是？">您的宠物名字是？</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="answer2" label="答案2" :validate-status="formErrors.answer2 ? 'error' : ''" :help="formErrors.answer2">
          <a-input v-model="securityQuestionForm.answer2" placeholder="请输入答案" />
        </a-form-item>
        
        <a-form-item field="question3" label="安全问题3" :validate-status="formErrors.question3 ? 'error' : ''" :help="formErrors.question3">
          <a-select v-model="securityQuestionForm.question3" placeholder="请选择安全问题">
            <a-option value="您的理想职业是？">您的理想职业是？</a-option>
            <a-option value="您的第一辆车是什么品牌？">您的第一辆车是什么品牌？</a-option>
            <a-option value="您最喜欢的歌手是？">您最喜欢的歌手是？</a-option>
            <a-option value="您的初中班主任姓名是？">您的初中班主任姓名是？</a-option>
          </a-select>
        </a-form-item>
        <a-form-item field="answer3" label="答案3" :validate-status="formErrors.answer3 ? 'error' : ''" :help="formErrors.answer3">
          <a-input v-model="securityQuestionForm.answer3" placeholder="请输入答案" />
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="isSubmitting">确认设置</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import userApi from '@/api/mall/user';

// 接收属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  questions: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'cancel']);

// 表单数据
const securityQuestionForm = ref({
  question1: '',
  answer1: '',
  question2: '',
  answer2: '',
  question3: '',
  answer3: ''
});

// 表单验证状态
const formErrors = ref({
  question1: '',
  answer1: '',
  question2: '',
  answer2: '',
  question3: '',
  answer3: ''
});

// 加载状态
const isSubmitting = ref(false);

// 监听模态框可见性变化，填充或重置表单
watch(() => props.visible, (newVal) => {
  if (newVal && props.questions.length > 0) {
    // 按照问题序号排序
    const sortedQuestions = [...props.questions].sort((a, b) => a.question_order - b.question_order);
    
    // 填充表单
    if (sortedQuestions.length >= 1) {
      securityQuestionForm.value.question1 = sortedQuestions[0].question_text;
      securityQuestionForm.value.answer1 = '';
    }
    
    if (sortedQuestions.length >= 2) {
      securityQuestionForm.value.question2 = sortedQuestions[1].question_text;
      securityQuestionForm.value.answer2 = '';
    }
    
    if (sortedQuestions.length >= 3) {
      securityQuestionForm.value.question3 = sortedQuestions[2].question_text;
      securityQuestionForm.value.answer3 = '';
    }
  } else if (!newVal) {
    resetForm();
  }
});

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
  resetForm();
};

// 验证表单
const validateForm = () => {
  let isValid = true;
  formErrors.value = {
    question1: '',
    answer1: '',
    question2: '',
    answer2: '',
    question3: '',
    answer3: ''
  };
  
  // 验证问题1和答案1
  if (!securityQuestionForm.value.question1) {
    formErrors.value.question1 = '请选择安全问题1';
    isValid = false;
  }
  if (!securityQuestionForm.value.answer1) {
    formErrors.value.answer1 = '请输入答案1';
    isValid = false;
  }
  
  // 验证问题2和答案2
  if (!securityQuestionForm.value.question2) {
    formErrors.value.question2 = '请选择安全问题2';
    isValid = false;
  }
  if (!securityQuestionForm.value.answer2) {
    formErrors.value.answer2 = '请输入答案2';
    isValid = false;
  }
  
  // 验证问题3和答案3
  if (!securityQuestionForm.value.question3) {
    formErrors.value.question3 = '请选择安全问题3';
    isValid = false;
  }
  if (!securityQuestionForm.value.answer3) {
    formErrors.value.answer3 = '请输入答案3';
    isValid = false;
  }
  
  // 检查问题是否重复
  if (isValid) {
    const questions = [
      securityQuestionForm.value.question1,
      securityQuestionForm.value.question2,
      securityQuestionForm.value.question3
    ];
    
    const uniqueQuestions = new Set(questions);
    if (uniqueQuestions.size !== questions.length) {
      Message.error('安全问题不能重复，请选择不同的问题');
      isValid = false;
    }
  }
  
  return isValid;
};

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  if (!validateForm()) {
    Message.error('请检查表单填写是否正确');
    return;
  }
  
  try {
    isSubmitting.value = true;
    
    // 构建安全问题数据
    const securityQuestions = [
      {
        question_order: 1,
        question_text: securityQuestionForm.value.question1,
        answer: securityQuestionForm.value.answer1
      },
      {
        question_order: 2,
        question_text: securityQuestionForm.value.question2,
        answer: securityQuestionForm.value.answer2
      },
      {
        question_order: 3,
        question_text: securityQuestionForm.value.question3,
        answer: securityQuestionForm.value.answer3
      }
    ];
    
    // 调用保存安全问题API
    const response = await userApi.profile.saveSecurityQuestions(securityQuestions);
    
    if (response && response.code === 200) {
      Message.success('安全问题设置成功');
      emit('submit', securityQuestions);
      emit('update:visible', false);
      resetForm();
    } else {
      Message.error(response?.message || '安全问题设置失败');
    }
  } catch (error) {
    console.error('设置安全问题失败:', error);
    Message.error('安全问题设置失败，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 重置表单
const resetForm = () => {
  securityQuestionForm.value = {
    question1: '',
    answer1: '',
    question2: '',
    answer2: '',
    question3: '',
    answer3: ''
  };
  
  // 重置错误信息
  formErrors.value = {
    question1: '',
    answer1: '',
    question2: '',
    answer2: '',
    question3: '',
    answer3: ''
  };
};
</script>

<style scoped>
/* 安全问题表单样式 */
</style>
