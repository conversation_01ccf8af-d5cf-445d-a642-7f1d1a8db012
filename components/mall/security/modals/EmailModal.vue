<template>
  <!-- 绑定邮箱模态框 -->
  <a-modal :visible="visible" @update:visible="(val) => emit('update:visible', val)" :title="email ? '修改邮箱绑定' : '绑定邮箱'" @cancel="handleCancel" width="500px" :mask-closable="false">
    <div class="email-form">
      <a-form :model="emailForm" layout="vertical">
        <a-form-item field="newEmail" :label="email ? '新邮箱' : '邮箱'" :validate-status="formErrors.newEmail ? 'error' : ''" :help="formErrors.newEmail">
          <a-input v-model="emailForm.newEmail" placeholder="请输入邮箱" />
        </a-form-item>
        <a-form-item field="verifyCode" label="验证码" :validate-status="formErrors.verifyCode ? 'error' : ''" :help="formErrors.verifyCode">
          <div class="flex space-x-2">
            <a-input v-model="emailForm.verifyCode" placeholder="请输入验证码" class="flex-1" />
            <a-button 
              :disabled="codeCountdown > 0" 
              @click="sendCode"
              :loading="isSendingCode"
            >
              {{ codeCountdown > 0 ? `${codeCountdown}秒后重新获取` : '获取验证码' }}
            </a-button>
          </div>
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="isSubmitting">确认</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import userApi from '@/api/mall/user';

// 接收属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  email: {
    type: String,
    default: ''
  },
  countdown: {
    type: Number,
    default: 0
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'send-code', 'cancel']);
const codeCountdown = ref(0);
// 表单数据
const emailForm = ref({
  newEmail: '',
  verifyCode: ''
});

// 表单验证状态
const formErrors = ref({
  newEmail: '',
  verifyCode: ''
});

// 加载状态
const isSubmitting = ref(false);
const isSendingCode = ref(false);

// 监听模态框可见性变化，重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm();
  }
});

// 验证表单
const validateForm = () => {
  let isValid = true;
  formErrors.value = {
    newEmail: '',
    verifyCode: ''
  };
  
  // 验证邮箱
  if (!emailForm.value.newEmail) {
    formErrors.value.newEmail = '请输入邮箱';
    isValid = false;
  } else if (!/^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/.test(emailForm.value.newEmail)) {
    formErrors.value.newEmail = '邮箱格式不正确';
    isValid = false;
  }
  
  // 验证验证码
  if (!emailForm.value.verifyCode) {
    formErrors.value.verifyCode = '请输入验证码';
    isValid = false;
  } else if (!/^\d{6}$/.test(emailForm.value.verifyCode)) {
    formErrors.value.verifyCode = '验证码格式不正确';
    isValid = false;
  }
  
  return isValid;
};

// 发送验证码
const sendCode = async () => {
  // 验证邮箱
  if (!emailForm.value.newEmail) {
    formErrors.value.newEmail = '请输入邮箱';
    return;
  } else if (!/^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,}$/.test(emailForm.value.newEmail)) {
    formErrors.value.newEmail = '邮箱格式不正确';
    return;
  }
  
  // 如果已经在倒计时中，不重复发送
  if (codeCountdown.value > 0) {
    return;
  }
  
  try {
    isSendingCode.value = true;
    
    // 调用发送验证码API
    const response = await userApi.profile.getEmailCaptcha(emailForm.value.newEmail);
    
    if (response && response.code === 200) {
      Message.success('验证码已发送到邮箱，请注意查收');
      // 执行倒计时
      codeCountdown.value = 60;
      const timer = setInterval(() => {
        codeCountdown.value--;
        if (codeCountdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    } else {
      Message.error(response?.message || '验证码发送失败，请稍后重试');
    }
  } catch (error) {
    console.error('发送邮箱验证码失败:', error);
    Message.error('验证码发送失败，请稍后重试');
  } finally {
    isSendingCode.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  if (!validateForm()) {
    Message.error('请检查表单填写是否正确');
    return;
  }
  
  try {
    isSubmitting.value = true;
    
    // 调用绑定/修改邮箱API
    const response = await userApi.profile.bindEmail(emailForm.value.newEmail, emailForm.value.verifyCode);
    
    if (response && response.code === 200) {
      Message.success(props.email ? '邮箱修改成功' : '邮箱绑定成功');
      emit('submit', emailForm.value);
      emit('update:visible', false);
      resetForm();
    } else {
      Message.error(response?.message || (props.email ? '邮箱修改失败' : '邮箱绑定失败'));
    }
  } catch (error) {
    console.error(props.email ? '修改邮箱失败:' : '绑定邮箱失败:', error);
    Message.error(props.email ? '修改邮箱失败，请稍后重试' : '绑定邮箱失败，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 重置表单
const resetForm = () => {
  emailForm.value = {
    newEmail: '',
    verifyCode: ''
  };
  
  // 重置错误信息
  formErrors.value = {
    newEmail: '',
    verifyCode: ''
  };
};
</script>

<style scoped>
/* 邮箱绑定表单样式 */
</style>
