<template>
  <!-- 修改密码模态框 -->
  <a-modal :visible="visible" @update:visible="(val) => emit('update:visible', val)" title="修改登录密码" @cancel="handleCancel" width="500px" :mask-closable="false">
    <div class="password-form">
      <a-form :model="passwordForm" layout="vertical">
        <a-form-item field="oldPassword" label="原密码" :validate-status="formErrors.oldPassword ? 'error' : ''" :help="formErrors.oldPassword">
          <a-input v-model="passwordForm.oldPassword" placeholder="请输入原密码" type="password" />
        </a-form-item>
        <a-form-item field="newPassword" label="新密码" :validate-status="formErrors.newPassword ? 'error' : ''" :help="formErrors.newPassword">
          <a-input v-model="passwordForm.newPassword" placeholder="请输入新密码" type="password" />
        </a-form-item>
        <a-form-item field="confirmPassword" label="确认新密码" :validate-status="formErrors.confirmPassword ? 'error' : ''" :help="formErrors.confirmPassword">
          <a-input v-model="passwordForm.confirmPassword" placeholder="请再次输入新密码" type="password" />
        </a-form-item>
        <a-form-item field="verifyCode" label="手机验证码" :validate-status="formErrors.verifyCode ? 'error' : ''" :help="formErrors.verifyCode">
          <div class="flex space-x-2">
            <a-input v-model="passwordForm.verifyCode" placeholder="请输入验证码" class="flex-1" />
            <a-button 
              :disabled="codeCountdown > 0" 
              @click="sendCode"
              :loading="isSendingCode"
            >
              {{ codeCountdown > 0 ? `${codeCountdown}秒后重新获取` : '获取验证码' }}
            </a-button>
          </div>
          <div class="text-xs text-gray-500 mt-1">验证码将发送至 {{ maskPhone(phone) }}</div>
        </a-form-item>
      </a-form>
    </div>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleSubmit" :loading="isSubmitting">确认修改</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';
import { Message } from '@arco-design/web-vue';
import userApi from '@/api/mall/user';
import { useMallUserStore } from '@/store/mall/user';

const userStore = useMallUserStore();

// 接收属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  phone: {
    type: String,
    default: ''
  },
  countdown: {
    type: Number,
    default: 0
  }
});

// 定义事件
const emit = defineEmits(['update:visible', 'submit', 'send-code', 'cancel']);
const codeCountdown = ref(0);
// 表单数据
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
  verifyCode: ''
});

// 表单验证状态
const formErrors = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
  verifyCode: ''
});

// 加载状态
const isSubmitting = ref(false);
const isSendingCode = ref(false);

// 监听模态框可见性变化，重置表单
watch(() => props.visible, (newVal) => {
  if (!newVal) {
    resetForm();
  }
});

// 验证表单
const validateForm = () => {
  let isValid = true;
  formErrors.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    verifyCode: ''
  };
  
  // 验证原密码
  if (!passwordForm.value.oldPassword) {
    formErrors.value.oldPassword = '请输入原密码';
    isValid = false;
  }
  
  // 验证新密码
  if (!passwordForm.value.newPassword) {
    formErrors.value.newPassword = '请输入新密码';
    isValid = false;
  } else if (passwordForm.value.newPassword.length < 6 || passwordForm.value.newPassword.length > 20) {
    formErrors.value.newPassword = '密码长度必须为6-20位';
    isValid = false;
  }
  
  // 验证确认密码
  if (!passwordForm.value.confirmPassword) {
    formErrors.value.confirmPassword = '请确认新密码';
    isValid = false;
  } else if (passwordForm.value.confirmPassword !== passwordForm.value.newPassword) {
    formErrors.value.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  }
  
  // 验证验证码
  if (!passwordForm.value.verifyCode) {
    formErrors.value.verifyCode = '请输入验证码';
    isValid = false;
  } else if (!/^\d{6}$/.test(passwordForm.value.verifyCode)) {
    formErrors.value.verifyCode = '验证码格式不正确';
    isValid = false;
  }
  
  return isValid;
};

// 发送验证码
const sendCode = async () => {
  // 如果没有绑定手机号，提示先绑定手机号
  if (!props.phone) {
    Message.warning('请先绑定手机号');
    return;
  }
  
  // 如果已经在倒计时中，不重复发送
  if (codeCountdown.value > 0) {
    return;
  }
  
  try {
    isSendingCode.value = true;
    
    // 调用发送验证码API
    const response = await userApi.profile.getPhoneCaptcha(props.phone);
    
    if (response && response.code === 200) {
      Message.success('验证码已发送，请注意查收');
      // 执行倒计时
      codeCountdown.value = 60;
      const timer = setInterval(() => {
        codeCountdown.value--;
        if (codeCountdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1000);
    } else {
      Message.error(response?.message || '验证码发送失败，请稍后重试');
    }
  } catch (error) {
    console.error('发送验证码失败:', error);
    Message.error('验证码发送失败，请稍后重试');
  } finally {
    isSendingCode.value = false;
  }
};


// 隐藏手机号中间四位
const maskPhone = (phone) => {
  if (!phone || phone.length < 5) return phone;
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// 取消操作
const handleCancel = () => {
  emit('update:visible', false);
  emit('cancel');
  resetForm();
};

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  if (!validateForm()) {
    return;
  }
  
  try {
    isSubmitting.value = true;
    
    // 调用修改密码API
    const response = await userApi.profile.changePassword({
      oldPassword: passwordForm.value.oldPassword,
      newPassword: passwordForm.value.newPassword,
      verifyCode: passwordForm.value.verifyCode
    });
    
    if (response && response.code === 200) {
      Message.success('密码修改成功');
      emit('submit', passwordForm.value);
      emit('update:visible', false);
      resetForm();
      userStore.logout();
      navigateTo('/mall/login');
    } else {
      Message.error(response?.message || '密码修改失败，请重试');
    }
  } catch (error) {
    console.error('修改密码失败:', error);
    Message.error('修改密码失败，请稍后重试');
  } finally {
    isSubmitting.value = false;
  }
};

// 重置表单
const resetForm = () => {
  passwordForm.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    verifyCode: ''
  };
  
  // 重置错误信息
  formErrors.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    verifyCode: ''
  };
};
</script>

<style scoped>
/* 密码表单样式 */
</style>

