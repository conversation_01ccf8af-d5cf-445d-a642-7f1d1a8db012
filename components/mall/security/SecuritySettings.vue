<template>
  <!-- 安全设置列表组件 -->
  <div class="security-settings bg-white p-6 rounded-lg shadow-sm border border-gray-100">
    <!-- 登录密码 -->
    <div class="security-item border-b py-4 flex items-center justify-between">
      <div>
        <div class="flex items-center">
          <i class="i-carbon-password text-xl text-blue-600 mr-2"></i>
          <span class="font-medium">登录密码</span>
        </div>
        <p class="text-gray-500 text-sm mt-1">
          {{ userInfo.hasPassword ? '已设置，建议定期修改密码以保障账户安全' : '未设置，请尽快设置密码' }}
        </p>
      </div>
      <a-button type="primary" @click="$emit('show-modal', 'password')">
        {{ userInfo.hasPassword ? '修改' : '设置' }}
      </a-button>
    </div>
    
    <!-- 手机绑定 -->
    <div class="security-item border-b py-4 flex items-center justify-between">
      <div>
        <div class="flex items-center">
          <i class="i-carbon-phone text-xl text-blue-600 mr-2"></i>
          <span class="font-medium">手机绑定</span>
        </div>
        <p class="text-gray-500 text-sm mt-1">
          {{ userInfo.phone ? `已绑定手机号：${maskPhone(userInfo.phone)}` : '未绑定，绑定手机可用于登录和找回密码' }}
        </p>
      </div>
      <a-button type="primary" @click="$emit('show-modal', 'phone')">
        {{ userInfo.phone ? '修改' : '绑定' }}
      </a-button>
    </div>
    
    <!-- 邮箱绑定 -->
    <div class="security-item border-b py-4 flex items-center justify-between">
      <div>
        <div class="flex items-center">
          <i class="i-carbon-email text-xl text-blue-600 mr-2"></i>
          <span class="font-medium">邮箱绑定</span>
        </div>
        <p class="text-gray-500 text-sm mt-1">
          {{ userInfo.email ? `已绑定邮箱：${maskEmail(userInfo.email)}` : '未绑定，绑定邮箱可用于接收重要通知' }}
        </p>
      </div>
      <a-button type="primary" @click="$emit('show-modal', 'email')">
        {{ userInfo.email ? '修改' : '绑定' }}
      </a-button>
    </div>
    
    <!-- 微信绑定 -->
    <div class="security-item border-b py-4 flex items-center justify-between">
      <div>
        <div class="flex items-center">
          <i class="i-carbon-logo-wechat text-xl text-green-600 mr-2"></i>
          <span class="font-medium">微信绑定</span>
        </div>
        <p class="text-gray-500 text-sm mt-1">
          {{ userInfo.wechatOpenid ? '已绑定微信账号，可使用微信扫码快速登录' : '未绑定，绑定后可使用微信扫码快速登录' }}
        </p>
      </div>
      <a-button type="primary" @click="$emit('show-modal', 'wechat')">
        {{ userInfo.wechatOpenid ? '重新绑定' : '立即绑定' }}
      </a-button>
    </div>
    
    <!-- 实名认证 -->
    <div class="security-item border-b py-4 flex items-center justify-between">
      <div>
        <div class="flex items-center">
          <i class="i-carbon-id-management text-xl text-blue-600 mr-2"></i>
          <span class="font-medium">实名认证</span>
        </div>
        <p class="text-gray-500 text-sm mt-1">
          <template v-if="userInfo.isVerified === 0">
            未认证，实名认证可提升账户安全等级
          </template>
          <template v-else-if="userInfo.isVerified === 1">
            <span class="text-blue-600">认证审核中，请耐心等待</span>
          </template>
          <template v-else-if="userInfo.isVerified === 2">
            <span class="text-green-600">已通过实名认证</span>
          </template>
          <template v-else-if="userInfo.isVerified === 3">
            <span class="text-red-600">实名认证失败，原因：{{ userInfo.verifyFailReason || '认证信息不符合要求' }}</span>
          </template>
        </p>
      </div>
      <a-button 
        @click="$emit('show-modal', 'verify')" 
        :type="getVerifyButtonType(userInfo.isVerified)" 
        :status="getVerifyButtonStatus(userInfo.isVerified)"
      >
        <template v-if="userInfo.isVerified === 0">立即认证</template>
        <template v-else-if="userInfo.isVerified === 1">查看进度</template>
        <template v-else-if="userInfo.isVerified === 2">查看</template>
        <template v-else-if="userInfo.isVerified === 3">重新认证</template>
      </a-button>
    </div>
    
    <!-- 安全问题 -->
    <div class="security-item py-4 flex items-center justify-between">
      <div>
        <div class="flex items-center">
          <i class="i-carbon-help text-xl text-blue-600 mr-2"></i>
          <span class="font-medium">安全问题</span>
        </div>
        <p class="text-gray-500 text-sm mt-1">
          {{ userInfo.hasSecurityQuestion ? '已设置，可用于身份验证和找回密码' : '未设置，设置安全问题可提高账户安全性' }}
        </p>
      </div>
      <a-button type="primary" @click="$emit('show-modal', 'securityQuestion')">
        {{ userInfo.hasSecurityQuestion ? '修改' : '设置' }}
      </a-button>
    </div>
  </div>
</template>

<script setup>
// 接收属性
const props = defineProps({
  userInfo: {
    type: Object,
    required: true
  }
});

// 定义事件
defineEmits(['show-modal']);

// 隐藏手机号中间四位
const maskPhone = (phone) => {
  if (!phone || phone.length < 7) return phone;
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// 隐藏邮箱用户名部分
const maskEmail = (email) => {
  if (!email || !email.includes('@')) return email;
  const [username, domain] = email.split('@');
  if (username.length <= 2) {
    return `${username}***@${domain}`;
  } else {
    return `${username.substring(0, 2)}***@${domain}`;
  }
};

// 获取实名认证按钮类型
const getVerifyButtonType = (status) => {
  switch (status) {
    case 0: // 未认证
      return 'primary';
    case 1: // 审核中
      return 'outline';
    case 2: // 已认证
      return 'outline';
    case 3: // 认证失败
      return 'primary';
    default:
      return 'primary';
  }
};

// 获取实名认证按钮状态
const getVerifyButtonStatus = (status) => {
  switch (status) {
    case 0: // 未认证
      return 'normal';
    case 1: // 审核中
      return 'warning';
    case 2: // 已认证
      return 'success';
    case 3: // 认证失败
      return 'danger';
    default:
      return 'normal';
  }
};
</script>

<style scoped>
/* 安全设置项样式 */
.security-item {
  transition: all 0.3s ease;
}

.security-item:hover {
  background-color: #f9fafb;
}

.security-item:last-child {
  border-bottom: none;
}
</style>
