<template>
  <!-- 安全评分组件 -->
  <div class="security-score bg-white p-6 rounded-lg mb-6 shadow-sm border border-gray-100">
    <div class="flex items-center">
      <div class="w-1/4">
        <div class="score-circle-container mx-auto">
          <svg width="120" height="120" viewBox="0 0 120 120">
            <!-- 背景圆环 -->
            <circle 
              cx="60" 
              cy="60" 
              r="54" 
              fill="none" 
              stroke="#f1f5f9" 
              stroke-width="12"
            />
            <!-- 进度圆环 -->
            <circle 
              cx="60" 
              cy="60" 
              r="54" 
              fill="none" 
              stroke="#3b82f6" 
              stroke-width="12"
              stroke-dasharray="339.3"
              :stroke-dashoffset="339.3 * (1 - score/100)"
              transform="rotate(-90 60 60)"
            />
          </svg>
          <!-- 中间的数字 -->
          <div class="score-text">
            <span class="score-value">{{ score }}</span>
          </div>
        </div>
        <div class="text-center mt-2">
          <div class="font-medium">安全评分</div>
        </div>
      </div>
      <div class="w-3/4 pl-6">
        <div class="text-lg font-medium mb-2">
          {{ securityLevelText }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 接收属性
const props = defineProps({
  score: {
    type: Number,
    required: true
  }
});

// 计算安全等级文本
const securityLevelText = computed(() => {
  const score = props.score;
  if (score >= 90) {
    return '您的账户安全状况非常好，请继续保持良好的安全习惯！';
  } else if (score >= 70) {
    return '您的账户安全状况良好，建议完善剩余安全设置以提高账户安全性。';
  } else if (score >= 50) {
    return '您的账户安全状况一般，建议尽快完善安全设置。';
  } else {
    return '您的账户安全风险较高，请尽快完善安全设置！';
  }
});
</script>

<style scoped>
/* 安全评分圆环样式 */
.score-circle-container {
  position: relative;
  width: 120px;
  height: 120px;
}

.score-text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-value {
  font-size: 28px;
  font-weight: bold;
  color: #3b82f6;
}
</style>
