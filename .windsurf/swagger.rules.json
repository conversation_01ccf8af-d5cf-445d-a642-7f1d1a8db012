{"apiRules": {"baseRules": "../../server/docs/swagger.md", "endpoints": {"naming": {"pattern": "kebab-case", "prefix": "/api/v1"}}, "documentation": {"required": ["summary", "description", "parameters", "requestBody", "responses"], "tags": {"required": true, "format": "模块名称-子模块"}}, "responses": {"standard": {"success": {"code": 200, "structure": {"code": "number", "data": "any", "message": "string"}}, "error": {"code": 400, "structure": {"code": "number", "message": "string"}}}}}}