{"schemaRules": {"baseRules": "../../server/docs/database.md", "modelFiles": {"location": "server/apps/master/prisma/models", "naming": {"pattern": "snake_case", "matchTable": true, "description": "文件名必须与数据库表名完全一致（不包括 schema 名称）"}}, "fieldRules": {"naming": "snake_case", "required": [{"name": "id", "type": "BigInt", "attributes": ["@id"], "description": "主键，16位雪花算法"}, {"name": "created_at", "type": "BigInt", "description": "创建时间戳（毫秒）"}, {"name": "updated_at", "type": "BigInt", "description": "更新时间戳（毫秒）"}, {"name": "deleted_at", "type": "BigInt?", "description": "删除时间戳（毫秒）"}], "order": ["主键字段", "基本信息字段", "状态相关字段", "关联字段", "审计字段"]}, "commentRules": {"model": {"format": "/// 表的中文名称，用途说明", "required": true}, "field": {"format": "/// @db.Comment('字段说明')", "required": true}}}}