# Windsurf 规范配置

本目录包含 Windsurf/Cascade AI 助手的规范配置文件。所有配置都基于 `server/docs` 目录下的标准规范文档。

## 文件说明

### index.json
规范配置索引文件，定义了所有规范文件的位置和对应的源文档。

### *.rules.json
各个领域的具体规范配置：

- `prisma.rules.json`: 数据库设计规范
  - 基于 [server/docs/database.md](../server/docs/database.md)
  - 定义了 Schema 文件组织、命名规则等

- `architecture.rules.json`: 架构设计规范
  - 基于 [server/docs/architecture.md](../server/docs/architecture.md)
  - 定义了项目结构、代码风格等

- `swagger.rules.json`: API 设计规范
  - 基于 [server/docs/swagger.md](../server/docs/swagger.md)
  - 定义了 API 文档格式、响应结构等

- `unit-test.rules.json`: 单元测试规范
  - 基于 [server/docs/unit-test.md](../server/docs/unit-test.md)
  - 定义了测试文件组织、覆盖率要求等

## 使用方法
这些规则文件会被 Windsurf/Cascade 自动加载，确保 AI 助手在处理相关任务时严格遵循项目规范。

## 规则更新流程
1. 首先更新 `server/docs` 下的标准规范文档
2. 然后更新对应的 `.rules.json` 文件
3. 提交 PR 进行审核
4. 合并后，所有团队成员的 Windsurf/Cascade 将自动应用新规则

## 注意事项
- 规则文件使用 JSON 格式，便于程序解析
- 所有规则都应该有明确的源文档引用
- 规则更新需要同时更新源文档和规则文件
