const providerMenuMock = {
  user: {
    id: 1,
    username: "provider",
    user_type: "200",
    nickname: "服务商",
    phone: "16858888999",
    email: "<EMAIL>",
    avatar: null,
    signed: "提供优质服务",
    dashboard: "provider-workbench", // 设置默认仪表盘
    status: 1,
    login_ip: "**************",
    login_time: "2025-05-29 17:04:37",
    backend_setting: null,
    created_by: 0,
    updated_by: 0,
    created_at: "2025-03-14 07:30:04",
    updated_at: "2025-05-29 17:04:37",
    remark: null,
  },
  routers: [
    // {
    //   "id": "4900",
    //   "parent_id": "0",
    //   "level": "0",
    //   "name": "公司信息管理",
    //   "code": "provider-information",
    //   "icon": "ma-icon-info",
    //   "route": "/provider/information",
    //   "component": "/provider/information/index",
    //   "redirect": null,
    //   "is_hidden": 0,
    //   "type": "M",
    //   "status": 1,
    //   "sort": 1,
    //   "created_by": "174097662365274112",
    //   "updated_by": "174097662365274112",
    //   "created_at": "1747122281040",
    //   "updated_at": "1747219376579",
    //   "deleted_at": null,
    //   "remark": null
    // },
    {
      "id": "5000",
      "parent_id": "0",
      "level": "0",
      "name": "工作台",
      "code": "provider-workbench",
      "icon": "ma-icon-dashboard",
      "route": "/provider/workbench",
      "component": "/provider/workbench/index",
      "redirect": null,
      "is_hidden": 0,
      "type": "M",
      "status": 1,
      "sort": 2,
      "created_by": "174097662365274112",
      "updated_by": "174097662365274112",
      "created_at": "1747122281040",
      "updated_at": "1747219376579",
      "deleted_at": null,
      "remark": null,
      "badge": 11
    },
    {
      "id": "5100",
      "parent_id": "0",
      "level": "0",
      "name": "竞价管理",
      "code": "provider-bid",
      "icon": "ma-icon-bid",
      "route": "/provider/bid",
      "component": "/provider/bid/index",
      "redirect": null,
      "is_hidden": 0,
      "type": "M",
      "status": 1,
      "sort": 3,
      "created_by": "174097662365274112",
      "updated_by": "174097662365274112",
      "created_at": "1747122281040",
      "updated_at": "1747219376579",
      "deleted_at": null,
      "remark": null,
      "badge": 1,
      "children": [
        {
          "id": "5101",
          "parent_id": "5100",
          "level": "1",
          "name": "竞价项目列表",
          "code": "provider-bid-bidList",
          "icon": "ma-icon-list",
          "route": "/provider/bid/bidList",
          "component": "/provider/bid/bidList/index",
          "redirect": null,
          "is_hidden": 0,
          "type": "M",
          "status": 1,
          "sort": 1,
          "created_by": "174097662365274112",
          "updated_by": "174097662365274112",
          "created_at": "1747122281040",
          "updated_at": "1747219376579",
          "deleted_at": null,
          "remark": null
        },
      ]
    },
  
    {
      "id": "5300",
      "parent_id": "0",
      "level": "0",
      "name": "商品管理",
      "code": "provider-goods",
      "icon": "ma-icon-goods",
      "route": "/goods",
      "component": null,
      "redirect": null,
      "is_hidden": 0,
      "type": "M",
      "status": 1,
      "sort": 5,
      "created_by": "174097662365274112",
      "updated_by": "174097662365274112",
      "created_at": "1747122281040",
      "updated_at": "1747219376579",
      "deleted_at": null,
      "remark": null,
      "badge": 6,
      "children": [
        {
          "id": "5302",
          "parent_id": "5300",
          "level": "1",
          "name": "平台商品库",
          "code": "provider-platformGoods",
          "icon": "ma-icon-goods-pool",
          "route": "/provider/goods/platformGoods",
          "component": "/provider/goods/platformGoods/index",
          "redirect": null,
          "is_hidden": 0,
          "type": "M",
          "status": 1,
          "sort": 1,
          "created_by": "174097662365274112",
          "updated_by": "174097662365274112",
          "created_at": "1746524096868",
          "updated_at": "1746588972652",
          "deleted_at": null,
          "remark": null
        },
        // {
        //   "id": "5301",
        //   "parent_id": "5300",
        //   "level": "1",
        //   "name": "商品列表",
        //   "code": "provider-goods-goodsList",
        //   "icon": "ma-icon-list",
        //   "route": "/provider/goods/goodsList",
        //   "component": "/provider/goods/goodsList/index",
        //   "redirect": null,
        //   "is_hidden": 0,
        //   "type": "M",
        //   "status": 1,
        //   "sort": 2,
        //   "created_by": "171196850903322624",
        //   "updated_by": "174097662365274112",
        //   "created_at": "1745488600668",
        //   "updated_at": "1746606457336",
        //   "deleted_at": null,
        //   "remark": null
        // },
        {
          "id": "5303",
          "parent_id": "5300",
          "level": "1",
          "name": "品牌授权管理",
          "code": "provider-brandManage",
          "icon": "ma-icon-brand",
          "route": "/provider/goods/brandManage",
          "component": "/provider/goods/brandManage/index",
          "redirect": null,
          "is_hidden": 0,
          "type": "M",
          "status": 1,
          "sort": 1,
          "created_by": "174097662365274112",
          "updated_by": "174097662365274112",
          "created_at": "1746524096868",
          "updated_at": "1746588972652",
          "deleted_at": null,
          "remark": null
        },
        // {
        //   "id": "5305",
        //   "parent_id": "5300",
        //   "level": "1",
        //   "name": "品牌授权管理",
        //   "code": "provider-brandAuth",
        //   "icon": "ma-icon-brand",
        //   "route": "/provider/goods/brandAuth",
        //   "component": "/provider/goods/brandAuth/index",
        //   "redirect": null,
        //   "is_hidden": 0,
        //   "type": "M",
        //   "status": 1,
        //   "sort": 1,
        //   "created_by": "174097662365274112",
        //   "updated_by": "174097662365274112",
        //   "created_at": "1746524096868",
        //   "updated_at": "1746588972652",
        //   "deleted_at": null,
        //   "remark": null
        // },
        {
          "id": "5304",
          "parent_id": "5300",
          "level": "1",
          "name": "产品线管理",
          "code": "provider-productLine",
          "icon": "ma-icon-brand",
          "route": "/provider/goods/productLine",
          "component": "/provider/goods/productLine/index",
          "redirect": null,
          "is_hidden": 0,
          "type": "M",
          "status": 1,
          "sort": 1,
          "created_by": "174097662365274112",
          "updated_by": "174097662365274112",
          "created_at": "1746524096868",
          "updated_at": "1746588972652",
          "deleted_at": null,
          "remark": null
        },
     
   
        {
          "id": "34",
          "parent_id": "23",
          "level": "0,23",
          "name": "添加商品",
          "code": "provider-addGoods",
          "icon": null,
          "route": "/provider/goods/addGoods/:id",
          "component": "",
          "redirect": null,
          "is_hidden": 1,
          "type": "M",
          "status": 1,
          "sort": 1,
          "created_by": "171196850903322624",
          "updated_by": "174097662365274112",
          "created_at": "1745488600668",
          "updated_at": "1746606457336",
          "deleted_at": null,
          "remark": null
      },
      {
        "id": "50",
        "parent_id": "23",
        "level": "0,23",
        "name": "商品详情",
        "code": "provider-goodDetail",
        "icon": "",
        "route": "/provider/goods/goodDetail/:id",
        "component": null,
        "redirect": null,
        "is_hidden": 1,
        "type": "M",
        "status": 0,
        "sort": 1,
        "created_by": "174097662365274112",
        "updated_by": "174097662365274112",
        "created_at": "1746524096868",
        "updated_at": "1746588972652",
        "deleted_at": null,
        "remark": null
    },

      ]
    },
    {
      "id": "5400",
      "parent_id": "0",
      "level": "0",
      "name": "订单管理",
      "code": "provider-ordermanagement",
      "icon": "ma-icon-ordermanagement",
      "route": "/ordermanagement",
      "component": null,
      "redirect": null,
      "is_hidden": 0,
      "type": "M",
      "status": 1,
      "sort": 6,
      "created_by": "174097662365274112",
      "updated_by": "174097662365274112",
      "created_at": "1747122281040",
      "updated_at": "1747219376579",
      "deleted_at": null,
      "remark": null,
      "badge": 5,
      "children": [
        {
          "id": "5402",
          "parent_id": "5400",
          "level": "1",
          "name": "订单列表",
          "code": "provider-ordermanagement-orderlist",
          "icon": "ma-icon-orderlist",
          "route": "/provider/ordermanagement/orderlist",
          "component": "/provider/ordermanagement/orderlist/index",
          "redirect": null,
          "is_hidden": 0,
          "type": "M",
          "status": 1,
          "sort": 1,
          "created_by": "174097662365274112",
          "updated_by": "174097662365274112",
          "created_at": "1747122281040",
          "updated_at": "1747219376579",
          "deleted_at": null,
          "remark": null
        },
        {
          "id": "5401",
          "parent_id": "5400",
          "level": "1",
          "name": "订单报备",
          "code": "provider-ordermanagement-reportingmanagement",
          "icon": "ma-icon-reportingmanagement",
          "route": "/provider/ordermanagement/reportingmanagement",
          "component": "/provider/ordermanagement/reportingmanagement/index",
          "redirect": null,
          "is_hidden": 0,
          "type": "M",
          "status": 1,
          "sort": 1,
          "created_by": "174097662365274112",
          "updated_by": "174097662365274112",
          "created_at": "1747122281040",
          "updated_at": "1747219376579",
          "deleted_at": null,
          "remark": null
        },
        {
          "id": "79",
          "parent_id": "5400",
          "level": "1",
          "name": "订单详情",
          "code": "provider-ordermanagement-detail",
          "icon": null,
          "route": "/provider/ordermanagement/detail/:id",
          "component": "/provider/ordermanagement/detail/index",
          "redirect": null,
          "is_hidden": 1,
          "type": "M",
          "status": 0,
          "sort": 1,
          "created_by": "174097662365274112",
          "updated_by": null,
          "created_at": "1747129330477",
          "updated_at": "1747129330477",
          "deleted_at": null,
          "remark": null
      },
      {
        "id": "81",
        "parent_id": "5400",
        "level": "1",
        "name": "发货详情",
        "code": "provider-ordermanagement-sendoutgoods",
        "icon": null,
        "route": "/provider/ordermanagement/sendoutgoods/:id",
        "component": "/provider/ordermanagement/sendoutgoods/index",
        "redirect": null,
        "is_hidden": 1,
        "type": "M",
        "status": 0,
        "sort": 1,
        "created_by": "174097662365274112",
        "updated_by": null,
        "created_at": "1747129330477",
        "updated_at": "1747129330477",
        "deleted_at": null,
        "remark": null
    },
      // {
      //   "id": "80",
      //   "parent_id": "5400",
      //   "level": "1",
      //   "name": "售后管理",
      //   "code": "provider-ordermanagement-after",
      //   "icon": "ma-icon-after-sale",
      //   "route": "/provider/ordermanagement/after",
      //   "component": "/provider/ordermanagement/after/index",
      //   "redirect": null,
      //   "is_hidden": 0,
      //   "type": "M",
      //   "status": 1,
      //   "sort": 1,
      //   "created_by": "174097662365274112",
      //   "updated_by": "174097662365274112",
      //   "created_at": "1747122281040",
      //   "updated_at": "1747219376579",
      //   "deleted_at": null,
      //   "remark": null
      // },

      ]
    },
  
    // {
    //   "id": "5600",
    //   "parent_id": "0",
    //   "level": "0",
    //   "name": "财务管理",
    //   "code": "provider-finance",
    //   "icon": "ma-icon-finance",
    //   "route": "/finance",
    //   "component": null,
    //   "redirect": null,
    //   "is_hidden": 0,
    //   "type": "M",
    //   "status": 1,
    //   "sort": 8,
    //   "created_by": "174097662365274112",
    //   "updated_by": "174097662365274112",
    //   "created_at": "1747122281040",
    //   "updated_at": "1747219376579",
    //   "deleted_at": null,
    //   "remark": null,
    //   "badge": 3,
    //   "children": [
    //     {
    //       "id": "5601",
    //       "parent_id": "5600",
    //       "level": "1",
    //       "name": "订单发票管理",
    //       "code": "provider-finance-invoice",
    //       "icon": "ma-icon-invoice",
    //       "route": "/provider/finance/invoice",
    //       "component": "/provider/finance/invoice/index",
    //       "redirect": null,
    //       "is_hidden": 0,
    //       "type": "M",
    //       "status": 1,
    //       "sort": 1,
    //       "created_by": "174097662365274112",
    //       "updated_by": "174097662365274112",
    //       "created_at": "1747122281040",
    //       "updated_at": "1747219376579",
    //       "deleted_at": null,
    //       "remark": null
    //     },
    //     {
    //       "id": "5608",
    //       "parent_id": "5600",
    //       "level": "1",
    //       "name": "申请开票",
    //       "code": "provider-finance-invoice-apply",
    //       "icon": "ma-icon-invoice",
    //       "route": "/provider/finance/invoice/apply",
    //       "component": "/provider/finance/invoice/apply/index",
    //       "redirect": null,
    //       "is_hidden": 1,
    //       "type": "M",
    //       "status": 1,
    //       "sort": 1,
    //       "created_by": "174097662365274112",
    //       "updated_by": "174097662365274112",
    //       "created_at": "1747122281040",
    //       "updated_at": "1747219376579",
    //       "deleted_at": null,
    //       "remark": null
    //     },
    //     {
    //       "id": "5602",
    //       "parent_id": "5600",
    //       "level": "1",
    //       "name": "进项发票上传",
    //       "code": "provider-finance-invoice-upload",
    //       "icon": "ma-icon-invoice",
    //       "route": "/provider/finance/invoiceUpload",
    //       "component": "/provider/finance/invoiceUpload/index",
    //       "redirect": null,
    //       "is_hidden": 1,
    //       "type": "M",
    //       "status": 1,
    //       "sort": 2,
    //       "created_by": "174097662365274112",
    //       "updated_by": "174097662365274112",
    //       "created_at": "1747122281040",
    //       "updated_at": "1747219376579",
    //       "deleted_at": null,
    //       "remark": null
    //     },
    //     {
    //       "id": "5609",
    //       "parent_id": "5600",
    //       "level": "1",
    //       "name": "认款申请",
    //       "code": "provider-finance-claim-application",
    //       "icon": "ma-icon-invoice",
    //       "route": "/provider/finance/claim/application",
    //       "component": "/provider/finance/claim/application/index",
    //       "redirect": null,
    //       "is_hidden": 1,
    //       "type": "M",
    //       "status": 1,
    //       "sort": 9,
    //       "created_by": "174097662365274112",
    //       "updated_by": "174097662365274112",
    //       "created_at": "1747122281040",
    //       "updated_at": "1747219376579",
    //       "deleted_at": null,
    //       "remark": null
    //     },
    //     {
    //       "id": "5603",
    //       "parent_id": "5600",
    //       "level": "1",
    //       "name": "认款申请",
    //       "code": "provider-finance-claim",
    //       "icon": "ma-icon-invoice",
    //       "route": "/provider/finance/claim",
    //       "component": "/provider/finance/claim/index",
    //       "redirect": null,
    //       "is_hidden": 0,
    //       "type": "M",
    //       "status": 1,
    //       "sort": 3,
    //       "created_by": "174097662365274112",
    //       "updated_by": "174097662365274112",
    //       "created_at": "1747122281040",
    //       "updated_at": "1747219376579",
    //       "deleted_at": null,
    //       "remark": null
    //     },
    //     {
    //       "id": "5605",
    //       "parent_id": "5600",
    //       "level": "1",
    //       "name": "认款申请记录",
    //       "code": "provider-finance-claim-record",
    //       "icon": "ma-icon-invoice",
    //       "route": "/provider/finance/claimRecord",
    //       "component": "/provider/finance/claimRecord/index",
    //       "redirect": null,
    //       "is_hidden": 0,
    //       "type": "M",
    //       "status": 1,
    //       "sort": 4,
    //       "created_by": "174097662365274112",
    //       "updated_by": "174097662365274112",
    //       "created_at": "1747122281040",
    //       "updated_at": "1747219376579",
    //       "deleted_at": null,
    //       "remark": null
    //     },
    //     {
    //       "id": "5604",
    //       "parent_id": "5600",
    //       "level": "1",
    //       "name": "订单结算",
    //       "code": "provider-finance-order-settlement",
    //       "icon": "ma-icon-invoice",
    //       "route": "/provider/finance/orderSettlement",
    //       "component": "/provider/finance/orderSettlement/index",
    //       "redirect": null,
    //       "is_hidden": 0,
    //       "type": "M",
    //       "status": 1,
    //       "sort": 4,
    //       "created_by": "174097662365274112",
    //       "updated_by": "174097662365274112",
    //       "created_at": "1747122281040",
    //       "updated_at": "1747219376579",
    //       "deleted_at": null,
    //       "remark": null
    //     }
    //   ]
    // },
   
    {
      "id": "27",
      "parent_id": "0",      "level": "0",
      "name": "系统管理",
      "code": "system",
      "icon": "IconSettings",
      "route": "/system",
      "component": "",
      "redirect": null,
      "is_hidden": 0,
      "type": "M",
      "status": 1,
      "sort": 10,
      "created_by": "171196850903322624",
      "updated_by": "174097662365274112",
      "created_at": "1745465465892",
      "updated_at": "1747122428462",
      "deleted_at": null,
      "remark": null,
      "children": [
   
          {
            "id": "52",
            "parent_id": "27",
            "level": "0,27",
            "name": "个人中心",
            "code": "provider-system-center",
            "icon": "IconTool",
            "route": "/provider/system/center",
            "component": null,
            "redirect": null,
            "is_hidden": 0,
            "type": "M",
            "status": 1,
            "sort": 1,
            "created_by": "174097662365274112",
            "updated_by": "174097662365274112",
            "created_at": "1746683665772",
            "updated_at": "1748308872978",
            "deleted_at": null,
            "remark": null,
        },
  //       {
  //         "id": "32",
  //         "parent_id": "28",
  //         "level": "0,27,28",
  //         "name": "角色管理",
  //         "code": "provider-system-role",
  //         "icon": "IconUser",
  //         "route": "/provider/system/role",
  //         "component": "",
  //         "redirect": null,
  //         "is_hidden": 0,
  //         "type": "M",
  //         "status": 1,
  //         "sort": 2,
  //         "created_by": "171196850903322624",
  //         "updated_by": "174097662365274112",
  //         "created_at": "1745465647185",
  //         "updated_at": "1748309418228",
  //         "deleted_at": null,
  //         "remark": null
  //     },
  //     {
  //       "id": "31",
  //       "parent_id": "28",
  //       "level": "0,27,28",
  //       "name": "用户管理",
  //       "code": "provider-system-user",
  //       "icon": "IconUserGroup",
  //       "route": "/provider/system/user",
  //       "component": "",
  //       "redirect": null,
  //       "is_hidden": 0,
  //       "type": "M",
  //       "status": 1,
  //       "sort": 1,
  //       "created_by": "171196850903322624",
  //       "updated_by": "174097662365274112",
  //       "created_at": "1745465626253",
  //       "updated_at": "1748309410265",
  //       "deleted_at": null,
  //       "remark": null,
  //   },
  //   {
  //     "id": "38",
  //     "parent_id": "28",
  //     "level": "0,27,28",
  //     "name": "菜单管理",
  //     "code": "provider-system-menu",
  //     "icon": "IconList",
  //     "route": "/provider/system/menu",
  //     "component": null,
  //     "redirect": null,
  //     "is_hidden": 0,
  //     "type": "M",
  //     "status": 1,
  //     "sort": 4,
  //     "created_by": "171196850903322624",
  //     "updated_by": "174097662365274112",
  //     "created_at": "1745489022388",
  //     "updated_at": "1748309528890",
  //     "deleted_at": null,
  //     "remark": null
  // }
        ]
  },
  {
    "id": "6700",
    "parent_id": "0",
    "level": "0",
    "name": "报备车",
    "code": "provider-reportCar",
    "icon": "ma-icon-goods-pool",
    "route": "/provider/reportCar",
    "component": "/provider/reportCar/index",
    "redirect": null,
    "is_hidden": 1,
    "type": "M",
    "status": 1,
    "sort": 1,
    "created_by": "174097662365274112",
    "updated_by": "174097662365274112",
    "created_at": "1746524096868",
    "updated_at": "1746588972652",
    "deleted_at": null,
    "remark": null
  },
  ],
  codes: ["provider:goods:view", "provider:goods:edit", "provider:order:view", "provider:order:process"]
};

export default providerMenuMock;