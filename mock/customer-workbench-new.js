/**
 * 客户工作台模拟数据
 * 
 * <AUTHOR>
 */

// 数据统计
export const statistics = {
  myCustomers: 42,
  activeCustomers: 128,
  newCustomersThisMonth: 24
};

// 今日数据
export const todayData = {
  orderAmount: 56000,
  orderCount: 12
};

// 待办事项数据
export const todoData = {
  logisticsException: 5,
  pendingShipment: 12,
  pendingReceipt: 8,
  pendingAcceptance: 6,
  pendingInvoice: 10,
  pendingPayment: 15,
  
  // 物流异常详情
  logisticsExceptionItems: [
    {
      orderId: 'ORD20250501001',
      customerName: '上海科技有限公司',
      products: '高性能服务器 x2',
      trackingNumber: 'SF1234567890',
      status: '运输中断',
      exceptionTime: '2025-05-08 14:30:00'
    },
    {
      orderId: 'ORD20250502002',
      customerName: '北京信息技术有限公司',
      products: '网络交换机 x5',
      trackingNumber: 'YT9876543210',
      status: '包裹损坏',
      exceptionTime: '2025-05-08 16:45:00'
    },
    {
      orderId: 'ORD20250503003',
      customerName: '广州电子科技有限公司',
      products: '工业控制器 x3',
      trackingNumber: 'ZT5678901234',
      status: '配送延迟',
      exceptionTime: '2025-05-09 09:15:00'
    },
    {
      orderId: 'ORD20250504004',
      customerName: '深圳数字科技有限公司',
      products: '数据存储设备 x1',
      trackingNumber: 'JD6789012345',
      status: '地址错误',
      exceptionTime: '2025-05-09 10:30:00'
    },
    {
      orderId: 'ORD20250505005',
      customerName: '杭州网络科技有限公司',
      products: '路由器 x8',
      trackingNumber: 'SF2345678901',
      status: '签收异常',
      exceptionTime: '2025-05-09 11:20:00'
    }
  ],
  
  // 待发货详情
  pendingShipmentItems: [
    {
      orderId: 'ORD20250506006',
      customerName: '成都软件科技有限公司',
      products: '开发工作站 x3',
      orderTime: '2025-05-07 09:30:00',
      expectedShipTime: '2025-05-10 15:00:00'
    },
    {
      orderId: 'ORD20250506007',
      customerName: '武汉通信科技有限公司',
      products: '通信模块 x20',
      orderTime: '2025-05-07 10:45:00',
      expectedShipTime: '2025-05-10 15:00:00'
    },
    {
      orderId: 'ORD20250506008',
      customerName: '南京电子有限公司',
      products: '电源模块 x15',
      orderTime: '2025-05-07 14:20:00',
      expectedShipTime: '2025-05-10 15:00:00'
    },
    {
      orderId: 'ORD20250507009',
      customerName: '西安芯片技术有限公司',
      products: '芯片开发板 x5',
      orderTime: '2025-05-08 09:10:00',
      expectedShipTime: '2025-05-11 15:00:00'
    },
    {
      orderId: 'ORD20250507010',
      customerName: '重庆电子科技有限公司',
      products: '显示模块 x10',
      orderTime: '2025-05-08 11:30:00',
      expectedShipTime: '2025-05-11 15:00:00'
    }
  ],
  
  // 待收货详情
  pendingReceiptItems: [
    {
      orderId: 'ORD20250501011',
      customerName: '天津科技有限公司',
      products: '服务器机柜 x1',
      shipTime: '2025-05-05 14:30:00',
      trackingNumber: 'SF3456789012'
    },
    {
      orderId: 'ORD20250502012',
      customerName: '济南网络科技有限公司',
      products: '网络安全设备 x2',
      shipTime: '2025-05-06 10:20:00',
      trackingNumber: 'YT4567890123'
    },
    {
      orderId: 'ORD20250503013',
      customerName: '长沙电子有限公司',
      products: '监控设备 x5',
      shipTime: '2025-05-06 15:40:00',
      trackingNumber: 'ZT5678901234'
    },
    {
      orderId: 'ORD20250504014',
      customerName: '青岛通信科技有限公司',
      products: '通信服务器 x1',
      shipTime: '2025-05-07 09:30:00',
      trackingNumber: 'JD6789012345'
    }
  ],
  
  // 待验收详情
  pendingAcceptanceItems: [
    {
      orderId: 'ORD20250428015',
      customerName: '大连软件科技有限公司',
      products: '开发服务器 x2',
      receiveTime: '2025-05-03 14:30:00',
      acceptanceDeadline: '2025-05-10 23:59:59'
    },
    {
      orderId: 'ORD20250429016',
      customerName: '沈阳电子科技有限公司',
      products: '工控电脑 x3',
      receiveTime: '2025-05-04 10:20:00',
      acceptanceDeadline: '2025-05-11 23:59:59'
    },
    {
      orderId: 'ORD20250430017',
      customerName: '哈尔滨通信科技有限公司',
      products: '通信基站 x1',
      receiveTime: '2025-05-05 15:40:00',
      acceptanceDeadline: '2025-05-12 23:59:59'
    }
  ],
  
  // 待开票详情
  pendingInvoiceItems: [
    {
      orderId: 'ORD20250425018',
      customerName: '长春电子有限公司',
      products: '电子元件 x50',
      acceptanceTime: '2025-05-02 14:30:00',
      invoiceType: '增值税专用发票'
    },
    {
      orderId: 'ORD20250426019',
      customerName: '福州科技有限公司',
      products: '传感器 x30',
      acceptanceTime: '2025-05-03 10:20:00',
      invoiceType: '增值税普通发票'
    },
    {
      orderId: 'ORD20250427020',
      customerName: '厦门网络科技有限公司',
      products: '网络设备 x5',
      acceptanceTime: '2025-05-03 15:40:00',
      invoiceType: '增值税专用发票'
    },
    {
      orderId: 'ORD20250428021',
      customerName: '南宁电子科技有限公司',
      products: '电源适配器 x20',
      acceptanceTime: '2025-05-04 09:30:00',
      invoiceType: '增值税普通发票'
    },
    {
      orderId: 'ORD20250429022',
      customerName: '贵阳通信科技有限公司',
      products: '通信模块 x10',
      acceptanceTime: '2025-05-05 11:20:00',
      invoiceType: '增值税专用发票'
    }
  ],
  
  // 待付款详情
  pendingPaymentItems: [
    {
      orderId: 'ORD20250420023',
      customerName: '昆明电子有限公司',
      products: '电子元件 x100',
      invoiceTime: '2025-04-30 14:30:00',
      amount: 56000,
      paymentDeadline: '2025-05-15 23:59:59'
    },
    {
      orderId: 'ORD20250421024',
      customerName: '兰州科技有限公司',
      products: '服务器 x2',
      invoiceTime: '2025-05-01 10:20:00',
      amount: 78000,
      paymentDeadline: '2025-05-16 23:59:59'
    },
    {
      orderId: 'ORD20250422025',
      customerName: '银川网络科技有限公司',
      products: '网络设备 x8',
      invoiceTime: '2025-05-02 15:40:00',
      amount: 45000,
      paymentDeadline: '2025-05-17 23:59:59'
    },
    {
      orderId: 'ORD20250423026',
      customerName: '西宁电子科技有限公司',
      products: '显示器 x10',
      invoiceTime: '2025-05-03 09:30:00',
      amount: 32000,
      paymentDeadline: '2025-05-18 23:59:59'
    },
    {
      orderId: 'ORD20250424027',
      customerName: '乌鲁木齐通信科技有限公司',
      products: '通信设备 x5',
      invoiceTime: '2025-05-04 11:20:00',
      amount: 67000,
      paymentDeadline: '2025-05-19 23:59:59'
    }
  ]
};

// 销售数据分析
export const salesAnalysisData = {
  // 热销类目排行
  hotCategories: [
    { name: '服务器', amount: 850000, count: 42, ratio: 28.3 },
    { name: '网络设备', amount: 620000, count: 85, ratio: 20.7 },
    { name: '存储设备', amount: 450000, count: 36, ratio: 15.0 },
    { name: '安全设备', amount: 380000, count: 48, ratio: 12.7 },
    { name: '工作站', amount: 320000, count: 28, ratio: 10.7 },
    { name: '软件服务', amount: 210000, count: 65, ratio: 7.0 },
    { name: '云服务', amount: 170000, count: 54, ratio: 5.6 }
  ],
  
  // 热销品牌排行
  hotBrands: [
    { name: '华为', amount: 780000, count: 68, ratio: 26.0 },
    { name: '思科', amount: 650000, count: 72, ratio: 21.7 },
    { name: '戴尔', amount: 520000, count: 45, ratio: 17.3 },
    { name: 'IBM', amount: 350000, count: 28, ratio: 11.7 },
    { name: 'HPE', amount: 320000, count: 32, ratio: 10.7 },
    { name: '联想', amount: 280000, count: 36, ratio: 9.3 },
    { name: '浦科特', amount: 100000, count: 12, ratio: 3.3 }
  ],
  
  // 销售额环比分析
  salesGrowth: [
    { month: '1月', current: 2800000, previous: 2500000, growth: 12.0 },
    { month: '2月', current: 2650000, previous: 2400000, growth: 10.4 },
    { month: '3月', current: 3100000, previous: 2700000, growth: 14.8 },
    { month: '4月', current: 2950000, previous: 2850000, growth: 3.5 },
    { month: '5月', current: 3200000, previous: 2900000, growth: 10.3 }
  ],
  
  // 销售渠道分析
  salesChannels: [
    { name: '直销', amount: 1850000, ratio: 61.7 },
    { name: '分销商', amount: 750000, ratio: 25.0 },
    { name: '电商平台', amount: 320000, ratio: 10.7 },
    { name: '其他', amount: 80000, ratio: 2.6 }
  ]
};

// 团队概况数据
export const teamOverviewData = {
  targetSalesAmount: 1000000,
  targetCompletionRate: 75,
  dailyOrderCount: 12,
  dailyOrderAmount: 56000,
  
  // 应收情况
  receivables: {
    within3Months: { amount: 580000, count: 35 },
    over3Months: { amount: 320000, count: 18 },
    over6Months: { amount: 180000, count: 10 },
    overOneYear: { amount: 120000, count: 6 }
  },
  
  // 回款情况
  payments: {
    within3Months: { amount: 520000, count: 32 },
    over3Months: { amount: 280000, count: 15 },
    over6Months: { amount: 150000, count: 8 },
    overOneYear: { amount: 90000, count: 4 }
  },
  teamMembers: [
    // 基础团队成员
    { name: '张三', salesAmount: 280000, targetAmount: 400000, completionRate: 70 },
    { name: '李四', salesAmount: 350000, targetAmount: 400000, completionRate: 87.5 },
    { name: '王五', salesAmount: 120000, targetAmount: 300000, completionRate: 40 },
    { name: '赵六', salesAmount: 180000, targetAmount: 300000, completionRate: 60 },
    
    // 扩展团队成员（可根据需要注释掉部分成员来模拟小团队）
    { name: '孙七', salesAmount: 210000, targetAmount: 350000, completionRate: 60 },
    { name: '周八', salesAmount: 320000, targetAmount: 400000, completionRate: 80 },
    { name: '吴九', salesAmount: 150000, targetAmount: 300000, completionRate: 50 },
    { name: '郑十', salesAmount: 270000, targetAmount: 350000, completionRate: 77.1 },
    { name: '陈明', salesAmount: 190000, targetAmount: 300000, completionRate: 63.3 },
    { name: '林涛', salesAmount: 380000, targetAmount: 400000, completionRate: 95 },
    { name: '黄健', salesAmount: 160000, targetAmount: 300000, completionRate: 53.3 },
    { name: '刘强', salesAmount: 240000, targetAmount: 350000, completionRate: 68.6 },
    { name: '宋江', salesAmount: 290000, targetAmount: 350000, completionRate: 82.9 },
    { name: '杨洋', salesAmount: 310000, targetAmount: 400000, completionRate: 77.5 },
    { name: '孟浩', salesAmount: 130000, targetAmount: 300000, completionRate: 43.3 },
    { name: '董文', salesAmount: 220000, targetAmount: 350000, completionRate: 62.9 },
    { name: '马超', salesAmount: 260000, targetAmount: 350000, completionRate: 74.3 },
    { name: '谢涛', salesAmount: 170000, targetAmount: 300000, completionRate: 56.7 },
    { name: '高峰', salesAmount: 230000, targetAmount: 350000, completionRate: 65.7 },
    { name: '彭丽', salesAmount: 340000, targetAmount: 400000, completionRate: 85 },
    { name: '贾磊', salesAmount: 200000, targetAmount: 300000, completionRate: 66.7 },
    { name: '卢斌', salesAmount: 250000, targetAmount: 350000, completionRate: 71.4 },
    { name: '汪涛', salesAmount: 300000, targetAmount: 400000, completionRate: 75 },
    { name: '钱伟', salesAmount: 140000, targetAmount: 300000, completionRate: 46.7 },
    { name: '孙亚', salesAmount: 360000, targetAmount: 400000, completionRate: 90 },
    { name: '郭涛', salesAmount: 110000, targetAmount: 300000, completionRate: 36.7 },
    { name: '张文', salesAmount: 330000, targetAmount: 400000, completionRate: 82.5 },
    { name: '王浩', salesAmount: 370000, targetAmount: 400000, completionRate: 92.5 }
  ],
  monthlyOrderCount: [
    { date: '05-01', count: 8 },
    { date: '05-02', count: 10 },
    { date: '05-03', count: 6 },
    { date: '05-04', count: 12 },
    { date: '05-05', count: 15 },
    { date: '05-06', count: 8 },
    { date: '05-07', count: 9 },
    { date: '05-08', count: 11 },
    { date: '05-09', count: 12 }
  ],
  monthlyOrderAmount: [
    { date: '05-01', amount: 42000 },
    { date: '05-02', amount: 56000 },
    { date: '05-03', amount: 36000 },
    { date: '05-04', amount: 68000 },
    { date: '05-05', amount: 78000 },
    { date: '05-06', amount: 45000 },
    { date: '05-07', amount: 52000 },
    { date: '05-08', amount: 63000 },
    { date: '05-09', amount: 56000 }
  ],
  newCustomers: [
    { date: '05-01', count: 3 },
    { date: '05-02', count: 5 },
    { date: '05-03', count: 2 },
    { date: '05-04', count: 4 },
    { date: '05-05', count: 6 },
    { date: '05-06', count: 3 },
    { date: '05-07', count: 4 },
    { date: '05-08', count: 5 },
    { date: '05-09', count: 3 }
  ]
};

// 我的概况数据
export const personalOverviewData = {
  targetSalesAmount: 300000,
  targetCompletionRate: 65,
  dailyOrderCount: 5,
  dailyOrderAmount: 28000,
  
  // 应收情况
  receivables: {
    within3Months: { amount: 180000, count: 12 },
    over3Months: { amount: 95000, count: 6 },
    over6Months: { amount: 45000, count: 3 },
    overOneYear: { amount: 30000, count: 2 }
  },
  
  // 回款情况
  payments: {
    within3Months: { amount: 160000, count: 10 },
    over3Months: { amount: 80000, count: 5 },
    over6Months: { amount: 40000, count: 2 },
    overOneYear: { amount: 20000, count: 1 }
  },
  monthlyOrderCount: [
    { date: '05-01', count: 3 },
    { date: '05-02', count: 4 },
    { date: '05-03', count: 2 },
    { date: '05-04', count: 5 },
    { date: '05-05', count: 6 },
    { date: '05-06', count: 4 },
    { date: '05-07', count: 3 },
    { date: '05-08', count: 5 },
    { date: '05-09', count: 4 }
  ],
  monthlyOrderAmount: [
    { date: '05-01', amount: 18000 },
    { date: '05-02', amount: 22000 },
    { date: '05-03', amount: 15000 },
    { date: '05-04', amount: 28000 },
    { date: '05-05', amount: 32000 },
    { date: '05-06', amount: 24000 },
    { date: '05-07', amount: 19000 },
    { date: '05-08', amount: 26000 },
    { date: '05-09', amount: 23000 }
  ],
  newCustomers: [
    { date: '05-01', count: 1 },
    { date: '05-02', count: 2 },
    { date: '05-03', count: 1 },
    { date: '05-04', count: 2 },
    { date: '05-05', count: 3 },
    { date: '05-06', count: 1 },
    { date: '05-07', count: 2 },
    { date: '05-08', count: 2 },
    { date: '05-09', count: 1 }
  ]
};
