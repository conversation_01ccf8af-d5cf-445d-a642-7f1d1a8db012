/**
 * 会员中心模拟数据
 */

// 订单状态枚举
export const OrderStatus = {
  PENDING_PAYMENT: 1, // 待付款
  PENDING_SHIPMENT: 2, // 待发货
  PENDING_RECEIPT: 3, // 待收货
  PENDING_REVIEW: 4, // 待评价
  COMPLETED: 5, // 已完成
  CANCELLED: 6, // 已取消
  REFUNDING: 7, // 退款中
  REFUNDED: 8 // 已退款
};

// 订单状态文本
export const OrderStatusText = {
  [OrderStatus.PENDING_PAYMENT]: '待付款',
  [OrderStatus.PENDING_SHIPMENT]: '待发货',
  [OrderStatus.PENDING_RECEIPT]: '待收货',
  [OrderStatus.PENDING_REVIEW]: '待评价',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
  [OrderStatus.REFUNDING]: '退款中',
  [OrderStatus.REFUNDED]: '已退款'
};

// 支付方式枚举
export const PaymentMethod = {
  WECHAT: 1, // 微信支付
  ALIPAY: 2, // 支付宝
  BANK_CARD: 3, // 银行卡
  CASH: 4 // 货到付款
};

// 支付方式文本
export const PaymentMethodText = {
  [PaymentMethod.WECHAT]: '微信支付',
  [PaymentMethod.ALIPAY]: '支付宝',
  [PaymentMethod.BANK_CARD]: '银行卡',
  [PaymentMethod.CASH]: '货到付款'
};

// 模拟商品数据
export const mockProducts = [
  {
    id: 1001,
    name: 'Apple iPad 2021款 10.2英寸平板电脑',
    price: 2499.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=iPad',
    category: '数码产品',
    description: '10.2英寸Retina显示屏，A13仿生芯片，支持Apple Pencil，支持智能键盘',
    stock: 100,
    sales: 1200
  },
  {
    id: 1002,
    name: '戴尔(DELL)灵越14英寸轻薄笔记本电脑',
    price: 4799.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=戴尔笔记本',
    category: '电脑办公',
    description: '14英寸全高清屏幕，第11代Intel酷睿i5处理器，16GB内存，512GB SSD',
    stock: 50,
    sales: 800
  },
  {
    id: 1003,
    name: '美的(Midea)1.5匹变频冷暖空调挂机',
    price: 2399.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=美的空调',
    category: '家用电器',
    description: '1.5匹变频，智能温控，静音节能，快速制冷/制热',
    stock: 80,
    sales: 500
  },
  {
    id: 1004,
    name: '华为手机 HUAWEI P50 Pro 8GB+256GB',
    price: 6488.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=华为手机',
    category: '手机通讯',
    description: '骁龙888处理器，6.6英寸OLED屏幕，5000万像素徕卡四摄',
    stock: 30,
    sales: 2000
  },
  {
    id: 1005,
    name: '飞利浦(PHILIPS)55英寸4K超高清智能电视',
    price: 3199.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=飞利浦电视',
    category: '家用电器',
    description: '55英寸4K超高清，HDR显示，智能语音控制，丰富的应用生态',
    stock: 40,
    sales: 300
  },
  {
    id: 1006,
    name: '小米12 Pro 5G手机 12GB+256GB',
    price: 4699.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=小米手机',
    category: '手机通讯',
    description: '骁龙8 Gen 1处理器，6.73英寸2K AMOLED屏幕，5000万像素三摄',
    stock: 60,
    sales: 1800
  },
  {
    id: 1007,
    name: '索尼(SONY)WH-1000XM4无线降噪耳机',
    price: 2299.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=索尼耳机',
    category: '数码产品',
    description: '数字降噪技术，LDAC高解析音频，触控操作，30小时续航',
    stock: 120,
    sales: 950
  },
  {
    id: 1008,
    name: 'Apple Watch Series 7 GPS款 45mm',
    price: 3199.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=Apple Watch',
    category: '数码产品',
    description: '全新视网膜显示屏，IP6X防尘，游泳级防水，血氧监测',
    stock: 85,
    sales: 760
  },
  {
    id: 1009,
    name: '海尔(Haier)455升风冷无霜双开门冰箱',
    price: 3899.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=海尔冰箱',
    category: '家用电器',
    description: '风冷无霜，智能控温，静音设计，大容量',
    stock: 35,
    sales: 280
  },
  {
    id: 1010,
    name: '九阳(Joyoung)破壁料理机家用多功能',
    price: 799.00,
    image: 'https://placehold.co/140x140/ffffff/333333?text=九阳料理机',
    category: '厨房电器',
    description: '1200W大功率，8叶钝齿刀片，智能预设，静音设计',
    stock: 150,
    sales: 1100
  }
];

// 模拟用户订单数据
export const mockOrders = [
  {
    id: 'ORD202504140001',
    userId: 1001,
    status: OrderStatus.COMPLETED,
    totalAmount: 4998.00,
    paymentMethod: PaymentMethod.ALIPAY,
    paymentTime: '2025-04-01 15:30:22',
    createTime: '2025-04-01 15:20:10',
    updateTime: '2025-04-05 10:15:30',
    address: {
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '海淀区',
      detail: '中关村科技园区8号楼606室',
      isDefault: true
    },
    items: [
      {
        productId: 1001,
        productName: 'Apple iPad 2021款 10.2英寸平板电脑',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=iPad',
        price: 2499.00,
        quantity: 2,
        subtotal: 4998.00
      }
    ],
    logistics: {
      company: '顺丰速运',
      trackingNumber: 'SF1234567890',
      deliveryTime: '2025-04-02 09:15:30',
      receivedTime: '2025-04-03 14:20:45'
    },
    comment: {
      rating: 5,
      content: '商品质量很好，物流速度快，包装完好，非常满意！',
      images: ['https://placehold.co/100x100/ffffff/333333?text=评价图片1'],
      createTime: '2025-04-05 10:15:30'
    }
  },
  {
    id: 'ORD202504140006',
    userId: 1001,
    status: OrderStatus.COMPLETED,
    totalAmount: 2299.00,
    paymentMethod: PaymentMethod.WECHAT,
    paymentTime: '2025-03-15 10:20:33',
    createTime: '2025-03-15 10:15:22',
    updateTime: '2025-03-20 14:30:45',
    address: {
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '海淀区',
      detail: '中关村科技园区8号楼606室',
      isDefault: true
    },
    items: [
      {
        productId: 1007,
        productName: '索尼(SONY)WH-1000XM4无线降噪耳机',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=索尼耳机',
        price: 2299.00,
        quantity: 1,
        subtotal: 2299.00
      }
    ],
    logistics: {
      company: '京东物流',
      trackingNumber: 'JD1234567891',
      deliveryTime: '2025-03-16 09:10:20',
      receivedTime: '2025-03-17 15:40:22'
    },
    comment: {
      rating: 5,
      content: '降噪效果很好，音质清晰，续航时间长，很满意的购物体验！',
      images: ['https://placehold.co/100x100/ffffff/333333?text=评价图片1'],
      createTime: '2025-03-20 14:30:45'
    }
  },
  {
    id: 'ORD202504140007',
    userId: 1001,
    status: OrderStatus.CANCELLED,
    totalAmount: 799.00,
    paymentMethod: null,
    paymentTime: null,
    createTime: '2025-03-25 16:20:33',
    updateTime: '2025-03-25 16:50:45',
    address: {
      name: '李四',
      phone: '13900139000',
      province: '北京市',
      city: '北京市',
      district: '朝阳区',
      detail: '朝阳公园南路8号院',
      isDefault: false
    },
    items: [
      {
        productId: 1010,
        productName: '九阳(Joyoung)破壁料理机家用多功能',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=九阳料理机',
        price: 799.00,
        quantity: 1,
        subtotal: 799.00
      }
    ],
    logistics: null,
    comment: null
  },
  {
    id: 'ORD202504140008',
    userId: 1001,
    status: OrderStatus.REFUNDED,
    totalAmount: 3199.00,
    paymentMethod: PaymentMethod.ALIPAY,
    paymentTime: '2025-03-10 11:25:33',
    createTime: '2025-03-10 11:20:15',
    updateTime: '2025-03-15 09:30:45',
    address: {
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '海淀区',
      detail: '中关村科技园区8号楼606室',
      isDefault: true
    },
    items: [
      {
        productId: 1008,
        productName: 'Apple Watch Series 7 GPS款 45mm',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=Apple Watch',
        price: 3199.00,
        quantity: 1,
        subtotal: 3199.00
      }
    ],
    logistics: {
      company: '顺丰速运',
      trackingNumber: 'SF9876543211',
      deliveryTime: '2025-03-11 14:20:15',
      receivedTime: '2025-03-12 10:30:45'
    },
    comment: null
  },
  {
    id: 'ORD202504140002',
    userId: 1001,
    status: OrderStatus.PENDING_SHIPMENT,
    totalAmount: 4799.00,
    paymentMethod: PaymentMethod.WECHAT,
    paymentTime: '2025-04-10 18:45:12',
    createTime: '2025-04-10 18:40:22',
    updateTime: '2025-04-10 18:45:12',
    address: {
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '海淀区',
      detail: '中关村科技园区8号楼606室',
      isDefault: true
    },
    items: [
      {
        productId: 1002,
        productName: '戴尔(DELL)灵越14英寸轻薄笔记本电脑',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=戴尔笔记本',
        price: 4799.00,
        quantity: 1,
        subtotal: 4799.00
      }
    ],
    logistics: null,
    comment: null
  },
  {
    id: 'ORD202504140003',
    userId: 1001,
    status: OrderStatus.PENDING_PAYMENT,
    totalAmount: 6488.00,
    paymentMethod: null,
    paymentTime: null,
    createTime: '2025-04-14 09:30:15',
    updateTime: '2025-04-14 09:30:15',
    address: {
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '海淀区',
      detail: '中关村科技园区8号楼606室',
      isDefault: true
    },
    items: [
      {
        productId: 1004,
        productName: '华为手机 HUAWEI P50 Pro 8GB+256GB',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=华为手机',
        price: 6488.00,
        quantity: 1,
        subtotal: 6488.00
      }
    ],
    logistics: null,
    comment: null
  },
  {
    id: 'ORD202504140004',
    userId: 1001,
    status: OrderStatus.PENDING_RECEIPT,
    totalAmount: 3199.00,
    paymentMethod: PaymentMethod.ALIPAY,
    paymentTime: '2025-04-08 14:20:33',
    createTime: '2025-04-08 14:15:10',
    updateTime: '2025-04-09 10:30:22',
    address: {
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '海淀区',
      detail: '中关村科技园区8号楼606室',
      isDefault: true
    },
    items: [
      {
        productId: 1005,
        productName: '飞利浦(PHILIPS)55英寸4K超高清智能电视',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=飞利浦电视',
        price: 3199.00,
        quantity: 1,
        subtotal: 3199.00
      }
    ],
    logistics: {
      company: '京东物流',
      trackingNumber: 'JD9876543210',
      deliveryTime: '2025-04-09 10:30:22',
      receivedTime: null
    },
    comment: null
  },
  {
    id: 'ORD202504140005',
    userId: 1001,
    status: OrderStatus.PENDING_REVIEW,
    totalAmount: 2399.00,
    paymentMethod: PaymentMethod.WECHAT,
    paymentTime: '2025-04-05 11:10:45',
    createTime: '2025-04-05 11:05:30',
    updateTime: '2025-04-07 16:40:12',
    address: {
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '海淀区',
      detail: '中关村科技园区8号楼606室',
      isDefault: true
    },
    items: [
      {
        productId: 1003,
        productName: '美的(Midea)1.5匹变频冷暖空调挂机',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=美的空调',
        price: 2399.00,
        quantity: 1,
        subtotal: 2399.00
      }
    ],
    logistics: {
      company: '顺丰速运',
      trackingNumber: 'SF9876543210',
      deliveryTime: '2025-04-06 09:20:15',
      receivedTime: '2025-04-07 16:40:12'
    },
    comment: null
  },
  {
    id: 'ORD202504140009',
    userId: 1001,
    status: OrderStatus.REFUNDING,
    totalAmount: 4699.00,
    paymentMethod: PaymentMethod.ALIPAY,
    paymentTime: '2025-04-12 09:15:33',
    createTime: '2025-04-12 09:10:22',
    updateTime: '2025-04-13 14:30:45',
    address: {
      name: '张三',
      phone: '13800138000',
      province: '北京市',
      city: '北京市',
      district: '海淀区',
      detail: '中关村科技园区8号楼606室',
      isDefault: true
    },
    items: [
      {
        productId: 1006,
        productName: '小米12 Pro 5G手机 12GB+256GB',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=小米手机',
        price: 4699.00,
        quantity: 1,
        subtotal: 4699.00
      }
    ],
    logistics: {
      company: '京东物流',
      trackingNumber: 'JD9876543212',
      deliveryTime: '2025-04-13 09:20:15',
      receivedTime: '2025-04-13 14:30:45'
    },
    comment: null
  },
  {
    id: 'ORD202504140010',
    userId: 1001,
    status: OrderStatus.PENDING_SHIPMENT,
    totalAmount: 6998.00,
    paymentMethod: PaymentMethod.ALIPAY,
    paymentTime: '2025-04-15 14:30:22',
    createTime: '2025-04-15 14:25:10',
    updateTime: '2025-04-15 14:30:22',
    address: {
      id: 1,
      name: '张三',
      phone: '13800138000',
      province: '广东省',
      city: '深圳市',
      district: '南山区',
      detail: '科技园南区10栋101室',
      isDefault: true
    },
    items: [
      {
        productId: 1001,
        productName: 'Apple iPad 2021款 10.2英寸平板电脑',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=iPad',
        price: 2499.00,
        quantity: 1,
        subtotal: 2499.00
      },
      {
        productId: 1004,
        productName: '华为手机 HUAWEI P50 Pro 8GB+256GB',
        productImage: 'https://placehold.co/140x140/ffffff/333333?text=华为手机',
        price: 4499.00,
        quantity: 1,
        subtotal: 4499.00
      }
    ],
    logistics: null,
    comment: null
  }
];

// 模拟用户收货地址数据
export const mockAddresses = [
  {
    id: 1,
    userId: 1001,
    name: '张三',
    phone: '13800138000',
    province: '北京市',
    city: '北京市',
    district: '海淀区',
    detail: '中关村科技园区8号楼606室',
    isDefault: true
  },
  {
    id: 2,
    userId: 1001,
    name: '张三',
    phone: '13800138000',
    province: '上海市',
    city: '上海市',
    district: '浦东新区',
    detail: '张江高科技园区博云路2号',
    isDefault: false
  },
  {
    id: 3,
    userId: 1001,
    name: '李四',
    phone: '13900139000',
    province: '北京市',
    city: '北京市',
    district: '朝阳区',
    detail: '朝阳公园南路8号院',
    isDefault: false
  }
];

// 模拟用户积分记录数据
export const mockPointsRecords = [
  {
    id: 1,
    userId: 1001,
    points: 100,
    type: 'increase', // 增加
    description: '注册奖励',
    createTime: '2024-01-15 10:30:22'
  },
  {
    id: 2,
    userId: 1001,
    points: 200,
    type: 'increase',
    description: '完成首单',
    createTime: '2025-04-01 15:40:10'
  },
  {
    id: 3,
    userId: 1001,
    points: 50,
    type: 'increase',
    description: '商品评价',
    createTime: '2025-04-05 10:20:30'
  },
  {
    id: 4,
    userId: 1001,
    points: 100,
    type: 'decrease', // 减少
    description: '兑换优惠券',
    createTime: '2025-04-08 14:25:45'
  },
  {
    id: 5,
    userId: 1001,
    points: 150,
    type: 'increase',
    description: '每日签到',
    createTime: '2025-04-14 08:00:15'
  }
];

// 模拟用户优惠券数据
export const mockCoupons = [
  {
    id: 1,
    userId: 1001,
    name: '满1000减100优惠券',
    type: 'discount', // 满减券
    value: 100,
    minAmount: 1000,
    status: 'valid', // 有效
    startTime: '2025-04-01 00:00:00',
    endTime: '2025-05-01 23:59:59',
    useTime: null,
    orderId: null
  },
  {
    id: 2,
    userId: 1001,
    name: '满2000减200优惠券',
    type: 'discount',
    value: 200,
    minAmount: 2000,
    status: 'valid',
    startTime: '2025-04-01 00:00:00',
    endTime: '2025-05-01 23:59:59',
    useTime: null,
    orderId: null
  },
  {
    id: 3,
    userId: 1001,
    name: '满3000减300优惠券',
    type: 'discount',
    value: 300,
    minAmount: 3000,
    status: 'used', // 已使用
    startTime: '2025-04-01 00:00:00',
    endTime: '2025-05-01 23:59:59',
    useTime: '2025-04-01 15:20:10',
    orderId: 'ORD202504140001'
  },
  {
    id: 4,
    userId: 1001,
    name: '满500减50优惠券',
    type: 'discount',
    value: 50,
    minAmount: 500,
    status: 'expired', // 已过期
    startTime: '2025-03-01 00:00:00',
    endTime: '2025-04-01 23:59:59',
    useTime: null,
    orderId: null
  },
  {
    id: 5,
    userId: 1001,
    name: '无门槛50元优惠券',
    type: 'cash', // 现金券
    value: 50,
    minAmount: 0,
    status: 'valid',
    startTime: '2025-04-10 00:00:00',
    endTime: '2025-05-10 23:59:59',
    useTime: null,
    orderId: null
  }
];

// 模拟用户浏览历史数据
export const mockBrowsingHistory = [
  {
    id: 1,
    userId: 1001,
    productId: 1004,
    productName: '华为手机 HUAWEI P50 Pro 8GB+256GB',
    productImage: 'https://placehold.co/140x140/ffffff/333333?text=华为手机',
    price: 6488.00,
    viewTime: '2025-04-14 09:15:30'
  },
  {
    id: 2,
    userId: 1001,
    productId: 1002,
    productName: '戴尔(DELL)灵越14英寸轻薄笔记本电脑',
    productImage: 'https://placehold.co/140x140/ffffff/333333?text=戴尔笔记本',
    price: 4799.00,
    viewTime: '2025-04-13 16:40:22'
  },
  {
    id: 3,
    userId: 1001,
    productId: 1005,
    productName: '飞利浦(PHILIPS)55英寸4K超高清智能电视',
    productImage: 'https://placehold.co/140x140/ffffff/333333?text=飞利浦电视',
    price: 3199.00,
    viewTime: '2025-04-12 11:20:15'
  },
  {
    id: 4,
    userId: 1001,
    productId: 1001,
    productName: 'Apple iPad 2021款 10.2英寸平板电脑',
    productImage: 'https://placehold.co/140x140/ffffff/333333?text=iPad',
    price: 2499.00,
    viewTime: '2025-04-11 14:30:45'
  },
  {
    id: 5,
    userId: 1001,
    productId: 1003,
    productName: '美的(Midea)1.5匹变频冷暖空调挂机',
    productImage: 'https://placehold.co/140x140/ffffff/333333?text=美的空调',
    price: 2399.00,
    viewTime: '2025-04-10 09:50:10'
  }
];

// 模拟用户收藏商品数据
export const mockFavorites = [
  {
    id: 1,
    userId: 1001,
    productId: 1001,
    productName: 'Apple iPad 2021款 10.2英寸平板电脑',
    productImage: 'https://placehold.co/140x140/ffffff/333333?text=iPad',
    price: 2499.00,
    collectTime: '2025-04-01 15:25:30'
  },
  {
    id: 2,
    userId: 1001,
    productId: 1004,
    productName: '华为手机 HUAWEI P50 Pro 8GB+256GB',
    productImage: 'https://placehold.co/140x140/ffffff/333333?text=华为手机',
    price: 6488.00,
    collectTime: '2025-04-05 10:15:45'
  },
  {
    id: 3,
    userId: 1001,
    productId: 1002,
    productName: '戴尔(DELL)灵越14英寸轻薄笔记本电脑',
    productImage: 'https://placehold.co/140x140/ffffff/333333?text=戴尔笔记本',
    price: 4799.00,
    collectTime: '2025-04-10 18:30:20'
  }
];

// 模拟用户消息通知数据
export const mockMessages = [
  {
    id: 1,
    userId: 1001,
    title: '注册成功通知',
    content: '恭喜您成功注册成为聚灵商城会员，您将享受会员专属优惠和服务。',
    type: 'system', // 系统通知
    isRead: true,
    createTime: '2024-01-15 10:30:30'
  },
  {
    id: 2,
    userId: 1001,
    title: '订单发货通知',
    content: '您的订单 ORD202504140004 已发货，物流公司：京东物流，运单号：JD9876543210。',
    type: 'order', // 订单通知
    isRead: true,
    createTime: '2025-04-09 10:35:22'
  },
  {
    id: 3,
    userId: 1001,
    title: '支付成功通知',
    content: '您的订单 ORD202504140002 已支付成功，支付金额：4799.00元。',
    type: 'payment', // 支付通知
    isRead: false,
    createTime: '2025-04-10 18:45:30'
  },
  {
    id: 4,
    userId: 1001,
    title: '优惠活动通知',
    content: '五一大促即将开始，全场商品低至5折，更有满减优惠券等您来领取！',
    type: 'promotion', // 促销通知
    isRead: false,
    createTime: '2025-04-14 09:00:00'
  },
  {
    id: 5,
    userId: 1001,
    title: '会员等级提升通知',
    content: '恭喜您成功升级为黄金会员，可享受更多专属优惠和服务。',
    type: 'member', // 会员通知
    isRead: false,
    createTime: '2025-04-05 10:30:15'
  }
];

// 获取用户订单列表
export const getUserOrders = (userId, status = null) => {
  let orders = mockOrders.filter(order => order.userId === userId);
  
  if (status !== null) {
    orders = orders.filter(order => order.status === status);
  }
  
  return orders.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
};

// 获取订单详情
export const getOrderDetail = (orderId) => {
  console.log('mock数据 - 获取订单详情:', orderId);
  console.log('mock数据 - 可用订单列表:', mockOrders.map(o => o.id));
  
  if (!orderId) {
    console.warn('mock数据 - 订单ID为空');
    return null;
  }
  
  const order = mockOrders.find(order => order.id === orderId);
  
  if (!order) {
    console.warn('mock数据 - 未找到订单:', orderId);
    return null;
  }
  
  console.log('mock数据 - 找到订单:', order.id);
  return order;
};

// 获取用户地址列表
export const getUserAddresses = (userId) => {
  return mockAddresses.filter(address => address.userId === userId);
};

// 获取用户积分记录
export const getUserPointsRecords = (userId) => {
  return mockPointsRecords.filter(record => record.userId === userId)
    .sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
};

// 获取用户优惠券
export const getUserCoupons = (userId, status = null) => {
  let coupons = mockCoupons.filter(coupon => coupon.userId === userId);
  
  if (status !== null) {
    coupons = coupons.filter(coupon => coupon.status === status);
  }
  
  return coupons.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
};

// 获取用户浏览历史
export const getUserBrowsingHistory = (userId) => {
  return mockBrowsingHistory.filter(history => history.userId === userId)
    .sort((a, b) => new Date(b.viewTime) - new Date(a.viewTime));
};

// 获取用户收藏商品
export const getUserFavorites = (userId) => {
  return mockFavorites.filter(favorite => favorite.userId === userId)
    .sort((a, b) => new Date(b.collectTime) - new Date(a.collectTime));
};

// 获取用户消息通知
export const getUserMessages = (userId, isRead = null) => {
  let messages = mockMessages.filter(message => message.userId === userId);
  
  if (isRead !== null) {
    messages = messages.filter(message => message.isRead === isRead);
  }
  
  return messages.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
};

// 获取用户订单统计
export const getUserOrderStats = (userId) => {
  const orders = getUserOrders(userId);
  
  return {
    total: orders.length,
    pendingPayment: orders.filter(order => order.status === OrderStatus.PENDING_PAYMENT).length,
    pendingShipment: orders.filter(order => order.status === OrderStatus.PENDING_SHIPMENT).length,
    pendingReceipt: orders.filter(order => order.status === OrderStatus.PENDING_RECEIPT).length,
    pendingReview: orders.filter(order => order.status === OrderStatus.PENDING_REVIEW).length
  };
};

export default {
  OrderStatus,
  OrderStatusText,
  PaymentMethod,
  PaymentMethodText,
  mockProducts,
  mockOrders,
  mockAddresses,
  mockPointsRecords,
  mockCoupons,
  mockBrowsingHistory,
  mockFavorites,
  mockMessages,
  getUserOrders,
  getOrderDetail,
  getUserAddresses,
  getUserPointsRecords,
  getUserCoupons,
  getUserBrowsingHistory,
  getUserFavorites,
  getUserMessages,
  getUserOrderStats
};
