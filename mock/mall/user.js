/**
 * 商城会员模拟数据
 */

// 模拟用户数据库
const mockUsers = [
  {
    id: 1001,
    username: 'test',
    password: '123456',
    phone: '13800138000',
    nickname: '测试用户',
    avatar: 'https://placehold.co/100x100/ffffff/333333?text=测试用户',
    level: '黄金会员',
    points: 1000,
    balance: 500.00,
    registerTime: '2024-01-15',
    lastLoginTime: '2025-04-10'
  },
  {
    id: 1002,
    username: 'admin',
    password: 'admin123',
    phone: '13900139000',
    nickname: '管理员',
    avatar: 'https://placehold.co/100x100/ffffff/333333?text=管理员',
    level: '钻石会员',
    points: 5000,
    balance: 2000.00,
    registerTime: '2023-12-01',
    lastLoginTime: '2025-04-12'
  }
];

// 模拟会员等级数据
const memberLevels = [
  { id: 1, name: '普通会员', minPoints: 0, discount: 0.98 },
  { id: 2, name: '银卡会员', minPoints: 500, discount: 0.95 },
  { id: 3, name: '黄金会员', minPoints: 1000, discount: 0.9 },
  { id: 4, name: '钻石会员', minPoints: 5000, discount: 0.85 },
  { id: 5, name: '至尊会员', minPoints: 10000, discount: 0.8 }
];

/**
 * 模拟登录接口
 * @param {Object} loginData 登录数据
 * @returns {Promise<Object>} 登录结果
 */
export const mockLogin = (loginData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 查找用户
      const user = mockUsers.find(
        u => (u.username === loginData.username || u.phone === loginData.username) && 
             u.password === loginData.password
      );
      
      if (user) {
        // 登录成功，返回用户信息（不包含密码）
        const { password, ...userInfo } = user;
        resolve({
          success: true,
          code: 200,
          message: '登录成功',
          data: {
            ...userInfo,
            token: `mock_token_${Date.now()}_${user.id}`
          }
        });
      } else {
        // 登录失败
        resolve({
          success: false,
          code: 401,
          message: '用户名或密码错误',
          data: null
        });
      }
    }, 800); // 模拟网络延迟
  });
};

/**
 * 模拟注册接口
 * @param {Object} registerData 注册数据
 * @returns {Promise<Object>} 注册结果
 */
export const mockRegister = (registerData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // 检查用户名是否已存在
      const usernameExists = mockUsers.some(u => u.username === registerData.username);
      
      // 检查手机号是否已存在
      const phoneExists = mockUsers.some(u => u.phone === registerData.phone);
      
      if (usernameExists) {
        resolve({
          success: false,
          code: 400,
          message: '用户名已存在',
          data: null
        });
        return;
      }
      
      if (phoneExists) {
        resolve({
          success: false,
          code: 400,
          message: '手机号已被注册',
          data: null
        });
        return;
      }
      
      // 创建新用户
      const newUser = {
        id: mockUsers.length > 0 ? Math.max(...mockUsers.map(u => u.id)) + 1 : 1001,
        username: registerData.username,
        password: registerData.password,
        phone: registerData.phone,
        nickname: registerData.username,
        avatar: `https://placehold.co/100x100/ffffff/333333?text=${registerData.username}`,
        level: '普通会员',
        points: 100, // 注册奖励积分
        balance: 0,
        registerTime: new Date().toISOString().split('T')[0],
        lastLoginTime: new Date().toISOString().split('T')[0]
      };
      
      // 添加到模拟数据库
      mockUsers.push(newUser);
      
      // 返回注册成功信息
      resolve({
        success: true,
        code: 200,
        message: '注册成功',
        data: {
          username: newUser.username,
          phone: newUser.phone
        }
      });
    }, 1200); // 模拟网络延迟
  });
};

/**
 * 模拟发送验证码接口
 * @param {string} phone 手机号
 * @returns {Promise<Object>} 发送结果
 */
export const mockSendVerificationCode = (phone) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 生成6位随机验证码
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      
      console.log(`向手机号 ${phone} 发送验证码: ${code}`);
      
      resolve({
        success: true,
        code: 200,
        message: '验证码发送成功',
        data: {
          phone,
          code, // 实际项目中不应返回验证码，这里为了方便测试
          expireTime: Date.now() + 5 * 60 * 1000 // 5分钟有效期
        }
      });
    }, 500);
  });
};

/**
 * 模拟获取用户信息接口
 * @param {string} token 用户令牌
 * @returns {Promise<Object>} 用户信息
 */
export const mockGetUserInfo = (token) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (!token) {
        resolve({
          success: false,
          code: 401,
          message: '未登录或登录已过期',
          data: null
        });
        return;
      }
      
      // 从token中提取用户ID（实际项目中应该通过JWT解析或其他方式）
      const tokenParts = token.split('_');
      const userId = parseInt(tokenParts[tokenParts.length - 1]);
      
      // 查找用户
      const user = mockUsers.find(u => u.id === userId);
      
      if (user) {
        // 返回用户信息（不包含密码）
        const { password, ...userInfo } = user;
        resolve({
          success: true,
          code: 200,
          message: '获取用户信息成功',
          data: userInfo
        });
      } else {
        resolve({
          success: false,
          code: 401,
          message: '用户不存在或登录已过期',
          data: null
        });
      }
    }, 600);
  });
};

export default {
  mockLogin,
  mockRegister,
  mockSendVerificationCode,
  mockGetUserInfo,
  memberLevels
};
