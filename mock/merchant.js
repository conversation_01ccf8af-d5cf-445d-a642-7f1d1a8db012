const merchantMenuMock = {
  user: {
    id: 1,
    username: "merchantAdmin",
    user_type: "200",
    nickname: "商户管理员",
    phone: "16858888988",
    email: "<EMAIL>",
    avatar: null,
    signed: "商户管理系统，助力商业成功",
    dashboard: "statistics",
    status: 1,
    login_ip: "**************",
    login_time: "2025-03-25 01:10:58",
    backend_setting: null,
    created_by: 0,
    updated_by: 0,
    created_at: "2025-03-14 07:30:04",
    updated_at: "2025-03-25 01:10:58",
    remark: null,
  },
  roles: ["merchantAdmin"],
  routers: [
    {
      id: 3000,
      parent_id: 0,
      name: "goods",
      component: "", 
      path: "/merchant/goods",
      redirect: "",
      meta: {
        type: "M", // M代表目录
        icon: "ma-icon-goods", // 商品管理图标
        title: "商品管理",
        hidden: false,
        hiddenBreadcrumb: false,
      },
      children: [
        {
          id: 3001,
          parent_id: 3000,
          name: "merchant-addGoods",
          component: "/merchant/goods/addGoods/index", 
          path: "/merchant/goods/addGoods", 
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-add", 
            title: "添加商品",
            hidden: true,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3002,
          parent_id: 3000,
          name: "merchant-goodsList",
          component: "/merchant/goods/goodsList/index", 
          path: "/merchant/goods/goodsList", 
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-goods-list", 
            title: "商品列表",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3003,
          parent_id: 3000,
          name: "merchant-goodsClass",
          component: "/merchant/goods/goodsClass/index", 
          path: "/merchant/goods/goodsClass",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-category",
            title: "商品分类",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3004,
          parent_id: 3000,
          name: "merchant-brandManage",
          component: "/merchant/goods/brandManage/index", 
          path: "/merchant/goods/brandManage",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-brand",
            title: "品牌管理",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3005,
          parent_id: 3000,
          name: "merchant-labelManage",
          component: "/merchant/goods/labelManage/index", 
          path: "/merchant/goods/labelManage",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-label",
            title: "标签管理",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3006,
          parent_id: 3000,
          name: "merchant-paramTemplate",
          component: "/merchant/goods/paramTemplate/index", 
          path: "/merchant/goods/paramTemplate",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-template",
            title: "属性模板",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
      ],
    },
    {
      id: 1000,
      parent_id: 0,
      name: "order",
      component: "",
      path: "/merchant/order",
      redirect: "",
      meta: {
        type: "M",
        icon: "ma-icon-order",
        title: "订单管理",
        hidden: false,
        hiddenBreadcrumb: false,
      },
      children: [
        {
          id: 1100,
          parent_id: 1000,
          name: "merchant-orderManage",
          component: "/merchant/order/orderManage/index",
          path: "/merchant/order/orderManage",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-list",
            title: "订单列表",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 1200,
          parent_id: 1000,
          name: "merchant-afterOrder",
          component: "/merchant/order/afterOrder/index",
          path: "/merchant/order/afterOrder",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-service",
            title: "售后订单",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
      ],
    },
    {
      id: 2000,
      parent_id: 0,
      name: "merchant-user",
      component: "",
      path: "/merchant/user",
      redirect: "",
      meta: {
        type: "M",
        icon: "ma-icon-user",
        title: "用户管理",
        hidden: false,
        hiddenBreadcrumb: false,
      },
      children: [],
    },
  ],
  codes: ["*"],
};

export default merchantMenuMock;
