const masterMenuMock = {
  user: {
    id: 1,
    username: "superAdmin",
    user_type: "100",
    nickname: "创始人",
    phone: "16858888988",
    email: "<EMAIL>",
    avatar: null,
    signed: "广阔天地，大有所为",
    dashboard: "statistics",
    status: 1,
    login_ip: "**************",
    login_time: "2025-03-25 01:10:58",
    backend_setting: null,
    created_by: 0,
    updated_by: 0,
    created_at: "2025-03-14 07:30:04",
    updated_at: "2025-03-25 01:10:58",
    remark: null,
  },
  // roles: ["superAdmin"],
  routers: [
    {
      id: 3000,
      parent_id: 0,
      name: "goods",
      component: "", 
      path: "/goods",
      redirect: "",
      meta: {
        type: "M", // M代表目录
        icon: "ma-icon-goods", // 你可以换成合适的商品管理图标
        title: "商品管理",
        hidden: false,
        hiddenBreadcrumb: false,
      },
      children: [
        {
          id: 1999,
          parent_id: 3000,
          name: "master-addGoods",
          component: "/master/goods/addGoods", 
          path: "/master/goods/addGoods", 
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-goods-list", 
            title: "添加商品",
            hidden: true,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3001,
          parent_id: 3000,
          name: "master-goodsList",
          component: "/master/goods/goodsList", 
          path: "/master/goods/goodsList", 
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-goods-list", 
            title: "商品列表",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3002,
          parent_id: 3000,
          name: "master-goodsClass",
          component: "/master/goods/goodsClass/index", 
          path: "/master/goods/goodsClass",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-category",
            title: "商品分类",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3003,
          parent_id: 3000,
          name: "master-brandManage",
          component: "/master/goods/brandManage/index", 
          path: "/master/goods/brandManage",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-brand",
            title: "品牌管理",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3004,
          parent_id: 3000,
          name: "master-labelManage",
          component: "/master/goods/labelManage/index", 
          path: "/master/goods/labelManage",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-label",
            title: "标签管理",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3005,
          parent_id: 3000,
          name: "master-paramTemplate",
          component: "/master/goods/paramTemplate/index", 
          path: "/master/goods/paramTemplate",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-template",
            title: "属性模板",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3006,
          parent_id: 3000,
          name: "master-freightTemplate",
          component: "/master/goods/freightTemplate/index", 
          path: "/master/goods/freightTemplate",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-logistics",
            title: "运费模板",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 3007,
          parent_id: 3000,
          name: "master-goodsService",
          component: "/master/goods/goodsService/index", 
          path: "/master/goods/goodsService",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-service",
            title: "商品服务",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
      ],
    },
    {
      id: 1000,
      parent_id: 0,
      name: "order",
      component: "",
      path: "/order",
      redirect: "",
      meta: {
        type: "M",
        icon: "ma-icon-permission",
        title: "订单管理",
        hidden: false,
        hiddenBreadcrumb: false,
      },
      children: [
        {
          id: 1100,
          parent_id: 1000,
          name: "master-order",
          component: "",
          path: "/master/order/orderManage",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-user",
            title: "订单列表",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 4001, // 使用一个唯一ID
          parent_id: 1000, // 修改父ID为1000
          name: "master-order-detail",
          component: "/master/order/orderManage/detail/[id]", 
          path: "/master/order/orderManage/detail/:id", 
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-order-detail", 
            title: "订单详情",
            hidden: true, // 设为true，不在菜单中显示
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 1200,
          parent_id: 1000,
          name: "master-after-order",
          component: "",
          path: "/master/order/afterOrder",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-user",
            title: "售后订单",
            hidden: false,
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 4002, // 使用一个唯一ID
          parent_id: 1000, // 修改父ID为1000
          name: "master-after-order-detail",
          component: "/master/order/afterOrder/detail/[id]", 
          path: "/master/order/afterOrder/detail/:id", 
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-order-detail", 
            title: "售后详情",
            hidden: true, // 设为true，不在菜单中显示
            hiddenBreadcrumb: false,
          },
        },
        {
          id: 4003, // 使用一个唯一ID
          parent_id: 1000, // 修改父ID为1000
          name: "master-order-detail",
          component: "/master/customer/ordermanagement/detail/[id]", 
          path: "/master/customer/ordermanagement/detail/:id", 
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-order-detail", 
            title: "直销订单详情",
            hidden: true, // 设为true，不在菜单中显示
            hiddenBreadcrumb: false,
          },
        },
      ],
    },
    {
      id: 2000,
      parent_id: 0,
      name: "system",
      component: "",
      path: "/system",
      redirect: "",
      meta: {
        type: "M",
        icon: "ma-icon-setting",
        title: "系统管理",
        hidden: false,
        hiddenBreadcrumb: false,
      },
      children: [
        {
          id: 2100,
          parent_id: 2000,
          name: "system-permission",
          component: "",
          path: "/master/system/permission",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-permission",
            title: "权限管理",
            hidden: false,
            hiddenBreadcrumb: false,
          },
          children: [
            {
              id: 2110,
              parent_id: 2100,
              name: "master-system-permission-user",
              component: "master/system/permission/user/index",
              path: "/master/system/permission/user",
              redirect: "",
              meta: {
                type: "M",
                icon: "ma-icon-user",
                title: "用户管理",
                hidden: false,
                hiddenBreadcrumb: false,
              },
            },
            {
              id: 2120,
              parent_id: 2100,
              name: "master-system-permission-role",
              component: "master/system/permission/role/index",
              path: "/master/system/permission/role",
              redirect: "",
              meta: {
                type: "M",
                icon: "ma-icon-role",
                title: "角色管理",
                hidden: false,
                hiddenBreadcrumb: false,
              },
            },
            {
              id: 2130,
              parent_id: 2100,
              name: "master-system-permission-menu",
              component: "master/system/permission/menu/index",
              path: "/master/system/permission/menu",
              redirect: "",
              meta: {
                type: "M",
                icon: "ma-icon-menu",
                title: "菜单管理",
                hidden: false,
                hiddenBreadcrumb: false,
              },
            },
            {
              id: 2140,
              parent_id: 2100,
              name: "master-system-permission-dept",
              component: "master/system/permission/dept/index",
              path: "/master/system/permission/dept",
              redirect: "",
              meta: {
                type: "M",
                icon: "ma-icon-department",
                title: "部门管理",
                hidden: false,
                hiddenBreadcrumb: false,
              },
            },
          ],
        },
        {
          id: 2200,
          parent_id: 2000,
          name: "system-config",
          component: "",
          path: "/master/system/config",
          redirect: "",
          meta: {
            type: "M",
            icon: "ma-icon-config",
            title: "系统配置",
            hidden: false,
            hiddenBreadcrumb: false,
          },
          children: [
            {
              id: 2210,
              parent_id: 2200,
              name: "system-config-upload",
              component: "master/system/config/upload/index",
              path: "/master/system/config/upload",
              redirect: "",
              meta: {
                type: "M",
                icon: "ma-icon-upload",
                title: "上传设置",
                hidden: false,
                hiddenBreadcrumb: false,
              },
            },
            {
              id: 2220,
              parent_id: 2200,
              name: "system-config-notification",
              component: "/master/system/config/notification/index",
              path: "/master/system/config/notification",
              redirect: "",
              meta: {
                type: "M",
                icon: "ma-icon-notification",
                title: "消息通知",
                hidden: false,
                hiddenBreadcrumb: false,
              },
            },
            {
              id: 2230,
              parent_id: 2200,
              name: "system-config-thirdparty",
              component: "/master/system/config/thirdparty/index",
              path: "/master/system/config/thirdparty",
              redirect: "",
              meta: {
                type: "M",
                icon: "ma-icon-thirdparty",
                title: "第三方登录",
                hidden: false,
                hiddenBreadcrumb: false,
              }
            },
          ],
        },
      ],
    },
  ],
  codes: ["system:user:view", "system:user:edit"]  // 按钮权限
};

export default masterMenuMock;
