// 菜单管理模拟数据
import masterMenuMock from './master'

// 从masterMenuMock中提取菜单数据并转换为菜单管理页面需要的格式
const extractMenuData = () => {
  // 递归处理菜单数据
  const processMenus = (menus, parentId = 0) => {
    const result = []
    
    menus.forEach(menu => {
      // 转换菜单项格式
      const menuItem = {
        id: menu.id,
        parent_id: menu.parent_id,
        name: menu.name,
        code: menu.name, // 菜单标识使用name
        icon: menu.meta?.icon || '',
        route: menu.path || '',
        component: menu.component || '',
        redirect: menu.redirect || '',
        sort: menu.id % 100, // 使用id的末两位作为排序值
        is_hidden: menu.meta?.hidden ? '1' : '2', // 1表示隐藏，2表示显示
        status: 1, // 默认启用
        type: menu.meta?.type || 'M', // 默认为菜单类型
        created_at: '2025-03-14 07:30:04',
        remark: '',
      }
      
      result.push(menuItem)
      
      // 递归处理子菜单
      if (menu.children && menu.children.length > 0) {
        const childMenus = processMenus(menu.children, menu.id)
        result.push(...childMenus)
      }
    })
    
    return result
  }
  
  return processMenus(masterMenuMock.routers)
}

// 菜单列表数据
const menuList = extractMenuData()

// 菜单树数据（用于上级菜单选择）
const getMenuTree = (params = {}) => {
  // 过滤只返回菜单类型的数据（如果onlyMenu参数为true）
  let filteredMenus = menuList
  if (params.onlyMenu) {
    filteredMenus = menuList.filter(item => item.type === 'M')
  }
  
  // 构建树形结构
  const buildTree = (items, parentId = 0) => {
    const result = []
    
    items.filter(item => item.parent_id === parentId).forEach(item => {
      const children = buildTree(items, item.id)
      const node = {
        id: item.id,
        parent_id: item.parent_id,
        name: item.name,
        title: item.name, // 显示名称
        value: item.id, // 选择值
        key: item.id, // 唯一标识
      }
      
      if (children.length > 0) {
        node.children = children
      }
      
      result.push(node)
    })
    
    return result
  }
  
  return buildTree(filteredMenus)
}

// 模拟API响应
export default {
  // 获取菜单列表
  getList: (params = {}) => {
    return Promise.resolve({
      success: true,
      message: '获取成功',
      data: menuList
    })
  },
  
  // 获取回收站菜单列表
  getRecycleList: (params = {}) => {
    return Promise.resolve({
      success: true,
      message: '获取成功',
      data: []
    })
  },
  
  // 获取菜单树
  tree: (params = {}) => {
    return Promise.resolve({
      success: true,
      message: '获取成功',
      data: getMenuTree(params)
    })
  },
  
  // 添加菜单
  save: (params = {}) => {
    // 生成新的ID
    const newId = Math.max(...menuList.map(item => item.id)) + 1
    
    // 创建新菜单项
    const newMenu = {
      ...params,
      id: newId,
      created_at: new Date().toISOString().replace('T', ' ').substring(0, 19)
    }
    
    // 添加到列表
    menuList.push(newMenu)
    
    return Promise.resolve({
      success: true,
      message: '添加成功',
      data: newMenu
    })
  },
  
  // 更新菜单
  update: (id, data = {}) => {
    // 查找要更新的菜单
    const index = menuList.findIndex(item => item.id === parseInt(id))
    
    if (index !== -1) {
      // 更新菜单数据
      menuList[index] = { ...menuList[index], ...data }
      
      return Promise.resolve({
        success: true,
        message: '更新成功',
        data: menuList[index]
      })
    }
    
    return Promise.resolve({
      success: false,
      message: '菜单不存在',
      data: null
    })
  },
  
  // 删除菜单（移到回收站）
  deletes: (data) => {
    // 在实际应用中，这里应该将菜单标记为已删除
    // 在这个模拟中，我们简单地返回成功
    return Promise.resolve({
      success: true,
      message: '删除成功',
      data: null
    })
  },
  
  // 恢复菜单
  recoverys: (data) => {
    return Promise.resolve({
      success: true,
      message: '恢复成功',
      data: null
    })
  },
  
  // 真实删除菜单
  realDeletes: (data) => {
    return Promise.resolve({
      success: true,
      message: '删除成功',
      data: null
    })
  },
  
  // 数字运算操作（如修改排序值）
  numberOperation: (data = {}) => {
    const { id, numberName, numberValue } = data
    
    // 查找要更新的菜单
    const index = menuList.findIndex(item => item.id === parseInt(id))
    
    if (index !== -1 && numberName) {
      // 更新指定的数字字段
      menuList[index][numberName] = numberValue
      
      return Promise.resolve({
        success: true,
        message: '操作成功',
        data: menuList[index]
      })
    }
    
    return Promise.resolve({
      success: false,
      message: '操作失败',
      data: null
    })
  },
  
  // 更改菜单状态
  changeStatus: (data = {}) => {
    const { id, status } = data
    
    // 查找要更新的菜单
    const index = menuList.findIndex(item => item.id === parseInt(id))
    
    if (index !== -1) {
      // 更新状态
      menuList[index].status = status
      
      return Promise.resolve({
        success: true,
        message: '状态更新成功',
        data: menuList[index]
      })
    }
    
    return Promise.resolve({
      success: false,
      message: '菜单不存在',
      data: null
    })
  }
}
