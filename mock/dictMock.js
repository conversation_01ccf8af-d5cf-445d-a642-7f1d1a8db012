// 系统字典和数据相关的 mock 数据
const dictMock = {
  // 角色列表
  getRoleList: () => {
    return {
      success: true,
      message: "获取成功",
      data: [
        {
          id: 1,
          name: "超级管理员",
          code: "superAdmin",
          status: 1,
          sort: 0,
          data_scope: 0,
        },
        {
          id: 2,
          name: "普通管理员",
          code: "admin",
          status: 1,
          sort: 0,
          data_scope: 1,
        },
        {
          id: 3,
          name: "测试角色",
          code: "test",
          status: 1,
          sort: 0,
          data_scope: 1,
        },
      ],
    };
  },

  // 部门树结构
  getDeptTree: () => {
    return {
      success: true,
      message: "获取成功",
      data: [
        {
          id: 1,
          parent_id: 0,
          name: "曼艺科技",
          code: "MY",
          status: 1,
          sort: 0,
          children: [
            {
              id: 2,
              parent_id: 1,
              name: "技术部",
              code: "TECH",
              status: 1,
              sort: 0,
              children: [],
            },
            {
              id: 3,
              parent_id: 1,
              name: "市场部",
              code: "MARKET",
              status: 1,
              sort: 0,
              children: [],
            },
            {
              id: 4,
              parent_id: 1,
              name: "运营部",
              code: "OPS",
              status: 1,
              sort: 0,
              children: [],
            },
          ],
        },
      ],
    };
  },

  // 岗位列表
  getPostList: () => {
    return {
      success: true,
      message: "获取成功",
      data: [
        { id: 1, name: "董事长", code: "ceo", status: 1, sort: 0 },
        { id: 2, name: "技术总监", code: "cto", status: 1, sort: 0 },
        { id: 3, name: "产品经理", code: "pm", status: 1, sort: 0 },
        { id: 4, name: "前端工程师", code: "fe", status: 1, sort: 0 },
        { id: 5, name: "后端工程师", code: "be", status: 1, sort: 0 },
      ],
    };
  },

  // 数据字典列表
  getDataDictList: () => {
    return {
      success: true,
      message: "获取成功",
      data: [
        { id: 1, name: "性别", code: "sex", status: 1, sort: 0 },
        { id: 2, name: "状态", code: "status", status: 1, sort: 0 },
        { id: 3, name: "数据状态", code: "data_status", status: 1, sort: 0 },
        { id: 4, name: "仪表盘", code: "dashboard", status: 1, sort: 0 },
      ],
    };
  },

  // 获取字典数据
  getDictByCode: (code) => {
    const dictMap = {
      sex: [
        { key: "1", title: "男", value: "1", status: 1 },
        { key: "2", title: "女", value: "2", status: 1 },
      ],
      status: [
        { key: "1", title: "启用", value: "1", status: 1 },
        { key: "2", title: "禁用", value: "2", status: 1 },
      ],
      data_status: [
        { key: "1", title: "启用", value: "1", status: 1 },
        { key: "2", title: "禁用", value: "2", status: 1 },
      ],
      dashboard: [
        { key: "statistics", title: "统计页", value: "statistics", status: 1 },
        { key: "work", title: "工作台", value: "work", status: 1 },
      ],
    };

    return {
      success: true,
      message: "获取成功",
      data: dictMap[code] || [],
    };
  },
};

export default dictMock;
