// 客户工作台模拟数据

// 年度数据
export const annualData = {
  targetAmount: 1200000, // 年度销售目标金额
  completedAmount: 780000, // 已完成销售金额
  completedOrders: 156, // 已完成订单数量
  completionRate: 65 // 完成率（百分比）
};

// 本月数据
export const monthlyData = {
  targetAmount: 100000, // 月度销售目标金额
  completedAmount: 68000, // 已完成销售金额
  completedOrders: 15, // 已完成订单数量
  completionRate: 68, // 完成率（百分比）
  ownBrandAmount: 32000, // 自主品牌金额
  ownBrandOrders: 8 // 自主品牌订单数
};

// 今日数据
export const todayData = {
  orderAmount: 12500, // 今日下单金额
  orderCount: 3, // 今日下单数量
  newCustomerWechat: true // 是否添加客户微信
};

// 待下推采购及ERP数量
export const pendingPurchaseErpData = {
  count: 5, // 总数量
  items: [
    {
      orderId: 'DD202504150001',
      assignTime: '2025-04-15 10:30',
      source: '官网',
      products: '高性能服务器 x2',
      remark: '客户要求尽快安排',
      reminderTime: '2025-04-17 14:00'
    },
    {
      orderId: 'DD202504150002',
      assignTime: '2025-04-15 11:45',
      source: '电话订单',
      products: '网络交换机 x1, 路由器 x1',
      remark: '需要技术支持',
      reminderTime: '2025-04-17 16:30'
    },
    {
      orderId: 'DD202504160001',
      assignTime: '2025-04-16 09:15',
      source: '销售顾问',
      products: '存储设备 x1',
      remark: '客户希望当天发货',
      reminderTime: '2025-04-17 10:00'
    },
    {
      orderId: 'DD202504160002',
      assignTime: '2025-04-16 14:20',
      source: '官网',
      products: '显示器 x5',
      remark: '大客户订单，优先处理',
      reminderTime: '2025-04-18 09:00'
    },
    {
      orderId: 'DD202504170001',
      assignTime: '2025-04-17 08:45',
      source: '微信小程序',
      products: '笔记本电脑 x2',
      remark: '需要提前确认库存',
      reminderTime: '2025-04-17 15:30'
    }
  ]
};

// 待提醒客户物流信息数量
export const pendingLogisticsData = {
  count: 4, // 总数量
  items: [
    {
      orderId: 'DD202504130001',
      assignTime: '2025-04-13 14:30',
      source: '官网',
      products: '高性能服务器 x1',
      logistics: {
        company: '顺丰速运',
        trackingNumber: 'SF1234567890'
      },
      remark: '',
      reminderTime: '2025-04-17 14:00'
    },
    {
      orderId: 'DD202504140001',
      assignTime: '2025-04-14 10:15',
      source: '电话订单',
      products: '网络交换机 x2',
      logistics: {
        company: '京东物流',
        trackingNumber: 'JD9876543210'
      },
      remark: '客户要求送货上门',
      reminderTime: '2025-04-17 16:30'
    },
    {
      orderId: 'DD202504140002',
      assignTime: '2025-04-14 16:45',
      source: '销售顾问',
      products: '显示器 x3',
      logistics: {
        company: '圆通速递',
        trackingNumber: 'YT2468013579'
      },
      remark: '',
      reminderTime: '2025-04-18 09:00'
    },
    {
      orderId: 'DD202504150001',
      assignTime: '2025-04-15 09:30',
      source: '微信小程序',
      products: '笔记本电脑 x1',
      logistics: {
        company: '中通快递',
        trackingNumber: 'ZT1357924680'
      },
      remark: '客户希望周末送达',
      reminderTime: '2025-04-17 15:30'
    }
  ]
};

// 待提醒客户验收
export const pendingAcceptanceData = {
  count: 3, // 总数量
  items: [
    {
      orderId: 'DD202504100001',
      assignTime: '2025-04-10 11:30',
      source: '官网',
      products: '服务器 x1',
      signTime: '2025-04-13 14:30',
      remark: '',
    },
    {
      orderId: 'DD202504110001',
      assignTime: '2025-04-11 09:45',
      source: '电话订单',
      products: '路由器 x2',
      signTime: '2025-04-14 10:15',
      remark: '客户反馈包装有轻微损坏',
    },
    {
      orderId: 'DD202504120001',
      assignTime: '2025-04-12 15:20',
      source: '销售顾问',
      products: '打印机 x1',
      signTime: '2025-04-15 16:45',
      remark: '',
    }
  ]
};

// 待提醒及开票数量
export const pendingInvoiceData = {
  count: 4, // 总数量
  items: [
    {
      orderId: 'DD202504050001',
      assignTime: '2025-04-05 10:30',
      source: '官网',
      products: '服务器 x2',
      status: 'pending', // pending: 待申请, applied: 已申请未下载
      remark: '',
    },
    {
      orderId: 'DD202504060001',
      assignTime: '2025-04-06 14:15',
      source: '电话订单',
      products: '网络设备套装',
      status: 'pending',
      remark: '客户要求开专票',
    },
    {
      orderId: 'DD202504070001',
      assignTime: '2025-04-07 09:20',
      source: '销售顾问',
      products: '办公设备 x5',
      status: 'applied',
      remark: '',
    },
    {
      orderId: 'DD202504080001',
      assignTime: '2025-04-08 16:40',
      source: '微信小程序',
      products: '笔记本电脑 x3',
      status: 'applied',
      remark: '客户要求电子发票',
    }
  ]
};

// 待提醒客户付款数量
export const pendingPaymentData = {
  count: 3, // 总数量
  items: [
    {
      orderId: 'DD202504010001',
      assignTime: '2025-04-01 11:30',
      source: '官网',
      products: '服务器 x1',
      amount: 45000,
      invoiceTime: '2025-04-03 14:30',
      remark: '',
      reminderTime: '2025-04-17 14:00'
    },
    {
      orderId: 'DD202504020001',
      assignTime: '2025-04-02 09:45',
      source: '电话订单',
      products: '网络设备套装',
      amount: 28500,
      invoiceTime: '2025-04-04 10:15',
      remark: '客户表示本周五付款',
      reminderTime: '2025-04-19 10:00'
    },
    {
      orderId: 'DD202504030001',
      assignTime: '2025-04-03 15:20',
      source: '销售顾问',
      products: '办公设备 x5',
      amount: 36800,
      invoiceTime: '2025-04-05 16:45',
      remark: '',
      reminderTime: '2025-04-18 09:30'
    }
  ]
};

// 应收数据
export const receivableData = {
  totalAmount: 320000, // 总应收金额
  totalCount: 12, // 总应收笔数
  receivedAmount: 180000, // 已回款金额
  receivedCount: 7, // 已回款笔数
  receivableDetails: [
    {
      orderId: 'DD202503150001',
      assignTime: '2025-03-15 10:30',
      amount: 45000,
    },
    {
      orderId: 'DD202503200001',
      assignTime: '2025-03-20 14:15',
      amount: 28500,
    },
    {
      orderId: 'DD202503250001',
      assignTime: '2025-03-25 09:20',
      amount: 36800,
    },
    {
      orderId: 'DD202503300001',
      assignTime: '2025-03-30 16:40',
      amount: 29700,
    }
  ],
  receivedDetails: [
    {
      orderId: 'DD202502150001',
      paymentTime: '2025-03-01 11:30',
      amount: 32000,
    },
    {
      orderId: 'DD202502200001',
      paymentTime: '2025-03-05 09:45',
      amount: 25600,
    },
    {
      orderId: 'DD202502250001',
      paymentTime: '2025-03-10 15:20',
      amount: 42500,
    }
  ]
};

// 商机跟进
export const opportunityData = {
  totalAmount: 580000, // 商机总金额
  items: [
    {
      id: 'SJ202504010001',
      customer: '北京科技有限公司',
      contact: '张经理',
      products: '服务器集群解决方案',
      amount: 120000,
      stage: '需求确认', // 商机阶段
      probability: 70, // 成功概率
      nextFollowTime: '2025-04-18 14:00'
    },
    {
      id: 'SJ202504050001',
      customer: '上海贸易有限公司',
      contact: '李总',
      products: '企业网络安全解决方案',
      amount: 85000,
      stage: '方案提交',
      probability: 60,
      nextFollowTime: '2025-04-19 10:30'
    },
    {
      id: 'SJ202504080001',
      customer: '广州电子科技公司',
      contact: '王工',
      products: '数据中心设备',
      amount: 230000,
      stage: '商务谈判',
      probability: 80,
      nextFollowTime: '2025-04-20 15:00'
    },
    {
      id: 'SJ202504100001',
      customer: '深圳智能科技有限公司',
      contact: '赵总',
      products: '智能办公设备',
      amount: 145000,
      stage: '初步接触',
      probability: 40,
      nextFollowTime: '2025-04-21 09:00'
    }
  ]
};
